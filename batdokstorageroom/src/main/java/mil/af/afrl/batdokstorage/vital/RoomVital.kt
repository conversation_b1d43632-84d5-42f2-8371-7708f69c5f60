package mil.af.afrl.batdokstorage.vital

import android.util.Log
import androidx.room.ColumnInfo
import androidx.room.PrimaryKey
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import mil.af.afrl.batdokdata.id.EncounterId
import mil.af.afrl.batdokdata.id.VitalId
import mil.af.afrl.batdokdata.models.patient.VitalWithRank
import mil.af.afrl.batdokdata.models.vital.EncounterVitals
import mil.af.afrl.batman.batdokid.*
import java.io.*
import java.time.Instant
import java.util.*


/**
 * Object to define and store a vital for an encounter
 *
 * @property id The vital's ID
 * @property encounterId The associated encounter's ID
 * @property hr Heart rate
 * @property spo2 SPO2
 * @property resp Respiration rate
 * @property bps Blood Pressure - Systolic
 * @property bpd Blood Pressure - Diastolic
 * @property ecg ECG
 * @property temp Temperature
 * @property etco2 Entitle CO2
 * @property ibps Internal Blood Pressure - Systolic
 * @property ibpd Internal Blood Pressure - Diastolic
 * @property painScale Pain scale
 * @property extraVitals Extra vitals
 * @property timestamp Timestamp for this vital
 */

@TypeConverters(VitalConverters::class)
open class RoomVital(@PrimaryKey var id: DomainId,
                     @ColumnInfo(name="owner_id") var encounterId: DomainId,
                     @ColumnInfo(name="heart_rate") var hr: Int? = null,
                     @ColumnInfo(name="spo2") var spo2: Int? = null,
                     @ColumnInfo(name="respiration") var resp: Int? = null,
                     @ColumnInfo(name="bp_systolic") var bps: Int? = null,
                     @ColumnInfo(name="bp_diastolic") var bpd: Int? = null,
                     @ColumnInfo(name="ecg_data") var ecg: ShortArray? = null,
                     @ColumnInfo(name="temp") var temp: Float? = null,
                     @ColumnInfo(name="etco2_new") var etco2: Int? = null,
                     @ColumnInfo(name="ibp_systolic") var ibps: Int? = null,
                     @ColumnInfo(name="ibp_diastolic") var ibpd: Int? = null,
                     @ColumnInfo(name="pain_scale") var painScale: Int? = null,
                     @ColumnInfo(name="extra_keys") var extraVitals: Map<String, Float>? = HashMap(),
                     @ColumnInfo(name="timestamp") var timestamp: Instant = Instant.now()){
    fun getExtrasStrings() : Pair<String, String>{
        if(extraVitals != null){
            var keysStr = ""
            var valsStr = ""
            for(key in extraVitals!!.keys){
                keysStr += key.replace(",", "") + ","
                valsStr += extraVitals!![key].toString().replace(",", "") + ","
            }
            if(keysStr.isNotEmpty()){
                keysStr = keysStr.substring(0, keysStr.length - 1)
                valsStr = valsStr.substring(0, valsStr.length - 1)
            }
            return Pair(keysStr, valsStr)
        }
        else{
            return Pair("", "")
        }
    }
    fun toEncounterVital(): EncounterVitals {
        return EncounterVitals(
            hr = VitalWithRank(hr, Int.MAX_VALUE),
            spo2 = VitalWithRank(spo2, Int.MAX_VALUE),
            resp = VitalWithRank(resp, Int.MAX_VALUE),
            bps = VitalWithRank(bps, Int.MAX_VALUE),
            bpd = VitalWithRank(bpd, Int.MAX_VALUE),
            etco2 = VitalWithRank(etco2, Int.MAX_VALUE),
            ecg = VitalWithRank(ecg, Int.MAX_VALUE),
            painScale = painScale,
            extras = extraVitals,
            ibps = VitalWithRank(ibps, Int.MAX_VALUE),
            ibpd = VitalWithRank(ibpd, Int.MAX_VALUE),
            temp = VitalWithRank(temp, Int.MAX_VALUE),
            isLive = false, timestamp=timestamp)
    }
}

const val SPO2_MAX = 100

class VitalConverters{
    @TypeConverter
    fun ecgToBytes(ecg: ShortArray?): ByteArray?{
        try {
            ByteArrayOutputStream().use { bos ->
                ObjectOutputStream(bos).use { oos ->
                    oos.writeObject(ecg)
                    return bos.toByteArray()
                }
            }
        } catch (ignored: Exception) {
        }
        return null
    }
    @TypeConverter
    fun bytesToEcg(ecgBytes: ByteArray?): ShortArray{
        return try {
            ecgBytes?.let { bytes ->
                ObjectInputStream(ByteArrayInputStream(bytes)).use {
                    it.readObject() as ShortArray?
                }
            }
        } catch (ex: Exception) {
            Log.e("DB ERROR", ex.message ?: "",ex)
            null
        } ?: ShortArray(0)
    }

    @TypeConverter
    fun extraVitalsToBytes(extraVitals: Map<String, Float>?): ByteArray?{
        try {
            ByteArrayOutputStream().use { bos ->
                ObjectOutputStream(bos).use { oos ->
                    oos.writeObject(extraVitals)
                    return bos.toByteArray()
                }
            }
        } catch (e: IOException) {
            e.printStackTrace()
        }
        return null
    }
    @TypeConverter
    fun bytesToExtraVitals(extrasBytes: ByteArray?): Map<String, Float>?{
        if(extrasBytes == null){
            return null
        }

        var extras : HashMap<String,Float>? = HashMap()
        try {
            val inputStream = ObjectInputStream(ByteArrayInputStream(extrasBytes))
            extras = inputStream.readObject() as HashMap<String,Float>?
        } catch (ex: Exception) {
            Log.e("DB ERROR", ex.message ?: "",ex)
        }
        return extras
    }
}