package mil.af.afrl.batdokstorage.user

import android.util.Log
import androidx.room.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import mil.af.afrl.batdokdata.models.user.User
import mil.af.afrl.batdokdata.models.user.UserDataStore
import mil.af.afrl.batman.batdokid.DomainId

@Dao
abstract class UserDao: UserDataStore {

    @Insert
    abstract suspend fun insert(user: RoomUser)
    @Update
    abstract suspend fun update(user: RoomUser)
    @Query("SELECT * FROM RoomUser WHERE callsign=:callsign")
    abstract suspend fun getRoomUser(callsign: String): RoomUser?
    @Query("SELECT * FROM RoomUser WHERE userId=:userId")
    abstract suspend fun getRoomUser(userId: DomainId): RoomUser?
    @Query("SELECT * FROM RoomUser")
    abstract suspend fun getRoomUsers(): List<RoomUser>

    @Query("SELECT * FROM RoomUser")
    abstract fun getRoomLiveUsers(): Flow<List<RoomUser>>

    override suspend fun getUsers(): List<User> = getRoomUsers().map { it.toUser() }

    override fun getLiveUsers(): Flow<List<User>> = getRoomLiveUsers().map { it.map { it.toUser() }}

    override suspend fun addUser(user: User, pword: String){
        insert(RoomUser(user, pword))
    }

    @Transaction
    override suspend fun changePassword(callsign: String, pword: String) {
        val user = requireNotNull(getUserByCallsign(callsign)){
            "No user with callsign $callsign found to update"
        }
        update(RoomUser(user, pword))
    }

    @Transaction
    override suspend fun updateUserData(user: User) {
        val roomUser = requireNotNull(getRoomUser(user.userId)){
            "No user with UserId ${user.userId.toHexString()} found to update"
        }
        update(roomUser.updateUserData(user))
    }

    @Transaction
    override suspend fun authenticate(callsign: String, pword: String): Boolean{
        Log.e("User", "$callsign - " + getRoomUser(callsign)?.let { it.hashedPWord.joinToString(""){ "%02X".format(it) }  + " - " + it.hashedPWord.toString(Charsets.ISO_8859_1)})
        val user = getRoomUser(callsign) ?: return false
        return RoomUser.hashPassword(pword, user.salt).second.contentEquals(user.hashedPWord)
    }
}