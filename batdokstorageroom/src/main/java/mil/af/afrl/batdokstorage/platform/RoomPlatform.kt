package mil.af.afrl.batdokstorage.platform

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import mil.af.afrl.batdokdata.models.platform.Platform
import mil.af.afrl.batman.batdokid.DomainId

@Entity(tableName = "platform")
class RoomPlatform(@PrimaryKey val id: DomainId,
                   val name: String?,
                   @ColumnInfo(defaultValue = "-1") val out_time: Long,
                   val status: String?,
                   @ColumnInfo(defaultValue = "0") val tutorial: Boolean?,
                   val destination: String?){
    fun toPlatform(): Platform {
        return Platform(id.copy(), name?:"", listOf(), out_time, status, tutorial?:false, destination)
    }

    companion object{
        @JvmStatic
        fun fromPlatform(platform: Platform): RoomPlatform {
            return RoomPlatform(platform.id, platform.name, platform.outTime, platform.status, platform.isTutorialPlatform, platform.destination)
        }
    }
}