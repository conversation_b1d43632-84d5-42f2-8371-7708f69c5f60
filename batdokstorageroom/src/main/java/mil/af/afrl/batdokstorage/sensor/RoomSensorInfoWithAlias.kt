package mil.af.afrl.batdokstorage.sensor

import androidx.room.Embedded
import androidx.room.Relation
import mil.af.afrl.batdokdata.models.sensor.SensorInfo

class RoomSensorInfoWithAlias(
    @Embedded val info: RoomSensorInfo,
    @Relation(parentColumn = "address", entityColumn = "address") val aliasInfo: RoomSensorAliasInfo?
) {
    fun toSensorInfo(): SensorInfo {
        return SensorInfo(
            info.id.copy(),
            info.address,
            info.name,
            aliasInfo?.alias,
            info.autoConnect,
            info.isFloating,
            info.encounterId?.copy(),
            info.rank,
            info.extras,
            aliasInfo?.color
        )
    }
}