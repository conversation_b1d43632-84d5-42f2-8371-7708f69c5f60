package mil.af.afrl.batdokstorage.treatment

import androidx.room.Entity
import gov.afrl.batdok.encounter.medicine.Medicine
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant

@Entity(tableName = "unattached_medicines",
    primaryKeys = ["id"])
class RoomUnattachedMedicine(
    val id: DomainId = DomainId.create(),
    val name: String,
    val ndc: String?,
    val rxcui: String?,
    val administrationTime: Instant? = null,
    val route: String? = null,
    val volume: Float? = null,
    val unit: String? = null,
    val serialNumber: String? = null,
    val expirationDate: String? = null,
    val type: String? = null
){
    constructor(medicine: Medicine): this(
        medicine.id,
        medicine.name,
        medicine.ndc,
        medicine.rxcui,
        medicine.administrationTime,
        medicine.route,
        medicine.volume,
        medicine.unit,
        medicine.serialNumber,
        medicine.expirationDate,
        medicine.type
    )

    fun toMedicine() = Medicine(
        name,
        ndc,
        rxcui,
        administrationTime,
        id.copy(),
        route,
        volume,
        unit,
        serialNumber,
        expirationDate,
        type
    )
}