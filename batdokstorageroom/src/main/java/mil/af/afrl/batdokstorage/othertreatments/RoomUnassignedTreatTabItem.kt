package mil.af.afrl.batdokstorage.othertreatments

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.protobuf.Any
import mil.af.afrl.batdokdata.models.othertreatments.UnassignedTreatTabItem
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant

@Entity(tableName = "unassigned_treat_tab_items")
class RoomUnassignedTreatTabItem(
    @PrimaryKey var rowId: DomainId,
    var name: String,
    var timestamp: Instant,
    var dataProtoBytes: ByteArray?
){
    constructor(item: UnassignedTreatTabItem): this(
        item.id,
        item.name,
        item.timestamp,
        item.treatmentData?.toByteArray()
    )
    fun toUnassignedItem() = UnassignedTreatTabItem(
        rowId.copy(),
        name,
        timestamp,
        Any.parseFrom(dataProtoBytes)
    )
}