package mil.af.afrl.batdokstorage.document

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import mil.af.afrl.batdokdata.id.EncounterId
import mil.af.afrl.batdokdata.models.document.DocumentQrCodeDataStore
import mil.af.afrl.batdokdata.models.document.QrCode
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant

@Dao
abstract class DocumentQrCodeDao: DocumentQrCodeDataStore {

    @Insert
    abstract suspend fun saveQrCode(qrCode: RoomQrCode)
    override suspend fun saveQrCode(qrCode: QrCode) {
        saveQrCode(RoomQrCode(qrCode))
    }

    @Query("SELECT * FROM DocumentQr WHERE encounter_id=:encounterId AND doc_type=:docType AND created_time=:createTime")
    abstract suspend fun getRoomQrCode(encounterId : DomainId, docType: String, createTime: Instant): RoomQrCode?
    override suspend fun getQrCode(
        encounterId : EncounterId,
        docType: String,
        createTime: Instant
    ) = getRoomQrCode(encounterId, docType, createTime)?.toQrCode()

    @Query("SELECT * FROM DocumentQr WHERE encounter_id=:encounterId AND doc_type=:docType")
    abstract suspend fun getAllRoomQrCodes(encounterId : DomainId, docType: String): MutableList<RoomQrCode>
    override suspend fun getAllQrCodes(encounterId : EncounterId, docType: String) =
        getAllRoomQrCodes(encounterId, docType).map { it.toQrCode() }

    @Query("SELECT * FROM DocumentQr WHERE encounter_id=:encounterId")
    abstract suspend fun getAllRoomQrCodes(encounterId : DomainId): MutableList<RoomQrCode>
    override suspend fun getAllQrCodes(encounterId : EncounterId) = getAllRoomQrCodes(encounterId).map { it.toQrCode() }

    @Query("SELECT * FROM DocumentQr WHERE encounter_id=:encounterId AND doc_type=:docType ORDER BY created_time DESC LIMIT 1")
    abstract suspend fun getLatestRoomQrCode(encounterId : DomainId, docType: String): RoomQrCode?
    override suspend fun getLatestQrCode(encounterId : EncounterId, docType: String) =
        getLatestRoomQrCode(encounterId, docType)?.toQrCode()

}