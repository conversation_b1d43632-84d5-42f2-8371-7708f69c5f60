package mil.af.afrl.batdokstorage.database

import android.content.Context
import androidx.room.RoomDatabase
import androidx.security.crypto.EncryptedFile
import androidx.security.crypto.MasterKey
import net.sqlcipher.database.SupportFactory
import java.io.File
import kotlin.random.Random

fun <T: RoomDatabase> RoomDatabase.Builder<T>.encrypt(password: ByteArray) = apply{
    openHelperFactory(SupportFactory(password))
}

fun generateKey(context: Context): ByteArray {
    val keyFile = File(context.noBackupFilesDir, "key.key")
    val encryptedFile = EncryptedFile(
        context,
        keyFile,
        MasterKey.Builder(context, "database-key").setKeyScheme(MasterKey.KeyScheme.AES256_GCM).build()
    )
    val key = if(!keyFile.exists()){
        Random.nextBytes(36).also { key ->
            encryptedFile.openFileOutput().use { fos ->
                fos.write(key)
            }
        }
    }else{
        encryptedFile.openFileInput().readBytes()
    }
    return key
}