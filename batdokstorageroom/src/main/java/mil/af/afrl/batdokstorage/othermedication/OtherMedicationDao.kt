package mil.af.afrl.batdokstorage.othermedication

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import kotlinx.coroutines.flow.Flow
import mil.af.afrl.batdokdata.models.othermedication.OtherMedicationDataStore

@Dao
abstract class OtherMedicationDao : OtherMedicationDataStore {

    @Query("SELECT name FROM suc_other_medication")
    abstract override suspend fun getOtherMedications(): List<String>

    @Query("SELECT name FROM suc_other_medication")
    abstract override fun getOtherMedicationsFlow(): Flow<List<String>>

    @Insert
    abstract suspend fun addRoomOtherMedication(otherMedication: RoomOtherMedication)

    override suspend fun addOtherMedication(otherMedication: String) {
        if (otherMedication !in getOtherMedications()) {
            addRoomOtherMedication(RoomOtherMedication(otherMedication))
        }
    }

    @Query("DELETE FROM suc_other_medication WHERE name=:otherMedication")
    abstract override suspend fun deleteOtherMedication(otherMedication: String)
}