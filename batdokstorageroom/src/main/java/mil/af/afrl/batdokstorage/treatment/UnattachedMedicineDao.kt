package mil.af.afrl.batdokstorage.treatment

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import androidx.room.Update
import gov.afrl.batdok.encounter.ids.MedicineId
import gov.afrl.batdok.encounter.medicine.Medicine
import mil.af.afrl.batdokdata.models.medication.UnattachedMedicineDataStore
import mil.af.afrl.batman.batdokid.DomainId

@Dao
abstract class UnattachedMedicineDao : UnattachedMedicineDataStore {
    @Insert
    abstract suspend fun insert(treatment: RoomUnattachedMedicine)
    @Update
    abstract suspend fun update(treatment: RoomUnattachedMedicine)

    override suspend fun addMedicine(treatment: Medicine) {
        var treatmentToUse = treatment
        if (medicineExists(treatment.id)) {
            // Generate unique ID for treatment if exists already
            treatmentToUse = treatment.copy(medId = DomainId.create())
        }
        insert(RoomUnattachedMedicine(treatmentToUse))
    }

    @Query("DELETE FROM unattached_medicines WHERE id=:id")
    abstract override suspend fun removeMedicine(id: MedicineId)

    override suspend fun updateMedicine(treatment: Medicine) {
        update(RoomUnattachedMedicine(treatment))
    }

    @Query("SELECT * FROM unattached_medicines")
    abstract suspend fun getRoomUnattachedMedicines(): List<RoomUnattachedMedicine>
    override suspend fun getUnattachedMedicines(): List<Medicine> = getRoomUnattachedMedicines().map { it.toMedicine() }

    @Query("SELECT * FROM unattached_medicines WHERE id=:id")
    protected abstract suspend fun getMedicineIfIdExists(id: DomainId): RoomUnattachedMedicine?

    override suspend fun medicineExists(id: MedicineId): Boolean {
        return getMedicineIfIdExists(id) != null
    }

    @Query("SELECT * FROM unattached_medicines WHERE id=:id")
    abstract suspend fun roomGetMedicine(id: DomainId): RoomUnattachedMedicine
    override suspend fun getMedicine(id: MedicineId): Medicine = roomGetMedicine(id).toMedicine()
}