package mil.af.afrl.batdokstorage.a2a

import androidx.room.Entity
import androidx.room.PrimaryKey
import mil.af.afrl.batdokdata.models.a2a.A2AApplication

@Entity(tableName = "a2a_applications")
class RoomA2AApplication (val name: String,
                          @PrimaryKey val receiverClass: String,
                          var enabled: Boolean,
                          var interval: Int,
                          var packageName: String) {
    constructor(a2AApplication: A2AApplication): this(
        a2AApplication.name,
        a2AApplication.receiverClass,
        a2AApplication.enabled,
        a2AApplication.interval,
        a2AApplication.packageName
    )
    fun toA2AApplication() = A2AApplication(name, receiverClass, enabled, interval, packageName)
}