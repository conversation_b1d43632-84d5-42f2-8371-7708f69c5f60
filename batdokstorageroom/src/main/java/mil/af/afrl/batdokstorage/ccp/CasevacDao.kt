package mil.af.afrl.batdokstorage.ccp

import androidx.room.*
import mil.af.afrl.batdokdata.models.ccp.CasevacDataStore
import mil.af.afrl.batdokdata.models.ccp.Casevac
import kotlinx.coroutines.flow.Flow

@Dao
abstract class CasevacDao: CasevacDataStore {
    @Query("SELECT * FROM casevac ORDER BY callsign COLLATE NOCASE, type COLLATE NOCASE")
    abstract override fun getAll(): Flow<List<Casevac>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun add(casevac: RoomCasevac)
    override suspend fun add(casevac: Casevac) = add(RoomCasevac(casevac))

    @Update
    abstract suspend fun update(casevac: RoomCasevac)
    override suspend fun update(casevac: Casevac) = update(RoomCasevac(casevac))

    @Transaction
    override suspend fun update(oldCasevac: Casevac, newCasevac: Casevac) {
        delete(oldCasevac)
        add(newCasevac)
    }

    @Delete
    abstract suspend fun delete(casevac: RoomCasevac)
    override suspend fun delete(casevac: Casevac) = delete(RoomCasevac(casevac))

    @Query("DELETE FROM casevac")
    abstract override suspend fun deleteAll()

    @Query("DELETE FROM casevac WHERE tutorial=1")
    abstract override suspend fun deleteAllTutorial()

}