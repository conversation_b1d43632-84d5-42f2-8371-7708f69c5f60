package mil.af.afrl.batdokstorage.patient

import mil.af.afrl.batdokdata.models.patient.GeoTag
import mil.af.afrl.batdokdata.models.patient.Location
import java.io.Serializable
import java.time.Instant

data class RoomPatientLocation(val tagged: <PERSON><PERSON>an,
                               val attached: Boolean,
                               val latitude: Double,
                               val longitude: Double,
                               val altitude: Double?,
                               val markTime: Instant?) : Serializable {
    constructor(tag: GeoTag) : this(tag.isTagged, tag.isAttached, tag.location.lat, tag.location.lon, tag.location.altitude, tag.markTime)
    fun toGeoTag(): GeoTag {
        return GeoTag(tagged, attached, Location(latitude, longitude, altitude), markTime)
    }
}
