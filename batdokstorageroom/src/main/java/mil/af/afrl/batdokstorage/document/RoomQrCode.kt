package mil.af.afrl.batdokstorage.document

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.ForeignKey
import com.batman.batdok.infrastructure.contactlesstransfer.proto.ContactlessTransferCommands
import mil.af.afrl.batdokdata.models.document.QrCode
import mil.af.afrl.batdokstorage.patient.RoomEncounter
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant

@Entity(tableName = "DocumentQr",
        primaryKeys = ["doc_type", "created_time"],
        foreignKeys = [ForeignKey(
            entity = RoomEncounter::class,
            parentColumns = ["id"],
            childColumns = ["encounter_id"],
            onDelete = ForeignKey.CASCADE
        )])
class RoomQrCode(
    @JvmField @ColumnInfo(name = "encounter_id", index = true) var encounterId: DomainId?,
    @JvmField @ColumnInfo(name = "doc_type") var docType: String,
    @JvmField @ColumnInfo(name = "created_time") var createTime: Instant,
    @JvmField @ColumnInfo(name = "data") var qrCodeData: ByteArray?
){
    constructor(qrCode: QrCode): this(qrCode.encounterId, qrCode.docType, qrCode.createTime, qrCode.qrCodeData?.toByteArray())
    fun toQrCode() = QrCode(
        encounterId?.copy(),
        docType,
        createTime,
        ContactlessTransferCommands.ContactlessTransferMessage.parseFrom(qrCodeData)
    )
}