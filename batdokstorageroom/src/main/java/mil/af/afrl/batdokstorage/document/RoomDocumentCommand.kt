package mil.af.afrl.batdokstorage.document

import androidx.room.*
import gov.afrl.batdok.commands.proto.DocumentCommands
import gov.afrl.batdok.commands.proto.documentCommand
import gov.afrl.batdok.encounter.ids.toByteString
import gov.afrl.batdok.encounter.ids.toDomainId
import mil.af.afrl.batdokstorage.patient.RoomEncounter
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant


@Entity(tableName = "command",
        foreignKeys = [ForeignKey(entity= RoomEncounter::class,
                parentColumns = ["id"],
                childColumns = ["encounter_id"],
                onDelete = ForeignKey.CASCADE)],
        indices = [Index(value = ["encounter_id"]),Index(value = ["date"]),Index(value = ["user"]),Index(value = ["date","user"])])
class RoomDocumentCommand(@PrimaryKey val command_id: DomainId,
                          val encounter_id: DomainId,
                          @ColumnInfo(name = "data",typeAffinity = ColumnInfo.BLOB)
                          /**
                           * ByteArray of CommandData
                           */
                          val data: ByteArray?,
                          val date: Instant?,
                          val user: String?){
    fun toEditCommand(): DocumentCommands.DocumentCommand{
        return documentCommand {
            this.encounterId = encounter_id.toByteString()
            this.commands.add(
                DocumentCommands.CommandData.parseFrom(data)
            )
        }
    }

    companion object{
        @JvmStatic
        fun fromEditCommand(command: DocumentCommands.DocumentCommand): List<RoomDocumentCommand>{
            return command.commandsList.map {
                RoomDocumentCommand(
                    it.commandId.toDomainId(),
                    command.encounterId.toDomainId(),
                    it.toByteArray(),
                    Instant.ofEpochSecond(it.timestamp),
                    it.callsign
                )
            }
        }
    }
}