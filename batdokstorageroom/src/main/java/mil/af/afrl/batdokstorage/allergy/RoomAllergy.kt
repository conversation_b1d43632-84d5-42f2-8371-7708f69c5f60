package mil.af.afrl.batdokstorage.allergy

import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import mil.af.afrl.batdokdata.models.allergy.MultumAllergy

@Entity(tableName = "Allergies_table", indices = [
    Index(value = ["term"], name = "idx_term"),
    Index(value = ["code"], name = "idx_code")
])
data class RoomAllergy(@PrimaryKey(autoGenerate = true) val id: Int?,
                       val term: String,
                       val code: String,
                       val vocab: String) {

    constructor(multumAllergy: MultumAllergy) : this(multumAllergy.id,
        multumAllergy.term, multumAllergy.code, multumAllergy.vocab)

    fun toMultumAllergy() = MultumAllergy(id, term, code, vocab)
}
