package mil.af.afrl.batdokstorage.chat

import androidx.room.Entity
import mil.af.afrl.batdokdata.models.chat.ChatMessage
import mil.af.afrl.batdokdata.models.chat.SentStatus
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant

/**
 *  Chat Message object
 *  @param conversationId The conversation the message belongs to
 *  @param from Who the message was from.  If it is null, user has sent it.
 *  @param message The message
 *  @param timeStamp Time stamp of when message was sent
 *  @param sentStatus The sent status of the message
 */
@Entity(tableName = "chat_messages",
    //composite primary keys are also composite indexes.  Table column order matters
    //since we'll be primarily searching on conversationId
        primaryKeys = ["conversationId", "timeStamp"])
data class RoomChatMessage @JvmOverloads constructor(var conversationId: String = "",
                                                     var from: DomainId? = null,
                                                     var message: String? = null,
                                                     var timeStamp: Instant = Instant.now(),
                                                     var read: Boolean = true,
                                                     var sentStatus: String = SentStatus.NONE.status) {
    constructor(chatMessage: ChatMessage): this(
        chatMessage.conversationId,
        chatMessage.from,
        chatMessage.message,
        chatMessage.timeStamp,
        chatMessage.read,
        chatMessage.sentStatus
    )

    fun toChatMessage() = ChatMessage(
        this.conversationId,
        this.from?.copy(),
        this.message,
        this.timeStamp,
        this.read,
        this.sentStatus
    )
}