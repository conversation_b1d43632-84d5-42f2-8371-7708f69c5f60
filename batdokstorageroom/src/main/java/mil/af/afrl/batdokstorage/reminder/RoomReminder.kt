package mil.af.afrl.batdokstorage.reminder

import androidx.room.*
import mil.af.afrl.batdokdata.id.EncounterId
import mil.af.afrl.batdokdata.models.reminder.Reminder
import mil.af.afrl.batdokstorage.patient.RoomEncounter
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Duration
import java.time.Instant

/**
 * Object for future treatment reminder
 *
 * @property id ID of this MedReminder
 * @property encounterId Patient ID to be applied to
 * @property description Description to show up when reminder activates
 * @property timeStamp Time stamp of current time
 * @property reminderTime Time in future of reminder
 */
@Entity(tableName = "reminder",
        foreignKeys = [ForeignKey(entity = RoomEncounter::class,
                parentColumns = ["id"],
                childColumns = ["encounter_id"],
                onDelete = ForeignKey.CASCADE)],
    indices = [Index(value = ["encounter_id"])])
class RoomReminder(@PrimaryKey var id: DomainId = DomainId.nil(),
                   @ColumnInfo(name = "encounter_id") var encounterId: DomainId = DomainId.nil(),
                   @ColumnInfo(name = "description") var description: String? = "",
                   @ColumnInfo(name = "time_stamp") var timeStamp: Instant? = Instant.now(),
                   @ColumnInfo(name = "reminder_time") var reminderTime: Long? = 0,
                   @Ignore var reminderType: String = Reminder.REMINDER_TYPE) {

    @Ignore constructor(model: Reminder): this(model.id, EncounterId(model.encounterId.unique), model.description, model.timestamp, model.reminderTime.toMillis())

    fun toReminder() : Reminder {
        return Reminder(
            id,
            encounterId,
            description ?: "",
            timeStamp,
            Duration.ofMillis(reminderTime?:0) ?: Duration.ZERO,
            reminderType
        )
    }
}
