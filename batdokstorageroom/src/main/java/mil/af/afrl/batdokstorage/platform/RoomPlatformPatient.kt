package mil.af.afrl.batdokstorage.platform

import androidx.room.Embedded
import androidx.room.Relation
import mil.af.afrl.batdokdata.id.EncounterId
import mil.af.afrl.batdokdata.models.platform.PlatformPatient
import mil.af.afrl.batdokstorage.patient.RoomEncounter
import mil.af.afrl.batman.batdokid.DomainId

class RoomPlatformPatientbyPlatform(@Embedded val platformPatientRelation: RoomPlatformPatientRelation,
                                    @Relation(entity = RoomEncounter::class,
                                            parentColumn = "encounter_id",
                                            entityColumn = "id")
                                    val patientSubset: PatientSubset
){

    fun toPlatformPatient(): PlatformPatient {
        return PlatformPatient(platformPatientRelation.encounter_id.copy(), patientSubset.local_name, patientSubset.name, patientSubset.triage, patientSubset.patient_type,
                platformPatientRelation.patient_rank)
    }
}

class RoomPlatformPatientByPatient(@Embedded val patientSubset: PatientSubset,
                                   @Relation(entity = RoomPlatformPatientRelation::class,
                                             parentColumn = "id",
                                             entityColumn = "encounter_id")
                                   val platformPatientRelation: RoomPlatformPatientRelation?){

    fun toPlatformPatient(): PlatformPatient {
        return PlatformPatient(patientSubset.id.copy(), patientSubset.local_name, patientSubset.name, patientSubset.triage, patientSubset.patient_type,
                platformPatientRelation?.patient_rank?:0)
    }
}

class PatientSubset(val id: DomainId, val local_name: String, val name: String, val info_name: String, val triage: Int, val patient_type: Int)