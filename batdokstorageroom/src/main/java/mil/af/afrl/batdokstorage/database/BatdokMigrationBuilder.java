package mil.af.afrl.batdokstorage.database;

import android.content.Context;

import androidx.room.migration.Migration;

public class  BatdokMigrationBuilder extends RoomMigrationBuilder {

    public BatdokMigrationBuilder(Context context) {
        super(context);
    }

    public final static int VERSION = 1;

    public Migration[] getMigrations() {
        return new Migration[]{
//                MIGRATION_1_2
        };
    }

    private final Migration MIGRATION_1_2 = buildMigration(1, 2, db -> {
        // Do migration
    });
}
