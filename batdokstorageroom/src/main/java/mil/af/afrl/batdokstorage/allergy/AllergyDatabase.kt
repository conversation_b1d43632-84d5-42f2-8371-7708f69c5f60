package mil.af.afrl.batdokstorage.allergy

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase

@Database(entities = [RoomAllergy::class], version = 1)
abstract class AllergyDatabase : RoomDatabase() {
    abstract fun allergyDao(): AllergyDao

    companion object {
        @Volatile
        private var INSTANCE: AllergyDatabase? = null

        fun getDatabase(context: Context, dbName: String = "multum_allergies") : AllergyDatabase {
            val tempInstance = INSTANCE
            if (tempInstance != null) {
                return tempInstance
            }
            synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    AllergyDatabase::class.java,
                    dbName
                ).createFromAsset("multum_allergiesdb.db3").fallbackToDestructiveMigration().build()
                INSTANCE = instance
                return instance
            }
        }
    }
}