package mil.af.afrl.batdokstorage.moi.suc

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import kotlinx.coroutines.flow.Flow
import mil.af.afrl.batdokdata.models.moi.OtherMoiDataStore

@Dao
abstract class OtherMoiDao : OtherMoiDataStore {

    @Query("SELECT name FROM suc_other_moi")
    abstract override suspend fun getOtherMois(): List<String>
    @Query("SELECT name FROM suc_other_moi")
    abstract override fun getOtherMoisFlow(): Flow<List<String>>
    @Insert
    abstract suspend fun setRoomOtherMois(otherMois: List<RoomOtherMoi>)

    @Query("DELETE FROM suc_other_moi")
    abstract suspend fun deleteAll()

    override suspend fun setOtherMois(otherMois: List<String>) {
        deleteAll()
        setRoomOtherMois(otherMois.map { RoomOtherMoi(0, it) })
    }
}