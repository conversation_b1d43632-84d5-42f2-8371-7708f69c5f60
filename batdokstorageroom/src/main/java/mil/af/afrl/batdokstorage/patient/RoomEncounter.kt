package mil.af.afrl.batdokstorage.patient

import androidx.room.*
import mil.af.afrl.batdokdata.models.patient.*
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Duration
import java.time.Instant

@Entity(tableName = "encounter",        indices = [Index(value = ["owner"]), Index(value = ["date"])])
class RoomEncounter(@PrimaryKey val id: DomainId,
                    val date: Instant,
                    val version: Int,
                    val name: String,
                    val local_name: String,
                    val info_name: String,
                    val patient_type: Int,
                    val owner: DomainId,//default is a null blob
                    @ColumnInfo(defaultValue = "false") val has_new_documentation: Boolean,
                    val triage: Int,
                    @Embedded(prefix = "geo_") val location: RoomPatientLocation,
                    val training_mode: Boolean,
                    val platformStatus: String?,
                    val local_mode: String?,
                    val owner_mode: String?,
                    @ColumnInfo(defaultValue = "true") val notify_user: Boolean,
                    val is_auto_logging: Boolean,
                    val start_logging: Boolean,
                    @ColumnInfo(defaultValue = "3000") val auto_logging_time: Long,
                    val pinned: Boolean,
                    @ColumnInfo(defaultValue = "0") val commandChecksum: Byte = 0,
                    val exportStatus: ExportStatus,
                    val maskingJustification: String?,
                    val patientId: DomainId,
                    val externalIds: Map<String, String>,
                    val handedOffTime: Instant?,
                    val encounterState: EncounterState,
                    val encounterRank: Int
) {

    fun toEncounter(): EncounterModel {
        return EncounterModel(
            id.copy(),
            PatientName(name, local_name),
            patient_type,
            owner.copy(),
            triage=Triage(triage),
            hasNewDocumentation = has_new_documentation,
            geoTag = location.toGeoTag(),
            isTrainingMode = training_mode,
            platformStatus = platformStatus,
            mode = PatientMode(local_mode, owner_mode, notify_user),
            autoLog = AutoLog(is_auto_logging, start_logging, Duration.ofMillis(auto_logging_time)),
            active = pinned,
            commandChecksum = commandChecksum,
            created = date,
            exportStatus = exportStatus,
            maskingJustification = maskingJustification,
            patientId = patientId.copy(),
            externalIds = externalIds,
            handedOffTime = handedOffTime,
            encounterState = encounterState,
            encounterRank = encounterRank
        )
    }

    companion object {
        @JvmStatic
        fun fromEncounter(patient: EncounterModel): RoomEncounter {
            return RoomEncounter(
                patient.id,
                patient.created,
                0,
                patient.name.name,
                patient.name.displayName,
                patient.name.displayName,
                patient.type,
                patient.owner,
                patient.hasNewDocumentation,
                patient.triage.triage,
                RoomPatientLocation(patient.geoTag),
                patient.isTrainingMode,
                patient.status,
                patient.mode.localMode,
                patient.mode.ownerMode,
                patient.mode.notifyUser,
                patient.autoLog.isLogging,
                patient.autoLog.startLogging,
                patient.autoLog.loggingInterval.toMillis(),
                patient.active,
                patient.commandChecksum,
                patient.exportStatus,
                patient.maskingJustification,
                patient.patientId,
                patient.externalIds,
                patient.handedOffTime,
                patient.encounterState,
                patient.encounterRank
            )
        }
    }
}