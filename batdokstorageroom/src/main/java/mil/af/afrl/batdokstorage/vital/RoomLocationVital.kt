package mil.af.afrl.batdokstorage.vital

import androidx.room.*
import gov.afrl.batdok.util.Patterns
import gov.afrl.batdok.util.format
import mil.af.afrl.batdokdata.models.patient.VitalWithRank
import mil.af.afrl.batdokdata.models.vital.LocationEncounterVitals
import mil.af.afrl.batdokstorage.patient.RoomEncounter
import mil.af.afrl.batman.batdokid.*
import java.text.DecimalFormat
import java.time.Instant
import java.util.*

/**
 * Object to create vital with associated location (using Long, Lat, and Altitude)
 */
@Entity(tableName = "trends_vitals",
        foreignKeys = [ForeignKey(entity = RoomEncounter::class,
                parentColumns = ["id"],
                childColumns = ["owner_id"],
                onDelete = ForeignKey.CASCADE)],
        indices = [Index(value = ["owner_id"]),Index(value = ["timestamp"])])
@TypeConverters(VitalConverters::class)
class RoomLocationVital(id: DomainId, encounterId: DomainId, hr: Int? = null, spo2: Int? = null, resp: Int? = null,
                        bps: Int? = null, bpd: Int? = null, ecg: ShortArray? = null, temp: Float? = null,
                        etco2: Int? = null, ibps: Int? = null, ibpd: Int? = null, painScale: Int? = null,
                        extraVitals: Map<String, Float>? = null,
                        timestamp: Instant = Instant.now(),
                        @ColumnInfo(name = "location_lat") var locationLat: Float? = null,
                        @ColumnInfo(name = "location_lon") var locationLon: Float? = null,
                        @ColumnInfo(name = "altitude") var altitude: Float? = null) : RoomVital(id,
        encounterId,
        hr,
        spo2,
        resp,
        bps,
        bpd,
        ecg,
        temp,
        etco2,
        ibps,
        ibpd,
        painScale,
        extraVitals,
        timestamp){

    fun toLocationPatientVital(): LocationEncounterVitals {
        return LocationEncounterVitals(VitalWithRank(hr, Int.MAX_VALUE),
                VitalWithRank(spo2, Int.MAX_VALUE),
                VitalWithRank(resp, Int.MAX_VALUE),
                VitalWithRank(bps, Int.MAX_VALUE),
                VitalWithRank(bpd, Int.MAX_VALUE),
                VitalWithRank(etco2, Int.MAX_VALUE),
                VitalWithRank(ecg, Int.MAX_VALUE),
                extraVitals,
                VitalWithRank(ibps, Int.MAX_VALUE),
                VitalWithRank(ibpd, Int.MAX_VALUE),
                VitalWithRank(temp, Int.MAX_VALUE),
                false, timestamp=timestamp,
                locationLat = locationLat,
                locationLon = locationLon,
                altitude = altitude)
    }

    override fun toString(): String{
        return "${timestamp.format(Patterns.ymdhms_24_dash_space_colon)},${toString(hr)}," +
                "${toString(spo2)},${toString(resp)},${toString(bps)},${toString(bpd)}," +
                "${toString(temp)},${toString(etco2)},${toString(ibps)},${toString(ibpd)}," +
                "${toString(extraVitals)}," +
                "${toString(locationLat)},${toString(locationLon)},${toString(altitude)}\n"
    }
    
    private fun toString(obj: Number?): String{
        return if(obj == null || obj == -1 || obj == -1f){
            ""
        } else {
            if(obj is Float || obj is Double){
                DecimalFormat("#.##").format(obj)
            }else {
                obj.toString()
            }
        }
    }

    private fun toString(obj: Map<String, Float>?): String{
        return if(obj == null || obj.isEmpty()){
            ""
        } else {
            obj.map { "${it.key}:${DecimalFormat("#.##").format(it.value)}" }.joinToString(";")
        }
    }

    companion object{
        @JvmStatic
        fun getStringHeaders(): String{
            return "Time,Heart Rate,Oxygen Saturation,Respiration Rate,Systolic Blood Pressure,Diastolic Blood Pressure,Temp,EtCO2,Invasive Systolic Blood Pressure,Invasive Diastolic Blood Pressure,Extra Vitals,Latitude,Longitude,Altitude\n"
        }
    }
}

//SELECT * FROM test JOIN (SELECT MAX(time) as time from test GROUP BY id) as t ON test.time = t.time;
@DatabaseView("WITH max_timestamps AS (SELECT owner_id, MAX(timestamp) AS max_time  FROM trends_vitals GROUP BY owner_id) SELECT tv.* FROM trends_vitals tv JOIN max_timestamps mt ON tv.owner_id = mt.owner_id AND tv.timestamp = mt.max_time")
class LatestVital(@Embedded val LocationVital: RoomLocationVital)