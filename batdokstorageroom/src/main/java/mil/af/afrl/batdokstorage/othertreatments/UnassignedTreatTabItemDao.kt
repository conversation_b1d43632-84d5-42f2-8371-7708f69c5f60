package mil.af.afrl.batdokstorage.othertreatments

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import gov.afrl.batdok.encounter.ids.TreatmentId
import mil.af.afrl.batdokdata.models.othertreatments.UnassignedTreatTabItem
import mil.af.afrl.batdokdata.models.othertreatments.UnassignedTreatTabItemDatastore
import mil.af.afrl.batman.batdokid.DomainId

@Dao
abstract class UnassignedTreatTabItemDao: UnassignedTreatTabItemDatastore {

    @Query("SELECT * FROM unassigned_treat_tab_items")
    abstract suspend fun getRoomItems(): List<RoomUnassignedTreatTabItem>
    override suspend fun getItems() = getRoomItems().map { it.toUnassignedItem() }

    @Insert
    abstract suspend fun addItem(item: RoomUnassignedTreatTabItem)
    override suspend fun addItem(item: UnassignedTreatTabItem) {
        addItem(RoomUnassignedTreatTabItem(item))
    }

    @Query("DELETE FROM unassigned_treat_tab_items WHERE rowId=:id")
    abstract suspend fun roomDeleteItem(id: DomainId)
    override suspend fun deleteItem(id: TreatmentId) = roomDeleteItem(id)
}