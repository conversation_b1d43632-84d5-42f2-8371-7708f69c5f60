package mil.af.afrl.batdokstorage.otherinjury

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import androidx.room.Transaction
import kotlinx.coroutines.flow.Flow
import mil.af.afrl.batdokdata.models.otherinjury.OtherInjury
import mil.af.afrl.batdokdata.models.otherinjury.OtherInjuryDataStore

@Dao
abstract class OtherInjuryDao : OtherInjuryDataStore {

    @Query("SELECT * FROM dd1380_other_injury")
    abstract suspend fun getRoomOtherInjuries(): List<RoomOtherInjury>
    @Query("SELECT * FROM dd1380_other_injury")
    abstract override fun getOtherInjuriesFlow(): Flow<List<OtherInjury>>
    override suspend fun getOtherInjuries(): List<OtherInjury> = getRoomOtherInjuries().map { OtherInjury(it.name, it.abbreviation) }


    @Insert
    abstract suspend fun setRoomOtherInjuries(otherInjuries: List<RoomOtherInjury>)

    @Query("DELETE FROM dd1380_other_injury")
    abstract suspend fun deleteAll()

    @Transaction
    override suspend fun setOtherInjuries(otherInjuries: List<OtherInjury>) {
        deleteAll()
        setRoomOtherInjuries(otherInjuries.map { RoomOtherInjury(0, it.name, it.abbreviation) })
    }
}