package mil.af.afrl.batdokstorage.burnsheet

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.ForeignKey.Companion.CASCADE
import androidx.room.PrimaryKey
import mil.af.afrl.batdokdata.models.burnsheet.MedCardFlowRowDataModel
import mil.af.afrl.batman.batdokid.DomainId
import java.io.Serializable

/**
 * This class contains the information to fill out the flow rows on the Burn Sheet
 */
@Entity(
    tableName = "burn_document_flow_row",
    foreignKeys = [ForeignKey(
        entity = RoomMedCardBurnSheetInfo::class,
        parentColumns = ["id"],
        childColumns = ["owner_id"],
        onDelete = CASCADE
    )]
)
class RoomMedCardFlowRowDataModel(
    @ColumnInfo(name = "id") @PrimaryKey var id: DomainId,
    @ColumnInfo(name = "owner_id", index = true) var ownerId: DomainId?,
    @ColumnInfo(name = "tx_site") var treatmentSiteOrTeam: String?,
    @ColumnInfo(name = "local_time") var localTime: String?,
    @ColumnInfo(name = "base_deficit") var baseDeficitLactat: String?,
    @ColumnInfo(name = "heart_rate") var heartRate: String?,
    @ColumnInfo(name = "pressors") var pressorsAndBladderPressure: String?,
    @ColumnInfo(name = "crys") var crystalloid: String?,
    @ColumnInfo(name = "coll") var colloid: String?,
    @ColumnInfo(name = "total") var ccTotal: String?,
    @ColumnInfo(name = "map") var map: String?,
    @ColumnInfo(name = "cvp") var cvp: String?,
    @ColumnInfo(name = "uop") var uop: String?
) : Serializable {

    constructor(model: MedCardFlowRowDataModel): this(
        model.id,
        model.ownerId,
        model.treatmentSiteOrTeam,
        model.localTime,
        model.baseDeficitLactat,
        model.heartRate,
        model.pressorsAndBladderPressure,
        model.crystalloid,
        model.colloid,
        model.ccTotal,
        model.map,
        model.cvp,
        model.uop
    )

    fun toMedCardFlowRowDataModel() = MedCardFlowRowDataModel(
        id,
        ownerId,
        treatmentSiteOrTeam,
        localTime,
        baseDeficitLactat,
        heartRate,
        pressorsAndBladderPressure,
        crystalloid,
        colloid,
        ccTotal,
        map,
        cvp,
        uop
    )
}