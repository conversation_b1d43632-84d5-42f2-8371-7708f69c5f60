package mil.af.afrl.batdokstorage.quality

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import mil.af.afrl.batdokdata.models.quality.Quality
import mil.af.afrl.batdokdata.models.quality.QualityDataStore

@Dao
interface QualityDao : QualityDataStore {
    @Query("SELECT * FROM quality")
    suspend fun getRoomQualities(): List<RoomQuality>
    override suspend fun getQualities(): List<Quality> = getRoomQualities().map { Quality(it.rowId,it.vitalType, it.quality)}

    @Query("SELECT * FROM quality WHERE vitalType=:vitalType")
    suspend fun getRoomQualities(vitalType: String): List<RoomQuality>
    override suspend fun getQualities(vitalType: String): List<Quality> = getRoomQualities(vitalType).map { Quality(it.rowId,it.vitalType, it.quality)}

    @Query("DELETE FROM quality WHERE rowId=:id")
    override suspend fun deleteQuality(id: Int)

    @Insert
    suspend fun addRoomQuality(quality: RoomQuality)
    override suspend fun addQuality(quality: Quality) = addRoomQuality(RoomQuality(0, quality.vitalType, quality.quality))
}