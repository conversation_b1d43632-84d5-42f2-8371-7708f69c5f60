package mil.af.afrl.batdokstorage.medinventory

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import mil.af.afrl.batdokdata.models.medinventory.MedRouteListItem

/**
 * The object for a DD1380 Route List Item
 */
@Entity(tableName = "dd1380_route_list_item")
class RoomMedRouteListItem(
    @JvmField @field:PrimaryKey(autoGenerate = true) val id: Int,
    @JvmField @field:ColumnInfo(name = "route") val name: String
) {
    constructor(route: MedRouteListItem): this(route.id, route.name)
    fun toMedRouteListItem() = MedRouteListItem(id, name)
}