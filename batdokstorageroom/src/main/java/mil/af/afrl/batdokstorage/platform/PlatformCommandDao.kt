package mil.af.afrl.batdokstorage.platform

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import androidx.room.Transaction
import com.batman.batdok.infrastructure.network.commands.PlatformCommands
import com.google.protobuf.ByteString
import gov.afrl.batdok.util.ProtobufUtils
import mil.af.afrl.batdokdata.id.PlatformId
import mil.af.afrl.batdokdata.models.platform.PlatformCommandDataStore
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant
import java.util.*

@Dao
abstract class PlatformCommandDao: PlatformCommandDataStore {

    @Query("SELECT * FROM platform_command")
    abstract suspend fun roomCommands(): List<RoomPlatformCommand>

    @Insert
    abstract suspend fun add(command: RoomPlatformCommand)

    @Query("DELETE FROM platform_command WHERE command_id=:id")
    abstract override suspend fun remove(id: String)

    @Query("DELETE FROM platform_command")
    abstract override suspend fun removeAll()


    @Query("SELECT * FROM platform_command")
    abstract suspend fun synchronousRoomCommands(): List<RoomPlatformCommand>
    @Transaction
    open suspend fun removeForPlatformTransaction(id: PlatformId){
        synchronousRoomCommands()
            .map { roomCommand ->
                val command = roomCommand.toPlatformCommand()
                val fieldList = ProtobufUtils.getFieldsWithName(command, "platformId")
                val platformIdOfCommand = if(fieldList.isNotEmpty()){
                    PlatformId((fieldList.first() as ByteString).toByteArray())
                }else{
                    DomainId.nil()
                }
                if(platformIdOfCommand == id){
                    remove(roomCommand.command_id.toString())
                }
            }
    }

    override suspend fun removeForPlatform(id: PlatformId) {
        return removeForPlatformTransaction(id)
    }

    override suspend fun commands(): List<PlatformCommands.PlatformMessage> {
        return roomCommands().map { it.toPlatformCommand() }
    }

    @Query("SELECT date, event FROM platform_command WHERE date > :startDate AND date < :endDate ORDER BY date ASC;")
    abstract override suspend fun platformEventsByDateRange(
        startDate: Instant,
        endDate: Instant
    ): List<PlatformCommandDataStore.PlatformExecReportEvent>

    override suspend fun add(command: PlatformCommands.PlatformMessage, event: String) {
        add(RoomPlatformCommand.fromPlatformCommand(command, event))
    }
}