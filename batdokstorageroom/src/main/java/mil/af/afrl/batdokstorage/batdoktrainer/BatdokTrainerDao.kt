package mil.af.afrl.batdokstorage.batdoktrainer

import androidx.room.*
import mil.af.afrl.batdokdata.id.BatdokTrainerSensorId
import mil.af.afrl.batdokdata.models.batdoktrainer.BatdokTrainerSensorDataStore
import mil.af.afrl.batdokdata.models.batdoktrainer.BatdokTrainerSensor

@Dao
abstract class BatdokTrainerDao: BatdokTrainerSensorDataStore {

    @Insert
    abstract suspend fun create(sensor: RoomBatdokTrainerSensor)
    override suspend fun create(sensor: BatdokTrainerSensor) = create(RoomBatdokTrainerSensor(sensor))

    @Query("SELECT * FROM batdok_trainer")
    abstract suspend fun readRoom(): List<RoomBatdokTrainerSensor>
    override suspend fun read(): List<BatdokTrainerSensor> = readRoom().map { it.toBatdokTrainerSensor() }

    @Update
    abstract suspend fun update(sensor: RoomBatdokTrainerSensor)
    override suspend fun update(sensor: BatdokTrainerSensor) = update(RoomBatdokTrainerSensor(sensor))

    @Query("DELETE FROM batdok_trainer WHERE id=:id")
    abstract override suspend fun delete(id: BatdokTrainerSensorId)
}