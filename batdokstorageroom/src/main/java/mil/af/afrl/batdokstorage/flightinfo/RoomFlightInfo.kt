package mil.af.afrl.batdokstorage.flightinfo

import androidx.room.Entity
import androidx.room.PrimaryKey
import gov.afrl.batdok.encounter.metadata.FlightInfo
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant

@Entity(tableName = "af3899_flight_info")
class RoomFlightInfo(
    @PrimaryKey val id: DomainId,
    var tail: String = "",
    var origin: String = "",
    var destination: String = "",
    var takeoff: Instant? = null,
    var landing: Instant? = null,
    var maxAlt: Long? = null,
    var unit: String = ""
){
    constructor(flightInfo: FlightInfo): this(
        flightInfo.id,
        flightInfo.tail,
        flightInfo.origin,
        flightInfo.destination,
        flightInfo.takeoff,
        flightInfo.landing,
        flightInfo.maxAlt,
        flightInfo.unit,
    )
    fun toFlightInfo() =
        FlightInfo(id.copy(), tail, origin, destination, takeoff, landing, maxAlt, unit)
}