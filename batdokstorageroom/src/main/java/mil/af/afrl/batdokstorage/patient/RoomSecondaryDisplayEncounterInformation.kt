package mil.af.afrl.batdokstorage.patient

import androidx.room.Embedded
import androidx.room.Junction
import androidx.room.Relation
import mil.af.afrl.batdokdata.models.secondarydisplay.SecondaryDisplayEncounterInformation
import mil.af.afrl.batdokstorage.platform.RoomPlatform
import mil.af.afrl.batdokstorage.platform.RoomPlatformPatientRelation

class RoomSecondaryDisplayEncounterInformation(
    @Embedded val roomEncounter: RoomEncounterWithThresholds,
    @Relation(parentColumn = "id", entityColumn = "id",
              associateBy = Junction(
                  RoomPlatformPatientRelation::class,
                  parentColumn = "encounter_id",
                  entityColumn = "platform_id"
              )
    )
    private val roomPlatform: RoomPlatform?) {

    fun toSecondaryDisplayEncounterInformation() = SecondaryDisplayEncounterInformation(
        roomEncounter.toEncounter(),
        roomPlatform?.toPlatform(),
        roomEncounter.latestVital?.LocationVital?.toEncounterVital()
    )
}