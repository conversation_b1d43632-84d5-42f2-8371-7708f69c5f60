package mil.af.afrl.batdokstorage.othertreatments

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import androidx.room.Transaction
import kotlinx.coroutines.flow.Flow
import mil.af.afrl.batdokdata.models.othertreatments.OtherTreatmentsDataStore


@Dao
abstract class OtherTreatmentsDao : OtherTreatmentsDataStore {

    @Query("SELECT name FROM dd1380_other_treatments")
    abstract override suspend fun getOtherTreatments(): List<String>

    @Query("SELECT name FROM dd1380_other_treatments WHERE tab = :tab")
    abstract override suspend fun getOtherTreatmentsByTab(tab: String): List<String>

    @Query("SELECT name FROM dd1380_other_treatments WHERE tab = :tab")
    abstract override fun getOtherTreatmentsFlow(tab: String): Flow<List<String>>
    @Insert
    abstract suspend fun setRoomOtherTreatments(otherTreatments: List<RoomOtherTreatment>)

    @Insert
    abstract suspend fun insertOtherTreatment(otherTreatments: RoomOtherTreatment)

    @Query("DELETE FROM dd1380_other_treatments WHERE tab = :tab")
    abstract suspend fun deleteAll(tab: String)
    @Transaction
    override suspend fun setOtherTreatments(otherTreatments: List<String>, tab: String) {
        deleteAll(tab)
        setRoomOtherTreatments(otherTreatments.map { it -> RoomOtherTreatment(0, it, tab) })
    }
}