package mil.af.afrl.batdokstorage.user

import androidx.room.Entity
import androidx.room.PrimaryKey
import mil.af.afrl.batdokdata.models.user.User
import mil.af.afrl.batman.batdokid.DomainId
import java.security.SecureRandom
import javax.crypto.SecretKeyFactory
import javax.crypto.spec.PBEKeySpec

@Entity
class RoomUser(
    @PrimaryKey val userId: DomainId,
    val callsign: String,
    val salt: ByteArray,
    val hashedPWord: ByteArray,
    val name: String,
    val ssn: String,
    val afscMos: String?,
    val dodId: String,
    val rank: String,
    val unit: String,
    val capability: String
){
    fun updateUserData(user: User) = RoomUser(
        this.userId,
        user.callsign,
        this.salt,
        this.hashedPWord,
        user.name,
        user.ssn,
        user.afscMos,
        user.dodId,
        user.rank,
        user.unit,
        user.capability
    )

    internal constructor(user: User, passwordPair: Pair<ByteArray, ByteArray>): this(
        user.userId,
        user.callsign,
        passwordPair.first,
        passwordPair.second,
        user.name,
        user.ssn,
        user.afscMos,
        user.dodId,
        user.rank,
        user.unit,
        user.capability
    )

    constructor(user: User, password: String): this(user, hashPassword(password))

    fun toUser() = User(
        callsign,
        name,
        ssn,
        afscMos,
        dodId,
        rank,
        unit,
        capability,
        userId
    )

    companion object {
        internal fun hashPassword(pword: String, salt: ByteArray = SecureRandom.getSeed(32)): Pair<ByteArray, ByteArray>{
            val keyspec = PBEKeySpec(pword.toCharArray(), salt, 10000, 256)
            val key = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA256").generateSecret(keyspec)
            return salt to key.encoded
        }
    }
}