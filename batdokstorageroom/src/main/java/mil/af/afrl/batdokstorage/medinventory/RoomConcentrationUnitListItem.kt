package mil.af.afrl.batdokstorage.medinventory

import androidx.room.Entity
import androidx.room.PrimaryKey
import mil.af.afrl.batdokdata.models.medinventory.ConcentrationUnitListItem

@Entity(tableName = "concentration_units")
class RoomConcentrationUnitListItem(
    @JvmField @PrimaryKey(autoGenerate = true) val id: Int,
    @JvmField val unit: String
) {
    constructor(item: ConcentrationUnitListItem): this(item.id, item.unit)
    fun toConcentrationItem() = ConcentrationUnitListItem(id, unit)
}