package mil.af.afrl.batdokstorage.sensor

import androidx.room.Entity
import androidx.room.PrimaryKey
import mil.af.afrl.batdokdata.models.sensor.SensorAliasInfo
import mil.af.afrl.batdokdata.models.sensor.SensorInfo


@Entity(tableName = "sensorAliasInfo")
class RoomSensorAliasInfo(
    @PrimaryKey val address: String,
    val alias: String?,
    val color: String?
) {
    fun toSensorAliasInfo(): SensorAliasInfo = SensorAliasInfo(address, alias, color)

    companion object{
        @JvmStatic
        fun fromSensor(sensor: SensorInfo) =
            RoomSensorAliasInfo(sensor.address, sensor.alias, sensor.color)

        @JvmStatic
        fun fromAliasInfo(info: SensorAliasInfo) =
            RoomSensorAliasInfo(info.address, info.alias, info.color)
    }
}