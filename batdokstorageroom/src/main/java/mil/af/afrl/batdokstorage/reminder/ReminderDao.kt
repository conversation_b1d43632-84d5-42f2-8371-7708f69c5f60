package mil.af.afrl.batdokstorage.reminder

import androidx.room.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import mil.af.afrl.batdokdata.id.EncounterId
import mil.af.afrl.batdokdata.models.reminder.Reminder
import mil.af.afrl.batdokdata.models.reminder.ReminderDataStore
import mil.af.afrl.batman.batdokid.*
import java.time.Duration
import java.time.Instant

@Dao
abstract class ReminderDao: ReminderDataStore {
    @Query("SELECT * FROM reminder")
    abstract fun roomReminders(): Flow<List<RoomReminder>>
    override fun reminders(): Flow<List<Reminder>> = roomReminders().map { reminders -> reminders.map { it.toReminder() } }

    @Insert
    abstract suspend fun insert(reminder: RoomReminder)
    override suspend fun insert(reminder: Reminder) = insert(RoomReminder(reminder))

    @Update
    abstract suspend fun updateReminder(reminder: RoomReminder)
    override suspend fun updateReminder(reminder: Reminder) = updateReminder(RoomReminder(reminder))

    @Query("DELETE FROM reminder WHERE id=:id")
    abstract override suspend fun delete(id: DomainId)

    @Query("DELETE FROM reminder")
    abstract suspend fun deleteAllReminders()

    override suspend fun create(
        encounterId : EncounterId,
        description : String,
        timestamp: Instant?,
        reminderTime: Duration,
        reminderType: String
    ): Reminder {
        val reminder = Reminder(
            DomainId.create(),
            encounterId,
            description,
            timestamp,
            reminderTime,
            reminderType
        )
        insert(reminder)
        return reminder
    }
}