package mil.af.afrl.batdokstorage.medinventory

import androidx.room.Entity
import androidx.room.PrimaryKey
import mil.af.afrl.batdokdata.models.medinventory.MedListItem
import mil.af.afrl.batdokdata.models.medinventory.MedListType

/**
 * The object for a DD1380 Med List Item
 */
/**
 * Constructor
 * @param id the id of the med list item
 * @param type the type of the med list item
 * @param name the name of the med list item
 */
@Entity(tableName = "dd1380_med_list_item")
class RoomMedListItem @JvmOverloads constructor(@field:PrimaryKey(autoGenerate = true) val id: Int,
                                            val type: String,
                                            val name: String,
                                            val mode: String? = null){
    constructor(med: MedListItem): this(med.id, med.type, med.name, med.mode)
    fun toMed() = MedListItem(id, type, name, mode)
}