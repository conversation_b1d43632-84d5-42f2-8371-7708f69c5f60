package mil.af.afrl.batdokstorage.burnsheet

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.ForeignKey.Companion.CASCADE
import androidx.room.PrimaryKey
import mil.af.afrl.batdokdata.id.EncounterId
import mil.af.afrl.batdokdata.models.burnsheet.MedCardBurnSheetInfo
import mil.af.afrl.batdokstorage.patient.RoomEncounter
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant

@Entity(
    tableName = "burn_document",
    foreignKeys = [ForeignKey(
        entity = RoomEncounter::class,
        parentColumns = ["id"],
        childColumns = ["encounter_id"],
        onDelete = CASCADE
    )]
)
class RoomMedCardBurnSheetInfo(
    @ColumnInfo(name = "patient_date") var patientDate: Instant?,
    @ColumnInfo(name = "id") @PrimaryKey var id: DomainId,
    @ColumnInfo(name = "injury_date_time") var injuryDateTime: Instant?,
    @ColumnInfo(name = "eval_date_time") var evaluationDateTime: Instant?,
    @ColumnInfo(name = "ssn") var patientSSN: String?,
    @ColumnInfo(name = "name") var patientName: String?,
    @ColumnInfo(name = "weight") var patientWeightInKGs: Float?,
    @ColumnInfo(name = "tbsa") var percentTBSA: String?,
    @ColumnInfo(name = "rule_of_tens") var ruleOfTens: String?,
    @ColumnInfo(name = "vol") var dailyMax: String?,
    @ColumnInfo(name = "facility") var treatmentFacility: String?,
    @ColumnInfo(name = "encounter_id", index = true) var encounterId: DomainId?,
    @ColumnInfo(name = "patient_display_name") var patientDisplayName: String?,
    @ColumnInfo(name = "contacted_burn_team") var burnTeam: Boolean?,
    @ColumnInfo(name = "unit") var weightUnit: String? = "KG"
) {

    constructor(info: MedCardBurnSheetInfo) : this(
        info.patientDate,
        info.id,
        info.injuryDateTime,
        info.evaluationDateTime,
        info.patientSSN,
        info.patientName,
        info.patientWeightInKGs,
        info.percentTBSA,
        info.ruleOfTens,
        info.dailyMax,
        info.treatmentFacility,
        info.encounterID,
        info.patientDisplayName,
        info.burnTeam,
        info.weightUnit
    )

    fun toMedCardBurnSheetInfo() = MedCardBurnSheetInfo(
        id,
        patientDate,
        injuryDateTime,
        evaluationDateTime,
        patientSSN,
        patientName,
        patientWeightInKGs,
        percentTBSA,
        ruleOfTens,
        dailyMax,
        treatmentFacility,
        encounterId?.copy(),
        patientDisplayName,
        burnTeam,
        weightUnit
    )
}