package mil.af.afrl.batdokstorage.vital

import androidx.room.*
import mil.af.afrl.batdokdata.id.EncounterId
import mil.af.afrl.batdokdata.id.VitalId
import mil.af.afrl.batdokdata.models.vital.LocationEncounterVitals
import mil.af.afrl.batdokdata.models.vital.EncounterVitals
import mil.af.afrl.batdokdata.models.vital.VitalDataStore
import mil.af.afrl.batdokstorage.vital.RoomLocationVital
import mil.af.afrl.batdokstorage.vital.RoomVital
import mil.af.afrl.batdokstorage.vital.VitalConverters
import mil.af.afrl.batman.batdokid.*
import java.time.Instant

@Dao
@TypeConverters(VitalConverters::class)
abstract class VitalDao : VitalDataStore {
    @Transaction
    @RewriteQueriesToDropUnusedColumns
    @Query("SELECT * FROM trends_vitals WHERE owner_id=:encounterId")
    abstract suspend fun roomVitalsByEncounter(encounterId: DomainId): List<RoomVital>
    override suspend fun vitalsByEncounter(encounterId: EncounterId): List<EncounterVitals> = roomVitalsByEncounter(encounterId).map { it.toEncounterVital() }

    @Query("SELECT * FROM trends_vitals WHERE owner_id=:encounterId")
    abstract suspend fun roomLocationVitalsByPatient(encounterId: DomainId): List<RoomLocationVital>
    override suspend fun locationVitalsByEncounter(encounterId: EncounterId): List<LocationEncounterVitals> = roomLocationVitalsByPatient(encounterId).map { it.toLocationPatientVital() }
    @Insert
    abstract suspend fun insert(vital: RoomLocationVital)
    override suspend fun insert(encounterId: EncounterId, vitalId: VitalId, vital: EncounterVitals) = insert(RoomLocationVital(id = vitalId,
            encounterId = encounterId,
            hr = vital.hr?.value,
            spo2 = vital.spo2?.value,
            resp = vital.resp?.value,
            bps = vital.bps?.value,
            bpd = vital.bpd?.value,
            ecg = vital.ecg?.value,
            temp = vital.temp?.value,
            etco2 = vital.etco2?.value,
            ibps = vital.ibps?.value,
            ibpd = vital.ibpd?.value,
            extraVitals = vital.extras,
            timestamp = vital.timestamp
    ))

    override suspend fun insert(
        encounterId: EncounterId,
        vitalId: VitalId,
        vital: LocationEncounterVitals
    ) = insert(RoomLocationVital(id = vitalId,
            encounterId = encounterId,
            hr = vital.hr?.value,
            spo2 = vital.spo2?.value,
            resp = vital.resp?.value,
            bps = vital.bps?.value,
            bpd = vital.bpd?.value,
            ecg = vital.ecg?.value,
            temp = vital.temp?.value,
            etco2 = vital.etco2?.value,
            ibps = vital.ibps?.value,
            ibpd = vital.ibpd?.value,
            extraVitals = vital.extras,
            timestamp = vital.timestamp,
            locationLat = vital.locationLat,
            locationLon = vital.locationLon,
            altitude = vital.altitude
        ))

    @Query("DELETE FROM trends_vitals WHERE owner_id=:encounterId AND timestamp < :earliestDateToKeep")
    abstract suspend fun roomDeleteOldVitals(encounterId: DomainId, earliestDateToKeep: Instant)
    override suspend fun deleteOldVitals(encounterId: EncounterId, earliestDateToKeep: Instant) = roomDeleteOldVitals(encounterId, earliestDateToKeep)
}
