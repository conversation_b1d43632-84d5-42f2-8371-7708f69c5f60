package mil.af.afrl.batdokstorage.platform

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.batman.batdok.infrastructure.network.commands.PlatformCommands
import com.google.protobuf.ByteString
import java.time.Instant

@Entity(tableName = "platform_command")
class RoomPlatformCommand(@PrimaryKey(autoGenerate = true)
                          val command_id: Int,
                          @ColumnInfo(name = "data",typeAffinity = ColumnInfo.BLOB)
                          val data: ByteArray,
                          val date: Instant,
                          @ColumnInfo(defaultValue = "") val event: String): Comparable<RoomPlatformCommand>{
    fun toPlatformCommand(): PlatformCommands.PlatformMessage {
        return commandFromData(data)
    }

    companion object{
        @JvmStatic
        fun fromPlatformCommand(platformCommand: PlatformCommands.PlatformMessage, event: String): RoomPlatformCommand {
            return RoomPlatformCommand(0, platformCommand.toByteArray(), Instant.ofEpochMilli(platformCommand.date), event)
        }
    }

    override fun compareTo(other: RoomPlatformCommand): Int {
        return date.compareTo(other.date)
    }
}

/**
 * Parse Platform Message [data]
 */
fun commandFromData(data: ByteArray): PlatformCommands.PlatformMessage {
    return PlatformCommands.PlatformMessage.parseFrom(ByteString.copyFrom(data))
}