package mil.af.afrl.batdokstorage.sensor

import androidx.room.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import mil.af.afrl.batdokdata.id.SensorId
import mil.af.afrl.batdokdata.models.sensor.SensorAliasInfo
import mil.af.afrl.batdokdata.models.sensor.SensorInfo
import mil.af.afrl.batdokdata.models.sensor.SensorInfoDataStore

@Dao
abstract class SensorInfoDao: SensorInfoDataStore {
    @Query("SELECT * FROM sensorInfo")
    abstract fun roomSensors(): Flow<List<RoomSensorInfoWithAlias>>
    override fun sensorStream() = roomSensors().map { it.map { it.toSensorInfo() } }

    @Insert
    abstract suspend fun add(sensor: RoomSensorInfo)

    @Update
    abstract suspend fun update(sensor: RoomSensorInfo)

    @Query("DELETE FROM sensorInfo WHERE id=:sensorId")
    abstract override suspend fun remove(sensorId: SensorId)

    @Query("DELETE FROM sensorInfo WHERE encounterId is null")
    abstract override suspend fun purgeUnconnectedSensors()

    @Transaction
    override suspend fun add(sensor: SensorInfo) {
        //Only add if none of the addresses are the same
        if(roomSensors().first().none { it.info.address == sensor.address }) {
            add(RoomSensorInfo.fromSensor(sensor))
        }
    }

    override suspend fun update(sensor: SensorInfo) {
        update(RoomSensorInfo.fromSensor(sensor))
    }

    //region Alias IO
    @Query("SELECT * FROM sensorAliasInfo")
    abstract fun roomAliasStream(): Flow<List<RoomSensorAliasInfo>>
    @Insert abstract suspend fun addAlias(alias: RoomSensorAliasInfo)
    @Update abstract suspend fun updateAlias(alias: RoomSensorAliasInfo)
    @Query("DELETE FROM sensorAliasInfo WHERE address = :address")
    abstract suspend fun deleteAlias(address: String)
    @Query("DELETE FROM sensorAliasInfo")
    abstract suspend fun deleteAllAliases()

    override suspend fun addAlias(alias: SensorAliasInfo) {
        addAlias(RoomSensorAliasInfo.fromAliasInfo(alias))
    }

    @Transaction
    override suspend fun setAliasList(aliases: List<SensorAliasInfo>) {
        deleteAllAliases()
        aliases.map { RoomSensorAliasInfo.fromAliasInfo(it) }.forEach { addAlias(it) }
    }

    override fun aliasStream(): Flow<List<SensorAliasInfo>> {
        return roomAliasStream().map { it.map { it.toSensorAliasInfo() } }
    }

    override suspend fun updateAlias(alias: SensorAliasInfo) {
        updateAlias(RoomSensorAliasInfo.fromAliasInfo(alias))
    }

    override suspend fun removeAlias(alias: SensorAliasInfo) {
        deleteAlias(alias.address)
    }
    //endregion
}