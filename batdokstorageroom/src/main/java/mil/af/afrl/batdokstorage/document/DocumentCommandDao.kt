package mil.af.afrl.batdokstorage.document

import androidx.room.*
import gov.afrl.batdok.commands.proto.DocumentCommands
import gov.afrl.batdok.util.combineByEncounter
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import mil.af.afrl.batdokdata.id.EncounterId
import mil.af.afrl.batdokdata.models.document.CommandDataStore
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant


@Dao
abstract class DocumentCommandDao: CommandDataStore {

    @Query("SELECT * FROM command WHERE encounter_id=:id ORDER BY date, user")
    abstract suspend fun commands(id: DomainId): List<RoomDocumentCommand>
    override suspend fun commandsByEncounterId(id: EncounterId) = commands(id).map { it.toEditCommand() }


    /**
     * Query Explanation: "Get all commands where the encounter id is in the list of (encounter ids whose patient ids are equal to (the patient id for the given encounter id) and whose rank is less than or equal to the current encounter's rank (meaning it's an older encounter))"
     */
    @Query("SELECT * FROM command WHERE encounter_id in (SELECT id FROM encounter WHERE patientId = (SELECT patientId FROM encounter WHERE id=:id) AND encounterRank <= (SELECT encounterRank from encounter WHERE id=:id))")
    abstract fun liveRoomCommandsWithHistory(id: EncounterId): Flow<List<RoomDocumentCommand>>
    override fun liveCommandsWithHistory(id: EncounterId) = liveRoomCommandsWithHistory(id).distinctUntilChanged().map { roomCommands ->
        roomCommands
            .map { it.toEditCommand() }
            .combineByEncounter()
            .values
            .toList()
            .filterNotNull()
    }

    @Query("SELECT * FROM command ORDER BY date, user")
    abstract fun liveRoomCommands(): Flow<List<RoomDocumentCommand>>
    override fun liveCommands() = liveRoomCommands().distinctUntilChanged().map { roomCommands -> roomCommands.map { it.toEditCommand() }  }

    @Query("SELECT * FROM command WHERE encounter_id=:id ORDER BY date, user")
    abstract fun liveRoomCommands(id: DomainId): Flow<List<RoomDocumentCommand>>
    override fun liveCommands(id: EncounterId) = liveRoomCommands().distinctUntilChanged().map { roomCommands -> roomCommands.map { it.toEditCommand() }  }

    @Query("SELECT * FROM command WHERE encounter_id=:encounterId AND date > :startDate AND date < :endDate ORDER BY date, user")
    abstract suspend fun roomCommandsByDateRange(encounterId: DomainId, startDate: Instant, endDate: Instant): List<RoomDocumentCommand>

    override suspend fun commandsByDateRange(
        encounterId: EncounterId,
        startDate: Instant,
        endDate: Instant
    ): List<DocumentCommands.DocumentCommand> =
        roomCommandsByDateRange(encounterId, startDate, endDate).map {
            it.toEditCommand()
        }

    @Query("SELECT * FROM command WHERE date > :startDate AND date < :endDate ORDER BY date, user")
    abstract suspend fun roomCommandsByDateRange(startDate: Instant, endDate: Instant): List<RoomDocumentCommand>

    override suspend fun commandsByDateRange(startDate: Instant, endDate: Instant) =
        roomCommandsByDateRange(startDate, endDate).map { it.toEditCommand() }

    @Query("SELECT * FROM command WHERE encounter_id=:encounterId AND date > :startDate ORDER BY date, user")
    abstract suspend fun roomCommandsNewerThan(encounterId: DomainId, startDate: Instant): List<RoomDocumentCommand>

    override suspend fun patientCommandsNewerThan(
        encounterId: EncounterId,
        startDate: Instant
    ) = roomCommandsNewerThan(encounterId, startDate).map { it.toEditCommand() }

    @Query("SELECT * FROM command")
    abstract suspend fun allRoomCommands(): List<RoomDocumentCommand>
    override suspend fun allCommands() = allRoomCommands().map { it.toEditCommand() }

    override suspend fun add(command: DocumentCommands.DocumentCommand) {
        addRoomCommands(RoomDocumentCommand.fromEditCommand(command))
    }

    @Insert
    abstract suspend fun addRoomCommands(commands: List<RoomDocumentCommand>)
    override suspend fun add(commands: List<DocumentCommands.DocumentCommand>) {
        addRoomCommands(commands.flatMap { RoomDocumentCommand.fromEditCommand(it) })
    }

    @Delete
    abstract suspend fun remove(command: RoomDocumentCommand)

    @Query("DELETE FROM command where encounter_id=:id")
    abstract override suspend fun remove(id: EncounterId)
}