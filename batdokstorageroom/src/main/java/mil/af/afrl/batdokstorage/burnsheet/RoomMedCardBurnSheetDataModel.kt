package mil.af.afrl.batdokstorage.burnsheet

import androidx.room.Embedded
import androidx.room.Relation
import mil.af.afrl.batdokdata.models.burnsheet.MedCardBurnSheetDataModel

/**
 * This class contains the information to fill out the Burn Sheet
 */
data class RoomMedCardBurnSheetDataModel(
    @Embedded val info: RoomMedCardBurnSheetInfo,
    @Relation(parentColumn = "id", entityColumn = "owner_id") val flowRowDataModelList: List<RoomMedCardFlowRowDataModel> = listOf()
){
    fun toDataModel() = MedCardBurnSheetDataModel(info.toMedCardBurnSheetInfo(), flowRowDataModelList.map { it.toMedCardFlowRowDataModel() })
}