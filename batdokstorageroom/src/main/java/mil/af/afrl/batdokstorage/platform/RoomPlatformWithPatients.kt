package mil.af.afrl.batdokstorage.platform

import androidx.room.Embedded
import androidx.room.Relation
import mil.af.afrl.batdokdata.models.platform.Platform

class RoomPlatformWithPatients(@Embedded val platform: RoomPlatform?,
                               @Relation(entity = RoomPlatformPatientRelation::class,
                                         parentColumn = "id",
                                         entityColumn = "platform_id")
                               val patients: List<RoomPlatformPatientbyPlatform>){
    fun toPlatform(): Platform? {
        val platform = this.platform?.toPlatform()
        platform?.patients = patients.map { it.toPlatformPatient() }.toMutableList()
        return platform
    }
}