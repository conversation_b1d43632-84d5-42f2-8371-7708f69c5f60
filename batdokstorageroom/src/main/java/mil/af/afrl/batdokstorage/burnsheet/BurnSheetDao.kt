package mil.af.afrl.batdokstorage.burnsheet

import androidx.room.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import mil.af.afrl.batdokdata.models.burnsheet.BurnSheetDataStore
import mil.af.afrl.batdokdata.models.burnsheet.MedCardBurnSheetDataModel
import mil.af.afrl.batdokdata.models.burnsheet.MedCardBurnSheetInfo
import mil.af.afrl.batdokdata.models.burnsheet.MedCardFlowRowDataModel
import mil.af.afrl.batdokdata.models.patient.MY_PATIENT
import mil.af.afrl.batdokdata.models.patient.NETWORK_PATIENT
import mil.af.afrl.batman.batdokid.DomainId
import java.util.UUID

@Dao
abstract class BurnSheetDao : BurnSheetDataStore {
    @Transaction
    @Query("SELECT * FROM burn_document WHERE (encounter_id IN (SELECT id FROM encounter WHERE patient_type IN ($MY_PATIENT, $NETWORK_PATIENT)) OR encounter_id IS NULL)")
    abstract fun liveRoomBurnSheets(): Flow<List<RoomMedCardBurnSheetDataModel>>
    override fun liveBurnSheets(): Flow<List<MedCardBurnSheetDataModel>> = liveRoomBurnSheets()
        .map { models ->
            models.map {it.toDataModel() }
        }

    @Transaction
    @Query("SELECT * FROM burn_document")
    abstract suspend fun roomBurnSheets(): List<RoomMedCardBurnSheetDataModel>
    override suspend fun burnSheets(): List<MedCardBurnSheetDataModel> = roomBurnSheets().map { it.toDataModel() }

    @Insert
    abstract suspend fun insert(info: RoomMedCardBurnSheetInfo)
    override suspend fun insert(info: MedCardBurnSheetInfo) = insert(RoomMedCardBurnSheetInfo(info))

    @Insert
    abstract suspend fun insert(flowModel: RoomMedCardFlowRowDataModel)
    override suspend fun insert(flowModel: MedCardFlowRowDataModel) = insert(RoomMedCardFlowRowDataModel(flowModel))

    override suspend fun insert(dataModel: MedCardBurnSheetDataModel){
        insert(dataModel.info)
    }

    @Update
    abstract suspend fun update(info: RoomMedCardBurnSheetInfo)
    override suspend fun update(info: MedCardBurnSheetInfo) = update(RoomMedCardBurnSheetInfo(info))

    @Update
    abstract suspend fun update(flowModel: RoomMedCardFlowRowDataModel)
    override suspend fun update(flowModel: MedCardFlowRowDataModel) = update(RoomMedCardFlowRowDataModel(flowModel))

    override suspend fun update(dataModel: MedCardBurnSheetDataModel){
        update(dataModel.info)
        for(row in dataModel.flowRowDataModelList){
            update(row)
        }
    }

    @Query("DELETE FROM burn_document WHERE id=:id")
    abstract override suspend fun delete(id: DomainId)

    override fun getLiveIdModelMap(): Flow<HashMap<DomainId, MedCardBurnSheetDataModel>>{
        return liveBurnSheets().map { dataModels -> HashMap(dataModels.associateBy{ it.info.id })}
    }

    @Transaction
    override suspend fun getIdModelMap(): HashMap<DomainId, MedCardBurnSheetDataModel> {
        return HashMap(burnSheets().associateBy{ it.info.id })
    }
}