package mil.af.afrl.batdokstorage.medinventory

import androidx.room.Entity
import androidx.room.PrimaryKey
import mil.af.afrl.batdokdata.models.medinventory.MedUnitListItem

/**
 * The object for a DD1380 Unit
 */
@Entity(tableName = "dd1380_units")
class RoomMedUnitListItem(
    @field:PrimaryKey(autoGenerate = true) val id: Int,
    val unit: String
) {
    constructor(unit: MedUnitListItem): this(unit.id, unit.unit)
    fun toMedUnitListItem() = MedUnitListItem(id, unit)
}