package mil.af.afrl.batdokstorage.patient

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.PrimaryKey
import mil.af.afrl.batdokdata.id.EncounterId
import mil.af.afrl.batdokdata.models.patient.EncounterHandoffData
import mil.af.afrl.batman.batdokid.DomainId

/**
 * Object of data used for patient handoff
 *
 * @property age The patient's age
 * @property stability The patient's current stability (stable or unstable)
 * @property injury Injuries that the patient has
 */
@Entity(tableName = "handoff_data",
        foreignKeys = [ForeignKey(entity = RoomEncounter::class,
                parentColumns = ["id"],
                childColumns = ["id"],
                onDelete = ForeignKey.CASCADE)])
data class RoomEncounterHandoffData @JvmOverloads constructor(@PrimaryKey val id: DomainId,
                                                              val age: String? = null,
                                                              val stability: Boolean? = null,
                                                              val injury: String? = null){
    fun toEncounterHandoffData(): EncounterHandoffData {
        return EncounterHandoffData(id.copy(),age, stability, injury)
    }
}

