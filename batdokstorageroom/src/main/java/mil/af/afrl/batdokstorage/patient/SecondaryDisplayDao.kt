package mil.af.afrl.batdokstorage.patient

import androidx.room.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.map
import mil.af.afrl.batdokdata.id.EncounterId
import mil.af.afrl.batdokdata.models.patient.MY_PATIENT
import mil.af.afrl.batdokdata.models.patient.NETWORK_PATIENT
import mil.af.afrl.batdokdata.models.secondarydisplay.SecondaryDisplayDataStore
import mil.af.afrl.batman.batdokid.DomainId

@Dao
abstract class SecondaryDisplayDao : SecondaryDisplayDataStore {
    @Query("SELECT id FROM encounter WHERE patient_type=$MY_PATIENT OR patient_type=$NETWORK_PATIENT ORDER BY triage DESC")
    abstract fun getRoomEncounterIds(): Flow<List<DomainId>>
    override fun getEncounterIds(): Flow<List<EncounterId>> = getRoomEncounterIds().map { it.map { id -> id.copy() } }

    @Transaction
    @Query("SELECT * FROM encounter WHERE id=:id")
    abstract fun getLiveRoomEncounter(id : DomainId): Flow<RoomSecondaryDisplayEncounterInformation>

    override fun getLiveEncounter(id: EncounterId) = getLiveRoomEncounter(id).filterNotNull().map { it.toSecondaryDisplayEncounterInformation() }
}