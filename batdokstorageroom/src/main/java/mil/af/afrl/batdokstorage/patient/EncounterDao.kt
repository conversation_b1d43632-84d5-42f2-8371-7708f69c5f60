package mil.af.afrl.batdokstorage.patient

import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.Transformations
import androidx.room.*
import com.batman.batdok.infrastructure.network.EndpointId
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import mil.af.afrl.batdokdata.id.EncounterId
import mil.af.afrl.batdokdata.models.patient.*
import mil.af.afrl.batdokdata.models.vital.LocationEncounterVitals
import mil.af.afrl.batdokstorage.encounter.AggregatedRoomEncounter
import mil.af.afrl.batdokstorage.vital.RoomLocationVital
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant
import kotlin.reflect.KProperty
import kotlin.reflect.jvm.isAccessible

@Dao
abstract class EncounterDao : EncounterDataStore {

    @Insert abstract suspend fun insert(encounter: RoomEncounter)
    @Insert abstract suspend fun insert(roomVitalThresholds: RoomVitalThresholds)
    @Insert abstract suspend fun insert(handoffData: RoomEncounterHandoffData)

    @Update abstract suspend fun update(encounter: RoomEncounter)
    @Update abstract suspend fun update(roomVitalThresholds: RoomVitalThresholds)
    @Update abstract suspend fun update(handoffData: RoomEncounterHandoffData)

    @Transaction
    @Query("SELECT * FROM encounter WHERE id=:id")
    internal abstract suspend fun internalUseGetEncounter(id: DomainId): AggregatedRoomEncounter?
    internal suspend fun getEncounter(id: EncounterId): Encounter? {
        return internalUseGetEncounter(id)?.toEncounter()
    }

    @Transaction
    override suspend fun createNew(encounterId: EncounterId): EncounterId {
        insert(RoomEncounter.fromEncounter(EncounterModel(encounterId)))
        insert(RoomVitalThresholds.fromThresholds(encounterId, VitalThresholds()))
        insert(RoomEncounterHandoffData(encounterId))
        return encounterId
    }

    @Transaction
    override suspend fun update(encounterId: EncounterId, block: Encounter.() -> Encounter): Boolean {
        val encounter = getEncounter(encounterId) ?: return false // dont use the stream here. it queries all encounters unnecessarily
        val updatedEncounter = encounter.block()
        update(RoomEncounter.fromEncounter(updatedEncounter.data))
        update(RoomVitalThresholds.fromThresholds(encounterId, updatedEncounter.vitalThresholds))
        with(updatedEncounter.encounterHandoffData){
            update(RoomEncounterHandoffData(encounterId, age, stability, injury))
        }
        return true
    }

    @Transaction
    @Query("SELECT * FROM encounter")
    internal abstract fun roomAggregatedEncounter(): Flow<List<AggregatedRoomEncounter>>

    override fun aggregatedEncounterStream(): Flow<List<Encounter>> = roomAggregatedEncounter().distinctUntilChanged()
        .map { it.map { it.toEncounter() } }

    @Query("DELETE FROM encounter WHERE id=:id")
    abstract suspend fun roomRemove(id : DomainId)
    override suspend fun remove(id : EncounterId) = roomRemove(id)

    //Old, Deprecated functions
    @Transaction
    @Query("SELECT * FROM encounter")
    @Deprecated("Use aggregatedEncounterStream instead")
    abstract suspend fun roomEncounter(): List<RoomEncounterWithThresholds>
    @Transaction
    @Query("SELECT * FROM encounter ORDER BY date ASC ")
    @Deprecated("Use aggregatedEncounterStream instead")
    abstract fun liveRoomEncounters(): Flow<List<RoomEncounterWithThresholds>>

    @Transaction
    @Query("SELECT * FROM encounter")
    @Deprecated("Use aggregatedEncounterStream instead")
    abstract fun roomEncountersFlow(): Flow<List<RoomEncounterWithThresholds>>

    @Deprecated("Use aggregatedEncounterStream instead")
    override fun encountersFlow(): Flow<List<EncounterModel>> {
        throw NotImplementedError("Use aggregatedEncounterStream instead")
    }

    @Transaction
    @Query("SELECT * FROM encounter WHERE id=:id")
    @Deprecated("Use aggregatedEncounterStream instead")
    abstract suspend fun roomEncounter(id : DomainId): RoomEncounterWithThresholds?

    @Query("SELECT * FROM encounter WHERE date > :startDate AND date < :endDate;")
    @Deprecated("Use aggregatedEncounterStream instead")
    abstract suspend fun roomEncountersByDateRange(startDate: Instant, endDate: Instant): List<RoomEncounter>

    @Deprecated("Use aggregatedEncounterStream instead")
    override suspend fun encountersByDateRange(
        startDate: Instant,
        endDate: Instant
    ): List<EncounterModel> {
        throw NotImplementedError("Use aggregatedEncounterStream instead")
    }

    @Query("SELECT owner FROM encounter WHERE id=:id")
    @Deprecated("Use aggregatedEncounterStream instead")
    abstract suspend fun getRoomOwner(id : DomainId): DomainId?
    @Deprecated("Use aggregatedEncounterStream instead")
    override suspend fun getOwner(id : EncounterId): EndpointId?{
        throw NotImplementedError("Use aggregatedEncounterStream instead")
    }


    @Query("UPDATE encounter SET owner = :newOwner WHERE id=:id")
    @Deprecated("Use aggregatedEncounterStream instead")
    abstract suspend fun setRoomOwner(id : DomainId, newOwner: String)
    @Deprecated("Use aggregatedEncounterStream instead")
    override suspend fun setOwner(id : EncounterId, newOwner: String) {
        throw NotImplementedError("Use aggregatedEncounterStream instead")
    }


    @Query("SELECT commandChecksum FROM encounter WHERE id=:id")
    @Deprecated("Use aggregatedEncounterStream instead")
    abstract suspend fun roomChecksum(id : DomainId): Byte?
    @Deprecated("Use aggregatedEncounterStream instead")
    override suspend fun checksum(id : EncounterId): Byte? {
        throw NotImplementedError("Use aggregatedEncounterStream instead")
    }

    @Query("SELECT EXISTS (SELECT 1 FROM encounter WHERE id=:id)")
    abstract suspend fun roomExists(id : DomainId): Boolean

    @Deprecated("Undeprecate this method. it is a critical one for optimization")
    override suspend fun exists(id : EncounterId): Boolean = roomExists(id)

    @Query("UPDATE encounter SET commandChecksum = :newChecksum WHERE id=:id")
    @Deprecated("Use aggregatedEncounterStream instead")
    abstract suspend fun roomUpdateChecksum(id : DomainId, newChecksum: Byte)
    @Deprecated("Use aggregatedEncounterStream instead")
    override suspend fun updateChecksum(id : EncounterId, newChecksum: Byte) {
        throw NotImplementedError("Use aggregatedEncounterStream instead")
    }

    @Query("SELECT * FROM trends_vitals WHERE owner_id=:id ORDER BY timestamp DESC LIMIT 1;")
    abstract suspend fun roomLastVitalRow(id : DomainId): RoomLocationVital?
    suspend fun lastVitalRow(id : EncounterId): LocationEncounterVitals?{
        throw NotImplementedError("Use aggregatedEncounterStream instead")
    }

    @Deprecated("Use aggregatedEncounterStream instead")
    override suspend fun encounters(): List<EncounterModel> {
        throw NotImplementedError("Use aggregatedEncounterStream instead")
    }

    @Deprecated("Use aggregatedEncounterStream instead")
    override fun liveEncounters(): Flow<List<EncounterModel>> {
        throw NotImplementedError("Use aggregatedEncounterStream instead")
    }

    @Deprecated("Use aggregatedEncounterStream instead")
    override suspend fun encounter(id: EncounterId): EncounterModel? {
        throw NotImplementedError("Use aggregatedEncounterStream instead")
    }

    @Deprecated("Use aggregatedEncounterStream instead")
    override fun encounterFlow(id: EncounterId): Flow<EncounterModel?> {
        throw NotImplementedError("Use aggregatedEncounterStream instead")
    }

    @Transaction
    @Deprecated("Use createNew() the update instead")
    override suspend fun add(encounter: EncounterModel) {
        throw NotImplementedError("Use aggregatedEncounterStream instead")
    }

    /**
     * this method is handles threaded writes where
     * repo ->> thread1: read object1
     * repo ->> thread2: read object1
     * thread1 ->> object1a: update field
     * thread2 ->> object1b: update field
     * thread1 ->> repo: write object1a
     * thread2 ->> repo: write object1b
     *
     * would cause the encounter to be overwritten with wrong values
     * instead it will read current state of encounter then update the current state and then update
     */
    @Transaction
    @Deprecated("Use update(EncounterId, Encounter.() -> Encounter) instead")
    override suspend fun update(encounter: EncounterModel): Boolean {
            throw NotImplementedError("Use aggregatedEncounterStream instead")
    }

    @Deprecated("Use update(EncounterId, Encounter.() -> Encounter) instead")
    override suspend fun updateHandoff(id: EncounterId, handoffData: EncounterHandoffData) {
        throw NotImplementedError("Use aggregatedEncounterStream instead")
    }

    @Query("SELECT * FROM handoff_data WHERE id=:id")
    @Deprecated("Use aggregatedEncounterStream instead")
    abstract suspend fun getRoomHandoffData(id : DomainId): RoomEncounterHandoffData?
    @Deprecated("Use aggregatedEncounterStream instead")
    override suspend fun getHandoffData(id : EncounterId): EncounterHandoffData? {
        throw NotImplementedError("Use aggregatedEncounterStream instead")
    }


    @Query("SELECT * FROM handoff_data WHERE id=:id")
    @Deprecated("Use aggregatedEncounterStream instead")
    abstract fun getRoomLiveHandoffData(id : DomainId): LiveData<RoomEncounterHandoffData>
    @Deprecated("Use aggregatedEncounterStream instead")
    override fun getLiveHandoffData(id : EncounterId): LiveData<EncounterHandoffData> {
        throw NotImplementedError("Use aggregatedEncounterStream instead")
    }

    @Query("SELECT * FROM handoff_data WHERE id=:id")
    @Deprecated("Use aggregatedEncounterStream instead")
    abstract fun getRoomFlowHandoffData(id : DomainId): Flow<RoomEncounterHandoffData>
    @Deprecated("Use aggregatedEncounterStream instead")
    override fun getFlowHandoffData(id : EncounterId): Flow<EncounterHandoffData> {
        throw NotImplementedError("Use aggregatedEncounterStream instead")
    }
}
