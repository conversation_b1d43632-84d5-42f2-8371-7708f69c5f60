package mil.af.afrl.batdokstorage.database

import androidx.room.Database
import androidx.room.RoomDatabase
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import com.batman.documentcommandslibrary.command.util.PlatformStatus
import mil.af.afrl.batdokdata.models.patient.EncounterState
import mil.af.afrl.batdokdata.models.patient.ExportStatus
import mil.af.afrl.batdokstorage.a2a.A2ADao
import mil.af.afrl.batdokstorage.a2a.RoomA2AApplication
import mil.af.afrl.batdokstorage.allergy.OtherAllergyDao
import mil.af.afrl.batdokstorage.allergy.RoomOtherAllergy
import mil.af.afrl.batdokstorage.batdoktrainer.BatdokTrainerDao
import mil.af.afrl.batdokstorage.batdoktrainer.RoomBatdokTrainerSensor
import mil.af.afrl.batdokstorage.burnsheet.BurnSheetDao
import mil.af.afrl.batdokstorage.burnsheet.RoomMedCardBurnSheetInfo
import mil.af.afrl.batdokstorage.burnsheet.RoomMedCardFlowRowDataModel
import mil.af.afrl.batdokstorage.ccp.CasevacDao
import mil.af.afrl.batdokstorage.ccp.RoomCasevac
import mil.af.afrl.batdokstorage.chat.ChatMessageDao
import mil.af.afrl.batdokstorage.chat.RoomChatDraft
import mil.af.afrl.batdokstorage.chat.RoomChatMessage
import mil.af.afrl.batdokstorage.checklistitem.ChecklistItemDao
import mil.af.afrl.batdokstorage.checklistitem.RoomCheckListModel
import mil.af.afrl.batdokstorage.document.DocumentCommandDao
import mil.af.afrl.batdokstorage.document.DocumentQrCodeDao
import mil.af.afrl.batdokstorage.document.RoomDocumentCommand
import mil.af.afrl.batdokstorage.document.RoomQrCode
import mil.af.afrl.batdokstorage.encryptionkey.EncryptionKeyDao
import mil.af.afrl.batdokstorage.encryptionkey.RoomEncryptionKey
import mil.af.afrl.batdokstorage.medinventory.*
import mil.af.afrl.batdokstorage.othertreatments.RoomUnassignedTreatTabItem
import mil.af.afrl.batdokstorage.othertreatments.UnassignedTreatTabItemDao
import mil.af.afrl.batdokstorage.patient.*
import mil.af.afrl.batdokstorage.platform.*
import mil.af.afrl.batdokstorage.quality.QualityDao
import mil.af.afrl.batdokstorage.quality.RoomQuality
import mil.af.afrl.batdokstorage.reminder.ReminderDao
import mil.af.afrl.batdokstorage.reminder.RoomReminder
import mil.af.afrl.batdokstorage.roster.RoomRosterEntry
import mil.af.afrl.batdokstorage.roster.RosterDao
import mil.af.afrl.batdokstorage.sensor.RoomSensor
import mil.af.afrl.batdokstorage.sensor.RoomSensorAliasInfo
import mil.af.afrl.batdokstorage.sensor.RoomSensorInfo
import mil.af.afrl.batdokstorage.sensor.SensorDao
import mil.af.afrl.batdokstorage.sensor.SensorInfoDao
import mil.af.afrl.batdokstorage.treatment.RoomUnattachedMedicine
import mil.af.afrl.batdokstorage.treatment.UnattachedMedicineDao
import mil.af.afrl.batdokstorage.user.RoomUser
import mil.af.afrl.batdokstorage.user.UserDao
import mil.af.afrl.batdokstorage.vital.LatestVital
import mil.af.afrl.batdokstorage.vital.RoomLocationVital
import mil.af.afrl.batdokstorage.vital.VitalDao
import mil.af.afrl.batman.batdokid.DomainId
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.ObjectInputStream
import java.io.ObjectOutputStream
import java.time.Duration
import java.time.Instant
import java.time.LocalDate
import java.util.Arrays

@Database(
    entities = [
        RoomBatdokTrainerSensor::class, RoomQrCode::class, RoomEncryptionKey::class, RoomEncounter::class,
        RoomPlatformCommand::class, RoomUser::class, RoomMedRouteListItem::class, RoomUnassignedTreatTabItem::class,
        RoomMedUnitListItem::class, RoomConcentrationUnitListItem::class, RoomPlatform::class, RoomSensor::class, RoomSensorInfo::class,
        RoomLocationVital::class, RoomReminder::class, RoomEncounterHandoffData::class, RoomCasevac::class, RoomMedCardBurnSheetInfo::class,
        RoomMedCardFlowRowDataModel::class, RoomSavedFastMed::class, RoomVitalThresholds::class, RoomMedListItem::class,
        RoomCombatPillPack::class, RoomPlatformPatientRelation::class, RoomDocumentCommand::class,
        RoomCheckListModel::class, RoomUnattachedMedicine::class, RoomOtherAllergy::class, RoomRosterEntry::class,
        RoomChatMessage::class, RoomChatDraft::class, RoomQuality::class, RoomA2AApplication::class, RoomSensorAliasInfo::class
    ],
    views = [LatestVital::class], version = BatdokMigrationBuilder.VERSION
)
@TypeConverters(
    BatdokDatabase.BasicConverters::class, BatdokDatabase.DomainIdConverters::class
)
abstract class BatdokDatabase: RoomDatabase() {
    abstract val encounterDao: EncounterDao
    abstract val medInventoryDao: MedInventoryDao?
    abstract val batdokTrainerDao: BatdokTrainerDao?
    abstract val casevacDao: CasevacDao?
    abstract val documentCommandDao: DocumentCommandDao
    abstract val documentQrCodeDao: DocumentQrCodeDao?
    abstract val encryptionKeyDao: EncryptionKeyDao
    abstract val otherAllergyDao: OtherAllergyDao?
    abstract val platformCommandDao: PlatformCommandDao?
    abstract val reminderDao: ReminderDao?
    abstract val sensorDao: SensorDao?
    abstract val sensorInfoDao: SensorInfoDao
    abstract val unassignedTreatTabItemDao: UnassignedTreatTabItemDao
    abstract val vitalDao: VitalDao?
    abstract val qualityDao: QualityDao?
    abstract val burnSheetDao: BurnSheetDao?
    abstract val platformDao: PlatformDao?
    abstract val secondaryDisplayDao: SecondaryDisplayDao?
    abstract val checklistItemDao: ChecklistItemDao
    abstract val rosterDao: RosterDao
    abstract val chatMessageDao: ChatMessageDao
    abstract val unattachedTreatmentDao: UnattachedMedicineDao?
    abstract val a2ADao: A2ADao?
    abstract val userDao: UserDao

    object BasicConverters {
        @JvmStatic
        @TypeConverter
        fun fromTimestamp(value: Long?): Instant? {
            return value?.let { Instant.ofEpochSecond(value) }
        }

        @JvmStatic
        @TypeConverter
        fun instantToTimestamp(date: Instant?): Long? {
            return date?.epochSecond
        }
        @JvmStatic
        @TypeConverter
        fun fromDate(value: Long?): LocalDate? {
            return value?.let { LocalDate.ofEpochDay(value) }
        }

        @JvmStatic
        @TypeConverter
        fun localDateToTimestamp(date: LocalDate?): Long? {
            return date?.toEpochDay()
        }

        @JvmStatic
        @TypeConverter
        fun fromDuration(value: Long?): Duration? {
            return value?.let { Duration.ofMillis(it) }
        }

        @JvmStatic
        @TypeConverter
        fun toDuration(duration: Duration?): Long? {
            return duration?.toMillis()
        }

        @TypeConverter
        fun toString(status: PlatformStatus?): String? {
            return status?.name
        }

        @TypeConverter
        fun platformStatusfromString(status: String?): PlatformStatus? {
            return status?.let { PlatformStatus.valueOf(it) }
        }

        @TypeConverter
        fun toString(state: EncounterState?): String? {
            return state?.name
        }

        @TypeConverter
        fun encounterStateFromString(status: String?): EncounterState? {
            return status?.let { EncounterState.valueOf(it) }
        }

        @TypeConverter
        fun toString(strings: List<String?>?): String? {
            if (strings == null) {
                return null
            }
            val builder = StringBuilder()
            for (string in strings) {
                if (builder.length != 0) {
                    builder.append(",")
                }
                builder.append(string)
            }
            return builder.toString()
        }

        @JvmStatic
        @TypeConverter
        fun stringListfromString(joinedString: String?): List<String>? {
            if (joinedString == null) {
                return null
            }
            return if (joinedString.isEmpty()) {
                ArrayList()
            } else Arrays.asList(*joinedString.split(",").toTypedArray())
        }

        @TypeConverter
        fun toString(data: Int): String {
            return "" + data
        }

        @JvmStatic
        @TypeConverter
        fun intFromString(string: String): Int {
            return string.toInt()
        }

        @JvmStatic
        @TypeConverter
        fun toString(data: Float): String {
            return "" + data
        }

        @JvmStatic
        @TypeConverter
        fun floatFromString(string: String): Float {
            return string.toFloat()
        }

        @JvmStatic
        @TypeConverter
        fun toBlob(data: Map<String, String>?): ByteArray? {
            data ?: return null
            return ByteArrayOutputStream().use { baos ->
                ObjectOutputStream(baos).use { oos ->
                    oos.writeObject(HashMap(data))
                }
                baos.toByteArray()
            }
        }

        @JvmStatic
        @TypeConverter
        fun mapFromBlob(blob: ByteArray?): Map<String, String>? {
            blob ?: return null
            return ByteArrayInputStream(blob).use { bais ->
                ObjectInputStream(bais).use { ois ->
                    val obj = ois.readObject()
                    (obj as Map<*, *>)
                        .mapValues { it.value.toString() }
                        .mapKeys { it.key.toString() }
                }
            }
        }

        @JvmStatic
        @TypeConverter
        fun toString(data: IntArray?): String? {
            data ?: return null
            return "" + data.joinToString(",")
        }

        @JvmStatic
        @TypeConverter
        fun intArrayFromString(string: String?): IntArray? {
            string ?: return null
            return string.split(",").mapNotNull { it.toIntOrNull() }.toIntArray()
        }

        @TypeConverter
        fun exportStatusToString(exportStatus: ExportStatus): ByteArray {
            return ByteArrayOutputStream().use { baos ->
                ObjectOutputStream(baos).use { oos ->
                    oos.writeObject(exportStatus.getExports())
                }
                baos.toByteArray()
            }
        }

        @TypeConverter
        fun stringToExportStatus(map: ByteArray): ExportStatus {
            return ByteArrayInputStream(map).use { bais ->
                ObjectInputStream(bais).use { ois ->
                    val obj = ois.readObject()
                    val map = (obj as Map<String, Pair<Int, Instant>>)
                    return ExportStatus.fromExportWithTime(map)
                }
            }
        }

    }

    object DomainIdConverters {
        @JvmStatic
        @TypeConverter
        fun from(domainId: DomainId?): ByteArray? {
            return domainId?.unique
        }

        @JvmStatic
        @TypeConverter
        fun toDomainId(bytes: ByteArray?): DomainId? {
            return bytes?.let { DomainId(it) }
        }
    }
}