package mil.af.afrl.batdokstorage.chat

import androidx.room.Entity
import androidx.room.PrimaryKey
import mil.af.afrl.batdokdata.models.chat.ChatDraft

@Entity(tableName = "chat_drafts")
class RoomChatDraft(@PrimaryKey val conversationId: String, val message: String){
    constructor(draft: ChatDraft): this(draft.conversationId, draft.message)
    fun toChatDraft() = ChatDraft(conversationId, message)
}