package mil.af.afrl.batdokstorage.platform

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import mil.af.afrl.batdokdata.id.EncounterId
import mil.af.afrl.batdokdata.id.PlatformId
import mil.af.afrl.batdokstorage.patient.RoomEncounter
import mil.af.afrl.batman.batdokid.DomainId

@Entity(tableName = "platform_patient_relation",
        foreignKeys = [ForeignKey(entity = RoomEncounter::class,
                parentColumns = ["id"],
                childColumns = ["encounter_id"],
                onDelete = ForeignKey.CASCADE), ForeignKey(entity = RoomPlatform::class,
                                                           parentColumns = ["id"],
                                                           childColumns = ["platform_id"],
                                                           onDelete = ForeignKey.CASCADE)],
        indices = [Index(value = ["encounter_id"]),Index(value=["platform_id"])])
class RoomPlatformPatientRelation(@PrimaryKey val encounter_id : DomainId,
                                  val platform_id: DomainId?,
                                  var patient_rank: Int){
}