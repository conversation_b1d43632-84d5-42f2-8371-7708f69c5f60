package mil.af.afrl.batdokstorage.allergy

import androidx.room.*
import kotlinx.coroutines.flow.Flow
import mil.af.afrl.batdokdata.models.info.allergy.OtherAllergyDataStore

@Dao
abstract class OtherAllergyDao : OtherAllergyDataStore {
    @Query("SELECT name FROM dd1380_other_allergy")
    abstract override suspend fun getOtherAllergies(): List<String>
    @Query("SELECT name FROM dd1380_other_allergy")
    abstract override fun getOtherAllergiesFlow(): Flow<List<String>>

    @Query("SELECT * FROM dd1380_other_allergy WHERE name=:otherAllergy")
    abstract suspend fun getAllergy(otherAllergy: String): RoomOtherAllergy

    @Insert
    abstract suspend fun addRoomOtherAllergy(otherAllergy: RoomOtherAllergy)

    @Delete
    abstract suspend fun deleteRoomOtherAllergy(otherAllergy: RoomOtherAllergy)
    @Transaction
    override suspend fun addOtherAllergy(otherAllergy: String) {
        if (otherAllergy !in getOtherAllergies()) {
            addRoomOtherAllergy(RoomOtherAllergy(otherAllergy))
        }
    }
    @Transaction
    override suspend fun deleteOtherAllergy(otherAllergy: String) {
        if (otherAllergy in getOtherAllergies()) {
            deleteRoomOtherAllergy(getAllergy(otherAllergy))
        }
    }

}
