package mil.af.afrl.batdokstorage.ccp

import androidx.room.ColumnInfo
import androidx.room.Entity
import mil.af.afrl.batdokdata.models.ccp.Casevac

/**
 * Created on 1/25/18.
 */
@Entity(tableName = "casevac", primaryKeys = ["callsign", "type"])
class RoomCasevac(val callsign: String = "",
                  val type: String = "",
                  @ColumnInfo(name= "tutorial", defaultValue = "0") var isTutorialCasevac : Boolean? = false) {
    constructor(casevac: Casevac): this(casevac.callsign, casevac.type, casevac.isTutorialCasevac?: false)
    fun toCasevac() = Casevac(callsign, type, isTutorialCasevac)
}
