package mil.af.afrl.batdokstorage.checklistitem

import androidx.room.Entity
import androidx.room.PrimaryKey
import mil.af.afrl.batdokdata.models.checklistitem.CheckListModel

@Entity(tableName = "checklist_item")
class RoomCheckListModel(@PrimaryKey var name: String,
                         var interval: String,
                         var category: String?,
                         var isHidden: Boolean,
                         var isCustom: Boolean   ) {

    constructor(model: CheckListModel): this(model.name, model.interval, model.category, model.isHidden,
        model.customItem)
    fun toCheckListModel(): CheckListModel {
        return CheckListModel(name, interval, category, isHidden, isCustom)
    }
}