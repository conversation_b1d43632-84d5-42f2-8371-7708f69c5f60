package mil.af.afrl.batdokstorage.allergy

import androidx.room.Dao
import androidx.room.Query
import mil.af.afrl.batdokdata.models.allergy.AllergyDataStore
import mil.af.afrl.batdokdata.models.allergy.MultumAllergy

@Dao
interface AllergyDao : AllergyDataStore {
    override suspend fun findAllergiesContaining(query: String) = roomFindAllergies(query).map { it.toMultumAllergy() }

    @Query("SELECT * FROM Allergies_table WHERE term LIKE '%' || :searchQuery || '%'")
    suspend fun roomFindAllergies(searchQuery: String): List<RoomAllergy>

    override suspend fun findAllergy(name: String) = roomFindAllergy(name)?.toMultumAllergy()

    @Query("SELECT * FROM Allergies_table WHERE term IS :name COLLATE NOCASE")
    suspend fun roomFindAllergy(name: String) : RoomAllergy?

    override suspend fun findAllergiesByCode(code: String) = roomFindAllergiesByCode(code).map { it.toMultumAllergy() }

    @Query("SELECT * FROM Allergies_table WHERE code IS :code")
    suspend fun roomFindAllergiesByCode(code: String) : List<RoomAllergy>

    override suspend fun getAllAllergies() = roomGetAllAllergies().map { it.toMultumAllergy() }

    @Query("SELECT * FROM Allergies_table")
    suspend fun roomGetAllAllergies(): List<RoomAllergy>
}
