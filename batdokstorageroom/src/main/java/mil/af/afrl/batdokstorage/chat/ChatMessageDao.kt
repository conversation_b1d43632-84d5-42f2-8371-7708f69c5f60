package mil.af.afrl.batdokstorage.chat

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import mil.af.afrl.batdokdata.models.chat.ChatMessageDataStore
import androidx.room.*
import mil.af.afrl.batdokdata.models.chat.ChatDraft
import mil.af.afrl.batdokdata.models.chat.ChatMessage
import mil.af.afrl.batdokdata.models.chat.DBChatParticipant
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

@Dao
abstract class ChatMessageDao: ChatMessageDataStore {
    @Query("SELECT * FROM chat_messages WHERE conversationId = :conversationId")
    abstract fun roomChatMessagesForConversation(conversationId: String): Flow<List<RoomChatMessage>>
    override fun chatMessagesForConversation(conversationId: String): Flow<List<ChatMessage>> = roomChatMessagesForConversation(conversationId).map { list -> list.map { it.toChatMessage() } }

    @Query("SELECT conversationId, SUM(CASE WHEN read = 0 THEN 1 ELSE 0 END) AS newMessageCount FROM chat_messages " +
            "GROUP BY conversationId " +
            "ORDER BY MAX(timeStamp) DESC")
    abstract override fun conversationIdsOrderedByLastMessageTimeStamp(): Flow<List<DBChatParticipant>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun upsert(message: RoomChatMessage)
    override suspend fun upsert(message: ChatMessage) = upsert(RoomChatMessage(message))

    @Query("DELETE FROM chat_messages WHERE conversationId = :conversationId")
    abstract override suspend fun deleteConversation(conversationId: String)

    @Query("UPDATE chat_messages SET conversationId = :newConversationId WHERE conversationId = :oldConversationId")
    abstract suspend fun updateChatMessageConversationIds(oldConversationId: String, newConversationId: String)

    @Query("UPDATE chat_messages SET read = 1 WHERE conversationId = :conversationId AND read = 0")
    abstract override suspend fun updateConversationsAsRead(conversationId: String)

    @Query("SELECT EXISTS(SELECT * FROM chat_messages WHERE read = 0)")
    abstract override fun areMessagesUnread(): Flow<Boolean>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun saveDraftMessageForConversation(draft: RoomChatDraft)
    override suspend fun saveDraftMessageForConversation(draft: ChatDraft) = saveDraftMessageForConversation(RoomChatDraft(draft))

    @Query("SELECT * FROM chat_drafts WHERE conversationId = :conversationId")
    abstract override suspend fun getDraftMessageForConversation(conversationId: String): ChatDraft?

    @Query("DELETE FROM chat_drafts WHERE conversationId = :conversationId")
    abstract override suspend fun deleteDraftMessageForConversation(conversationId: String)

    @Query("UPDATE chat_drafts SET conversationId = :newConversationId WHERE conversationId = :oldConversationId")
    abstract suspend fun updateDraftConversationIds(oldConversationId: String, newConversationId: String)

    @Transaction
    override suspend fun updateConversationIds(oldConversationId: String, newConversationId: String) {
        updateChatMessageConversationIds(oldConversationId, newConversationId)
        updateDraftConversationIds(oldConversationId, newConversationId)
    }
}