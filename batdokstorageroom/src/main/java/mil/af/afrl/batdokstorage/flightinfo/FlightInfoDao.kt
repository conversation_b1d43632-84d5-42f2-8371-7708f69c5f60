package mil.af.afrl.batdokstorage.flightinfo

import androidx.room.*
import gov.afrl.batdok.encounter.metadata.FlightInfo
import gov.afrl.batdok.encounter.metadata.FlightInfoId
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import mil.af.afrl.batdokdata.models.flightinfo.FlightInfoDataStore

@Dao
abstract class FlightInfoDao: FlightInfoDataStore {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insert(flight: RoomFlightInfo)
    override suspend fun insert(flight: FlightInfo) = insert(RoomFlightInfo(flight))

    @Update
    abstract suspend fun update(flight: RoomFlightInfo)
    override suspend fun update(flight: FlightInfo) = update(RoomFlightInfo(flight))

    @Delete
    abstract suspend fun delete(flightId: RoomFlightInfo)
    override suspend fun delete(flightId: FlightInfo) = delete(RoomFlightInfo(flightId))

    @Query("SELECT * FROM af3899_flight_info")
    abstract fun getLiveRoomFlights(): Flow<List<RoomFlightInfo>>
    override fun getLiveFlights() = getLiveRoomFlights().map { flights -> flights.map { it.toFlightInfo() } }

    @Query("SELECT * FROM af3899_flight_info WHERE id=:flightId")
    abstract suspend fun getRoomFlightById(flightId: FlightInfoId): RoomFlightInfo?
    override suspend fun getById(flightId: FlightInfoId) = getRoomFlightById(flightId)?.toFlightInfo()

    override suspend fun flightExists(flightId: FlightInfoId): Boolean {
        return getById(flightId) != null
    }
}