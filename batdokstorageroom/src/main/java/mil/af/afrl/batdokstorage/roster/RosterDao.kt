package mil.af.afrl.batdokstorage.roster

import androidx.room.*
import kotlinx.coroutines.flow.Flow
import mil.af.afrl.batdokdata.models.roster.RosterDataStore
import mil.af.afrl.batdokdata.models.roster.RosterEntry
import mil.af.afrl.batdokdata.models.roster.RosterWithBloodTypes

@Dao
abstract class RosterDao: RosterDataStore {

    @Query("SELECT * FROM roster")
    abstract override suspend fun getRosters(): List<RosterEntry>

    @Query("SELECT * FROM roster")
    abstract override fun getLiveRosters(): Flow<List<RosterEntry>>

    @Query("SELECT first, last, bloodType, bloodDate, bloodLT FROM roster")
    abstract override suspend  fun getRostersWithBloodTypes(): List<RosterWithBloodTypes>

    @Query("SELECT first, last, bloodType, bloodDate, bloodLT FROM roster")
    abstract override fun getLiveRostersWithBloodTypes(): Flow<List<RosterWithBloodTypes>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun addRoomRosters(rosters: List<RoomRosterEntry>)
    override suspend fun addRosters(rosters: List<RosterEntry>) = addRoomRosters(rosters.map { RoomRosterEntry(it) })

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun addRoomRostersTakeChanges(rosters: List<RoomRosterEntry>)
    override suspend fun addRostersTakeChanges(rosters: List<RosterEntry>) = addRoomRostersTakeChanges(rosters.map { RoomRosterEntry(it) })

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    abstract suspend fun addRoomRostersIgnoreChanges(rosters: List<RoomRosterEntry>)
    override suspend fun addRostersIgnoreChanges(rosters: List<RosterEntry>) = addRoomRostersIgnoreChanges(rosters.map { RoomRosterEntry(it) })


    @Transaction
    override suspend fun overwriteRosters(newRosters: List<RosterEntry>) {
        removeAllRosters()
        addRosters(newRosters)
    }

    @Query("DELETE FROM roster")
    abstract override suspend fun removeAllRosters()

    @Delete
    abstract suspend fun removeRoomRoster(roster: RoomRosterEntry)
    override suspend fun removeRoster(roster: RosterEntry) = removeRoomRoster(RoomRosterEntry(roster))
}