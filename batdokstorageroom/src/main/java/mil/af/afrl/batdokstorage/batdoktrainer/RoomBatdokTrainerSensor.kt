package mil.af.afrl.batdokstorage.batdoktrainer

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import mil.af.afrl.batdokdata.id.BatdokTrainerSensorId
import mil.af.afrl.batdokdata.models.batdoktrainer.BatdokTrainerSensor
import mil.af.afrl.batdokdata.models.batdoktrainer.NOT_AVAILABLE
import mil.af.afrl.batman.batdokid.DomainId
import java.io.Serializable

/**
 * A [RoomBatdokTrainerSensor] is a data class which holds a sensor's current slider values,
 * the enabled vitals, and the current vital.
 */
@Entity(tableName = "batdok_trainer")
data class RoomBatdokTrainerSensor(@ColumnInfo(name="id") @PrimaryKey val id: DomainId = DomainId.create(),
                                   @ColumnInfo(name="name") var name: String,
                                   @ColumnInfo(name="hr", typeAffinity = ColumnInfo.TEXT) var hr: Int = 60,
                                   @ColumnInfo(name="bps", typeAffinity = ColumnInfo.TEXT) var bps: Int = 120,
                                   @ColumnInfo(name="bpd", typeAffinity = ColumnInfo.TEXT) var bpd: Int = 80,
                                   @ColumnInfo(name="rr", typeAffinity = ColumnInfo.TEXT) var rr: Int = 8,
                                   @ColumnInfo(name="spo2", typeAffinity = ColumnInfo.TEXT) var spo2: Int = 98,
                                   @ColumnInfo(name="etco2", typeAffinity = ColumnInfo.TEXT) var etco2: Int = 40,
                                   @ColumnInfo(name="ibps", typeAffinity = ColumnInfo.TEXT) var ibps: Int = 120,
                                   @ColumnInfo(name="ibpd", typeAffinity = ColumnInfo.TEXT) var ibpd: Int = 80,
                                   @ColumnInfo(name="temp", typeAffinity = ColumnInfo.TEXT) var temp: Float = 97.7f,
                                   @ColumnInfo(name="is_hr") var isHR: Int = NOT_AVAILABLE,
                                   @ColumnInfo(name="is_spo2") var isBP: Int = NOT_AVAILABLE,
                                   @ColumnInfo(name="is_rr") var isRR: Int = NOT_AVAILABLE,
                                   @ColumnInfo(name="is_bp") var isSPO2: Int = NOT_AVAILABLE,
                                   @ColumnInfo(name="is_etco2") var isETCO2: Int = NOT_AVAILABLE,
                                   @ColumnInfo(name="is_ibp") var isIBP: Int = NOT_AVAILABLE,
                                   @ColumnInfo(name="is_temp") var isTEMP: Int = NOT_AVAILABLE,
                                   @ColumnInfo(name="batdok_network") val sendOverBatdokNet: Boolean = true,
                                   @ColumnInfo(name="send_to_bt_spoofer") val sendToBTSpoofer: Boolean = false,
                                   @ColumnInfo(name="bt_spoofer_address") val btSpooferMacAddress: String? = null): Serializable {

    fun toBatdokTrainerSensor() = BatdokTrainerSensor(
        id.copy(), name, hr, bps, bpd, rr, spo2, etco2, ibps, ibpd, temp, isHR, isBP, isRR, isSPO2, isETCO2,
        isIBP, isTEMP, sendOverBatdokNet, sendToBTSpoofer, btSpooferMacAddress
    )

    constructor(batdokTrainerSensor: BatdokTrainerSensor): this(
        batdokTrainerSensor.id,
        batdokTrainerSensor.name,
        batdokTrainerSensor.hr,
        batdokTrainerSensor.bps,
        batdokTrainerSensor.bpd,
        batdokTrainerSensor.rr,
        batdokTrainerSensor.spo2,
        batdokTrainerSensor.etco2,
        batdokTrainerSensor.ibps,
        batdokTrainerSensor.ibpd,
        batdokTrainerSensor.temp,
        batdokTrainerSensor.isHR,
        batdokTrainerSensor.isBP,
        batdokTrainerSensor.isRR,
        batdokTrainerSensor.isSPO2,
        batdokTrainerSensor.isETCO2,
        batdokTrainerSensor.isIBP,
        batdokTrainerSensor.isTEMP,
        batdokTrainerSensor.sendOverBatdokNet,
        batdokTrainerSensor.sendToBTSpoofer,
        batdokTrainerSensor.btSpooferMacAddress
    )
}