package mil.af.afrl.batdokstorage.encounter

import androidx.room.Embedded
import androidx.room.Junction
import androidx.room.Relation
import gov.afrl.batdok.util.buildDocumentCommand
import gov.afrl.batdok.util.combine
import mil.af.afrl.batdokdata.models.patient.Encounter
import mil.af.afrl.batdokdata.models.vital.EncounterVitals
import mil.af.afrl.batdokstorage.document.RoomDocumentCommand
import mil.af.afrl.batdokstorage.patient.RoomEncounter
import mil.af.afrl.batdokstorage.patient.RoomEncounterHandoffData
import mil.af.afrl.batdokstorage.patient.RoomVitalThresholds
import mil.af.afrl.batdokstorage.platform.RoomPlatform
import mil.af.afrl.batdokstorage.platform.RoomPlatformPatientRelation
import mil.af.afrl.batdokstorage.vital.RoomLocationVital

internal data class AggregatedRoomEncounter(
    @Embedded val data: RoomEncounter,
    @Relation(parentColumn = "id", entityColumn = "encounter_id")
    val thresholds: RoomVitalThresholds,
    @Relation(parentColumn = "id", entityColumn = "encounter_id")
    val commands: List<RoomDocumentCommand>,
    @Relation(parentColumn = "id", entityColumn = "owner_id")
    val trendsVitals: List<RoomLocationVital>,
    @Relation(
        parentColumn = "id",
        entityColumn = "id",
        associateBy = Junction(
            RoomPlatformPatientRelation::class,
            parentColumn = "encounter_id",
            entityColumn = "platform_id"
        )
    )
    val platform: RoomPlatform?,
    @Relation(parentColumn = "id", entityColumn = "id")
    val encounterHandoffData: RoomEncounterHandoffData
){
    fun toEncounter(): Encounter {
        val docCommands = commands.map { it.toEditCommand() }.takeIf { it.isNotEmpty() }
        val platform = platform?.toPlatform()
        val trends = trendsVitals.map { it.toLocationPatientVital() }.sortedBy { it.timestamp }
        val encounterData = data.toEncounter().apply {
            trendsVitals = trends.lastOrNull() ?: EncounterVitals()
        }
        return Encounter(
            data.id.copy(),
            encounterData,
            docCommands?.combine() ?: buildDocumentCommand(data.id.copy()),
            platform,
            encounterHandoffData.toEncounterHandoffData(),
            EncounterVitals(),
            trends,
            thresholds.thresholds
        )
    }
}