package mil.af.afrl.batdokstorage.a2a

import androidx.room.*
import mil.af.afrl.batdokdata.models.a2a.A2AApplication
import mil.af.afrl.batdokdata.models.a2a.A2ADataStore

@Dao
abstract class A2ADao: A2ADataStore {
    @Transaction
    @Query("SELECT * FROM a2a_applications")
    abstract suspend fun getAllApps(): List<RoomA2AApplication>
    override suspend fun allApps(): List<A2AApplication> = getAllApps().map { it.toA2AApplication() }

    @Transaction
    @Insert
    abstract suspend fun insert(a2AApplication: RoomA2AApplication)
    override suspend fun insert(a2AApplication: A2AApplication) = insert(RoomA2AApplication(a2AApplication))

    @Query("UPDATE a2a_applications SET name=:name, enabled=:enabled, interval=:interval, packageName=:packageName WHERE receiverClass=:receiverClass")
    abstract override suspend fun updateApp(name: String, receiverClass: String, enabled: Boolean, interval: Int, packageName: String)
}
