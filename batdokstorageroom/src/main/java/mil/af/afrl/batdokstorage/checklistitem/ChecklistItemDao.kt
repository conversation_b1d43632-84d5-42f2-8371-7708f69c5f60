package mil.af.afrl.batdokstorage.checklistitem

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import mil.af.afrl.batdokdata.models.checklistitem.CheckListModel
import mil.af.afrl.batdokdata.models.checklistitem.ChecklistItemDataStore

@Dao
abstract class ChecklistItemDao: ChecklistItemDataStore {

    @Query("SELECT * FROM checklist_item")
    abstract suspend fun getAllRoomChecklistItems(): List<RoomCheckListModel>
    override suspend fun getAllChecklistItems(): List<CheckListModel> = getAllRoomChecklistItems().map { it.toCheckListModel()}

    @Query("SELECT * FROM checklist_item")
    abstract fun getRoomAllLiveChecklistItems(): Flow<List<RoomCheckListModel>>
    override fun getAllLiveChecklistItems(): Flow<List<CheckListModel>> = getRoomAllLiveChecklistItems().map { it.map { it.toCheckListModel() } }

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun upsertRoomChecklistItem(checkListModel: RoomCheckListModel)
    override suspend fun upsertChecklistItem(checkListModel: CheckListModel) = upsertRoomChecklistItem(
        RoomCheckListModel(checkListModel)
    )

    @Query("DELETE FROM checklist_item")
    abstract override suspend fun deleteAll()

    @Query("DELETE FROM checklist_item WHERE name=:name")
    abstract suspend fun deleteRoomCheckListItem(name: String)
    override suspend fun deleteCheckListItem(checkListModel: CheckListModel) = deleteRoomCheckListItem(
        checkListModel.name
    )
}