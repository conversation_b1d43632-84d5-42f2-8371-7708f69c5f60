package mil.af.afrl.batdokstorage.patient

import androidx.room.*
import mil.af.afrl.batdokdata.id.EncounterId
import mil.af.afrl.batdokdata.models.patient.*
import mil.af.afrl.batman.batdokid.DomainId


@Entity(tableName = "patient_vital_thresholds",
        foreignKeys = [ForeignKey(entity = RoomEncounter::class,
                                  parentColumns = ["id"],
                                  childColumns = ["encounter_id"],
                                  onDelete = ForeignKey.CASCADE)])
class RoomVitalThresholds(@PrimaryKey val encounter_id: DomainId,
                          @Embedded(prefix = "hr_") val hrThreshold: FourPartIntVitalThreshold,
                          @Embedded(prefix = "spo2_") val spo2Threshold: TwoPartIntVitalThreshold,
                          @Embedded(prefix = "resp_") val respThreshold: FourPartIntVitalThreshold,
                          @Embedded(prefix = "syst_") val bpsThreshold: FourPartIntVitalThreshold,
                          @Embedded(prefix = "dia_") val bpdThreshold: FourPartIntVitalThreshold,
                          @Embedded(prefix = "etco2_") val etco2Threshold: FourPartIntVitalThreshold,
                          @Embedded(prefix = "isyst_") val ibpsThreshold: FourPartIntVitalThreshold,
                          @Embedded(prefix = "idia_") val ibpdThreshold: FourPartIntVitalThreshold,
                          @Embedded(prefix = "temp_") val tempThreshold: FourPartFloatVitalThreshold
) {
    @Ignore
    val thresholds = VitalThresholds(
        Threshold("HR", 0, hrThreshold),
        Threshold("SPO2", 0, spo2Threshold),
        Threshold("Resp", 0, respThreshold),
        Threshold("Systolic", 0, bpsThreshold),
        Threshold("Diastolic", 0, bpdThreshold),
        Threshold("EtCO2", 0, etco2Threshold),
        Threshold("iSystolic", 0, ibpsThreshold),
        Threshold("iDiastolic", 0, ibpdThreshold),
        Threshold("Temp", 1, tempThreshold))

    companion object{
        @JvmStatic
        @Deprecated("Use fromThresholds instead")
        fun fromEncounter(patient: EncounterModel): RoomVitalThresholds {
            return fromThresholds(patient.id, patient.vitalThresholds)
        }
        @JvmStatic
        internal fun fromThresholds(encounterId: EncounterId, thresholds: VitalThresholds): RoomVitalThresholds {
            return RoomVitalThresholds(
                encounterId,
                thresholds.hrThreshold.toFourPartIntVitalThreshold(),
                thresholds.spo2Threshold.toTwoPartIntVitalThresholdUsingLow(),
                thresholds.respThreshold.toFourPartIntVitalThreshold(),
                thresholds.bpsThreshold.toFourPartIntVitalThreshold(),
                thresholds.bpdThreshold.toFourPartIntVitalThreshold(),
                thresholds.etco2Threshold.toFourPartIntVitalThreshold(),
                thresholds.ibpsThreshold.toFourPartIntVitalThreshold(),
                thresholds.ibpdThreshold.toFourPartIntVitalThreshold(),
                thresholds.tempThreshold.toFourPartFloatVitalThreshold()
            )
        }
    }
}