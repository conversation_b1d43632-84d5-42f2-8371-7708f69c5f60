package mil.af.afrl.batdokstorage.medinventory

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import gov.afrl.batdok.encounter.medicine.KnownMedTypes
import gov.afrl.batdok.medications.MedList
import mil.af.afrl.batdokdata.models.medinventory.MedListType
import mil.af.afrl.batdokdata.models.medinventory.SavedFastMed
import mil.af.afrl.batman.batdokid.DomainId

/**
 * This class holds the information needed to create a fast med.
 *
 * This class is also used for Combat pill pack
 */
@Entity(tableName = "dd1380_saved_fast_med")
open class RoomSavedFastMed @JvmOverloads constructor(@PrimaryKey var id: DomainId,
                                                      @ColumnInfo(name = "med_name") var medName: String,
                                                      var route: String,
                                                      var dose: String,
                                                      var unit: String,
                                                      @ColumnInfo(name = "med_type") var type: String,
                                                      @ColumnInfo(name = "description") var description: String,
                                                      var mode: String? = null) {
    constructor(fastMed: SavedFastMed): this(
        fastMed.id,
        fastMed.medName,
        fastMed.route,
        fastMed.dose,
        fastMed.unit,
        fastMed.type,
        fastMed.description,
        fastMed.mode,
    )

    fun toSavedFastMed() = SavedFastMed(id.copy(), medName, route, dose, unit, type, description, mode)

    companion object {
        /**
         * The default fast meds to initially write to the database
         */
        @JvmField
        val defaultItems: List<SavedFastMed> = listOf()

        /**
         * The default combat pill pack items to initially write to the database
         */
        @JvmField
        val defaultCombatPillPackItems: List<SavedFastMed> = listOf(
            SavedFastMed(DomainId.nil(), MedList.MELOXICAM.medName, "PO", "15", "mg", KnownMedTypes.ANALGESIC.dataString, ""),
            SavedFastMed(DomainId.nil(), MedList.ACETAMINOPHEN.medName, "PO", "500", "mg", KnownMedTypes.ANALGESIC.dataString, ""),
            SavedFastMed(DomainId.nil(), MedList.MOXIFLOXACIN.medName, "PO", "400", "mg", KnownMedTypes.ANTIBIOTIC.dataString, ""),
        )
    }
}