package mil.af.afrl.batdokstorage.patient

import androidx.room.Embedded
import androidx.room.Relation
import mil.af.afrl.batdokdata.models.patient.EncounterModel
import mil.af.afrl.batdokstorage.vital.LatestVital

class RoomEncounterWithThresholds(@Embedded val encounter: RoomEncounter,
                                  @Relation(parentColumn = "id", entityColumn = "encounter_id") val vitalThresholds: RoomVitalThresholds?,
                                  @Relation(parentColumn = "id", entityColumn = "owner_id") val latestVital: LatestVital?) {
    fun toEncounter(): EncounterModel{
        val encounter = encounter.toEncounter()
        latestVital?.LocationVital?.toEncounterVital()?.let{encounter.vitals =it}
        latestVital?.LocationVital?.toEncounterVital()?.let{ encounter.trendsVitals = it }
        if (vitalThresholds != null) {
            encounter.vitalThresholds = vitalThresholds.thresholds
        }
        // do not validate thresholds here
        // this newly created encounter does not have up to date vitals
        return encounter
    }
}