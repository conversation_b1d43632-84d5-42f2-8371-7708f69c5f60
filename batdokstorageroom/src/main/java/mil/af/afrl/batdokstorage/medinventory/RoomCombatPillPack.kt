package mil.af.afrl.batdokstorage.medinventory

import androidx.room.Entity
import androidx.room.Ignore
import mil.af.afrl.batdokdata.models.medinventory.SavedFastMed
import mil.af.afrl.batman.batdokid.DomainId

@Entity(tableName = "combat_pill_pack",
        ignoredColumns = ["description"])
class RoomCombatPillPack @Ignore constructor(med: SavedFastMed): RoomSavedFastMed(med.id, med.medName, med.route, med.dose, med.unit, med.type, med.description){
    constructor(id: DomainId, medName: String, route: String, dose: String, unit: String, type: String, mode: String? = null): this(
        SavedFastMed(id.copy(), medName, route, dose, unit, type, "", mode)
    )
}