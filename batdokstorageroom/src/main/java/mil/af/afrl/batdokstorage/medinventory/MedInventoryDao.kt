package mil.af.afrl.batdokstorage.medinventory

import androidx.room.*
import gov.afrl.batdok.encounter.medicine.Medicine
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import mil.af.afrl.batdokdata.models.medinventory.*
import mil.af.afrl.batdokdata.models.medinventory.MedInventoryDataStore.Companion.ALL_MODES
import mil.af.afrl.batman.batdokid.*

@Dao
abstract class MedInventoryDao: MedInventoryDataStore {

    @Insert
    abstract suspend fun addMed(med: RoomMedListItem)
    override suspend fun addMed(med: MedListItem) = addMed(RoomMedListItem(med))
    
    @Insert
    abstract suspend fun addRoute(route: RoomMedRouteListItem)
    override suspend fun addRoute(route: MedRouteListItem) = addRoute(RoomMedRouteListItem(route))
    
    @Insert
    abstract suspend fun addUnit(unit: RoomMedUnitListItem)
    override suspend fun addUnit(unit: MedUnitListItem) = addUnit(RoomMedUnitListItem(unit))

    @Insert
    abstract suspend fun addConcentrationUnit(unit: RoomConcentrationUnitListItem)
    override suspend fun addConcentrationUnit(unit: ConcentrationUnitListItem) = addConcentrationUnit(RoomConcentrationUnitListItem(unit))

    @Query("SELECT * FROM dd1380_med_list_item")
    abstract fun medInventoryRoomFlow(): Flow<List<RoomMedListItem>>
    override  fun medInventoryFlow(type: String?): Flow<List<MedInventory>> = medInventoryRoomFlow().map { listOf(medInventory(type))}

    @Query("SELECT * from dd1380_med_list_item")
    abstract suspend fun allRoomMeds(): List<RoomMedListItem>
    override suspend fun allMeds(): List<MedListItem> = allRoomMeds().map { it.toMed() }

    @Query("SELECT * from dd1380_med_list_item WHERE mode = :mode OR (mode IS NULL AND (:includeNullMode OR :mode IS NULL))")
    abstract suspend fun allRoomMedsByMode(mode: String?, includeNullMode: Boolean): List<RoomMedListItem>
    override suspend fun allMedsByMode(mode: String?, includeNullMode: Boolean) =
        allRoomMedsByMode(mode, includeNullMode).map { it.toMed() }

    @Query("SELECT * from dd1380_med_list_item WHERE type = :type")
    abstract suspend fun allMedsByType(type: String): List<RoomMedListItem>

    @Query("SELECT * from dd1380_med_list_item WHERE type = :type AND (mode = :mode OR (mode IS NULL AND (:includeNullMode OR :mode IS NULL)))")
    abstract suspend fun allMedsByTypeAndMode(type: String, mode: String?, includeNullMode: Boolean): List<RoomMedListItem>

    @Query("SELECT * from dd1380_route_list_item")
    abstract suspend fun allRoutes(): List<RoomMedRouteListItem>

    @Query("SELECT * from dd1380_units")
    abstract suspend fun allUnits(): List<RoomMedUnitListItem>

    @Query("SELECT * from concentration_units")
    abstract suspend fun allConcentrationUnits(): List<RoomConcentrationUnitListItem>
    
    @Delete
    abstract suspend fun removeMed(med: RoomMedListItem)

    @Query("DELETE FROM dd1380_med_list_item")
    abstract suspend fun removeAllMeds()

    @Query("DELETE FROM dd1380_med_list_item WHERE mode=:mode OR (mode IS NULL AND :mode IS NULL)")
    abstract suspend fun removeAllMedsByMode(mode: String?)

    @Delete
    abstract suspend fun removeRoute(route: RoomMedRouteListItem)

    @Delete
    abstract suspend fun removeUnit(unit: RoomMedUnitListItem)

    @Delete
    abstract suspend fun removeConcentrationUnit(unit: RoomConcentrationUnitListItem)

    override suspend fun removeMed(id: Int) {
        removeMed(RoomMedListItem(id, MedListType.OTHER, ""))
    }

    override suspend fun removeRoute(id: Int) {
        removeRoute(RoomMedRouteListItem(id, ""))
    }

    override suspend fun removeUnit(id: Int) {
        removeUnit(RoomMedUnitListItem(id, ""))
    }

    override suspend fun removeConcentrationUnit(id: Int) {
        removeConcentrationUnit(RoomConcentrationUnitListItem(id, ""))
    }

    override suspend fun medInventory(type: String?, mode: String?, includeNullMode: Boolean): MedInventory {
        val meds: List<MedListItem> = when {
            mode == ALL_MODES && type == null -> allRoomMeds()
            mode == ALL_MODES && type != null -> allMedsByType(type)
            type == null -> allRoomMedsByMode(mode, includeNullMode)
            else -> allMedsByTypeAndMode(type, mode, includeNullMode)
        }.map { it.toMed() }
        return MedInventory(
            meds.distinctBy { Pair(it.name, it.type) },
            allRoutes().distinctBy { it.name }.map { it.toMedRouteListItem() },
            allUnits().distinctBy { it.unit }.map { it.toMedUnitListItem() },
            allConcentrationUnits().distinctBy { it.unit }.map { it.toConcentrationItem() }
        )
    }
    @Transaction
    override suspend fun updateMedItemLists(medListItems: List<MedListItem>){
        removeAllMeds()
        for(item in medListItems){
            addMed(item)
        }
    }
    @Transaction
    override suspend fun updateMedItemLists(mode: String?, medListItems: List<MedListItem>) {
        removeAllMedsByMode(mode)
        for(item in medListItems){
            addMed(item)
        }
    }

    @Query("SELECT * from dd1380_saved_fast_med")
    abstract suspend fun retrieveSavedFastMeds(): List<RoomSavedFastMed>

    @Query("SELECT * from dd1380_saved_fast_med WHERE med_type=:type")
    abstract suspend fun retrieveRoomSavedFastMeds(type: String?): List<RoomSavedFastMed>
    override suspend fun retrieveSavedFastMeds(type: String?): List<SavedFastMed> = retrieveRoomSavedFastMeds(type).map { it.toSavedFastMed() }

    @Query("SELECT * from dd1380_saved_fast_med WHERE mode =:mode OR (mode IS NULL AND (:includeNullMode OR :mode IS NULL))")
    abstract suspend fun retrieveSavedFastMeds(mode: String?, includeNullMode: Boolean): List<RoomSavedFastMed>

    @Query("SELECT * from dd1380_saved_fast_med WHERE med_type = :type AND (mode =:mode OR (mode IS NULL AND (:includeNullMode OR :mode IS NULL)))")
    abstract suspend fun retrieveRoomSavedFastMeds(type: String?, mode: String?, includeNullMode: Boolean): List<RoomSavedFastMed>

    override suspend fun retrieveSavedFastMeds(type: String?, mode: String?, includeNullMode: Boolean): List<SavedFastMed> {
        return when {
            mode == ALL_MODES && type == null -> retrieveSavedFastMeds()
            mode == ALL_MODES && type != null -> retrieveRoomSavedFastMeds(type)
            type == null -> retrieveSavedFastMeds(mode, includeNullMode)
            else -> retrieveRoomSavedFastMeds(type, mode, includeNullMode)
        }.map { it.toSavedFastMed() }
    }

    @Insert
    abstract suspend fun addSavedFastMed(fastMed: RoomSavedFastMed)
    override suspend fun addSavedFastMed(fastMed: SavedFastMed) = addSavedFastMed(RoomSavedFastMed(fastMed))

    @Query("DELETE FROM dd1380_saved_fast_med WHERE id=:fastMedId")
    abstract override suspend fun removeFastMed(fastMedId: DomainId)

    @Update
    abstract suspend fun updateFastMed(fastMed: RoomSavedFastMed)
    override suspend fun updateFastMed(fastMed: SavedFastMed) = updateFastMed(RoomSavedFastMed(fastMed))

    @Query("DELETE FROM dd1380_saved_fast_med")
    abstract override suspend fun clearFastMeds()

    @Query("DELETE FROM dd1380_saved_fast_med WHERE mode=:mode OR (mode is null AND :mode is null)")
    abstract override suspend fun clearFastMeds(mode: String?)

    @Query("SELECT * FROM combat_pill_pack")
    abstract suspend fun getRoomCombatPillPackMeds(): MutableList<RoomCombatPillPack>

    override suspend fun getCombatPillPackMeds(): List<SavedFastMed> {
        return getRoomCombatPillPackMeds().map { it.toSavedFastMed() }
    }

    @Insert
    abstract suspend fun insertCombatPillPackMed(med: RoomCombatPillPack)
    override suspend fun insertCombatPillPackMed(med: SavedFastMed) = insertCombatPillPackMed(
        RoomCombatPillPack(med)
    )
//fix Batdok Data
    override suspend fun addCombatPillPackMed(med: Medicine, type: String) {
        return insertCombatPillPackMed(SavedFastMed(med.id, med.name, med.route?:"", med.volume?.toString()?:"", med.unit?:"", med.type?:"", ""))
    }

    @Query("DELETE FROM combat_pill_pack WHERE id=:fastMedId")
    abstract override suspend fun removeCombatPillPackMed(fastMedId: DomainId)

    @Update
    abstract suspend fun updateCombatPillPack(combatPillPack: RoomCombatPillPack)

    override suspend fun updateCombatPillPackMed(fastMed: SavedFastMed) {
        return updateCombatPillPack(RoomCombatPillPack(fastMed))
    }
}