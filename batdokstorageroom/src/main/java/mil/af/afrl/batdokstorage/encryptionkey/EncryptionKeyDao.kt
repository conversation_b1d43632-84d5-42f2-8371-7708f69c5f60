package mil.af.afrl.batdokstorage.encryptionkey

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import mil.af.afrl.batdokdata.models.encryptionkey.EncryptionKey
import mil.af.afrl.batdokdata.models.encryptionkey.EncryptionKeyDataStore

@Dao
abstract class EncryptionKeyDao : EncryptionKeyDataStore {
    override suspend fun getKey(keyType: EncryptionKey.KeyType): String? {
        return getKey(keyType.name)
    }

    @Query("SELECT keyString FROM `key` WHERE keyType=:keyType")
    abstract override suspend fun getKey(keyType: String): String?

    override suspend fun setKey(keyType: EncryptionKey.KeyType, key: String) {
        upsertKey(EncryptionKey(keyType, key))
    }

    override suspend fun setKey(keyType: String, key: String) {
        upsertRoomKey(RoomEncryptionKey(keyType, key))
    }

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun upsertRoomKey(key: RoomEncryptionKey)

    override suspend fun upsertKey(key: EncryptionKey) = upsertRoomKey(RoomEncryptionKey(key.keyType.name, key.keyString))

}
