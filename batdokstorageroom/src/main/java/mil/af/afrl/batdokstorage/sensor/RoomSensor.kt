package mil.af.afrl.batdokstorage.sensor

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import mil.af.afrl.batdokdata.id.EncounterId
import mil.af.afrl.batdokdata.id.SensorId
import mil.af.afrl.batdokdata.models.sensor.Sensor
import mil.af.afrl.batdokstorage.patient.RoomEncounter
import mil.af.afrl.batman.batdokid.DomainId


@Entity(tableName = "sensor",
        foreignKeys = [ForeignKey(entity = RoomEncounter::class,
                parentColumns = ["id"],
                childColumns = ["encounter_id"],
                onDelete = ForeignKey.SET_NULL)],
        indices = [Index(value = ["encounter_id"])]
)
@Deprecated("Use RoomSensorInfo instead", ReplaceWith("RoomSensorInfo()"))
class RoomSensor(@PrimaryKey val id: DomainId,
                 val name: String?,
                 val address: String?,
                 val sensor_type: String?,
                 val encounter_id : DomainId?,
                 val state: String?,
                 val auto_connect: Boolean?,
                 val is_floating: Boolean?,
                 val rank: Int?,
                 val pluginName: String?) {
    companion object{
        @JvmStatic
        fun fromSensor(sensor: Sensor): RoomSensor {
            return RoomSensor(sensor.id, sensor.name, sensor.address, sensor.sensorType, sensor.encounterId,
                    sensor.state, sensor.autoConnect, sensor.isFloating, sensor.rank, sensor.pluginName)
        }
        @JvmStatic
        fun toSensor(roomSensor: RoomSensor): Sensor {
            return Sensor(roomSensor.id.copy(), roomSensor.name, roomSensor.address, roomSensor.sensor_type,
                    roomSensor.state, roomSensor.auto_connect?:false,
            roomSensor.is_floating?:false, roomSensor.encounter_id?.copy(),
                roomSensor.rank?: Int.MAX_VALUE, roomSensor.pluginName)
        }
    }
}