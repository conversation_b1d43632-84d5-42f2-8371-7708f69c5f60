package mil.af.afrl.batdokstorage.sensor

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import mil.af.afrl.batdokdata.models.sensor.SensorInfo
import mil.af.afrl.batdokstorage.patient.RoomEncounter
import mil.af.afrl.batman.batdokid.DomainId


@Entity(tableName = "sensorInfo",
        foreignKeys = [ForeignKey(entity = RoomEncounter::class,
                parentColumns = ["id"],
                childColumns = ["encounterId"],
                onDelete = ForeignKey.SET_NULL)],
        indices = [Index(value = ["encounterId"])]
)
class RoomSensorInfo(
    @PrimaryKey val id: DomainId = DomainId.create(),
    val address: String = "",
    val name: String = "Unknown Sensor",
    val autoConnect: Boolean = false,
    val isFloating: Boolean = true,
    val encounterId: DomainId? = null,
    val rank: Int = 0,
    val extras: Map<String, String> = mapOf(),
) {
    fun toSensorInfo(): SensorInfo {
        return SensorInfo(
            id.copy(),
            address,
            name,
            null,
            autoConnect,
            isFloating,
            encounterId?.copy(),
            rank,
            extras,
            null
        )
    }

    companion object{
        @JvmStatic
        fun fromSensor(sensor: SensorInfo): RoomSensorInfo {
            return RoomSensorInfo(
                sensor.id,
                sensor.address,
                sensor.name,
                sensor.autoConnect,
                sensor.isFloating,
                sensor.encounterId,
                sensor.rank,
                sensor.extras,
            )
        }
    }
}