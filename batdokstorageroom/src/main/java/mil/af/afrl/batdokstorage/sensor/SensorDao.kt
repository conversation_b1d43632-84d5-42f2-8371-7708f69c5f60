package mil.af.afrl.batdokstorage.sensor

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import androidx.room.Update
import mil.af.afrl.batdokdata.id.SensorId
import mil.af.afrl.batdokdata.models.sensor.Sensor
import mil.af.afrl.batdokdata.models.sensor.SensorDataStore

@Dao
@Deprecated("Use SensorInfoDao instead", ReplaceWith("RoomSensorDao()"))
abstract class SensorDao: SensorDataStore {
    @Query("SELECT * FROM sensor")
    abstract suspend fun roomSensors(): List<RoomSensor>
    override suspend fun sensors(): List<Sensor> = roomSensors().map { RoomSensor.toSensor(it) }
    @Insert
    abstract suspend fun add(sensor: RoomSensor)

    @Update
    abstract suspend fun update(sensor: RoomSensor)

    @Query("DELETE FROM sensor WHERE id=:sensorId")
    abstract override suspend fun remove(sensorId: SensorId)

    // the encounter_id 0000... is the same value as DomainId.NIL except as required compile time constant
    @Query("DELETE FROM sensor WHERE encounter_id is null")
    abstract override suspend fun purgeUnconnectedSensors()

    override suspend fun add(sensor: Sensor) {
        add(RoomSensor.fromSensor(sensor))
    }

    override suspend fun update(sensor: Sensor) {
        update(RoomSensor.fromSensor(sensor))
    }
}