package mil.af.afrl.batdokstorage.platform

import androidx.room.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import mil.af.afrl.batdokdata.id.EncounterId
import mil.af.afrl.batdokdata.id.PlatformId
import mil.af.afrl.batdokdata.models.platform.Platform
import mil.af.afrl.batdokdata.models.platform.PlatformDataStore
import mil.af.afrl.batdokdata.models.platform.PlatformPatient
import mil.af.afrl.batman.batdokid.DomainId
import kotlin.math.max
import kotlin.math.min

@Dao
abstract class PlatformDao: PlatformDataStore {
    @Transaction
    @Query("SELECT * FROM platform WHERE id IS NOT NULL")
    abstract suspend fun getPlatformWithPatients(): List<RoomPlatformWithPatients>
    @Transaction
    @Query("SELECT * FROM platform WHERE id IS NOT NULL")
    abstract fun getLivePlatformWithPatients(): Flow<List<RoomPlatformWithPatients>>
    @Transaction
    @RewriteQueriesToDropUnusedColumns
    @Query("SELECT * FROM encounter WHERE patient_type=1 OR patient_type=2")
    abstract fun getLiveAllPatients(): Flow<List<RoomPlatformPatientByPatient>>

    private fun getLivePatientsWithoutPlatforms(): Flow<List<PlatformPatient>>{
        return getLiveAllPatients().map {
            it.filter {patient ->
                patient.platformPatientRelation?.platform_id==null
            }.map { patient -> patient.toPlatformPatient() }
        }
    }
    @Transaction
    @RewriteQueriesToDropUnusedColumns
    @Query("SELECT * FROM encounter WHERE id=:id")
    abstract suspend fun getRoomEncounter(id : DomainId): RoomPlatformPatientByPatient?
    suspend fun getEncounter(id : EncounterId) = getRoomEncounter(id)?.toPlatformPatient()

    @Transaction
    @Query("SELECT * FROM platform WHERE id=:id")
    abstract suspend fun getPlatform(id: DomainId): RoomPlatformWithPatients?

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    abstract suspend fun insert(roomPlatform: RoomPlatform)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insert(platformPatientRelation: RoomPlatformPatientRelation)

    @Update
    abstract suspend fun update(platform: RoomPlatform)

    @Update
    abstract suspend fun update(relation: RoomPlatformPatientRelation)

    @Query("UPDATE platform_patient_relation SET patient_rank=:rank WHERE encounter_id=:id")
    abstract suspend fun updateRank(id : DomainId, rank: Int)

    @Query("DELETE FROM platform_patient_relation WHERE encounter_id in (:ids)")
    abstract suspend fun delete(ids: List<EncounterId>)

    @Query("SELECT COUNT(*) FROM platform_patient_relation WHERE platform_id=:id")
    abstract suspend fun patientsInPlatform(id: DomainId): Int

    @Query("SELECT * FROM platform_patient_relation WHERE platform_id=:id AND patient_rank >= :lowRank AND patient_rank <= :highRank;")
    abstract suspend fun getPatientsInRankRange(id: DomainId, lowRank: Int, highRank: Int): List<RoomPlatformPatientRelation>

    @Query("SELECT * FROM platform_patient_relation WHERE platform_id=:id ORDER BY patient_rank")
    abstract suspend fun getPatientsOrderedByRank(id: DomainId) : List <RoomPlatformPatientRelation>

    override suspend fun platforms(): List<Platform> {
        return getPlatformWithPatients()
                    .mapNotNull { it.toPlatform() }
    }

    override suspend fun platform(id: PlatformId): Platform? {
        return getPlatform(id)?.toPlatform()
    }

    override fun livePlatforms(): Flow<List<Platform>> {
        return getLivePlatformWithPatients().map {
            it.mapNotNull { roomPlatform -> roomPlatform.toPlatform() }
        }
    }

    override fun livePatientsWithoutPlatform(): Flow<MutableList<PlatformPatient>> {
        return getLivePatientsWithoutPlatforms().map { it.toMutableList() }
    }

    override suspend fun patient(encounterId: EncounterId): PlatformPatient? {
        return getEncounter(encounterId)
    }

    override suspend fun add(platform: Platform) {
        insert(RoomPlatform.fromPlatform(platform))
    }

    @Transaction
    override suspend fun addPatientsToPlatform(platform: Platform, encounterIds: List<EncounterId>) {
        delete(encounterIds)
        var patientCount = patientsInPlatform(platform.id)
        for (patient in encounterIds) {
            // Make sure foreign key items exist before trying to insert the new relation
            // This check is being made by the caller, but things seem to be getting messed up in
            //  networking, so add another check in the transaction to make sure
            if (getEncounter(patient) != null && getPlatform(platform.id) != null) {
                insert(RoomPlatformPatientRelation(patient, platform.id, patientCount++))
            }
        }
    }

    @Transaction
    override suspend fun updateAllPatientRanks(platformId : PlatformId) {
        val patients = getPatientsOrderedByRank(platformId)
        for(i in patients.indices){
            val relation = patients[i]
            relation.patient_rank = i
            updateRank(relation.encounter_id, patients.indexOf(relation))
        }

    }
    @Transaction
    override suspend fun updatePatientRank(platformId: PlatformId, encounterId: EncounterId, oldRank: Int, newRank: Int) {
        val lowRank = min(oldRank+1, newRank)
        val highRank = max(oldRank-1, newRank)
        //Get all of the patients that need to change rank
        val relations : List<RoomPlatformPatientRelation> = getPatientsInRankRange(platformId, lowRank, highRank)

        for(relation in relations){
            //If the old rank is lower than the new rank, all ranks go down by 1.
            if(oldRank < newRank) {
                relation.patient_rank--
            }
            if(oldRank > newRank){
                relation.patient_rank++
            }
            update(relation)
        }
        updateRank(encounterId, newRank)
    }

    @Query("DELETE FROM platform_patient_relation WHERE encounter_id IN (:encounterId)")
    abstract override suspend fun removePatientsFromPlatforms(encounterId: List<EncounterId>)

    @Query("DELETE FROM platform")
    abstract override suspend fun deleteAll()

    override suspend fun update(platform: Platform) {
        update(RoomPlatform.fromPlatform(platform))
    }

    @Query("DELETE FROM platform WHERE id=:id")
    abstract override suspend fun delete(id: PlatformId)

    @Query("DELETE FROM platform WHERE tutorial=1")
    abstract override suspend fun deleteTutorialPlatforms()
}