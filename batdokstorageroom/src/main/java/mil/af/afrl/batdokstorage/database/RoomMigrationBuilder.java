package mil.af.afrl.batdokstorage.database;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.room.migration.Migration;
import androidx.sqlite.db.SupportSQLiteDatabase;


public abstract class RoomMigrationBuilder {

    protected Context context;

    public RoomMigrationBuilder(Context context) {
        this.context = context;
    }

    protected Migration buildMigration(int oldVersion, int newVersion, MigrationFunction function) {
        return new Migration(oldVersion, newVersion) {
            @Override
            public void migrate(@NonNull SupportSQLiteDatabase database) {
                if(function != null) {
                    function.migrate(database);
                }
            }
        };
    }

    protected interface MigrationFunction {
        void migrate(@NonNull SupportSQLiteDatabase db);
    }

    public abstract Migration[] getMigrations();

    protected void changeTableSchema(SupportSQLiteDatabase db, String table, String newTableSchema, String columns) {
        changeTableSchema(db, table, newTableSchema, columns, columns);
    }

    protected void changeTableSchema(SupportSQLiteDatabase db, String table, String newTableSchema, String newColumns, String oldColumns) {
        db.execSQL("CREATE TABLE IF NOT EXISTS tmp_table_name (" + newTableSchema + ");");
        db.execSQL("INSERT INTO tmp_table_name (" + newColumns + ") SELECT " + oldColumns + " FROM " + table + ";");
        db.execSQL("DROP TABLE " + table);
        db.execSQL("ALTER TABLE tmp_table_name RENAME TO " + table + ";");
    }
}
