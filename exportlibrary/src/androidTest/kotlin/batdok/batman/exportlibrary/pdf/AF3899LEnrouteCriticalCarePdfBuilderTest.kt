package batdok.batman.exportlibrary.pdf

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.Document
import gov.afrl.batdok.encounter.Gender
import gov.afrl.batdok.encounter.commands.*
import gov.afrl.batdok.encounter.metadata.FlightInfo
import gov.afrl.batdok.encounter.metadata.FlightInfoId
import gov.afrl.batdok.util.Patterns
import gov.afrl.batdok.util.buildCommandData
import gov.afrl.batdok.util.format
import kotlinx.coroutines.test.runTest
import mil.af.afrl.batman.batdokid.DomainId
import mil.afrl.batdok.pdfrobots.af3899L
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import java.time.Instant
import java.time.LocalDate
import java.time.temporal.ChronoUnit

class AF3899LEnrouteCriticalCarePdfBuilderTest {
    @get:Rule
    val pdfTestRule = PdfTestRule()

    private lateinit var classUnderTest: AF3899LEnrouteCriticalCarePdfBuilder

    @Before
    fun setup() {
        classUnderTest = AF3899LEnrouteCriticalCarePdfBuilder(pdfTestRule.pdfIO)
    }

    @Test
    fun testBlank() = runTest {
        val document = Document().apply {
            handle(
                listOf<Message>(
                ).map { buildCommandData(it) }
            )
        }
        classUnderTest.createPdf(pdfTestRule.context, "Path.pdf", document, "")

        pdfTestRule.verify {
            af3899L(it){
                printFields()

                assertNamePage1("")
                assertNamePage2("")
                assertCitNumberPage1("")
                assertCitNumberPage2("")
                assertSSNPage1("")
                assertSSNPage2("")
                assertAge("")
                assertSex("")
                assertWeight("")
                assertHeight("")
                assertAllergies("")
                assertPrecedence("")
                assertFieldContainsText("10Mission Date & Time", "")
                assertOriginatingFacility("")
                assertDestinationFacility("")
                assertHoursEnRoute("")
                assertTailNum("")
                assertUnit("")

            }
        }
    }

    @Test
    fun testSectionOne() = runTest {
        LocalDate.now()

        val document = Document().apply {
            handle(
                listOf<Message>(
                    buildChangeNameCommand("First", "Middle", "Last"),
                    buildChangeCiteNumberCommand("citeNum"),
                    buildChangeDodIdCommand("DODid"),
                    buildChangeDOBCommand(LocalDate.now().minusYears(2).minusMonths(2)),
                    buildChangeGenderCommand(Gender.FEMALE),
                    buildChangeWeightCommand(100.0F),
                    buildChangeHeightCommand(150.0F),
                    buildAddRemoveAllergyListCommand(listOf("allergy1", "allergy2"), listOf("")),
                    buildPrecedenceCommand("Urgent"),
                    buildChangeOpenCloseEncounterCommand(true),
                    buildOriginatingMtfCommand("originMtf"),
                    buildDestinationMtfCommand("destMtf"),
                    buildChangeFlightInfoCommand(FlightInfo(id = DomainId.create<FlightInfoId>(), takeoff = Instant.now(),
                        landing = Instant.now().plus(1, ChronoUnit.HOURS), tail = "tail"),)

                ).map { buildCommandData(it)}
            )
        }
        classUnderTest.createPdf(pdfTestRule.context, "Path.pdf", document, "usersUnit")

        pdfTestRule.verify {
            af3899L(it){
                printFields()
                assertNamePage1("Last, First M")
                assertNamePage2("Last, First M")
                assertSSNPage1("DODid")
                assertSSNPage2("DODid")
                assertCitNumberPage1("citeNum")
                assertCitNumberPage2("citeNum")
                assertAge("2Y, 2M")
                assertSex("Female")
                assertWeight("100.0 kg")
                assertHeight("150.0 cm")
                assertAllergies("allergy1, allergy2")
                assertPrecedence("Urgent")
                var monthDay =  Instant.now().format(
                    Patterns.mdhm_24_space_comma_colon, true).split(",")[0]
                assertFieldContainsText("10Mission Date & Time", monthDay)
                assertOriginatingFacility("originMtf")
                assertDestinationFacility("destMtf")
                assertHoursEnRoute("1")
                assertTailNum("tail")
                assertUnit("usersUnit")

            }
        }
    }

}