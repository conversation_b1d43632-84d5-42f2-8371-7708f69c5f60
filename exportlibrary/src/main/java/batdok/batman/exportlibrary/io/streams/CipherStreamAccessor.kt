package batdok.batman.exportlibrary.io.streams

import android.content.Context
import android.util.Log
import androidx.security.crypto.EncryptedFile
import androidx.security.crypto.MasterKey
import java.io.File
import java.io.IOException
import java.io.InputStream
import java.io.OutputStream

/**
 * Class is responsible for providing the Cipher input stream and output stream
 * @param applicationContext - The Context to get the Android encryption key
 */
class CipherStreamAccessor(private val applicationContext: Context): FileStreamAccessor() {

    private fun getEncryptedFile(filename: String): EncryptedFile {
        val masterKey = MasterKey.Builder(applicationContext, "file-key")
            .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
            .build()

        val file = File(filename)
        return EncryptedFile.Builder(
            applicationContext,
            file,
            masterKey,
            EncryptedFile.FileEncryptionScheme.AES256_GCM_HKDF_4KB
        )
            .setKeysetAlias("file-key")
            .build()
    }

    /**
     * Creates and returns a CipherInputStream based on the path provided
     * @param path - The path for the CipherInputStream
     * @return Returns the CipherInputStream for the path provided if able, null otherwise
     */
    override fun access(path: String): InputStream? {
        super.access(path) ?: return null
        return getEncryptedFile(path).openFileInput()
    }

    /**
     * Creates and returns a CipherOutputStream based on the path provided
     * @param path - The path for the CipherOutputStream
     * @return Returns the CipherOutputStream for the path provided if able, null otherwise
     */
    override fun prepare(path: String): OutputStream? {
        super.prepare(path) ?: return null
        File(path).run {
            if(exists() && !delete()){
                Log.e("Encrypted File Write", "Error Deleting existing file")
            }
        }
        return try{
            getEncryptedFile(path).openFileOutput()
        } catch(ex:IOException){
            null
        }
    }
}
