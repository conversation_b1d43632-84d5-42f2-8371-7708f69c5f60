package batdok.batman.exportlibrary.io

/**
 * Class is responsible for getting the Export IO. Holds DefaultFileIO, ZipIO,EncryptedIO, and PdfIO
 * @param defaultFileIO - The DefaultFileIO
 * @param zipIO - The ZipIO
 * @param encryptedFileIO - The Encrypted FileIO
 * @param pdfIO - The PDF IO
 */
class ExportIO(val defaultFileIO: DefaultFileIO, val zipIO: ZipIO, val encryptedFileIO: EncryptedFileIO, val pdfIO: PdfIO)