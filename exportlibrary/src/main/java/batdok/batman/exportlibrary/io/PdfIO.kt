package batdok.batman.exportlibrary.io

import android.graphics.pdf.PdfDocument
import android.os.Build
import android.util.Log
import batdok.batman.exportlibrary.io.streams.CipherStreamAccessor
import batdok.batman.exportlibrary.io.streams.FileStreamAccessor
import com.tom_roush.pdfbox.pdmodel.PDDocument
import com.tom_roush.pdfbox.pdmodel.encryption.AccessPermission
import com.tom_roush.pdfbox.pdmodel.encryption.StandardProtectionPolicy
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import java.io.File
import java.io.IOException
import java.io.InputStream
import java.io.OutputStream
import kotlin.coroutines.CoroutineContext

/**
 * Class is responsible for Reading and writing to PDF files
 * @param fileStreamAccessor - The FileStreamAccessor
 * @param cipherAccessor - The CipherStreamAccessor
 */
class PdfIO(private val fileStreamAccessor: FileStreamAccessor,
            private val cipherAccessor: CipherStreamAccessor) : FileIO<PDDocument?, File?> {

    /**
     * Read from a path and returns a PDDocument if the path could be read from
     * @return - A PDDocument if the path could be read from, null otherwise
     */
    override fun read(path: String): PDDocument? {
        val cipherInputStream = cipherAccessor.access(path) ?: return null

        val bytes = cipherInputStream.readBytes()
        cipherInputStream.close()

        return PDDocument.load(bytes)
    }

    /**
     * Gets the InputStream for the path
     * @param path - The path to get the InputStream for
     * @return The InputStream if the path could be accessed, null otherwise
     */
    override fun getInputStream(path: String): InputStream? {
        return cipherAccessor.access(path)
    }

    /**
     * Copies data from a path to an encrypted file using the paths and password provided and returns the File
     * @param fromPath - The path to read the data from
     * @param toPath - The path to write the data to
     * @param password- The password to give the file
     * @return the File created if the paths were accessible, null otherwise
     */
    fun copyToEncryptedTempFile(fromPath: String, toPath: String, password: String): File? {
        val encryptedFile = File(fromPath)
        val decryptedFile = File(toPath, encryptedFile.name.removeSuffix(".enc"))
        decryptedFile.parentFile.mkdirs()

        val inputStream = if(fromPath.contains(".enc")) {
            cipherAccessor.access(fromPath) ?: return null
        } else {
            fileStreamAccessor.access(fromPath) ?: return null
        }

        val bytes = inputStream.readBytes()
        val pdf = PDDocument.load(bytes)
        inputStream.close()

        //If password is empty, don't protect the pdf.  Addresses an issue where BATDOK PDF Viewer
        //cannot render protected PDFs.
        if (password.isNotEmpty()) {
            val ap = AccessPermission()
            val spp = StandardProtectionPolicy(password, password, ap)
            spp.encryptionKeyLength = 256
            spp.permissions = ap
            pdf.protect(spp)
        }

        val outputStream = fileStreamAccessor.prepare(decryptedFile.absolutePath)
                ?: return decryptedFile
        pdf.save(outputStream)

        outputStream.flush()
        outputStream.close()

        return decryptedFile
    }


    /**
     * Write data to an encrypted PDF File
     * @param path - The path to the file
     * @param data - The data to be written
     * @return The File created if the path was accessible, null otherwise
     */
    override fun write(path: String, data: Any?): File? = runBlocking { coWrite(path, data) }

    /**
     * Write data to an encrypted PDF File
     * @param path - The path to the file
     * @param data - The data to be written
     * @return The File created if the path was accessible, null otherwise
     */
    override suspend fun coWrite(
        path: String,
        data: Any?,
        coroutineContext: CoroutineContext
    ): File? = withContext(coroutineContext){
        val file = File("$path.enc")
        file.parentFile?.mkdirs()
        val cipherOutputStream = cipherAccessor.prepare(file.path) ?: return@withContext null

        if (data is PDDocument) {
            // the PDDocument save function will close the cipherOutputStream
            data.save(cipherOutputStream)
            data.close()
        } else if (data is PdfDocument) {
            data.writeTo(cipherOutputStream)
            data.close()
            cipherOutputStream.flush()
            cipherOutputStream.close()
        }

        file
    }

    /**
     * Write data to a PDF File
     * @param path - The path to the file
     * @param data - The data to be written
     * @return The File created if the path was accessible, null otherwise
     */
    fun write(path: String, data: Any?, encrypted: Boolean): File? {
        if(encrypted){
            return write(path, data)
        }
        val file = File(path)
        file.parentFile.mkdirs()
        val cipherOutputStream = fileStreamAccessor.prepare(file.path) ?: return null

        if (data is PDDocument) {
            data.save(cipherOutputStream)
            data.close()

        } else if (data is PdfDocument) {
            data.writeTo(cipherOutputStream)
            data.close()
        }

        cipherOutputStream.flush()
        cipherOutputStream.close()

        return file
    }

    /**
     * Gets the OutputStream for the path provided
     * @param path - the Path to get the OutputStream for
     * @return The OutputStream if the path could be accessed, null otherwise
     */
    override fun getOutputStream(path: String): OutputStream? {
        return cipherAccessor.prepare(path)
    }
}