package batdok.batman.exportlibrary.io

import batdok.batman.exportlibrary.io.streams.CipherStreamAccessor
import batdok.batman.exportlibrary.io.streams.FileStreamAccessor
import com.tom_roush.pdfbox.io.IOUtils
import kotlinx.coroutines.withContext
import java.io.File
import java.io.InputStream
import java.io.OutputStream
import javax.crypto.SecretKey
import kotlin.coroutines.CoroutineContext

/**
 * Class is responsible for Encrypted File IO Operations
 * @param fileStreamAccessor - The FileStreamAccessor
 * @param cipherAccessor - The Cipher Accessor
 */
class EncryptedFileIO(private val fileStreamAccessor: FileStreamAccessor,
                      private val cipherAccessor: CipherStreamAccessor) : FileIO<ByteArray?, File?> {

    /**
     * Read from a path provided and return the bytes read
     * @param path - The path to read from
     * @return The bytes read if able, null if <PERSON><PERSON>her<PERSON><PERSON><PERSON><PERSON> could not access the path
     */
    override fun read(path: String): ByteArray? {
        val cipherInputStream = cipherAccessor.access(path) ?: return null

        val bytes = cipherInputStream.readBytes()
        cipherInputStream.close()

        return bytes
    }

    /**
     * Gets the InputStream based on the path provided
     * @return The Inputstream for the path if able, null if unable to access path
     */
    override fun getInputStream(path: String): InputStream?{
        return cipherAccessor.access(path)
    }

    /**
     * Copies data from an encrypted file to a decrypted file and returns the decrypted file
     * @param fromPath - The path to the encrypted file
     * @param toPath - The path for the decrypted file
     * @return The decrypted file if able to access the encrypted file and can write to the decrypted file, otherwise null
     */
    @JvmOverloads
    fun copyToDecrypted(fromPath: String, toPath: String): File? {
        val encryptedFile = File(fromPath)
        val decryptedFile = File(toPath, encryptedFile.name.removeSuffix(".enc"))
        decryptedFile.parentFile?.mkdirs() ?: return null

        val cipherInputStream = cipherAccessor.access(fromPath) ?: return null

        val outputStream = fileStreamAccessor.prepare(decryptedFile.absolutePath) ?: return decryptedFile

        IOUtils.copy(cipherInputStream, outputStream)
        outputStream.flush()
        outputStream.close()
        cipherInputStream.close()

        return decryptedFile
    }

    /**
     * Copies data from a decrypted file to an encrypted file and returns the encrypted file
     * @param fromPath - The path to the decrypted file
     * @param toPath - The path for the encrypted file
     * @return The encrypted file if able to access the decrypted file and can write to the encrypted file, otherwise null
     */
    @JvmOverloads
    fun copyToEncrypted(fromPath: String, toPath: String, encryptionKey: SecretKey? = null): File? {
        return fileStreamAccessor.access(fromPath)?.let{
            copyToEncrypted(it, toPath, encryptionKey)
        }
    }

    /**
     * Copies data from a decrypted file to an encrypted file and returns the encrypted file
     * @param inputStream - The input stream of the decrypted file
     * @param toPath - The path for the encrypted file
     * @return The encrypted file if able to access the decrypted file and can write to the encrypted file, otherwise null
     */
    @JvmOverloads
    fun copyToEncrypted(inputStream: InputStream, toPath: String, encryptionKey: SecretKey? = null): File? {
        val encryptedFile = File("$toPath.enc")
        encryptedFile.parentFile?.mkdirs() ?: return null

        val cipherOutputStream = cipherAccessor.prepare(encryptedFile.absolutePath) ?: return null

        IOUtils.copy(inputStream, cipherOutputStream)
        cipherOutputStream.flush()
        cipherOutputStream.close()
        inputStream.close()

        return encryptedFile
    }

    /**
     * Writes data to an encrypted file
     * @param path - The path to write the data
     * @param data - The data to write to the file
     */
    override suspend fun coWrite(
        path: String,
        data: Any?,
        coroutineContext: CoroutineContext
    ): File? = withContext(coroutineContext){
        val filePath = "$path.enc"
        val file = File(filePath)
        file.parentFile?.mkdirs() ?: return@withContext null
        val cipherOutputStream = cipherAccessor.prepare(filePath) ?: return@withContext null

        cipherOutputStream.write(data as? ByteArray ?: return@withContext null)
        cipherOutputStream.flush()
        cipherOutputStream.close()

        file
    }

    /**
     * Gets the OutputStream for the path provided
     * @param path - The path to get the OutputStream for
     */
    override fun getOutputStream(path: String): OutputStream? {
        return cipherAccessor.prepare(path)
    }
}