package batdok.batman.exportlibrary.io

import android.util.Log
import batdok.batman.exportlibrary.io.streams.FileStreamAccessor
import batdok.batman.exportlibrary.io.streams.ZipStreamAccessor
import kotlinx.coroutines.withContext
import java.io.*
import java.util.*
import java.util.zip.ZipEntry
import java.util.zip.ZipInputStream
import java.util.zip.ZipOutputStream
import kotlin.coroutines.CoroutineContext

/**
 * Class is responsible for Zip File IO
 * @param fileStreamAccessor - The FileStreamAccessor
 * @param zipAccessor - The ZipStreamAccessor
 */
class ZipIO(private val fileStreamAccessor: FileStreamAccessor,
            private val zipAccessor: ZipStreamAccessor) : FileIO<ByteArray?, File?> {

    /**
     * The default buffer size
     */
    private val BUFFER_SIZE = 10000

    /**
     * Read data from a zip file using the path provided
     * @param path - The path to read from
     * @return Returns the byte array read from the path, null if path cannot be accessed
     */
    override fun read(path: String): ByteArray? {
        val inputStream = zipAccessor.access(path) ?: return null
        val bytes = inputStream.readBytes()
        inputStream.close()
        return bytes
    }

    /**
     * Gets the InputStream for the path provided
     * @param path - the path to get the InputStream for
     * @return The InputStream if the path is accessible, null otherwise
     */
    override fun getInputStream(path: String): InputStream? {
        return zipAccessor.access(path)
    }

    /**
     * ZIPs the directory provided
     * @param path - Path to the directory to ZIP
     * @param data - Unused (can be null)
     * @return The File created if able, null if the path cannot be accessed
     */
    override suspend fun coWrite(
        path: String,
        data: Any?,
        coroutineContext: CoroutineContext
    ): File? = withContext(coroutineContext){
        val dir = File(path)
        dir.mkdirs()
        val files = dir.list() ?: return@withContext null
        var origin: BufferedInputStream?
        val out = zipAccessor.prepare(dir.path + "/" + dir.name + ".zip")
        try {
            val bufferedData = ByteArray(BUFFER_SIZE)

            for (i in files.indices) {
                val fi = fileStreamAccessor.access(path + "/" + files[i])
                origin = BufferedInputStream(fi, BUFFER_SIZE)
                try {
                    val entry = ZipEntry(files[i].substring(files[i].lastIndexOf("/") + 1))
                    out.putNextEntry(entry)
                    var count: Int = origin.read(bufferedData, 0, BUFFER_SIZE)
                    while (count != -1) {
                        out.write(bufferedData, 0, count)
                        count = origin.read(bufferedData, 0, BUFFER_SIZE)
                    }
                } finally {
                    origin.close()
                }
            }
        } finally {
            out.flush()
            out.close()
        }
        File(dir.path + "/" + dir.name + ".zip")
    }

    /**
     * Gets the OutputStream for the path provided
     * @param path - the path to get the OutputStream for
     * @param returns the OutputStream if the path can be accessed, null otherwise
     */
    override fun getOutputStream(path: String): OutputStream? {
        return zipAccessor.prepare(path)
    }

    /**
     * Take the list of files, zip them up, and write that to the zip Path
     *
     * @param srcFiles - The list of files to zip up
     * @param zipFile  - The file to write the zip file to
     * @return - True if successful
     */
    fun zipFilesIntoFile(srcFiles: List<File>, zipFile: File): Boolean {
        if (!zipFile.exists() && !zipFile.createNewFile()) {
            Log.i("ZIPIO", "Creating file " + zipFile.path + " failed while zipping files.")
            return false
        }

        try {
            FileOutputStream(zipFile).use { fos ->
                ZipOutputStream(fos).use { zos ->
                    // create byte buffer
                    val buffer = ByteArray(1024)
                    for (srcFile in srcFiles) {
                        FileInputStream(srcFile).use { fis ->
                            // begin writing a new ZIP entry, positions the stream to the start of the entry data
                            zos.putNextEntry(ZipEntry(srcFile.name))
                            var length: Int
                            while (fis.read(buffer).also { length = it } > 0) {
                                zos.write(buffer, 0, length)
                            }
                            zos.closeEntry()
                        }
                    }
                    return true
                }
            }
        } catch (ioe: IOException) {
            Log.d("ZIPIO", "Error creating zip file", ioe)
        }
        return false
    }

    /**
     * Unzip zipped data stream [inputStream] and place its files in [outputDir]
     */
    fun unzipFileToDir(inputStream: InputStream, outputDir: File): ArrayList<String> {
        val cfgPaths = ArrayList<String>()

        try {
            ZipInputStream(BufferedInputStream(inputStream)).use { zipStream ->
                val buffer = ByteArray(2048)

                var entry = zipStream.nextEntry
                while (entry != null) {
                    if (entry.isDirectory) {
                        File(outputDir, entry.name).mkdirs()
                        entry = zipStream.nextEntry
                        continue
                    }

                    val filename = entry.name.substringAfterLast("/")

                    val outputFile = File(outputDir, filename)
                    if (!outputFile.exists()) {
                        outputFile.createNewFile()
                    }

                    cfgPaths.add(outputFile.absolutePath)

                    FileOutputStream(outputFile.absolutePath).use { outputStream ->

                        var count = zipStream.read(buffer)
                        while (count != -1) {
                            outputStream.write(buffer, 0, count)
                            count = zipStream.read(buffer)
                        }

                        zipStream.closeEntry()
                        entry = zipStream.nextEntry
                    }
                }
            }
        } catch (e: IOException) {
            Log.e("ZIPIO", "Could not unzip file", e)
        }

        return cfgPaths
    }

}
