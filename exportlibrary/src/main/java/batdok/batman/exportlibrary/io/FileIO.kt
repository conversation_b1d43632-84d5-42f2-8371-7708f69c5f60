package batdok.batman.exportlibrary.io

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import java.io.InputStream
import java.io.OutputStream
import kotlin.coroutines.CoroutineContext

/**
 * Interface for FileIO
 */
interface FileIO<DATA, PATH> {

    /**
     * Read data from a path
     * @return the data type read from the path
     */
    fun read(path: String): DATA

    /**
     * Writes data to a path
     * @return the path of the location that the data was written
     */
    fun write(path: String, data: Any?): PATH = runBlocking { coWrite(path, data) }

    /**
     * Writes data to a path
     * @return the path of the location that the data was written
     */
    suspend fun coWrite(path: String, data: Any?, coroutineContext: CoroutineContext = Dispatchers.IO): PATH

    /**
     * Get the InputStream for a path
     * @return the InputStream, can be null
     */
    fun getInputStream(path: String): InputStream?

    /**
     * Gets the OutputStream for a path
     * @return the OutputStream, can be null
     */
    fun getOutputStream(path: String): OutputStream?
}