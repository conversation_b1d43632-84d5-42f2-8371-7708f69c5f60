package batdok.batman.exportlibrary.io.streams

import java.io.*

/**
 * Class is responsible for creating and returning a FileInputStream or FileOutputStream for a path
 */
open class FileStreamAccessor {

    /**
     * Creates and returns a FileInputStream based on the path provided
     * @param path - The path for the FileInputStream
     * @return The FileInputStream for the path if able, null otherwise
     */
    open fun access(path: String): InputStream? {
        return try {
            FileInputStream(path)
        } catch (e: FileNotFoundException) {
            null
        }
    }

    /**
     * Creates and returns a FileOutputStream based on the path provided
     * @param path - The path for the FileOutputStream
     * @return The FileOutputStream for the path
     */
    open fun prepare(path: String): OutputStream? {
        return try {
            FileOutputStream(path)
        } catch (e: FileNotFoundException) {
            e.printStackTrace()
            null
        }
    }
}