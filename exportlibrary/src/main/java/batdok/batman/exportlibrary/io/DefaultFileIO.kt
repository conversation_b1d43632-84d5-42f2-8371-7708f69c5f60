package batdok.batman.exportlibrary.io

import batdok.batman.exportlibrary.io.streams.FileStreamAccessor
import kotlinx.coroutines.withContext
import java.io.File
import java.io.InputStream
import java.io.OutputStream
import kotlin.coroutines.CoroutineContext

/**
 * Class is responsible for basic File IO. Includes, reading/writing and accessing the Input and Output Stream
 */
class DefaultFileIO(private val fileStreamAccessor: FileStreamAccessor): FileIO<ByteArray?, File?> {
    /**
     * Reads data from an input stream using the path provided
     * @param path - The path to read from
     * @return The byte array read from the input stream if file was accessible, null otherwise
     */
    override fun read(path: String): ByteArray? {
        val inputStream = fileStreamAccessor.access(path) ?: return null

        val bytes = inputStream.readBytes()
        inputStream.close()
        return bytes
    }

    /**
     * Gets the input stream for a path provided
     * @param path - The path for the input stream
     * @return The InputStream for the path if able, null otherwise
     */
    override fun getInputStream(path: String): InputStream? {
        return fileStreamAccessor.access(path)
    }

    /**
     * Writes data to a path provided with data provided
     * @param path- The path to write to
     * @param data- The data to be written to the path
     * @return The File that had data written to it, returns null if output stream could not be accessed
     */
    override suspend fun coWrite(
        path: String,
        data: Any?,
        coroutineContext: CoroutineContext
    ): File? = withContext(coroutineContext){
        val file = File(path)
        file.parentFile?.mkdirs()
        val outputStream = fileStreamAccessor.prepare(path) ?: return@withContext null

        outputStream.write(data as ByteArray)
        outputStream.flush()

        outputStream.close()

        file
    }

    /**
     * Gets the output stream using the path provided
     * @return The Outputstream if the path could be accessed, null otherwise
     */
    override fun getOutputStream(path: String): OutputStream? {
        return fileStreamAccessor.prepare(path)
    }
}