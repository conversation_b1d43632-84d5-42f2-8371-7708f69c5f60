package batdok.batman.exportlibrary.share.dd1380;

import android.content.Context;
import android.content.res.AssetManager;

import java.io.IOException;
import java.io.InputStream;

import batdok.batman.exportlibrary.io.PdfIO;

public class DD1380PdfExporter {
    private PdfIO pdfFileReaderWriter;
    private InputStream fontStream;
    private Context context;

    public DD1380PdfExporter(PdfIO pdfFileReaderWriter, Context context) {
        this.context = context;
        this.pdfFileReaderWriter = pdfFileReaderWriter;
        AssetManager assets = context.getAssets();
        try {
            fontStream = assets.open("com/tom_roush/pdfbox/resources/ttf/LiberationSans-Regular.ttf");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    // Export the tccc patient to pdf
    public boolean export(DD1380ExportModel handler, String path, boolean isKeyed) {
        DD1380PdfBuilder dd1380PdfBuilder = new DD1380PdfBuilder(handler, pdfFileReaderWriter, fontStream);
        try {
            DD1380DrawToCanvasPdf.draw(dd1380PdfBuilder, context, isKeyed);
            dd1380PdfBuilder.export(path, true);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}
