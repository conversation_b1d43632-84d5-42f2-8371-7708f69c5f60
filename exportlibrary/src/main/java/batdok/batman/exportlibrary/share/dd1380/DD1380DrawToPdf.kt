package batdok.batman.exportlibrary.share.dd1380

import gov.afrl.batdok.encounter.DrawingPoint
import java.io.IOException

/**
 * Created on 3/31/2017.
 */
interface DD1380DrawToPdf {
    @Throws(IOException::class)
    fun draw(exportModel: DD1380ExportModel?): Boolean
}

// Deep copy this point collection
fun deepCopyPointList(pointList: List<DrawingPoint>): List<DrawingPoint> {
    val clonedPointListList: MutableList<DrawingPoint> = ArrayList()
    for (point in pointList) {
        clonedPointListList.add(DrawingPoint(point.label, point.x, point.y))
    }
    return clonedPointListList
}
