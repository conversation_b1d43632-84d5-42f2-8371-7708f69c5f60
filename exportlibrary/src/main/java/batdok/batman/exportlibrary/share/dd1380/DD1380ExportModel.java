package batdok.batman.exportlibrary.share.dd1380;

import java.io.File;
import java.util.List;

import gov.afrl.batdok.commands.proto.DocumentCommands;
import gov.afrl.batdok.encounter.Document;

/**
 * Created on 3/30/2017.
 */

public class DD1380ExportModel {
    private String callsign;
    private String patientLocalName;
    private String firstResponderName;
    private String firstResponderLast4;
    private String firstResponderAfscMos;
    private String path;
    private Document document;
    private File bodyMap;
    private String countryCode;
    private List<DocumentCommands.CommandData> commands;

    public String getPatientLocalName() {
        return patientLocalName;
    }

    public String getFirstResponderName() {
        return firstResponderName;
    }

    public String getFirstResponderLast4() {
        return firstResponderLast4;
    }

    public String getFirstResponderAfscMos() {
        return firstResponderAfscMos;
    }

    public String getCallsign() {
        return callsign;
    }

    public Document getDocumentModel() {
        return document;
    }

    public String getPath() {
        return path;
    }

    public File getBodyMap() {
        return bodyMap;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public List<DocumentCommands.CommandData> getCommands() { return commands; }

    public DD1380ExportModel(String callsign, String patientLocalName, String firstResponderName, String firstResponderLast4, String firstResponderAfscMos,
                             Document document, List<DocumentCommands.CommandData> commands, String path, File bodyMap, String countryCode) {
        this.callsign = callsign;
        this.patientLocalName = patientLocalName;
        this.document = document;
        this.firstResponderLast4 = firstResponderLast4;
        this.firstResponderName = firstResponderName;
        this.firstResponderAfscMos = firstResponderAfscMos;
        this.path = path;
        this.bodyMap = bodyMap;
        this.countryCode = countryCode;
        this.commands = commands;
    }
}
