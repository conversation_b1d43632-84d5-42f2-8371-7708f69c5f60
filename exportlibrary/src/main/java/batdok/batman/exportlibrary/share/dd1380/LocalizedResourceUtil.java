package batdok.batman.exportlibrary.share.dd1380;

import android.content.Context;
import android.content.res.Configuration;
import android.content.res.Resources;

import java.util.Locale;

public class LocalizedResourceUtil {
    // Based on https://stackoverflow.com/a/33629163
    public static Resources createLocalizedResources(Context context, String localeString) {
        Configuration configuration = new Configuration(context.getResources().getConfiguration());
        configuration.setLocale(new Locale(localeString));
        return context.createConfigurationContext(configuration).getResources();
    }
}
