package batdok.batman.exportlibrary.share

import android.graphics.Bitmap
import batdok.batman.exportlibrary.io.PdfIO
import batdok.batman.exportlibrary.valueobject.RGB
import com.tom_roush.harmony.awt.AWTColor
import com.tom_roush.pdfbox.cos.*
import com.tom_roush.pdfbox.pdmodel.PDDocument
import com.tom_roush.pdfbox.pdmodel.PDPage
import com.tom_roush.pdfbox.pdmodel.PDPageContentStream
import com.tom_roush.pdfbox.pdmodel.PDResources
import com.tom_roush.pdfbox.pdmodel.common.PDRectangle
import com.tom_roush.pdfbox.pdmodel.font.PDFont
import com.tom_roush.pdfbox.pdmodel.font.PDType0Font
import com.tom_roush.pdfbox.pdmodel.font.PDType1Font
import com.tom_roush.pdfbox.pdmodel.graphics.image.LosslessFactory
import com.tom_roush.pdfbox.pdmodel.graphics.image.PDImageXObject
import com.tom_roush.pdfbox.pdmodel.interactive.annotation.PDAppearanceCharacteristicsDictionary
import com.tom_roush.pdfbox.pdmodel.interactive.annotation.PDAppearanceDictionary
import com.tom_roush.pdfbox.pdmodel.interactive.annotation.PDAppearanceEntry
import com.tom_roush.pdfbox.pdmodel.interactive.form.PDAcroForm
import com.tom_roush.pdfbox.pdmodel.interactive.form.PDCheckBox
import com.tom_roush.pdfbox.pdmodel.interactive.form.PDTextField
import com.tom_roush.pdfbox.util.Matrix
import java.io.File
import java.io.IOException
import java.io.InputStream
import kotlin.math.max

abstract class PDFBuilder(private val pdfReaderWriter: PdfIO, fontStream: InputStream? = null) {

    open fun getDimensions(): Dimensions{
        return Dimensions()
    }

    class Dimensions(val width: Int = 450, val height: Int = 594, val offsetX: Int = 10, val offsetY: Int = 10)


    private val k = 0.552284749831f
    private var document: PDDocument = PDDocument()
    private var currentPage = PDPage(PDRectangle(getDimensions().width.toFloat(), getDimensions().height.toFloat()))
    private var stream: PDPageContentStream = PDPageContentStream(document, currentPage)
    private var currentContentHeight = 0
    private var acroForm = PDAcroForm(document)
    private val pdfFont = if(fontStream == null) PDType1Font.HELVETICA else PDType0Font.load(document, fontStream)

    init {
        val resources = PDResources()
        resources.put(COSName.getPDFName("Helv"), PDType1Font.HELVETICA)
        acroForm.defaultResources = resources
        document.documentCatalog.acroForm = acroForm
    }

    @Throws(IOException::class)
    fun calculateTextWidth(s: String, fontSize: Int): Float {
        return if (s.contains("\n")) {
            val index = s.lastIndexOf("\n")
            max(calculateTextWidth(s.substring(0, index), fontSize), calculateTextWidth(s.substring(index + 1), fontSize))
        } else {
            pdfFont.getStringWidth(s) / 1000 * fontSize
        }
    }

    @Throws(IOException::class)
    fun drawLine(startX: Float, startY: Float, endX: Float, endY: Float) {
        val startYInverse = getDimensions().height - startY
        val endYInverse = getDimensions().height - endY
        stream.moveTo(startX, startYInverse)
        stream.lineTo(endX, endYInverse)
        stream.stroke()
    }

    @Throws(IOException::class)
    @JvmOverloads
    fun drawText(s: String, startX: Float, startY: Float, size: Int, rgb: RGB? = RGB(0, 0, 0)) {
        val startYInverse = getDimensions().height - startY
        if (s.contains("\n")) {
            val index = s.lastIndexOf("\n")
            val line1 = s.substring(0, index)
            val line2 = s.substring(index + 1)
            drawText(line1, startX, startYInverse, size)
            drawText(line2, startX, startYInverse + 15, size)
        } else {
            stream.beginText()
            stream.setFont(pdfFont, size.toFloat())
            stream.setNonStrokingColor(rgb!!.red, rgb.green, rgb.blue)
            stream.newLineAtOffset(startX, startYInverse)
            stream.showText(s)
            stream.endText()
            stream.setNonStrokingColor(0, 0, 0)
        }

        if (currentContentHeight < startY) {
            currentContentHeight = startY.toInt()
        }
    }

    @Throws(IOException::class)
    @JvmOverloads
    fun drawCenteredText(s: String, centerX: Float, startY: Float, size: Int, rgb: RGB = RGB(0, 0, 0)) {
        val halfWidth = calculateTextWidth(s, size)/2f;
        val startYInverse = getDimensions().height - startY
        if (s.contains("\n")) {
            val index = s.lastIndexOf("\n")
            val line1 = s.substring(0, index)
            val line2 = s.substring(index + 1)
            drawText(line1, centerX-halfWidth, startYInverse, size)
            drawText(line2, centerX-halfWidth, startYInverse + 15, size)
        } else {
            stream.beginText()
            stream.setFont(pdfFont, size.toFloat())
            stream.setNonStrokingColor(rgb.red, rgb.green, rgb.blue)
            stream.newLineAtOffset(centerX-halfWidth, startYInverse)
            stream.showText(s)
            stream.endText()
            stream.setNonStrokingColor(0, 0, 0)
        }

        if (currentContentHeight < startY) {
            currentContentHeight = startY.toInt()
        }
    }

    @Throws(IOException::class)
    @JvmOverloads
    fun drawRect(startX: Float, startY: Float, endX: Float, endY: Float, background: AWTColor = AWTColor.WHITE) {
        val startYInverse = getDimensions().height - startY
        val endYInverse = getDimensions().height - endY
        stream.addRect(startX, endYInverse, endX - startX, startYInverse - endYInverse)
        if (background != AWTColor.WHITE) {
            stream.setNonStrokingColor(background)
            stream.fill()
            stream.setNonStrokingColor(0, 0, 0)
        }
        stream.stroke()
    }

    @Throws(IOException::class)
    fun drawCircle(centerX: Float, centerY: Float, radius: Float, background: AWTColor = AWTColor.WHITE) {
        val y = getDimensions().height - centerY
        stream.moveTo(centerX - radius, y)
        stream.curveTo(centerX - radius, y + k * radius, centerX - k * radius, y + radius, centerX, y + radius)
        stream.curveTo(centerX + k * radius, y + radius, centerX + radius, y + k * radius, centerX + radius, y)
        stream.curveTo(centerX + radius, y - k * radius, centerX + k * radius, y - radius, centerX, y - radius)
        stream.curveTo(centerX - k * radius, y - radius, centerX - radius, y - k * radius, centerX - radius, y)
        if (background != AWTColor.WHITE) {
            stream.setNonStrokingColor(background)
            stream.fill()
            stream.setNonStrokingColor(AWTColor.WHITE)
        }
        stream.stroke()
    }

    @Throws(IOException::class)
    @JvmOverloads
    fun drawCheckbox(name: String, isChecked: Boolean, x: Float, y: Float, textSize: Int, background: AWTColor? = AWTColor(0, 0, 0)): Float {
        drawText(name, x + 7, y + 3, textSize)

        drawCheckboxNoText(name, isChecked, x, y)

        val height = calculateTextWidth(name, textSize) + 5f + 20f

        if (currentContentHeight < y + height) {
            currentContentHeight = y.toInt() + height.toInt()
        }
        return height
    }

    @JvmOverloads
    fun drawCheckboxNoText(name: String, isChecked: Boolean, x: Float, y: Float, boxSize: Float = 10f): Float {
        val startYInverse = getDimensions().height - y - boxSize/2

        val checkBox = PDCheckBox(acroForm)
        //adding the fields size to partial name ensures that no duplicate names will exist
        checkBox.partialName = name + acroForm.fields.size

        val widget = checkBox.widgets[0]
        widget.rectangle = PDRectangle(x - boxSize/2, startYInverse, boxSize, boxSize)
        widget.annotationFlags = 4
        widget.page = currentPage

        // inspired by annot 92 of file from PDFBOX-563
        // annot 172 has "checkmark" instead, but more more complex, needs ZaDb

        val end = String.format("%.1f", boxSize-.5)
        val endMin1 = String.format("%.1f", boxSize-1)
        val endMin2 = String.format("%f", boxSize-2)
        val offNString = ("1 g\n"
                + "0 0 $end $end re\n"
                + "f\n"
                + "0.5 0.5 $endMin1 $endMin1 re\n"
                + "s")
        val yesNString = ("1 g\n"
                + "0 0 $end $end re\n"
                + "f\n"
                + "0.5 0.5 $endMin1 $endMin1 re\n"
                + "s\n"
                + "q\n"
                + "  2 $endMin2 m\n"
                + "  $endMin2 2 l\n"
                + "  $endMin2 $endMin2 m\n"
                + "  2 2 l\n"
                + "  s\n"
                + "Q")

        val apNDict = COSDictionary()
        val offNStream = createCheckboxCOSStream(offNString, boxSize)
        apNDict.setItem(COSName.Off, offNStream)

        val yesNStream = createCheckboxCOSStream(yesNString, boxSize)
        apNDict.setItem(COSName.getPDFName("Yes"), yesNStream)

        val appearance = PDAppearanceDictionary()
        val appearanceNEntry = PDAppearanceEntry(apNDict)
        appearance.normalAppearance = appearanceNEntry

        widget.appearance = appearance

        // MK (appearance characteristics dictionary)
        val acdDict = COSDictionary()
        acdDict.setItem(COSName.CA, COSString("8")) // 8 is X, 4 is checkmark
        val bcArray = COSArray()
        bcArray.add(COSInteger.ZERO)
        acdDict.setItem(COSName.BC, bcArray)
        val bgArray = COSArray()
        bgArray.add(COSInteger.ONE)
        acdDict.setItem(COSName.BG, bgArray)
        val acd = PDAppearanceCharacteristicsDictionary(acdDict)
        widget.appearanceCharacteristics = acd
        widget.isPrinted = true
        currentPage.annotations.add(widget)

        if(isChecked){
            checkBox.check()
        }
        else{
            checkBox.unCheck()
        }
        acroForm.fields.add(checkBox)

        val height = 5f + 20f

        if (currentContentHeight < y + height) {
            currentContentHeight = y.toInt() + height.toInt()
        }
        return height
    }

    fun createCheckboxCOSStream(string: String, boxSize: Float = 10f): COSStream{
        val cosStream = COSStream()
        cosStream.setItem(COSName.BBOX, PDRectangle(boxSize, boxSize))
        cosStream.setItem(COSName.FORMTYPE, COSInteger.ONE)
        cosStream.setItem(COSName.TYPE, COSName.XOBJECT)
        cosStream.setItem(COSName.SUBTYPE, COSName.FORM)
        cosStream.setItem(COSName.MATRIX, Matrix().toCOSArray())
        cosStream.setItem(COSName.RESOURCES, COSDictionary())
        var os = cosStream.createOutputStream()
        os.write(string.toByteArray())//was the java method toBytes()
        os.close()
        return cosStream
    }

    @Throws(IOException::class)
    fun drawNonEditableCheckbox(name: String, isChecked: Boolean, x: Float, y: Float, textSize: Int, background: AWTColor = AWTColor(0, 0, 0)): Float {

        drawRect(x - 5, y - 5, x + 5, y + 5, background)
        drawText(name, x + 7, y + 3, textSize)

        if (isChecked) {
            drawText("X", x - 3, y + 4, textSize, RGB(255, 0, 0))
        }

        val height = calculateTextWidth(name, textSize) + 5f + 20f

        if (currentContentHeight < y + height) {
            currentContentHeight = y.toInt() + height.toInt()
        }
        return height
    }


    @Throws(IOException::class)
    fun drawCheckboxList(checkboxes: List<String>, isChecked: List<Boolean>, x: Float, y: Float, textSize: Int, background: AWTColor = AWTColor(0, 0, 0)): Float {
        var curSpacingX = 0f
        for (i in checkboxes.indices) {
            curSpacingX += drawCheckbox(checkboxes[i], isChecked[i], x + curSpacingX, y, textSize, background)
        }

        return curSpacingX
    }

    @Throws(IOException::class)
    fun drawCheckboxList(checkboxes: List<Pair<String, Boolean>>, x: Float, y: Float, textSize: Int, background: AWTColor = AWTColor(0, 0, 0)): Float {
        var curSpacingX = 0f
        for (i in checkboxes.indices) {
            curSpacingX += drawCheckbox(checkboxes[i].first, checkboxes[i].second, x + curSpacingX, y, textSize, background)
        }

        return curSpacingX
    }

    @JvmOverloads
    @Throws(IOException::class)
    fun drawRectangleField(name: String, value: String, left: Float, top: Float, right: Float, bottom: Float, textSize: Int, centered: Boolean = false, rgb: RGB = RGB()){
        drawRect(left, top, right, bottom)

        //This value is appropriate for the Helvetica font
        val rectangle = PDRectangle(left, getDimensions().height - bottom, right-left, bottom-top)

        val textField = PDTextField(acroForm)

        sharedTextFieldActions(textField, name, textSize, rectangle, true)

        if(centered) textField.q = 1
        textField.value = value
        textField.isMultiline = true
    }

    @Throws(IOException::class)
    fun drawField(name: String, value: String, x: Float, y: Float, width: Float, offsetLine: Float, labelSize: Int, textSize: Int, rgb: RGB? = RGB()) {
        // make extra room in the field if needed
        val newWidth = max(calculateTextWidth(value, textSize) + 5, width)

        drawText(name, x, y, labelSize)

        //This value is appropriate for the Helvetica font
        val fieldHeight = pdfFont.fontDescriptor.fontBoundingBox.height / 1000 * 12 * (.865).toFloat()
        drawFieldNoText(name, value, calculateTextWidth(name, labelSize) + x + offsetLine, y, width, fieldHeight, labelSize)
    }

    @Throws(IOException::class)
    @JvmOverloads
    fun drawFieldNoText(name: String, value: String, x: Float, y: Float, width: Float, height: Float, textSize: Int, centered: Boolean = false) {
        drawLine(x, y, x + width, y)

        val rectangle = PDRectangle(x, getDimensions().height - y - 1, width, height)

        val textField = PDTextField(acroForm)

        sharedTextFieldActions(textField, name, textSize, rectangle)

        if(centered) textField.q = 1
        textField.value = value
        textField.maxLen = (width/(pdfFont.averageFontWidth/1000 * textSize)).toInt()
    }

    @Throws(IOException::class)
    @JvmOverloads
    fun drawCenteredField(name: String, value: String, x: Float, y: Float, width: Float, textSize: Int, rgb: RGB = RGB()) {
        // make extra room in the field if needed
        val newWidth = max(calculateTextWidth(value, textSize) + 5, width)

        drawText(name, x, y, textSize)
        drawLine(calculateTextWidth(name, textSize) + x + 5, y, x + newWidth + calculateTextWidth(name, textSize), y)

        //This value is appropriate for the Helvetica font
        val fieldHeight = pdfFont.fontDescriptor.fontBoundingBox.height / 1000 * 12 * (.865).toFloat()

        val rectangle = PDRectangle((calculateTextWidth(name, textSize) + x + 5), getDimensions().height - y - 1, newWidth, fieldHeight)

        val textField = PDTextField(acroForm)

        sharedTextFieldActions(textField, name, textSize, rectangle)

        textField.q = 1
        textField.value = value
        textField.maxLen = (newWidth/(pdfFont.averageFontWidth/1000 * textSize)).toInt()
    }

    @Throws(IOException::class)
    fun drawField(name: String, value: String, x: Float, y: Float, width: Float, offsetLine: Float, textSize: Int, rgb: RGB = RGB()) {
        drawField(name, value, x, y, width, offsetLine, textSize, textSize, rgb)
    }

    @Throws(IOException::class)
    @JvmOverloads
    fun drawField(name: String, value: String, x: Float, y: Float, width: Float, textSize: Int, rgb: RGB = RGB()) {
        drawField(name, value, x, y, width, 5f, textSize, rgb)
    }

    @Throws(IOException::class)
    fun drawTable(value: String, x: Float, y: Float, width: Float, height: Float, centered:Boolean, textSize: Int, rgb: RGB? = RGB()) {

        val rectangle = PDRectangle(x, getDimensions().height - y - 1, width, height)

        val textField = PDTextField(acroForm)

        sharedTextFieldActions(textField, "table_field", textSize, rectangle)

        //centers the text
        if(centered){
            textField.q = 1
        }

        textField.value = value.trim()
        textField.maxLen = (width/(pdfFont.averageFontWidth/1000 * textSize)).toInt()
    }

    @Throws(IOException::class)
    fun drawExtraNotesAcroForm(text: String, x: Float, y: Float, width: Float, height: Float, textSize: Int) {
        val rectangle = PDRectangle(x, getDimensions().height - y - 1, width, height)

        val textField = PDTextField(acroForm)

        sharedTextFieldActions(textField, "notes_field", textSize, rectangle, true)
        textField.value = text
        textField.isMultiline = true
    }

    fun sharedTextFieldActions(textField: PDTextField, fieldName: String, textSize: Int, fieldBorder: PDRectangle, multiline: Boolean = false){
        //adding the fields size to partial name ensures that no duplicate names will exist
        textField.partialName = fieldName + acroForm.fields.size
        textField.defaultAppearance = "/Helv $textSize Tf 0 0 0 rg"
        textField.isMultiline = multiline
        acroForm.fields.add(textField)

        val widget = textField.widgets[0]
        widget.rectangle = fieldBorder
        widget.page = currentPage

        widget.isPrinted = true
        currentPage.annotations.add(widget)
    }

    @Throws(IOException::class)
    fun drawBitmap(image: PDImageXObject, width: Int, height: Int, x: Float, y: Float) {
        stream.drawImage(image, x, y, width.toFloat(), height.toFloat())

        if (currentContentHeight < y + height) {
            currentContentHeight = height + y.toInt()
        }
    }

    @Throws(IOException::class)
    fun drawBitmap(image: Bitmap, x: Float, y: Float) {
        val pdImageObject = LosslessFactory.createFromImage(document, image)
        stream.drawImage(pdImageObject, x, y, image.width.toFloat(), image.height.toFloat())

        if (currentContentHeight < y + image.height) {
            currentContentHeight = image.height + y.toInt()
        }
    }

    fun getCurrentPage(): PDPage {
        return currentPage
    }

    fun getDocument(): PDDocument {
        return document
    }

    fun getStream(): PDPageContentStream {
        return stream
    }

    fun getPdfFont(): PDFont {
        return pdfFont
    }

    fun setCurrentContentHeight(height: Int) {
        this.currentContentHeight = height
    }

    fun getCurrentContentHeight(): Int {
        return currentContentHeight
    }

    @Throws(IOException::class)
    fun createPage(): PDPage {
        currentContentHeight = 0
        val page = PDPage(PDRectangle(getDimensions().width.toFloat(), getDimensions().height.toFloat()))
        document.addPage(page)
        currentPage = page
        stream.close()
        stream = PDPageContentStream(document, page)
        stream.setNonStrokingColor(0, 0, 0)
        stream.setStrokingColor(0, 0, 0)

        return page
    }

    @Throws(IOException::class)
    fun export(filePath: String, encrypted: Boolean): File? {
        stream.close()
        return pdfReaderWriter.write(filePath, document, encrypted)
    }

    /**
     * Calculates the maximum number of characters that can fit in a certain width
     * based on the fonts average font width
     * @param width the width of the field
     * @param textSize the text size used
     * @return the max number of characters that can fit in that width.
     */
    fun calculateFieldMaxLength(width: Float, textSize: Int) : Int {
        //if font doesn't have an average font width, then use helvetica bold for calculation.
        val averageFontWidth =
                if (pdfFont.averageFontWidth == 0f)
                    PDType1Font.HELVETICA.averageFontWidth
                else
                    pdfFont.averageFontWidth
        return (width / (averageFontWidth / 1000 * textSize)).toInt()
    }

    /**
     * Checks if the text can fit within a certain field width and if it cannot,
     * creates a shortened text appended with ellipsis.
     * @param originalText Text to fit
     * @param fieldWidth Width of field to fit text into
     * @return Shortened text
     */
    fun getEllipsizedText(originalText: String, fieldWidth: Float, textSize: Int): String {
        val maxFieldLength = calculateFieldMaxLength(fieldWidth, textSize)
        return if (originalText.length > maxFieldLength) originalText.substring(0, maxFieldLength - 3) + "..." else originalText
    }

    /**
     * Checks if the text can fit within a certain field width and if it cannot,
     * creates a shortened text appended with ellipsis, split on a space.
     * @param originalText Text to fit
     * @param fieldWidth Width of field to fit text into
     * @return Shortened text
     */
    fun getEllipsizedTextByWord(originalText: String, fieldWidth: Float, textSize: Int): String {
        val words = originalText.split(Regex("\\s"))
        // After testing a bit, a text width of 135 didn't fit in a field width of 150,
        // so we need give less room before ellipsizing to make sure it will fit.
        val paddedFieldWidth = fieldWidth-20
        if(words.size == 1){
            return getEllipsizedText(originalText, paddedFieldWidth, textSize)
        }
        var ellipsizedText = words[0]
        var i = 1
        while(i < words.size && calculateTextWidth("$ellipsizedText...", textSize) < paddedFieldWidth) {
            ellipsizedText += " " + words[i]
            i++
        }
        return if(calculateTextWidth(ellipsizedText, textSize) > paddedFieldWidth){
            ellipsizedText.removeSuffix(words[i-1]) + "..."
        }else {
            ellipsizedText
        }
    }
}