package batdok.batman.exportlibrary.share.dd1380;

import android.content.res.Resources;

import com.tom_roush.harmony.awt.AWTColor;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

import batdok.batman.exportlibrary.valueobject.RGB;

/**
 * Created on 1/31/2017.
 */
public class DD1380PdfTable {

    private DD1380PdfBuilder dd1380PdfBuilder;

    private int numRows = 0;

    final float rowHeight = 12;

    private float currentOffsetY;

    private Resources localizedResources;

    public int height() {
        return numRows * (int) rowHeight + (int) currentOffsetY;
    }

    private HashMap<String, ArrayList<String>> ellipsizedNamesMap = new LinkedHashMap<>();

    public DD1380PdfTable(TableBuilder tableBuilder, DD1380PdfBuilder DD1380PdfBuilder, int numRows, Resources localizedResources) throws IOException {
        this.numRows = numRows;
        this.dd1380PdfBuilder = DD1380PdfBuilder;
        this.localizedResources = localizedResources;
        setupBuilder(tableBuilder);
    }

    private void setupBuilder(TableBuilder tableBuilder) throws IOException {

        float x = tableBuilder.getX();
        float y = tableBuilder.getY();

        float incY = 15;
        float pageOffsetX = 20;

        float incX = 0;
        int b = 0;
        for (String header : tableBuilder.getColumnList()) {
            float offsetX = dd1380PdfBuilder.calculateTextWidth(header, tableBuilder.textSize) / 2.0f;
            float halfOfColumn = tableBuilder.getColumnWidths().get(b) / 2.0f;

            dd1380PdfBuilder.drawText(header,
                    x + pageOffsetX + tableBuilder.getTableNameWidth() + incX - offsetX + halfOfColumn,
                    y + 7, tableBuilder.textSize, new RGB());

            dd1380PdfBuilder.drawRect(x + pageOffsetX + tableBuilder.getTableNameWidth() + incX,
                    y - 2,
                    x + pageOffsetX + tableBuilder.getTableNameWidth() + incX + tableBuilder.getColumnWidths().get(b),
                    y + 12, AWTColor.WHITE);

            incX += tableBuilder.getColumnWidths().get(b);
            ++b;
        }

        incY = 0;
        float pageOffsetY = y;
        boolean isFirstPage = true;
        for (int i = 0; i < tableBuilder.getTableList().size(); ++i) { // tables
            String tableName = tableBuilder.getTableList().get(i);
            float offsetX = dd1380PdfBuilder.calculateTextWidth(tableName, tableBuilder.textSize) / 2.0f;
            float halfWidth = (tableBuilder.getTableNameWidth() / 2.0f);
            //create an entry in the hashmap for the table
            if (!ellipsizedNamesMap.containsKey(tableName)) {
                ellipsizedNamesMap.put(tableName, new ArrayList<>());
            }

            // table name
            dd1380PdfBuilder.drawText(
                    tableName,
                    x + pageOffsetX + halfWidth - offsetX,
                    pageOffsetY + incY + rowHeight + 9,
                    tableBuilder.textSize, new RGB());

            for (int row = 0; row < tableBuilder.getValueList().get(i).size(); ++row) { // rows
                incX = 0;
                for (int col = 0; col < tableBuilder.getValueList().get(i).get(row).size(); ++col) { // columns
                    if (tableBuilder.getY() + incY + rowHeight > dd1380PdfBuilder.getCurrentPage().getBBox().getHeight() - rowHeight * 4) {
                        // Create the table rectangle
                        if (!isFirstPage) {
                            dd1380PdfBuilder.drawRect(
                                    x + pageOffsetX,
                                    pageOffsetY + rowHeight,
                                    x + pageOffsetX + tableBuilder.getTableNameWidth() + 1,
                                    pageOffsetY + incY + rowHeight, AWTColor.WHITE);
                        } else {
                            dd1380PdfBuilder.drawRect(
                                    x + pageOffsetX,
                                    pageOffsetY + rowHeight,
                                    x + pageOffsetX + tableBuilder.getTableNameWidth() + 1,
                                    pageOffsetY + incY + rowHeight, AWTColor.WHITE);
                            isFirstPage = false;
                        }

                        // ... request a new stream
                        dd1380PdfBuilder.createPage();
                        dd1380PdfBuilder.drawHeaderFooter(localizedResources);
                        tableBuilder.setY(dd1380PdfBuilder.getCurrentContentHeight());
                        incY = 0;
                        pageOffsetY = dd1380PdfBuilder.getCurrentContentHeight();
                    }

                    // Create data rectangle

                    // Create the table text
                    Float startX = tableBuilder.getX() + tableBuilder.getTableNameWidth() + pageOffsetX + incX;
                    Float startY = pageOffsetY + incY + rowHeight;
                    Float width = tableBuilder.getX() + tableBuilder.getTableNameWidth() + pageOffsetX + tableBuilder.getColumnWidths().get(col) + incX - startX;
                    Float height = pageOffsetY + incY + rowHeight + rowHeight - startY;

                    dd1380PdfBuilder.drawRect(
                            startX,
                            startY,
                            tableBuilder.getX() + tableBuilder.getTableNameWidth() + pageOffsetX + tableBuilder.getColumnWidths().get(col) + incX,
                            pageOffsetY + incY + rowHeight + rowHeight, AWTColor.WHITE);


                    // Create data text
                    String dataText = tableBuilder.getValueList().get(i).get(row).get(col);

                    if (dataText == null) dataText = "";
                    String originalDataText = dataText;
                    dataText = dd1380PdfBuilder.getEllipsizedText(dataText, width - 2, tableBuilder.textSize);
                    //If the text is ellipsized, store the original name into the arraylist for that table.
                    if (!dataText.equals(originalDataText) && !ellipsizedNamesMap.get(tableName).contains(originalDataText)) {
                        ellipsizedNamesMap.get(tableName).add(originalDataText);
                    }

                    dd1380PdfBuilder.drawTable(dataText, startX + 2, pageOffsetY + incY + rowHeight / 2.0f + 3.0f + rowHeight+1, width - 4, height, true, tableBuilder.textSize, new RGB());

                    incX += tableBuilder.getColumnWidths().get(col);
                }
                incY += rowHeight;
            }

            // Create the table rectangle
            dd1380PdfBuilder.drawRect(
                    x + pageOffsetX,
                    pageOffsetY + rowHeight,
                    x + pageOffsetX + tableBuilder.getTableNameWidth(),
                    pageOffsetY + incY + rowHeight, AWTColor.WHITE);

            currentOffsetY = incY + pageOffsetY;
        }
    }


    public HashMap<String, ArrayList<String>> getEllipsizedNamesMap() {
        return ellipsizedNamesMap;
    }

    public static final class TableBuilder {
        private List<String> tableList;
        private List<String> columnList;
        private List<List<List<String>>> valueList;
        private List<Integer> columnWidths;
        private batdok.batman.exportlibrary.share.dd1380.DD1380PdfBuilder DD1380PdfBuilder;
        private float x;
        private float y;
        private float tableNameWidth;
        private float columnWidth;
        private int numRows;

        public List<String> getTableList() {
            return tableList;
        }

        public List<String> getColumnList() {
            return columnList;
        }

        public List<List<List<String>>> getValueList() {
            return valueList;
        }

        public float getX() {
            return x;
        }

        public float getY() {
            return y;
        }

        public float getTableNameWidth() {
            return tableNameWidth;
        }

        public float getColumnWidth() {
            return columnWidth;
        }

        public void setY(float y) {
            this.y = y;
        }

        public List<Integer> getColumnWidths() {
            return columnWidths;
        }

        public int textSize;

        public TableBuilder(batdok.batman.exportlibrary.share.dd1380.DD1380PdfBuilder DD1380PdfBuilder, float tableNameWidth,
                            float columnWidth, int textSize, float currentOffsetY) {
            this.DD1380PdfBuilder = DD1380PdfBuilder;
            this.tableList = new ArrayList<>();
            this.columnList = new ArrayList<>();
            this.valueList = new ArrayList<>();
            this.x = 0;
            this.y = 0;
            this.tableNameWidth = tableNameWidth;
            this.columnWidth = columnWidth;
            this.columnWidths = new ArrayList<>();
            this.textSize = textSize;
            this.DD1380PdfBuilder.setCurrentContentHeight((int)currentOffsetY);
        }

        public TableBuilder addTable(String name) {
            tableList.add(name);
            List<List<String>> list = new ArrayList<>();
            valueList.add(list);
            return this;
        }

        public TableBuilder addColumn(String name, int width) {
            columnList.add(name);
            columnWidths.add(width);
            return this;
        }

        public TableBuilder addRow(int tableIndex, List<String> values) {
            if (valueList.size() <= tableIndex) {
                valueList.add(new ArrayList<List<String>>());
            }
            valueList.get(tableIndex).add(values);
            ++numRows;
            return this;
        }

        public TableBuilder setPosition(float x, float y) {
            this.x = x;
            this.y = y;
            return this;
        }

        public DD1380PdfTable build(Resources localizedResources) throws IOException {
            return new DD1380PdfTable(this, DD1380PdfBuilder, numRows, localizedResources);
        }
    }
}
