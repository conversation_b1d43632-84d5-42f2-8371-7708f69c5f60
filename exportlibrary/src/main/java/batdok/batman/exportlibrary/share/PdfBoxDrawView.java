package batdok.batman.exportlibrary.share;

import android.graphics.Color;

import com.tom_roush.pdfbox.pdmodel.PDPageContentStream;
import com.tom_roush.pdfbox.pdmodel.font.PDFont;
import com.tom_roush.pdfbox.pdmodel.font.PDType1Font;
import com.tom_roush.pdfbox.pdmodel.graphics.color.PDColor;
import com.tom_roush.pdfbox.pdmodel.graphics.color.PDDeviceRGB;

import org.jetbrains.annotations.NotNull;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import gov.afrl.batdok.encounter.DrawingPoint;

/**
 * Use this view for drawing on the screen.
 */
public class PdfBoxDrawView {

    private List<DrawingPoint> pointListList;
    private float width;
    private float height;
    private static float pageHeight;
    private final float verticalAdjustment;

    public void initialize() {
        pointListList = new ArrayList<>();  //List of freeform shapes
        width = -1;
        height = -1;
    }

    public void setPointShapeList(List<DrawingPoint> pointShapeList) {
        pointListList = pointShapeList;
    }

    private final PDPageContentStream stream;

    public PdfBoxDrawView(PDPageContentStream stream, int verticalAdjustment) {
        this.stream = stream;
        this.verticalAdjustment = verticalAdjustment;
        initialize();
    }

    // Deep copy this point collection
    private List<DrawingPoint> deepCopyPointListList(List<DrawingPoint> pointListList) {
        List<DrawingPoint> clonedPointListList = new ArrayList<>();

        for (DrawingPoint point : pointListList) {
            clonedPointListList.add(new DrawingPoint(point.getLabel(), point.getX(), point.getY()));
        }
        return clonedPointListList;
    }

    /**
     * Draw the same points from the DrawView to a different canvas given offsets in the x and y direction
     * Also provide the width and height of the canvas and the paint for the line
     */
    public void drawToCanvas(int x, int y, int width, int height, boolean showLabels, float pageHeight) throws IOException {
        drawToCanvas(x, y, width, height, showLabels, pageHeight, 40);
    }
    public void drawToCanvas(int x, int y, int width, int height, boolean showLabels, float pageHeight, int xFactor) throws IOException {
        PdfBoxDrawView.pageHeight = pageHeight;
        List<DrawingPoint> copiedPointListList = deepCopyPointListList(pointListList);
        PDColor textPaint;
        //Move to the first point, line to the rest.
        //TODO: fix off-by-one size on QR patient point list/color list
        for (int i = 0; i < copiedPointListList.size(); i++) {

            DrawingPoint point = copiedPointListList.get(i);
            Color color = Color.valueOf(point.getColor());
            float red = color.red() / 255f;
            float green = color.green() / 255f;
            float blue = color.blue() / 255f;

            textPaint = new PDColor(new float[]{red, green, blue}, PDDeviceRGB.INSTANCE);

            String label = point.getLabel();

            float xWidth = width / xFactor;
            float lineWidth = xWidth * 1.5f;
            if (label.startsWith("TQ")) {
                drawLine(stream, textPaint, point.getScaledX(width) - lineWidth + x, point.getScaledY(height) + y, point.getScaledX(width) + lineWidth + x, point.getScaledY(height) + y);
                drawLine(stream, textPaint, width - (point.getScaledX(width) - lineWidth) + x, point.getScaledY(height) + y, width - (point.getScaledX(width) + lineWidth) + x, point.getScaledY(height) + y);
            }else {
                drawX(x, y, width, height, point, textPaint, xFactor);
            }
            if (showLabels) {
                drawText(stream, label, point.getScaledX(width) + x, point.getScaledY(height) + y - (xWidth + 3), textPaint);
                if(label.startsWith("TQ")){
                    drawText(stream, label, width - (point.getScaledX(width)) + x, point.getScaledY(height) + y - (xWidth + 3), textPaint);
                }
            }
        }
        stream.setStrokingColor(0, 0, 0);
        stream.setNonStrokingColor(0, 0, 0);

    }

    private void drawLine(PDPageContentStream stream, PDColor col, float startX, float startY, float endX, float endY) throws IOException {
        float startYInverse = pageHeight - (startY + verticalAdjustment);
        float endYInverse = pageHeight - (endY + verticalAdjustment);
        stream.setStrokingColor(col);
        stream.moveTo(startX, startYInverse);
        stream.lineTo(endX, endYInverse);
        stream.stroke();
    }

    private void drawText(PDPageContentStream stream, String s, float startX, float startY, PDColor color) throws IOException {
        final int size = 6;
        float startYInverse = pageHeight - (startY + verticalAdjustment);
        stream.setStrokingColor(color);
        stream.setNonStrokingColor(color);
        PDFont font = PDType1Font.HELVETICA;
        int textCenter = (int)((font.getStringWidth(s) / 1000 * size) / 2);
        stream.beginText();
        stream.setFont(font, size);
        stream.newLineAtOffset(startX - textCenter, startYInverse);
        stream.showText(s);
        stream.endText();
    }

    private void drawX(int x, int y, int width, int height, DrawingPoint point, PDColor color, int xFactor) throws IOException {
        int xWidth = width / xFactor;
        drawLine(stream, color, point.getScaledX(width) - xWidth + x, point.getScaledY(height) - xWidth + y,
                point.getScaledX(width) + xWidth + x, point.getScaledY(height) + xWidth + y);
        drawLine(stream, color, point.getScaledX(width) + xWidth + x, point.getScaledY(height) - xWidth + y,
                point.getScaledX(width) - xWidth + x, point.getScaledY(height) + xWidth + y);
    }

    private void drawX(int x, int y, int width, int height, DrawingPoint point, PDColor color) throws IOException {
        drawX(x, y, width, height, point, color, 40);
    }
    /**
     * Remove all of the points from the point list
     */
    public void clear() {
        pointListList.clear();
     }

    /**
     * Print out all of the normalized points to a string, Putting a slash between points
     * color1/x11,y11/x12,y12/x13,y13//color2/x21,y21/x22,y22/...
     */
    @NotNull
    @Override
    public String toString() {
        StringBuilder returnString = new StringBuilder();
        for (int i = 0; i < pointListList.size(); i++) {
            returnString.append(pointListList.get(i).getColor()).append("/");
            if (width != -1) {
                returnString.append(pointListList.get(i).getScaledX((int) width) / width).append(",").append(pointListList.get(i).getScaledY((int) height) / height).append("/");
            } else {
                returnString.append(pointListList.get(i).getX()).append(",").append(pointListList.get(i).getY()).append("/");
            }
            returnString.append("/");
        }
        return returnString.toString();
    }

    /**
     * Take the string and turn it createDD1380Document the paths from it
     *
     * @param string The string representation of the DrawView
     */
    public void fromString(String string) {
        //Set the width and height to -1 so that it will de-normalize the points when they are drawn
        width = -1;
        height = -1;
        //Split the point string by / to get a list of points.
        String[] points = string.split("/", -1);
        pointListList = new ArrayList<>();
        boolean needColor = true;
        for (String s : points) {
            if (needColor) {
                needColor = false;
                continue;
            }
            if (s.equals("")) {//This means one shape is done and the next is beginning.
                needColor = true;
            }
        }
    }
}

