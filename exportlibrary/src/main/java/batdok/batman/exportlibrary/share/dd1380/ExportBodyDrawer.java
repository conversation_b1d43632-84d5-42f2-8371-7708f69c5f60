package batdok.batman.exportlibrary.share.dd1380;

import android.graphics.*;

import java.util.ArrayList;
import java.util.List;

import gov.afrl.batdok.encounter.DrawingPoint;

/**
 * Use this view for drawing on the screen.
 */
public class ExportBodyDrawer {
    //Set up class fields
    private List<DrawingPoint> pointList = new ArrayList<>(); //List of freeform shapes
    private List<Integer> colorList = new ArrayList<>();      //List of colors
    private List<String> labelList = new ArrayList<>();      //List of labels
    private Paint drawPaint = new Paint();
    private Paint textPaint = new Paint();

    public void setPointShapeList(List<DrawingPoint> pointShapeList) {
        pointList = pointShapeList;
    }

    public void setColorList(List<Integer> colorList){
        this.colorList = colorList;
    }

    public void setLabelList(List<String> labelList){
        this.labelList = labelList;
    }

    public ExportBodyDrawer() {
        init();
    }

    private void init() {
        //Set up the paint
        drawPaint.setColor(Color.RED);
        drawPaint.setStrokeWidth(6);
        drawPaint.setStyle(Paint.Style.STROKE);

        textPaint.setColor(Color.RED);
        textPaint.setStyle(Paint.Style.FILL);
        textPaint.setTypeface(Typeface.create("Arial", Typeface.NORMAL));
        textPaint.setTextSize(50);
        textPaint.setTextAlign(Paint.Align.CENTER);
    }

    // Deep copy this point collection
    private List<DrawingPoint> deepCopyPointList ( List<DrawingPoint> pointList ) {
        List<DrawingPoint> clonedPointListList = new ArrayList<>();

        for(DrawingPoint point : pointList){
            clonedPointListList.add (new DrawingPoint (point.getLabel(), point.getX(), point.getY()));
        }

        return clonedPointListList;
    }

    public void drawToCanvas ( Canvas canvas, int x, int y, int width, int height, Paint linePaint, boolean showLabels) {
        Paint startPaint = new Paint(linePaint);

        List<DrawingPoint> copiedPointList = deepCopyPointList(pointList);

        Path path = new Path();
        textPaint.setTextSize(width/40);

        float xWidth = width / 40f;
        //Move to the first point, line to the rest.
        for (int i = 0; i < copiedPointList.size();i++){
            path.reset();
            linePaint.setColor(colorList.get(i));
            textPaint.setColor(colorList.get(i));
            DrawingPoint point = copiedPointList.get(i);
            if(labelList.get(i).equals("TQ")){
                path.moveTo(point.getScaledX(width) - xWidth*1.5f + x, point.getScaledY(height) + y);
                path.lineTo(point.getScaledX(width) + xWidth*1.5f + x, point.getScaledY(height) + y);
                path.moveTo(width - (point.getScaledX(width) - xWidth*1.5f) + x, point.getScaledY(height) + y);
                path.lineTo(width - (point.getScaledX(width) + xWidth*1.5f) + x, point.getScaledY(height) + y);
            }else {
                path.moveTo(point.getScaledX(width)-xWidth + x, point.getScaledY(height)-xWidth + y);
                path.lineTo(point.getScaledX(width)+xWidth + x, point.getScaledY(height)+xWidth + y);
                path.moveTo(point.getScaledX(width)+xWidth + x, point.getScaledY(height)-xWidth + y);
                path.lineTo(point.getScaledX(width)-xWidth + x, point.getScaledY(height)+xWidth + y);
            }
            if(showLabels) {
                if(labelList.get(i).equals("TQ")){
                    canvas.drawText(labelList.get(i), point.getScaledX(width) + x, point.getScaledY(height) + y - (xWidth + 3), textPaint);
                    canvas.drawText(labelList.get(i), width - point.getScaledX(width) + x, point.getScaledY(height) + y - (xWidth + 3), textPaint);
                }else {
                    canvas.drawText(labelList.get(i), point.getScaledX(width) + x, point.getScaledY(height) + y - (xWidth + 3), textPaint);
                }
            }
            canvas.drawPath(path, linePaint);

        }

        linePaint.setColor(startPaint.getColor());
        textPaint.setTextSize(50);
    }
}

