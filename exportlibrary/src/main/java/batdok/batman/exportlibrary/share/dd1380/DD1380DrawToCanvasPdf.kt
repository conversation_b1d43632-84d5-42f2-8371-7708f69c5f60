package batdok.batman.exportlibrary.share.dd1380

import android.content.Context
import android.content.res.Resources
import androidx.annotation.StringRes
import batdok.batman.exportlibrary.R
import batdok.batman.exportlibrary.share.PdfBoxDrawView
import batdok.batman.exportlibrary.share.dd1380.DD1380PdfTable.TableBuilder
import batdok.batman.exportlibrary.valueobject.RGB
import com.tom_roush.harmony.awt.AWTColor
import com.tom_roush.pdfbox.pdmodel.font.PDFont
import com.tom_roush.pdfbox.pdmodel.graphics.image.PDImageXObject
import gov.afrl.batdok.commands.proto.DocumentCommands.CommandData
import gov.afrl.batdok.encounter.*
import gov.afrl.batdok.encounter.medicine.KnownMedTypes
import gov.afrl.batdok.encounter.medicine.Medicine
import gov.afrl.batdok.encounter.medicine.Medicines
import gov.afrl.batdok.encounter.observation.Blood
import gov.afrl.batdok.encounter.observation.BloodList
import gov.afrl.batdok.encounter.observation.CommonObservations
import gov.afrl.batdok.encounter.treatment.*
import gov.afrl.batdok.encounter.vitals.*
import gov.afrl.batdok.util.NameFormatter
import gov.afrl.batdok.util.Patterns
import gov.afrl.batdok.util.format
import java.io.IOException
import java.time.Instant
import java.util.*

/**
 * Created on 1/31/2017.
 */
object DD1380DrawToCanvasPdf {
    private const val normalTextSize = 10
    private const val smallTextSize = 8
    private const val ALLERGY_TEXT_WIDTH = 150f
    private var localizedResources: Resources? = null

    @JvmStatic
    @Throws(IOException::class)
    fun draw(dd1380PdfBuilder: DD1380PdfBuilder, context: Context?, isKeyed: Boolean = false) {
        localizedResources = LocalizedResourceUtil.createLocalizedResources(context, dd1380PdfBuilder.countryCode)
        dd1380PdfBuilder.setPdfHeaderText(getLocalizedString(R.string.header))
        dd1380PdfBuilder.setPdfLeftFooter(getLocalizedString(R.string.left_footer))
        dd1380PdfBuilder.setPdfRightFooter(getLocalizedString(R.string.right_footer))
        dd1380PdfBuilder.createPage()
        dd1380PdfBuilder.drawHeaderFooter(localizedResources)
        drawPatient(dd1380PdfBuilder, isKeyed)
        createSignsAndSymptoms(dd1380PdfBuilder, (dd1380PdfBuilder.currentContentHeight + 35).toFloat())
        dd1380PdfBuilder.createPage()
        dd1380PdfBuilder.drawHeaderFooter(localizedResources)
        drawPatientTreatments(dd1380PdfBuilder)
    }

    private fun getLocalizedString(id: Int) = localizedResources!!.getString(id)

    private fun getLocalizedDimen(id: Int) = localizedResources!!.getInteger(id)

    @Throws(IOException::class)
    private fun drawPatient(DD1380PdfBuilder: DD1380PdfBuilder, isKeyed: Boolean) {
        var offsetY = DD1380PdfBuilder.currentContentHeight.toFloat()
        offsetY = createPatientInfo(DD1380PdfBuilder, offsetY, isKeyed)
        offsetY = createMOI(DD1380PdfBuilder, offsetY)
        createInjury(DD1380PdfBuilder, offsetY, PDImageXObject.createFromFile(DD1380PdfBuilder.exportModel.bodyMap.path, DD1380PdfBuilder.getDocument()))
    }

    @Throws(IOException::class)
    private fun drawPatientTreatments(DD1380PdfBuilder: DD1380PdfBuilder) {
        val offsetY = DD1380PdfBuilder.currentContentHeight.toFloat()
        createTreatments(DD1380PdfBuilder, 0f, offsetY)
    }

    @Throws(IOException::class)
    private fun createPatientInfo(dd1380PdfBuilder: DD1380PdfBuilder, y: Float, isKeyed: Boolean): Float {
        val w = dd1380PdfBuilder.getDimensions().width.toFloat()
        val info = dd1380PdfBuilder.exportModel.documentModel.info
        val ss = info.ssn ?: ""
        val timeInfo = info.timeInfo
        val service = info.patcat?.service?.abbreviation ?: ""
        val status = info.patcat?.status?.shortDescription ?: ""
        val unit = info.unit ?: ""
        val allergyList = info.allergies
        val gender = info.gender
        var name = info.name?.toString(NameFormatter.Format.LAST_FIRST_MIDDLE_INITIAL) ?: ""

        val nameFieldWidth = 100
        if (name.length > 10) {
            if (name.length > 12) {
                name = name.substring(0, 12) + "..."
            }
            dd1380PdfBuilder.drawField(getLocalizedString(R.string.first_last_label), name, 20f, y, nameFieldWidth.toFloat(), smallTextSize, RGB(255, 0, 0))
        } else {
            dd1380PdfBuilder.drawField(getLocalizedString(R.string.first_last_label), name, 20f, y, nameFieldWidth.toFloat(), getLocalizedDimen(R.integer.header_label_size), RGB(255, 0, 0))
        }
        dd1380PdfBuilder.drawField(getLocalizedString(R.string.last_four_label), ss.takeLast(4), getLocalizedDimen(R.integer.last_4_start_x).toFloat(), y, 50f, getLocalizedDimen(R.integer.header_label_size), RGB(255, 0, 0))

        dd1380PdfBuilder.drawField(getLocalizedString(R.string.ref_name_label), "", getLocalizedDimen(R.integer.ref_name_start_x).toFloat(), y, 70f, getLocalizedDimen(R.integer.header_label_size), RGB(255, 0, 0))
        dd1380PdfBuilder.drawText(getLocalizedString(R.string.gender_label), 20f, y + 20, normalTextSize, RGB(0, 0, 0))
        dd1380PdfBuilder.drawCheckboxList(listOf(getLocalizedString(R.string.male_label), getLocalizedString(R.string.female_label)), listOf(gender == Gender.MALE.dataString, gender == Gender.FEMALE.dataString), getLocalizedDimen(R.integer.gender_checkbox_start_x).toFloat(), y + 15, normalTextSize, AWTColor.WHITE)

        val date = timeInfo?.format(Patterns.dmy_dash, useZuluTimeFormatting = true)?:""
        val time = timeInfo?.format(Patterns.hm_24_colon, useZuluTimeFormatting = true)?:""
        var serviceText = "$service $status"
        if(serviceText.length > 10){
            // Dont use the abbreviation for contractor
            serviceText = "$service..."
        }
        dd1380PdfBuilder.drawField(getLocalizedString(R.string.date_ddmmmyy_label), date, 130f, y + 20, 75f, normalTextSize, RGB(255, 0, 0))
        dd1380PdfBuilder.drawField(getLocalizedString(R.string.time_label),  time, (150 + 175 + 5).toFloat(), y + 20, 50f, normalTextSize, RGB(255, 0, 0))
        dd1380PdfBuilder.drawField(getLocalizedString(R.string.service_label), serviceText, 20f, y + 35, 50f, normalTextSize, RGB(255, 0, 0))

        if(unit.length < 6) {
            dd1380PdfBuilder.drawField(getLocalizedString(R.string.unit_label), unit, 125f, y + 35, 50f, normalTextSize, RGB(255, 0, 0))
        }
        else if ( unit.length < 7){
            dd1380PdfBuilder.drawField(getLocalizedString(R.string.unit_label), unit, 125f, y + 35, 50f, smallTextSize, RGB(255, 0, 0))
        }
        else{
            dd1380PdfBuilder.drawField(getLocalizedString(R.string.unit_label), unit.substring(0, 6) + "...", 125f, y + 35, 50f, smallTextSize, RGB(255, 0, 0))
        }

        val allergyTextSize = if (allergyList.size < 2) normalTextSize else smallTextSize
        val allergies = dd1380PdfBuilder.getEllipsizedTextByWord(getAllergyString(allergyList), ALLERGY_TEXT_WIDTH, allergyTextSize)
        dd1380PdfBuilder.drawField(getLocalizedString(R.string.allergies_label), allergies, 215f, y + 35, ALLERGY_TEXT_WIDTH, allergyTextSize, RGB(255, 0, 0))
        dd1380PdfBuilder.drawLine(10f, y + 40, w - 10, y + 40)
        return y + 40 + 10
    }

    private fun getAllergyString(allergyList: List<String>) = allergyList.joinToString(", ")

    /**
     * Creates a formatted string from the supplied hash map displaying a list of names that had
     * to be ellipsized for the report
     *
     * @param ellipsedNames Hashmap containing list of names that had to be ellipsized for each
     * category
     * @return The formatted string.
     */
    private fun getEllipsedNames(ellipsedNames: HashMap<String, ArrayList<String?>>): String {
        val names = ellipsedNames.toList().filter { it.second.isNotEmpty() }.joinToString("\n") { (key, value) ->
            "  $key: ${value.joinToString(", ")}"
        }
        return if (names.isNotEmpty()) {
            "Med Table values too long for fields: \n$names"
        }else{
            names
        }
    }

    private fun makeCheckboxPair(@StringRes id: Int, boolean: Boolean) = getLocalizedString(id) to boolean

    @Throws(IOException::class)
    private fun createMOI(dd1380PdfBuilder: DD1380PdfBuilder, y: Float): Float {
        val w = dd1380PdfBuilder.getCurrentPage().bBox.width
        val injury = dd1380PdfBuilder.exportModel.documentModel.injuries
        fun pairMOI(@StringRes id: Int, vararg moiEnum: Moi) = makeCheckboxPair(id, moiEnum.any { injury.hasMoi(it) })

        dd1380PdfBuilder.drawText(getLocalizedString(R.string.moi_label), 15f, y + 5, normalTextSize, RGB())
        dd1380PdfBuilder.drawCheckboxList(
            listOf(
                pairMOI(R.string.artillery_label),
                pairMOI(R.string.blunt_label),
                pairMOI(R.string.burn_label, Moi.BURN),
                pairMOI(R.string.fall_label, Moi.FALL),
                pairMOI(R.string.grenade_label),
                pairMOI(R.string.gsw_label, Moi.GSW),
                pairMOI(R.string.ied_label),

            ),
            25f, y + 15, getLocalizedDimen(R.integer.moi_label_size), AWTColor.WHITE
        )
        var otherInjury = buildOtherMoiString(injury.mechanismsOfInjury.values.flatten())
        //Only show the first 30 characters of custom moi
        if (otherInjury.length > 30) {
            val temp = otherInjury.substring(0, 30)
            otherInjury = if(temp.lastIndexOf(",")==-1){
                "..."
            } else {
                temp.substring(0, temp.lastIndexOf(",")) + ", ..."
            }
        }
        dd1380PdfBuilder.drawCheckboxList(
            listOf(
                pairMOI(R.string.landmine_label),
                pairMOI(R.string.mvc_label, Moi.MVC),
                pairMOI(R.string.rpg_label),
                getLocalizedString(R.string.other_label) to (otherInjury.isNotEmpty() || getNonDD1380MoiString(injury.mechanismsOfInjury.values.flatten()).isNotEmpty())
            ),
            25f, y + 15 + 15, getLocalizedDimen(R.integer.moi_label_size), AWTColor.WHITE
        )
        dd1380PdfBuilder.drawText(otherInjury, w / 2 + 15, y + 33, normalTextSize, RGB())
        dd1380PdfBuilder.drawLine(10f, y + 40, w - 10, y + 40)
        val treatments = dd1380PdfBuilder.exportModel.documentModel.treatments
        val tqTreatment = treatments["TQ"].filter { it.getData<TqData>()?.tqLocation == TqData.Location.EXTREMITY.dataString }
        var rightArm = false
        var rightLeg = false
        var leftArm = false
        var leftLeg = false
        for (tq in tqTreatment) {
            val tqData = tq.getData<TqData>()
            val subLocation = tqData?.subLocation?: ""
            val type = tqData?.tqType?: ""
            val date = tq.timestamp?.format(Patterns.mdhm_24_backslash_space_colon, useZuluTimeFormatting = true)?:""
            if (!rightArm && subLocation == TqData.SubLocation.RUE.dataString) {
                createArmTQ(dd1380PdfBuilder, getLocalizedString(R.string.tq_right_arm), type, date, 15f, y + 75)
                rightArm = true
            } else if (!rightLeg && subLocation == TqData.SubLocation.RLE.dataString) {
                createLegTQ(dd1380PdfBuilder, getLocalizedString(R.string.tq_right_leg), type, date, 15f, y + 225)
                rightLeg = true
            } else if (!leftLeg && subLocation == TqData.SubLocation.LLE.dataString) {
                createLegTQ(dd1380PdfBuilder, getLocalizedString(R.string.tq_left_leg), type, date, w - 95, y + 225)
                leftLeg = true
            } else if (!leftArm && subLocation == TqData.SubLocation.LUE.dataString) {
                createArmTQ(dd1380PdfBuilder, getLocalizedString(R.string.tq_left_arm), type, date, w - 95, y + 75)
                leftArm = true
            }
        }

        //Draw layout if no corresponding tq exist
        if (!rightArm) {
            createArmTQ(dd1380PdfBuilder, getLocalizedString(R.string.tq_right_arm), "", "", 15f, y + 75)
        }
        if (!rightLeg) {
            createLegTQ(dd1380PdfBuilder, getLocalizedString(R.string.tq_right_leg), "", "", 15f, y + 225)
        }
        if (!leftLeg) {
            createLegTQ(dd1380PdfBuilder, getLocalizedString(R.string.tq_left_leg), "", "", w - 95, y + 225)
        }
        if (!leftArm) {
            createArmTQ(dd1380PdfBuilder, getLocalizedString(R.string.tq_left_arm), "", "", w - 95, y + 75)
        }
        return y + 40 + 10
    }

    @Throws(IOException::class)
    private fun createArmTQ(dd1380PdfBuilder: DD1380PdfBuilder, name: String, type: String, time: String, x: Float, y: Float) {
        dd1380PdfBuilder.drawRect(x, y, x + 80, y + 60, AWTColor.WHITE)
        dd1380PdfBuilder.drawText(name, x + getLocalizedDimen(R.integer.arm_tq_label_start_x), y + 13, normalTextSize, RGB())
        dd1380PdfBuilder.drawField(getLocalizedString(R.string.type_label), type, x + 2, y + 30, 50f, 3f, smallTextSize, smallTextSize, RGB())
        dd1380PdfBuilder.drawField(getLocalizedString(R.string.time_label), time, x + 2, y + 50, 50f, 3f, smallTextSize, smallTextSize, RGB())
    }

    @Throws(IOException::class)
    private fun createLegTQ(dd1380PdfBuilder: DD1380PdfBuilder, name: String, type: String, time: String, x: Float, y: Float) {
        dd1380PdfBuilder.drawRect(x, y, x + 80, y + 60, AWTColor.WHITE)
        dd1380PdfBuilder.drawText(name, x + 15, y + 13, normalTextSize, RGB())
        dd1380PdfBuilder.drawField(getLocalizedString(R.string.type_label), type, x + 2, y + 30, 50f, 3f, smallTextSize, smallTextSize, RGB())
        dd1380PdfBuilder.drawField(getLocalizedString(R.string.time_label), time, x + 2, y + 50, 50f, 3f, smallTextSize, smallTextSize, RGB())
    }

    @Throws(IOException::class)
    private fun createInjury(dd1380PdfBuilder: DD1380PdfBuilder, y: Float, bodyMap: PDImageXObject?): Float {
        val w = dd1380PdfBuilder.getDimensions().width.toFloat()
        dd1380PdfBuilder.drawText(getLocalizedString(R.string.injury_label), 15f, y + 5, normalTextSize, RGB())
        if (bodyMap != null) {
            drawBodyBitmap(dd1380PdfBuilder, y - 36, dd1380PdfBuilder.exportModel.documentModel, bodyMap)
        }
        dd1380PdfBuilder.drawLine(10f, y + 250, w - 10, y + 250)
        return (400 + 25 + 10).toFloat()
    }

    private fun List<EncounterVital>.getFourVitals() = if(size >= 4){
        intArrayOf(0, size / 3, size * 2 / 3, size - 1).map {
            get(it)
        }
    }else{
        this + arrayOfNulls(4 - size)
    }

    @Throws(IOException::class)
    private fun createSignsAndSymptoms(dd1380PdfBuilder: DD1380PdfBuilder, y: Float) {
        dd1380PdfBuilder.drawText(getLocalizedString(R.string.signs_symptoms_label), 15f, y + 7, normalTextSize, RGB())
        val tableBuilder = TableBuilder(dd1380PdfBuilder, 100f, 64f, normalTextSize, dd1380PdfBuilder.currentContentHeight.toFloat())
            .addTable(getLocalizedString(R.string.pulse_ss_label))
            .addTable(getLocalizedString(R.string.bp_ss_label))
            .addTable(getLocalizedString(R.string.rr_ss_label))
            .addTable(getLocalizedString(R.string.pulse_ox_ss_label))
            .addTable(getLocalizedString(R.string.avpu_ss_label))
            .addTable(getLocalizedString(R.string.pain_ss_label))
            .setPosition(10f, y + 17)

        val dd1380Vitals = dd1380PdfBuilder.exportModel.documentModel.vitals.vitalList.sortedBy { it.timestamp.epochSecond }

        val pulseValues = mutableListOf<String>()
        val bloodValues = mutableListOf<String>()
        val respValues = mutableListOf<String>()
        val oxValues = mutableListOf<String>()
        val avpuValues = mutableListOf<String>()
        val painScaleValues = mutableListOf<String>()

        dd1380Vitals.getFourVitals().forEach { row ->
            val time = row?.timestamp
            tableBuilder.addColumn(time?.let {
                time.format(Patterns.hm_24_colon, useZuluTimeFormatting = true)
            }?: "", 70)

            pulseValues.add(row?.let {
                it.get<HR>()?.eventMessage
            } ?: "")
            bloodValues.add(row?.let {
                it.get<BloodPressure>()?.eventMessage
            } ?: "")
            respValues.add(row?.let {
                it.get<Resp>()?.eventMessage
            } ?: "")
            oxValues.add(row?.let {
                it.get<SpO2>()?.eventMessage
            } ?: "")
            avpuValues.add(row?.let {
                listOfNotNull(it.get<Avpu>()?.avpu, it.get<GCS>()?.eventMessage?.let { "G$it" }).joinToString(", ")
            } ?: "")
            painScaleValues.add(row?.let {
                it.get<Pain>()?.eventMessage
            } ?: "")
        }

        tableBuilder.addRow(0, pulseValues)
        tableBuilder.addRow(1, bloodValues)
        tableBuilder.addRow(2, respValues)
        tableBuilder.addRow(3, oxValues)
        tableBuilder.addRow(4, avpuValues)
        tableBuilder.addRow(5, painScaleValues)
        tableBuilder.build(localizedResources)
    }

    @Throws(IOException::class)
    private fun createTreatmentsChecklist(dd1380PdfBuilder: DD1380PdfBuilder, x: Float, y: Float, treatments: Treatments, observations: Observations) {
        dd1380PdfBuilder.drawText(getLocalizedString(R.string.treatments_label), x + 15, y + 5, normalTextSize, RGB())
        dd1380PdfBuilder.drawText(getLocalizedString(R.string.type_label), x + 355, y + 5, normalTextSize, RGB())
        dd1380PdfBuilder.drawText("C: TQ- ", x + 15, y + 20, normalTextSize, RGB())
        fun Pair(@StringRes id: Int, location: TqData.Location) = makeCheckboxPair(id, getTourniquetCountByLocation(treatments, location) > 0)
        dd1380PdfBuilder.drawCheckboxList(
            listOf(
                Pair(R.string.extremity_label, TqData.Location.EXTREMITY),
                Pair(R.string.juncitonal_label, TqData.Location.JUNCTIONAL),
                Pair(R.string.truncal_label, TqData.Location.TRUNCAL)
            ),
            x + 60, y + 15, normalTextSize, AWTColor.WHITE
        )
        dd1380PdfBuilder.drawText(getLocalizedString(R.string.dressing_label), x + 30, y + 32, normalTextSize, RGB())
        fun Pair(@StringRes id: Int, type: DressingData.Type) = makeCheckboxPair(id, treatments.getDressingCountByType(type) > 0)
        //Count all dressings, remove the hemostatic and pressure dressings, and use the leftovers for the other count
        val otherDressingCount = treatments.count(CommonTreatments.DRESSING) -
                treatments.getDressingCountByType(DressingData.Type.HEMOSTATIC) -
                treatments.getDressingCountByType(DressingData.Type.PRESSURE)

        dd1380PdfBuilder.drawCheckboxList(
            listOf(
                Pair(R.string.hemostatic_label, DressingData.Type.HEMOSTATIC),
                Pair(R.string.pressure_label, DressingData.Type.PRESSURE),
                makeCheckboxPair(R.string.other, otherDressingCount > 0)
            ),
            x + 90, y + 30, normalTextSize, AWTColor.WHITE
        )
        dd1380PdfBuilder.drawText("A:", x + 15, y + 50, normalTextSize, RGB())
        fun Pair(string: String, type: CommonTreatments) = string to isTreatmentApplied(treatments, type)
        val intact = isTreatmentApplied(treatments, CommonTreatments.INTACT)|| isObservationApplied(observations, CommonObservations.INTACT_AIRWAY)
        dd1380PdfBuilder.drawCheckboxList(
            listOf(
                Pair(getLocalizedString(R.string.intact_label), intact),
                Pair("NPA", CommonTreatments.NPA),
                Pair("CRIC", CommonTreatments.CRIC),
                Pair("ET-Tube", CommonTreatments.ET_TUBE),
                Pair("SGA", CommonTreatments.SGA)
            ),
            x + 40, y + 45, normalTextSize, AWTColor.WHITE
        )
        dd1380PdfBuilder.drawText("B:", x + 15, y + 65, normalTextSize, RGB())
        dd1380PdfBuilder.drawCheckboxList(
            listOf(
                Pair("O2", CommonTreatments.O2),
                Pair(getLocalizedString(R.string.needle_d_label), CommonTreatments.NEEDLE_D),
                Pair(getLocalizedString(R.string.chest_tube_label), CommonTreatments.CHEST_TUBE),
                Pair(getLocalizedString(R.string.chest_seal_label), CommonTreatments.CHEST_SEAL)
            ),
            x + 40, y + 60, normalTextSize, AWTColor.WHITE
        )
        dd1380PdfBuilder.drawText("C:", x + 15, y + 80, normalTextSize, RGB())
    }

    private fun getTourniquetCountByLocation(treatments: Treatments, type: TqData.Location): Int {
        // Only one of these should return a value > 0, depending on how the commands are processed
        return treatments[CommonTreatments.TQ].count { it.getData<TqData>()?.tqLocation == type.dataString }
    }

    private fun Treatments.getDressingCountByType(type: DressingData.Type): Int {
        // Only one of these should return a value > 0, depending on how the commands are processed
        return get(CommonTreatments.DRESSING).count { it.getData<DressingData>()?.type == type.dataString }
    }

    private fun isTreatmentApplied(treatments: Treatments, type: CommonTreatments): Boolean {
        return type in treatments
    }

    private fun isObservationApplied(observations: Observations, type: CommonObservations): Boolean{
        return type in observations
    }

    private fun isEyeShieldApplied(treatments: Treatments, leftSide: Boolean): Boolean {
        fun EyeShieldData.hasSide() = (leftSide && left) || (!leftSide && right)
        return treatments[CommonTreatments.EYE_SHIELD].any {
            it.getData<EyeShieldData>()?.hasSide() == true
        }
    }

    private fun isPillPackApplied(treatments: Treatments) : Boolean {
        return CommonTreatments.COMBAT_PILL_PACK in treatments
    }

    private fun isSplintApplied(treatments: Treatments) : Boolean {
        return CommonTreatments.SPLINT in treatments
    }

    private fun isSplintPulseSet(treatments: Treatments, withPulse: Boolean) : Boolean {
        fun SplintData.matchPulse() = (withPulse && pulsePresent == true) || (!withPulse && (pulsePresent == false))
        return treatments[CommonTreatments.SPLINT].any {
            it.getData<SplintData>()?.matchPulse() == true
        }
    }

    private fun TableBuilder.addToRow(index: Int, list: List<Medicine>){
        if (list.isNotEmpty()) {
            for (treatment in list) {
                addRow(index,
                    listOf(
                        treatment.name,
                        treatment.getVolumeString(),
                        treatment.route,
                        treatment.administrationTime?.format(Patterns.mdhm_24_backslash_space_colon, useZuluTimeFormatting = true)
                    )
                )
            }
        } else {
            addRow(index, listOf("", "", "", ""))
        }
    }
    private fun TableBuilder.addBloodToRow(index: Int, list: List<Blood>){
        if (list.isNotEmpty()) {
            for (treatment in list) {
                addRow(index,
                    listOf(
                        treatment.bloodProduct,
                        treatment.getVolumeString(),
                        treatment.route,
                        treatment.administrationTime?.format(Patterns.mdhm_24_backslash_space_colon, useZuluTimeFormatting = true)
                    )
                )
            }
        } else {
            addRow(index, listOf("", "", "", ""))
        }
    }

    @Throws(IOException::class)
    private fun createBloodFluidTable(dd1380PdfBuilder: DD1380PdfBuilder, x: Float, y: Float, treatments: Medicines, bloods: BloodList): Pair<Float, DD1380PdfTable> {
        val bloodFluidTableBuilder = TableBuilder(dd1380PdfBuilder, 100f, 64f, normalTextSize, dd1380PdfBuilder.currentContentHeight.toFloat()+2)
            .addTable(getLocalizedString(R.string.fluid_label))
            .addTable(getLocalizedString(R.string.blood_product_label))
            .addColumn(getLocalizedString(R.string.name_label), 120)
            .addColumn(getLocalizedString(R.string.volume_label), 60)
            .addColumn(getLocalizedString(R.string.route_label), 50)
            .addColumn(getLocalizedString(R.string.time), 70)
            .setPosition(x + 5, y + 77)

        bloodFluidTableBuilder.addToRow(0, treatments[KnownMedTypes.FLUID])
        bloodFluidTableBuilder.addBloodToRow(1, bloods.list)

        val numOfBloodProducts = bloods.list.size * 6
        val bloodFluidTable = bloodFluidTableBuilder.build(localizedResources)
        dd1380PdfBuilder.drawText("", x + 15, (dd1380PdfBuilder.currentContentHeight + numOfBloodProducts).toFloat(), normalTextSize, RGB())
        val currentOffsetY: Float = (dd1380PdfBuilder.currentContentHeight + numOfBloodProducts + 17).toFloat()
        dd1380PdfBuilder.drawText(getLocalizedString(R.string.meds_label), x + 15, currentOffsetY, normalTextSize, RGB())
        return Pair(currentOffsetY, bloodFluidTable)
    }

    @Throws(IOException::class)
    private fun createMedsTable(dd1380PdfBuilder: DD1380PdfBuilder, x: Float, currentOffsetY: Float, med: Medicines): Pair<Float, DD1380PdfTable> {
        val medsTableBuilder = TableBuilder(dd1380PdfBuilder, 100f, 64f, normalTextSize, currentOffsetY+2)
                .addTable(getLocalizedString(R.string.analgesic_label))
                .addTable(getLocalizedString(R.string.antibiotic_label))
                .addTable(getLocalizedString(R.string.other))
                .addColumn(getLocalizedString(R.string.name_label), 120)
                .addColumn(getLocalizedString(R.string.dose_label), 60)
                .addColumn(getLocalizedString(R.string.route_label), 50)
                .addColumn(getLocalizedString(R.string.time), 70)
                .setPosition(x + 5, dd1380PdfBuilder.currentContentHeight.toFloat()+2)
        medsTableBuilder.addToRow(0, med[KnownMedTypes.ANALGESIC])
        medsTableBuilder.addToRow(1, med[KnownMedTypes.ANTIBIOTIC])
        medsTableBuilder.addToRow(2, med.getAllExcept(KnownMedTypes.ANALGESIC, KnownMedTypes.ANTIBIOTIC, KnownMedTypes.FLUID))

        val medsTable = medsTableBuilder.build(localizedResources)
        val otherTypes = med.getAllExcept(KnownMedTypes.ANALGESIC, KnownMedTypes.ANTIBIOTIC).size
        dd1380PdfBuilder.drawText("", x + 15, (dd1380PdfBuilder.currentContentHeight +  otherTypes * 5).toFloat(), normalTextSize, RGB())
        val curYOffset: Float = (dd1380PdfBuilder.currentContentHeight + otherTypes * 6).toFloat()
        return Pair(curYOffset, medsTable)
    }

    /**
     * @return The full name/afsc if ellipsized. Otherwise, null
     */
    @Throws(IOException::class)
    private fun createFirstResponderInfo(dd1380PdfBuilder: DD1380PdfBuilder, x: Float, h: Float, w: Float): String? {
        val firstResponderName = dd1380PdfBuilder.exportModel.firstResponderName
        val firstResponderLast4 = dd1380PdfBuilder.exportModel.firstResponderLast4
        var curYOffset = h - 70
        dd1380PdfBuilder.drawLine(10f, curYOffset, w - 10, curYOffset)
        curYOffset += 10f
        dd1380PdfBuilder.drawText(getLocalizedString(R.string.first_responder_label), x + 15, curYOffset, normalTextSize, RGB())
        val split = firstResponderName.split(" ".toRegex()).dropLastWhile { it.isEmpty() }
        val last = if (split.size > 1) split[1] + ", " else ""
        val first =  if (split.isNotEmpty()) split[0] else ""
        curYOffset += 17f
        val fullFirstResponderString = last + first + "    AFSC/MOS/Rate: " + dd1380PdfBuilder.exportModel.firstResponderAfscMos
        var displayedString = fullFirstResponderString
        val maxLength = 33
        if (displayedString.length > maxLength) {
            displayedString = displayedString.substring(0, maxLength) + "..."
        }
        dd1380PdfBuilder.drawField(getLocalizedString(R.string.first_last_label), displayedString, x + 15, curYOffset, 200f, normalTextSize, RGB(255, 0, 0))
        dd1380PdfBuilder.drawField(getLocalizedString(R.string.last_four_label), firstResponderLast4, x + 340, curYOffset, 50f, normalTextSize, RGB(255, 0, 0))
        return fullFirstResponderString.takeIf { displayedString.length > maxLength }
    }

    @Throws(IOException::class)
    private fun createTreatmentsOtherChecklist(dd1380PdfBuilder: DD1380PdfBuilder, x: Float, curYOffset: Float, treatments: Treatments, w: Float) {
        dd1380PdfBuilder.drawText(getLocalizedString(R.string.other_label), x + 15, curYOffset + 33, normalTextSize, RGB())
        val totalXOffset = dd1380PdfBuilder.drawCheckboxList(
            listOf(
                makeCheckboxPair(R.string.combat_pill_pack_label, isPillPackApplied(treatments)),
                makeCheckboxPair(R.string.eye_shield_l_label, isEyeShieldApplied(treatments, true)),
                makeCheckboxPair(R.string.eye_shield_r_label, isEyeShieldApplied(treatments, false)),
                makeCheckboxPair(R.string.splint_label, isSplintApplied(treatments)),
            ),
            x + getLocalizedDimen(R.integer.other_treatment_checkbox_start_x),
            curYOffset + 30,
            getLocalizedDimen(R.integer.other_treatment_label_size),
            AWTColor.WHITE
        )
        val splintPulsePresent = isSplintPulseSet(treatments, true)
        val splintPulseNotPresent = isSplintPulseSet(treatments, false)
        val pulseStatus = if (splintPulsePresent) {
            getLocalizedString(R.string.pulse_label)
        } else if (splintPulseNotPresent) {
            getLocalizedString(R.string.no_pulse_label)
        } else {
            ""
        }
        dd1380PdfBuilder.drawText(pulseStatus, x + getLocalizedDimen(R.integer.pulse_status_start_x) + totalXOffset - 15, curYOffset + 33, getLocalizedDimen(R.integer.other_treatment_label_size), RGB())
        dd1380PdfBuilder.drawCheckboxList(
            listOf(makeCheckboxPair(R.string.hypothermia_prevention_label, isTreatmentApplied(treatments, CommonTreatments.HYPOTHERMIA_PREVENTION))),
            x + getLocalizedDimen(R.integer.other_treatment_checkbox_start_x),
            curYOffset + 45,
            getLocalizedDimen(R.integer.other_treatment_label_size),
            AWTColor.WHITE
        )
        dd1380PdfBuilder.drawField(getLocalizedString(R.string.type_lowercase_label), "", x + 225, curYOffset + 50, 100f, getLocalizedDimen(R.integer.other_treatment_label_size), RGB())
        dd1380PdfBuilder.drawLine(10f, curYOffset + 55, w - 10, curYOffset + 55)
    }

    @Throws(IOException::class)
    private fun createTreatments(dd1380PdfBuilder: DD1380PdfBuilder, x: Float, y: Float) {
        val h = dd1380PdfBuilder.getDimensions().height.toFloat()
        val w = dd1380PdfBuilder.getDimensions().width.toFloat()
        val med = dd1380PdfBuilder.exportModel.documentModel.medicines
        val blood = dd1380PdfBuilder.exportModel.documentModel.bloodList
        val marchPawsTreatments = dd1380PdfBuilder.exportModel.documentModel.treatments
        val observations = dd1380PdfBuilder.exportModel.documentModel.observations
        createTreatmentsChecklist(dd1380PdfBuilder, x, y, marchPawsTreatments, observations)
        var (curYOffset, bloodFluidTable) = createBloodFluidTable(dd1380PdfBuilder, x, y, med, blood)
        val medsTableInfo = createMedsTable(dd1380PdfBuilder, x, curYOffset, med)
        curYOffset = medsTableInfo.first
        val medsTable = medsTableInfo.second

        // Need at least 200 pixels of space for this last part of the document.
        if (dd1380PdfBuilder.currentContentHeight > h - 200) {
            dd1380PdfBuilder.createPage()
            dd1380PdfBuilder.drawHeaderFooter(localizedResources)
            curYOffset = (dd1380PdfBuilder.currentContentHeight - 33).toFloat()
        }
        createTreatmentsOtherChecklist(dd1380PdfBuilder, x, curYOffset, marchPawsTreatments, w)
        var notesYOffset = curYOffset + 55
        if (notesYOffset > h - 100) {
            dd1380PdfBuilder.createPage()
            dd1380PdfBuilder.drawHeaderFooter(localizedResources)
            notesYOffset = dd1380PdfBuilder.currentContentHeight.toFloat()
        }
        val ellipsizedNamesMap = bloodFluidTable.ellipsizedNamesMap
        ellipsizedNamesMap.putAll(medsTable.ellipsizedNamesMap)
        val extraInfoToDisplay = ArrayList<String>()
        extraInfoToDisplay.add(getEllipsedNames(ellipsizedNamesMap))
        drawNotes(dd1380PdfBuilder, x, notesYOffset, extraInfoToDisplay)
    }

    @Throws(IOException::class)
    private fun drawNotes(dd1380PdfBuilder: DD1380PdfBuilder, x: Float, yOffset: Float, ellipsizedValues: List<String>) {
        var curYOffset = yOffset
        val h = dd1380PdfBuilder.getDimensions().height.toFloat()
        val w = dd1380PdfBuilder.getDimensions().width.toFloat()
        dd1380PdfBuilder.drawText(getLocalizedString(R.string.notes_label), x + 15, curYOffset + 10, normalTextSize, RGB())
        curYOffset += 10f
        val documentModel = dd1380PdfBuilder.exportModel.documentModel
        val patientID = "Patient ID: " + documentModel.id.toHexString()
        val name = "Name: " + (dd1380PdfBuilder.exportModel.documentModel.info.name?.toString(NameFormatter.Format.FIRST_MIDDLE_LAST) ?:"")
        var unit = ""
        if((dd1380PdfBuilder.exportModel.documentModel.info.unit?.length ?: 0) > 6){
             unit = "Unit : " + dd1380PdfBuilder.exportModel.documentModel.info.unit
        }

        val timeSinceInjury = (System.currentTimeMillis() - (documentModel.info.timeInfo?.toEpochMilli() ?: System.currentTimeMillis()))
        val timeString = "Time since Injury: " + millisToHrMinString(timeSinceInjury)
        var customTreatmentsString = ""
        val customTreatmentsBuilder = StringBuilder()
        for (customTreatment in documentModel.treatments.list) {
            if (customTreatmentsBuilder.isNotEmpty()) {
                customTreatmentsBuilder.append(", ")
            }
            val treatmentData = (": " + customTreatment.treatmentData?.toEventString()).takeIf { customTreatment.treatmentData!=null }
            customTreatmentsBuilder.append(customTreatment.name + (treatmentData?:"") + " " +customTreatment.timestamp?.format(Patterns.mdhm_24_backslash_space_colon, useZuluTimeFormatting = true))
        }

        //Add observations
        for (customObservation in documentModel.observations.list){
            if (customTreatmentsBuilder.isNotEmpty()) {
                customTreatmentsBuilder.append(", ")
            }
            val treatmentData = (": " + customObservation.observationData?.toEventText()).takeIf { customObservation.observationData!=null }
            customTreatmentsBuilder.append(customObservation.name + (treatmentData?:"") + " " +customObservation.timestamp.format(Patterns.mdhm_24_backslash_space_colon, useZuluTimeFormatting = true))
        }

        if (customTreatmentsBuilder.isNotEmpty()) {
            customTreatmentsString = "Other Treatments: $customTreatmentsBuilder\n"
        }
        var allergyString = getAllergyString(documentModel.info.allergies)
        //If the ellipsized text is the same as the original, it fit above, so we don't need it here.
        allergyString = if (dd1380PdfBuilder.getEllipsizedTextByWord(allergyString, ALLERGY_TEXT_WIDTH, smallTextSize) == allergyString) {
            ""
        } else {
            "Allergies: $allergyString\n"
        }
        var moiString = ""
        val otherMoiString = buildOtherMoiString(documentModel.injuries.mechanismsOfInjury.values.flatten())
        if (otherMoiString.split(",".toRegex()).dropLastWhile { it.isEmpty() }.isNotEmpty()) {
            moiString = "Other MOIs: $otherMoiString\n"
        }
        var injuryString = documentModel.injuries.allInjuryString()
        if (injuryString.isNotEmpty()) {
            injuryString = "Injuries: $injuryString\n"
        }
        val customEvents = documentModel.events.list.filter {
            it.eventType == KnownEventTypes.OTHER.dataString || it.eventType == KnownEventTypes.CASEVAC.dataString
        }
        val otherProviderCallsigns = getOtherProviderCallsigns(dd1380PdfBuilder.exportModel.commands)
        val ellipsizedBuilder = StringBuilder()
        for (value in ellipsizedValues) {
            ellipsizedBuilder.append(value).append("\n")
        }

        val firstResponderName = dd1380PdfBuilder.exportModel.firstResponderName
        val firstResponder = NameFormatter(firstResponderName).toString(NameFormatter.Format.LAST_FIRST_MIDDLE)
        val fullFirstResponderString = firstResponder + "    AFSC/MOS/Rate: " + dd1380PdfBuilder.exportModel.firstResponderAfscMos

        var lines = patientID +"\n" +
            name +"\n" +
                (if(unit.isNotEmpty()) "$unit\n" else "")+
            timeString +"\n" +
            allergyString+"\n" +
            moiString +"\n" +
            injuryString +"\n" +
            customTreatmentsString +"\n" +
            ellipsizedBuilder+"\n" +
            makeNoteFromSTabExtras(documentModel) +"\n" +
            makeNoteFromEvents(customEvents)+"\n" +
            otherProviderCallsigns


        if (fullFirstResponderString.length > 33) {
            lines += "\nFirst Responder Name: $fullFirstResponderString"
        }


        curYOffset += 15f
        writeMultiPageNotesSection(curYOffset, dd1380PdfBuilder, lines, h, w)
    }

    /**
     * Goes through commands and identifies providers and time range they treated patient.
     * @param commands Command Data for patient that contains provider and date time.
     * @return List of providers and their time range they treated the patient.
     */
    private fun getOtherProviderCallsigns(commands: List<CommandData>): String {
        val map = commands.groupBy { it.callsign }.mapValues { (_, value) ->
            Pair(value.minBy { it.timestamp }.timestamp, value.maxBy { it.timestamp }.timestamp)
        }
        return if (map.isNotEmpty()) {
            fun Long.toFormattedString() = Instant.ofEpochSecond(this).format(Patterns.mdhm_24_space_comma_colon, useZuluTimeFormatting = true)
            "Other Providers:\n" + map.entries.joinToString("\n") { (user, dateRange) ->
                val username = user.takeUnless { it.isEmpty() } ?: "Unknown"
                "$username - ${dateRange.first.toFormattedString()}-${dateRange.second.toFormattedString()}"
            }
        } else {
            ""
        }
    }

    @Throws(IOException::class)
    private fun writeMultiPageNotesSection(curYOffset: Float, dd1380PdfBuilder: DD1380PdfBuilder, lines: String, h: Float, w: Float) {
        //mark the start of the notes field
        var extraNotesStartY = curYOffset - 14
        var spaceToKeep = 90f
        var notesLines = lines.split("\n".toRegex()).dropLastWhile { it.isEmpty() }
        var leftoverLines = lines
        while (estimateTextHeight(notesLines, dd1380PdfBuilder.pdfFont, w - 20) > h - spaceToKeep - extraNotesStartY) {
            var addNewPage = false
            val currentPageNotes = mutableListOf<String>()
            var k = 0
            //Calculate how many lines can fit on the current page.
            while (estimateTextHeight(currentPageNotes, dd1380PdfBuilder.pdfFont, w - 20) <= h - spaceToKeep - extraNotesStartY) {
                val c = notesLines[k]
                currentPageNotes.add(c)
                k++
            }
            val currentPageString = StringBuilder()
            //If there are additional lines not accounted for, pop the last line to append continued on next page message
            if (currentPageNotes.size > 0 && currentPageNotes.size < notesLines.size) {
                currentPageNotes.removeAt(currentPageNotes.size - 1)
                addNewPage = true
            }
            for (note in currentPageNotes) {
                currentPageString.append(note).append("\n")
            }
            if (addNewPage) {
                currentPageString.append("(Events continued on next page)")
            }
            //Add notes to form
            dd1380PdfBuilder.drawExtraNotesAcroForm(currentPageString.toString(), 10f, h - spaceToKeep, w - 20, h - spaceToKeep - extraNotesStartY, normalTextSize)
            if (addNewPage) {
                createFirstResponderInfo(dd1380PdfBuilder, 5f, h, w)
                //Create a new page
                dd1380PdfBuilder.createPage()
                dd1380PdfBuilder.drawHeaderFooter(localizedResources)
                extraNotesStartY = dd1380PdfBuilder.currentContentHeight.toFloat()
                spaceToKeep = 0f
            }
            //numOfLine is now the remaining lines.
            notesLines = notesLines.subList(currentPageNotes.size, notesLines.size)
            //Update leftoverLines
            leftoverLines = notesLines.joinToString("\n")
        }
        if (leftoverLines.isNotEmpty()) {
            dd1380PdfBuilder.drawExtraNotesAcroForm(leftoverLines, 10f, h - spaceToKeep, w - 20, h - spaceToKeep - extraNotesStartY, normalTextSize)
        }
        createFirstResponderInfo(dd1380PdfBuilder, 5f, h, w)
    }

    /**
     * Estimates the height for the array of lines fit into a certain width using the specified font
     *
     * @param lines          array of string to estimate height needed from.
     * @param font           font that is used for the text.
     * @param widthToWriteIn the width that the text needs to fit in.
     * @return Estimated height of the text.
     */
    private fun estimateTextHeight(lines: List<String>, font: PDFont, widthToWriteIn: Float): Int {
        var estimatedNoOfLines = 0
        val textSize = 13
        val fontHeight = (font.fontDescriptor.fontBoundingBox.height / 1000 * textSize).toInt()
        for (line in lines) {
            try {
                val width = font.getStringWidth(line) / 1000 * textSize
                estimatedNoOfLines += (width.toInt() / widthToWriteIn + 1).toInt() //any remaining width will be its own line.
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
        return estimatedNoOfLines * fontHeight
    }

    private fun millisToHrMinString(millis: Long): String {
        var time = millis
        time /= (1000 * 60).toLong() //Remove millis and sec
        val min = time % 60
        time /= 60
        val hr = time
        var returnString = ""
        if (hr != 0L) {
            returnString += "$hr hr "
        }
        returnString += "$min min"
        return returnString
    }

    private fun buildOtherMoiString(injury: List<String>): String {
        val otherInjury = StringBuilder()
        val customMois = getNonDD1380MoiString(injury)
        if (customMois.isNotEmpty()) {
            if (otherInjury.isNotEmpty()) {
                otherInjury.append(", ")
            }
            otherInjury.append(customMois)
        }
        return otherInjury.toString()
    }

    private fun getNonDD1380MoiString(injury: List<String>): String {
        val excluding = listOf(
            Moi.BURN,
            Moi.FALL,
            Moi.GSW,
            Moi.MVC
        ).map { it.dataString }
        return injury.filter { it !in excluding }.joinToString(", ")
    }

    // Draw body to stream
    @Throws(IOException::class)
    private fun drawBodyBitmap(DD1380PdfBuilder: DD1380PdfBuilder, offsetY: Float, handler: Document, bodyBitmap: PDImageXObject) {
        val w = DD1380PdfBuilder.getDimensions().width.toFloat()
        val streamWidth = w.toInt()
        val bitmapWidthScaled = 250 // the size we want the body size to translate to on the pdf (orig. 325)
        val bitmapHeightScaled = 250 // (orig. 325)
        val bitmapY = offsetY.toInt()

        // Center the body bitmap horizontally
        val centerBitmapX = (streamWidth / 2.0f).toInt() - (bitmapWidthScaled / 2.0f).toInt()
        // Draw body image bitmap
        DD1380PdfBuilder.drawBitmap(bodyBitmap, bitmapWidthScaled, bitmapHeightScaled, centerBitmapX.toFloat(), bitmapY.toFloat())

        //get the body image
        val bodyView = PdfBoxDrawView(DD1380PdfBuilder.getStream(), 35)
        val (labelList, shapeList) = handler.injuries.drawingPoints.map { Pair(it.label, it) }.unzip()

        bodyView.setPointShapeList(shapeList)

        // Draw the lines on top of the body image
        bodyView.drawToCanvas(centerBitmapX, bitmapY, bitmapWidthScaled, bitmapHeightScaled, true, DD1380PdfBuilder.getDimensions().height.toFloat())
    }

    private fun EncounterVital.toNotesText(): String {
        val timeInfo = timestamp.format(Patterns.hm_24_colon, useZuluTimeFormatting = true)
        val iSystolic = get<InvasiveBloodPressure>()?.systolic?.toString() ?: "--"
        val iDiastolic = get<InvasiveBloodPressure>()?.diastolic?.toString() ?: "--"
        val ibp = "$iSystolic/$iDiastolic"
        val etco2 = get<EtCO2>()?.etco2 ?: -1
        val temp = get<Temp>()?.temp?: -1f
        val extraVitalStrings = listOfNotNull(
            ibp.takeUnless { it == "--/--" }?.let { "iBP = $it" },
            etco2.takeUnless { it <= 0 }?.let { "EtCO2 = $it" },
            temp.takeUnless { it <= 0 }?.let { "Temp. = $it" }
        )
        return ("$timeInfo: ".takeIf { extraVitalStrings.isNotEmpty() }?:"") + extraVitalStrings.joinToString(", ")
    }

    private fun makeNoteFromSTabExtras(model: Document): String {
        return model.vitals.vitalList
            .joinToString(" | ") { it.toNotesText() }
    }

    private fun makeNoteFromEvents(events: List<Event>): String {
        return events.joinToString("\n") {
            it.timestamp?.format(Patterns.mdhm_24_space_comma_colon, useZuluTimeFormatting = true) + " - " + it.event
        }
    }
}