package batdok.batman.exportlibrary.pdf

import android.content.Context
import batdok.batman.exportlibrary.R
import batdok.batman.exportlibrary.io.PdfIO
import gov.afrl.batdok.encounter.Document
import gov.afrl.batdok.util.NameFormatter

class AF3899EIntakeAndOutputPdfBuilder(pdfIO: PdfIO): AcroformPdfBuilder(pdfIO) {
    suspend fun createPdf(context: Context, path: String, document: Document){
        setupDocument(context.resources.openRawResource(R.raw.af_3899_e_intake_and_output))
        setSectionOne(document)
        save(path)
    }

    private fun setSectionOne(document: Document){


        document.info.name?.toString(NameFormatter.Format.LAST_FIRST_MIDDLE_INITIAL)
            ?.let { displayName ->
                setField("NameRow", displayName)
            }

        val citeNumber = document.info.citeNumber ?: ""
        val dodNumber = document.info.dodId ?: ""
        val citeSSNValue = listOf(citeNumber,dodNumber).filter { it.isNotEmpty() }.joinToString ("/")
        //Ticket: 22820 DoDID should be inplace of SSN
        setField("SSNRow", citeSSNValue)


    }
}