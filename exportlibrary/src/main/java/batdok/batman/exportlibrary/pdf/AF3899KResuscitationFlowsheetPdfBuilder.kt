package batdok.batman.exportlibrary.pdf

import android.content.Context
import batdok.batman.exportlibrary.R
import batdok.batman.exportlibrary.io.PdfIO
import gov.afrl.batdok.encounter.Document
import gov.afrl.batdok.util.NameFormatter
import gov.afrl.batdok.util.Patterns
import gov.afrl.batdok.util.format
import java.time.Instant
import java.time.LocalDate
import java.time.Period
import java.time.ZoneId
import java.time.format.DateTimeFormatter

class AF3899KResuscitationFlowsheetPdfBuilder(pdfIO: PdfIO) : AcroformPdfBuilder(pdfIO) {
    suspend fun createPdf(context: Context, path: String, document: Document) {
        setupDocument(context.resources.openRawResource(R.raw.af_3899_k_resuscitation_flowsheet))
        setSectionOne(document)
        save(path)
    }

    private fun setSectionOne(document: Document) {

        document.info.name?.toString(NameFormatter.Format.LAST_FIRST_MIDDLE_INITIAL)
            ?.let { displayName ->
                setField("PatientName", displayName)
                setField("PatientName2", displayName)
            }
        val citeNumber = document.info.citeNumber ?: ""
        val dodNumber = document.info.dodId ?: ""
        val citeSSNValue =
            listOf(citeNumber, dodNumber).filter { !it.isNullOrBlank() }.joinToString("/")
        //Ticket: 22820 DoDID should be inplace of SSN
        setField("SSN", citeSSNValue)
        setField("SSN2", citeSSNValue)

        //(When the encounter closed or now if open)
        var dateTime = document.events.list?.filter {
            it.event == "Patient encounter closed"
        }?.maxOrNull()?.timestamp?.format(Patterns.mdhm_24_space_comma_colon, true)
            ?: Instant.now().format(Patterns.mdhm_24_space_comma_colon, true)

        setField("Date", dateTime)
        setField("Date2", dateTime)

        val patientSex = document.info.gender ?: ""
        setField("Sex", patientSex)


        document.info.dateOfBirth.dob?.let { dob ->
            val period = Period.between(dob, LocalDate.now())
            val years = period.years
            if (years == 0) {
                setField("Age", "${period.months} M")
            } else {
                setField("Age", "${years}Y, ${period.months}M")
            }
        }

        // Per ticket 22820
        // Mission #/ Destination (Destination MTF only)
        val missionNumberDestination = document.movement.destinationMtf ?: ""
        setField("MissionNumberDestination", missionNumberDestination)
    }
}