package batdok.batman.exportlibrary.pdf

import android.content.Context
import batdok.batman.exportlibrary.R
import batdok.batman.exportlibrary.io.PdfIO
import gov.afrl.batdok.encounter.Document
import gov.afrl.batdok.util.NameFormatter
import gov.afrl.batdok.util.Patterns
import gov.afrl.batdok.util.format
import java.time.Instant

class DnbiDD689PDFBuilder (pdfIO: PdfIO): AcroformPdfBuilder(pdfIO) {

    suspend fun createPdf(context: Context,
                          path: String,
                          document: Document,
                          disposition :String,
                          remarks: String,
                          otherSpecifiedDisposition: String ){
        setupDocument(context.resources.openRawResource(R.raw.dnbi_dd_689))
        fillDemographics(document)
        fillMedicalOfficersSection(disposition,remarks,otherSpecifiedDisposition)
        save(path)
    }

    private fun fillDemographics(document: Document){

        //Medical Condition is the complaints within the subjective object
        val medicalConditionText  = document.subjective.complaints.list.map { it.complaint }.joinToString(", ")
        setField("MEDICALDESCRIPTION", medicalConditionText)


        //Per Graham no check boxes
        setField("ILLNESS", "Off")
        setField("INJURY", "Off")

        //(When the encounter closed or now if open)
        var dateTime = document.events.list?.filter {
            it.event == "Patient encounter closed"
        }?.maxOrNull()?.timestamp?.format(Patterns.ymd, false)
            ?:
            Instant.now().format(Patterns.ymd, true)
        setField("DATE", dateTime)


        document.info.name?.toString(NameFormatter.Format.LAST_FIRST_MIDDLE_INITIAL)
            ?.let { displayName ->
                setField("NAME", displayName)
            }

        var dodIdNumber = document.info.dodId ?: ""
        setField("DoDIDNUMBER", dodIdNumber)

        var gradeRank = document.info.grade ?: ""
        setField("GRADERANK", gradeRank)

        // Organization and Station is the unit
        var organizationStation = document.info.unit ?: ""
        setField("ORGANIZATIONANDSTATION", organizationStation)

    }

    private fun fillMedicalOfficersSection(disposition :String,
                                           remarks: String,
                                           otherSpecifiedDisposition: String){
        disposition?.also {
            when (it) {
                "Duty" -> setField("DUTY", "On")
                "Quarters" -> setField("QUARTERS", "On")
                "Sick Bay" -> setField("SICK BAY", "On")
                "Hospital" -> setField("HOSPITAL", "On")
                "Not Examined" -> setField("NOT EXAMINED", "On")
                "Other" -> setField("OTHER", "On")
                else -> {}
            }
        }

        var otherDispositionOtherString  = if (otherSpecifiedDisposition.isNullOrEmpty()) "" else "Specification of Disposition: $otherSpecifiedDisposition\n\n"

        setField("OFFICERSREMARKS", otherDispositionOtherString + remarks)
    }
}