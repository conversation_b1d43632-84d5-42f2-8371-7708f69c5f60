package batdok.batman.exportlibrary.pdf

import android.content.Context
import batdok.batman.exportlibrary.R
import batdok.batman.exportlibrary.io.PdfIO
import gov.afrl.batdok.encounter.Document
import gov.afrl.batdok.util.NameFormatter
import gov.afrl.batdok.util.Patterns
import gov.afrl.batdok.util.format
import java.text.SimpleDateFormat
import java.time.Instant
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.Date

class AF3899HNeuroAssessmentPdfBuilder(pdfIO: PdfIO) : AcroformPdfBuilder(pdfIO) {
    suspend fun createPdf(context: Context, path: String, document: Document) {
        setupDocument(context.resources.openRawResource(R.raw.af_3899_h_neuro_assessment))
        setSectionOne(document)
        save(path)
    }


    private fun setSectionOne(document: Document) {
        document.info.name?.toString(NameFormatter.Format.LAST_FIRST_MIDDLE_INITIAL)
            ?.let { displayName ->
                setField("Name", displayName)
            }

        val citeNumber = document.info.citeNumber ?: ""
        val dodNumber = document.info.dodId ?: ""
        val citeSSNValue =
            listOf(citeNumber, dodNumber).filter { it.isNotEmpty() }.joinToString("/")
        //Ticket: 22820 DoDID should be inplace of SSN
        setField("SSN", citeSSNValue)


        //(When the encounter closed or now if open)
        var dateTime = document.events.list?.filter {
            it.event == "Patient encounter closed"
        }?.maxOrNull()?.timestamp?.format(Patterns.mdhm_24_space_comma_colon, true)
            ?: Instant.now().format(Patterns.mdhm_24_space_comma_colon, true)
        setField("DATE", dateTime)
    }
}