package batdok.batman.exportlibrary.pdf

import batdok.batman.exportlibrary.io.PdfIO
import com.tom_roush.pdfbox.cos.COSName
import com.tom_roush.pdfbox.pdmodel.PDDocument
import com.tom_roush.pdfbox.pdmodel.PDPage
import com.tom_roush.pdfbox.pdmodel.PDPageContentStream
import com.tom_roush.pdfbox.pdmodel.font.PDType1Font
import com.tom_roush.pdfbox.pdmodel.interactive.form.PDAcroForm
import com.tom_roush.pdfbox.pdmodel.interactive.form.PDTextField
import kotlinx.coroutines.Dispatchers
import java.io.IOException
import java.io.InputStream
import kotlin.coroutines.CoroutineContext
import kotlin.math.max
import kotlin.math.min

abstract class AcroformPdfBuilder(private val pdfIO: PdfIO) {
    lateinit var pdfDocument: PDDocument
    lateinit var acroform: PDAcroForm

    // A map of fields that we have wrapped text for already
    private val wrappedFields = mutableMapOf<String, List<String>>()

    private fun parseFontSizeFromAppearance(appearance: String) = appearance.split(" ").getOrNull(1)?.toIntOrNull()
    val defaultTextSize: Int
        get() = if(::acroform.isInitialized){
            parseFontSizeFromAppearance(acroform.defaultAppearance)?:0
        }else{
            0
        }

    fun leftAlign(fieldName: String){
        (acroform.getField(fieldName) as PDTextField).q = 0
    }

    class Overflow(){
        class OverflowItem(val text: String, val canSplit: Boolean, val separatingPrefix: String){
            override fun toString() = separatingPrefix + text
        }
        val overflowMap: MutableMap<String, MutableList<OverflowItem>> = mutableMapOf()

        fun addToSection(section: String, text: String, canSplit: Boolean = true, separator: String = ", "){
            val list = overflowMap.getOrDefault(section, mutableListOf())
            val overflowSep = if(list.isEmpty()) "" else separator
            list += OverflowItem(text, canSplit, overflowSep)
            overflowMap[section] = list
        }
        fun clearSection(section: String) = overflowMap.remove(section)
    }
    /**
     * If text is too large to fit in the field, it will be added here as a (Field title, full value) pair
     * Text that is added here will be grouped into chunks as they were added to the overflow that may
     * or may not be splittable
     */
    val overflow = Overflow()

    fun setupDocument(blankPdfFile: InputStream){
        pdfDocument = PDDocument.load(blankPdfFile)
        acroform = pdfDocument.documentCatalog.acroForm
    }

    suspend fun save(path: String, context: CoroutineContext = Dispatchers.IO){
        pdfIO.coWrite(path, pdfDocument, context)
    }

    open fun setField(fieldName: String, value: String){
        acroform.getField(fieldName).setValue(value)
    }

    /**
     * Add the given string to the given field in its entirety if it can fit, or to the overflow buffer if it does not fit or if there is already overflow
     */
    open fun addToFieldOrOverflow(
        fieldName: String,
        content: String,
        overflowKey: String = fieldName,
        fontSize: Int = 12,
        overflowText: String = "...",
        overflowSeparator: String = ", ",
        allowSplitting: Boolean = true
    ): Boolean {
        if(content.isEmpty()) return false
        when(val field = acroform.getField(fieldName)){
            is PDTextField -> {
                val currentText = field.value
                // If there is text in the field that we have not wrapped, wrap it now
                if(currentText.isNotEmpty() && fieldName !in wrappedFields) wrappedFields[fieldName] = wrapTextByWidthLimit(currentText, field.widgets[0].rectangle.width.toInt(), fontSize)
                field.defaultAppearance = "/Helv $fontSize Tf 0 g"
                // Calculate a wrapping for the given content, then add the existing wrappings to that list
                val currentTextLines = wrapTextByWidthLimit(content, field.widgets[0].rectangle.width.toInt(), fontSize)
                val textLines = (wrappedFields[fieldName] ?: listOf()) + currentTextLines
                // Update our most recent wrappings
                wrappedFields[fieldName] = textLines
                val maxLines = (field.widgets[0].rectangle.height / ((PDType1Font.HELVETICA_BOLD.boundingBox.height / 1000f) * fontSize)).toInt() - overflowText.count { it == '\n' }
                when{
                    overflowKey in overflow.overflowMap.keys -> {
                        // If this section already overflowed, continue to overflow
                        overflow.addToSection(overflowKey, content, allowSplitting, overflowSeparator)
                        return true
                    }
                    // Text fits, fill it
                    maxLines >= textLines.size -> field.value = textLines.joinToString("\n")
                    else -> {
                        // It doesnt fit
                        if(allowSplitting){
                            // Fill what we can, overflow the rest
                            field.value = (textLines.take(maxLines).joinToString("\n") + overflowText)
                            overflow.addToSection(overflowKey, textLines.drop(maxLines).joinToString("\n"), allowSplitting, overflowSeparator)
                            return true
                        } else{
                            field.value += overflowText
                            // Add the whole string to overflow
                            overflow.addToSection(overflowKey, content, allowSplitting, overflowSeparator)
                            return true
                        }
                    }
                }
            }
        }
        return false
    }

    protected fun setField(fieldName: String, value: Number?){
        setField(fieldName, when(value){
            -1 -> return
            -1f -> return
            is Int -> value.toString()
            is Float -> "%.1f".format(value)
            else -> return
        })
    }

    /**
     * Take the current [overflow] and create an appendix page to display the content
     */
    fun buildOverflowAppendix(){
        val newPage = PDPage(pdfDocument.getPage(0).mediaBox)
        val stream = PDPageContentStream(pdfDocument, newPage)
        stream.beginMarkedContent(COSName.ACTUAL_TEXT)
        stream.beginText()

        // must set font before calling showText()
        stream.setFont(PDType1Font.TIMES_BOLD, 25f)

        // offsets are relative to the previous one
        stream.newLineAtOffset(10f, 750f)
        stream.showText("Appendix")
        stream.newLineAtOffset(0f, -25f)

        val textSize = 12f

        stream.setFont(PDType1Font.COURIER, textSize)

        overflow.overflowMap.forEach { (header, value) ->
            // additional section header
            stream.setFont(PDType1Font.TIMES_BOLD, 16f)
            stream.showText(header)

            // add overflow text
            stream.setFont(PDType1Font.COURIER, 10f)
            stream.newLineAtOffset(5f, -20f)
            for (line in wrapTextByCharLimit(value.joinToString(separator = ""), 90)) {
                stream.showText(line)
                stream.newLineAtOffset(0f, -20f)
            }
            stream.newLineAtOffset(-5f, -25f)
        }

        stream.endText()
        stream.endMarkedContent()
        stream.close()
        pdfDocument.addPage(newPage)
    }

    /**
     * Return a list of lines that are less than [lineWidth] long. Each item in the list should be exactly one line
     */
    fun wrapTextByCharLimit(text: String, lineWidth: Int): List<String> {
        val lines = mutableListOf<String>()
        var textBuffer = text
        fun containsLineBreak() = '\n' in textBuffer.substring(0, min(lineWidth, textBuffer.length - 1))
        while (textBuffer.length > lineWidth || containsLineBreak()) {
            // find the right spot to cut it
            var cutPoint = lineWidth
            if(containsLineBreak()){
                cutPoint = textBuffer.indexOf('\n')-1
                textBuffer = textBuffer.replaceFirst("\n", "")
            } else {
                while (textBuffer[cutPoint] !in listOf(' ', ',', ';')) {
                    cutPoint--
                    // if there are no spaces, just cut it at the lineWidth
                    if (cutPoint <= 0) {
                        cutPoint = lineWidth
                        break
                    }
                }
            }
            lines.add(textBuffer.slice(IntRange(0, cutPoint)))
            textBuffer = textBuffer.slice(IntRange(cutPoint + 1, textBuffer.length - 1))
        }
        lines.add(textBuffer)
        return lines
    }

    /**
     * Return a list of lines that fit within [width] at [fontSize] and the index in [message] where each line begins.
     * Calling message.substring(i) where i is the first item in the pair will return a string that includes the given line and the rest of the message
     * The font used in measurement is HELVETICA_BOLD
     */
    fun wrapTextByWidthLimit(message: String, width: Int, fontSize: Int = defaultTextSize): List<String> {
        var text = message
        val wrappedLines = mutableListOf<String>()
        // Track the current index in the message string so we can get the remaining text in message from any line
        var correction = 0
        // while the event string is wider than the table width
        while (text.isNotEmpty()) {
            var line = ""
            var k = 0
            // add characters until it reaches the table width
            while (calculateTextWidth(line, fontSize) < width && k < text.length) {
                val char = text[k++]
                //If there's a newline, add it to the wrapped list and start a new line
                if (char == '\n') {
                    wrappedLines +=line
                    text = text.substring(k)
                    k = 0
                    line = ""
                    correction = 0
                }else{
                    line += char
                }
            }
            if (line.trim().contains(" ") && k < text.length) {
                text = "    " + text.substring(line.lastIndexOf(" ") + 1) // split on the last space of the string so that we don't split in the middle of a word
                k = line.lastIndexOf(" ")
                line = line.substring(0, k++)
                k += correction
                // Ignore the indent in the message string
                correction = -4
            } else {
                if(calculateTextWidth(line, fontSize) >= width){
                    line = line.dropLast(1)
                    text = text.substring(k - 1)
                } else {
                    text = text.substring(k)
                }
            }
            wrappedLines += line
        }
        return wrappedLines
    }

    @Throws(IOException::class)
    fun calculateTextWidth(s: String, fontSize: Int = defaultTextSize): Float {
        return if (s.contains("\n")) {
            val index = s.lastIndexOf("\n")
            max(
                calculateTextWidth(s.substring(0, index), fontSize),
                calculateTextWidth(s.substring(index + 1), fontSize)
            )
        } else {
            PDType1Font.HELVETICA_BOLD.getStringWidth(s) / 1000 * fontSize
        }
    }
}