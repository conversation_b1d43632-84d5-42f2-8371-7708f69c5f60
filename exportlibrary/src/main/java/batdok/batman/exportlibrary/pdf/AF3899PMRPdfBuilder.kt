package batdok.batman.exportlibrary.pdf

import android.content.Context
import batdok.batman.exportlibrary.R
import batdok.batman.exportlibrary.io.PdfIO
import gov.afrl.batdok.encounter.Document
import gov.afrl.batdok.encounter.Gender
import gov.afrl.batdok.encounter.Grade
import gov.afrl.batdok.util.NameFormatter
import gov.afrl.batdok.util.Patterns
import gov.afrl.batdok.util.format
import java.time.Instant
import java.time.Period
import java.time.ZoneId

@Deprecated("Use AF3899PMRPdfBuilder instead", ReplaceWith("AF3899PMRPdfBuilder"))
typealias AE3899PMRPdfBuilder = AF3899PMRPdfBuilder
class AF3899PMRPdfBuilder (pdfIO: PdfIO): AcroformPdfBuilder(pdfIO){

    suspend fun createPdf(context: Context, path: String, document: Document){
        setupDocument(context.resources.openRawResource(R.raw.af_3899_patient_movement_record))
        setSectionOne(document)
        save(path)
    }


    //Name, SSN, Date of Birth, Age, Sex, Service, Grade, Unit of Record and Phone Number Cite Number
    /** Sets the
     * Name - Last, First Middle Initial
     * SSN,
     * Date of Birth,
     * Age,
     * Sex,
     * Service,
     * Grade,
     * Unit of Record
     * Phone Number
     * Cite Number
    */
    private fun setSectionOne(document: Document){
        document.info.name?.toString(NameFormatter.Format.LAST_FIRST_MIDDLE_INITIAL)?.let { displayName->
            setField("name", displayName)
        }

        val ssn = document.info.ssn?: ""
        setField("ssn", ssn)

        val dob = document.info.dateOfBirth.dob
        if (!document.info.dateOfBirth.estimate) {
            // only show DOB on PDF if it is *not* an estimate
            val dobString = dob?.format(Patterns.ymd_dash) ?: ""
            setField("DATE OF BIRTH", dobString)
        }

        // age is shown even if DOB is an estimate
        if (dob != null) {
            val now = Instant.now().atZone(ZoneId.systemDefault()).toLocalDate()
            val lifespan = Period.between(dob, now)
            val age = lifespan.years
            setField("Age", age)
        }

        val sex = document.info.gender?: ""
        // Valid Sex Values are [1, 2] and Off
        setField("sex", when (sex) {
            "M", Gender.MALE.dataString -> "1"
            "F", Gender.FEMALE.dataString -> "2"
            else -> "Off"
        })


        val service = document.info.patcat?.service?.shortName?: ""
        setField("Service", service)

        val status = document.info.patcat?.status?.simpleStatus?: ""
        setField("status", status)

        val grade = Grade.entries.find { it.dataString == document.info.grade }?.shortString?: ""
        setField("Grade", grade)


        val unitOfRecord = document.info.unit?: ""
        setField("unit", unitOfRecord)

        val phoneNumber = document.info.unitPhoneNumber?: ""
        setField("unit phone", phoneNumber)

        val citeNumber = document.info.citeNumber?: ""
        setField("CITE NUMBER", citeNumber)
    }
}