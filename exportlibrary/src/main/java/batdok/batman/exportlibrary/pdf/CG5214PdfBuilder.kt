package batdok.batman.exportlibrary.pdf

import android.content.Context
import batdok.batman.exportlibrary.R
import batdok.batman.exportlibrary.io.PdfIO
import com.tom_roush.pdfbox.cos.COSName
import com.tom_roush.pdfbox.pdmodel.PDPage
import com.tom_roush.pdfbox.pdmodel.PDPageContentStream
import com.tom_roush.pdfbox.pdmodel.font.PDType1Font
import com.tom_roush.pdfbox.pdmodel.interactive.annotation.PDAnnotationWidget
import com.tom_roush.pdfbox.pdmodel.interactive.form.PDField
import com.tom_roush.pdfbox.pdmodel.interactive.form.PDTextField
import gov.afrl.batdok.encounter.*
import gov.afrl.batdok.encounter.movement.EvacStatus
import gov.afrl.batdok.encounter.movement.TriageCategory
import gov.afrl.batdok.encounter.treatment.CommonTreatments
import gov.afrl.batdok.encounter.treatment.ImmobilizationData
import gov.afrl.batdok.encounter.vitals.*
import gov.afrl.batdok.util.NameFormatter
import gov.afrl.batdok.util.Patterns
import gov.afrl.batdok.util.format
import mil.af.afrl.batdokdata.models.patient.EncounterModel
import mil.nga.mgrs.MGRS
import java.time.LocalDate
import java.time.Period

fun PDField.setFontSize(newSize: Int): PDField {
    if (this !is PDTextField) return this
    val appearanceString = acroForm.defaultAppearance
    val list = appearanceString.split(" ").toMutableList()
    list[1] = newSize.toString()
    defaultAppearance = list.joinToString(" ")
    return this
}

class CG5214PdfBuilder(pdfIO: PdfIO): AcroformPdfBuilder(pdfIO) {

    // "C-Collar" is sometimes called "C-Collar for positioning" -  batdok#20250
    private val cCollarTreatmentNames = listOf("C-Collar", "C-Collar for positioning")


    suspend fun createPdf(
        context: Context,
        path: String,
        patient: EncounterModel,
        doc: Document,
        userName: String,
        userUnit: String
    ) {
        setupDocument(context.resources.openRawResource(R.raw.cg_5214))
        overflow.overflowMap.clear()

        fillEncounterIdentification(doc)
        fillRescuerIdentification(userName, userUnit)
        fillIncidentDescription(patient, doc)
        fillObservationOfVictim(doc)
        fillMedsAndAllergies(doc)
        fillPriority(doc)
        fillVitals(doc)

        fillComments(doc)

        handleExtraLongFields(patient, doc)

        //Check for null widget pages in the acroForm fields
        //4 widget pages for fields are still null here -- ('L', 'R', 'ABD', and 'TIME')
        //Iterate through all in case of future PDF changes though
        fillEmptyWidgetsInField()

        save(path)
    }

    private fun fillEmptyWidgetsInField(){
        //Theres only 1 page on the PDF (except for the appendix), so make that the page for empty ones
        for (field: PDField in acroform.fieldTree) {
            for(widget: PDAnnotationWidget in field.widgets)
            {
                if(widget.page == null){
                    widget.page = pdfDocument.getPage(0)
                }
            }
        }
    }

    private fun fillComments(doc: Document){
        val stringBuilder = StringBuilder()

        val extraMedList = doc.medicines.list.sorted().drop(3)

        extraMedList.forEach {
            stringBuilder
                .append("\n  ")
                .append(it.administrationTime?.format(Patterns.hm_24_colon, useZuluTimeFormatting = true))
                .append(", ")
                .append(it.name)
                .append(", ")
                .append(it.getVolumeString())
        }
        if(stringBuilder.isNotEmpty()){
            stringBuilder.insert(0, "Additional Meds:")
        }

        doc.treatments.getTreatmentString(false, separator = "\n  ")
            .takeIf { it.isNotBlank() }
            ?.let {
                if(stringBuilder.isNotEmpty()){
                    stringBuilder.append("\n")
                }
                stringBuilder.append("Treatments:\n  $it")
            }

        val events = makeNoteFromEvents(
            doc.events.getAll(
                KnownEventTypes.OTHER,
                KnownEventTypes.SUBJECTIVE,
                KnownEventTypes.OBJECTIVE,
                KnownEventTypes.ASSESSMENT,
                KnownEventTypes.PLANNING,
                KnownEventTypes.PCC_CHECKLIST,
                KnownEventTypes.CASEVAC,
            )
        )

        if(events.isNotEmpty()) {
            stringBuilder.append("\n")
                .append(events)
        }

        val ellipsizedComments = setField("MedicalHistory", stringBuilder.toString(), ellipsizeIfLong = true)
            .takeIf { !it.first }?.second

        if(ellipsizedComments != null && overflow.overflowMap.toList().lastOrNull()?.first == "MedicalHistory"){
            val allComments = overflow.overflowMap.remove("MedicalHistory")?.joinToString { it.separatingPrefix + it.text } ?: ""
            //Take the text that was printed, remove the "..." from the end, and remove that string from the beginning of the full text
            // to get the left over text that didn't make it on the first page.
            val overflowComments = allComments.removePrefix(ellipsizedComments.removeSuffix("..."))
            overflow.addToSection("Comments (cont.)", overflowComments, true, "")
        }
    }

    private fun makeNoteFromEvents(events: List<Event>): String {
        return if(events.isEmpty()){
            ""
        }else {
            "Events:\n" + events.joinToString("\n") {
                it.timestamp!!.format(Patterns.mdhm_24_space_comma_colon, useZuluTimeFormatting = true) + " - ${it.event}"
            }
        }
    }

    private fun handleExtraLongFields(patient: EncounterModel, doc: Document){
        val stringBuilder = StringBuilder()
        overflow.overflowMap.forEach { (field, value) ->
            var header = when(field){
                "VictimName2" -> "Name"
                "IncidentLocation2" -> "Location"
                "MedicalHistory" -> "Comments (cont.)"
                else -> field
            }
            val text = when(field){
                "VictimName2" -> doc.info.name?.toString(NameFormatter.Format.LAST_FIRST_MIDDLE) ?: ""
                "IncidentLocation2" -> getLocationString(patient)
                else -> value.joinToString()
            }
            stringBuilder.append(header)
                .append(": ")
                .append(text)
                .append("\n")

        }
        if(stringBuilder.length>=90){
            addAppendix(addBreakLines(stringBuilder.toString(), 90))
        }else{
            addAppendix(stringBuilder.toString())
        }
    }

    private fun addBreakLines(string: String, breakLength: Int): String {
        val sb = StringBuilder()
        var newStartIndex = 0
        var endIndex: Int

        while(newStartIndex+breakLength<string.length){
            endIndex= string.substring(0, breakLength+newStartIndex).lastIndexOf(" ")
            sb.append(string.substring(newStartIndex, endIndex)).append("\n ")
            newStartIndex = endIndex
        }
        sb.append(string.substring(newStartIndex, string.lastIndex))

        return sb.toString()
    }

    private fun getLocationString(patient: EncounterModel) = patient.geoTag
        .takeIf { it.isTagged || it.isAttached }
        ?.location
        ?.let { MGRS.from(it.lon, it.lat).toString() }
        ?:""

    private fun addAppendix(string: String) {
        val newPage = PDPage(pdfDocument.getPage(0).mediaBox)
        val stream = PDPageContentStream(pdfDocument, newPage)
        stream.beginMarkedContent(COSName.ACTUAL_TEXT)
        stream.beginText()

        // must set font before calling showText()
        stream.setFont(PDType1Font.TIMES_BOLD, 25f)

        // offsets are relative to the previous one
        stream.newLineAtOffset(10f, 750f)
        stream.showText("Appendix")
        stream.newLineAtOffset(0f, -25f)

        val textSize = 10f

        stream.setFont(PDType1Font.COURIER, textSize)

        string.split("\n").forEach {
            stream.showText(it)
            stream.newLineAtOffset(
                0f,
                -(PDType1Font.COURIER.boundingBox.height / 1000 * textSize) * 1.5f
            )
        }

        stream.endText()
        stream.endMarkedContent()
        stream.close()
        pdfDocument.addPage(newPage)
    }

    private fun fillEncounterIdentification(doc: Document){

        val nameString = (doc.info.name?.toString(NameFormatter.Format.LAST_FIRST_MIDDLE) ?:"").takeUnless { it == "build**, **dev" }?:"**dev build**"
        val nameFits = setField("VictimName1", nameString, false)
        if (!nameFits.first) {
            setField("VictimName2", nameString.removePrefix(nameFits.second ?: ""))
        }

        when(doc.info.gender){
            Gender.MALE.dataString -> setField("Male", "X")
            Gender.FEMALE.dataString -> setField("Female", "X")
            else -> {}
        }
        doc.info.dob?.toAge()?.let {
            setField("AgeYears", it.first.toString())
            setField("AgeMonths", it.second.toString())
        }
    }

    private fun fillRescuerIdentification(userName: String, userUnit: String){
        setField("RescuerName", NameFormatter(userName).toString(NameFormatter.Format.LAST_FIRST_MIDDLE_INITIAL))
        setField("RescuerUnit", userUnit)
    }

    private fun fillIncidentDescription(patient: EncounterModel, doc: Document){
        doc.info.timeInfo?.let {
            setField("IncidentDate", it.format(Patterns.mdy_dash, useZuluTimeFormatting = true))
            setField("IncidentTime", it.format(Patterns.hm_24_colon, useZuluTimeFormatting = true))
        }
        setFieldToX("Aviation", doc.injuries.hasMoi(Moi.AIRCRAFT_INCIDENT, Moi.PARACHUTE_INCIDENT_OPERATIONS))
        setFieldToX("Auto", doc.injuries.hasMoi(Moi.MVC))
        setFieldToX(
            "Other",
            doc.injuries.mechanismsOfInjury
                .flatMap { it.value }
                .any {
                    it !in listOf(
                        Moi.AIRCRAFT_INCIDENT,
                        Moi.PARACHUTE_INCIDENT_OPERATIONS,
                        Moi.MVC
                    ).map { moi -> moi.dataString }
                }
        )
        setField("NatureOfEmergency", doc.injuries.allMoiInjuryString())

        val locationString = getLocationString(patient)
        acroform.getField("IncidentLocation1").setFontSize(8)
        val nameFits = setField("IncidentLocation1", locationString, false)
        if(!nameFits.first){
            setField("IncidentLocation2", locationString.removePrefix(nameFits.second?:""))
        }
    }

    private fun fillObservationOfVictim(doc: Document){
        fun fillTreatment(treatmentToFind: String, treatmentToFill: String = treatmentToFind){
            if(doc.treatments.contains(treatmentToFind)){
                setField(treatmentToFill, "X")
            }
            if(treatmentToFind in cCollarTreatmentNames && CommonTreatments.IMMOBILIZATION in doc.treatments ){
                doc.treatments[CommonTreatments.IMMOBILIZATION].forEach{
                    with(it.getData<ImmobilizationData>()){
                        if(this != null && type == treatmentToFind){
                            setField(treatmentToFill, "X")
                        }
                    }
                }
            }
        }

        fillTreatment(CommonTreatments.DRESSING.dataString)
        fillTreatment(CommonTreatments.SPLINT.dataString, "Splint")
        cCollarTreatmentNames.forEach {
            fillTreatment(it, "CCollar")
        }
        fillTreatment(CommonTreatments.TQ.dataString, "Tourniquet")
        fillTreatment(CommonTreatments.O2.dataString, "Oxygen")
    }

    private fun fillVitals(doc: Document){
        val allVitals = doc.vitals.vitalList
        val numVitals = allVitals.size
        val vitals = if (numVitals >= 4) {
            listOf(allVitals[0], allVitals[numVitals / 3], allVitals[numVitals * 2 / 3], allVitals[numVitals - 1])
        } else {
            doc.vitals.vitalList
        }
        vitals.forEachIndexed { i, vital ->
            val index = i+1
            setField("Time$index", vital.timestamp.format(Patterns.hm_24_colon, useZuluTimeFormatting = true))
            when(vital.get<Avpu>()?.avpu){
                null -> null
                Avpu.Level.UNRESPONSIVE.dataString -> "Uncon"
                Avpu.Level.VERBAL.dataString -> "Verbal"
                else -> vital.get<Avpu>()?.avpu
            }?.let {
                setField("$it$index", "X")
            }
            setField("PulseRate$index", vital.get<HR>()?.pulse)
            setField("RespRate$index", vital.get<Resp>()?.resp)
            setField("Systolic$index", vital.get<BloodPressure>()?.systolic)
            setField("Diastolic$index", vital.get<BloodPressure>()?.diastolic)
            setField("OralTemp$index", vital.get<Temp>()?.temp)
        }
    }

    private fun fillMedsAndAllergies(doc: Document){
        setField("Allergies", doc.info.allergies.joinToString(", "))
        val medList = doc.medicines.list.sorted()
        medList.take(3).forEachIndexed { i, medTreatment ->
            val index = i + 1
            setField("MedTime$index", medTreatment.administrationTime?.format(Patterns.hm_24_colon, useZuluTimeFormatting = true))
            setField("MedName$index", medTreatment.name)
            setField("MedDose$index", medTreatment.getVolumeString())
        }
    }

    private fun fillPriority(doc: Document){
        val assessedEvac = doc.movement.assessedEvac
        if(assessedEvac != null && assessedEvac != ""){
            setField("Priority" + when(doc.movement.assessedEvac){
                EvacStatus.URGENT.dataString -> "1"
                EvacStatus.URGENTSURGICAL.dataString -> "1"
                EvacStatus.PRIORITY.dataString -> "2"
                EvacStatus.ROUTINE.dataString -> "3"
                EvacStatus.CONVENIENCE.dataString -> "3"
                else -> return
            }, "X")
        } else {
            setField("Priority" + when(doc.movement.dispatchEvac){
                EvacStatus.URGENT.dataString -> "1"
                EvacStatus.URGENTSURGICAL.dataString -> "1"
                EvacStatus.PRIORITY.dataString -> "2"
                EvacStatus.ROUTINE.dataString -> "3"
                EvacStatus.CONVENIENCE.dataString -> "3"
                else -> return
            }, "X")
        }
    }

    override fun setField(fieldName: String, value: String) {
        setField(fieldName, value, true)
    }

    /**
     * Set the text of the field with name [fieldName] to be [value].
     *
     * Returns a pair consisting of if [value] is too long to fit and the string that fits inside the view.
     */
    private fun setField(fieldName: String, value: String?, ellipsizeIfLong: Boolean = true): Pair<Boolean, String?>{
        return acroform.getField(fieldName).run {
            if(value == null){
                setValue(value)
                Pair(true, value)
            }else if(this is PDTextField){
                val textSize = defaultAppearance.split(" ").getOrNull(1)?.toIntOrNull()?:0
                val maxChars = if(!isMultiline) {
                    //If it's a single line, only look at the text width
                    val fieldWidth = widgets[0].rectangle.width
                    val textWidth = calculateTextWidth(value, textSize).coerceAtLeast(1f)
                    ((value.length * fieldWidth / textWidth).toInt()).coerceIn(0, value.length)
                }else{
                    //If it's multiline, look at width and height
                    val fieldWidth = widgets[0].rectangle.width
                    val fontBoundingBox = PDType1Font.COURIER.boundingBox
                    val textWidth = fontBoundingBox.width /1000 * textSize
                    val charsPerLine = (fieldWidth / textWidth).toInt()
                    val numLines = widgets[0].rectangle.height / (PDType1Font.COURIER.boundingBox.height / 1000 * textSize)
                    (charsPerLine * numLines).toInt().coerceAtMost(value.length)
                }

                fun getSubstring(): String{
                    if(maxChars == value.length){
                        return value
                    }
                    val maxString = value.substring(0, maxChars - (if(ellipsizeIfLong) 3 else 0))
                    val lastSpaceIndex = maxString.lastIndexOf(" ")
                    return if(lastSpaceIndex != -1) {
                        maxString.substring(0, lastSpaceIndex)
                    }else{
                        maxString
                    }
                }

                if(maxChars < value.length && ellipsizeIfLong){
                    (getSubstring().trim() + "...").let {
                        overflow.addToSection(fieldName, value)
                        setValue(it)
                        Pair(false, it)
                    }
                }else{
                    setValue(getSubstring().trim())
                    Pair(maxChars >= value.length, getSubstring())
                }
            }else{
                setValue(value)
                Pair(true, value)
            }
        }
    }

    private fun setFieldToX(fieldName: String, checked: Boolean){
        setField(fieldName, if(checked) "X" else "", ellipsizeIfLong = true)
    }

    private fun LocalDate.toAge(): Pair<Int, Int> {
        val age = Period.between(this, LocalDate.now())
        return Pair(age.years, age.months)
    }
}