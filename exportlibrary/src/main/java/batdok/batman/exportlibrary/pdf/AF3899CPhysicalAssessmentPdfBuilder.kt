package batdok.batman.exportlibrary.pdf

import android.content.Context
import batdok.batman.exportlibrary.R
import batdok.batman.exportlibrary.io.PdfIO
import gov.afrl.batdok.encounter.Document
import gov.afrl.batdok.util.NameFormatter
import gov.afrl.batdok.util.Patterns
import gov.afrl.batdok.util.format
import java.time.Instant
import java.time.ZoneId

class AF3899CPhysicalAssessmentPdfBuilder(pdfIO: PdfIO): AcroformPdfBuilder(pdfIO) {
    suspend fun createPdf(context: Context,
                          path: String,
                          document: Document,
                          providersName: String,
                          providersInitials: String,
                          providersUnit: String,
                          providersLocation: String){
        setupDocument(context.resources.openRawResource(R.raw.af_3899_c_physical_assessment))
        setSectionOne(document, providersName, providersInitials,
            providersUnit, providersLocation
        )
        save(path)
    }


    private fun setSectionOne(document: Document,  providersName: String,
                              providersInitials: String,
                              providersUnit: String,
                              providersLocation: String){
        document.info.name?.toString(NameFormatter.Format.LAST_FIRST_MIDDLE_INITIAL)
            ?.let { displayName ->
                setField("NamePage1", displayName)
                setField("NAME  Last First Middle Initial_2", displayName)
            }

        val citeNumber = document.info.citeNumber ?: ""
        val dodNumber = document.info.dodId ?: ""
        val citeSSNValue = listOf(citeNumber,dodNumber).filter { it.isNotEmpty() }.joinToString("/")
        //Ticket: 22820 DoDID should be inplace of SSN
        setField("CITESSN", citeSSNValue)
        setField("CITESSN_2", citeSSNValue)


        //(When the encounter closed or now if open)
        var dateTime = document.events.list?.filter {
            it.event == "Patient encounter closed"
        }?.maxOrNull()?.timestamp?.format(Patterns.mdhm_24_space_comma_colon, true)
            ?: Instant.now().format(Patterns.mdhm_24_space_comma_colon, true)
        setField("DATETIMEZULU", dateTime)



         val providersNameInitialUnitLocation =
             listOf(providersName, providersInitials, providersUnit, providersLocation)
                 .filter { it.isNotEmpty() }.joinToString(separator = ", ")

        setField("Print Providers NameSignatureInitialsUnit  Location", providersNameInitialUnitLocation)

    }
}