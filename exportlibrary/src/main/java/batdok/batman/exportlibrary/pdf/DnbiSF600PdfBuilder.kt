package batdok.batman.exportlibrary.pdf

import android.content.Context
import batdok.batman.exportlibrary.R
import batdok.batman.exportlibrary.io.PdfIO
import com.tom_roush.pdfbox.pdmodel.PDDocument
import gov.afrl.batdok.encounter.*
import gov.afrl.batdok.encounter.observation.CommonObservations
import gov.afrl.batdok.encounter.observation.PhysicalExamData
import gov.afrl.batdok.encounter.vitals.*
import gov.afrl.batdok.util.NameFormatter
import gov.afrl.batdok.util.Patterns
import gov.afrl.batdok.util.format

private const val SOAP_OVERFLOW = "soap"

class DnbiSF600PdfBuilder(pdfIO: PdfIO): AcroformPdfBuilder(pdfIO) {
    private var nextPageNumber = 1
    private val extraPages = mutableListOf<PDDocument>()
    suspend fun createPdf(context: Context, path: String, document: Document, providerInfo: String = ""){
        setupDocument(context.resources.openRawResource(R.raw.dnbi_sf600_page1))
        acroform.fields.forEach { it.partialName += "_0" }

        addCommonPageInfo(0, document, providerInfo)
        fillSoapInfo(document)

        handleOverflow(context, document, providerInfo)

        save(path)
        extraPages.forEach { it.close() }
    }

    private fun buildNewPage(context: Context, document: Document, providerInfo: String){
        val doc = PDDocument.load(context.resources.openRawResource(R.raw.dnbi_sf600_page2))
        doc.documentCatalog.acroForm.fields.forEach { it.partialName += "_$nextPageNumber" }
        pdfDocument.addPage(doc.getPage(0))
        pdfDocument.documentCatalog.acroForm.fields.addAll(doc.documentCatalog.acroForm.fields)
        extraPages += doc
        addCommonPageInfo(nextPageNumber, document, providerInfo)
        nextPageNumber++
    }

    private fun handleOverflow(context: Context, document: Document, providerInfo: String){
        handleSoapOverflow(context, document, providerInfo)
        if(overflow.overflowMap.isNotEmpty()){
            buildOverflowAppendix()
        }
    }

    private fun handleSoapOverflow(context: Context, document: Document, providerInfo: String){
        overflow.overflowMap[SOAP_OVERFLOW]?.let {
            overflow.clearSection(SOAP_OVERFLOW)
            buildNewPage(context, document, providerInfo)
            var moreOverflow = false
            it.forEach {
                moreOverflow = addToFieldOrOverflow("SOAP_2_${nextPageNumber - 1}", it.text, overflowKey = SOAP_OVERFLOW, overflowText = "\n(Continued on next page)", overflowSeparator = it.separatingPrefix, allowSplitting = it.canSplit) || moreOverflow
            }
            if(moreOverflow){
                handleSoapOverflow(context, document, providerInfo)
            }
        }
    }

    private fun buildSectionHeader(header: String) =
        listOf(
            "——————————",
            header,
            "——————————"
        ).joinToString("\n")

    private fun fillSoapInfo(document: Document){
        val complaints = document.subjective.complaints
        val history = document.history
        val fieldName = "SOAP_1_0"
        fun addSoap(content: String) = addToFieldOrOverflow(fieldName, content, overflowKey = SOAP_OVERFLOW, overflowText = "\n(Continued on next page)", overflowSeparator = "\n")
        fun addSectionHeader(header: String) = addToFieldOrOverflow(fieldName, buildSectionHeader(header), overflowKey = SOAP_OVERFLOW, overflowText = "\n(Continued on next page)", allowSplitting = false, overflowSeparator = "\n")
        addSoap("Chief Complaint: ${complaints.list.joinToString("; ") { it.complaint }}")
        addSectionHeader("Subjective")
        addSoap("History of Present Illness:")
        addSoap(complaints.list.joinToString("\n", postfix = "\n") { it.history })
        addSoap("Review of Systems:")
        addSoap(complaints.list.joinToString("\n", postfix = "\n") { it.complaint + ": " + it.reviewOfSystems })
        addSoap("Past Medical / Surgical / Family History:")
        addSoap(getPastMedHistory(history))
        addSectionHeader("Objective")
        addSoap(getObjectiveString(document.observations))
        addSectionHeader("Assessment and Plan")
        addSoap(getAssessmentAndPlanString(document))
    }

    private fun getAssessmentAndPlanString(document: Document): String {
        val groupedTexts = document.subjective.complaints.list.map { complaint ->
            val symptoms = complaint.getSymptoms(document).mapNotNull { it.name }
            val redFlags = complaint.getRedFlags(document).mapNotNull { it.name }
            val exams = complaint.getExams(document)
            val assessments = complaint.getAssociatedItems(document)
                .filterIsInstance<Event>()
                .filter { it.eventType == KnownEventTypes.ASSESSMENT.dataString }
                .map { it.event }
            val plans = complaint.getAssociatedItems(document)
                .filterIsInstance<Event>()
                .filter { it.eventType == KnownEventTypes.PLANNING.dataString }
                .map { it.event }
            listOfNotNull(
                "ADTMC Results for ${complaint.complaint}".takeUnless { redFlags.isEmpty() && symptoms.isEmpty()},
                "Red Flags: ${redFlags.joinToString()}".takeUnless { redFlags.isEmpty() },
                "Symptoms: ${symptoms.joinToString()}".takeUnless { symptoms.isEmpty() },
                exams.joinToString{ "${it.name}: ${it.result}" }.takeUnless { exams.isEmpty() },
                complaint.disposition,
                "".takeUnless { assessments.isEmpty() && plans.isEmpty() }, // Add an extra line
                "${complaint.complaint} Assessment: ${assessments.joinToString()}".takeUnless { assessments.isEmpty() },
                "${complaint.complaint} Plan: ${plans.joinToString()}".takeUnless { plans.isEmpty() },
            ).joinToString("\n")
        }
        return groupedTexts.joinToString("\n\n")
    }

    private fun getObjectiveString(observations: Observations): String{
        val examFindings = observations[CommonObservations.PHYSICAL_EXAM].mapNotNull { it.getData<PhysicalExamData>() }
        return examFindings.joinToString("\n", postfix = "\n") { it.toDetailString() }
    }

    private fun getPastMedHistory(history: History): String{
        fun getHistory(type: HistoryType) = history.histories[type.dataString]?.takeUnless { it.isBlank() }
        val medHx = getHistory(HistoryType.MEDICAL)?.let { "MedHx: $it" }
        val surgHx = getHistory(HistoryType.SURGICAL)?.let { "SurgHx: $it" }
        val famHx = getHistory(HistoryType.FAMILY)?.let { "FamHx: $it" }

        return listOfNotNull(medHx, surgHx, famHx).joinToString("\n", postfix = "\n")
    }

    private fun addCommonPageInfo(page: Int, document: Document, providerInfo: String){
        setPatientInfo(document.info, page)

        //Set Left column fields
        setField("Date_$page", document.metadata.appointmentDate?.format(Patterns.mdy_slash)?:"")
        setVitals(document.vitals.list, document.info, page)
        setMedications(document.info, page)
        setAllergies(document.info, page)

        setProviderInformation(providerInfo, page)
    }

    private fun setPatientInfo(info: Info, page: Int){
        info.patcat?.status?.let { setField("STATUS_$page", it.shortDescription) }
        info.patcat?.service?.let {
            setField("DEPARTMENTSERVICE_$page", it.abbreviation)
        }
        val patientInfoBuilder = StringBuilder()
        info.name?.toString(NameFormatter.Format.LAST_FIRST_MIDDLE)?.let {
            patientInfoBuilder.appendLine(it)
        }
        info.dodId?.let {
            patientInfoBuilder.appendLine("ID Number: $it")
        }
        info.gender?.let {
            patientInfoBuilder.appendLine("Sex: $it")
        }
        info.dateOfBirth.takeUnless { it.estimate }?.dob?.let {
            patientInfoBuilder.appendLine("DOB: " + it.format(Patterns.mdy_slash))
        }
        info.unit?.let {
            patientInfoBuilder.appendLine("Unit: $it")
        }
        setField("PatientID_$page", patientInfoBuilder.trimEnd('\n').toString())
        //If they have dodId, use that, other wise use SSN (unless they don't have that either)
        listOfNotNull(info.dodId, info.ssn).firstOrNull()?.let {
            setField("SOCIAL SECURITYID  NUMBER_$page", it)
        }
    }

    //region Left Column Stuff

    private fun setVitals(vitals: List<EncounterVital>, info: Info, page: Int){
        val builder = StringBuilder()

        listOfNotNull(
            "Vitals:",
            vitals.getLastVital<BloodPressure>()?.toPdfString("BP"),
            vitals.getLastVital<HR>()?.toPdfString("HR"),
            vitals.getLastVital<Resp>()?.toPdfString("RESP"),
            vitals.getLastVital<Temp>()?.toPdfString("TEMP"),
            vitals.getLastVital<SpO2>()?.toPdfString("SPO2","%"),
            vitals.getLastVital<EtCO2>()?.toPdfString("ETCO2"),
            vitals.getLastVital<Avpu>()?.toPdfString("AVPU"),
            vitals.getLastVital<GCS>()?.toPdfString("GCS"),
            vitals.getLastVital<Pain>()?.toPdfString("Pain Scale")
        ).map { builder.appendLine(it) }

        info.height?.let { builder.appendLine( "HT: ${(it * CM_TO_IN).toInt()}\"") }
        info.weight?.let { builder.appendLine( "WT: " + (it * KG_TO_LB).toInt() + " lbs") }
        val fieldName = "Vitals_$page"
        leftAlign(fieldName)
        setField(fieldName, builder.trimEnd().toString())
    }

    private inline fun <reified T: IndividualVital> List<EncounterVital>.getLastVital() =
        lastOrNull { T::class in it.vitalMap }?.get<T>()

    private fun IndividualVital.toPdfString(label: String, suffix: String = ""): String{
        return "$label: $eventMessage$suffix"
    }

    private fun setMedications(info: Info, page: Int){
        overflow.clearSection("Medications")
        val fieldName = "Medications_$page"
        leftAlign(fieldName)
        setField(fieldName, "Medications:")
        info.medications.forEach {
            addToFieldOrOverflow("Medications_$page", it, "Medications", allowSplitting = false, overflowText = if(page > 0) "\n(See Appendix)" else "\n(Cont. Next\nPage)")
        }
    }

    private fun setAllergies(info: Info, page: Int){
        overflow.clearSection("Allergies")
        val fieldName = "Allergies_$page"
        setField(fieldName, "Allergies:")
        info.allergies.forEach {
            addToFieldOrOverflow(fieldName, it, "Allergies", allowSplitting = false, overflowText = if(page > 0) "\n(See Appendix)" else "\n(Cont. Next\nPage)")
        }
    }

    //endregion

    fun setProviderInformation(providerInfo: String, page: Int) {
        setField("SignedBy_$page", "Signed by: $providerInfo")
    }

}