package batdok.batman.exportlibrary.pdf

import android.content.Context
import batdok.batman.exportlibrary.R
import batdok.batman.exportlibrary.io.PdfIO
import gov.afrl.batdok.encounter.Document
import gov.afrl.batdok.util.NameFormatter
import gov.afrl.batdok.util.Patterns
import gov.afrl.batdok.util.format
import java.time.Instant
import java.time.LocalDate
import java.time.Period
import java.time.ZoneId

class AF3899LEnrouteCriticalCarePdfBuilder(pdfIO: PdfIO) : AcroformPdfBuilder(pdfIO) {
    suspend fun createPdf(context: Context, path: String, document: Document, usersUnit: String) {
        setupDocument(context.resources.openRawResource(R.raw.af_3899_l_enroute_critical_care))
        setSectionOne(document, usersUnit)
        save(path)
    }

    /** Sets the
     *
     */

    //TODO update with pdf builder sections.
    private fun setSectionOne(document: Document, usersUnit: String) {
        document.info.name?.toString(NameFormatter.Format.LAST_FIRST_MIDDLE_INITIAL)
            ?.let { displayName ->
                setField("1Name", displayName)
                setField("1NamePage2", displayName)
            }

        //Ticket: 22820 DoDID should be inplace of SSN
        val ssnPDFFieldValue = document.info.dodId ?: ""
        setField("2SSNPage1", ssnPDFFieldValue)
        setField("2SSNPage2", ssnPDFFieldValue)


        val citeNumber = document.info.citeNumber ?: ""
        setField("3Cite Number", citeNumber)
        setField("3Cite NumberPage2", citeNumber)

        document.info.dateOfBirth.dob?.let { dob ->
            val period = Period.between(dob, LocalDate.now())
            val years = period.years
            if (years == 0) {
                setField("4Age", "${period.months} M")
            } else {
                setField("4Age", "${years}Y, ${period.months}M")
            }
        }


        val sex = document.info.gender ?: ""
        setField("5Sex", sex)

        val weight = document.info.weight ?: ""
        if (weight.toString().isNotEmpty()) {
            setField("6Weight", "$weight kg")
        }

        var height = document.info.height ?: ""
        if (height.toString().isNotEmpty()) {
            setField("7Height", "$height cm")
        }

        var allergies = ""
        document.info.allergies?.let {
            allergies = it.joinToString(", ")
        }
        setField("8Allergies", allergies)

        val precedence = document.movement.precedence ?: ""
        // Only values accepted by the PDF represented by U P R
        val validPrecedenceValues = setOf("Urgent", "Priority", "Routine")
        if (validPrecedenceValues.contains(precedence)) {
            setField("9Precedence", precedence)
        }


        //(When the encounter closed or now if open)
        //(When the encounter closed or now if open)
        var dateTime = document.events.list?.filter {
            it.event == "Patient encounter closed"
        }?.maxOrNull()?.timestamp?.format(Patterns.mdhm_24_space_comma_colon, true)
            ?: Instant.now().format(Patterns.mdhm_24_space_comma_colon, true)
        setField("10Mission Date & Time", dateTime)

        val originatingMtf = document.movement.originatingMtf ?: ""
        setField("11Originating Facility", originatingMtf)

        val destinationFacility = document.movement.destinationMtf ?: ""
        setField("12Destination Facility", destinationFacility)

        val acTail = document.metadata.flightInfo.tail ?: ""
        setField("13AC/Tail #", acTail)

        //(User's unit, not the patient's unit)
        setField("14AE/CCATT Unit", usersUnit)

        val hrEnRoute = document.metadata.flightInfo.timeEnRoute()?.toHours()
        setField("16Hours En Route", if (hrEnRoute == null) "" else hrEnRoute.toString())

    }
}