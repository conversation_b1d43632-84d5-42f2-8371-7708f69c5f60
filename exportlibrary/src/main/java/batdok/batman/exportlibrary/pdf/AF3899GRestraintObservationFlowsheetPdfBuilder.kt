package batdok.batman.exportlibrary.pdf

import android.content.Context
import batdok.batman.exportlibrary.R
import batdok.batman.exportlibrary.io.PdfIO
import gov.afrl.batdok.encounter.Document
import gov.afrl.batdok.util.NameFormatter
import gov.afrl.batdok.util.Patterns
import gov.afrl.batdok.util.format
import java.time.Instant

class AF3899GRestraintObservationFlowsheetPdfBuilder(pdfIO: PdfIO): AcroformPdfBuilder(pdfIO) {
    suspend fun createPdf(context: Context, path: String, document: Document){
        setupDocument(context.resources.openRawResource(R.raw.af_3899_g_restraint_observation_flowsheet))
        setSectionOne(document)
        save(path)
    }

    private fun setSectionOne(document: Document){
        document.info.name?.toString(NameFormatter.Format.LAST_FIRST_MIDDLE_INITIAL)
            ?.let { displayName ->
                //NAME is the label
                setField("PatientName", displayName)
            }


        val citeNumber = document.info.citeNumber ?: ""
        val dodNumber = document.info.dodId ?: ""
        val citeSSNValue = listOf(citeNumber,dodNumber).filter { it.isNotEmpty() }.joinToString ("/")
        //Ticket: 22820 DoDID should be inplace of SSN
        setField("SSN",citeSSNValue)


        // Per PMs just use Date of PDF creation we do not have Mission num
        val missionNumberDate = ""
        val dateTimeZulu = Instant.now().format(Patterns.mdhm_24_space_comma_colon, true)
        setField("MissionNumDate", dateTimeZulu)

        //Do not have this information
        val timeApplied = ""
        setField("TimeApplied", timeApplied)

    }
}