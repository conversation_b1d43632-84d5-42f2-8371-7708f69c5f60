package batdok.batman.exportlibrary.pdf

import android.content.Context
import batdok.batman.exportlibrary.R
import batdok.batman.exportlibrary.io.PdfIO
import gov.afrl.batdok.encounter.Document
import gov.afrl.batdok.encounter.HistoryLastEventType
import gov.afrl.batdok.util.NameFormatter
import gov.afrl.batdok.util.Patterns
import gov.afrl.batdok.util.format

class AF3899FRestraintOrdersPdfBuilder(pdfIO: PdfIO): AcroformPdfBuilder(pdfIO) {
    suspend fun createPdf(context: Context, path: String, document: Document){
        setupDocument(context.resources.openRawResource(R.raw.af_3899_f_restraint_orders))
        setSectionOne(document)
        save(path)
    }

    private fun setSectionOne(document: Document){
        document.info.name?.toString(NameFormatter.Format.LAST_FIRST_MIDDLE_INITIAL)
            ?.let { displayName ->
                setField("NAME", displayName)
            }


        val citeNumber = document.info.citeNumber ?: ""
        val dodNumber = document.info.dodId ?: ""
        val citeSSNValue = listOf(citeNumber,dodNumber).filter { it.isNotEmpty() }.joinToString ("/")
        //Ticket: 22820 DoDID should be inplace of SSN
        setField("SSN",citeSSNValue)

        var allergies = ""
        document.info.allergies?.let {
            allergies = it.joinToString(", ")
        }
        setField("ALLERGIES", allergies)

        val destinationFacilitiy = document.movement.destinationMtf ?: ""
        setField("DestinationFacility", destinationFacilitiy)

        val originatingFacility = document.movement.originatingMtf ?: ""
        setField("OrigFacility", originatingFacility)

        document.history.getLastEventTime(HistoryLastEventType.MENSTRUAL.dataString).apply {
          if(this != null){
             var menstralString  = this.time.format(Patterns.ymd_dash)
              setField("LastMenstrual", menstralString)
          }
      }
    }
}