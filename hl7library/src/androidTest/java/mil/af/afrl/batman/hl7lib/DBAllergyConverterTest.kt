package mil.af.afrl.batman.hl7lib

import androidx.test.platform.app.InstrumentationRegistry
import ca.uhn.hl7v2.model.v231.message.ADT_A31
import ca.uhn.hl7v2.parser.DefaultModelClassFactory
import kotlinx.coroutines.runBlocking
import mil.af.afrl.batman.hl7lib.converter.DBAllergyConverter
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class DBAllergyConverterTest {

    @Before
    fun setup() {
        val context = InstrumentationRegistry.getInstrumentation().targetContext
        DBAllergyConverter.initialize(context)
    }

    @Test
    fun testBatdokDataToCodedElement() = runBlocking {
        // ARRANGE
        val msg = ADT_A31().apply { initQuickstart("ADT", "A31", "P") }
        val allergy = "acetaminophen"

        // ACT
        val codeElement = DBAllergyConverter.batdokDataToCodedElement(msg, allergy)

        // ASSERT
        Assert.assertEquals("d00049", codeElement?.identifier?.value)
        Assert.assertEquals(allergy, codeElement?.text?.value)
        Assert.assertEquals("MULTUMDRUG", codeElement?.nameOfCodingSystem?.value)
    }

    @Test
    fun testCodeToBatdokData() = runBlocking {
        // ARRANGE

        // ACT
        val allergy = DBAllergyConverter.codeToBatdokData("d00049")

        // ASSERT
        // There is a lot of ambiguity in many of the Multum codes. d00049
        //  has about 45 matches, all of them containing acetaminophen. This
        //  is the first match in the list, which is what you should expect.
        Assert.assertEquals("8-Hour Acetaminophen E.R.", allergy)
    }

}
