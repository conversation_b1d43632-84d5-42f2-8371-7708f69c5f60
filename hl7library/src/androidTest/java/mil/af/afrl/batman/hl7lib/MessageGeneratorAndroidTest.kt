package mil.af.afrl.batman.hl7lib

import androidx.test.platform.app.InstrumentationRegistry
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.MessageGenerator
import mil.af.afrl.batman.hl7lib.util.OutboundHL7Data
import org.junit.Assert
import org.junit.Test


// If this test seems to fail sometimes, enable "Run with Gradle" (https://stackoverflow.com/a/77271279)
//https://stackoverflow.com/questions/72358843/sharedtest-got-warning-duplicate-content-root-detected-on-android-studio-chipm
class MessageGeneratorTest {

    private val hl7Data = OutboundHL7Data()

    @Test
    fun testPdf_omdsEndpoint() {
        // ARRANGE
        val messageGenerator = MessageGenerator("myId", Endpoint.OMDS_DELTA, context = InstrumentationRegistry.getInstrumentation().context, writeToFile = false)

        // ACT
        val messages = messageGenerator.writeMessagesForEncounter(hl7Data)

        // ASSERT
        // No PDF should be in the ORU^R01
        val messageLines = messages.split("\r")
        val obxs = messageLines.filter { it.startsWith("OBX") }
        Assert.assertEquals(44, obxs.size)
        val pdfs = obxs.filter { it.contains("PDF") }
        Assert.assertEquals(0,pdfs.size)
    }

    // PDF disabled now, so this test is broken
//    @Test
//    fun testPdf_mhsgtEndpoint() {
//        // ARRANGE
//        val messageGenerator = MessageGenerator("myId", Endpoint.MHSG_T.apply { url = "" }, context = InstrumentationRegistry.getInstrumentation().targetContext)
//
//        // ACT
//        val messages = messageGenerator.writeMessagesForEncounter(patient)
//
//        // ASSERT
//        val messageLines = messages.split("\r")
//        val msh = messageLines.find { it.contains("MMF") && it.startsWith("MSH") }
//        Assert.assertNotNull(msh)
//        Assert.assertEquals("MMF", msh!!.split('|')[4])
//        val obrs = messageLines.filter { it.startsWith("OBR") }
//        Assert.assertEquals(2, obrs.size)
//        Assert.assertTrue(obrs.first().contains("note", true))
//        Assert.assertTrue(obrs.last().startsWith("OBR|1|||**********^Trauma Note^^^BATDOK R1 to R3 TCCC|||"))
//        Assert.assertTrue(obrs.last().endsWith("||MDOC|F"))
//        val obxs = messageLines.filter { it.startsWith("OBX") }
//        Assert.assertTrue(obxs.first().contains("Name:", true))
//        // Verify core structure of this segment, accept anything as the PDF data
//        val pdfSegment = obxs.last()
//        Assert.assertTrue(pdfSegment.startsWith("OBX|1|ED|89036-8^Trauma Note^LOINC||^APPLICATION^PDF^BASE64^"))
//        Assert.assertEquals("F", pdfSegment.split('|')[11])
//        Assert.assertEquals("ATTACHMENT", pdfSegment.split('|')[13])
//        Assert.assertTrue("\\d{14}\\+0000".toRegex().matches(pdfSegment.split('|')[14]))  // Ending timestamp
//    }

}
