package mil.af.afrl.batman.hl7lib.outbound

import gov.afrl.batdok.commands.proto.DocumentCommands.CommandData
import gov.afrl.batdok.encounter.Gender
import gov.afrl.batdok.encounter.Grade
import gov.afrl.batdok.encounter.Patcat
import gov.afrl.batdok.encounter.PatcatService
import gov.afrl.batdok.encounter.commands.buildAddRemoveAllergyListCommand
import gov.afrl.batdok.encounter.commands.buildAddTreatmentCommand
import gov.afrl.batdok.encounter.commands.buildChangeBloodTypeCommand
import gov.afrl.batdok.encounter.commands.buildChangeDOBCommand
import gov.afrl.batdok.encounter.commands.buildChangeDodIdCommand
import gov.afrl.batdok.encounter.commands.buildChangeGenderCommand
import gov.afrl.batdok.encounter.commands.buildChangeGradeCommand
import gov.afrl.batdok.encounter.commands.buildChangeHeightCommand
import gov.afrl.batdok.encounter.commands.buildChangeInjuryTimeCommand
import gov.afrl.batdok.encounter.commands.buildChangeNameCommand
import gov.afrl.batdok.encounter.commands.buildChangeNationalityCommand
import gov.afrl.batdok.encounter.commands.buildChangeOpenCloseEncounterCommand
import gov.afrl.batdok.encounter.commands.buildChangePatcatCommand
import gov.afrl.batdok.encounter.commands.buildChangePatientIdCommand
import gov.afrl.batdok.encounter.commands.buildChangeSsnCommand
import gov.afrl.batdok.encounter.commands.buildChangeUnitCommand
import gov.afrl.batdok.encounter.commands.buildChangeWeightCommand
import gov.afrl.batdok.encounter.commands.buildLogMedicineCommand
import gov.afrl.batdok.encounter.medicine.Medicine
import gov.afrl.batdok.encounter.treatment.ChestTubeData
import gov.afrl.batdok.encounter.treatment.Treatment
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.temporal.ChronoUnit

fun buildHistoricalTestCommands() : List<CommandData>{
    val commandList = listOf(
        // Info Commands
        buildChangeNameCommand("test j patient"),
        buildChangeSsnCommand("123456789"),
        buildChangeBloodTypeCommand("A+"),
        buildChangeInjuryTimeCommand(Instant.ofEpochMilli(1687867264877).minus(1,ChronoUnit.DAYS)),
        buildChangePatcatCommand(Patcat(PatcatService.AIR_FORCE, null)),
        buildChangeUnitCommand("unit"),
        buildChangeGradeCommand(Grade.O01),
        buildAddRemoveAllergyListCommand(listOf("Acetaminophen", "Amoxicillin", "Anti-seizure Drugs"), emptyList()),
        buildChangeGenderCommand(Gender.MALE),
        buildChangeWeightCommand(456f),
        buildChangeHeightCommand(123f),
        buildChangeDOBCommand(LocalDate.ofInstant(Instant.ofEpochMilli(1056715353011), ZoneId.of("UTC"))),
        buildChangeDodIdCommand("**********"),
        buildChangePatientIdCommand(DomainId.create()),
        buildChangeNationalityCommand("United States of America"),

        // Med Commands
        buildLogMedicineCommand(
            Medicine("Ertapenem", ndc = null, rxcui = null, administrationTime = Instant.ofEpochMilli(1687868185002),
                route = "IV", volume = 1.0f, unit = "gm", type = "Analgesic")
        ),
        buildLogMedicineCommand(
            Medicine("Acetaminophen", ndc = null, rxcui = "", administrationTime = Instant.ofEpochMilli(1687868185003),
                route = "PR", volume = 99.0f, unit = "", type = "Analgesic")
        ),

        //treatment Commands
        buildAddTreatmentCommand(
            Treatment(
                name = "CHEST_TUBE",
                timestamp = Instant.ofEpochMilli(1687868117654),
                treatmentData = ChestTubeData("Right"),
            )
        ),

        //close encounter
        buildChangeOpenCloseEncounterCommand(
            open = false
        )
    ).map(::buildCommandDataFromCommand)

    return commandList
}