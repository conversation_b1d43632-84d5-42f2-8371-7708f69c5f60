package mil.af.afrl.batman.hl7lib.util

import android.content.Context
import android.util.Base64
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.MessageRequester
import mil.af.afrl.batman.hl7lib.OmdsNetworkEndpoint
import okhttp3.mockwebserver.MockResponse
import okhttp3.mockwebserver.MockWebServer
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import java.security.KeyStore

class MessageRequesterTest {

    private lateinit var mockServer: MockWebServer
    private lateinit var testHl7Data: InboundHL7Data

    @get:Rule val mockkRule = MockkRule(this, relaxed = true)

    @Before
    fun setup() {
        val testEndpoint = mockk<OmdsNetworkEndpoint>(relaxed = true)
        every { testEndpoint.format } returns EndpointType.OMDS

        // The default AndroidCAStore is used in code, but can't be reached in a test
        val defaultKeystore = KeyStore.getInstance(KeyStore.getDefaultType())
        mockkStatic(KeyStore::class)
        every { KeyStore.getInstance(any()) } returns defaultKeystore

        // https://github.com/square/okhttp/tree/master/mockwebserver
        mockServer = MockWebServer().apply {
            // Calls for each response will need to be queued... what is sent doesn't really matter
            val mockedResponse = MockResponse().setBody("{ Message: \"hl7Message\" }")
            repeat(3) { enqueue(mockedResponse) }
            start()
        }

        every { testEndpoint.a28Url } returns mockServer.url("/a28").toString()
        every { testEndpoint.pampiUrl } returns mockServer.url("/pampi").toString()
        every { testEndpoint.documentsUrl } returns mockServer.url("/document").toString()

        testHl7Data = InboundHL7Data().apply {
            patientId = "0"
            endpoint = testEndpoint
        }
    }

    @After
    fun teardown() {
        mockServer.shutdown()
    }

    @Test
    fun testRequestMessages() {
        // ARRANGE
        // This doesn't exist in unit tests, so mock it out
        mockkStatic(Base64::class)
        every { Base64.decode(any<String>(), any()) } returns "hl7Message".toByteArray()

        // Mocks out ConverterInitializer items
        val contextMock = mockk<Context>(relaxed = true)
        every { contextMock.assets.open(any()) } returns "asdf".byteInputStream()

        // Callback to ensure that each network request is successful
        var successfulCallbacks = 0
        MessageRequester.requestCompletedCallback = { _, response ->
            Assert.assertEquals(200, response.code)
            successfulCallbacks++
        }

        // ACT
        MessageRequester.requestMessages(testHl7Data, contextMock).join()
        // Wait for network callback
        Thread.sleep(1000)

        // ASSERT
        Assert.assertEquals(3, mockServer.requestCount)
        // The three requests above are queued. I can't be sure what order the endpoints are called,
        //   so take them all and look to see that each has been called once
        val requestData = List(3) { mockServer.takeRequest() }.map { it.path }
        Assert.assertTrue(requestData.any { it?.startsWith("/a28") == true })
        Assert.assertTrue(requestData.any { it?.startsWith("/pampi") == true })
        Assert.assertTrue(requestData.any { it?.startsWith("/document") == true })
        Assert.assertEquals(1, successfulCallbacks)
    }

    @Test
    fun testCancelRequests() {
        // ARRANGE
        // Callback to ensure that each network request is successful
        var successfulCallbacks = 0
        MessageRequester.requestCompletedCallback = { _, response ->
            Assert.assertEquals("Canceled", response.message)
            successfulCallbacks++
        }

        // ACT
        // Do a request, then immediately cancel it
        MessageRequester.requestMessages(testHl7Data, mockk(relaxed = true)).join()
        MessageRequester.cancelRequests()
        // Wait for network callback
        Thread.sleep(1000)

        // ASSERT
        Assert.assertEquals(0, mockServer.requestCount)
        Assert.assertEquals(1, successfulCallbacks)
    }

}
