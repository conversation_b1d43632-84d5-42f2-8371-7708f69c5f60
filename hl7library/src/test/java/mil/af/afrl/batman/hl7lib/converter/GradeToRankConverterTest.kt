package mil.af.afrl.batman.hl7lib.converter

import gov.afrl.batdok.encounter.Grade
import org.junit.Assert
import org.junit.Test

class GradeToRankConverterTest {

    @Test
    fun testGradeCodeFromString_validGrade() {
        // ARRANGE
        val grade = Grade.E01.dataString

        // ACT
        val hl7Grade = gradeCodeFromString(grade)

        // ASSERT
        Assert.assertEquals("E-1", hl7Grade)
    }

    @Test
    fun testGradeCodeFromString_invalidGrade() {
        // ARRANGE
        val grade = "badgrade"

        // ACT
        val hl7Grade = gradeCodeFromString(grade)

        // ASSERT
        Assert.assertEquals(grade, hl7Grade)
    }

}
