package mil.af.afrl.batman.hl7lib.util

import io.mockk.MockKAnnotations
import io.mockk.unmockkAll
import org.junit.rules.TestRule
import org.junit.runner.Description
import org.junit.runners.model.Statement

class MockkRule(val testObject: Any,
                val overrideRecordPrivateCalls: Boolean = false,
                val relaxUnitFun: Boolean = false,
                val relaxed: Boolean = false) : TestRule {
    override fun apply(base: Statement, description: Description?) : Statement {
        return object : Statement(){
            override fun evaluate() {
                try {
                    MockKAnnotations.init(testObject,
                            overrideRecordPrivateCalls = overrideRecordPrivateCalls,
                            relaxUnitFun = relaxUnitFun, relaxed = relaxed)
                    base.evaluate()
                } finally {
                    unmockkAll()
                }
            }
        }
    }
}
