package mil.af.afrl.batman.hl7lib.outbound

import ca.uhn.hl7v2.model.primitive.CommonTS
import ca.uhn.hl7v2.model.v231.message.ADT_A31
import gov.afrl.batdok.encounter.KnownEventTypes
import gov.afrl.batdok.encounter.commands.buildLogMedicineCommand
import gov.afrl.batdok.encounter.commands.buildRemoveTreatmentCommand
import gov.afrl.batdok.encounter.medicine.Medicine
import gov.afrl.batdok.util.buildCommandData
import io.mockk.coEvery
import io.mockk.mockkObject
import mil.af.afrl.batdokdata.id.EncounterId
import mil.af.afrl.batman.batdokid.DomainId
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.converter.DBAllergyConverter
import mil.af.afrl.batman.hl7lib.converter.LocationInjuryConverter
import mil.af.afrl.batman.hl7lib.converter.MedicationConverter
import mil.af.afrl.batman.hl7lib.converter.MoiConverter
import mil.af.afrl.batman.hl7lib.converter.RouteConverter
import mil.af.afrl.batman.hl7lib.converter.TimeConverter
import mil.af.afrl.batman.hl7lib.converter.TreatmentConverter
import mil.af.afrl.batman.hl7lib.converter.UnitConverter
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.ADT_A03Builder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.ADT_A04Builder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.ADT_A28Builder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.ADT_A31Builder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.MessageGenerator
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.ORM_O01Builder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.ORU_R01Builder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.ORU_ZB2Builder
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.msh03SendApplicationCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.msh05ReceiveApplicationCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.msh06ReceivingFacilityCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.msh11ProcessingIDCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.msh12VersionIDCode
import mil.af.afrl.batman.hl7lib.data.models.v231.field.CS54
import mil.af.afrl.batman.hl7lib.data.models.v231.field.DHMSM_ICD
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ExtendedCodedElement
import mil.af.afrl.batman.hl7lib.data.models.v231.field.RxNorm
import mil.af.afrl.batman.hl7lib.data.models.v231.field.SNOMED_CT
import mil.af.afrl.batman.hl7lib.data.models.v231.field.TACICD10
import mil.af.afrl.batman.hl7lib.util.MockkRule
import mil.af.afrl.batman.hl7lib.util.asUuidString
import mil.af.afrl.batman.hl7lib.util.hashedWith
import org.junit.Assert
import org.junit.Rule
import org.junit.Test
import java.time.Instant
import java.util.Date

class TacOutboundTest : BaseOutboundTest() {

    // TODO: Update codes once we get those figured out
    @get:Rule val mockkRule = MockkRule(this, relaxed = true)

    override fun setup() {
        super.setup()

        val encounterId1 = DomainId.create<EncounterId>()
        val encounterId2 = DomainId.create<EncounterId>()
        hl7Data.endpoint = Endpoint.TAC

        // Clear old codes set in earlier tests
        TreatmentConverter.therapeuticProcedures.clear()
        TreatmentConverter.diagnosticProcedures.clear()
        TreatmentConverter.unclassifiedProcedures.clear()

        hl7Data.commandsByEncounter = mapOf(
            encounterId1 to buildTestCommands(),
            encounterId2 to buildHistoricalTestCommands()
        )

        hl7Data.activeEncounterId = encounterId1
    }

    @Test
    fun testBuildMessagesForEncounter() {
        // ARRANGE
        mockkObject(DBAllergyConverter)
        val msgMock = ADT_A31().apply { initQuickstart("ADT", "A31", "P") }
        coEvery { DBAllergyConverter.batdokDataToCodedElement(any(), any()) } returns ExtendedCodedElement(msgMock, "161", "Acetaminophen", "MULTUMDRUG")

        // ACT
        val messages = MessageGenerator("deviceId", Endpoint.TAC, writeToFile = false).buildMessagesForEncounter(hl7Data)

        // ASSERT
        Assert.assertTrue(messages.isNotEmpty())
        // Validate no A02 is present
        Assert.assertFalse(messages.first().contains("ADT^A02"))
    }

    override fun testADTA02() {
        // Not present, see above test
    }

    override fun testADTA04() {
        // ARRANGE

        // ACT
        val result = ADT_A04Builder(Endpoint.TAC).populateAll(hl7Data).build().toResult(Endpoint.TAC, hasPv1Discharge = true)

        // ASSERT
        val expected = """
            MSH|^~\&|BATDOK||CDP||zzz||ADT^A04|yyy|P|2.3
            ${evnSegment("A04")}
            ${pidSegment(hl7Data,EndpointType.TAC)}
            ${pv1Segment(hl7Data)}
            PV2|||^Amputation, Crush, Burn
            ACC|20230627120104+0000
        """.trimIndent().replace("\n", "\r")
        Assert.assertEquals(expected, result)
    }

    override fun testADTA28() {
        // ARRANGE
        val testInjuryCodes = listOf(
            "Name\tDefault\t",
            "AMPUTATION\t47",
            "BURN\t5",
            "FIRE\tNA",
            "CRUSH\tT14.8XXA"
        ).joinToString("\n")
        LocationInjuryConverter.loadData(testInjuryCodes.byteInputStream(), TACICD10)

        MoiConverter.map.putAll(mapOf(
            buildMapEntry("Burn", "123", TACICD10),
            buildMapEntry("Crush", "456", TACICD10),
            buildMapEntry("Fire", "789", TACICD10),
        ))

        TreatmentConverter.therapeuticProcedures.putAll(mapOf(
            buildMapEntry("TQ", "20655006", SNOMED_CT),
            buildMapEntry("Dressing", "3895009", SNOMED_CT),
            buildMapEntry("O2", "57485005", SNOMED_CT),
            buildMapEntry("Chest Tube", "264957007", SNOMED_CT),
            buildMapEntry("Chest Seal", "182531007", SNOMED_CT),
            buildMapEntry("Splint", "79321009", SNOMED_CT),
        ))

        // ACT
        val result = ADT_A28Builder(Endpoint.TAC).populateAll(hl7Data).build().toResult(Endpoint.TAC)
        val noteTime = result.substringAfter("Foley|")  // After note tag
            .substringBefore('\r')  // Before newline
        val cleanResult = result.replace(noteTime, "ttt")

        // ASSERT
        val expected = """
            MSH|^~\&|BATDOK||CDP||zzz||ADT^A28|yyy|P|2.3
            ${evnSegment("A28")}
            ${pidSegment(hl7Data,EndpointType.TAC)}
            DG1|1||789^Fire^ICD10^MOI||20230627120104+0000|A
            DG1|2||47^Amputation^INJURY^Injury||20230627120104+0000|A
            ZDG|Anterior|Right Wrist
            DG1|3||47^Amputation^INJURY^Injury||20230627120104+0000|A
            ZDG|Anterior|Right Lower Leg
            DG1|4||T14.8XXA^Other injury of unspecified body region^ICD10^Injury||20230627120104+0000|A
            DG1|5||5^Burn^INJURY^Injury||20230627120104+0000|A
            ZDG|Posterior|Left Upper Leg
            DG1|6||47^Amputation^INJURY^Injury||20230627120104+0000|A
            ZDG|Anterior|Left Hand
            PR1|1||20655006^Application of tourniquet (procedure)^SCT|TQ (Location: Extremity, Sub-Location: LUE)|20230627121501+0000|P
            OBX|1|TX|66019005^Limb structure (body structure)^SCT|1|Extremity||||||F|||20230627121501+0000
            OBX|2|TX|368208006^Left upper arm structure (body structure)^SCT|2|LUE||||||F|||20230627121501+0000
            PR1|2||20655006^Application of tourniquet (procedure)^SCT|TQ (Location: Extremity, Sub-Location: RLE)|20230627121504+0000|P
            OBX|1|TX|66019005^Limb structure (body structure)^SCT|1|Extremity||||||F|||20230627121504+0000
            OBX|2|TX|62175007^Structure of right lower limb (body structure)^SCT|2|RLE||||||F|||20230627121504+0000
            PR1|3||20655006^Application of tourniquet (procedure)^SCT|TQ (Location: Junctional)|20230627121505+0000|P
            OBX|1|TX|50974003^Junctional (qualifier value)^SCT|1|Junctional||||||F|||20230627121505+0000
            PR1|4||20655006^Application of tourniquet (procedure)^SCT|TQ (Location: Truncal)|20230627121505+0000|P
            OBX|1|TX|22943007^Trunk structure (body structure)^SCT|1|Truncal||||||F|||20230627121505+0000
            PR1|5||3895009^Application of dressing (procedure)^SCT|Dressing (Type: Pressure)|20230627121509+0000|P
            OBX|1|TX|118414001^Pressure dressing, device (physical object)^SCT|1|Pressure||||||F|||20230627121509+0000
            PR1|6||69466000^Unknown procedure (finding)^SCT|INTACT|20230627121511+0000
            PR1|7||57485005^O2^SCT|O2|20230627121513+0000|P
            PR1|8||446847002^Drainage of pleural cavity via chest tube (procedure)^SCT|CHEST_TUBE (Location: Right)|20230627121517+0000|P
            PR1|9||464094004^Pneumothorax dressing (physical object)^SCT|CHEST_SEAL (LeftFront)|20230627121520+0000|P
            PR1|10||79321009^Splint^SCT|SPLINT (Pulse present)|20230627121524+0000|P
            PR1|11||69466000^Unknown procedure (finding)^SCT|Escharotomy|20230627121529+0000
            PR1|12||392231009^Intravenous cannulation^SCT|Line (Type: Peripheral, SubType: EZ-IO, Location: Left Hand, Gauge: 18)|20230627121529+0000
            OBX|1|TX|85151006^Structure of left hand (body structure)^SCT|1|Left Hand||||||F|||20230627121529+0000
            OBX|2|NM|277306007^18G (qualifier)^SCT|2|18.0||||||F|||20230627121529+0000
            PR1|13||112798008^Insertion of endotracheal tube (procedure)^SCT|ET-Tube (Confirmation: Breath Sounds, Depth: 7)|20230627121529+0000
            OBX|1|NM|131197000^Depth (qualifier value)^SCT|1|7||||||F|||20230627121529+0000
            PR1|14||69466000^Unknown procedure (finding)^SCT|Foley|ttt
            PR1|15||69466000^Unknown procedure (finding)^SCT|Dressing Change|ttt
            PR1|16||52653008^Respiratory sounds (observable entity)^SCT|Breath Sounds (Left Wheezing, Right Rhonchi)|20230627122108+0000
            PR1|17||69466000^Unknown procedure (finding)^SCT|Respiratory Effort (Labored)|20230627122108+0000
            PR1|18||268925001^Examination of respiratory system (procedure)^SCT|Chest Equal Rise and Fall|20230627122108+0000
            ZPI|1||||A+||||||||||HUMAN
            ZEI|1||USAF||||||||AF00
            ZMI|1|||unit
        """.trimIndent().replace("\n", "\r")
        Assert.assertEquals(expected, cleanResult)
    }

    override fun testADTA31() {
        // ARRANGE
        mockkObject(DBAllergyConverter)
        val msgMock = ADT_A31().apply { initQuickstart("ADT", "A31", "P") }
        coEvery { DBAllergyConverter.batdokDataToCodedElement(any(), "Acetaminophen") } returns ExtendedCodedElement(msgMock, "161", "Acetaminophen", "MULTUMDRUG")
        coEvery { DBAllergyConverter.batdokDataToCodedElement(any(), "Amoxicillin") } returns ExtendedCodedElement(msgMock, "723", "Amoxicillin", "MULTUMDRUG")
        coEvery { DBAllergyConverter.batdokDataToCodedElement(any(), "Anti-seizure Drugs") } returns ExtendedCodedElement(msgMock, "294620008", "Anti-Seizure Drugs", "MULTUMDRUG")

        // ACT
        val result = ADT_A31Builder(Endpoint.TAC).populateAll(hl7Data).joinToString("\r"){ it.toResult(Endpoint.TAC, hasPv1Discharge = true) }

        // ASSERT
        val expected = """
            MSH|^~\&|BATDOK||CDP||zzz||ADT^A31|yyy|P|2.3
            ${evnSegment("A31")}
            ${pidSegment(hl7Data,EndpointType.TAC)}
            ${pv1Segment(hl7Data)}
            AL1|1|DA|161^Acetaminophen^MULTUMDRUG|NE
            AL1|2|DA|723^Amoxicillin^MULTUMDRUG|NE
            AL1|3|DA|294620008^Anti-Seizure Drugs^MULTUMDRUG|NE
        """.trimIndent().replace("\n", "\r")
        Assert.assertEquals(expected, result)
    }

    @Test
    override fun testORUR01() {
        // ARRANGE
        UnitConverter.map.putAll(mapOf(
            buildMapEntry("deg c", "Centigrade", CS54),
            buildMapEntry("ml", "Milliliters", CS54),
        ))

        val document = hl7Data.getDocumentForEncounter(hl7Data.activeEncounterId!!)
        val treatmentToRemove = document?.treatments?.list?.getOrNull(2)

        treatmentToRemove?.let { 
            val command = buildCommandData(
            buildRemoveTreatmentCommand(
                treatmentId = it.id,
                docError = false,
            )
        )
            hl7Data.addCommand(hl7Data.activeEncounterId!! ,command)
            document.handle(listOf(command))
        }

        

        // ACT
        // All of the individual ORUs are built by this same class. Concatenate them all
        //  and make sure they look right.
        val result = ORU_R01Builder(Endpoint.TAC).populateAll(hl7Data)
            .joinToString("\r") { it.toResult(Endpoint.TAC,false, true) }

        // ASSERT
        val expected = """
            MSH|^~\&|BATDOK||CDP||zzz||ORU^R01|yyy|P|2.3
            ${pidSegment(hl7Data,EndpointType.TAC)}
            ${pv1Segment(hl7Data)}
            OBR|1|||67493-7^Initial patient acuity NEMSIS^LOINC|||ttt|||||||||||||||ttt|||F
            ZUI|1|${document!!.id.hashedWith("triage").asUuidString()}|UNKNOWN
            OBX|1|TX|LA17694-3^Minimal^LOINC||Minimal||||||F|||ttt
            MSH|^~\&|BATDOK||CDP||zzz||ORU^R01|yyy|P|2.3
            ${pidSegment(hl7Data,EndpointType.TAC)}
            ${pv1Segment(hl7Data)}
            OBR|1|||77153-5^EMS Transport mode descriptors NEMSIS^LOINC|||ttt|||||||||||||||ttt|||F
            ZUI|1|${document.id.hashedWith("evacprecedence").asUuidString()}|UNKNOWN
            OBX|1|TX|A^Urgent^PRECEDENCE||Urgent||||||F|||ttt
            MSH|^~\&|BATDOK||CDP||zzz||ORU^R01|yyy|P|2.3
            ${pidSegment(hl7Data,EndpointType.TAC)}
            ${pv1Segment(hl7Data)}
            OBR|1|||VITALSIGNS|||ttt|||||||||||||||ttt|||F
            ZUI|1|${document.id.hashedWith("heightandweight").asUuidString()}|UNKNOWN
            OBX|1|NM|4154138^Height/Length Estimated^GENESISAlias||123.0|^cm|||||F|||ttt
            OBX|2|NM|4154135^Weight Estimated^GENESISAlias||456.0|^kg|||||F|||ttt
            MSH|^~\&|BATDOK||CDP||zzz||ORU^R01|yyy|P|2.3
            ${pidSegment(hl7Data,EndpointType.TAC)}
            ${pv1Segment(hl7Data)}
            OBR|1|||VITALSIGNS|||20230627120856+0000|||||||||||||||20230627120856+0000|||F
            ZUI|1|${document.vitals.list[0].vitalId.asUuidString()}|UNKNOWN
            OBX|1|NM|703511^Peripheral Pulse Rate^GENESISAlias||1||||||F|||20230627120856+0000
            OBX|2|NM|3623994^SpO2^GENESISAlias||2||||||F|||20230627120856+0000
            OBX|3|NM|703540^Respiratory Rate^GENESISAlias||3||||||F|||20230627120856+0000
            OBX|4|NM|703501^Systolic Blood Pressure^GENESISAlias||10||||||F|||20230627120856+0000
            OBX|5|NM|703516^Diastolic Blood Pressure^GENESISAlias||11||||||F|||20230627120856+0000
            OBX|6|NM|4247425^Numeric Pain Scale^GENESISAlias||6||||||F|||20230627120856+0000
            OBX|7|NM|2700544^End Tidal CO2^GENESISAlias^^End-tidal carbon dioxide (ETCO2)||7||||||F|||20230627120856+0000
            OBX|8|NM|2820736^Temperature (Route Not Specified)^GENESISAlias||-12.833333|deg c^Centigrade^CS54|||||F|||20230627120856+0000
            OBX|9|NM|103344685^Urine Output^GENESISAlias||12.0||||||F|||20230627120856+0000
            OBX|10|NM|703608^Capillary Refill^GENESISAlias||111.1||||||F|||20230627120856+0000
            OBX|11|NM|703584^Eye Opening Response Glasgow^GENESISAlias||222||||||F|||20230627120856+0000
            OBX|12|NM|703783^Motor Response, Estimated Glasgow^GENESISAlias||333||||||F|||20230627120856+0000
            OBX|13|NM|703786^Best Verbal Response Glasgow^GENESISAlias||444||||||F|||20230627120856+0000
            OBX|14|NM|703565^Glasgow Coma Score^GENESISAlias||999||||||F|||20230627120856+0000
            MSH|^~\&|BATDOK||CDP||zzz||ORU^R01|yyy|P|2.3
            ${pidSegment(hl7Data,EndpointType.TAC)}
            ${pv1Segment(hl7Data)}
            OBR|1|||VITALSIGNS|||20230627120937+0000|||||||||||||||20230627120937+0000|||F
            ZUI|1|${document.vitals.list[1].vitalId.asUuidString()}|UNKNOWN
            OBX|1|NM|703511^Peripheral Pulse Rate^GENESISAlias||555||||||F|||20230627120937+0000
            OBX|2|NM|4247425^Numeric Pain Scale^GENESISAlias||8||||||F|||20230627120937+0000
            OBX|3|NM|703584^Eye Opening Response Glasgow^GENESISAlias||9||||||F|||20230627120937+0000
            OBX|4|TX|703783^Motor Response, Estimated Glasgow^GENESISAlias||NT||||||F|||20230627120937+0000
            OBX|5|NM|703786^Best Verbal Response Glasgow^GENESISAlias||8||||||F|||20230627120937+0000
            OBX|6|NM|703565^Glasgow Coma Score^GENESISAlias||17||||||F|||20230627120937+0000
            MSH|^~\&|BATDOK||CDP||zzz||ORU^R01|yyy|P|2.3
            ${pidSegment(hl7Data,EndpointType.TAC)}
            ${pv1Segment(hl7Data)}
            OBR|1|||2921288^Basic Metabolic Panel^GENESISAlias|||ttt|||||||||||||||ttt||GLB|F
            ZUI|1|${document.id.hashedWith("Basic Metabolic Panel").asUuidString()}|UNKNOWN
            OBX|1|NM|102593623^Sodium^GENESISAlias||6.0||||||F|||20230627122108+0000
            OBX|2|NM|102598663^Potassium Lvl^GENESISAlias||7.0||||||F|||20230627122108+0000
            OBX|3|NM|102595417^Calcium^GENESISAlias||8.0||||||F|||20230627122108+0000
            MSH|^~\&|BATDOK||CDP||zzz||ORU^R01|yyy|P|2.3
            ${pidSegment(hl7Data,EndpointType.TAC)}
            ${pv1Segment(hl7Data)}
            OBR|1|||2921414^CBC w/ Diff^GENESISAlias|||ttt|||||||||||||||ttt||GLB|F
            ZUI|1|${document.id.hashedWith("CBC w/ Diff").asUuidString()}|UNKNOWN
            OBX|1|NM|102599089^Hgb^GENESISAlias||10.0||||||F|||20230627122108+0000
            OBX|2|NM|102606671^Hematocrit^GENESISAlias||11||||||F|||20230627122108+0000
            MSH|^~\&|BATDOK||CDP||zzz||ORU^R01|yyy|P|2.3
            ${pidSegment(hl7Data,EndpointType.TAC)}
            ${pv1Segment(hl7Data)}
            OBR|1|||79569-0^Blood product given [Type]^LOINC|||20230627122108+0000|||||||||||||||20230627122108+0000|||F
            ZUI|1|${document.bloodList.list[0].bloodId.asUuidString()}|UNKNOWN
            OBX|1|NM|E0112^Whole blood (WB)^ISBT128||500|ml^Milliliters^CS54|||||F|||20230627122108+0000
            OBX|2|TX|933-2^Blood product type^LOINC||AB+||||||F|||20230627122108+0000
            MSH|^~\&|BATDOK||CDP||zzz||ORU^R01|yyy|P|2.3
            ${pidSegment(hl7Data,EndpointType.TAC)}
            ${pv1Segment(hl7Data)}
            OBR|1|||79569-0^Blood product given [Type]^LOINC|||20230627122108+0000|||||||||||||||20230627122108+0000|||F
            ZUI|1|${document.bloodList.list[1].bloodId.asUuidString()}|UNKNOWN
            OBX|1|NM|1^Fresh^BLOOD||500|ml^Milliliters^CS54|||||F|||20230627122108+0000
            OBX|2|TX|933-2^Blood product type^LOINC||O+||||||F|||20230627122108+0000
            MSH|^~\&|BATDOK||CDP||zzz||ORU^R01|yyy|P|2.3
            ${pidSegment(hl7Data,EndpointType.TAC)}
            ${pv1Segment(hl7Data)}
            OBR|1|||2820561^Transfer Note^GENESISAlias^^BATDOK R1 to R3 Note|||ttt|||||||||||||||ttt|||F
            ZUI|1|${document.id.hashedWith("allnotes").asUuidString()}|UNKNOWN
            OBX|1|TX|2820561^Transfer Note^GENESISAlias||test note sub (subjective)||||||F|||20230627121733+0000
            OBX|2|TX|2820561^Transfer Note^GENESISAlias||FAST Exam Result - Positive FAST: Suspected hemoperitoneum||||||F|||20230627121733+0000
            OBX|3|TX|2820561^Transfer Note^GENESISAlias||test note plan (plan)||||||F|||20230627121744+0000
            OBX|4|TX|2820561^Transfer Note^GENESISAlias||event linked to complaint *** (plan)||||||F|||20230627121744+0000
            OBX|5|TX|2820561^Transfer Note^GENESISAlias||Assessment linked to complaint *** (assessment)||||||F|||20230627121744+0000
            MSH|^~\&|BATDOK||CDP||zzz||ORU^R01|yyy|P|2.3
            ${pidSegment(hl7Data,EndpointType.TAC)}
            ${pv1Segment(hl7Data)}
            OBR|1|||2820561^Transfer Note^GENESISAlias^^BATDOK Order|||20230627122108+0000|||||||||||||||20230627122108+0000||MDOC|F
            ZUI|1|${document.events.list.filter { it.eventType == KnownEventTypes.ORDERS.dataString }.first().id.asUuidString()}|UNKNOWN
            OBX|1|TX|2820561^Transfer Note^GENESISAlias||Added Order:||||||F|||20230627122108+0000
            OBX|2|TX|2820561^Transfer Note^GENESISAlias||Title: Administer Action 1||||||F|||20230627122108+0000
            OBX|3|TX|2820561^Transfer Note^GENESISAlias||Instructions: When pain is greater than 7||||||F|||20230627122108+0000
            OBX|4|TX|2820561^Transfer Note^GENESISAlias||Frequency: Per Instruction||||||F|||20230627122108+0000
            OBX|5|TX|2820561^Transfer Note^GENESISAlias||Provider: Dr. Professorson||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.TAC)}||CDP||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.TAC)}|${msh12VersionIDCode(Endpoint.TAC)}
            ${pidSegment(hl7Data,EndpointType.TAC)}
            ${pv1Segment(hl7Data)}
            OBR|1|||2820561^Transfer Note^GENESISAlias^^BATDOK Order|||20230627122108+0000|||||||||||||||20230627122108+0000||MDOC|F
            ZUI|1|${document.events.list.filter { it.eventType == KnownEventTypes.ORDERS.dataString }[1].id.asUuidString()}|UNKNOWN
            OBX|1|TX|2820561^Transfer Note^GENESISAlias||Added Order:||||||F|||20230627122108+0000
            OBX|2|TX|2820561^Transfer Note^GENESISAlias||Title: Administer Action 2||||||F|||20230627122108+0000
            OBX|3|TX|2820561^Transfer Note^GENESISAlias||Instructions: When Eli makes the order||||||F|||20230627122108+0000
            OBX|4|TX|2820561^Transfer Note^GENESISAlias||Frequency: Per Graham||||||F|||20230627122108+0000
            OBX|5|TX|2820561^Transfer Note^GENESISAlias||Provider: Dr. Ezratty||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.TAC)}||CDP||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.TAC)}|${msh12VersionIDCode(Endpoint.TAC)}
            ${pidSegment(hl7Data,EndpointType.TAC)}
            ${pv1Segment(hl7Data)}
            OBR|1|||2820561^Transfer Note^GENESISAlias^^BATDOK Order|||20230627122108+0000|||||||||||||||20230627122108+0000||MDOC|F
            ZUI|1|${document.events.list.filter { it.eventType == KnownEventTypes.ORDERS.dataString }[2].id.asUuidString()}|UNKNOWN
            OBX|1|TX|2820561^Transfer Note^GENESISAlias||Added Custom Order:||||||F|||20230627122108+0000
            OBX|2|TX|2820561^Transfer Note^GENESISAlias||Title: 0.9% Sodium Chloride, IM, 5 gm||||||F|||20230627122108+0000
            OBX|3|TX|2820561^Transfer Note^GENESISAlias||Instructions: When pain is greater than 7||||||F|||20230627122108+0000
            OBX|4|TX|2820561^Transfer Note^GENESISAlias||Frequency: Per Instruction||||||F|||20230627122108+0000
            OBX|5|TX|2820561^Transfer Note^GENESISAlias||Provider: Dr. Professorson||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.TAC)}||CDP||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.TAC)}|${msh12VersionIDCode(Endpoint.TAC)}
            ${pidSegment(hl7Data,EndpointType.TAC)}
            ${pv1Segment(hl7Data)}
            OBR|1|||2820561^Transfer Note^GENESISAlias^^BATDOK Order Administration and Notation|||20230627122108+0000|||||||||||||||20230627122108+0000||MDOC|F
            ZUI|1|${document.notes.list[0].id.asUuidString()}|UNKNOWN
            OBX|1|TX|2820561^Transfer Note^GENESISAlias||Reference Order: 0.9% Sodium Chloride, IM, 5 gm||||||F|||20230627122108+0000
            OBX|2|TX|2820561^Transfer Note^GENESISAlias||Added Note:||||||F|||20230627122108+0000
            OBX|3|TX|2820561^Transfer Note^GENESISAlias||message2||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.TAC)}||CDP||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.TAC)}|${msh12VersionIDCode(Endpoint.TAC)}
            ${pidSegment(hl7Data,EndpointType.TAC)}
            ${pv1Segment(hl7Data)}
            OBR|1|||2820561^Transfer Note^GENESISAlias^^BATDOK Order Administration and Notation|||20230627122108+0000|||||||||||||||20230627122108+0000||MDOC|F
            ZUI|1|${document.notes.list[1].id.asUuidString()}|UNKNOWN
            OBX|1|TX|2820561^Transfer Note^GENESISAlias||Reference Order: Administer Action 2||||||F|||20230627122108+0000
            OBX|2|TX|2820561^Transfer Note^GENESISAlias||Added Note:||||||F|||20230627122108+0000
            OBX|3|TX|2820561^Transfer Note^GENESISAlias||*******MESSAGE*******||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.TAC)}||CDP||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.TAC)}|${msh12VersionIDCode(Endpoint.TAC)}
            ${pidSegment(hl7Data,EndpointType.TAC)}
            ${pv1Segment(hl7Data)}
            OBR|1|||2820561^Transfer Note^GENESISAlias^^BATDOK Order Administration and Notation|||ttt|||||||||||||||ttt||MDOC|F
            ZUI|1|${document.customActions.list.first().id.asUuidString()}|UNKNOWN
            OBX|1|TX|2820561^Transfer Note^GENESISAlias||Reference Order: Administer Action 2||||||F|||ttt
            OBX|2|TX|2820561^Transfer Note^GENESISAlias||Added Action:||||||F|||ttt
            OBX|3|TX|2820561^Transfer Note^GENESISAlias||message||||||F|||ttt
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.TAC)}||CDP||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.TAC)}|${msh12VersionIDCode(Endpoint.TAC)}
            ${pidSegment(hl7Data,EndpointType.TAC)}
            ${pv1Segment(hl7Data)}
            OBR|1|||2820561^Transfer Note^GENESISAlias^^BATDOK Fluid Log|||20230627122108+0000|||||||||||||||20230627122108+0000||MDOC|F
            ZUI|1|${document.intakeOutputs.list.first().id.asUuidString()}|UNKNOWN
            OBX|1|TX|2820561^Transfer Note^GENESISAlias||Logged Intake:||||||F|||20230627122108+0000
            OBX|2|TX|2820561^Transfer Note^GENESISAlias||Oral Fluid 100.0 mL water||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.TAC)}||CDP||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.TAC)}|${msh12VersionIDCode(Endpoint.TAC)}
            ${pidSegment(hl7Data,EndpointType.TAC)}
            ${pv1Segment(hl7Data)}
            OBR|1|||2820561^Transfer Note^GENESISAlias^^BATDOK Fluid Log|||20230627122108+0000|||||||||||||||20230627122108+0000||MDOC|F
            ZUI|1|${document.intakeOutputs.list[1].id.asUuidString()}|UNKNOWN
            OBX|1|TX|2820561^Transfer Note^GENESISAlias||Logged Intake:||||||F|||20230627122108+0000
            OBX|2|TX|2820561^Transfer Note^GENESISAlias||Parenteral Fluid 200.0 mL saline||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.TAC)}||CDP||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.TAC)}|${msh12VersionIDCode(Endpoint.TAC)}
            ${pidSegment(hl7Data,EndpointType.TAC)}
            ${pv1Segment(hl7Data)}
            OBR|1|||2820561^Transfer Note^GENESISAlias^^BATDOK Fluid Log|||20230627122108+0000|||||||||||||||20230627122108+0000||MDOC|F
            ZUI|1|${document.intakeOutputs.list[2].id.asUuidString()}|UNKNOWN
            OBX|1|TX|2820561^Transfer Note^GENESISAlias||Logged Output:||||||F|||20230627122108+0000
            OBX|2|TX|2820561^Transfer Note^GENESISAlias||Urine 300.0 mL||||||F|||20230627122108+0000
        """.trimIndent().replace("\n", "\r")

        // Get current time from the message (this always generates with the current time here)
        val noteTime = result.substringAfter("2820561^Transfer Note^GENESISAlias^^BATDOK R1 to R3 Note|||")  // After note tag
            .substringBefore('\r')  // Before newline
            .substringBefore("||||")  // Before the empty pipes
        val cleanResult = result.replace(noteTime, "ttt")

        Assert.assertEquals(expected, cleanResult)

        // Make sure the time in the note is relatively recent (within last 3 seconds)
        val tsHandler = CommonTS()
        tsHandler.value = noteTime
        Assert.assertTrue(tsHandler.valueAsDate.time > Date().time - 3000)
    }

    override fun testORUZB2(){
        //ARRANGE
        val document = hl7Data.getDocumentForEncounter(hl7Data.activeEncounterId!!)
        //ACT
        val result = ORU_ZB2Builder(Endpoint.TAC).populateAll(hl7Data).build().toResult(Endpoint.TAC,false, true)

        //ASSERT
        val expected = """
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.TAC)}||${msh05ReceiveApplicationCode(Endpoint.TAC)}|${msh06ReceivingFacilityCode(Endpoint.TAC)}|zzz||ORU^ZB2|yyy|${msh11ProcessingIDCode(Endpoint.TAC)}|${msh12VersionIDCode(Endpoint.TAC)}
            ${pidSegment(hl7Data,EndpointType.TAC)}
            ${pv1Segment(hl7Data)}
            OBR|1|||1^Blood products^LOINC|||ttt|||||||||||||||ttt||BB|F
            NTE|1||test|47
            ZUI|1|${document!!.id.hashedWith("blood").asUuidString()}|UNKNOWN
            ZBP|1|123456|654321|55555|Fresh Whole Blood(FWB)^description^alt description|TRANSFUSED|20241118143430|20241218143430|O|Rh-Pos|||Tacos^^147258369||||N|500|300|100|1||mLs
        """.trimIndent().replace("\n", "\r")
        val cleanResult = result.lines().joinToString("\r") { line ->
            if (line.startsWith("OBR|")){
                line.replace(Regex("""\d{14}\+\d{4}"""), "ttt")
            }else {
                line
            }
        }

        Assert.assertEquals(expected, cleanResult)
    }

    override fun testORMO01() {
        // ARRANGE
        // Add a sample extra med in to verify it gets meds from the last 12 hours
        val med = Medicine("Ertapenem", ndc = null, administrationTime = Instant.now(),
            route = "IV", volume = 1.0f, unit = "gm", type = "Analgesic")
        val existingCommands = hl7Data.commandsByEncounter[hl7Data.activeEncounterId].orEmpty()
        hl7Data.commandsByEncounter += (hl7Data.activeEncounterId!! to (existingCommands + buildCommandData(
            buildLogMedicineCommand(med)
        )))
        val document = hl7Data.getDocumentForEncounter(hl7Data.activeEncounterId!!)


        MedicationConverter.map.putAll(mapOf(
            buildMapEntry("0.9% Sodium Chloride", "1159317", RxNorm),
            buildMapEntry("Ertapenem", "325642", RxNorm),
            buildMapEntry("Acetaminophen", "161", RxNorm),
            buildMapEntry("Fresh blood","","")
        ))

        UnitConverter.map.putAll(mapOf(
            buildMapEntry("g", "Gram", CS54),
            buildMapEntry("mg", "Milligrams", CS54),
        ))

        RouteConverter.map.putAll(mapOf(
            buildMapEntry("IM", "IntraMuscular", DHMSM_ICD),
            buildMapEntry("NS", "Nostril-Both", DHMSM_ICD),
            buildMapEntry("IVPU", "IV Push", DHMSM_ICD),
            buildMapEntry("PO", "Oral", DHMSM_ICD),
        ))

        // ACT
        val result = ORM_O01Builder(Endpoint.TAC).populateAll(hl7Data).joinToString("\r") { it.toResult(Endpoint.TAC, hasEvn = false, true) }

        // ASSERT
        val expected = """
            MSH|^~\&|BATDOK||CDP||zzz||ORM^O01|yyy|P|2.3
            ${pidSegment(hl7Data,EndpointType.TAC)}
            ${pv1Segment(hl7Data)}
            ORC|NW|RE^^${document!!.medicines.list.last().id.asUuidString()}|||CM||||${TimeConverter.timestampToFormattedDateTime(med.administrationTime)}
            RXO|325642^Ertapenem^RxNorm|1.0||g^Gram
            RXR|IVPU^IV Push||||Medication
        """.trimIndent().replace("\n", "\r")
        Assert.assertEquals(expected, result)
    }

    override fun testADTA03() {
        // ARRANGE

        // ACT
        val result = ADT_A03Builder(Endpoint.TAC).populateAll(hl7Data).build().toResult(Endpoint.TAC, hasPv1Discharge = true)

        // ASSERT
        val expected = """
            MSH|^~\&|BATDOK||CDP||zzz||ADT^A03|yyy|P|2.3
            ${evnSegment("A03")}
            ${pidSegment(hl7Data,EndpointType.TAC)}
            ${pv1Segment(hl7Data)}
        """.trimIndent().replace("\n", "\r")
        Assert.assertEquals(expected, result)
    }
}
