package mil.af.afrl.batman.hl7lib.inbound.procedure

import gov.afrl.batdok.encounter.observation.CommonObservations
import gov.afrl.batdok.encounter.observation.PupilDilationData
import gov.afrl.batdok.encounter.observation.CasualtyPPEData
import gov.afrl.batdok.encounter.observation.ChestRiseFallData
import gov.afrl.batdok.encounter.observation.TextData
import gov.afrl.batdok.encounter.treatment.ChestSealData
import gov.afrl.batdok.encounter.treatment.ChestTubeData
import gov.afrl.batdok.encounter.treatment.CommonTreatments
import gov.afrl.batdok.encounter.treatment.DressingData
import gov.afrl.batdok.encounter.treatment.EyeShieldData
import gov.afrl.batdok.encounter.treatment.FingerThorData
import gov.afrl.batdok.encounter.treatment.HypothermiaPreventionData
import gov.afrl.batdok.encounter.treatment.FoleyCatheterData
import gov.afrl.batdok.encounter.treatment.GastricTubeData
import gov.afrl.batdok.encounter.treatment.ImmobilizationData
import gov.afrl.batdok.encounter.treatment.NeedleDData
import gov.afrl.batdok.encounter.treatment.O2Data
import gov.afrl.batdok.encounter.treatment.PelvicBinderData
import gov.afrl.batdok.encounter.treatment.PericardiocentesisData
import gov.afrl.batdok.encounter.treatment.TqData
import gov.afrl.batdok.encounter.treatment.TubeData
import gov.afrl.batdok.encounter.treatment.WoundPackingData
import kotlinx.coroutines.test.runTest
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.message.MessageReceiver
import org.junit.Assert
import org.junit.Test
import java.io.ByteArrayInputStream

class CdpTreatmentIngestTest {

    @Test
    fun testLines() = runTest {
        // ARRANGE
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20240913131503+0000||ADT^A28|52590246-3f7e-49e7-8a15-73750ec012aa|T|2.3|
            EVN|A28|20240913131503+0000|
            PID|1||^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI|6CAB3872-FCB8-4274-BB6E-710A73DD04EF|treatments^test^||19891112|M||||||||||FIN NBR||||||||||
            PR1|1||440009003^Insertion of needle for intraosseous infusion (procedure)^SCT||20250110134527+0000
            OBX|1|TX|1303680005^Structure of head of left humerus (body structure)^SCT|1|Left Humerus||||||F|||20250110134527+0000
            PR1|2||233527006^Central venous cannula insertion (procedure)^SCT|Line (Type: Central, SubType: Triple Lumen, Location: Left Fem)|20250110140247+0000
            OBX|1|TX|85119005^Left inguinal region structure (body structure)^SCT|1|Left Fem||||||F|||20250110140247+0000
            OBX|2|TX|397970004^Triple lumen catheter (physical object)^SCT|2|Triple Lumen||||||F|||20250110140247+0000
            PR1|3||440009003^Insertion of needle for intraosseous infusion (procedure)^SCT|Line (Type: IO, SubType: EZ-IO, Location: Sternum, Size: 5 mm)|20250117144349+0000
            OBX|1|TX|56873002^Bone structure of sternum (body structure)^SCT|1|Sternum||||||F|||20250117144349+0000
            OBX|2|NM|410667008^Length (attribute)^SCT|2|5.0|mm^mm^CS54|||||F|||20250117144349+0000
            ZPI|1||||||||||||||HUMAN
        """.trimIndent().replace("\n", "\r")

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        val treatments = document.treatments.list
        Assert.assertEquals(3, treatments.size)
        Assert.assertEquals("Line", treatments[0].name)
        Assert.assertNotNull(treatments[0].treatmentData)
        Assert.assertEquals("Type: IO, Location: Left Humerus", treatments[0].treatmentData?.toDetailString())
        // If NPA shows up here instead of code text, we matched the code correctly
        Assert.assertEquals("Line", treatments[1].name)
        Assert.assertNotNull(treatments[1].treatmentData)
        Assert.assertEquals("Type: Central, SubType: Triple Lumen, Location: Left Fem", treatments[1].treatmentData?.toDetailString())
        //size example
        Assert.assertEquals("Line", treatments[2].name)
        Assert.assertNotNull(treatments[2].treatmentData)
        Assert.assertEquals("Type: IO, Location: Sternum, Size: 5 mm", treatments[2].treatmentData?.toDetailString())
    }

    @Test
    fun testIngestTq() = runTest {
        // ARRANGE
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20250122195042+0000||ADT^A28|91e9dc92-3ede-4c69-902e-d7deef9871bb|T|2.3
            EVN|A28|20250122195042+0000
            PID|1||^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI|B133AD63-AA86-4C6E-B808-E6311537E625|UT-BADAA^YWAS^||19000101|M||||||||||FIN NBR||||||||||
            PR1|1||20655006^Tourniquet^SCT||20250122195025+0000|P
            OBX|1|ST|368209003^Tourniquet location^SCT||Right arm
            PR1|2||20655006^Tourniquet^SCT||20250122195029+0000|P
            OBX|1|ST|50974003^Tourniquet location^SCT||Junctional
            OBX|2|ST|19654004^Junctional tourniqet location^SCT||Right axilla
            OBX|3|ST|373066001^Tourniquet effective^SCT||Yes
            PR1|3||20655006^Tourniquet^SCT||20250122195036+0000|P
            OBX|1|ST|373066001^Tourniquet effective^SCT||Yes
            OBX|2|ST|22943007^Tourniquet location^SCT||Truncal
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals(3, document.treatments.size)
        for (treat in document.treatments.list) {
            Assert.assertEquals(CommonTreatments.TQ.dataString, treat.name)
            Assert.assertTrue(treat.treatmentData is TqData)
        }
        val tqDatas = document.treatments.list.map { it.treatmentData as TqData }
        Assert.assertEquals(TqData.Location.EXTREMITY.dataString, tqDatas[0].tqLocation)
        Assert.assertEquals(TqData.SubLocation.RUE.dataString, tqDatas[0].subLocation)
        Assert.assertEquals(TqData.Location.JUNCTIONAL.dataString, tqDatas[1].tqLocation)
        Assert.assertEquals(TqData.Location.TRUNCAL.dataString, tqDatas[2].tqLocation)
    }

    @Test
    fun testIngestChestTube() = runTest {
        // ARRANGE
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20250123143644+0000||ADT^A28|7a8c17d1-0789-4bab-9f5b-d5f638b5fafd|T|2.3
            EVN|A28|20250123143644+0000
            PID|1||^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI|C55EF805-0F33-4273-967A-4324B8AE7E89|chestTube^test^||19891112|M||||||||||FIN NBR||||||||||
            PR1|1||446847002^Breathing interventions^SCT||20250123143633+0000|P
            OBX|1|ST|91199000^Axillary^SNOMED CT|1|Right anterior axillary
            OBX|2|ST|24028007^Right^SNOMED CT|1|Right anterior axillary
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals(1, document.treatments.size)
        for (treat in document.treatments.list) {
            Assert.assertEquals(CommonTreatments.CHEST_TUBE.dataString, treat.name)
            Assert.assertTrue(treat.treatmentData is ChestTubeData)
        }
        val chestTubeDatas = document.treatments.list.map { it.treatmentData as ChestTubeData }
        Assert.assertEquals(ChestTubeData.Location.RIGHT.dataString, chestTubeDatas[0].location)
    }

    @Test
    fun testIngestEtt() = runTest {
        // ARRANGE
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20250123140805+0000||ADT^A28|6e7c9798-aa9f-4d9e-a6be-a69359e2585b|T|2.3
            EVN|A28|20250123140805+0000
            PID|1||^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI|82174DFE-A774-4C87-BBFF-9EF6F1CB29E3|UT-DFEAC^OILI^||19000101|M||||||||||FIN NBR||||||||||
            PR1|1||112798008^Airway intervention^SCT||20250123140734+0000|P
            OBX|1|NM|246115007^ETT size^SCT||7
            OBX|2|NM|131197000^ETT depth at teeth^SCT||7
            OBX|3|ST|250784008^Airway verified method^SCT||EtCO2
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals(1, document.treatments.size)
        val treat = document.treatments.list[0]
        Assert.assertEquals(CommonTreatments.ET_TUBE.dataString, treat.name)
        Assert.assertTrue(treat.treatmentData is TubeData)
        Assert.assertEquals(7, (treat.treatmentData as TubeData).depth)
    }

    @Test
    fun testIngestO2() = runTest {
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20250129200430+0000||ADT^A28|de373cf9-abd9-43ce-9245-b4dc867aac81|T|2.3
            EVN|A28|20250129200430+0000
            PID|1|||648A9EAE-635A-40DC-AD8E-A7A53C51D089^^^2.16.840.1.113883.3.42.10032.100001.13^TAC|UT-AEAEA^TINT||19000101|M
            PR1|1||336623009^Oxygen^SCT||20250129200336+0000|P
            PR1|2||427591007^Oxygen^SCT||20250129200350+0000|P
            PR1|3||425696007^Oxygen^SCT||20250129200403+0000|P
            PR1|4||1258985005^Oxygen^SCT||20250129200424+0000|P
            OBX|1|ST|2796635^Ventilator mode^72|1|APRV
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals(4, document.treatments.size)
        val treatments = document.treatments.list
        Assert.assertEquals(CommonTreatments.O2.dataString, treatments[0].name)
        Assert.assertTrue(treatments[0].treatmentData is O2Data)
        Assert.assertEquals(O2Data.DeliveryMethod.NC.dataString, treatments[0].getData<O2Data>()?.deliveryMethod)

        Assert.assertEquals(CommonTreatments.O2.dataString, treatments[1].name)
        Assert.assertTrue(treatments[1].treatmentData is O2Data)
        Assert.assertEquals(O2Data.DeliveryMethod.NRB.dataString, treatments[1].getData<O2Data>()?.deliveryMethod)

        Assert.assertEquals(CommonTreatments.O2.dataString, treatments[2].name)
        Assert.assertTrue(treatments[2].treatmentData is O2Data)
        Assert.assertEquals(O2Data.DeliveryMethod.BVM.dataString, treatments[2].getData<O2Data>()?.deliveryMethod)

        Assert.assertEquals(CommonTreatments.O2.dataString, treatments[3].name)
        Assert.assertTrue(treatments[3].treatmentData is O2Data)
        Assert.assertEquals(O2Data.DeliveryMethod.VENT.dataString, treatments[3].getData<O2Data>()?.deliveryMethod)
    }

    @Test
    fun testIngestDressing() = runTest {
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20250205144031+0000||ADT^A28|472a48ac-6a54-41d9-adbd-dd8b5259a48d|T|2.3
            EVN|A28|20250205144031+0000
            PID|1|||6AD2C713-3F04-4D44-B9BC-E6C650DCF7FC^^^2.16.840.1.113883.3.42.10032.100001.13^TAC|asd^test||19000101|M
            PR1|1||3895009^Dressing^SCT||20250205144005+0000|P
            OBX|1|ST|261365000^Hemostatic agent^SCT|1|Yes
            OBX|2|ST|1481000124102^Dressing site^SCT|1|dressing location
            PR1|2||118414001^Pressure dressing^SCT||20250205144023+0000|P
            OBX|1|ST|1481000124102^Dressing site^SCT|1|pd location
            PR1|3||3895009^Dressing^SCT||20250205144005+0000|P
            OBX|1|ST|1481000124102^Dressing site^SCT|1|more locations
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals(3, document.treatments.size)
        val treatments = document.treatments.list
        Assert.assertEquals(CommonTreatments.DRESSING.dataString, treatments[0].name)
        Assert.assertTrue(treatments[0].treatmentData is DressingData)
        Assert.assertEquals(DressingData.Type.HEMOSTATIC.dataString, treatments[0].getData<DressingData>()?.type)
        Assert.assertEquals("dressing location", treatments[0].getData<DressingData>()?.location)

        Assert.assertEquals(CommonTreatments.DRESSING.dataString, treatments[1].name)
        Assert.assertTrue(treatments[1].treatmentData is DressingData)
        Assert.assertEquals(DressingData.Type.NONHEMOSTATIC.dataString, treatments[1].getData<DressingData>()?.type)
        Assert.assertEquals("more locations", treatments[1].getData<DressingData>()?.location)

        Assert.assertEquals(CommonTreatments.DRESSING.dataString, treatments[2].name)
        Assert.assertTrue(treatments[2].treatmentData is DressingData)
        Assert.assertEquals(DressingData.Type.PRESSURE.dataString, treatments[2].getData<DressingData>()?.type)
        Assert.assertEquals("pd location", treatments[2].getData<DressingData>()?.location)
    }

    @Test
    fun testIngestWoundPacking() = runTest {
        // ARRANGE
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20250312121832+0000||ADT^A28|3509bdea-b61f-4615-ba2e-0b065777bbb4|T|2.3
            EVN|A28|20250312121832+0000
            PID|1|||EBD1946F-9964-4956-8E92-EC7E385A195E^^^2.16.840.1.113883.3.42.10032.100001.13^TAC|UT-EBDFE^ALAA||19360312|M
            PR1|1||241019003^Wound packing^SCT||20250312121818+0000|P
            OBX|1|ST|1481000124102^Dressing site^SCT|1|site
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        val treatments = document.treatments.list
        Assert.assertEquals(1, treatments.size)
        Assert.assertEquals(CommonTreatments.WOUND_PACKING.dataString, treatments[0].name)
        Assert.assertTrue(treatments[0].treatmentData is WoundPackingData)
        Assert.assertEquals("site", treatments[0].getData<WoundPackingData>()?.location)
    }

    @Test
    fun testIngestEyeShield() = runTest {
        // TODO replace message with real message when cdp adds it
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20250205144031+0000||ADT^A28|472a48ac-6a54-41d9-adbd-dd8b5259a48d|T|2.3
            EVN|A28|20250205144031+0000
            PID|1|||6AD2C713-3F04-4D44-B9BC-E6C650DCF7FC^^^2.16.840.1.113883.3.42.10032.100001.13^TAC|asd^test||19000101|M
            PR1|1||225696009^Applying eye shield (procedure)^SCT||20230627121501+0000
            OBX|1|TX|51440002^Right and left (qualifier value)^SCT|1|Both||||||F|||20230627121501+0000
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals(1, document.treatments.size)
        val treatments = document.treatments.list
        Assert.assertEquals(CommonTreatments.EYE_SHIELD.dataString, treatments[0].name)
        Assert.assertTrue(treatments[0].treatmentData is EyeShieldData)
        treatments[0].getData<EyeShieldData>()?.left?.let { Assert.assertTrue(it) }
        treatments[0].getData<EyeShieldData>()?.right?.let { Assert.assertTrue(it) }
    }

    @Test
    fun testIngestHypothermiaPrevention() = runTest {
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20250221203012+0000||ADT^A28|cb3d9667-b7b2-42d3-9e36-ed44cbcc3ff9|T|2.3
            EVN|A28|20250221203012+0000
            PID|1|||750E31DA-9700-4259-B901-FF0407160E5C^^^2.16.840.1.113883.3.42.10032.100001.13^TAC|test^blanket||19891112|M
            PR1|1||19328000^Warming^SCT||20250221195857+0000|P
            PR1|2||309433007^Warming^SCT||20250221202949+0000|P
            OBX|1|TX|74964007^Other (qualifier value)^SCT|1|HPMK||||||F|||20250224153605+0000
            PR1|3||309433007^Warming^SCT||20250221202956+0000|P
            OBX|1|TX|74964007^Other (qualifier value)^SCT|1|test||||||F|||20250224153605+0000
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals(3, document.treatments.size)
        val treatments = document.treatments.list
        Assert.assertEquals(CommonTreatments.HYPOTHERMIA_PREVENTION.dataString, treatments[0].name)
        Assert.assertTrue(treatments[0].treatmentData is HypothermiaPreventionData)
        Assert.assertEquals(HypothermiaPreventionData.Type.BLANKET.dataString,treatments[0].getData<HypothermiaPreventionData>()?.type)
        Assert.assertEquals(CommonTreatments.HYPOTHERMIA_PREVENTION.dataString, treatments[1].name)
        Assert.assertTrue(treatments[1].treatmentData is HypothermiaPreventionData)
        Assert.assertEquals("HPMK",treatments[1].getData<HypothermiaPreventionData>()?.type)
        Assert.assertEquals(CommonTreatments.HYPOTHERMIA_PREVENTION.dataString, treatments[2].name)
        Assert.assertTrue(treatments[2].treatmentData is HypothermiaPreventionData)
        Assert.assertEquals("test",treatments[2].getData<HypothermiaPreventionData>()?.type)
    }

    @Test
    fun testIngestPupils() = runTest {
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20250221203012+0000||ADT^A28|cb3d9667-b7b2-42d3-9e36-ed44cbcc3ff9|T|2.3
            EVN|A28|20250221203012+0000
            PID|1|||750E31DA-9700-4259-B901-FF0407160E5C^^^2.16.840.1.113883.3.42.10032.100001.13^TAC|test^blanket||19891112|M
            PR1|1||363953003^Size of pupil (observable entity)^SCT|Pupil Dilation (Left pupil dilated 6mm, Right pupil dilated 8mm)|20250224185745+0000|D
            OBX|1|NM|16089004^Structure of pupil of left eye (body structure)^SCT|1|6|mm^mm^CS54|||||F|||20250224185745+0000
            OBX|2|NM|52378001^Structure of pupil of right eye (body structure)^SCT|2|8|mm^mm^CS54|||||F|||20250224185745+0000
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals(1, document.observations.size)
        val observations = document.observations.list
        Assert.assertEquals(CommonObservations.PUPIL_DILATION.dataString, observations[0].name)
        Assert.assertTrue(observations[0].observationData is PupilDilationData)
        Assert.assertEquals(6,observations[0].getData<PupilDilationData>()?.perrlaSizeLeft)
        Assert.assertEquals(8,observations[0].getData<PupilDilationData>()?.perrlaSizeRight)
       
    }

    @Test
    fun testNoObx() = runTest {
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20250221203012+0000||ADT^A28|cb3d9667-b7b2-42d3-9e36-ed44cbcc3ff9|T|2.3
            EVN|A28|20250221203012+0000
            PID|1|||750E31DA-9700-4259-B901-FF0407160E5C^^^2.16.840.1.113883.3.42.10032.100001.13^TAC|test^blanket||19891112|M
            PR1|1||363953003^Size of pupil (observable entity)^SCT||20250224185745+0000|D
            PR1|2||225696009^Applying eye shield (procedure)^SCT||20230627121501+0000
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals(1, document.observations.size)
        Assert.assertEquals(1, document.treatments.size)
        
        val observations = document.observations.list
        val treatments = document.treatments.list
        Assert.assertEquals(CommonObservations.PUPIL_DILATION.dataString, observations[0].name)
        Assert.assertTrue(observations[0].observationData is PupilDilationData)
        Assert.assertEquals(CommonTreatments.EYE_SHIELD.dataString, treatments[0].name)
    }

    //plus tab
    @Test
    fun testIngestCasualtyPPE() = runTest {
        // todo replace with a real cdp message
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20250205144031+0000||ADT^A28|472a48ac-6a54-41d9-adbd-dd8b5259a48d|T|2.3
            EVN|A28|20250205144031+0000
            PID|1|||6AD2C713-3F04-4D44-B9BC-E6C650DCF7FC^^^2.16.840.1.113883.3.42.10032.100001.13^TAC|asd^test||19000101|M
            PR1|1||449399005^Application of personal protective equipment (procedure)^SCT|Casualty PPE (Helmet, Ballistic, Blast Gauge)|20230627122108+0000
            OBX|1|TX|285695004^Helmet (physical object)^SCT|1|Helmet, Ballistic||||||F|||20230627122108+0000
            OBX|2|TX|BG^Blast Gauge^SCT|2|Blast Gauge||||||F|||20230627122108+0000
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals(1, document.observations.size)
        val observations = document.observations.list
        Assert.assertEquals(CommonObservations.CASUALTY_PPE.dataString, observations[0].name)
        Assert.assertTrue(observations[0].observationData is CasualtyPPEData)
        Assert.assertEquals(CasualtyPPEData.Type.HELMET_BALLISTIC.dataString,
            observations[0].getData<CasualtyPPEData>()?.ppeList?.get(0) ?: "")
        Assert.assertEquals(CasualtyPPEData.Type.BLAST_GAUGE.dataString,
            observations[0].getData<CasualtyPPEData>()?.ppeList?.get(1) ?: "")
    }

    @Test
    fun testIngestFoley() = runTest {
        // todo replace with a real cdp message
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20250205144031+0000||ADT^A28|472a48ac-6a54-41d9-adbd-dd8b5259a48d|T|2.3
            EVN|A28|20250205144031+0000
            PID|1|||6AD2C713-3F04-4D44-B9BC-E6C650DCF7FC^^^2.16.840.1.113883.3.42.10032.100001.13^TAC|asd^test||19000101|M
            PR1|1||73368009^Foley catheter (physical object)^SCT|Foley Catheter (Size: 25, Type: Condom)|20230627121501+0000
            OBX|1|TX|1230041002^Application of incontinence sheath (procedure)^SCT|1|Condom||||||F|||20230627121501+0000
            OBX|2|NM|260967009^Size of catheter (qualifier value)^SCT|2|25.0||||||F|||20230627121501+0000
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals(1, document.treatments.size)
        val treatments = document.treatments.list
        Assert.assertEquals(CommonTreatments.FOLEY_CATHETER.dataString, treatments[0].name)
        Assert.assertTrue(treatments[0].treatmentData is FoleyCatheterData)
        Assert.assertEquals("Condom", treatments[0].getData<FoleyCatheterData>()?.color)
        Assert.assertEquals(25f, treatments[0].getData<FoleyCatheterData>()?.size)
    }

    @Test
    fun testIngestVisualAcuityTest() = runTest {
        // todo replace with a real cdp message
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20250205144031+0000||ADT^A28|472a48ac-6a54-41d9-adbd-dd8b5259a48d|T|2.3
            EVN|A28|20250205144031+0000
            PID|1|||6AD2C713-3F04-4D44-B9BC-E6C650DCF7FC^^^2.16.840.1.113883.3.42.10032.100001.13^TAC|asd^test||19000101|M
            PR1|1||16830007^Visual acuity testing (procedure)^SCT|Visual Acuity Test (test)|20230627122108+0000
            OBX|1|TX|260246004^Visual acuity finding (finding)^SCT|1|test||||||F|||20230627122108+0000
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals(1, document.observations.size)
        val observations = document.observations.list
        Assert.assertEquals("Visual Acuity Test", observations[0].name)
        Assert.assertTrue(observations[0].observationData is TextData)
        Assert.assertEquals("test", observations[0].getData<TextData>()?.description)
    }

    @Test
    fun testIngestGastricTube() = runTest {
        // todo replace with a real cdp message
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20250205144031+0000||ADT^A28|472a48ac-6a54-41d9-adbd-dd8b5259a48d|T|2.3
            EVN|A28|20250205144031+0000
            PID|1|||6AD2C713-3F04-4D44-B9BC-E6C650DCF7FC^^^2.16.840.1.113883.3.42.10032.100001.13^TAC|asd^test||19000101|M
            PR1|1||87750000^Insertion of nasogastric tube (procedure)^SCT|Gastric Tube (Type: Nasal)|20230627121501+0000
            PR1|2||235425002^Insertion of orogastric tube (procedure)^SCT|Gastric Tube (Type: Oral)|20230627121501+0000
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals(2, document.treatments.size)
        val treatments = document.treatments.list
        Assert.assertEquals(CommonTreatments.GASTRIC_TUBE.dataString, treatments[0].name)
        Assert.assertTrue(treatments[0].treatmentData is GastricTubeData)
        Assert.assertEquals(GastricTubeData.Type.NASAL.dataString, treatments[0].getData<GastricTubeData>()?.type)
        Assert.assertEquals(CommonTreatments.GASTRIC_TUBE.dataString, treatments[1].name)
        Assert.assertTrue(treatments[1].treatmentData is GastricTubeData)
        Assert.assertEquals(GastricTubeData.Type.ORAL.dataString, treatments[1].getData<GastricTubeData>()?.type)
    }

    @Test
    fun testIngestChestSeal() = runTest {
        val msg = """MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20250304083912+0000||ADT^A28|b4b46761-abf8-4c1b-8b68-eb22f47eca13|T|2.3
            EVN|A28|20250304083912+0000
            PID|1|||C9C74189-093F-4B36-96D6-732614A8A735^^^2.16.840.1.113883.3.42.10032.100001.13^TAC|Bob^John||19960304|M
            PR1|1||464094004^Breathing interventions^SCT||20250304083901+0000|P
            """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals(1, document.treatments.size)

        val treatments = document.treatments.list
        Assert.assertEquals(CommonTreatments.CHEST_SEAL.dataString, treatments[0].name)
        Assert.assertTrue(treatments[0].treatmentData is ChestSealData)
    }

    @Test
    fun ingestFingerThor() = runTest {
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20250304084443+0000||ADT^A28|247655ad-8836-4687-bfe1-fa233c108355|T|2.3
            EVN|A28|20250304084443+0000
            PID|1|||9AB20F4A-ACB8-4F0F-8551-9CA3511B3A70^^^2.16.840.1.113883.3.42.10032.100001.13^TAC|Doe^John||20030304|M
            PR1|1||182705007^Breathing interventions^SCT||20250304084431+0000|P
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals(1, document.treatments.size)

        val treatments = document.treatments.list
        Assert.assertEquals(CommonTreatments.FINGER_THOR.dataString, treatments[0].name)
        Assert.assertTrue(treatments[0].treatmentData is FingerThorData)
    }

    @Test
    fun ingestNeedleD() = runTest {
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20250304084735+0000||ADT^A28|5154d92b-fffb-4895-838b-caf04d1e6a78|T|2.3
            EVN|A28|20250304084735+0000
            PID|1|||BD6F2B2D-13F6-41A7-A099-B0D152694614^^^2.16.840.1.113883.3.42.10032.100001.13^TAC|Doe^Polly||19980304|F
            PR1|1||1290622004^Breathing interventions^SCT||20250304084721+0000|P
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals(1, document.treatments.size)

        val treatments = document.treatments.list
        Assert.assertEquals(CommonTreatments.NEEDLE_D.dataString, treatments[0].name)
        Assert.assertTrue(treatments[0].treatmentData is NeedleDData)
    }

    @Test
    fun ingestPericardiocentesis() = runTest {
        // ARRANGE
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20250313122235+0000||ADT^A28|0fd66392-f9ce-4bea-84b6-e3af41b5c625|T|2.3
            EVN|A28|20250313122235+0000
            PID|1|||ABC7F592-5DED-41F0-A110-6BE3A3006F12^^^2.16.840.1.113883.3.42.10032.100001.13^TAC|UT-ABCFD^WALK||19490313|M
            PR1|1||309849004^Pericardiocentesis^SCT||20250313122219+0000|P
            OBX|1|NM|16086006^Pericardiocentesis blood removed^SCT|1|500
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals(1, document.treatments.size)
        val treatments = document.treatments.list
        Assert.assertEquals(CommonTreatments.PERICARDIOCENTESIS.dataString, treatments[0].name)
        Assert.assertTrue(treatments[0].treatmentData is PericardiocentesisData)
        Assert.assertEquals(500f, treatments[0].getData<PericardiocentesisData>()?.volume)
        Assert.assertEquals(null, treatments[0].getData<PericardiocentesisData>()?.volumeUnit)
    }

    @Test
    fun testIngestPelvicBinder() = runTest {
        // ARRANGE
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20250311161805+0000||ADT^A28|d529d0d6-cde4-410a-bd74-bb3ab73e8b33|T|2.3
            EVN|A28|20250311161805+0000
            PID|1|||826221B9-31CC-441E-974B-BEBE03C16DA5^^^2.16.840.1.113883.3.42.10032.100001.13^TAC|UT-BCCEB^MILO||19690311|M
            PR1|1||767693007^Pelvic binder^SCT||20250311161759+0000|P
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        val treatments = document.treatments.list
        Assert.assertEquals(1, treatments.size)
        Assert.assertEquals(CommonTreatments.PELVIC_BINDER.dataString, treatments[0].name)
        Assert.assertTrue(treatments[0].treatmentData is PelvicBinderData)
    }

    @Test
    fun testIngestImmobilizationData() = runTest {
        // ARRANGE
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20250314183757+0000||ADT^A28|f658f3d2-436f-485a-83b6-2800d766e7d3|T|2.3
            EVN|A28|20250314183757+0000
            PID|1|||15C666DC-5F14-4A92-8423-2A8E5B665688^^^2.16.840.1.113883.3.42.10032.100001.13^TAC|UT-CDCFA^SMUR||19470314|M
            PR1|1||79321009^Splint^SCT||20250314181045+0000|P
            OBX|1|ST|69536005^Immobilization location^SCT|1|Head
            PR1|2||79321009^Splint^SCT||20250314183221+0000|P
            OBX|1|ST|301151001^Distal pulse assessment after immobilization^SCT|1|Present
            OBX|2|ST|32153003^Immobilization location^SCT|2|Left lower extremity
            PR1|3||79321009^Splint^SCT||20250314183230+0000|P
            OBX|1|ST|22943007^Immobilization location^SCT|1|Anterior torso
            PR1|4||79321009^Splint^SCT||20250314183238+0000|P
            OBX|1|ST|368208006^Immobilization location^SCT|1|Left upper extremity
            OBX|2|ST|54518005^Distal pulse assessment after immobilization^SCT|2|Absent
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        val treatments = document.treatments.list
        Assert.assertEquals(4, treatments.size)
        for (treat in treatments) {
            Assert.assertEquals(CommonTreatments.IMMOBILIZATION.dataString, treat.name)
            Assert.assertTrue(treat.treatmentData is ImmobilizationData)
            Assert.assertEquals("Unknown Splint Type", treat.getData<ImmobilizationData>()?.type)
        }
        Assert.assertEquals("Head", treatments[0].getData<ImmobilizationData>()?.location)  // Non-standard location
        Assert.assertEquals(ImmobilizationData.TernaryStatus.YES, treatments[1].getData<ImmobilizationData>()?.neurovascularAfter?.pulse)
        Assert.assertEquals(ImmobilizationData.Location.LEFT_LOWER_LEG.dataString, treatments[1].getData<ImmobilizationData>()?.location)
        Assert.assertEquals("Front Torso", treatments[2].getData<ImmobilizationData>()?.location)  // Non-standard location
        Assert.assertEquals(ImmobilizationData.TernaryStatus.NO, treatments[3].getData<ImmobilizationData>()?.neurovascularAfter?.pulse)
        Assert.assertEquals(ImmobilizationData.Location.LEFT_UPPER_ARM.dataString, treatments[3].getData<ImmobilizationData>()?.location)
    }

    @Test
    fun testIngestCCollar() = runTest {
        // ARRANGE
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20250314183757+0000||ADT^A28|f658f3d2-436f-485a-83b6-2800d766e7d3|T|2.3
            EVN|A28|20250314183757+0000
            PID|1|||15C666DC-5F14-4A92-8423-2A8E5B665688^^^2.16.840.1.113883.3.42.10032.100001.13^TAC|UT-CDCFA^SMUR||19470314|M
            PR1|1||398041008^Rigid collar^SCT||20250314183735+0000|P
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        val treatments = document.treatments.list
        Assert.assertEquals(1, treatments.size)
        Assert.assertEquals(CommonTreatments.IMMOBILIZATION.dataString, treatments[0].name)
        Assert.assertTrue(treatments[0].treatmentData is ImmobilizationData)
        Assert.assertEquals(ImmobilizationData.Type.C_COLLAR.dataString, treatments[0].getData<ImmobilizationData>()?.type)
    }

    @Test
    fun testIngestSpineBoard() = runTest {
        // ARRANGE
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20250314183757+0000||ADT^A28|f658f3d2-436f-485a-83b6-2800d766e7d3|T|2.3
            EVN|A28|20250314183757+0000
            PID|1|||15C666DC-5F14-4A92-8423-2A8E5B665688^^^2.16.840.1.113883.3.42.10032.100001.13^TAC|UT-CDCFA^SMUR||19470314|M
            PR1|1||1290630003^Spine board^SCT||20250314183742+0000|P
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        val treatments = document.treatments.list
        Assert.assertEquals(1, treatments.size)
        Assert.assertEquals(CommonTreatments.IMMOBILIZATION.dataString, treatments[0].name)
        Assert.assertTrue(treatments[0].treatmentData is ImmobilizationData)
        Assert.assertEquals(ImmobilizationData.Type.SPINE_BOARD.dataString, treatments[0].getData<ImmobilizationData>()?.type)
    }

    @Test
    fun testIngestChestEqualRiseFall() = runTest {
        // ARRANGE
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20250408150757+0000||ADT^A28|b217af2e-4046-4552-95be-7e560b723136|T|2.3
            EVN|A28|20250408150757+0000
            PID|1|||0321D9C8-2840-4CE8-BC50-17070B46276B^^^2.16.840.1.113883.3.42.10032.100001.13^TAC|UT-DCCEB^LAYS||19470408|M
            PR1|1||268925001^Breathing exam^SCT||20250408150748+0000|P
            OBX|1|ST|248560005^R > L chest wall movement^SCT|1|R > L
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        val observations = document.observations.list
        Assert.assertEquals(1, observations.size)
        Assert.assertEquals(CommonObservations.CHEST_EQUAL_RISE_FALL.dataString, observations[0].name)
        Assert.assertTrue(observations[0].observationData is ChestRiseFallData)
        Assert.assertEquals(ChestRiseFallData.Type.R_L.dataString, observations[0].getData<ChestRiseFallData>()?.riseOrFall)
    }
}
