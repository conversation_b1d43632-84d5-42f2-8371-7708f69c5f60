package mil.af.afrl.batman.hl7lib.outbound.observation

import gov.afrl.batdok.encounter.commands.buildLogObservationCommand
import gov.afrl.batdok.encounter.ids.ObservationId
import gov.afrl.batdok.encounter.observation.CommonObservations
import gov.afrl.batdok.encounter.observation.EFastExamData
import mil.af.afrl.batdokdata.id.EncounterId
import mil.af.afrl.batman.batdokid.DomainId
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.orur01.EFastOruBuilder
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.msh03SendApplicationCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.msh11ProcessingIDCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.msh12VersionIDCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.oidIPI
import mil.af.afrl.batman.hl7lib.outbound.buildSingletonCommandDataList
import mil.af.afrl.batman.hl7lib.outbound.pidSegmentSimpleNoPatient
import mil.af.afrl.batman.hl7lib.outbound.toResult
import mil.af.afrl.batman.hl7lib.util.OutboundHL7Data
import mil.af.afrl.batman.hl7lib.util.ProviderInfo
import mil.af.afrl.batman.hl7lib.util.asUuidString
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import java.time.Instant

class EFastExportTest {

    // Note this message is not sent for GT

    private lateinit var hl7Data: OutboundHL7Data
    private val observationTime = Instant.ofEpochMilli(1687868468000)
    private val observationId = DomainId.create<ObservationId>()

    @Before
    fun setup() {
        val encounterId1 = DomainId.create<EncounterId>()
        val command = buildLogObservationCommand(
            name = CommonObservations.EFAST_EXAM.dataString,
            data = EFastExamData(
                leftLungSliding = EFastExamData.Type.POSITIVE.dataString,
                rightLungSliding = EFastExamData.Type.NEGATIVE.dataString,
                pericardialFluid = EFastExamData.Type.EQUIVOCAL.dataString,
                rightUpperQuadrant = EFastExamData.Type.POSITIVE.dataString,
                leftUpperQuadrant = EFastExamData.Type.NEGATIVE.dataString,
                suprapubicFluid = EFastExamData.Type.EQUIVOCAL.dataString,
                interpretation = EFastExamData.Type.POSITIVE.dataString
            ),
            timestamp = observationTime,
            id = observationId
        )
        hl7Data = OutboundHL7Data().apply {
            providerInfo = ProviderInfo("providerfirst providerlast", "123456")
            externalIds = mapOf(oidIPI to "testid")
            commandsList = buildSingletonCommandDataList(command)
            commandsByEncounter = mapOf(encounterId1 to buildSingletonCommandDataList(command))
            
        }
    }

    @Test
    fun testEFast_OMDS() {
        // ARRANGE
        hl7Data.endpoint = Endpoint.OMDS_DELTA

        // ACT
        val result = EFastOruBuilder(hl7Data, Instant.now()).buildOrus()
            .joinToString("\r") { it.toResult(hl7Data.endpoint!!, hasEvn = false, hasPv1Discharge = true) }

        // ASSERT
        val expected = """
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${pidSegmentSimpleNoPatient(hl7Data)}
            PV1|1|E|^^^THEATER HX||||123456^providerlast^providerfirst|||||||||||HX|||||||||||||||||||||||D||||ttt
            ORC|CM
            OBR|1|||123320627^PoC US E-FAST Scan|||20230627122108+0000|||||||||||||||20230627122108+0000||RAD|F
            ZUI|1|${observationId.asUuidString()}|UNKNOWN
            OBX|1|TX|Report|1|Positive||||||F|||20230627122108+0000
        """.trimIndent().replace("\n", "\r")

        Assert.assertEquals(expected, result)
    }

    @Test
    fun testEFast_CDP() {
        // ARRANGE
        hl7Data.endpoint = Endpoint.TAC

        // ACT
        val result = EFastOruBuilder(hl7Data, Instant.now()).buildOrus()
            .joinToString("\r") { it.toResult(hl7Data.endpoint!!, hasEvn = false, hasPv1Discharge = true) }

        // ASSERT
        val expected = """
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.TAC)}||CDP||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.TAC)}|${msh12VersionIDCode(Endpoint.TAC)}
            ${pidSegmentSimpleNoPatient(hl7Data)}
            PV1|1|E|||||123456^providerlast^providerfirst|||||||||||HX|||||||||||||||||||||||||||ttt
            OBR|1|||123320627^PoC US E-FAST Scan^GENESISAlias|||20230627122108+0000|||||||||||||||20230627122108+0000||RAD|F
            ZUI|1|${observationId.asUuidString()}|UNKNOWN
            OBX|1|TX|3341006^Right lung structure (body structure)^SCT|1|Negative||||||F|||20230627122108+0000
            OBX|2|TX|260385009^Negative (qualifier)^SCT|1|Negative||||||F|||20230627122108+0000
            OBX|3|TX|44029006^Left lung structure (body structure)^SCT|2|Positive||||||F|||20230627122108+0000
            OBX|4|TX|10828004^Positive (qualifier)^SCT|2|Positive||||||F|||20230627122108+0000
            OBX|5|TX|24949005^Pericardial sac structure (body structure)^SCT|3|Equivocal||||||F|||20230627122108+0000
            OBX|6|TX|42425007^Equivocal (qualifier)^SCT|3|Equivocal||||||F|||20230627122108+0000
            OBX|7|TX|50519007^Structure of right upper quadrant of abdomen (body structure)^SCT|4|Positive||||||F|||20230627122108+0000
            OBX|8|TX|10828004^Positive (qualifier)^SCT|4|Positive||||||F|||20230627122108+0000
            OBX|9|TX|86367003^Structure of left upper quadrant of abdomen (body structure)^SCT|5|Negative||||||F|||20230627122108+0000
            OBX|10|TX|260385009^Negative (qualifier)^SCT|5|Negative||||||F|||20230627122108+0000
            OBX|11|TX|11708003^Hypogastric region structure (body structure)^SCT|6|Equivocal||||||F|||20230627122108+0000
            OBX|12|TX|42425007^Equivocal (qualifier)^SCT|6|Equivocal||||||F|||20230627122108+0000
            OBX|13|TX|Report|7|Positive||||||F|||20230627122108+0000
        """.trimIndent().replace("\n", "\r")

        Assert.assertEquals(expected, result)
    }

}
