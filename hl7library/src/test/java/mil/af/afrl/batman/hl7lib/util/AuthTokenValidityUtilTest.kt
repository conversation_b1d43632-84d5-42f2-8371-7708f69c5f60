package mil.af.afrl.batman.hl7lib.util

import org.json.JSONObject
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class AuthTokenValidityUtilTest {

    @Test
    fun testTokenInfoValid_ValidToken() {
        // ARRANGE
        val tokenInfo = JSONObject().apply {
            put("device_code", "device_code")
            put("refresh_token", "refresh_token")
            put("issue_time", Instant.now().epochSecond)
        }.toString()

        // ACT
        val result = AuthTokenValidityUtil.isTokenInfoValid(tokenInfo)

        // ASSERT
        Assert.assertTrue(result)
    }

    @Test
    fun testTokenInfoValid_NoDeviceCodeToken() {
        // ARRANGE
        val tokenInfo = JSONObject().apply {
            put("refresh_token", "refresh_token")
            put("issue_time", Instant.now().epochSecond)
        }.toString()

        // ACT
        val result = AuthTokenValidityUtil.isTokenInfoValid(tokenInfo)

        // ASSERT
        Assert.assertFalse(result)
    }

    @Test
    fun testTokenInfoValid_NoRefreshToken() {
        // ARRANGE
        val tokenInfo = JSONObject().apply {
            put("device_code", "device_code")
            put("issue_time", Instant.now().epochSecond)
        }.toString()

        // ACT
        val result = AuthTokenValidityUtil.isTokenInfoValid(tokenInfo)

        // ASSERT
        Assert.assertFalse(result)
    }

    @Test
    fun testTokenInfoValid_oldToken() {
        // ARRANGE
        val timeAge = Instant.now().minus(31, ChronoUnit.DAYS).epochSecond
        val tokenInfo = JSONObject().apply {
            put("device_code", "device_code")
            put("refresh_token", "refresh_token")
            put("issue_time", timeAge)
        }.toString()

        // ACT
        val result = AuthTokenValidityUtil.isTokenInfoValid(tokenInfo)

        // ASSERT
        Assert.assertFalse(result)
    }

    @Test
    fun testTokenInfoValid_TimeToken() {
        // ARRANGE
        val timeAge = Instant.now().minus(28, ChronoUnit.DAYS).epochSecond
        val tokenInfo = JSONObject().apply {
            put("device_code", "device_code")
            put("refresh_token", "refresh_token")
            put("issue_time", timeAge)
        }.toString()

        // ACT
        val result = AuthTokenValidityUtil.isTokenInfoValid(tokenInfo)

        // ASSERT
        Assert.assertTrue(result)
    }

    @Test
    fun testTokenInfoValid_malformedToken() {
        // ARRANGE
        val tokenInfo = "{not valid json}"

        // ACT
        val result = AuthTokenValidityUtil.isTokenInfoValid(tokenInfo)

        // ASSERT
        Assert.assertFalse(result)
    }

    @Test
    fun testTokenInfoValid_nullOrEmptyToken() {
        // ARRANGE
        // ACT
        val result = AuthTokenValidityUtil.isTokenInfoValid(null)

        // ASSERT
        Assert.assertFalse(result)
    }
}