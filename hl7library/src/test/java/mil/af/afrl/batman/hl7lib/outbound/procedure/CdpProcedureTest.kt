package mil.af.afrl.batman.hl7lib.outbound.procedure

import gov.afrl.batdok.commands.proto.TreatmentCommands.AddTreatment
import gov.afrl.batdok.encounter.commands.buildAddTreatmentCommand
import gov.afrl.batdok.encounter.commands.buildLogObservationCommand
import gov.afrl.batdok.encounter.observation.BreathSoundsData
import gov.afrl.batdok.encounter.observation.CasualtyPPEData
import gov.afrl.batdok.encounter.observation.ChestRiseFallData
import gov.afrl.batdok.encounter.observation.CommonObservations
import gov.afrl.batdok.encounter.observation.PulseValuesData
import gov.afrl.batdok.encounter.observation.PupilDilationData
import gov.afrl.batdok.encounter.observation.RhythmData
import gov.afrl.batdok.encounter.observation.TextData
import gov.afrl.batdok.encounter.treatment.ChestSealData
import gov.afrl.batdok.encounter.treatment.ChestTubeData
import gov.afrl.batdok.encounter.treatment.CommonTreatments
import gov.afrl.batdok.encounter.treatment.DressingData
import gov.afrl.batdok.encounter.treatment.EyeShieldData
import gov.afrl.batdok.encounter.treatment.FingerThorData
import gov.afrl.batdok.encounter.treatment.FoleyCatheterData
import gov.afrl.batdok.encounter.treatment.GastricTubeData
import gov.afrl.batdok.encounter.treatment.HypothermiaPreventionData
import gov.afrl.batdok.encounter.treatment.ImmobilizationData
import gov.afrl.batdok.encounter.treatment.LineData
import gov.afrl.batdok.encounter.treatment.NeedleDData
import gov.afrl.batdok.encounter.treatment.NeurovascularStatus
import gov.afrl.batdok.encounter.treatment.O2Data
import gov.afrl.batdok.encounter.treatment.PelvicBinderData
import gov.afrl.batdok.encounter.treatment.PericardiocentesisData
import gov.afrl.batdok.encounter.treatment.Treatment
import gov.afrl.batdok.encounter.treatment.TreatmentData
import gov.afrl.batdok.encounter.treatment.WoundPackingData
import mil.af.afrl.batdokdata.id.EncounterId
import mil.af.afrl.batman.batdokid.DomainId
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.ADT_A28Builder
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.oidIPI
import mil.af.afrl.batman.hl7lib.outbound.buildCommandDataFromCommand
import mil.af.afrl.batman.hl7lib.outbound.buildSingletonCommandDataList
import mil.af.afrl.batman.hl7lib.outbound.evnSegment
import mil.af.afrl.batman.hl7lib.outbound.pidSegmentSimpleNoPatient
import mil.af.afrl.batman.hl7lib.outbound.toResult
import mil.af.afrl.batman.hl7lib.util.OutboundHL7Data
import mil.af.afrl.batman.hl7lib.util.ProviderInfo
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import java.time.Instant

class CdpProcedureTest {

    private lateinit var hl7Data: OutboundHL7Data
    private val observationTime = Instant.ofEpochMilli(1687868468000)

    @Before
    fun setup() {
        hl7Data = OutboundHL7Data().apply {
            providerInfo = ProviderInfo("providerfirst providerlast", "123456")
            externalIds = mapOf(oidIPI to "testid")
            endpoint = Endpoint.TAC
            activeEncounterId = DomainId.create<EncounterId>()
        }
    }

    //region Attribute Item tests
    @Test
    fun testChestTubeAttributes() = assertTreatmentData(
        buildAddTreatmentCommand(
            name = CommonTreatments.CHEST_TUBE.dataString,
            timestamp = Instant.ofEpochMilli(1687868101221),
            data = ChestTubeData(
                location = ChestTubeData.Location.LEFT.dataString
            )
        ),
        """
            PR1|1||446847002^Drainage of pleural cavity via chest tube (procedure)^SCT|Chest Tube (Location: Left side)|20230627121501+0000
            OBX|1|TX|7771000^Left (qualifier value)^SCT|1|Left side||||||F|||20230627121501+0000
            OBX|2|TX|91199000^Anterior axillary line structure (body structure)^SCT|1|Anterior Axillary||||||F|||20230627121501+0000
        """
    )

    @Test
    fun testChestSealAttributes() = assertTreatmentData(
        buildAddTreatmentCommand(
            name = CommonTreatments.CHEST_SEAL.dataString,
            timestamp = Instant.ofEpochMilli(1687868101221),
            data = ChestSealData(
                location = ChestSealData.Location.LEFT_BACK.dataString
            )
        ),
        """
            PR1|1||464094004^Pneumothorax dressing (physical object)^SCT|Chest Seal (Left Back)|20230627121501+0000
            OBX|1|TX|788647001^Structure of left half of posterior chest wall (body structure)^SCT|1|Left Back||||||F|||20230627121501+0000
        """
    )

    @Test
    fun testFingerThorAttributes() = assertTreatmentData(
        buildAddTreatmentCommand(
            name = CommonTreatments.FINGER_THOR.dataString,
            timestamp = Instant.ofEpochMilli(1687868101221),
            data = FingerThorData(
                location = FingerThorData.Location.LEFT.dataString
            )
        ),
        """
            PR1|1||182705007^Tension pneumothorax relief (procedure)^SCT|Finger Thor (Left side)|20230627121501+0000
            OBX|1|TX|1290343009^Structure of left half of anterior chest wall (body structure)^SCT|1|Left side||||||F|||20230627121501+0000
        """
    )

    @Test
    fun testNeedleDAttributes() = assertTreatmentData(
        buildAddTreatmentCommand(
            name = CommonTreatments.NEEDLE_D.dataString,
            timestamp = Instant.ofEpochMilli(1687868101221),
            data = NeedleDData(
                location = NeedleDData.Location.L_MID_CLAV.dataString
            )
        ),
        """
            PR1|1||1290622004^Needle chest decompression for tension pneumothorax (procedure)^SCT|Needle-D (L Mid-Clav)|20230627121501+0000
            OBX|1|TX|7771000^Left (qualifier value)^SCT|1|L Mid-Clav||||||F|||20230627121501+0000
            OBX|2|TX|279013009^Midclavicular line (body structure)^SCT|1|L Mid-Clav||||||F|||20230627121501+0000
        """
    )

    @Test
    fun testDressingAttributes() = assertTreatmentData(
        buildAddTreatmentCommand(
            name = CommonTreatments.DRESSING.dataString,
            timestamp = Instant.ofEpochMilli(1687868101221),
            data = DressingData(
                type = DressingData.Type.HEMOSTATIC.dataString,
                location = DressingData.Location.LUE.dataString
            )
        ),
        """
            PR1|1||3895009^Application of dressing (procedure)^SCT|Dressing (Type: Hemostatic - Location: LUE)|20230627121501+0000
            OBX|1|TX|261365000^Hemostatic gauze (physical object)^SCT|1|Hemostatic||||||F|||20230627121501+0000
            OBX|2|TX|1481000124102^Wound dressing observable (observable entity)^SCT|2|LUE||||||F|||20230627121501+0000
        """
    )

    @Test
    fun testOtherDressingAttributes() = assertTreatmentData(
        buildAddTreatmentCommand(
            name = CommonTreatments.DRESSING.dataString,
            timestamp = Instant.ofEpochMilli(1687868101221),
            data = DressingData(
                type = "Test",
                location = DressingData.Location.LUE.dataString
            )
        ),
        """
            PR1|1||3895009^Application of dressing (procedure)^SCT|Dressing (Type: Test - Location: LUE)|20230627121501+0000
            OBX|1|TX|1581000124103^Wound dressing type (observable entity)^SCT|1|Other||||||F|||20230627121501+0000
            OBX|2|TX|1481000124102^Wound dressing observable (observable entity)^SCT|2|LUE||||||F|||20230627121501+0000
        """
    )

    @Test
    fun testWoundPackingAttributes() = assertTreatmentData(
        treatmentName = CommonTreatments.WOUND_PACKING.dataString,
        treatmentData = WoundPackingData(
            location = WoundPackingData.Location.LUE.dataString,
            type = WoundPackingData.Type.HEMOSTATIC.dataString
        ),
        expectedTreatmentSegments = """
            PR1|1||241019003^Packing of wound (procedure)^SCT|Wound Packing (Type: Hemostatic, Location: LUE)|20230627121501+0000
            OBX|1|TX|261365000^Hemostatic gauze (physical object)^SCT|1|Hemostatic||||||F|||20230627121501+0000
            OBX|2|TX|1481000124102^Wound dressing observable (observable entity)^SCT|2|LUE||||||F|||20230627121501+0000
        """
    )

    @Test
    fun testRhythmEctopyAttributes() = assertTreatmentData(
        buildLogObservationCommand(
            name = CommonObservations.RHYTHM.dataString,
            data = RhythmData(
                type = "NSR;ST;SB;Asystole;Paced;PEA;A-FIB;A-FLUT;SVT;VF;VT"
            ),
            timestamp = observationTime
        ),
        """
            PR1|1||70878000^Electrocardiogram, rhythm (procedure)^SCT|Rhythm (NSR;ST;SB;Asystole;Paced;PEA;A-FIB;A-FLUT;SVT;VF;VT)|20230627122108+0000
        """
        /*"""
            PR1|1||70878000^Electrocardiogram, rhythm (procedure)^SCT|Rhythm (NSR;ST;SB;Asystole;Paced;PEA;A-FIB;A-FLUT;SVT;VF;VT)|20230627122108+0000
            OBX|1|TX|64730000^Normal sinus rhythm (finding)^SCT|1|NSR||||||F|||20230627122108+0000
            OBX|2|TX|11092001^Sinus tachycardia (finding)^SCT|2|ST||||||F|||20230627122108+0000
            OBX|3|TX|49710005^Sinus bradycardia (disorder)^SCT|3|SB||||||F|||20230627122108+0000
            OBX|4|TX|397829000^Asystole (disorder)^SCT|4|Asystole||||||F|||20230627122108+0000
            OBX|5|TX|10370003^Rhythm from artificial pacing (finding)^SCT|5|Paced||||||F|||20230627122108+0000
            OBX|6|TX|234172002^Electromechanical dissociation (disorder)^SCT|6|PEA||||||F|||20230627122108+0000
            OBX|7|TX|49436004^Atrial fibrillation (disorder)^SCT|7|A-FIB||||||F|||20230627122108+0000
            OBX|8|TX|5370000^Atrial flutter (disorder)^SCT|8|A-FLUT||||||F|||20230627122108+0000
            OBX|9|TX|6456007^Supraventricular tachycardia (disorder)^SCT|9|SVT||||||F|||20230627122108+0000
            OBX|10|TX|71908006^Ventricular fibrillation (disorder)^SCT|10|VF||||||F|||20230627122108+0000
            OBX|11|TX|25569003^Ventricular tachycardia (disorder)^SCT|11|VT||||||F|||20230627122108+0000
        """*/
        )

    @Test
    fun testPulseAttributes() = assertTreatmentData(
        buildLogObservationCommand(
            name = CommonObservations.PULSE_VALUES.dataString,
            data = PulseValuesData(
                brachial = PulseValuesData.Quality.TWO.dataString,
                carotid = null,
                femoral = PulseValuesData.Quality.ONE.dataString,
                pedal = null,
                radial = PulseValuesData.Quality.D.dataString,
                temperature = null
            ),
            timestamp = observationTime
        ),
        """
            PR1|1||69466000^Unknown procedure (finding)^SCT|Pulse Values (Brachial: +2, Femoral: +1, Radial: D)|20230627122108+0000
        """
        /*"""
            PR1|1||410196005^Pulse taking management (procedure)^SCT|Pulse Values (Brachial: +2, Femoral: +1, Radial: D)|20230627122108+0000
            OBX|1|TX|420340005^Brachial pulse, function (observable entity)^SCT|1|+2||||||F|||20230627122108+0000
            OBX|2|TX|65452004^Radial pulse, function (observable entity)^SCT|2|D||||||F|||20230627122108+0000
        """*/
    )

    @Test
    fun testEyeShieldAttributes() = assertTreatmentData(
        buildAddTreatmentCommand(
            name = CommonTreatments.EYE_SHIELD.dataString,
            timestamp = Instant.ofEpochMilli(1687868101221),
            data = EyeShieldData(
                left = true,
                right = true
            )
        ),
        """
            PR1|1||225696009^Applying eye shield (procedure)^SCT|Eye Shield (Both eyes)|20230627121501+0000
            OBX|1|TX|51440002^Right and left (qualifier value)^SCT|1|Both||||||F|||20230627121501+0000
        """
    )

    @Test
    fun testHypothermiaPreventionAttributes() = assertTreatmentData(
        buildAddTreatmentCommand(
            name = CommonTreatments.HYPOTHERMIA_PREVENTION.dataString,
            timestamp = Instant.ofEpochMilli(1687868101221),
            data = HypothermiaPreventionData(
                type = listOf(
                    HypothermiaPreventionData.Type.BLANKET.dataString,
                    HypothermiaPreventionData.Type.BLIZZARD_BLANKET.dataString,
                    HypothermiaPreventionData.Type.MAX_HEAT_IN_CABIN.dataString,
                ).joinToString(", ")
            )
        ),
        """
            PR1|1||19328000^Blanket, device (physical object)^SCT|Hypothermia Prevention (Blanket)|20230627121501+0000
            PR1|2||309433007^Patient warming therapy (procedure)^SCT|Hypothermia Prevention (Blizzard Blanket)|20230627121501+0000
            OBX|1|TX|74964007^Other (qualifier value)^SCT|1|Blizzard Blanket||||||F|||20230627121501+0000
            PR1|3||309433007^Patient warming therapy (procedure)^SCT|Hypothermia Prevention (Max heat in cabin)|20230627121501+0000
            OBX|1|TX|74964007^Other (qualifier value)^SCT|1|Max heat in cabin||||||F|||20230627121501+0000
        """
    )

    @Test
    fun testPupilDilationAttributes() = assertTreatmentData(
        buildLogObservationCommand(
            name = CommonObservations.PUPIL_DILATION.dataString,
            data = PupilDilationData(
                perrlaSizeLeft = 7,
                perrlaSizeRight = 3
            ),
            timestamp = observationTime
        ),
        """
            PR1|1||363953003^Size of pupil (observable entity)^SCT|Pupil Dilation (Left pupil dilated 7mm, Right pupil dilated 3mm)|20230627122108+0000
            OBX|1|NM|16089004^Structure of pupil of left eye (body structure)^SCT|1|7|^mm|||||F|||20230627122108+0000
            OBX|2|NM|52378001^Structure of pupil of right eye (body structure)^SCT|2|3|^mm|||||F|||20230627122108+0000
        """
    )
    
    /*@Test
    fun testImmobilizationAttributes() = assertTreatmentData(
        buildAddTreatmentCommand(
            name = CommonTreatments.IMMOBILIZATION.dataString,
            timestamp = Instant.ofEpochMilli(1687868101221),
            data = ImmobilizationData(
                type = "C-Collar; C-Spine; Spine Board; Pelvic Splint; Pelvic Binder: hhbh; Other Splint: Air, Board/Wire, SAM, Traction, Pulse Present, test, Location: LUE"
            )
        ),
        """
            PR1|1||257884004^Immobilization - action (qualifier value)^SCT|Immobilization (C-Collar)|20230627121501+0000
            OBX|1|TX|49689007^Application of cervical collar (procedure)^SCT|1|C-Collar||||||F|||20230627121501+0000
        """
    )*/

    @Test
    fun testCasualtyPPEAttributes() = assertTreatmentData(
        buildLogObservationCommand(
            name = CommonObservations.CASUALTY_PPE.dataString,
            data = CasualtyPPEData(
                ppeList = (listOf(CasualtyPPEData.Type.HELMET_BALLISTIC.dataString, CasualtyPPEData.Type.BLAST_GAUGE.dataString)),
                ),
            timestamp = observationTime
        ),
        """
            PR1|1||449399005^Application of personal protective equipment (procedure)^SCT|Casualty PPE (Helmet, Ballistic, Blast Gauge)|20230627122108+0000
            OBX|1|TX|285695004^Helmet (physical object)^SCT|1|Helmet, Ballistic||||||F|||20230627122108+0000
            OBX|2|TX|BG^Blast Gauge^SCT|2|Blast Gauge||||||F|||20230627122108+0000
        """
    )

    @Test
    fun testFoleyAttributes() = assertTreatmentData(
        buildAddTreatmentCommand(
            name = CommonTreatments.FOLEY_CATHETER.dataString,
            timestamp = Instant.ofEpochMilli(1687868101221),
            data = FoleyCatheterData(
                size = 25f,
                color = "Condom"
            )
        ),
        """
            PR1|1||73368009^Foley catheter (physical object)^SCT|Foley Catheter (Size: 25, Type: Condom)|20230627121501+0000
            OBX|1|TX|1230041002^Application of incontinence sheath (procedure)^SCT|1|Condom||||||F|||20230627121501+0000
            OBX|2|NM|260967009^Size of catheter (qualifier value)^SCT|2|25.0||||||F|||20230627121501+0000
        """
    )

    @Test
    fun testGastricTubeAttributes() = assertTreatmentData(
        buildAddTreatmentCommand(
            name = CommonTreatments.GASTRIC_TUBE.dataString,
            timestamp = Instant.ofEpochMilli(1687868101221),
            data = GastricTubeData(
                type = "Nasal, Oral",
            )
        ),
        """
            PR1|1||87750000^Insertion of nasogastric tube (procedure)^SCT|Gastric Tube (Type: Nasal)|20230627121501+0000
            PR1|2||235425002^Insertion of orogastric tube (procedure)^SCT|Gastric Tube (Type: Oral)|20230627121501+0000
        """
    )

    @Test
    fun testVisualAcuityAttributes() = assertTreatmentData(
        buildLogObservationCommand(
            name = "Visual Acuity Test",
            data = TextData(description="test"),
            timestamp = observationTime
        ),
        """
            PR1|1||16830007^Visual acuity testing (procedure)^SCT|Visual Acuity Test (test)|20230627122108+0000
            OBX|1|TX|260246004^Visual acuity finding (finding)^SCT|1|test||||||F|||20230627122108+0000
        """
    )
    

    @Test
    fun testBreathSoundsAttributes() = assertTreatmentData(
        buildLogObservationCommand(
            name = CommonObservations.BREATH_SOUNDS.dataString,
            data = BreathSoundsData(
                typeList = (listOf(BreathSoundsData.Type.RIGHT_NORMAL.dataString,
                    BreathSoundsData.Type.RIGHT_DIMINISHED.dataString,
                    BreathSoundsData.Type.RIGHT_ABSENT.dataString,
                    BreathSoundsData.Type.LEFT_NORMAL.dataString,
                    BreathSoundsData.Type.LEFT_DIMINISHED.dataString,
                    BreathSoundsData.Type.LEFT_ABSENT.dataString,
                )),
            ),
            timestamp = observationTime
        ),
        """
            PR1|1||52653008^Respiratory sounds (observable entity)^SCT|Breath Sounds (Right Normal, Right Diminished, Right Absent, Left Normal, Left Diminished, Left Absent)|20230627122108+0000
            OBX|1|TX|22803001R^Normal respiratory function (finding) - Right^SCT|1|Right Normal||||||F|||20230627122108+0000
            OBX|2|TX|58840004R^Decreased breath sounds (finding) - Right^SCT|2|Right Diminished||||||F|||20230627122108+0000
            OBX|3|TX|65503000R^Absent breath sounds (finding) - Right^SCT|3|Right Absent||||||F|||20230627122108+0000
            OBX|4|TX|22803001L^Normal respiratory function (finding) - Left^SCT|4|Left Normal||||||F|||20230627122108+0000
            OBX|5|TX|58840004L^Decreased breath sounds (finding) - Left^SCT|5|Left Diminished||||||F|||20230627122108+0000
            OBX|6|TX|65503000L^Absent breath sounds (finding) - Left^SCT|6|Left Absent||||||F|||20230627122108+0000
        """
    )

    @Test
    fun testLineAttributes() = assertTreatmentData(
        buildAddTreatmentCommand(
            name = CommonTreatments.LINE.dataString,
            timestamp = Instant.ofEpochMilli(1687868101221),
            data = LineData(
                type = LineData.Type.IO.dataString,
                subtype = LineData.Subtype.CORDIS.dataString,
                size = 15f,
                sizeUnit = "mm",
                gauge = 16f,
                gaugeUnit = "mm",
                location = LineData.Location.ARM.dataString,
                side = LineData.Side.LEFT.dataString
            )
        ), """
            PR1|1||440009003^Insertion of needle for intraosseous infusion (procedure)^SCT|Line (Type: IO, SubType: Cordis, Location: Left Arm, Gauge: 16 mm, Size: 15 mm)|20230627121501+0000
            OBX|1|TX|368208006^Left upper arm structure (body structure)^SCT|1|Left Arm||||||F|||20230627121501+0000
            OBX|2|NM|277305006^16G (qualifier)^SCT|2|16.0||||||F|||20230627121501+0000
            OBX|3|NM|371240000^Red color (qualifier value)^SCT|3|15.0||||||F|||20230627121501+0000
            OBX|4|TX|257279004^Single lumen catheter (physical object)^SCT|4|Cordis||||||F|||20230627121501+0000
        """
    )

    @Test
    fun testPelvicBinderAttributes() = assertTreatmentData(
        buildAddTreatmentCommand(
            name = CommonTreatments.PELVIC_BINDER.dataString,
            timestamp = Instant.ofEpochMilli(1687868101221),
            data = PelvicBinderData("pbtype")
        ), """
            PR1|1||767693007^Pelvic binder (physical object)^SCT|Pelvic Binder (pbtype)|20230627121501+0000
            OBX|1|TX|410657003^Type (attribute)^SCT|1|pbtype||||||F|||20230627121501+0000
        """
    )

    @Test
    fun testCSpineTreatment() = assertTreatmentData(
        buildAddTreatmentCommand(
            name = CommonTreatments.IMMOBILIZATION.dataString,
            timestamp = Instant.ofEpochMilli(1687868101221),
            data = ImmobilizationData(ImmobilizationData.Type.C_SPINE.dataString)
        ),
        """
            PR1|1||467221000^Cervical spine immobilization frame (physical object)^SCT|Immobilization (C-Spine)|20230627121501+0000
        """
    )

    @Test
    fun testCCollarTreatment() = assertTreatmentData(
        buildAddTreatmentCommand(
            name = CommonTreatments.IMMOBILIZATION.dataString,
            timestamp = Instant.ofEpochMilli(1687868101221),
            data = ImmobilizationData(ImmobilizationData.Type.C_COLLAR.dataString)
        ),
        """
            PR1|1||398041008^Cervical spine immobilization^SCT|Immobilization (C-Collar)|20230627121501+0000
        """
    )

    @Test
    fun testSpineBoardTreatment() = assertTreatmentData(
        buildAddTreatmentCommand(
            name = CommonTreatments.IMMOBILIZATION.dataString,
            timestamp = Instant.ofEpochMilli(1687868101221),
            data = ImmobilizationData(ImmobilizationData.Type.SPINE_BOARD.dataString)
        ),
        """
            PR1|1||1290630003^Immobilization of spine using spine board (procedure)^SCT|Immobilization (Spine Board)|20230627121501+0000
        """
    )

    @Test
    fun testSwathTreatment() = assertTreatmentData(
        buildAddTreatmentCommand(
            name = CommonTreatments.IMMOBILIZATION.dataString,
            timestamp = Instant.ofEpochMilli(1687868101221),
            data = ImmobilizationData(ImmobilizationData.Type.SWATH.dataString)
        ),
        """
            PR1|1||52037006^Application of sling^SCT|Immobilization (Swath)|20230627121501+0000
        """
    )

    // TODO: Simplify this test once the immobilization stuff is broken up. For now,
    //  make it an exhaustive test of all the immobilization things together.
    @Test
    fun testSplintTreatment() = assertTreatmentData(
        buildAddTreatmentCommand(
            Treatment(
                CommonTreatments.IMMOBILIZATION.dataString,
                buildImmobilizationData("Air", "Right Upper Arm", true, true),
                timestamp = Instant.ofEpochMilli(1687868101221),
            )
        ),
        """
            PR1|1||79321009^Application of splint^SCT|Immobilization (Air, Right Upper Arm, Neuro Before - Pulse: Yes, Neuro After - Pulse: Yes)|20230627121501+0000
            OBX|1|TX|301151001^Peripheral pulse present (finding)^SCT|1|Yes||||||F|||20230627121501+0000
            OBX|2|TX|368209003^Right upper arm structure (body structure)^SCT|2|Right Upper Arm||||||F|||20230627121501+0000
            OBX|3|TX|410657003^Type (attribute)^SCT|3|Air||||||F|||20230627121501+0000
        """
    )

    @Test
    fun testSplintOBX() = assertTreatmentData(
        CommonTreatments.IMMOBILIZATION.dataString,
        ImmobilizationData("Air", location = "Right Lower Arm"),
        """
            PR1|1||79321009^Application of splint^SCT|Immobilization (Air, Right Lower Arm)|20230627121501+0000
            OBX|1|TX|64262003^Structure of right forearm (body structure)^SCT|1|Right Lower Arm||||||F|||20230627121501+0000
            OBX|2|TX|410657003^Type (attribute)^SCT|2|Air||||||F|||20230627121501+0000
        """
    )

    @Test
    fun testPericardiocentesisAttributes() = assertTreatmentData(
        treatmentName = CommonTreatments.PERICARDIOCENTESIS.dataString,
        treatmentData = PericardiocentesisData(
            volume = 25f,
            volumeUnit = "mL"
        ),
        """
            PR1|1||309849004^Pericardiocentesis (procedure)^SCT|Pericardiocentesis (Volume: 25 mL)|20230627121501+0000
            OBX|1|NM|16086006^Blood volume (observable entity)^SCT|1|25.0|^mL|||||F|||20230627121501+0000
        """
    )

    @Test
    fun testChestEqualRiseFallAttributes() = assertTreatmentData(
        buildLogObservationCommand(
            name = CommonObservations.CHEST_EQUAL_RISE_FALL.dataString,
            data = ChestRiseFallData(ChestRiseFallData.Type.L_R.dataString),
            timestamp = observationTime
        ),
        """
            PR1|1||268925001^Examination of respiratory system (procedure)^SCT|Chest Equal Rise and Fall (L > R)|20230627122108+0000
            OBX|1|TX|248561009^Right side of chest moves less than left (finding)^SCT|1|L > R||||||F|||20230627122108+0000
        """
    )

    private fun assertTreatmentData(treatmentName: String, treatmentData: TreatmentData, expectedTreatmentSegments: String) {
        return assertTreatmentData(
            buildAddTreatmentCommand(
                name = treatmentName,
                timestamp = Instant.ofEpochMilli(1687868101221),
                data = treatmentData
            ),
            expectedTreatmentSegments
        )
    }

    private fun assertTreatmentData(command: com.google.protobuf.Message, expectedTreatmentSegments: String) {
        // ARRANGE
        hl7Data.commandsByEncounter = mapOf(hl7Data.activeEncounterId!! to buildSingletonCommandDataList(command))

        // ACT
        val result = ADT_A28Builder(Endpoint.TAC).populateAll(hl7Data).build().toResult(Endpoint.TAC)

        // ASSERT
        val expected = """
            MSH|^~\&|BATDOK||CDP||zzz||ADT^A28|yyy|P|2.3
            ${evnSegment("A28")}
            ${pidSegmentSimpleNoPatient(hl7Data)}
            ${expectedTreatmentSegments.trim()}
            ZPI|1||||||||||||||HUMAN
        """.trimIndent().replace("\n", "\r")
        Assert.assertEquals(expected, result)
    }
    //endregion
    
    //region MARCH Tab Simple Treatments
    @Test
    fun testATabSimpleTreatments() {
        // ARRANGE
        fun buildO2Command(name: String) : AddTreatment {
            return buildAddTreatmentCommand(
                name = CommonTreatments.O2.dataString,
                timestamp = Instant.ofEpochMilli(1687868101221),
                data = O2Data(deliveryMethod = name)
            )
        }
        val commands = listOf(
            buildSimpleTreatmentCommand(CommonTreatments.CRIC.dataString),
            buildSimpleTreatmentCommand(CommonTreatments.TRACH.dataString),
            buildSimpleTreatmentCommand(CommonTreatments.NPA.dataString),
            buildSimpleTreatmentCommand(CommonTreatments.OPA.dataString),
            buildO2Command(O2Data.DeliveryMethod.NRB.dataString),
            buildO2Command(O2Data.DeliveryMethod.NC.dataString),
            buildO2Command(O2Data.DeliveryMethod.VENT.dataString),
            buildO2Command(O2Data.DeliveryMethod.BVM.dataString),
        ).map(::buildCommandDataFromCommand)
        hl7Data.commandsByEncounter = mapOf(hl7Data.activeEncounterId!! to commands)

        // ACT
        val result = ADT_A28Builder(Endpoint.TAC).populateAll(hl7Data).build().toResult(Endpoint.TAC)

        // ASSERT
        val expected = """
            MSH|^~\&|BATDOK||CDP||zzz||ADT^A28|yyy|P|2.3
            ${evnSegment("A28")}
            ${pidSegmentSimpleNoPatient(hl7Data)}
            PR1|1||69466000^Unknown procedure (finding)^SCT|CRIC|20230627121501+0000
            PR1|2||69466000^Unknown procedure (finding)^SCT|Trach|20230627121501+0000
            PR1|3||69466000^Unknown procedure (finding)^SCT|NPA|20230627121501+0000
            PR1|4||69466000^Unknown procedure (finding)^SCT|OPA|20230627121501+0000
            PR1|5||427591007^Nonrebreather oxygen mask (physical object)^SCT|O2 (Source: NRB)|20230627121501+0000
            PR1|6||336623009^Oxygen nasal cannula (physical object)^SCT|O2 (Source: NC)|20230627121501+0000
            PR1|7||**********^Invasive mechanical ventilation (regime/therapy)^SCT|O2 (Source: Vent)|20230627121501+0000
            PR1|8||425696007^Manual respiratory assistance using bag and mask (procedure)^SCT|O2 (Source: BVM)|20230627121501+0000
            ZPI|1||||||||||||||HUMAN
        """.trimIndent().replace('\n', '\r')
        Assert.assertEquals(expected, result)
    }

    @Test
    fun testHTabSimpleTreatments() {
        // ARRANGE
        val commands = listOf(
            buildSimpleTreatmentCommand(CommonTreatments.BLOOD_FLUID_WARMER.dataString),
            buildSimpleTreatmentCommand("C-Collar for positioning"),  // Not a common treatment
            buildSimpleTreatmentCommand(CommonTreatments.HOB_AT_30.dataString),
            buildSimpleTreatmentCommand(CommonTreatments.PROTECTIVE_EYEWEAR.dataString),
        ).map(::buildCommandDataFromCommand)
        hl7Data.commandsByEncounter = mapOf(hl7Data.activeEncounterId!! to commands)

        // ACT
        val result = ADT_A28Builder(Endpoint.TAC).populateAll(hl7Data).build().toResult(Endpoint.TAC)

        // ASSERT
        val expected = """
            MSH|^~\&|BATDOK||CDP||zzz||ADT^A28|yyy|P|2.3
            ${evnSegment("A28")}
            ${pidSegmentSimpleNoPatient(hl7Data)}
            PR1|1||69466000^Unknown procedure (finding)^SCT|Blood/Fluid Warmer|20230627121501+0000
            PR1|2||69466000^Unknown procedure (finding)^SCT|C-Collar for positioning|20230627121501+0000
            PR1|3||69466000^Unknown procedure (finding)^SCT|HOB @ 30|20230627121501+0000
            PR1|4||69466000^Unknown procedure (finding)^SCT|Protective Eyewear|20230627121501+0000
            ZPI|1||||||||||||||HUMAN
        """.trimIndent().replace('\n', '\r')
        Assert.assertEquals(expected, result)
    }

    private fun buildSimpleTreatmentCommand(name: String) : AddTreatment {
        return buildAddTreatmentCommand(
            name = name,
            timestamp = Instant.ofEpochMilli(1687868101221),
        )
    }

    private fun buildImmobilizationData(type: String?, location: String?, beforePulse: Boolean?, afterPulse: Boolean?) = ImmobilizationData(
        type = type,
        location = location,
        subtype = null,
        neurovascularBefore = NeurovascularStatus(
            pulse = ImmobilizationData.TernaryStatus.fromBool(beforePulse),
            motor = null,
            sensory = null
        ),
        neurovascularAfter = NeurovascularStatus(
            pulse = ImmobilizationData.TernaryStatus.fromBool(afterPulse),
            motor = null,
            sensory = null
        ),
    )
    //endregion
}
