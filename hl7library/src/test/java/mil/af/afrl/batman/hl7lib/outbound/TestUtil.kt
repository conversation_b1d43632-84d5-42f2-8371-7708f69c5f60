package mil.af.afrl.batman.hl7lib.outbound

import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.v231.message.ORM_O01
import ca.uhn.hl7v2.model.v231.message.ORU_R01
import ca.uhn.hl7v2.model.v231.segment.EVN
import ca.uhn.hl7v2.model.v231.segment.MSH
import ca.uhn.hl7v2.model.v231.segment.PV1
import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.DocumentCommands.CommandData
import gov.afrl.batdok.encounter.CommonComplaints
import gov.afrl.batdok.encounter.CommonLinkRelationships
import gov.afrl.batdok.encounter.Contact
import gov.afrl.batdok.encounter.CustomAction
import gov.afrl.batdok.encounter.DrawingPoint
import gov.afrl.batdok.encounter.Gender
import gov.afrl.batdok.encounter.Grade
import gov.afrl.batdok.encounter.HistoryType
import gov.afrl.batdok.encounter.IndividualLab
import gov.afrl.batdok.encounter.IntakeOutput
import gov.afrl.batdok.encounter.IntakeOutputType
import gov.afrl.batdok.encounter.Interval
import gov.afrl.batdok.encounter.KnownEventTypes
import gov.afrl.batdok.encounter.Link
import gov.afrl.batdok.encounter.Note
import gov.afrl.batdok.encounter.Patcat
import gov.afrl.batdok.encounter.PatcatService
import gov.afrl.batdok.encounter.VentSettings
import gov.afrl.batdok.encounter.commands.addVital
import gov.afrl.batdok.encounter.commands.buildAddComplaintCommand
import gov.afrl.batdok.encounter.commands.buildAddCustomActionCommand
import gov.afrl.batdok.encounter.commands.buildAddEventCommand
import gov.afrl.batdok.encounter.commands.buildAddIntakeOutputItemCommand
import gov.afrl.batdok.encounter.commands.buildAddNoteCommand
import gov.afrl.batdok.encounter.commands.buildAddPointWithLabelCommand
import gov.afrl.batdok.encounter.commands.buildAddRemoveAllergyListCommand
import gov.afrl.batdok.encounter.commands.buildAddRemoveEquipmentCommands
import gov.afrl.batdok.encounter.commands.buildAddRemoveMajorEventCommand
import gov.afrl.batdok.encounter.commands.buildAddTreatmentCommand
import gov.afrl.batdok.encounter.commands.buildChangeBloodTypeCommand
import gov.afrl.batdok.encounter.commands.buildChangeCareCommand
import gov.afrl.batdok.encounter.commands.buildChangeDOBCommand
import gov.afrl.batdok.encounter.commands.buildChangeDecisionCommand
import gov.afrl.batdok.encounter.commands.buildChangeDodIdCommand
import gov.afrl.batdok.encounter.commands.buildChangeGenderCommand
import gov.afrl.batdok.encounter.commands.buildChangeGradeCommand
import gov.afrl.batdok.encounter.commands.buildChangeHeightCommand
import gov.afrl.batdok.encounter.commands.buildChangeInjuryCommand
import gov.afrl.batdok.encounter.commands.buildChangeInjuryTimeCommand
import gov.afrl.batdok.encounter.commands.buildChangeMoiCommand
import gov.afrl.batdok.encounter.commands.buildChangeNameCommand
import gov.afrl.batdok.encounter.commands.buildChangeNationalityCommand
import gov.afrl.batdok.encounter.commands.buildChangeOpenCloseEncounterCommand
import gov.afrl.batdok.encounter.commands.buildChangePatcatCommand
import gov.afrl.batdok.encounter.commands.buildChangePatientIdCommand
import gov.afrl.batdok.encounter.commands.buildChangeProcedureCommand
import gov.afrl.batdok.encounter.commands.buildChangeSsnCommand
import gov.afrl.batdok.encounter.commands.buildChangeTBSACommand
import gov.afrl.batdok.encounter.commands.buildChangeTriageCommand
import gov.afrl.batdok.encounter.commands.buildChangeUnitCommand
import gov.afrl.batdok.encounter.commands.buildChangeWeightCommand
import gov.afrl.batdok.encounter.commands.buildCreateLinkCommand
import gov.afrl.batdok.encounter.commands.buildLastEventTimeCommand
import gov.afrl.batdok.encounter.commands.buildLogBloodCommand
import gov.afrl.batdok.encounter.commands.buildLogLabCommand
import gov.afrl.batdok.encounter.commands.buildLogMedicineCommand
import gov.afrl.batdok.encounter.commands.buildLogObservationCommand
import gov.afrl.batdok.encounter.commands.buildLogVentCommand
import gov.afrl.batdok.encounter.commands.buildLogVitalCommand
import gov.afrl.batdok.encounter.commands.buildNinelineLocationCommand
import gov.afrl.batdok.encounter.commands.buildPastHistoryCommand
import gov.afrl.batdok.encounter.commands.buildUpdateAssessedEvacCommand
import gov.afrl.batdok.encounter.commands.orders.buildAddMedicineOrderLineCommand
import gov.afrl.batdok.encounter.commands.orders.buildAddOrderLineCommand
import gov.afrl.batdok.encounter.ids.EventId
import gov.afrl.batdok.encounter.ids.ObservationId
import gov.afrl.batdok.encounter.medicine.Medicine
import gov.afrl.batdok.encounter.metadata.ArrhythmiaData
import gov.afrl.batdok.encounter.metadata.Equipment
import gov.afrl.batdok.encounter.metadata.LitterData
import gov.afrl.batdok.encounter.metadata.MajorEvent
import gov.afrl.batdok.encounter.metadata.Procedure
import gov.afrl.batdok.encounter.metadata.Signature
import gov.afrl.batdok.encounter.movement.EvacStatus
import gov.afrl.batdok.encounter.movement.NinelineLocation
import gov.afrl.batdok.encounter.movement.TriageCategory
import gov.afrl.batdok.encounter.observation.Blood
import gov.afrl.batdok.encounter.observation.BreathSoundsData
import gov.afrl.batdok.encounter.observation.RespEffortData
import gov.afrl.batdok.encounter.orders.CustomActionOrderLine
import gov.afrl.batdok.encounter.orders.MedicineOrderLine
import gov.afrl.batdok.encounter.orders.OrderStatus
import gov.afrl.batdok.encounter.orders.OrderType
import gov.afrl.batdok.encounter.orders.OrderedMedicine
import gov.afrl.batdok.encounter.panel.KnownLabs
import gov.afrl.batdok.encounter.treatment.ChestSealData
import gov.afrl.batdok.encounter.treatment.ChestTubeData
import gov.afrl.batdok.encounter.treatment.CommonTreatments
import gov.afrl.batdok.encounter.treatment.DressingData
import gov.afrl.batdok.encounter.treatment.LineData
import gov.afrl.batdok.encounter.treatment.SplintData
import gov.afrl.batdok.encounter.treatment.TqData
import gov.afrl.batdok.encounter.treatment.Treatment
import gov.afrl.batdok.encounter.treatment.TubeData
import gov.afrl.batdok.encounter.vitals.Avpu
import gov.afrl.batdok.encounter.vitals.BloodPressure
import gov.afrl.batdok.encounter.vitals.CapRefill
import gov.afrl.batdok.encounter.vitals.EtCO2
import gov.afrl.batdok.encounter.vitals.GCS
import gov.afrl.batdok.encounter.vitals.HR
import gov.afrl.batdok.encounter.vitals.InvasiveBloodPressure
import gov.afrl.batdok.encounter.vitals.Output
import gov.afrl.batdok.encounter.vitals.Pain
import gov.afrl.batdok.encounter.vitals.Resp
import gov.afrl.batdok.encounter.vitals.SpO2
import gov.afrl.batdok.encounter.vitals.Temp
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.converter.RouteConverter.toKey
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId


fun buildTestCommands() : List<CommandData> {

    val complaintId : DomainId = DomainId.create()
    val planEventId : EventId = DomainId.create()
    val assessmentEventId : EventId = DomainId.create()
    val adtmcObservationId : ObservationId = DomainId.create()



    val customActionOrder = CustomActionOrderLine(
            id = DomainId.create(),
            timestamp = Instant.now(),
            title = "Administer Action 2",
            instructions = "When Eli makes the order",
            orderType = OrderType.CUSTOM.dataString,
            orderStatus = OrderStatus.ORDERED.dataString,
            frequency = Interval("Per Graham"),
            provider = Contact("Dr. Ezratty")
        )
    val customAction1 =  CustomAction(
        id = DomainId.create(),
        timestamp = Instant.now(),
        description = "description",
        message = "message",
        callSign = "callsign"
    )

    val medicineOrderLine = MedicineOrderLine(
        id = DomainId.create(),
        timestamp = Instant.ofEpochMilli(1687867264877),
        instructions = "When pain is greater than 7",
        orderType = OrderType.CUSTOM.dataString,
        orderStatus = OrderStatus.COMPLETE.dataString,
        frequency = Interval("Per Instruction"),
        provider = Contact("Dr. Professorson"),
        signature = Signature(name = "Jim", timeStamp = Instant.ofEpochMilli(1687867264877)),
        orderedMedicine = OrderedMedicine(
            name = "0.9% Sodium Chloride",
            ndc = null,
            rxcui = null,
            medId = DomainId.create(),
            route = "IM",
            volume = 5.0f,
            unit = "gm",
            serialNumber = "cereal",
            expirationDate = "expiration",
            type = "Fluid",
            exportName = "0.9% Sodium Chloride",
        ))
    val medicine = Medicine(
        "0.9% Sodium Chloride",
        ndc = null,
        administrationTime = Instant.ofEpochMilli(1687868132418),
        route = "IM",
        volume = 5.0f,
        unit = "gm",
        type = "Fluid"
    )

    val note = Note(
        id = DomainId.create(),
        callsign = "*******C A L L S I G N *******",
        timeStamp = Instant.now(),
        message = "*******MESSAGE*******"
    )

    val note2 = Note(
        id = DomainId.create(),
        callsign = "callsign2",
        timeStamp = Instant.ofEpochMilli(1687867264877),
        message = "message2"
    )

    val commandList = listOf(
        // Info Commands
        buildChangeNameCommand("test j patient"),
        buildChangeSsnCommand("123456789"),
        buildChangeBloodTypeCommand("A+"),
        buildChangeInjuryTimeCommand(Instant.ofEpochMilli(1687867264877)),
        buildChangePatcatCommand(Patcat(PatcatService.AIR_FORCE, null)),
        buildChangeUnitCommand("unit"),
        buildChangeGradeCommand(Grade.O01),
        buildAddRemoveAllergyListCommand(listOf("Acetaminophen", "Amoxicillin", "Anti-seizure Drugs"), emptyList()),
        buildChangeGenderCommand(Gender.MALE),
        buildChangeWeightCommand(456f),
        buildChangeHeightCommand(123f),
        buildChangeDOBCommand(LocalDate.ofInstant(Instant.ofEpochMilli(1056715353011), ZoneId.of("UTC"))),
        buildChangeDodIdCommand("**********"),
        buildChangePatientIdCommand(DomainId.create()),
        buildChangeNationalityCommand("United States of America"),

        // Evac Commands
        buildNinelineLocationCommand(true, NinelineLocation(location = "pickup location")),
        buildNinelineLocationCommand(false, NinelineLocation(location = "drop off location")),
        buildChangeTriageCommand(TriageCategory.MINIMAL),
        buildUpdateAssessedEvacCommand(EvacStatus.URGENT),

        // Treatment Commands
        // TODO: TQ labels?
        buildAddTreatmentCommand(Treatment(
            name = "TQ",
            timestamp = Instant.ofEpochMilli(1687868101221),
            treatmentData = TqData("Extremity", subLocation = "LUE"),
        )),
        buildAddTreatmentCommand(Treatment(
            name = "TQ",
            timestamp = Instant.ofEpochMilli(1687868104051),
            treatmentData = TqData("Extremity", subLocation = "RLE"),
        )),
        buildAddTreatmentCommand(Treatment(
            name = "TQ",
            timestamp = Instant.ofEpochMilli(1687868105186),
            treatmentData = TqData("Junctional"),
        )),
        buildAddTreatmentCommand(Treatment(
            name = "TQ",
            timestamp = Instant.ofEpochMilli(1687868105487),
            treatmentData = TqData("Truncal"),
        )),
        buildAddTreatmentCommand(Treatment(
            name = CommonTreatments.DRESSING.dataString,
            timestamp = Instant.ofEpochMilli(1687868109634),
            treatmentData = DressingData(DressingData.Type.PRESSURE.dataString, null, null),
        )),
        buildAddTreatmentCommand(Treatment(
            name = "INTACT",
            timestamp = Instant.ofEpochMilli(1687868111288),
        )),
        buildAddTreatmentCommand(Treatment(
            name = "O2",
            timestamp = Instant.ofEpochMilli(1687868113873),
        )),
        buildAddTreatmentCommand(Treatment(
            name = "CHEST_TUBE",
            timestamp = Instant.ofEpochMilli(1687868117654),
            treatmentData = ChestTubeData("Right"),
        )),
        buildAddTreatmentCommand(Treatment(
            name = "CHEST_SEAL",
            timestamp = Instant.ofEpochMilli(1687868120050),
            treatmentData = ChestSealData("LeftFront"),
        )),
        buildAddTreatmentCommand(Treatment(
            name = "SPLINT",
            timestamp = Instant.ofEpochMilli(1687868124553),
            treatmentData = SplintData(true),
        )),
        buildAddTreatmentCommand(Treatment(
            name = "Escharotomy",
            timestamp = Instant.ofEpochMilli(1687868129363),
        )),
        buildAddTreatmentCommand(Treatment(
            name = CommonTreatments.LINE.dataString,
            timestamp = Instant.ofEpochMilli(1687868129363),
            treatmentData = LineData(
                type = LineData.Type.PERIPHERAL.dataString,
                subtype = LineData.Subtype.EZ_IO.dataString,
                gauge = 18f,
                location = "Hand",
                side = "Left"
            )
        )),
        buildAddTreatmentCommand(Treatment(
            name = CommonTreatments.ET_TUBE.dataString,
            timestamp = Instant.ofEpochMilli(1687868129363),
            treatmentData = TubeData(
                depth = 7,
                breathingConfirmation = TubeData.BreathingConfirmation.BREATH_SOUNDS.dataString
            )
        )),

        buildLogObservationCommand(name = "Breath Sounds",
            data = BreathSoundsData(listOf("Left Wheezing", "Right Rhonchi")),
            timestamp = Instant.ofEpochMilli(1687868468000)
        ),
        buildLogObservationCommand("Respiratory Effort",
            data = RespEffortData("Labored"),
            timestamp = Instant.ofEpochMilli(1687868468000)
        ),
        buildLogObservationCommand("Chest Equal Rise & Fall",
            data = null,
            timestamp = Instant.ofEpochMilli(1687868468000)
        ),

        // Injury Commands
        buildChangeInjuryCommand("Amputation", true, "AMP", ""),
        buildAddPointWithLabelCommand(DrawingPoint(label = "AMP", x = 0.09814453F, y = 0.49060574F)),
        buildChangeInjuryCommand("Burn", true, "BU", "Posterior Left Upper Leg"),
        buildChangeInjuryCommand("Amputation", true, "AMP", "Anterior Left Hand"),
        buildAddPointWithLabelCommand(DrawingPoint(label = "AMP", x = 0.16F, y = 0.72F)),
        buildChangeInjuryCommand("Crush",true,"CR",""),

        // MOI Commands
        buildChangeMoiCommand("Fire", true, null),

        // TBSA Commands
        buildChangeTBSACommand(12.0),

        // Vital Commands
        buildLogVitalCommand(Instant.ofEpochMilli(1687867736308)) {
            addVital(Avpu("Alert"))
            addVital(BloodPressure(4, 5, "bplocation"))
            addVital(CapRefill(111.1f))
            addVital(GCS(222, 444, 333, 0))
            addVital(EtCO2(7))
            addVital(InvasiveBloodPressure(10, 11))
            addVital(Output(12f))
            addVital(Pain(6))
            addVital(HR(1, "hrlocation"))
            addVital(Resp(3))
            addVital(SpO2(2))
            addVital(Temp(8.9f))
        },
        buildLogVitalCommand(Instant.ofEpochMilli(1687867777661)) {
            addVital(Avpu("Pain"))
            addVital(Pain(8))
            addVital(HR(555, null))
            addVital(GCS(9, 8, 0, 0))
        },

        // Med Commands
        buildLogMedicineCommand(Medicine("0.9% Sodium Chloride", ndc = null, rxcui = "1234", administrationTime = Instant.ofEpochMilli(1687868132418),
            route = "IM", volume = 5.0f, unit = "gm", type = "Fluid")),
        buildLogMedicineCommand(Medicine("Ertapenem", ndc = null, rxcui = null, administrationTime = Instant.ofEpochMilli(1687868185002),
            route = "IV", volume = 1.0f, unit = "gm", type = "Analgesic")),
        buildLogMedicineCommand(Medicine("Acetaminophen", ndc = null, rxcui = "", administrationTime = Instant.ofEpochMilli(1687868185003),
            route = "PR", volume = 99.0f, unit = "", type = "Analgesic")),
        buildLogMedicineCommand(Medicine("Fresh blood", ndc = null, administrationTime = Instant.ofEpochMilli(1687868185004),
            route = "IV", volume = 5.0f, unit = "gm", type = "Fluid")),

        // Blood Commands
        // Why does this have a patient ID? Donor ID? And why no time? What are blood age units?
        buildLogBloodCommand(Blood(administrationTime = Instant.ofEpochMilli(1687868146065), bloodProduct = "Whole Blood (WB)",
            bloodType = "AB+", donationIdNumber = "blood unit", expirationDate = "2023-09-18", bloodAge = 90, volume = 6L, unit = "mg" )),
        buildLogBloodCommand(Blood(administrationTime = Instant.ofEpochMilli(1687868146065), bloodProduct = "Fresh",
            bloodType = "O+", donationIdNumber = "blood unit", expirationDate = "", bloodAge = 90, volume = 6L, unit = "mg" )),

        // Event Commands
        buildAddEventCommand(Instant.ofEpochMilli(1687868253132), "test note sub", true, KnownEventTypes.SUBJECTIVE, true),
        buildAddEventCommand(Instant.ofEpochMilli(1687868264308), "test note plan", true, KnownEventTypes.PLANNING, true),
        buildAddEventCommand(Instant.ofEpochMilli(1687868264308), "event linked to complaint ***", true, KnownEventTypes.PLANNING, true, planEventId),
        buildAddEventCommand(Instant.ofEpochMilli(1687868264308), "Assessment linked to complaint ***", true, KnownEventTypes.ASSESSMENT, true, assessmentEventId),
        buildAddEventCommand(
            Instant.ofEpochMilli(1687868253132),
            "FAST Exam Result - Positive FAST: Suspected hemoperitoneum",
            true,
            KnownEventTypes.OTHER,
            false
        ),

        // Lab Commands
        buildLogLabCommand(listOf(
            IndividualLab(KnownLabs.PH, "1.0", id = DomainId.create()),
            IndividualLab(KnownLabs.PACO2, "2.0", id = DomainId.create()),
            IndividualLab(KnownLabs.PAO2, "3.0", id = DomainId.create()),
            IndividualLab(KnownLabs.HCO3, "4.0", id = DomainId.create()),
            IndividualLab(KnownLabs.BE, "5.0", id = DomainId.create()),
            IndividualLab(KnownLabs.NA, "6.0", id = DomainId.create()),
            IndividualLab(KnownLabs.K, "7.0", id = DomainId.create()),
            IndividualLab(KnownLabs.ICA, "8.0", id = DomainId.create()),
            IndividualLab(KnownLabs.GLUCOSE, "9.0", id = DomainId.create()),
            IndividualLab(KnownLabs.HGB, "10.0", id = DomainId.create()),
            IndividualLab(KnownLabs.HCT, "11", id = DomainId.create()),
            IndividualLab("custom", "value", id = DomainId.create())
        )),

        // Order Commands
        buildAddOrderLineCommand(CustomActionOrderLine(
            id = DomainId.create(),
            timestamp = Instant.ofEpochMilli(1687867264877),
            title = "Administer Action 1",
            instructions = "When pain is greater than 7",
            orderType = OrderType.CUSTOM.dataString,
            orderStatus = OrderStatus.COMPLETE.dataString,
            frequency = Interval("Per Instruction"),
            provider = Contact("Dr. Professorson"))),

        // IntakeOutput Commands
        buildAddIntakeOutputItemCommand(
            IntakeOutput(
            id = DomainId.create(),
            timestamp = Instant.ofEpochMilli(1687867264877),
            type = IntakeOutputType.ORAL.dataString,
            isIntake = true,
            label = "water",
            volume = 100.00,
            unit = "mL"
        )),
        buildAddIntakeOutputItemCommand(
            IntakeOutput(
                id = DomainId.create(),
                timestamp = Instant.ofEpochMilli(1687867264877),
                type = IntakeOutputType.PARENTERAL.dataString,
                isIntake = true,
                label = "saline",
                volume = 200.00,
                unit = "mL"
            )),
        buildAddIntakeOutputItemCommand(
            IntakeOutput(
                id = DomainId.create(),
                timestamp = Instant.ofEpochMilli(1687867264877),
                type = IntakeOutputType.URINE.dataString,
                isIntake = false,
                label = "",
                volume = 300.00,
                unit = "mL"
            )),

        // Custom Action Commands
        buildAddOrderLineCommand(customActionOrder),
        buildAddCustomActionCommand(
            customAction1),

        //Link above custom to order line here
        customAction1.createLinkCommand(customActionOrder),

        buildAddNoteCommand(note),

        //Link Note to customOrder here
        note.createLinkCommand(customActionOrder),


        buildAddMedicineOrderLineCommand(medicineOrderLine),
        buildLogMedicineCommand(medicine),

        //Link medicine and medicineOrderLine here
        medicine.createLinkCommand(medicineOrderLine),

        buildAddNoteCommand(note2),

        //Line note and medicineOrderLine here
        note2.createLinkCommand(medicineOrderLine),

        //DNBI Commands

        buildAddComplaintCommand(symptom = CommonComplaints.SORE_THROAT.dataString, id = complaintId, history = "No history of present illness", reviewOfSystems = "SYSTEM REVIEW HERE"),
        buildPastHistoryCommand(type = HistoryType.SURGICAL.dataString, history = "Appendectomy last year"),
        buildLastEventTimeCommand(type = HistoryType.SURGICAL.dataString, date = Instant.ofEpochMilli(1687867264877),),
        // Link complaints, assessment and plan together
        buildCreateLinkCommand(Link(listOf(complaintId, planEventId, assessmentEventId), relationship = CommonLinkRelationships.ASSOC_WITH_COMPLAINT.dataString)),

        buildLogObservationCommand("Wheezing", RespEffortData("Difficult to breathe" ), adtmcObservationId, timestamp = Instant.ofEpochMilli(1687868468000)),
        buildCreateLinkCommand(Link(listOf(complaintId, adtmcObservationId), CommonLinkRelationships.RED_FLAGS.dataString)),

        // Vent Commands
        buildLogVentCommand(VentSettings(DomainId.create(), Instant.ofEpochMilli(1687867264877), "mode", 1.2, 3, 4, 5.6, "vent")),
        buildLogVentCommand(VentSettings(DomainId.create(), Instant.ofEpochMilli(1687867264877), fio2 = 444.0)),

        // Mission Data Commands
        buildAddRemoveMajorEventCommand(
            "Preflight Facility",
            listOf(
                MajorEvent("Arrhythmia", ArrhythmiaData(true, false)),
                MajorEvent("Airway Loss", null),
            ),
            emptyList()
        ),
        buildAddRemoveMajorEventCommand(
            "Descent",
            listOf(MajorEvent("ΔMAP > 20%", null)),
            emptyList()
        ),
        buildChangeProcedureCommand(Procedure("Foley"), true),
        buildChangeProcedureCommand(Procedure("Dressing Change"), true),
        buildAddRemoveEquipmentCommands(
            listOf(Equipment("Propaq MD"), Equipment("Loading", LitterData())),
            emptyList()
        ),
        buildChangeCareCommand("Parynch", true),
        buildChangeCareCommand("J-Tube Feeding", true),
        buildChangeDecisionCommand("Cardiovascular", true),
        buildChangeDecisionCommand("Renal", true),
        
        buildChangeOpenCloseEncounterCommand(open = true)
    ).map(::buildCommandDataFromCommand)

    return commandList
}

fun buildCommandDataFromCommand(command: Message) : CommandData {
    // Use a common timestamp for all items for ease of use
    // Most of the times logged with the items above (except vitals for some reason) are ignored
    return buildCommandData(command, timestamp = Instant.ofEpochMilli(1687868468743))
}

fun buildSingletonCommandDataList(command: Message) : List<CommandData> {
    return listOf(buildCommandDataFromCommand(command))
}

fun buildMapEntry(name: String, code: String, codeSet: String) : Pair<String, Pair<String, MutableMap<String, CodeNameGroup>>> {
    return Pair(name.toKey()!!, Pair(name, mutableMapOf(codeSet to CodeNameGroup(code, name))))
}

fun AbstractMessage.toResult(endpoint: Endpoint, hasEvn: Boolean = true, hasPv1Discharge: Boolean = false): String {
    val config = parser.parserConfiguration
    config.isValidating = false
    // These values are all normally generated in the message builder, so they're hard
    //  to actually verify. Replace them with nicer values.
    if (endpoint.format == EndpointType.MHSG_T) {
        (this["MSH"] as MSH).sendingFacility.parse("xxx")
    }
    (this["MSH"] as MSH).messageControlID.parse("yyy")
    (this["MSH"] as MSH).dateTimeOfMessage.parse("zzz")
    if (hasEvn) {
        (this["EVN"] as EVN).recordedDateTime.parse("zzz")
    }
    if (hasPv1Discharge) {
        val pv1 = when (this) {
            is ORU_R01 -> pidpD1NK1NTEPV1PV2ORCOBRNTEOBXNTECTI.pidpD1NK1NTEPV1PV2.pV1PV2.pV1
            is ORM_O01 -> pidpD1NTEPV1PV2IN1IN2IN3GT1AL1.pV1PV2.pV1
            else -> (this["PV1"] as PV1)
        }
        
        pv1.dischargeDateTime.parse("ttt")
    }
    return encode().trim()
}