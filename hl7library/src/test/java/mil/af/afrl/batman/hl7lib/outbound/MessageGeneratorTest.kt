package mil.af.afrl.batman.hl7lib.outbound

import ca.uhn.hl7v2.model.v231.message.ADT_A31
import io.mockk.coEvery
import io.mockk.mockkObject
import mil.af.afrl.batdokdata.id.EncounterId
import mil.af.afrl.batman.batdokid.DomainId
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.converter.DBAllergyConverter
import mil.af.afrl.batman.hl7lib.converter.MedicationConverter
import mil.af.afrl.batman.hl7lib.converter.RouteConverter
import mil.af.afrl.batman.hl7lib.converter.UnitConverter
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.MessageGenerator
import mil.af.afrl.batman.hl7lib.data.models.v231.field.CNUM
import mil.af.afrl.batman.hl7lib.data.models.v231.field.CS4001
import mil.af.afrl.batman.hl7lib.data.models.v231.field.CS54
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ExtendedCodedElement
import mil.af.afrl.batman.hl7lib.util.OutboundHL7Data
import mil.af.afrl.batman.hl7lib.util.MockkRule
import org.junit.Assert
import org.junit.Before
import org.junit.Rule
import org.junit.Test

abstract class MessageGeneratorTest {

    protected val hl7Data = OutboundHL7Data()
    protected lateinit var messageGenerator: MessageGenerator
    protected abstract val endpoint: Endpoint

    @Before
    fun setup() {
        // Only OMDS codes, MHSG-T will miss the ORM here, but it's tested in the FullPatientTest
        MedicationConverter.map.putAll(mapOf(
            buildMapEntry("0.9% Sodium Chloride", "615106", CNUM),
            buildMapEntry("Ertapenem", "325642", CNUM),
            buildMapEntry("Acetaminophen", "161", CNUM),
        ))

        UnitConverter.map.putAll(mapOf(
            buildMapEntry("g", "Gram", CS54),
            buildMapEntry("mg", "Milligrams", CS54),
        ))

        RouteConverter.map.putAll(mapOf(
            buildMapEntry("IM", "IntraMuscular", CS4001),
            buildMapEntry("NS", "Nostril-Both", CS4001),
            buildMapEntry("IV", "IV Drip", CS4001),
            buildMapEntry("PO", "Oral", CS4001),
        ))

        mockkObject(DBAllergyConverter)
        val msgMock = ADT_A31().apply { initQuickstart("ADT", "A31", "P") }
        coEvery { DBAllergyConverter.batdokDataToCodedElement(any(), any()) } returns ExtendedCodedElement(msgMock, "161", "Acetaminophen", "MULTUMDRUG")

        val encounterId = DomainId.create<EncounterId>()
        hl7Data.activeEncounterId = encounterId
        hl7Data.commandsByEncounter = mapOf(encounterId to buildTestCommands())
        messageGenerator = MessageGenerator("myId", endpoint, writeToFile = false)
    }

    @Test
    fun testDeviceIdAdded() {
        // ARRANGE

        // ACT
        val messages = messageGenerator.writeMessagesForEncounter(hl7Data)

        // ASSERT
        // Device ID should be inserted into MSH.4. Find MSH segments and verify this.
        // Also note that MSH is dumb and the first pipe ("|") is the first field, which sets
        //  the field delimiter, so the message indices below are off by one.
        val mshSegments = messages.split('\r').filter { it.startsWith("MSH") }
        Assert.assertEquals(expectedNumberOfMessages(), mshSegments.size)  // One MSH segment for each expected message
        for (segment in mshSegments) {
            val segSplits = segment.split('|')
            Assert.assertEquals(expectedDeviceId(), segSplits[3])
        }
    }

    abstract fun expectedDeviceId() : String
    abstract fun expectedNumberOfMessages() : Int

    @Test
    open fun testEncounterIdAdded() {
        // ARRANGE

        // ACT
        val messages = messageGenerator.writeMessagesForEncounter(hl7Data)

        // ASSERT

        // Encounter ID should be inserted into PV1.19. Find PV1 segments and verify this.
        val pv1Segments = messages.split('\r').filter { it.startsWith("PV1") }

        Assert.assertEquals(expectedNumberOfMessages() - 1, pv1Segments.size)  // One PV1 segment for each expected message
        // Grab the first PV1 segment's random UUID and verify it matches a UUID format
        val firstId = pv1Segments[0].split('|')[19]
        Assert.assertEquals(36, firstId.length)
        Assert.assertTrue("[0-9a-f\\-]{36}".toRegex().matches(firstId))

        // Encounter ID should also be inserted into PID.18
        val pidSegments = messages.split('\r').filter { it.startsWith("PID") }
        Assert.assertEquals(expectedNumberOfMessages(), pidSegments.size)
        val firstPIDId = pidSegments[0].split("|")[18]
        Assert.assertEquals(36, firstPIDId.length)
        Assert.assertTrue("[0-9a-f\\-]{36}".toRegex().matches(firstPIDId))

        // Verify other messages have the same ID string
        for (segment in pv1Segments.drop(1)) {
            val segSplits = segment.split('|')
            Assert.assertEquals(firstId, segSplits[19])
        }
        for(segment in pidSegments.drop(1)) {
            val segSplits = segment.split('|')
            Assert.assertEquals(firstPIDId, segSplits[18])
        }
    }

    @Test
    fun testBatchExport() {
        // ARRANGE

        // ACT
        val messages = messageGenerator.writeMessagesForEncounter(hl7Data)

        // ASSERT
        val segments = messages.split('\r')
        // Make sure the header/trailer segments exist as expected
        val fhsSegments = segments.filter { it.startsWith("FHS") }
        Assert.assertEquals(1, fhsSegments.size)
        val bhsSegments = segments.filter { it.startsWith("BHS") }
        Assert.assertEquals(1, bhsSegments.size)
        val btsSegments = segments.filter { it.startsWith("BTS") }
        Assert.assertEquals(1, btsSegments.size)
        // Make sure expected message size appears in BTS
        val btsSegment = btsSegments.first()
        val btsFields = btsSegment.split('|')
        Assert.assertEquals(expectedNumberOfMessages().toString(), btsFields[1])
        Assert.assertEquals(expectedNumberOfMessages().toString(), btsFields[3])
        val ftsSegments = segments.filter { it.startsWith("FTS") }
        Assert.assertEquals(1, ftsSegments.size)
    }

    @Test
    open fun testCorrectDestinationId() {
        // ARRANGE

        // ACT
        val messages = messageGenerator.writeMessagesForEncounter(hl7Data)

        // ASSERT
        // Device ID should be inserted into MSH.5. Find MSH segments and verify this.
        // Also note that MSH is dumb and the first pipe ("|") is the first field, which sets
        //  the field delimiter, so the message indices below are off by one.
        val mshSegments = messages.split('\r').filter { it.startsWith("MSH") }
        for (segment in mshSegments) {
            Assert.assertEquals(expectedDestinationId(), segment.split("|")[4])
        }
    }

    abstract fun expectedDestinationId() : String

    @Test
    fun testHL7MessageForwarding() {
        // ARRANGE
        hl7Data.historicalHL7 = "spoof historical hl7"

        // ACT
        val messages = messageGenerator.writeMessagesForEncounter(hl7Data)

        // ASSERT
        // Ensure that the historical HL7 data appears at the start of the batch
        Assert.assertTrue(messages.startsWith(hl7Data.historicalHL7))
    }
}

class OmdsMessageGeneratorTest : MessageGeneratorTest() {
    override val endpoint = Endpoint.OMDS_DELTA
    override fun expectedDeviceId() = ""
    override fun expectedNumberOfMessages() = 35
    override fun expectedDestinationId() = ""
    @get:Rule val mockkRule = MockkRule(this, relaxed = true)

    @Test
    override fun testEncounterIdAdded() {
        // ARRANGE

        // ACT
        val messages = messageGenerator.writeMessagesForEncounter(hl7Data)

        // ASSERT
        // Encounter ID should be inserted into PV1.19. Find PV1 segments and verify this.
        val pv1Segments = messages.split('\r').filter { it.startsWith("PV1") }
        Assert.assertEquals(expectedNumberOfMessages() - 1, pv1Segments.size)  // One PV1 segment for each expected message
        // Grab the first PV1 segment's random UUID and verify it matches a UUID format
        val firstId = pv1Segments[0].split('|')[19]
        Assert.assertEquals(0, firstId.length)
        // Verify other messages have the same ID string
        for (segment in pv1Segments.drop(1)) {
            val segSplits = segment.split('|')
            Assert.assertEquals(firstId, segSplits[19])
        }
    }
}

class MhsgtMessageGeneratorTest : MessageGeneratorTest() {
    override val endpoint = Endpoint.MHSG_T
    override fun expectedDeviceId() = "myId"
    // One fewer since I did not add in codes for the ORM, but that is tested in FullPatientTest
    override fun expectedNumberOfMessages() = 28
    override fun expectedDestinationId() = endpoint.format.displayName
    @get:Rule val mockkRule = MockkRule(this, relaxed = true)
}