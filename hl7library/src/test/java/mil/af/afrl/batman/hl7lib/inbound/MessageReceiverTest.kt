package mil.af.afrl.batman.hl7lib.inbound

import gov.afrl.batdok.util.NameFormatter
import kotlinx.coroutines.test.runTest
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.message.MessageReceiver
import org.junit.Assert
import org.junit.Before
import org.junit.Test

class MessageReceiverTest {

    private lateinit var classUnderTest: MessageReceiver

    @Before
    fun setup() {
        classUnderTest = MessageReceiver()
    }

    @Test
    fun testProcessHL7Data() = runTest {
        // ARRANGE
        // Sample message batch from CDP, modified to have
        //  different demographics in each message
        // Actual content of message parsing is tested in
        //  individual message receiver tests
        val msg = """
            FHS|^~\&|CDP||BATDOK||20250307150221+0000
            BHS|^~\&|CDP||BATDOK||20250307150221+0000
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20250307150221+0000||ADT^A04|6447ba5f-3c85-48e0-8796-44a0ee7e0ded|T|2.3
            EVN|A04|20250307150221+0000
            PID|1|||12630804-363F-4A5B-87B7-4CC897577843^^^2.16.840.1.113883.3.42.10032.100001.13^TAC|name^correct||20130307|M
            ZPI|1||||||||||||||HUMAN
            PV1|1|||||||||||||||Y||||||||||||||||||||||||||||20250307150107+0000
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20250307150221+0000||ADT^A28|524c7578-85c1-4c5e-822f-750b5b18ce3e|T|2.3
            EVN|A28|20250307150221+0000
            PID|1|||12630804-363F-4A5B-87B7-4CC897577844^^^2.16.840.1.113883.3.42.10032.100001.13^TAC|name^bad||20130307|M
            ZPI|1||||||||||||||HUMAN
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20250307150221+0000||ADT^A28|801e9677-7b25-43e3-8bd6-e0ecf448fc34|T|2.3
            EVN|A28|20250307150221+0000
            PID|1|||12630804-363F-4A5B-87B7-4CC897577845^^^2.16.840.1.113883.3.42.10032.100001.13^TAC|UT-FABBC^POUT||20130307|M
            PR1|1||735346000^Direct pressure^SCT||20250307150118+0000|P
            PR1|2||7443007^OPA^SCT||20250307150119+0000|P
            PR1|3||79321009^Immobilization^SCT||20250307150212+0000|P
            OBX|1|ST|69536005^Immobilization location^SCT|1|Head
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20250307150221+0000||ORM^O01|7ba39b7d-6e35-49b4-8d09-07a649270cee|T|2.3
            PID|1|||12630804-363F-4A5B-87B7-4CC897579999^^^2.16.840.1.113883.3.42.10032.100001.18^OTHERSYSTEM|other^name||20130307|M
            PV1|1|||||||||||||||Y||||||||||||||||||||||||||||20250307150107+0000
            ORC|NW|RE^^CE3055DC-EE1E-4A48-AA4C-A8359B6626AD|||CM||||20250307150125+0000
            RXO|483017^Acetaminophen 1,000mg/100 mL injection (Ofirmev / IV Tylenol)^RxCUI|500||mg
            RXR|IVB^Route: IV Bolus^MHSGT||||Medication
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20250307150221+0000||ADT^A03|9bb6240c-fe44-4d0f-b9ff-907062f8ad57|T|2.3
            EVN|A03|20250307150221+0000
            PID|1|||12630804-363F-4A5B-87B7-4CC897577847^^^2.16.840.1.113883.3.42.10032.100001.13^TAC|man^person||20130307|M
            PV1|1|||||||||||||||||||||||||||||||||||||||||||20250307150107+0000
            BTS|5
            FTS|1
        """.trimIndent()

        // ACT
        val result = classUnderTest.processHL7Data(msg.byteInputStream())

        // ASSERT
        Assert.assertEquals("correct name", result.document?.info?.name?.toString(NameFormatter.Format.FIRST_LAST))
        Assert.assertTrue(result.document?.info?.maskingJustification?.isNotEmpty() == true)
        // IDs can be overwritten by later messages. This is fine, since if there are multiple
        //  IDs tied to the same system, I can't know which is "correct" anyway. Make sure
        //  they accumulate from across messages though.
        Assert.assertEquals("12630804-363F-4A5B-87B7-4CC897577847", result.externalIds?.get("2.16.840.1.113883.3.42.10032.100001.13"))
        Assert.assertEquals("12630804-363F-4A5B-87B7-4CC897579999", result.externalIds?.get("2.16.840.1.113883.3.42.10032.100001.18"))
    }

}
