package mil.af.afrl.batman.hl7lib.outbound

import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.pid08SexCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.pv102PatientClassCode
import mil.af.afrl.batman.hl7lib.util.HL7Data
import mil.af.afrl.batman.hl7lib.util.asUuidString

fun evnSegment(trigger: String): String{
    return "EVN|${trigger}|zzz"
}

fun pidSegment(hl7Data: HL7Data, endpoint: EndpointType): String {
    val openEncounterId = hl7Data.activeEncounterId
    val document = openEncounterId?.let { hl7Data.getDocumentForEncounter(it) }
    val sex = pid08SexCode(document?.info?.gender)
    val ssn = document?.info?.ssn
    val docId = document?.id?.asUuidString() ?: "UNKNOWN_ID"
    val docInfoID = document?.info?.patientId.asUuidString() ?:"UNKNOWN_ID"

    val segment = when (endpoint) {
        EndpointType.TAC -> "PID|1||**********^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI|${docInfoID}^^^2.16.840.1.113883.3.42.10033.100001.13^BATDOK~testid^^^2.16.840.1.113883.3.42.10001.100001.228~tac-id^^^2.16.840.1.113883.3.42.10032.100001.13^TAC|patient^test^j^^^^L||20030627|${sex}||||||||||${docId}||||||||||USA^United States of America^ISO 3166"
        else -> "PID|1||**********^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI|${docInfoID}^^^2.16.840.1.113883.3.42.10033.100001.13^BATDOK~testid^^^2.16.840.1.113883.3.42.10001.100001.228~tac-id^^^2.16.840.1.113883.3.42.10032.100001.13^TAC|patient^test^j^^^^L||20030627|${sex}||||||||||${docId}|${ssn}"
    }
    return segment
}

fun pidSegmentSimpleNoPatient(hl7Data: HL7Data): String {
    val docId = hl7Data.documentsByEncounter.values
        .firstOrNull { it.id != null}
        ?.id.asUuidString()
    
    return "PID|1|||^^^2.16.840.1.113883.3.42.10033.100001.13^BATDOK~testid^^^2.16.840.1.113883.3.42.10001.100001.228|||19000101|||||||||||${docId}"
}

fun pv1Segment(hl7Data: HL7Data, a02Data: Boolean = false) : String {
    val openEncounterId = hl7Data.activeEncounterId
    val document = openEncounterId?.let { hl7Data.getDocumentForEncounter(it) }
    val docId = document?.id?.asUuidString() ?: "UNKNOWN_ID"
    val gt = "PV1|1|${pv102PatientClassCode(Endpoint.MHSG_T)}|^^^THEATER${if (a02Data) "^^^^^drop off location" else ""}|||${if (a02Data) "^^^^^^^^pickup location" else ""}|123456^providerlast^providerfirst|||||||||||HX|${docId}|||||||||||||||||||||||||20230627120104+0000|ttt"
    val omds = "PV1|1|${pv102PatientClassCode(Endpoint.OMDS_DELTA)}|^^^THEATER HX${if (a02Data) "^^^^^drop off location" else ""}|||${if (a02Data) "^^^^^^^^pickup location" else ""}|123456^providerlast^providerfirst|||||||||||HX|||||||||||||||||||||||D|||20230627122108+0000|ttt"
    val cdp = "PV1|1|${pv102PatientClassCode(Endpoint.TAC)}|||||123456^providerlast^providerfirst|||||||||||HX||||||||||||||||||||||||||20230627122108+0000|ttt"
    return when (hl7Data.endpoint?.format) {
        EndpointType.MHSG_T -> gt
        EndpointType.OMDS -> omds
        EndpointType.TAC -> cdp
        else -> ""
    }
}
