package mil.af.afrl.batman.hl7lib.inbound

import androidx.annotation.CallSuper
import gov.afrl.batdok.encounter.BloodType
import gov.afrl.batdok.encounter.PatcatService
import gov.afrl.batdok.encounter.Service
import gov.afrl.batdok.encounter.movement.EvacStatus
import gov.afrl.batdok.encounter.movement.TriageCategory
import gov.afrl.batdok.encounter.panel.KnownLabs
import gov.afrl.batdok.encounter.treatment.ChestSealData
import gov.afrl.batdok.encounter.vitals.BloodPressure
import gov.afrl.batdok.encounter.vitals.EtCO2
import gov.afrl.batdok.encounter.vitals.GCS
import gov.afrl.batdok.encounter.vitals.Pain
import gov.afrl.batdok.encounter.vitals.Resp
import gov.afrl.batdok.encounter.vitals.SpO2
import gov.afrl.batdok.encounter.vitals.Temp
import io.mockk.coEvery
import io.mockk.mockkObject
import kotlinx.coroutines.test.runTest
import mil.af.afrl.batman.hl7lib.converter.DBAllergyConverter
import mil.af.afrl.batman.hl7lib.converter.LocationInjuryConverter
import mil.af.afrl.batman.hl7lib.converter.TreatmentConverter
import mil.af.afrl.batman.hl7lib.converter.MedicationConverter
import mil.af.afrl.batman.hl7lib.converter.MoiConverter
import mil.af.afrl.batman.hl7lib.converter.RouteConverter
import mil.af.afrl.batman.hl7lib.converter.UnitConverter
import mil.af.afrl.batman.hl7lib.data.models.v231.field.SNOMED_CT
import mil.af.afrl.batman.hl7lib.data.models.v231.field.TACICD10
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.message.MessageReceiver
import mil.af.afrl.batman.hl7lib.outbound.buildMapEntry
import mil.af.afrl.batman.hl7lib.util.MockkRule
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Rule
import org.junit.Test
import java.io.ByteArrayInputStream

class TacInboundPatientTest {

    @Before
    @CallSuper
    fun setup() {
        // Clear old codes set in earlier tests
        TreatmentConverter.therapeuticProcedures.clear()
        TreatmentConverter.diagnosticProcedures.clear()
        TreatmentConverter.unclassifiedProcedures.clear()
        MedicationConverter.map.clear()
        RouteConverter.map.clear()
        UnitConverter.map.clear()
        MoiConverter.map.clear()
        LocationInjuryConverter.clearData()
    }

    // TAC also sends immunizations, but we don't care about those, so they're not tested
    @get:Rule val mockkRule = MockkRule(this, relaxed = true)

    @Test
    fun testADT_A04() = runTest {
        // ARRANGE
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|OMDS|OMDS|20240215162356+0000||ADT^A04|71527ab0-a3b4-3952-7ab0-32736fe145b0|T|2.3|
            EVN|A04|20240215162356+0000|
            PID|1||**********^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI||Shirin^Rustam||19830203|M||||||||||||||||||||USA
            PV1|1|||||||||||||||||||||||||||||||||||||||||||20240123164900+0000
            ACC|20240123164900+0000
        """.trimIndent().replace("\n", "\r")

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals("**********", document.info.dodId ?: "")
        Assert.assertEquals("Rustam", document.info.name?.first ?: "")
        Assert.assertEquals("", document.info.name?.middle ?: "")
        Assert.assertEquals("Shirin", document.info.name?.last ?: "")
        Assert.assertEquals("1983-02-03", document.info.dateOfBirth.dob?.toString() ?: "")
        Assert.assertEquals("Male", document.info.gender ?: "")
        Assert.assertEquals("United States of America", document.info.nationality?.dataString ?: "")
        Assert.assertEquals(1706028540L, document.info.timeInfo?.epochSecond)  // Jan 23, 2024 @ 16:49:00 UTC
    }

    @Test
    fun testADT_A28_injuries() = runTest {
        // ARRANGE
        val testInjuryCodes = listOf(
            "Name\tDefault\t",
            "Amputation\tZ89.9",
            "Burn\tT30.0",
            "Fire\tT14.8",
            "Crush\tT14.8XXA"
        ).joinToString("\n")
        LocationInjuryConverter.loadData(testInjuryCodes.byteInputStream(), TACICD10)

        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20240731150103-0400||ADT^A28|13cc580f-3a2f-4897-a46c-c921c06fe1ac|T|2.3|
            EVN|A28|20240731150103-0400|
            PID|1||**********^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI|c196bd90-65ab-40c8-8e32-b0f029945873|Test^Injury^|||M||||||||||FIN NBR||||||||||
            DG1|1||T14.8^Fire^ICD10||20230627120104+0000|A
            DG1|2||Z89.9^Amputation^ICD10||20230627120104+0000|A
            ZDG|Anterior|Right Hand
            DG1|3||Z89.9^Amputation^ICD10||20230627120104+0000|A
            ZDG|Anterior|Right Lower Leg
            DG1|4||T14.8XXA^Crush^ICD10||20230627120104+0000|A
            DG1|5||T30.0^Burn^ICD10||20230627120104+0000|A
            ZDG|Posterior|Left Upper Leg
            DG1|6||Z89.9^Amputation^ICD10||20230627120104+0000|A
            ZDG|Anterior|Left Hand
        """.trimIndent().replace("\n", "\r")

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        val injury = document.injuries.injuries
        val drawingPoint = document.injuries.drawingPoints
        Assert.assertNotNull(injury)
        Assert.assertEquals(0, document.injuries.mechanismsOfInjury.size)
        Assert.assertEquals(4, injury[null]!!.size)
        Assert.assertEquals(4, drawingPoint.size)
        Assert.assertEquals("DrawingPoint(label=AMP, x=0.08, y=0.53)", drawingPoint.first().toString())
        Assert.assertEquals("DrawingPoint(label=AMP, x=0.42, y=0.53)", drawingPoint.last().toString())
        Assert.assertEquals("Fire", injury.values.first().first().injury)
        Assert.assertEquals("Burn", injury.values.last().last().injury)
    }

    @Test
    fun testADT_A28_mois() = runTest {
        // ARRANGE
        MoiConverter.map.putAll(mapOf(
            buildMapEntry("Aircraft Incident", "V95.9XXA", TACICD10),
            buildMapEntry("Animal Bite/Mauling", "W55.81XA", TACICD10),
            buildMapEntry("Asphyxiation", "R09.01", TACICD10),
            buildMapEntry("Assault", "Y09", TACICD10),
            buildMapEntry("CBRN", "Z77.29", TACICD10),
            buildMapEntry("Blast", "W40.9XXA", TACICD10),
            buildMapEntry("Fall", "W19.XXXA", TACICD10),
            buildMapEntry("Impalement", "W45.8XXA", TACICD10),
            buildMapEntry("Motor Vehicle Collision", "V89.9XXA", TACICD10),
            buildMapEntry("Stab Wound", "Y28.9XXA", TACICD10),
            buildMapEntry("Sports", "Y93.79", TACICD10),
            buildMapEntry("Parachute Incident/Airborne Operations", "V97.29XA", TACICD10),
            buildMapEntry("Crush", "T14.8XXA", TACICD10),
            buildMapEntry("Burn", "T30.0", TACICD10),
            buildMapEntry("Gunshot Wound", "Y24.9XXA", TACICD10)
        ))

        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20250224135000+0000||ADT^A28|f776d1c4-9085-4499-a344-bf831e731bc6|T|2.3
            EVN|A28|20250224135000+0000
            PID|1|||DFDD827D-7D32-4B8B-AC1C-B4267FD686BE^^^2.16.840.1.113883.3.42.10032.100001.13^TAC|UT-DFDDD^SOLO||19470224|M
            DG1|1||V95.9XXA^Aircraft incident^ICD-10^MOI||20250224134922+0000|A
            DG1|2||W55.81XA^Animal bite / sting^ICD-10^MOI||20250224134924+0000|A
            DG1|3||R09.01^Asphyxiation^ICD-10^MOI||20250224134925+0000|A
            DG1|4||Y09^Assault^ICD-10^MOI||20250224134927+0000|A
            DG1|5||W40.9XXA^Blast^ICD-10^MOI||20250224134929+0000|A
            DG1|6||T30.0^Burn^ICD-10^MOI||20250224134933+0000|A
            DG1|7||Z77.29^CBRN^ICD-10^MOI||20250224134935+0000|A
            DG1|8||T14.8XXA^Crush^ICD-10^MOI||20250224134938+0000|A
            DG1|9||W19.XXXA^Fall^ICD-10^MOI||20250224134940+0000|A
            DG1|10||Y24.9XXA^Gunshot wound (GSW)^ICD-10^MOI||20250224134943+0000|A
            DG1|11||W45.8XXA^Impalement^ICD-10^MOI||20250224134945+0000|A
            DG1|12||V89.9XXA^Motor vehicle collision^ICD-10^MOI||20250224134948+0000|A
            DG1|13||V97.29XA^Parachute incident / airborne operations^ICD-10^MOI||20250224134950+0000|A
            DG1|14||Y93.79^Sports^ICD-10^MOI||20250224134953+0000|A
            DG1|15||Y28.9XXA^Stab wound^ICD-10^MOI||20250224134955+0000|A
        """.trimIndent().replace("\n", "\r")

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        val mois = document.injuries.mechanismsOfInjury[null]
        Assert.assertNotNull(mois)
        Assert.assertEquals(15, mois!!.size)
        Assert.assertEquals("Aircraft Incident", mois[0])
        Assert.assertEquals("Animal Bite/Mauling", mois[1])
        Assert.assertEquals("Asphyxiation", mois[2])
        Assert.assertEquals("Assault", mois[3])
        Assert.assertEquals("Blast", mois[4])
        Assert.assertEquals("Burn", mois[5])
        Assert.assertEquals("CBRN", mois[6])
        Assert.assertEquals("Crush", mois[7])
        Assert.assertEquals("Fall", mois[8])
        Assert.assertEquals("Gunshot Wound", mois[9])
        Assert.assertEquals("Impalement", mois[10])
        Assert.assertEquals("Motor Vehicle Collision", mois[11])
        Assert.assertEquals("Parachute Incident/Airborne Operations", mois[12])
        Assert.assertEquals("Sports", mois[13])
        Assert.assertEquals("Stab Wound", mois[14])
        Assert.assertEquals(0, document.injuries.injuries.size)
    }

    @Test
    fun testADT_A28_treatments() = runTest {
        // ARRANGE

        TreatmentConverter.therapeuticProcedures.putAll(
            mapOf(
                buildMapEntry("Direct Pressure", "735346000", SNOMED_CT),
                buildMapEntry("NPA","182692007", SNOMED_CT),
                buildMapEntry("Chest Seal","464094004", SNOMED_CT)
            )
        )
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20240913131503+0000||ADT^A28|52590246-3f7e-49e7-8a15-73750ec012aa|T|2.3|
            EVN|A28|20240913131503+0000|
            PID|1||^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI|6CAB3872-FCB8-4274-BB6E-710A73DD04EF|treatments^test^||19891112|M||||||||||FIN NBR||||||||||
            PR1|1||735346000^Direct pressure^SCT||20240913125912+0000|P
            PR1|2||182692007^Nasal airway (NPA)^SCT||20240913125921+0000|P
            PR1|3||464094004^Chest seal^SCT||20240913125925+0000|P
        """.trimIndent().replace("\n", "\r")

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        val treatments = document.treatments.list
        Assert.assertEquals(3, treatments.size)
        Assert.assertEquals("Direct Pressure", treatments[0].name)
        Assert.assertNull(treatments[0].treatmentData)
        Assert.assertEquals(null, treatments[0].treatmentData?.toDetailString())
        // If NPA shows up here instead of code text, we matched the code correctly
        Assert.assertEquals("NPA", treatments[1].name)
        Assert.assertNull(treatments[1].treatmentData)
        Assert.assertEquals(null, treatments[1].treatmentData?.toDetailString())
        Assert.assertEquals("Chest Seal", treatments[2].name)
        Assert.assertTrue(treatments[2].treatmentData is ChestSealData)
    }

    @Test
    fun testADT_A28_serviceUnit() = runTest {
        // ARRANGE
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20240802084037-0400||ADT^A28|e64035d2-fef5-40f4-b42d-8400cdb6ddb8|T|2.3|
            EVN|A28|20240802084037-0400|
            PID|1||^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI||UT-EEDFA^XERO^|||F||||||||||FIN NBR||||||||||
            ZMI|1|||711hpw
            ZEI|1||||||||||AR00||||
            ZPI|1||||B+
        """.trimIndent().replace("\n", "\r")

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals(Service.ARMY.abbreviation, document.info.service)
        Assert.assertEquals("711hpw", document.info.unit)
        Assert.assertEquals(PatcatService.ARMY, document.info.patcat?.service)
        Assert.assertEquals(BloodType.B_POSITIVE.dataString, document.info.bloodType)
    }

    @Test
    fun testADT_A31() = runTest {
        // ARRANGE
        mockkObject(DBAllergyConverter)
        coEvery { DBAllergyConverter.nameToBatdokData(any()) } returns null

        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|OMDS|OMDS|20240215162356+0000||ADT^A31|81527ab0-abcd-4951-bbb0-32736fe145ab|T|2.3|
            EVN|A31|20240215162356+0000|
            PID|1||**********^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI||Shirin^Rustam||19830203|M|
            PV1|1|||||||||||||||||||||||||||||||||||||||||||20240123164900+0000
            AL1|1|DA|161^Acetaminophen^RxCUI|NE||20240123165707+0000
            ZAL|ADD|||||Active|62315008^Diarrhea^SCT|Patient
            ZAL|ADD|||||Active|49727002^Cough^SCT|Patient
            AL1|2|DA|d03751^12 Hour Cold^MULTUMDRUG|NE||20240913174024+0000
            DG1|1||T14.8^Fire^ICD10||20230627120104+0000|A
            DG1|2||Z89.9^Amputation^ICD10||20230627120104+0000|A
            ZDG|Anterior|Right Wrist
            DG1|3||Z89.9^Amputation^ICD10||20230627120104+0000|A
            ZDG|Anterior|Right Lower Leg
            DG1|4||T14.8XXA^Crush^ICD10||20230627120104+0000|A
            DG1|5||T30.0^Burn^ICD10||20230627120104+0000|A
            ZDG|Posterior|Left Upper Leg
            DG1|6||Z89.9^Amputation^ICD10||20230627120104+0000|A
            ZDG|Anterior|Left Hand
            PR1|1||20655006^TQ^CPT4|TQ (Location: EXTREMITY, Sub-Location: Left Arm)|20230627121501+0000|P
            PR1|2||20655006^TQ^CPT4|TQ (Location: EXTREMITY, Sub-Location: Right Leg)|20230627121504+0000|P
            PR1|3||20655006^TQ^CPT4|TQ (Location: JUNCTIONAL)|20230627121505+0000|P
            PR1|4||20655006^TQ^CPT4|TQ (Location: TRUNCAL)|20230627121505+0000|P
            PR1|5||3895009^Dressing^CPT4|Dressing (Type: PRESSURE)|20230627121509+0000|P
            PR1|6||99291^Critical care, evaluation and management of the critically ill or critically injured patient; first 30-74 minutes^CPT4|INTACT|20230627121511+0000
            PR1|7||57485005^O2^CPT4|O2|20230627121513+0000|P
            PR1|8||264957007^Chest Tube^CPT4|CHEST_TUBE (Location: Right)|20230627121517+0000|P
            PR1|9||182531007^Chest Seal^CPT4|CHEST_SEAL (LeftFront)|20230627121520+0000|P
            PR1|10||79321009^Splint^CPT4|SPLINT (Pulse present)|20230627121524+0000|P
            PR1|11||99291^Critical care, evaluation and management of the critically ill or critically injured patient; first 30-74 minutes^CPT4|Escharotomy|20230627121529+0000
        """.trimIndent().replace("\n", "\r")

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!
        val allergies = document.info.allergies

        // ASSERT
        Assert.assertEquals(2, document.info.allergies.size)
        Assert.assertEquals("Acetaminophen", allergies[0])
        Assert.assertEquals("12 Hour Cold", allergies[1])

        val injury = document.injuries.injuries
        val drawingPoint = document.injuries.drawingPoints
        Assert.assertNotNull(injury)
        Assert.assertEquals(0, document.injuries.mechanismsOfInjury.size)
        Assert.assertEquals(4, injury[null]!!.size)
        Assert.assertEquals(4, drawingPoint.size)
        Assert.assertEquals("DrawingPoint(label=AMP, x=0.09, y=0.48)", drawingPoint.first().toString())
        Assert.assertEquals("DrawingPoint(label=AMP, x=0.42, y=0.53)", drawingPoint.last().toString())
        Assert.assertEquals("Fire", injury.values.first().first().injury)
        Assert.assertEquals("Burn", injury.values.last().last().injury)
    }

    @Test
    fun testORM_O01() = runTest {
        // TODO: Update this once we get a good sample
        // ARRANGE
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|OMDS|OMDS|20240215162356+0000||ORM^O01|91527ab0-abcd-a951-bbb0-32736fe145b0|T|2.3|
            PID|1||**********^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI||Shirin^Rustam||19830203|M|
            PV1|1|||||||||||||||||||||||||||||||||||||||||||20240123164900+0000
            ORC|RE||a1ca642f-c2b5-4069-ad58-29668ebb1230|CM
            RXO|54738054803^ACETAMINOPHEN 325 MG TABLET 1000S^LOINC|||
            RXR|PO
            ORC|RE||a1ca642f-c2b5-4069-ad58-29668ebb1231|CM
            RXO|299591045^ADAPALENE 0.1% TOPICAL GEL 45GM^LOINC|||
            RXR|PO
            ORC|RE||a1ca642f-c2b5-4069-ad58-29668ebb1233|CM
            RXO|121050404^ACETAMINOPHEN 120 MG-CODEINE 12 MG/5 ML ORAL SOLUTION 118ML C-V^LOINC|||
            RXR|PO
        """.trimIndent().replace("\n", "\r")

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        val meds = document.medicines.list
        Assert.assertEquals(3, meds.size)
        Assert.assertEquals("ACETAMINOPHEN 325 MG TABLET 1000S", meds[0].name)
        Assert.assertEquals("ADAPALENE 0.1% TOPICAL GEL 45GM", meds[1].name)
        Assert.assertEquals("ACETAMINOPHEN 120 MG-CODEINE 12 MG/5 ML ORAL SOLUTION 118ML C-V", meds[2].name)
    }

    @Test
    fun testORU_R01_labs() = runTest {
        // ARRANGE
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20250503231333+0000||ORU^R01|98c207ad-462c-486a-8727-b4e5a2810eb8|T|2.3
            PID|1|||1641D691-0339-4329-9472-D7C8AB7385C6^^^2.16.840.1.113883.3.42.10032.100001.13^TAC|labs^all^the||20180503|F
            PV1|1|E
            ORC|CM|2E0C3495-A092-4CF6-967F-AC5DEB6C1D0C
            OBR|1|||101239539^*Differential Automated^GENESISAlias|||20250503230830+0000|||||||||||||||||GLB|F
            ZUI|1|BA43FA98-00C8-4E15-B401-35DAD93F5A50|UNKNOWN
            OBX|1|NM|102596353^Mono Absolute^GENESISAlias||8|x1000/mcL|||||F|||20250503230830+0000
            OBX|2|NM|102593677^Lymph Absolute^GENESISAlias||7|x1000/mcL|||||F|||20250503230830+0000
            OBX|3|NM|102597277^Lymphocyte % Auto^GENESISAlias||7|%|||||F|||20250503230830+0000
            OBX|4|NM|102599689^Monocyte % Auto^GENESISAlias||67|%|||||F|||20250503230830+0000
            OBX|5|NM|121871521^Imm. Granulocyte %^GENESISAlias||76|%|||||F|||20250503230830+0000
            OBX|6|NM|122771511^Granulocytes, Abs^GENESISAlias||78|x1000/mcL|||||F|||20250503230830+0000
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20250503231333+0000||ORU^R01|50d6136c-721c-4cc8-a145-0a74ae2f4885|T|2.3
            PID|1|||1641D691-0339-4329-9472-D7C8AB7385C6^^^2.16.840.1.113883.3.42.10032.100001.13^TAC|labs^all^the||20180503|F
            PV1|1|E
            ORC|CM|D6BC7A96-F124-49CE-8B6C-F64C0F5C8B5E
            OBR|1|||2921414^CBC w/ Diff^GENESISAlias|||20250503231017+0000|||||||||||||||||GLB|F
            ZUI|1|987A29CD-6876-45D6-88F6-3699E6AB9C07|UNKNOWN
            OBX|1|NM|102606671^Hematocrit^GENESISAlias||43|%|||||F|||20250503231017+0000
            OBX|2|NM|102594769^MPV^GENESISAlias||9|fL|||||F|||20250503231017+0000
            OBX|3|NM|102593461^RBC^GENESISAlias||4|x1000000/mcL|||||F|||20250503231017+0000
            OBX|4|NM|102596851^MCV^GENESISAlias||99|fL|||||F|||20250503231017+0000
            OBX|5|NM|102595669^Platelets^GENESISAlias||34|x1000/mcL|||||F|||20250503231017+0000
            OBX|6|NM|102599089^Hemoglobin^GENESISAlias||4|g/dL|||||F|||20250503231017+0000
            OBX|7|NM|102597493^WBC^GENESISAlias||34|x1000/mcL|||||F|||20250503231017+0000
        """.trimIndent().replace("\n", "\r")

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!
        val labs = document.labs.list
        
        // ASSERT
        Assert.assertEquals(13, document.labs.size)
        fun assertLab(value: String, lab: KnownLabs) = Assert.assertEquals(value, document.labs[lab][0].value)
        assertLab("8", KnownLabs.ABS_MONOCYCLE)
        assertLab("7", KnownLabs.ABS_LYMPHOCYTE)
        assertLab("7", KnownLabs.LYMPHOCYTE)
        assertLab("67", KnownLabs.MONOCYTE)
        assertLab("76", KnownLabs.GRANULOCYTE)
        assertLab("78", KnownLabs.ABS_GRANULOCYTE)
        assertLab("43", KnownLabs.HCT)
        assertLab("9", KnownLabs.MPV)
        assertLab("4", KnownLabs.RBC)
        assertLab("99", KnownLabs.MCV)
        assertLab("34", KnownLabs.PLT)
        assertLab("4", KnownLabs.HGB)
        assertLab("34", KnownLabs.WBC)
    }

    @Test
    fun testORU_R01_simple_vitals() = runTest {
        // TODO: Merge into the following test when we get a better sample from T6
        // ARRANGE
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|OMDS|OMDS|20240710161009-0400||ORU^R01|41b6d52f-88cb-4f73-b812-23bdf8d1238f|T|2.3|
            EVN|R01|20240710161009-0400|
            PID|1||1231312321^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI||VSTest^Vitals^TestMiddle||19990710|M||||||||||FIN NBR
            PV1|1|||||||||||||||||||||||||||||||||||||||||||20240710160553-0400
            ORC|RE||
            OBR|1|||VITALSIGNS|||20240710160909-0400||||||||||||||||||F
            ZUI|1|927E24BB-809D-4C4E-882A-7B55A95B3439
            OBX|1||4247425^PainScale^LOINC||NT||||||F|||20240710160940-0400
            ZUI|1|6D0ADB6B-5318-408F-823C-B352AB5CB338
            OBX|2||2820736^Temp^LOINC||-15.556|C|||||F|||20240710160940-0400
            ZUI|2|D3216E4A-524D-41DE-A50A-1A42B4A372E1
            OBX|3||75539-7^TempRoute^LOINC||Temporal||||||F|||20240710160909-0400
            ZUI|3|022E5129-358B-470A-B8D3-4D57AC95FDCE
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals(1, document.vitals.vitalList.size)
        val vital = document.vitals.list.first()
        Assert.assertEquals("NT", vital[Pain::class]?.painScore)
        Assert.assertEquals(3.9992008f, vital[Temp::class]?.temp)
        Assert.assertEquals("TPRL", vital[Temp::class]?.measurementMethod)
    }

    @Ignore("Needs updates from T6")
    @Test
    fun testORU_R01_vitals() = runTest {
        // FIXME: Only SPO2 and EtCO2 work as-is, others we need their updated code mappings for
        // ARRANGE
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|OMDS|OMDS|20240215162356+0000||ORU^R01|e2527ab0-a3b4-4951-bbb0-32736fe145cc|T|2.3|
            EVN|R01|20240215162356+0000|
            PID|1||**********^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI||Shirin^Rustam||19830203|M|
            PV1|1|||||||||||||||||||||||||||||||||||||||||||20240123164900+0000
            ORC|RE||a1ca642f-c2b5-4069-ad58-29668ebb1230
            OBR|1|||VITALSIGNS|||20240123165510+0000||||||||||||||||||F
            ZUI|1|51442845-9ecb-4db9-82dd-cf2aaff4d9f9
            OBX|1||^VitalsEcgStripRhythm^||1st degree||||||F|||20240123165510+0000
            ZUI|1|d84bad2c-52b7-4e47-94af-c66ec98a00ad
            OBX|2||2695127^RespiratoryRate^LOINC||15|bpm|||||F|||20240123165510+0000
            ZUI|2|03a5d8ec-ed03-4447-820f-dceba1f6c366
            OBX|3||2544737^HeartRate^LOINC||88|bpm|||||F|||20240123165510+0000
            ZUI|3|05657a67-8a99-4a88-bbd3-1dfd527028e9
            OBX|4||2348876^TempRoute^LOINC||Oral||||||F|||20240123165510+0000
            ZUI|4|3ebd59d7-8ae0-4f16-ad78-75d4a1731aa7
            OBX|5||3623991^RespiratorySao2^LOINC||95|%|||||F|||20240123165510+0000
            ZUI|5|0a617b4a-40bf-40c7-a68a-06a632a48e62
            OBX|6||2700544^EndTitalCO2Level^LOINC||40|mmHg|||||F|||20240123165510+0000
            ZUI|6|bf214b55-34e3-4294-9f3c-e615520f96a6
            OBX|7||^PainScale^||4||||||F|||20240123165510+0000
            ZUI|7|eb2f16de-b095-443d-b76b-e5d9b509e9dd
            OBX|8||8478-0^BloodPressureMAP^LOINC||93|mmHg|||||F|||20240123165510+0000
            ZUI|8|e091be54-02d6-4c1a-bc19-67e04d58281a
            OBX|9||703501^BloodPressureSystolic^LOINC||120|mmHg|||||F|||20240123165510+0000
            ZUI|9|a4041a46-20bb-42cf-8a3a-99b8302bcbd8
            OBX|10||280736^Temp^LOINC||37.222|C|||||F|||20240123165510+0000
            ZUI|10|ebdacf7b-aa87-4fcc-979f-0f17f14600b1
            OBX|11||703516^BloodPressureDiastolic^LOINC||80|mmHg|||||F|||20240123165510+0000
            ZUI|11|a3aad41b-25f7-4f0c-b4bf-78a4cda48dda
            OBX|12|ST|703584^GCS Eye response^LOINC||2||||||F|||20230627120937+0000
            ZUI|12|a3aad41b-25f7-4f0c-b4bf-78a4cda48abc
            OBX|13|ST|703786^GCS Verbal response^LOINC||5||||||F|||20230627120937+0000
            ZUI|13|a3aad41b-25f7-4f0c-b4bf-79a4cda48abc
            OBX|14|ST|703783^GCS Motor response^LOINC||6||||||F|||20230627120937+0000
            ZUI|14|a3aad41b-28f7-4f0c-b4bf-79a4cda48abc
            OBX|15|NM|34601069^Total GCS^LOINC||13||||||F|||20230627120937+0000
            ZUI|15|a3aac41b-28f7-4f0c-b4bf-79a4cda48abc
        """.trimIndent().replace("\n", "\r")

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals(1, document.vitals.vitalList.size)
        val vital = document.vitals.vitalList.first()
        Assert.assertEquals(15, vital[Resp::class]?.resp)
        Assert.assertEquals(95, vital[SpO2::class]?.spo2)  // Only this and ETco2 work as is
        Assert.assertEquals(40, vital[EtCO2::class]?.etco2)
        Assert.assertEquals(4, vital[Pain::class]?.pain)
        Assert.assertEquals(120, vital[BloodPressure::class]?.systolic)
        Assert.assertEquals(80, vital[BloodPressure::class]?.diastolic)
        Assert.assertEquals(37.222, vital[Temp::class]?.temp?.toDouble() ?: 0.0, 0.1)
        Assert.assertEquals(0, vital[GCS::class]?.motor)
        Assert.assertEquals(8, vital[GCS::class]?.verbal)
        Assert.assertEquals(9, vital[GCS::class]?.eye)
    }

    @Test
    fun testORU_R01_radiology() = runTest {
        // TODO: Update sample message when we get an update... I modified this message to match what we're expected to get
        // ARRANGE
        val msg = """
            MSH|^~\&|TAC|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|OMDS|OMDS|20240215162356+0000||ORU^R01|3d527ab0-a3b4-4951-7bb5-32736fe145af|T|2.3|
            EVN|R01|20240215162356+0000|
            PID|1||**********^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI||Shirin^Rustam||19830203|M|
            PV1|1|||||||||||||||||||||||||||||||||||||||||||20240123164900+0000
            ORC|RE||a1ca642f-c2b5-4069-ad58-29668ebb1230
            OBR|1|||RADIOLOGY|||20240130175209+0000||||||||RAD|||||||||XR|F
            ZUI|1|01dfb508-0695-48d9-aa8d-264fc4c8d8ba
            OBX|1|TX|Report^ABDXRResults||Abnormal|||A|||F|||20240130175209+0000
            OBX|2|TX|Report^OtherResults||Things look very bad|||A|||F|||20240130175209+0000
            ZUI|8|73399f8c-0cb8-4a50-8e9f-e280ff70bf08
            MSH|^~\&|TAC|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|OMDS|OMDS|20240215162356+0000||ORU^R01|84527ab0-a3b4-4951-bbb7-32736fe14541|T|2.3|
            EVN|R01|20240215162356+0000|
            PID|1||**********^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI||Shirin^Rustam||19830203|M|
            PV1|1|||||||||||||||||||||||||||||||||||||||||||20240123164900+0000
            ORC|RE||a1ca642f-c2b5-4069-ad58-29668ebb1230
            OBR|1|||RADIOLOGY|||20240130175209+0000||||||||RAD|||||||||XR|F
            ZUI|1|cf544f4d-bf3c-448b-9e44-e5e40830f180
            OBX|1|TX|Report^LSpineXRResults||Normal||||||F|||20240130175209+0000
            ZUI|8|0b9d961e-39e5-4521-af1d-e45d3de9eee7
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20240712143019-0400||ORU^R01|f8c8a601-3c42-4f5c-bf47-08fbba7a8167|T|2.3|
            EVN|R01|20240712143019-0400|
            PID|1||^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI||UT-FEFFC^RIGS^|||M||||||||||FIN NBR
            PV1|1|||||||||||||||||||||||||||||||||||||||||||20240712142729-0400
            ORC|RE||
            OBR|1|||RADIOLOGY|||20240712142740-0400||||||||RAD||||||||||F
            ZUI|1|CDF3E5E8-21D1-4E13-9FEC-7AF03268018F
            OBX|1|TX|RPID4675^CXRView^RADLEX||PA||||||F|||20240712142740-0400
            ZUI|1|8C74D294-457A-4363-8763-A3DBECDA57A0
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        // This info only will appear in our events log
        val customEvents = document.events.list.filter { it.isCustom }
        Assert.assertEquals(3, customEvents.size)
        Assert.assertEquals("Radiology report: ABDXRResults: Abnormal; OtherResults: Things look very bad", customEvents[0].event)
        Assert.assertEquals("Radiology report: LSpineXRResults: Normal", customEvents[1].event)
        Assert.assertEquals("Radiology report: CXRView: PA", customEvents[2].event)
    }

    @Test
    fun testORU_R01_notes() = runTest {
        // TODO: Replace with real note when CDP implements this!
        // ARRANGE
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20240712143019-0400||ORU^R01|f8c8a601-3c42-4f5c-bf47-08fbba7a8167|T|2.3|
            EVN|R01|20240712143019-0400|
            PID|1||^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI||UT-FEFFC^RIGS^|||M||||||||||FIN NBR
            PV1|1|||||||||||||||||||||||||||||||||||||||||||20240712142729-0400
            OBR|1|||18761-7^Transfer summary note^LOINC^^BATDOK R1 to R3 Note|||20240712142729-0400|||||||||||||||20240712142729-0400|||F
            ZUI|1|ididid|UNKNOWN
            OBX|1|TX|18761-7^Transfer summary note^LOINC||test note sub (subjective)||||||F|||20230627121733+0000
            OBX|2|TX|18761-7^Transfer summary note^LOINC||test note plan (plan)||||||F|||20230627121744+0000
            OBX|3|TX|18761-7^Transfer summary note^LOINC||note\F\with\R\control\S\characters\T\\X000A\newline (plan)||||||F|||20230627121744+0000
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        val customEvents = document.events.list.filter { it.isCustom }
        Assert.assertEquals(3, customEvents.size)
        Assert.assertEquals("Transfer summary note (BATDOK R1 to R3 Note): test note sub (subjective)", customEvents[0].event)
        Assert.assertEquals("Transfer summary note (BATDOK R1 to R3 Note): test note plan (plan)", customEvents[1].event)
        Assert.assertEquals("Transfer summary note (BATDOK R1 to R3 Note): note|with\ncontrol^characters&\nnewline (plan)", customEvents[2].event)
    }

    @Test
    fun testORU_R01_evacCategory() = runTest {
        // TODO: Replace with real note when CDP implements this!
        // ARRANGE
        val msg = """
            MSH|^~\&|CDP||CDP||20240712143019-0400||ORU^R01|yyy|P|2.3
            PID|1||**********^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI|randomid^^^2.16.840.1.113883.3.42.10033.100001.13^BATDOK|patient^test^j^^^^L||20030627|M||||||||||morandomid||||||||||USA^United States of America^ISO 3166
            PV1|1||||||123456^providerlast^providerfirst|||||||||||HX||||||||||||||||||||||||||20230627120104+0000
            OBR|1|||77153-5^EMS Transport mode descriptors NEMSIS^LOINC|||20240712143019-0400|||||||||||||||20240712143019-0400|||F
            ZUI|1|otherid|UNKNOWN
            OBX|1|TX|D^Routine^PRECEDENCE||Routine||||||F|||20240712143019-0400
            MSH|^~\&|CDP||CDP||20240712143019-0400||ORU^R01|yyy|P|2.3
            PID|1||**********^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI|randomid^^^2.16.840.1.113883.3.42.10033.100001.13^BATDOK|patient^test^j^^^^L||20030627|M||||||||||morandomid||||||||||USA^United States of America^ISO 3166
            PV1|1||||||123456^providerlast^providerfirst|||||||||||HX||||||||||||||||||||||||||20230627120104+0000
            OBR|1|||67493-7^Initial patient acuity NEMSIS^LOINC|||20240712143019-0400|||||||||||||||20240712143019-0400|||F
            ZUI|1|anyid|UNKNOWN
            OBX|1|TX|LA17694-3^Minimal^LOINC||Minimal||||||F|||20240712143019-0400
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals(TriageCategory.MINIMAL.dataString, document.info.triage)
        Assert.assertEquals(EvacStatus.ROUTINE.dataString,document.movement.dispatchEvac )
    }

    @Test
    fun testORU_R01_bloods() = runTest {
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20240919171425+0000||ORU^R01|71a083af-0cef-49c3-b77f-885a997a3771|T|2.3|
            PID|1||^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI|8593E1CC-22A5-44B2-A5AF-B29529B8D181|UT-ECCAB^GAEL^|||F||||||||||FIN NBR||||||||||
            PV1|1|||||||||||||||||||||||||||||||||||||||||||20240919164411+0000
            OBR|1|||79569-0^Blood product given [Type]^LOINC|||20240919164445+0000|||||||||||||||20240919164445+0000|||F
            ZUI|1|DB94FE4E-0926-4E6C-97D1-BB08E6E8396A|UNKNOWN
            OBX|1|ST|1^Fresh whole bood^BLOOD|||ml^mL^CS54|||||F|||20240919164445+0000
            OBX|2|ST|933-2^The type of blood contained in the blood bag^LOINC||A-||||||F|||20240919164445+0000
            OBX|3|ST|14579-7^ISBT 128 blood type code (ABO/Rh blood groups)^LOINC||din||||||F|||20240919164445+0000
            OBX|4|ST|E0112^Whole blood (WB)^IBST||Whole blood (WB)||||||F|||20240919164445+0000
            OBX|5|NM|936-5^ISBT 128 donation identification number (DIN)^LOINC||999||||||F|||20240919164445+0000
            OBX|6|ST|935-7^ISBT 128 Expiration date^LOINC||20240902164400+0000||||||F|||20240919164445+0000
            OBX|7|NM|104088002^ISBT 128 product code^SCT||000||||||F|||20240919164445+0000
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals(1, document.bloodList.size)
        val blood = document.bloodList.list.first()
        Assert.assertEquals("Fresh", blood.bloodProduct)
        Assert.assertEquals("A-", blood.bloodType)
        Assert.assertEquals("999", blood.donationIdNumber)
        Assert.assertEquals("20240902", blood.expirationDate)
        Assert.assertEquals("2024-09-19T16:44:45Z", blood.administrationTime.toString())
    }

    @Test
    fun testORU_R01_fluids() = runTest {
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20240918161822+0000||ORU^R01|722cf981-c52e-49e2-ab03-e726815ca249|T|2.3|
            PID|1||^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI|6607394C-9A3A-41FA-AA4E-8B21F47A9A78|UT-CAAFA^LOOP^|||M||||||||||FIN NBR||||||||||
            PV1|1|||||||||||||||||||||||||||||||||||||||||||20240918161733+0000
            OBR|1|||8975-5^Fluid intake intravascular Measured^LOINC|||20240918161739+0000|||||||||||||||20240918161739+0000|||F
            ZUI|1|F93EE988-370C-42A8-8C44-6560D78BBAE1|UNKNOWN
            OBX|1|NM|1741377^Albumin 25%^RxCUI||500|ml^mL^CS54|||||F|||20240918161739+0000
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals(1, document.medicines.size)
        val fluid = document.medicines.list.first()
        Assert.assertEquals("Albumin 25%", fluid.name)
        Assert.assertEquals(500.0f, fluid.volume)
        Assert.assertEquals("ml", fluid.unit)
    }

    @Test
    fun testORU_R01_maceNote() = runTest {
        // ARRANGE
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20240410183522+0000||ORU^R01|1-2-3-4-5|T|2.3
            PID|1|||dea5d1a2-0c40-42b3-869c-e44ca0ca9504^^^2.16.840.1.113883.3.42.10032.100001.13^TAC|||19000101|NA
            PV1|1|E
            OBR|1|||2820561^Transfer Note^GENESISAlias|||20240807120900+0000||||||||||||||||||F
            ZUI|1|AAD6DA3D-D767-4D00-9E97-F511A86133B6|UNKNOWN
            OBX|1|TX|2820561^Transfer Note^GENESISAlias||____________MACE 2 Results___________ ~ Neurological exam results: Abnormal ~ Cognitive results: 21 ~ Symptoms results: 1 or more symptoms ~ History results: Positive ~ MACE 2 results: Positive 07-Aug-24 1209||||||F|||20240807120900+0000
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        // There's also an event for opening encounter
        Assert.assertEquals(2, document.events.size)
        val maceEvent = document.events.list.find { it.event.contains("MACE 2 Results") }
        Assert.assertNotNull(maceEvent)
    }

}
