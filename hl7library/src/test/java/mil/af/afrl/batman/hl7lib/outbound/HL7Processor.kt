package mil.af.afrl.batman.hl7lib.outbound

class HL7Processor {
    fun replaceTimestamp(message: String, segment: String, fieldIndex: Int, replacement: String):String {
        return message.lines()
            .joinToString("\r") { line ->
                if (line.startsWith("$segment|")) {
                    val fields = line.split("|").toMutableList()
                    if (fields.size > fieldIndex){
                        fields[fieldIndex] = replacement
                    }
                    fields.joinToString("|")
                }else line
            }
    }
}