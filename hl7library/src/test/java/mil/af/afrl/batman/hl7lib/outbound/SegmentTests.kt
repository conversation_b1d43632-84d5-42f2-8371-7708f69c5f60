package mil.af.afrl.batman.hl7lib.outbound

import ca.uhn.hl7v2.DefaultHapiContext
import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.v231.message.ADT_A31
import ca.uhn.hl7v2.model.v231.message.ADT_AXX
import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.DocumentCommands.CommandData
import gov.afrl.batdok.encounter.Document
import gov.afrl.batdok.encounter.Gender
import gov.afrl.batdok.encounter.commands.buildAddRemoveAllergyListCommand
import gov.afrl.batdok.encounter.commands.buildChangeBloodTypeCommand
import gov.afrl.batdok.encounter.commands.buildChangeDOBCommand
import gov.afrl.batdok.encounter.commands.buildChangeDodIdCommand
import gov.afrl.batdok.encounter.commands.buildChangeGenderCommand
import gov.afrl.batdok.encounter.commands.buildChangeInjuryCommand
import gov.afrl.batdok.encounter.commands.buildChangeInjuryTimeCommand
import gov.afrl.batdok.encounter.commands.buildChangeNameCommand
import gov.afrl.batdok.encounter.commands.buildChangeOpenCloseEncounterCommand
import gov.afrl.batdok.encounter.commands.buildChangePatientIdCommand
import gov.afrl.batdok.encounter.commands.buildChangeRankCommand
import gov.afrl.batdok.encounter.commands.buildChangeServiceCommand
import gov.afrl.batdok.encounter.commands.buildChangeSsnCommand
import gov.afrl.batdok.encounter.commands.buildChangeUnitCommand
import gov.afrl.batdok.encounter.commands.buildPatientMaskingCommand
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.util.buildCommandData
import io.mockk.coEvery
import io.mockk.mockkObject
import mil.af.afrl.batdokdata.id.EncounterId
import mil.af.afrl.batman.batdokid.DomainId
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.converter.DBAllergyConverter
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.AL1Builder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.PIDBuilder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.PV1Builder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.PV2Builder
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.pv102PatientClassCode
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ExtendedCodedElement
import mil.af.afrl.batman.hl7lib.data.models.v231.segment.ZEI
import mil.af.afrl.batman.hl7lib.data.models.v231.customSegments.ZMI
import mil.af.afrl.batman.hl7lib.data.models.v231.segment.ZPI
import mil.af.afrl.batman.hl7lib.data.models.v231.segment.ZUI
import mil.af.afrl.batman.hl7lib.util.OutboundHL7Data
import mil.af.afrl.batman.hl7lib.util.ProviderInfo
import mil.af.afrl.batman.hl7lib.util.MockkRule
import mil.af.afrl.batman.hl7lib.util.asUuidString
import org.junit.Assert
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.openehealth.ipf.modules.hl7.kotlin.get
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId

abstract class AbstractSegmentTests {
    lateinit var msg: AbstractMessage

    @Before
    fun setUp() {
        val hapi = DefaultHapiContext()
        msg = hapi.newMessage(ADT_AXX::class.java)
        msg.initQuickstart("ADT", "A04", "P")
    }

    fun buildHl7Data( commands: List<Message> = emptyList()): OutboundHL7Data {
        return buildHl7DataFromCommandData(commands.map { buildCommandData(it) })
    }

    fun buildHl7DataFromCommandData(commandDataList: List<CommandData>): OutboundHL7Data {
        val hL7Data = OutboundHL7Data()
        val encounterId = DomainId.create<EncounterId>()
        hL7Data.activeEncounterId = encounterId
        hL7Data.commandsByEncounter = mapOf(encounterId to commandDataList)
        return hL7Data
    }

    @Test
    abstract fun encodeZMI()

    @Test
    abstract fun encodeZPI()

    @Test
    abstract fun encodeZVI()

    @Test
    abstract fun encodeZEI()

    @Test
    abstract fun encodeZUI()
}

class DirectPopulateSegmentTests : AbstractSegmentTests() {
    @get:Rule val mockkRule = MockkRule(this, relaxed = true)

    @Test
    override fun encodeZMI() {
        val segment = ZMI(msg)
        segment[1].parse("1")
        segment[4].parse("Awesome")
        segment[6].parse("ABC123")
        val sample = "ZMI|1|||Awesome||ABC123"
        assert(sample == segment.encode())
    }

    @Test
    override fun encodeZPI() {
        val segment = ZPI(msg)
        segment[1].parse("1")
        segment[5].parse("B-")
        segment[15].parse("HUMAN")
        val sample = "ZPI|1||||B-||||||||||HUMAN"
        assert(sample == segment.encode())
    }

    @Test
    override fun encodeZVI() {
    }

    @Test
    override fun encodeZEI() {
        val segment = ZEI(msg)
        segment[1].parse("1")
        segment[3].parse("USAF")
        segment[11].parse("AF00")
        segment[15].parse("1LT")
        val sample = "ZEI|1||USAF||||||||AF00||||1LT"
        Assert.assertEquals(sample, segment.encode())
    }

    override fun encodeZUI() {
        val segment = ZUI(msg).also {
            it[1].parse("1")
            it[2].parse("id")
            it[3].parse("type")
        }
        val expected = "ZUI|1|id|type"
        Assert.assertEquals(expected, segment.encode())
    }
}

class PopulateFromBatdokPatientSegmentTests : AbstractSegmentTests() {
    private val providerData = ProviderInfo("name", "dodid")
    @get:Rule val mockkRule = MockkRule(this, relaxed = true)

    override fun encodeZMI() {
        val commandList1 = listOf(
            buildChangeUnitCommand("Awesome"),
        )
        val hL7Data = buildHl7Data(commandList1)
        val segment = ZMI(msg)
        segment.populateFromBatdokPatient(msg, hL7Data)
        val sample = "ZMI|1|||Awesome"
        assert(sample == segment.encode())
    }

    override fun encodeZPI() {
        val commandList1 = listOf(
            buildChangeBloodTypeCommand("B-"),
        )
        val hL7Data = buildHl7Data(commandList1)
        val segment = ZPI(msg)
        segment.populateFromBatdokPatient(msg, hL7Data)
        val sample = "ZPI|1||||B-||||||||||HUMAN"
        assert(sample == segment.encode())
    }

    @Test
    fun encodeZPIWithWorkingDog() {
        val commandList1 = listOf(
            buildChangeBloodTypeCommand("B-"),
        )
        val hL7Data = buildHl7Data(commandList1)
        hL7Data.mode = "K9 TCCC"
        val segment = ZPI(msg)
        segment.populateFromBatdokPatient(msg, hL7Data)
        val sample = "ZPI|1||||B-||||||||||DOG"
        Assert.assertEquals(sample, segment.encode())
    }

    override fun encodeZVI() {
        // Not implemented - ZVI implementation is unstable
    }


    override fun encodeZEI() {
        val segment = ZEI(msg)
        segment[1].parse("1")

        val commandList1 = listOf(
            buildChangeServiceCommand("U.S. Air Force"),
            buildChangeRankCommand("1LT")
        )
        val hL7Data = buildHl7Data(commandList1)
        segment.populateFromBatdokPatient(msg, hL7Data)
        val sample = "ZEI|1||U.S. Air Force||||||||AF00||||1LT"
        Assert.assertEquals(sample, segment.encode())
    }

    override fun encodeZUI() {
        val hL7Data = OutboundHL7Data()
        val segment = ZUI(msg)
        segment.populateFromBatdokPatient(msg, hL7Data)
        val expected = "ZUI|1||UNKNOWN"
        Assert.assertEquals(expected, segment.encode())
    }

    @Test
    fun encodeSingleAL1_MHSG_T() {
        mockkObject(DBAllergyConverter)
        val msgMock = ADT_A31().apply { initQuickstart("ADT", "A31", "P") }
        coEvery { DBAllergyConverter.batdokDataToCodedElement(any(), any()) } returns ExtendedCodedElement(msgMock, "161", "Acetaminophen", "MULTUMDRUG")

        val commandList1 = listOf(
            buildAddRemoveAllergyListCommand(listOf("Unit Tests"), emptyList())
        ).map { buildCommandData(it) }
        val patient = Document().apply { handle(commandList1) }
        val segment = AL1Builder(Endpoint.MHSG_T).populateListFromBatdokPatient(msg, patient)[0]
        // BATDOK isn't specifying type, so all are being defaulted to OTHER
        val sample = "AL1|1|DA|161^Acetaminophen^MULTUMDRUG|NE"
        Assert.assertEquals(sample, segment.encode())
    }

    @Test
    fun encodeSingleAL1_OMDS() {
        mockkObject(DBAllergyConverter)
        val msgMock = ADT_A31().apply { initQuickstart("ADT", "A31", "P") }
        coEvery { DBAllergyConverter.batdokDataToCodedElement(any(), any()) } returns ExtendedCodedElement(msgMock, "161", "Acetaminophen", "MULTUMDRUG")

        val commandList1 = listOf(
            buildAddRemoveAllergyListCommand(listOf("Unit Tests"), emptyList())
        ).map { buildCommandData(it) }
        val patient = Document().apply { handle(commandList1) }
        val segment = AL1Builder(Endpoint.OMDS_DELTA).populateListFromBatdokPatient(msg, patient)[0]
        // BATDOK isn't specifying type, so all are being defaulted to OTHER
        val sample = "AL1|1|DA|161^Acetaminophen^MULTUMDRUG|NE"
        Assert.assertEquals(sample, segment.encode())
    }

    @Test
    fun encodePID_mhsg_t() {
        val commandList1 = listOf(
            buildChangeDodIdCommand("**********"),
            buildChangeNameCommand("John Doe"),
            buildChangeDOBCommand(LocalDate.ofInstant(Instant.ofEpochMilli(820414800000L), ZoneId.of("UTC"))),
            buildChangeSsnCommand("***********"),
            buildChangeGenderCommand(Gender.MALE),
            buildChangePatientIdCommand(DomainId.create())
        )
        val hL7Data = buildHl7Data(commandList1)
        // This field is a random UUID, manually reset for testing
         DomainId.fromSeeded(
            Instant.ofEpochMilli(820414800000L),
            "ea2641f0f5ae4d33".toByteArray(),
            DocumentId::class.java
        )
        val document = hL7Data.getDocumentForEncounter(hL7Data.activeEncounterId!!)

        val segment = PIDBuilder(Endpoint.MHSG_T).populateFromBatdokPatient(msg, hL7Data)

        val sample = "PID|1||" +
                "**********^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI|" + // DOD ID
                "${document!!.info.patientId.asUuidString()}^^^2.16.840.1.113883.3.42.10033.100001.13^BATDOK|" +  // Patient ID
                "Doe^John^^^^^L||" + // Name
                "19951231|" + // DOB
                "M||||||||||" + // Gender
                "${document.id.asUuidString()}|" + // Encounter ID
                "000000000" // SSN
        val test = segment.encode()
        Assert.assertEquals(sample, test)
    }

    @Test
    fun encodePID_omds() {
        val commandList1 = listOf(
            buildChangeDodIdCommand("**********"),
            buildChangeNameCommand("John Doe"),
            buildChangeDOBCommand(LocalDate.ofInstant(Instant.ofEpochMilli(820414800000L), ZoneId.of("UTC"))),
            buildChangeSsnCommand("***********"),
            buildChangeGenderCommand(Gender.MALE),
            buildChangePatientIdCommand(DomainId.create())
        )
        val hL7Data = buildHl7Data(commandList1)
        // This field is a random UUID, manually reset for testing
        DomainId.fromSeeded(
            Instant.ofEpochMilli(820414800000L),
            "ea2641f0f5ae4d33".toByteArray(),
            DocumentId::class.java
        )
        val document = hL7Data.getDocumentForEncounter(hL7Data.activeEncounterId!!)
        val segment = PIDBuilder(Endpoint.OMDS_DELTA).populateFromBatdokPatient(msg, hL7Data)

        val sample = "PID|1||" +
                "**********^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI|" + // DOD ID
                "${document!!.info.patientId.asUuidString()}^^^2.16.840.1.113883.3.42.10033.100001.13^BATDOK|" +  // Patient ID
                "Doe^John^^^^^L||" + // Name
                "19951231|" + // DOB
                "M||||||||||" + // Gender
                "${document.id.asUuidString()}|" + // Encounter ID
                "000000000" // SSN
        val test = segment.encode()
        Assert.assertEquals(sample, test)
    }

    @Test
    fun encodePV1_mhsg_t() {
        val commandList1 = listOf(
            buildChangeInjuryTimeCommand(Instant.ofEpochMilli(1679082153L * 1000)),
        )
        val hL7Data = buildHl7Data(commandList1)
        val document = hL7Data.getDocumentForEncounter(hL7Data.activeEncounterId!!)
        val segment = PV1Builder(Endpoint.MHSG_T, providerData).populateFromBatdokPatient(msg,hL7Data)
        val sample = "PV1|1|E|^^^THEATER||||dodid^name^name|||||||||||HX|${document!!.id.asUuidString()}|||||||||||||||||||||||||20230317194233+0000"
        val test = segment.encode().substringBeforeLast("|")  // Last piece is current timestamp
        Assert.assertEquals(sample, test)
    }

    @Test
    fun encodePV1_omds() {
        val commandList1 = listOf(
            buildChangeInjuryTimeCommand(Instant.ofEpochMilli(1679082153L * 1000)),
        )
        val hL7Data = buildHl7Data(commandList1)
        val segment = PV1Builder(Endpoint.OMDS_DELTA, providerData).populateFromBatdokPatient(msg, hL7Data)
        val sample = "PV1|1|${pv102PatientClassCode(Endpoint.OMDS_DELTA)}|^^^THEATER HX||||dodid^name^name|||||||||||HX|||||||||||||||||||||||D|||"
        val test = segment.encode().substringBeforeLast("|")  // Last piece is current timestamp
        Assert.assertEquals(sample, test)
    }

    @Test
    fun encodePV1_masking() {
        val commandList1 = listOf(
            buildPatientMaskingCommand(true, "Masked"),
            buildChangeInjuryTimeCommand(Instant.ofEpochMilli(1679082153L * 1000)),
        )
        val hL7Data = buildHl7Data(commandList1)
        val document = hL7Data.getDocumentForEncounter(hL7Data.activeEncounterId!!)
        val segment = PV1Builder(Endpoint.MHSG_T, providerData).populateFromBatdokPatient(msg, hL7Data)
        val sample = "PV1|1|E|^^^THEATER||||dodid^name^name|||||||||Y||HX|${document!!.id.asUuidString()}|||||||||||||||||||||||||20230317194233+0000"
        val test = segment.encode().substringBeforeLast("|")  // Last piece is current timestamp
        Assert.assertEquals(sample, test)
    }

    @Test
    fun encodePV1_usesEncounterTimestamp() {
        val commands = listOf(buildCommandData(buildChangeOpenCloseEncounterCommand(true), timestamp = Instant.ofEpochSecond(0)))
        val hl7Data = buildHl7DataFromCommandData(commands)
        val segment = PV1Builder(Endpoint.MHSG_T, providerData).populateFromBatdokPatient(msg, hl7Data)
        val sample = "PV1|1|E|^^^THEATER||||dodid^name^name|||||||||||HX|${hl7Data.document!!.id.asUuidString()}|||||||||||||||||||||||||19700101000000+0000"
        val test = segment.encode().substringBeforeLast("|")  // Last piece is current timestamp
        Assert.assertEquals(sample, test)
    }

    @Test
    fun encodePV2_noInjuries() {
        val hL7Data = buildHl7Data(emptyList())
        val segment = PV2Builder().populateFromBatdokPatient(msg, hL7Data)
        val sample = "PV2|||^Trauma"
        val test = segment.encode()
        assert(sample == test)
    }

    @Test
    fun encodePV2_withInjuries() {
        val commandList1 = listOf(
            buildChangeInjuryCommand("I1",true,"I1","hand"),
            buildChangeInjuryCommand("I2",true,"I2","left foot"),
            buildChangeInjuryCommand("I3",true,"I3","head"),
        )
        val hL7Data = buildHl7Data(commandList1)
        val segment = PV2Builder().populateFromBatdokPatient(msg, hL7Data)
        val sample = "PV2|||^I1, I2, I3"
        val test = segment.encode()
        assert(sample == test)
    }

    @Test
    fun encodePR1() {
        // Not implemented - PR1 implementation is unstable
    }
}
