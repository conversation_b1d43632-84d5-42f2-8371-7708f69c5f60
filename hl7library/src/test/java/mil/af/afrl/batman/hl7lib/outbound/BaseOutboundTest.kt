package mil.af.afrl.batman.hl7lib.outbound

import androidx.annotation.CallSuper
import mil.af.afrl.batman.hl7lib.converter.MedicationConverter
import mil.af.afrl.batman.hl7lib.converter.MoiConverter
import mil.af.afrl.batman.hl7lib.converter.RouteConverter
import mil.af.afrl.batman.hl7lib.converter.TreatmentConverter
import mil.af.afrl.batman.hl7lib.converter.UnitConverter
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.oidIPI
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.oidTAC
import mil.af.afrl.batman.hl7lib.util.OutboundHL7Data
import mil.af.afrl.batman.hl7lib.util.ProviderInfo
import org.junit.Before
import org.junit.Test

abstract class BaseOutboundTest {

    protected val hl7Data = OutboundHL7Data()

    @Before
    @CallSuper
    open fun setup() {
        hl7Data.providerInfo = ProviderInfo("providerfirst providerlast", "123456")
        hl7Data.commandsList = buildTestCommands()
        hl7Data.externalIds = mapOf(oidIPI to "testid", oidTAC to "tac-id")

        // Clear old codes set in earlier tests
        TreatmentConverter.therapeuticProcedures.clear()
        TreatmentConverter.diagnosticProcedures.clear()
        TreatmentConverter.unclassifiedProcedures.clear()
        MedicationConverter.map.clear()
        RouteConverter.map.clear()
        UnitConverter.map.clear()
        MoiConverter.map.clear()
    }

    @Test abstract fun testADTA02()
    @Test abstract fun testADTA04()
    @Test abstract fun testADTA28()
    @Test abstract fun testADTA31()
    @Test abstract fun testORUR01()
    @Test abstract fun testORUZB2()
    @Test abstract fun testORMO01()
    @Test abstract fun testADTA03()

}
