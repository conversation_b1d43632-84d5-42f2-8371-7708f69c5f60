package mil.af.afrl.batman.hl7lib.outbound

import ca.uhn.hl7v2.model.primitive.CommonTS
import ca.uhn.hl7v2.model.v231.message.ADT_A31
import com.google.protobuf.Message
import gov.afrl.batdok.encounter.KnownEventTypes
import gov.afrl.batdok.encounter.commands.buildAddTreatmentCommand
import gov.afrl.batdok.encounter.commands.buildChangeGenderCommand
import gov.afrl.batdok.encounter.commands.buildChangeInjuryCommand
import gov.afrl.batdok.encounter.commands.buildChangeMoiCommand
import gov.afrl.batdok.encounter.commands.buildChangeOpenCloseEncounterCommand
import gov.afrl.batdok.encounter.commands.buildRemoveTreatmentCommand
import gov.afrl.batdok.encounter.ids.TreatmentId
import gov.afrl.batdok.encounter.treatment.ChestTubeData
import gov.afrl.batdok.encounter.treatment.Treatment
import gov.afrl.batdok.util.buildCommandData
import io.mockk.coEvery
import io.mockk.mockkObject
import mil.af.afrl.batdokdata.id.EncounterId
import mil.af.afrl.batman.batdokid.DomainId
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.converter.DBAllergyConverter
import mil.af.afrl.batman.hl7lib.converter.LocationInjuryConverter
import mil.af.afrl.batman.hl7lib.converter.MedicationConverter
import mil.af.afrl.batman.hl7lib.converter.MoiConverter
import mil.af.afrl.batman.hl7lib.converter.RouteConverter
import mil.af.afrl.batman.hl7lib.converter.TreatmentConverter
import mil.af.afrl.batman.hl7lib.converter.UnitConverter
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.ADT_A02Builder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.ADT_A03Builder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.ADT_A04Builder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.ADT_A28Builder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.ADT_A31Builder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.ORM_O01Builder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.ORU_R01Builder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.ORU_ZB2Builder
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.msh03SendApplicationCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.msh05ReceiveApplicationCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.msh06ReceivingFacilityCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.msh11ProcessingIDCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.msh12VersionIDCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.oidIPI
import mil.af.afrl.batman.hl7lib.data.models.v231.field.CPT4
import mil.af.afrl.batman.hl7lib.data.models.v231.field.DHMSM_ICD
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ExtendedCodedElement
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ICD10
import mil.af.afrl.batman.hl7lib.data.models.v231.field.RxNorm
import mil.af.afrl.batman.hl7lib.util.MockkRule
import mil.af.afrl.batman.hl7lib.util.ProviderInfo
import mil.af.afrl.batman.hl7lib.util.asUuidString
import mil.af.afrl.batman.hl7lib.util.hashedWith
import org.junit.Assert
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import java.time.Instant
import java.util.Date

class OMDSOutboundTest :BaseOutboundTest() {

    @get:Rule val mockkRule = MockkRule(this, relaxed = true)

    @Before
    override fun setup() {
        super.setup()

        val encounterId1 = DomainId.create<EncounterId>()
        val encounterId2 = DomainId.create<EncounterId>()
        hl7Data.endpoint = Endpoint.OMDS_DELTA
        hl7Data.providerInfo = ProviderInfo("providerfirst providerlast", "123456")
        hl7Data.commandsList = buildTestCommands()
        hl7Data.mode = "TRAUMA"
        hl7Data.commandsByEncounter = mapOf(
            encounterId1 to buildTestCommands(),
            encounterId2 to buildHistoricalTestCommands()
        )

        hl7Data.activeEncounterId = encounterId1
    }

    override fun testADTA02() {
        // ARRANGE

        // ACT
        val result_doc = ADT_A02Builder(Endpoint.OMDS_DELTA).populateAll(hl7Data).build().toResult(Endpoint.OMDS_DELTA, hasPv1Discharge = true)

        // ASSERT
        val expected_doc = """
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ADT^A02|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${evnSegment("A02")}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data, a02Data = true)}
        """.trimIndent().replace("\n", "\r")
        Assert.assertEquals(expected_doc, result_doc)
    }

    override fun testADTA04() {
        // ARRANGE
        // Variation to ensure that sex is blank if unfilled
        hl7Data.commandsList += buildCommandData(buildChangeGenderCommand(null), timestamp = Instant.ofEpochMilli(1687868468743))

        // ACT
        val result = ADT_A04Builder(Endpoint.OMDS_DELTA).populateAll(hl7Data).build().toResult(Endpoint.OMDS_DELTA, hasPv1Discharge = true)

        // ASSERT
        val expected = """
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ADT^A04|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${evnSegment("A04")}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            ACC|20230627120104+0000
        """.trimIndent().replace("\n", "\r")
        Assert.assertEquals(expected, result)
    }


    @Test
    override fun testADTA28() {
        // ARRANGE

        // ACT
        val result = ADT_A28Builder(Endpoint.OMDS_DELTA).populateAll(hl7Data).build().toResult(Endpoint.OMDS_DELTA)

        // ASSERT
        val expected = getExpectedA28String(Endpoint.OMDS_DELTA)
        Assert.assertEquals(expected, result)

    }

    private fun getExpectedA28String(endpoint: Endpoint) : String {
        // First pipe is so trimMargin call works
        return """
            |MSH|^~\&|${msh03SendApplicationCode(endpoint)}|${if (endpoint.format == EndpointType.MHSG_T) "xxx" else ""}|${msh05ReceiveApplicationCode(endpoint)}|${msh06ReceivingFacilityCode(endpoint)}|zzz||ADT^A28|yyy|${msh11ProcessingIDCode(endpoint)}|${msh12VersionIDCode(endpoint)}
            |${evnSegment("A28")}
            |${pidSegment(hl7Data, EndpointType.OMDS)}
            |ZPI|1||||A+||||||||||${
            if (hl7Data.mode == "TCCC K9"){
                """DOG"""
            } else "HUMAN"
        }
            |ZEI|1||USAF||||||||AF00||||O-1|||2NDLT
            |ZMI|1|||unit
        """.trimMargin().replace("\n\n", "\n").replace("\n", "\r")
    }
    override fun testADTA31() {
        // ARRANGE
        mockkObject(DBAllergyConverter)
        val msgMock = ADT_A31().apply { initQuickstart("ADT", "A31", "P") }
        coEvery { DBAllergyConverter.batdokDataToCodedElement(any(), "Acetaminophen") } returns ExtendedCodedElement(msgMock, "161", "Acetaminophen", "MULTUMDRUG")
        coEvery { DBAllergyConverter.batdokDataToCodedElement(any(), "Amoxicillin") } returns ExtendedCodedElement(msgMock, "723", "Amoxicillin", "MULTUMDRUG")
        coEvery { DBAllergyConverter.batdokDataToCodedElement(any(), "Anti-seizure Drugs") } returns ExtendedCodedElement(msgMock, "294620008", "Anti-Seizure Drugs", "MULTUMDRUG")

        MoiConverter.map.putAll(mapOf(
            buildMapEntry("Burn", "123", ICD10),
            buildMapEntry("Crush", "456", ICD10),
            buildMapEntry("Fire", "789", ICD10),
        ))

        val testInjuryCodes = listOf(
            "Name\tDefault\t",
            "AMPUTATION\tZ89.9",
            "BURN\tT24.012",
            "FIRE\tY26",
            "CRUSH\tT14.8"
        ).joinToString("\n")
        LocationInjuryConverter.loadData(testInjuryCodes.byteInputStream(), ICD10)

        TreatmentConverter.therapeuticProcedures.putAll(mapOf(
            buildMapEntry("TQ", "20655006", CPT4),
            buildMapEntry("Dressing", "3895009", CPT4),
            buildMapEntry("O2", "57485005", CPT4),
            buildMapEntry("Chest Tube", "264957007", CPT4),
            buildMapEntry("Chest Seal", "182531007", CPT4),
            buildMapEntry("Splint", "79321009", CPT4),
        ))

        // ACT
        val result_doc = ADT_A31Builder(Endpoint.OMDS_DELTA).populateAll(hl7Data).joinToString("\r"){ it.toResult(Endpoint.OMDS_DELTA, hasPv1Discharge = true) }
        val noteTime = result_doc.substringAfter("Foley|")  // After note tag
            .substringBefore('\r')  // Before newline
        val cleanResult = result_doc.replace(noteTime, "ttt")

        // ASSERT
        val expected_doc = """
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ADT^A31|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${evnSegment("A31")}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            AL1|1|DA|161^Acetaminophen^MULTUMDRUG|NE
            AL1|2|DA|723^Amoxicillin^MULTUMDRUG|NE
            AL1|3|DA|294620008^Anti-Seizure Drugs^MULTUMDRUG|NE
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ADT^A31|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${evnSegment("A31")}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            DG1|1||789^Fire^ICD10|Fire|20230627120104+0000|A
            DG1|2||Z89.9^Acquired absence of limb, unspecified^ICD10|Amputation (Anterior Right Wrist)|20230627120104+0000|A
            DG1|3||Z89.9^Acquired absence of limb, unspecified^ICD10|Amputation (Anterior Right Lower Leg)|20230627120104+0000|A
            DG1|4||T14.8^Other injury of unspecified body region^ICD10|Crush (Location Unknown)|20230627120104+0000|A
            DG1|5||T24.012^Burn of unspecified degree of left thigh^ICD10|Burn (Posterior Left Upper Leg)|20230627120104+0000|A
            DG1|6||Z89.9^Acquired absence of limb, unspecified^ICD10|Amputation (Anterior Left Hand)|20230627120104+0000|A
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ADT^A31|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${evnSegment("A31")}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            PR1|1||20655006^Application of tourniquet (procedure)^SCT|TQ (Location: Extremity, Sub-Location: LUE)|20230627121501+0000|P
            OBX|1|TX|66019005^Limb structure (body structure)^SCT|1|Extremity||||||F|||20230627121501+0000
            OBX|2|TX|368208006^Left upper arm structure (body structure)^SCT|2|LUE||||||F|||20230627121501+0000
            PR1|2||20655006^Application of tourniquet (procedure)^SCT|TQ (Location: Extremity, Sub-Location: RLE)|20230627121504+0000|P
            OBX|1|TX|66019005^Limb structure (body structure)^SCT|1|Extremity||||||F|||20230627121504+0000
            OBX|2|TX|62175007^Structure of right lower limb (body structure)^SCT|2|RLE||||||F|||20230627121504+0000
            PR1|3||20655006^Application of tourniquet (procedure)^SCT|TQ (Location: Junctional)|20230627121505+0000|P
            OBX|1|TX|50974003^Junctional (qualifier value)^SCT|1|Junctional||||||F|||20230627121505+0000
            PR1|4||20655006^Application of tourniquet (procedure)^SCT|TQ (Location: Truncal)|20230627121505+0000|P
            OBX|1|TX|22943007^Trunk structure (body structure)^SCT|1|Truncal||||||F|||20230627121505+0000
            PR1|5||3895009^Application of dressing (procedure)^SCT|Dressing (Type: Pressure)|20230627121509+0000|P
            OBX|1|TX|118414001^Pressure dressing, device (physical object)^SCT|1|Pressure||||||F|||20230627121509+0000
            PR1|6||69466000^Unknown procedure (finding)^SCT|INTACT|20230627121511+0000
            PR1|7||69466000^Unknown procedure (finding)^SCT|O2|20230627121513+0000|P
            PR1|8||446847002^Drainage of pleural cavity via chest tube (procedure)^SCT|CHEST_TUBE (Location: Right)|20230627121517+0000|P
            PR1|9||464094004^Pneumothorax dressing (physical object)^SCT|CHEST_SEAL (LeftFront)|20230627121520+0000|P
            PR1|10||69466000^Unknown procedure (finding)^SCT|SPLINT (Pulse present)|20230627121524+0000|P
            PR1|11||69466000^Unknown procedure (finding)^SCT|Escharotomy|20230627121529+0000
            PR1|12||392231009^Intravenous cannulation^SCT|Line (Type: Peripheral, SubType: EZ-IO, Location: Left Hand, Gauge: 18)|20230627121529+0000
            OBX|1|TX|85151006^Structure of left hand (body structure)^SCT|1|Left Hand||||||F|||20230627121529+0000
            OBX|2|NM|277306007^18G (qualifier)^SCT|2|18.0||||||F|||20230627121529+0000
            PR1|13||112798008^Insertion of endotracheal tube (procedure)^SCT|ET-Tube (Confirmation: Breath Sounds, Depth: 7)|20230627121529+0000
            OBX|1|NM|131197000^Depth (qualifier value)^SCT|1|7||||||F|||20230627121529+0000
            PR1|14||69466000^Unknown procedure (finding)^SCT|Foley|ttt
            PR1|15||69466000^Unknown procedure (finding)^SCT|Dressing Change|ttt
            PR1|16||52653008^Respiratory sounds (observable entity)^SCT|Breath Sounds (Left Wheezing, Right Rhonchi)|20230627122108+0000
            PR1|17||69466000^Unknown procedure (finding)^SCT|Respiratory Effort (Labored)|20230627122108+0000
            PR1|18||268925001^Examination of respiratory system (procedure)^SCT|Chest Equal Rise and Fall|20230627122108+0000
        """.trimIndent().replace("\n", "\r")
        Assert.assertEquals(expected_doc, cleanResult)
    }

    @Test
    override fun testORUR01() {
        // TODO: Add back custom orders if they come back into encounters

        // ARRANGE
        UnitConverter.map.putAll(mapOf(
            buildMapEntry("Days", "Days", DHMSM_ICD),
            buildMapEntry("Units", "Units", DHMSM_ICD),
            buildMapEntry("br/min", "Breaths/minute", DHMSM_ICD),
            buildMapEntry("cmH20", "Centimeters water", DHMSM_ICD),
            buildMapEntry("mL", "Milliliters", DHMSM_ICD),
        ))

        val document = hl7Data.getDocumentForEncounter(hl7Data.activeEncounterId!!)
        val treatmentToRemove = document?.treatments?.list?.getOrNull(2)

        treatmentToRemove?.let {
            val command = buildCommandData(
                buildRemoveTreatmentCommand(
                    treatmentId = it.id,
                    docError = false,
                )
            )
            hl7Data.addCommand(hl7Data.activeEncounterId!! ,command)
            document.handle(listOf(command))
        }
        

        // ACT
        // All of the individual ORUs are built by this same class. Concatenate them all
        //  and make sure they look right.
        val result = ORU_R01Builder(Endpoint.OMDS_DELTA).populateAll(hl7Data)
            .joinToString("\r") { it.toResult(Endpoint.OMDS_DELTA,false, true) }

        // ASSERT
        val expected = """
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            ORC|CM
            OBR|1|||VITALSIGNS|||ttt|||||||||||||||ttt|||F
            ZUI|1|${document!!.id.hashedWith("heightandweight").asUuidString()}|UNKNOWN
            OBX|1|NM|4154138^Height/Length Estimated||123.0|cm|||||F|||ttt
            OBX|2|NM|4154135^Weight Estimated||456.0|kg|||||F|||ttt
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            ORC|CM
            OBR|1|||VITALSIGNS|||20230627120856+0000|||||||||||||||20230627120856+0000|||F
            ZUI|1|${document.vitals.vitalList[0].vitalId.asUuidString()}|UNKNOWN
            OBX|1|NM|703511^Peripheral Pulse Rate||1||||||F|||20230627120856+0000
            OBX|2|TX|34593195^Pulse Site||hrlocation||||||F|||20230627120856+0000
            OBX|3|NM|3623994^SpO2||2||||||F|||20230627120856+0000
            OBX|4|NM|703540^Respiratory Rate||3||||||F|||20230627120856+0000
            OBX|5|NM|2808512^Systolic Blood Pressure Invasive||10||||||F|||20230627120856+0000
            OBX|6|NM|2808516^Diastolic Blood Pressure Invasive||11||||||F|||20230627120856+0000
            OBX|7|NM|703501^Systolic Blood Pressure||4||||||F|||20230627120856+0000
            OBX|8|NM|703516^Diastolic Blood Pressure||5||||||F|||20230627120856+0000
            OBX|9|TX|473505875^BP Site||bplocation||||||F|||20230627120856+0000
            OBX|10|NM|703306^Mean Arterial Pressure, Calc||4.666667||||||F|||20230627120856+0000
            OBX|11|TX|36800381^Level of Consciousness AVPU||Alert||||||F|||20230627120856+0000
            OBX|12|NM|4247425^Numeric Pain Scale||6||||||F|||20230627120856+0000
            OBX|13|NM|2700544^End Tidal CO2^^^End-tidal carbon dioxide (ETCO2)||7||||||F|||20230627120856+0000
            OBX|14|NM|2820736^Temperature (Route Not Specified)||-12.833333|^deg c|||||F|||20230627120856+0000
            OBX|15|NM|103344685^Urine Output||12.0||||||F|||20230627120856+0000
            OBX|16|NM|703608^Capillary Refill||111.1||||||F|||20230627120856+0000
            OBX|17|NM|703584^Eye Opening Response Glasgow||222||||||F|||20230627120856+0000
            OBX|18|NM|703783^Motor Response, Estimated Glasgow||333||||||F|||20230627120856+0000
            OBX|19|NM|703786^Best Verbal Response Glasgow||444||||||F|||20230627120856+0000
            OBX|20|NM|34601069^Estimated Glasgow Coma Score||999||||||F|||20230627120856+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            ORC|CM
            OBR|1|||VITALSIGNS|||20230627120937+0000|||||||||||||||20230627120937+0000|||F
            ZUI|1|${document.vitals.vitalList[1].vitalId.asUuidString()}|UNKNOWN
            OBX|1|NM|703511^Peripheral Pulse Rate||555||||||F|||20230627120937+0000
            OBX|2|TX|36800381^Level of Consciousness AVPU||Pain||||||F|||20230627120937+0000
            OBX|3|NM|4247425^Numeric Pain Scale||8||||||F|||20230627120937+0000
            OBX|4|NM|703584^Eye Opening Response Glasgow||9||||||F|||20230627120937+0000
            OBX|5|TX|703783^Motor Response, Estimated Glasgow||NT||||||F|||20230627120937+0000
            OBX|6|NM|703786^Best Verbal Response Glasgow||8||||||F|||20230627120937+0000
            OBX|7|NM|34601069^Estimated Glasgow Coma Score||17||||||F|||20230627120937+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${pidSegment(hl7Data,EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            ORC|CM
            OBR|1|||2921288^Basic Metabolic Panel|||ttt|||||||||||||||ttt||GLB|F
            ZUI|1|${document.id.hashedWith("Basic Metabolic Panel").asUuidString()}|UNKNOWN
            OBX|1|NM|102593623^Sodium||6.0||||||F|||20230627122108+0000
            OBX|2|NM|102598663^Potassium Lvl||7.0||||||F|||20230627122108+0000
            OBX|3|NM|102595417^Calcium||8.0||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${pidSegment(hl7Data,EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            ORC|CM
            OBR|1|||2921414^CBC w/ Diff|||ttt|||||||||||||||ttt||GLB|F
            ZUI|1|${document.id.hashedWith("CBC w/ Diff").asUuidString()}|UNKNOWN
            OBX|1|NM|102599089^Hemoglobin||10.0||||||F|||20230627122108+0000
            OBX|2|NM|102606671^Hematocrit||11||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            ORC|CM
            OBR|1|||VITALSIGNS|||20230627122108+0000|||||||||||||||20230627122108+0000|||F
            ZUI|1|${document.bloodList.list[0].id.asUuidString()}|UNKNOWN
            OBX|1|TX|162324421^Blood Products Given||Whole Blood (WB)||||||F|||20230627122108+0000
            OBX|2|NM|19182882^Blood Unit Volume Infused:||500|ml^Milliliters^DHMSM-ICD|||||F|||20230627122108+0000
            OBX|3|TX|709176^Blood Unit Type||AB+||||||F|||20230627122108+0000
            OBX|4|TX|3016970^Blood Donor Nbr||blood unit||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            ORC|CM
            OBR|1|||VITALSIGNS|||20230627122108+0000|||||||||||||||20230627122108+0000|||F
            ZUI|1|${document.bloodList.list[1].id.asUuidString()}|UNKNOWN
            OBX|1|TX|162324421^Blood Products Given||Fresh||||||F|||20230627122108+0000
            OBX|2|NM|19182882^Blood Unit Volume Infused:||500|ml^Milliliters^DHMSM-ICD|||||F|||20230627122108+0000
            OBX|3|TX|709176^Blood Unit Type||O+||||||F|||20230627122108+0000
            OBX|4|TX|3016970^Blood Donor Nbr||blood unit||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            ORC|CM
            OBR|1|||VITALSIGNS|||20230627120104+0000|||||||||||||||20230627120104+0000|||F
            ZUI|1|${document.ventValues.ventSettingsList[0].id.asUuidString()}|UNKNOWN
            OBX|1|TX|3623806^Ventilator Model||vent||||||F|||20230627120104+0000
            OBX|2|TX|2796635^Ventilator Mode||mode||||||F|||20230627120104+0000
            OBX|3|NM|2796645^Tidal Volume, Delivered||4|mL^Milliliters^DHMSM-ICD|||||F|||20230627120104+0000
            OBX|4|NM|2796666^Positive End Expiratory Pressure||5.6|cmH20^Centimeters water^DHMSM-ICD|||||F|||20230627120104+0000
            OBX|5|NM|2700657^FIO2||1.2||||||F|||20230627120104+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            ORC|CM
            OBR|1|||VITALSIGNS|||20230627120104+0000|||||||||||||||20230627120104+0000|||F
            ZUI|1|${document.ventValues.ventSettingsList[1].id.asUuidString()}|UNKNOWN
            OBX|1|NM|2700657^FIO2||444.0||||||F|||20230627120104+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            ORC|CM
            OBR|1|||2820561^Transfer Note^^^BATDOK R1 to R3 Note|||ttt|||||||||||||||ttt||MDOC|F
            ZUI|1|${document.id.hashedWith("allnotes").asUuidString()}|UNKNOWN
            OBX|1|TX|2820561^Transfer Note||Name: test j patient||||||F|||ttt
            OBX|2|TX|2820561^Transfer Note||Provider: providerfirst providerlast||||||F|||ttt
            OBX|3|TX|2820561^Transfer Note||Patient EDIPI: **********||||||F|||ttt
            OBX|4|TX|2820561^Transfer Note||Injury Date/Time: 27/06/2023 12:01Z||||||F|||ttt
            OBX|5|TX|2820561^Transfer Note||test note sub (subjective)||||||F|||20230627121733+0000
            OBX|6|TX|2820561^Transfer Note||FAST Exam Result - Positive FAST: Suspected hemoperitoneum||||||F|||20230627121733+0000
            OBX|7|TX|2820561^Transfer Note||test note plan (plan)||||||F|||20230627121744+0000
            OBX|8|TX|2820561^Transfer Note||event linked to complaint *** (plan)||||||F|||20230627121744+0000
            OBX|9|TX|2820561^Transfer Note||Assessment linked to complaint *** (assessment)||||||F|||20230627121744+0000
            OBX|10|TX|2820561^Transfer Note||______________ Injuries Summary List ______________||||||F|||ttt
            OBX|11|TX|2820561^Transfer Note||Total number of Injuries: 5||||||F|||ttt
            OBX|12|TX|2820561^Transfer Note||Injury #1: Anterior Right Wrist Amputation||||||F|||ttt
            OBX|13|TX|2820561^Transfer Note||Injury #2: Anterior Right Lower Leg Amputation||||||F|||ttt
            OBX|14|TX|2820561^Transfer Note||Injury #3: Crush||||||F|||ttt
            OBX|15|TX|2820561^Transfer Note||Injury #4: Posterior Left Upper Leg Burn||||||F|||ttt
            OBX|16|TX|2820561^Transfer Note||Injury #5: Anterior Left Hand Amputation||||||F|||ttt
            OBX|17|TX|2820561^Transfer Note||______________ Treatments Summary List ______________||||||F|||ttt
            OBX|18|TX|2820561^Transfer Note||Total number of Treatments: 18||||||F|||ttt
            OBX|19|TX|2820561^Transfer Note||Treatment #1: TQ (Location: Extremity, Sub-Location: LUE)||||||F|||ttt
            OBX|20|TX|2820561^Transfer Note||Treatment #2: TQ (Location: Extremity, Sub-Location: RLE)||||||F|||ttt
            OBX|21|TX|2820561^Transfer Note||Treatment #3: TQ (Location: Truncal)||||||F|||ttt
            OBX|22|TX|2820561^Transfer Note||Treatment #4: Dressing (Type: Pressure)||||||F|||ttt
            OBX|23|TX|2820561^Transfer Note||Treatment #5: INTACT||||||F|||ttt
            OBX|24|TX|2820561^Transfer Note||Treatment #6: O2||||||F|||ttt
            OBX|25|TX|2820561^Transfer Note||Treatment #7: CHEST_TUBE (Location: Right)||||||F|||ttt
            OBX|26|TX|2820561^Transfer Note||Treatment #8: CHEST_SEAL (LeftFront)||||||F|||ttt
            OBX|27|TX|2820561^Transfer Note||Treatment #9: SPLINT (Pulse present)||||||F|||ttt
            OBX|28|TX|2820561^Transfer Note||Treatment #10: Escharotomy||||||F|||ttt
            OBX|29|TX|2820561^Transfer Note||Treatment #11: Line (Type: Peripheral, SubType: EZ-IO, Location: Left Hand, Gauge: 18)||||||F|||ttt
            OBX|30|TX|2820561^Transfer Note||Treatment #12: ET-Tube (Confirmation: Breath Sounds, Depth: 7)||||||F|||ttt
            OBX|31|TX|2820561^Transfer Note||Treatment #13: Removed TQ at ttt (Location: Junctional)||||||F|||ttt
            OBX|32|TX|2820561^Transfer Note||Treatment #14: Foley||||||F|||ttt
            OBX|33|TX|2820561^Transfer Note||Treatment #15: Dressing Change||||||F|||ttt
            OBX|34|TX|2820561^Transfer Note||Treatment #16: Breath Sounds (Left Wheezing, Right Rhonchi)||||||F|||ttt
            OBX|35|TX|2820561^Transfer Note||Treatment #17: Respiratory Effort (Labored)||||||F|||ttt
            OBX|36|TX|2820561^Transfer Note||Treatment #18: Chest Equal Rise \T\ Fall||||||F|||ttt
            OBX|37|TX|2820561^Transfer Note||______________ Medications Summary List ______________||||||F|||ttt
            OBX|38|TX|2820561^Transfer Note||Total number of Medications: 5||||||F|||ttt
            OBX|39|TX|2820561^Transfer Note||Medication #1: 0.9% Sodium Chloride 5 gm administered via IM at 2023-06-27T12:21:08Z||||||F|||ttt
            OBX|40|TX|2820561^Transfer Note||Medication #2: Ertapenem 1 gm administered via IV at 2023-06-27T12:21:08Z||||||F|||ttt
            OBX|41|TX|2820561^Transfer Note||Medication #3: Acetaminophen 99 administered via PR at 2023-06-27T12:21:08Z||||||F|||ttt
            OBX|42|TX|2820561^Transfer Note||Medication #4: Fresh blood 5 gm administered via IV at 2023-06-27T12:21:08Z||||||F|||ttt
            OBX|43|TX|2820561^Transfer Note||Medication #5: 0.9% Sodium Chloride 5 gm administered via IM at 2023-06-27T12:21:08Z||||||F|||ttt
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            ORC|CM
            OBR|1|||2820561^Transfer Note^^^BATDOK Order|||20230627122108+0000|||||||||||||||20230627122108+0000||MDOC|F
            ZUI|1|${document.events.list.filter { it.eventType == KnownEventTypes.ORDERS.dataString }.first().id.asUuidString()}|UNKNOWN
            OBX|1|TX|2820561^Transfer Note||Added Order:||||||F|||20230627122108+0000
            OBX|2|TX|2820561^Transfer Note||Title: Administer Action 1||||||F|||20230627122108+0000
            OBX|3|TX|2820561^Transfer Note||Instructions: When pain is greater than 7||||||F|||20230627122108+0000
            OBX|4|TX|2820561^Transfer Note||Frequency: Per Instruction||||||F|||20230627122108+0000
            OBX|5|TX|2820561^Transfer Note||Provider: Dr. Professorson||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            ORC|CM
            OBR|1|||2820561^Transfer Note^^^BATDOK Order|||20230627122108+0000|||||||||||||||20230627122108+0000||MDOC|F
            ZUI|1|${document.events.list.filter { it.eventType == KnownEventTypes.ORDERS.dataString }[1].id.asUuidString()}|UNKNOWN
            OBX|1|TX|2820561^Transfer Note||Added Order:||||||F|||20230627122108+0000
            OBX|2|TX|2820561^Transfer Note||Title: Administer Action 2||||||F|||20230627122108+0000
            OBX|3|TX|2820561^Transfer Note||Instructions: When Eli makes the order||||||F|||20230627122108+0000
            OBX|4|TX|2820561^Transfer Note||Frequency: Per Graham||||||F|||20230627122108+0000
            OBX|5|TX|2820561^Transfer Note||Provider: Dr. Ezratty||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            ORC|CM
            OBR|1|||2820561^Transfer Note^^^BATDOK Order|||20230627122108+0000|||||||||||||||20230627122108+0000||MDOC|F
            ZUI|1|${document.events.list.filter { it.eventType == KnownEventTypes.ORDERS.dataString }[2].id.asUuidString()}|UNKNOWN
            OBX|1|TX|2820561^Transfer Note||Added Custom Order:||||||F|||20230627122108+0000
            OBX|2|TX|2820561^Transfer Note||Title: 0.9% Sodium Chloride, IM, 5 gm||||||F|||20230627122108+0000
            OBX|3|TX|2820561^Transfer Note||Instructions: When pain is greater than 7||||||F|||20230627122108+0000
            OBX|4|TX|2820561^Transfer Note||Frequency: Per Instruction||||||F|||20230627122108+0000
            OBX|5|TX|2820561^Transfer Note||Provider: Dr. Professorson||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            ORC|CM
            OBR|1|||2820561^Transfer Note^^^BATDOK Order Administration and Notation|||20230627122108+0000|||||||||||||||20230627122108+0000||MDOC|F
            ZUI|1|${document.notes.list[0].id.asUuidString()}|UNKNOWN
            OBX|1|TX|2820561^Transfer Note||Reference Order: 0.9% Sodium Chloride, IM, 5 gm||||||F|||20230627122108+0000
            OBX|2|TX|2820561^Transfer Note||Added Note:||||||F|||20230627122108+0000
            OBX|3|TX|2820561^Transfer Note||message2||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            ORC|CM
            OBR|1|||2820561^Transfer Note^^^BATDOK Order Administration and Notation|||20230627122108+0000|||||||||||||||20230627122108+0000||MDOC|F
            ZUI|1|${document.notes.list[1].id.asUuidString()}|UNKNOWN
            OBX|1|TX|2820561^Transfer Note||Reference Order: Administer Action 2||||||F|||20230627122108+0000
            OBX|2|TX|2820561^Transfer Note||Added Note:||||||F|||20230627122108+0000
            OBX|3|TX|2820561^Transfer Note||*******MESSAGE*******||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            ORC|CM
            OBR|1|||2820561^Transfer Note^^^BATDOK Order Administration and Notation|||ttt|||||||||||||||ttt||MDOC|F
            ZUI|1|${document.customActions.list.first().id.asUuidString()}|UNKNOWN
            OBX|1|TX|2820561^Transfer Note||Reference Order: Administer Action 2||||||F|||ttt
            OBX|2|TX|2820561^Transfer Note||Added Action:||||||F|||ttt
            OBX|3|TX|2820561^Transfer Note||message||||||F|||ttt
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            ORC|CM
            OBR|1|||2820561^Transfer Note^^^BATDOK Fluid Log|||20230627122108+0000|||||||||||||||20230627122108+0000||MDOC|F
            ZUI|1|${document.intakeOutputs.list.first().id.asUuidString()}|UNKNOWN
            OBX|1|TX|2820561^Transfer Note||Logged Intake:||||||F|||20230627122108+0000
            OBX|2|TX|2820561^Transfer Note||Oral Fluid 100.0 mL water||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            ORC|CM
            OBR|1|||2820561^Transfer Note^^^BATDOK Fluid Log|||20230627122108+0000|||||||||||||||20230627122108+0000||MDOC|F
            ZUI|1|${document.intakeOutputs.list[1].id.asUuidString()}|UNKNOWN
            OBX|1|TX|2820561^Transfer Note||Logged Intake:||||||F|||20230627122108+0000
            OBX|2|TX|2820561^Transfer Note||Parenteral Fluid 200.0 mL saline||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            ORC|CM
            OBR|1|||2820561^Transfer Note^^^BATDOK Fluid Log|||20230627122108+0000|||||||||||||||20230627122108+0000||MDOC|F
            ZUI|1|${document.intakeOutputs.list[2].id.asUuidString()}|UNKNOWN
            OBX|1|TX|2820561^Transfer Note||Logged Output:||||||F|||20230627122108+0000
            OBX|2|TX|2820561^Transfer Note||Urine 300.0 mL||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            ORC|CM
            OBR|1|||2820561^Transfer Note^^^DNBI Log|||20230627120104+0000|||||||||||||||20230627120104+0000||MDOC|F
            ZUI|1|${document.id.asUuidString()}|UNKNOWN
            OBX|1|TX|2820561^Transfer Note||Chief Complaint: Sore Throat/Hoarseness||||||F|||20230627120104+0000
            OBX|2|TX|2820561^Transfer Note||Subjective||||||F|||20230627120104+0000
            OBX|3|TX|2820561^Transfer Note||History of Present Illness:No history of present illness||||||F|||20230627120104+0000
            OBX|4|TX|2820561^Transfer Note||Review of Systems:Sore Throat/Hoarseness: SYSTEM REVIEW HERE||||||F|||20230627120104+0000
            OBX|5|TX|2820561^Transfer Note||Past Medical / Surgical / Family History:SurgHx: Appendectomy last year||||||F|||20230627120104+0000
            OBX|6|TX|2820561^Transfer Note||Assessment and Plan||||||F|||20230627120104+0000
            OBX|7|TX|2820561^Transfer Note||Red Flags: Wheezing||||||F|||20230627120104+0000
            OBX|8|TX|2820561^Transfer Note||Sore Throat/Hoarseness: event linked to complaint ***||||||F|||20230627120104+0000
            OBX|9|TX|2820561^Transfer Note||Sore Throat/Hoarseness: Assessment linked to complaint ***||||||F|||20230627120104+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            ORC|CM
            OBR|1|||2820561^Transfer Note^^^BATDOK Observation|||20230627122108+0000|||||||||||||||20230627122108+0000||MDOC|F
            ZUI|1|${document.observations.list[0].id.asUuidString()}|UNKNOWN
            OBX|1|TX|2820561^Transfer Note||Breath Sounds||||||F|||20230627122108+0000
            OBX|2|TX|2820561^Transfer Note||Description: Left Wheezing, Right Rhonchi||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            ORC|CM
            OBR|1|||2820561^Transfer Note^^^BATDOK Observation|||20230627122108+0000|||||||||||||||20230627122108+0000||MDOC|F
            ZUI|1|${document.observations.list[1].id.asUuidString()}|UNKNOWN
            OBX|1|TX|2820561^Transfer Note||Respiratory Effort||||||F|||20230627122108+0000
            OBX|2|TX|2820561^Transfer Note||Description: Labored||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            ORC|CM
            OBR|1|||2820561^Transfer Note^^^BATDOK Observation|||20230627122108+0000|||||||||||||||20230627122108+0000||MDOC|F
            ZUI|1|${document.observations.list[2].id.asUuidString()}|UNKNOWN
            OBX|1|TX|2820561^Transfer Note||Chest Equal Rise And Fall||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            ORC|CM
            OBR|1|||2820561^Transfer Note^^^BATDOK Observation|||20230627122108+0000|||||||||||||||20230627122108+0000||MDOC|F
            ZUI|1|${document.observations.list[3].id.asUuidString()}|UNKNOWN
            OBX|1|TX|2820561^Transfer Note||Wheezing||||||F|||20230627122108+0000
            OBX|2|TX|2820561^Transfer Note||Description: Difficult to breathe||||||F|||20230627122108+0000
        """.trimIndent().replace("\n", "\r")

        // Get current time from the message (this always generates with the current time here)
        // This removes the current time from anywhere it is inserted in the message too
        val currentTime = result.substringAfter("2820561^Transfer Note^^^BATDOK R1 to R3 Note|||")  // After note tag
            .substringBefore('\r')  // Before newline
            .substringBefore("||||")  // Before the empty pipes
        val removedTreatmentTimeStamp = Regex("""Removed TQ at (.*?) \(Location: Junctional\)""")
            .find(result)
            ?.groupValues
            ?.getOrNull(1)
        val removeEncounterId = Regex("""Encounter: (.*?) - Encounter Date: 06/26/2023 12:01Z""")
            .find(result)
            ?.groupValues
            ?.getOrNull(1)
        val processor = HL7Processor()
        val cleanResult = result
            .let { processor.replaceTimestamp(it, "PV1", 45, "ttt")}
            .replace(currentTime, "ttt")
            .let { if (removedTreatmentTimeStamp != null) it.replace(removedTreatmentTimeStamp, "ttt") else it }
            .let { if (removeEncounterId != null) it.replace(removeEncounterId, "EncounterID") else it }


        Assert.assertEquals(expected, cleanResult)

        // Make sure the current timestamp was relatively recent (within last second)
        val tsHandler = CommonTS()
        tsHandler.value = currentTime
        Assert.assertTrue(tsHandler.valueAsDate.time > Date().time - 3000)
    }

    override fun testORUZB2(){
        //ARRANGE
        //ACT
        val result = ORU_ZB2Builder(Endpoint.OMDS_DELTA).populateAll(hl7Data).build().toResult(Endpoint.OMDS_DELTA,false, true)

        //ASSERT
        val expected = """
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||${msh05ReceiveApplicationCode(Endpoint.OMDS_DELTA)}|${msh06ReceivingFacilityCode(Endpoint.OMDS_DELTA)}|zzz||ORU^ZB2|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            OBR|1|||VITALSIGNS^Blood products|||ttt|||||||||||||||ttt||BB|F
            NTE|1||test|47
            ZUI|1|${hl7Data.document!!.id.hashedWith("blood").asUuidString()}|UNKNOWN
            ZBP|1|123456|654321|55555|Fresh Whole Blood(FWB)^description^alt description|TRANSFUSED|20241118143430|20241218143430|O|Rh-Pos|||Tacos^^147258369||||N|500|300|100|1||mLs
        """.trimIndent().replace("\n", "\r")
        val cleanResult = result.lines().joinToString("\r") { line ->
            if (line.startsWith("OBR|")){
                line.replace(Regex("""\d{14}\+\d{4}"""), "ttt")
            }else {
                line
            }
        }

        Assert.assertEquals(expected, cleanResult)
    }

    override fun testORMO01() {
        //todo: fix acetaminophen orders

        // ARRANGE
        MedicationConverter.map.putAll(mapOf(
            buildMapEntry("0.9% Sodium Chloride", "1159317", RxNorm),
            buildMapEntry("Ertapenem", "325642", RxNorm),
            buildMapEntry("Acetaminophen", "161", RxNorm),
            buildMapEntry("Fresh blood","","")
        ))

        UnitConverter.map.putAll(mapOf(
            buildMapEntry("g", "Gram", DHMSM_ICD),
            buildMapEntry("mg", "Milligrams", DHMSM_ICD),
            buildMapEntry("unknown unit", "Unknown Unit", DHMSM_ICD),
        ))

        RouteConverter.map.putAll(mapOf(
            buildMapEntry("IM", "IntraMuscular", DHMSM_ICD),
            buildMapEntry("NS", "Nostril-Both", DHMSM_ICD),
            buildMapEntry("IVPU", "IV Push", DHMSM_ICD),
            buildMapEntry("PO", "Oral", DHMSM_ICD),
            buildMapEntry("PR", "Rectal", DHMSM_ICD),
        ))

        // ACT
        val result_doc = ORM_O01Builder(Endpoint.OMDS_DELTA).populateAll(hl7Data).joinToString("\r"){it.toResult(Endpoint.OMDS_DELTA,false, true)}

        // ASSERT
        val expected_doc = """
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ORM^O01|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            ORC|NW|RE^^${hl7Data.document!!.medicines.list[0].id.asUuidString()}|||CM||||20230627122108+0000
            RXO|1234^0.9% Sodium Chloride^RxNorm|5.0||g^Gram
            RXR|IM^IntraMuscular
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ORM^O01|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            ORC|NW|RE^^${hl7Data.document!!.medicines.list[1].id.asUuidString()}|||CM||||20230627122108+0000
            RXO|325642^Ertapenem^RxNorm|1.0||g^Gram
            RXR|IVPU^IV Push
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ORM^O01|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            ORC|NW|RE^^${hl7Data.document!!.medicines.list[2].id.asUuidString()}|||CM||||20230627122108+0000
            RXO|161^Acetaminophen^RxNorm|99.0||unknown unit^Unknown Unit
            RXR|PR^Rectal
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ORM^O01|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            ORC|NW|RE^^${hl7Data.document!!.medicines.list[3].id.asUuidString()}|||CM||||20230627122108+0000
            RXO|^Fresh blood|5.0||g^Gram
            RXR|IVPU^IV Push
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ORM^O01|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
            ORC|NW|RE^^${hl7Data.document!!.medicines.list[4].id.asUuidString()}|||CM||||20230627122108+0000
            RXO|1159317^0.9% Sodium Chloride^RxNorm|5.0||g^Gram
            RXR|IM^IntraMuscular
        """.trimIndent().replace("\n", "\r")
        Assert.assertEquals(expected_doc, result_doc)

        // TODO: Add back in orders to the end of this message if they come back into the encounters
        //  Should look something like this:
        //  ORC|NW||||IP||^PRN||20230627122108+0000
        //  RXO|161^Acetaminophen^CNUM|1000.0||mg^Milligrams
        //  RXR|PO^Oral
    }

    override fun testADTA03() {
        // ARRANGE

        // ACT
        val result = ADT_A03Builder(Endpoint.OMDS_DELTA).populateAll(hl7Data).build().toResult(Endpoint.OMDS_DELTA,hasPv1Discharge = true)

        // ASSERT
        val expected = """
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ADT^A03|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${evnSegment("A03")}
            ${pidSegment(hl7Data, EndpointType.OMDS)}
            ${pv1Segment(hl7Data)}
        """.trimIndent().replace("\n", "\r")
        Assert.assertEquals(expected, result)
    }

    @Test
    fun testADT_A31_removedTreats() {
        // ARRANGE
        val treatId = DomainId.create<TreatmentId>()
        val addTreat = buildAddTreatmentCommand(Treatment(
            name = "CHEST_TUBE",
            timestamp = Instant.ofEpochMilli(1687868117654),
            treatmentData = ChestTubeData("Right"),
            id = treatId
        ))
        val removeTreat = buildRemoveTreatmentCommand(treatId)

        hl7Data.commandsByEncounter = emptyMap()
        val commands = listOf(addTreat, removeTreat, buildChangeOpenCloseEncounterCommand(open = true))
        val commandDataList = commands.map{ buildCommandDataFromCommand(it) }
        
        hl7Data.commandsByEncounter = mapOf(
            hl7Data.activeEncounterId!! to commandDataList
        )
        hl7Data.externalIds = hl7Data.externalIds?.toMutableMap()?.apply { remove("2.16.840.1.113883.3.42.10032.100001.13") }

        // ACT
        var result = ADT_A31Builder(Endpoint.OMDS_DELTA).populateAll(hl7Data).joinToString("\r"){ it.toResult(Endpoint.OMDS_DELTA, hasPv1Discharge = true) }
        // Cut the removal timestamp, it'll just be current time
        result = result.substringBeforeLast("|")

        // ASSERT
        val expected = """
            MSH|^~\&|BATDOK||||zzz||ADT^A31|yyy|P|2.3
            ${evnSegment("A31")}
            ${pidSegmentSimpleNoPatient(hl7Data)}
            ${pv1Segment(hl7Data)}
            PR1|1||446847002^Drainage of pleural cavity via chest tube (procedure)^SCT|CHEST_TUBE (Location: Right)|20230627121517+0000
            PR1|2||446847002^Drainage of pleural cavity via chest tube (procedure)^SCT^^REMOVED|CHEST_TUBE (Location: Right)
        """.trimIndent().replace("\n", "\r")
        Assert.assertEquals(expected, result)
    }

    @Test
    fun testInjuriesAsMOIs () = assertInjuryMoiData(
        listOf(
            buildChangeInjuryCommand("Gunshot Wound", true, "GSW", ""),
            buildChangeMoiCommand("Gunshot Wound", true, null),
        ), """
            DG1|1||Y24.9^GunShot Wound^ICD10|Gunshot Wound||A
            DG1|2||Y24.9^Unspecified firearm discharge, undetermined intent^ICD10|Gunshot Wound (Location Unknown)||A
        """
    )

    private fun assertInjuryMoiData(commands: List<Message>, expectedDG1Segments: String){
        // ARRANGE
        hl7Data.commandsByEncounter = emptyMap()
        val commandList = commands.map(::buildCommandDataFromCommand) + 
                buildCommandDataFromCommand(buildChangeOpenCloseEncounterCommand(open = true))
        hl7Data.commandsByEncounter = mapOf(
            hl7Data.activeEncounterId!! to commandList)
        hl7Data.providerInfo = ProviderInfo("providerfirst providerlast", "123456")
        hl7Data.externalIds = mapOf(oidIPI to "testid")

        MoiConverter.map.putAll(mapOf(
            buildMapEntry("GunShot Wound", "Y24.9", ICD10),
            buildMapEntry("Crush", "456", ICD10),
            buildMapEntry("Fire", "789", ICD10),
        ))

        val testInjuryCodes = listOf(
            "Name\tDefault\t",
            "GUNSHOT WOUND\tY24.9",
            "BURN\tT24.012"
        ).joinToString("\n")
        LocationInjuryConverter.loadData(testInjuryCodes.byteInputStream(), ICD10)

        // ACT
        val result = ADT_A31Builder(Endpoint.OMDS_DELTA).populateAll(hl7Data).joinToString("\r"){ it.toResult(Endpoint.OMDS_DELTA, hasPv1Discharge = true) }

        //ASSERT
        val expected = """
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.OMDS_DELTA)}||||zzz||ADT^A31|yyy|${msh11ProcessingIDCode(Endpoint.OMDS_DELTA)}|${msh12VersionIDCode(Endpoint.OMDS_DELTA)}
            ${evnSegment("A31")}
            ${pidSegmentSimpleNoPatient(hl7Data)}
            ${pv1Segment(hl7Data)}
            ${expectedDG1Segments.trim()}
            """.trimIndent().replace("\n","\r")
        val pv1Original = result.lines().first(){it.startsWith("PV1")}
        val pv1Cleaned = pv1Original.replace(Regex("""\|\d{14}\+\d{4}\|zzz"""), "|20230627120104+0000|zzz")
           
        val cleanResult = result.replace(pv1Original, pv1Cleaned)
        Assert.assertEquals(expected,cleanResult)
    }
}
