package mil.af.afrl.batman.hl7lib.outbound

import ca.uhn.hl7v2.model.primitive.CommonTS
import ca.uhn.hl7v2.model.v231.message.ADT_A31
import com.google.protobuf.Message
import gov.afrl.batdok.encounter.KnownEventTypes
import gov.afrl.batdok.encounter.commands.buildChangeOpenCloseEncounterCommand
import gov.afrl.batdok.encounter.commands.buildLogMedicineCommand
import gov.afrl.batdok.encounter.commands.buildRemoveTreatmentCommand
import gov.afrl.batdok.encounter.medicine.Medicine
import gov.afrl.batdok.util.buildCommandData
import io.mockk.coEvery
import io.mockk.mockkObject
import mil.af.afrl.batdokdata.id.EncounterId
import mil.af.afrl.batman.batdokid.DomainId
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.converter.DBAllergyConverter
import mil.af.afrl.batman.hl7lib.converter.LocationInjuryConverter
import mil.af.afrl.batman.hl7lib.converter.MedicationConverter
import mil.af.afrl.batman.hl7lib.converter.MoiConverter
import mil.af.afrl.batman.hl7lib.converter.RouteConverter
import mil.af.afrl.batman.hl7lib.converter.TreatmentConverter
import mil.af.afrl.batman.hl7lib.converter.UnitConverter
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.ADT_A02Builder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.ADT_A03Builder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.ADT_A04Builder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.ADT_A28Builder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.ORM_O01Builder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.ORU_R01Builder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.ORU_ZB2Builder
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.msh03SendApplicationCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.msh05ReceiveApplicationCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.msh06ReceivingFacilityCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.msh11ProcessingIDCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.msh12VersionIDCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.oidIPI
import mil.af.afrl.batman.hl7lib.data.models.v231.field.CPT4
import mil.af.afrl.batman.hl7lib.data.models.v231.field.DHMSM_ICD
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ExtendedCodedElement
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ICD10
import mil.af.afrl.batman.hl7lib.data.models.v231.field.RxNorm
import mil.af.afrl.batman.hl7lib.util.MockkRule
import mil.af.afrl.batman.hl7lib.util.ProviderInfo
import mil.af.afrl.batman.hl7lib.util.asUuidString
import mil.af.afrl.batman.hl7lib.util.hashedWith
import org.junit.Assert
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import java.time.Instant
import java.util.Date

class MHSGTOutboundTest : BaseOutboundTest() {

    @get:Rule val mockkRule = MockkRule(this, relaxed = true)

    @Before
    override fun setup() {
        super.setup()
        
        val encounterId1 = DomainId.create<EncounterId>()
        val encounterId2 = DomainId.create<EncounterId>()
        hl7Data.endpoint = Endpoint.MHSG_T
        hl7Data.providerInfo = ProviderInfo("providerfirst providerlast", "123456")
        hl7Data.commandsList = buildTestCommands()
        hl7Data.mode = "TRAUMA"
        hl7Data.commandsByEncounter = mapOf(
            encounterId1 to buildTestCommands(),
            encounterId2 to buildHistoricalTestCommands()
        )

        hl7Data.activeEncounterId = encounterId1
    }

    override fun testADTA02() {
        // ARRANGE

        // ACT
        val result_doc = ADT_A02Builder(Endpoint.MHSG_T).populateAll(hl7Data).build().toResult(Endpoint.MHSG_T, hasPv1Discharge = true)

        // ASSERT
        val expected_doc = """
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.MHSG_T)}|xxx|${msh05ReceiveApplicationCode(Endpoint.MHSG_T)}|${msh06ReceivingFacilityCode(Endpoint.MHSG_T)}|zzz||ADT^A02|yyy|${msh11ProcessingIDCode(Endpoint.MHSG_T)}|${msh12VersionIDCode(Endpoint.MHSG_T)}
            ${evnSegment("A02")}
            ${pidSegment(hl7Data,EndpointType.MHSG_T)}
            ${pv1Segment(hl7Data, a02Data = true)}
        """.trimIndent().replace("\n", "\r")
        Assert.assertEquals(expected_doc, result_doc)
    }

    override fun testADTA04() {
        // ARRANGE

        // ACT
        val result = ADT_A04Builder(Endpoint.MHSG_T).populateAll(hl7Data).build().toResult(Endpoint.MHSG_T, hasPv1Discharge = true)

        // ASSERT
        val expected = """
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.MHSG_T)}|xxx|${msh05ReceiveApplicationCode(Endpoint.MHSG_T)}|${msh06ReceivingFacilityCode(Endpoint.MHSG_T)}|zzz||ADT^A04|yyy|${msh11ProcessingIDCode(Endpoint.MHSG_T)}|${msh12VersionIDCode(Endpoint.MHSG_T)}
            ${evnSegment("A04")}
            ${pidSegment(hl7Data, EndpointType.MHSG_T)}
            ${pv1Segment(hl7Data)}
            PV2|||^Amputation, Crush, Burn
        """.trimIndent().replace("\n", "\r")
        Assert.assertEquals(expected, result)
    }
    
    @Test
    override fun testADTA28() {
        // ARRANGE
        mockkObject(DBAllergyConverter)
        val msgMock = ADT_A31().apply { initQuickstart("ADT", "A31", "P") }
        coEvery { DBAllergyConverter.batdokDataToCodedElement(any(), "Acetaminophen") } returns ExtendedCodedElement(msgMock, "161", "Acetaminophen", "MULTUMDRUG")
        coEvery { DBAllergyConverter.batdokDataToCodedElement(any(), "Amoxicillin") } returns ExtendedCodedElement(msgMock, "723", "Amoxicillin", "MULTUMDRUG")
        coEvery { DBAllergyConverter.batdokDataToCodedElement(any(), "Anti-seizure Drugs") } returns ExtendedCodedElement(msgMock, "294620008", "Anti-Seizure Drugs", "MULTUMDRUG")

        val testInjuryCodes = listOf(
            "Name\tDefault\t",
            "AMPUTATION\tZ89.9",
            "BURN\tT24.012",
            "FIRE\tY26",
            "CRUSH\tT14.8"
        ).joinToString("\n")
        LocationInjuryConverter.loadData(testInjuryCodes.byteInputStream(), ICD10)

        MoiConverter.map.putAll(mapOf(
            buildMapEntry("Burn", "123", ICD10),
            buildMapEntry("Crush", "456", ICD10),
            buildMapEntry("Fire", "789", ICD10),
        ))

        TreatmentConverter.therapeuticProcedures.putAll(mapOf(
            buildMapEntry("TQ", "20655006", CPT4),
            buildMapEntry("Dressing", "3895009", CPT4),
            buildMapEntry("O2", "57485005", CPT4),
            buildMapEntry("Chest Tube", "264957007", CPT4),
            buildMapEntry("Chest Seal", "182531007", CPT4),
            buildMapEntry("Splint", "79321009", CPT4),
        ))

            // ACT
            val result = ADT_A28Builder(Endpoint.MHSG_T).populateAll(hl7Data).build().toResult(Endpoint.MHSG_T)
            val noteTime = result.substringAfter("Foley|")  // After note tag
                .substringBefore('\r')  // Before newline
            val cleanResult = result.replace(noteTime, "ttt")

            // ASSERT
            val expected = getExpectedA28String(Endpoint.MHSG_T)
            Assert.assertEquals(expected, cleanResult)

    }

    private fun getExpectedA28String(endpoint: Endpoint) : String {
        // First pipe is so trimMargin call works
        return """
            |MSH|^~\&|${msh03SendApplicationCode(endpoint)}|${if (endpoint.format == EndpointType.MHSG_T) "xxx" else ""}|${msh05ReceiveApplicationCode(endpoint)}|${msh06ReceivingFacilityCode(endpoint)}|zzz||ADT^A28|yyy|${msh11ProcessingIDCode(endpoint)}|${msh12VersionIDCode(endpoint)}
            |${evnSegment("A28")}
            |${pidSegment(hl7Data, EndpointType.MHSG_T)}
            |AL1|1|DA|161^Acetaminophen^MULTUMDRUG|NE
            |AL1|2|DA|723^Amoxicillin^MULTUMDRUG|NE
            |AL1|3|DA|294620008^Anti-Seizure Drugs^MULTUMDRUG|NE
            |DG1|1||789^Fire^ICD10||20230627120104+0000|A
            |DG1|2||Z89.9^Acquired absence of limb, unspecified^ICD10||20230627120104+0000|A
            |DG1|3||Z89.9^Acquired absence of limb, unspecified^ICD10||20230627120104+0000|A
            |DG1|4||T14.8^Other injury of unspecified body region^ICD10||20230627120104+0000|A
            |DG1|5||T24.012^Burn of unspecified degree of left thigh^ICD10||20230627120104+0000|A
            |DG1|6||Z89.9^Acquired absence of limb, unspecified^ICD10||20230627120104+0000|A
            |PR1|1||20655006^TQ^CPT4|TQ (Location: Extremity, Sub-Location: LUE)|20230627121501+0000|P
            |PR1|2||20655006^TQ^CPT4|TQ (Location: Extremity, Sub-Location: RLE)|20230627121504+0000|P
            |PR1|3||20655006^TQ^CPT4|TQ (Location: Junctional)|20230627121505+0000|P
            |PR1|4||20655006^TQ^CPT4|TQ (Location: Truncal)|20230627121505+0000|P
            |PR1|5||3895009^Dressing^CPT4|Dressing (Type: Pressure)|20230627121509+0000|P
            |PR1|6||99291^Critical care, evaluation and management of the critically ill or critically injured patient; first 30-74 minutes^CPT4|INTACT|20230627121511+0000
            |PR1|7||57485005^O2^CPT4|O2|20230627121513+0000|P
            |PR1|8||264957007^Chest Tube^CPT4|CHEST_TUBE (Location: Right)|20230627121517+0000|P
            |PR1|9||182531007^Chest Seal^CPT4|CHEST_SEAL (LeftFront)|20230627121520+0000|P
            |PR1|10||79321009^Splint^CPT4|SPLINT (Pulse present)|20230627121524+0000|P
            |PR1|11||99291^Critical care, evaluation and management of the critically ill or critically injured patient; first 30-74 minutes^CPT4|Escharotomy|20230627121529+0000
            |PR1|12||99291^Critical care, evaluation and management of the critically ill or critically injured patient; first 30-74 minutes^CPT4|Line (Type: Peripheral, SubType: EZ-IO, Location: Left Hand, Gauge: 18)|20230627121529+0000
            |PR1|13||99291^Critical care, evaluation and management of the critically ill or critically injured patient; first 30-74 minutes^CPT4|ET-Tube (Confirmation: Breath Sounds, Depth: 7)|20230627121529+0000
            |PR1|14||99291^Critical care, evaluation and management of the critically ill or critically injured patient; first 30-74 minutes^CPT4|Foley|ttt
            |PR1|15||99291^Critical care, evaluation and management of the critically ill or critically injured patient; first 30-74 minutes^CPT4|Dressing Change|ttt
            |PR1|16||99291^Critical care, evaluation and management of the critically ill or critically injured patient; first 30-74 minutes^CPT4|Breath Sounds (Left Wheezing, Right Rhonchi)|20230627122108+0000
            |PR1|17||99291^Critical care, evaluation and management of the critically ill or critically injured patient; first 30-74 minutes^CPT4|Respiratory Effort (Labored)|20230627122108+0000
            |PR1|18||99291^Critical care, evaluation and management of the critically ill or critically injured patient; first 30-74 minutes^CPT4|Chest Equal Rise and Fall|20230627122108+0000
            |ZPI|1||||A+||||||||||${
            if (hl7Data.mode == "TCCC K9"){
                """DOG"""
            } else "HUMAN"
        }
            |ZEI|1||USAF||||||||AF00||||O-1|||2NDLT
            |ZMI|1|||unit
        """.trimMargin().replace("\n\n", "\n").replace("\n", "\r")
    }
    override fun testADTA31() {
        //not present
    }

    override fun testORUR01() {
        // TODO: Add back custom orders if they come back into encounters

        // ARRANGE
        UnitConverter.map.putAll(mapOf(
            buildMapEntry("Days", "Days", DHMSM_ICD),
            buildMapEntry("Units", "Units", DHMSM_ICD),
            buildMapEntry("br/min", "Breaths/minute", DHMSM_ICD),
            buildMapEntry("cmH20", "Centimeters water", DHMSM_ICD),
            buildMapEntry("mL", "Milliliters", DHMSM_ICD),
            buildMapEntry("cm", "Centimeters", DHMSM_ICD),
            buildMapEntry("kg", "Kilograms", DHMSM_ICD),
            buildMapEntry("deg c", "Centigrade", DHMSM_ICD),
        ))

        val testInjuryCodes = listOf(
            "Name\tDefault\t",
            "AMPUTATION\tZ89.9",
            "BURN\tT24.012",
            "FIRE\tY26",
            "CRUSH\tT14.8"
        ).joinToString("\n")
        LocationInjuryConverter.loadData(testInjuryCodes.byteInputStream(), ICD10)

        val document = hl7Data.getDocumentForEncounter(hl7Data.activeEncounterId!!)
        val treatmentToRemove = document?.treatments?.list?.getOrNull(2)

        treatmentToRemove?.let {
            val command = buildCommandData(
                buildRemoveTreatmentCommand(
                    treatmentId = it.id,
                    docError = false,
                )
            )
            hl7Data.addCommand(hl7Data.activeEncounterId!!, command)
            document.handle(listOf(command))
        }
        
        
        // ACT
        // All of the individual ORUs are built by this same class. Concatenate them all
        //  and make sure they look right.
        val result = ORU_R01Builder(Endpoint.MHSG_T).populateAll(hl7Data)
            .joinToString("\r") { it.toResult(Endpoint.MHSG_T,false) } ?: ""

        // ASSERT
        val expected = """
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.MHSG_T)}|xxx|${msh05ReceiveApplicationCode(Endpoint.MHSG_T)}|${msh06ReceivingFacilityCode(Endpoint.MHSG_T)}|zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.MHSG_T)}|${msh12VersionIDCode(Endpoint.MHSG_T)}
            ${pidSegment(hl7Data, EndpointType.MHSG_T)}
            ${pv1Segment(hl7Data)}
            OBR|1||${document!!.id.hashedWith("heightandweight").asUuidString()}|8716-3^Vital signs^LOINC|||ttt|||||||||||||||ttt|||F
            ZUI|1|${document.id.hashedWith("heightandweight").asUuidString()}|UNKNOWN
            OBX|1|NM|8302-2^Body height^LOINC||123.0|cm^Centimeters^DHMSM-ICD|||||F|||ttt
            OBX|2|NM|3141-9^Body weight Measured^LOINC||456.0|kg^Kilograms^DHMSM-ICD|||||F|||ttt
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.MHSG_T)}|xxx|${msh05ReceiveApplicationCode(Endpoint.MHSG_T)}|${msh06ReceivingFacilityCode(Endpoint.MHSG_T)}|zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.MHSG_T)}|${msh12VersionIDCode(Endpoint.MHSG_T)}
            ${pidSegment(hl7Data, EndpointType.MHSG_T)}
            ${pv1Segment(hl7Data)}
            OBR|1||${document.vitals.list[0].vitalId.asUuidString()}|8716-3^Vital signs^LOINC|||20230627120856+0000|||||||||||||||20230627120856+0000|||F
            ZUI|1|${document.vitals.list[0].vitalId.asUuidString()}|UNKNOWN
            OBX|1|NM|8867-4^Heart rate^LOINC||1||||||F|||20230627120856+0000
            OBX|2|TX|8885-6^Heart rate measurement site^LOINC||hrlocation||||||F|||20230627120856+0000
            OBX|3|NM|59408-5^Oxygen saturation in Arterial blood by Pulse oximetry^LOINC||2||||||F|||20230627120856+0000
            OBX|4|NM|9279-1^Respiratory Rate^LOINC||3||||||F|||20230627120856+0000
            OBX|5|NM|76215-3^Invasive Systolic Blood Pressure^LOINC||10||||||F|||20230627120856+0000
            OBX|6|NM|76213-8^Invasive Diastolic Blood Pressure^LOINC||11||||||F|||20230627120856+0000
            OBX|7|NM|8480-6^Systolic Blood Pressure^LOINC||4||||||F|||20230627120856+0000
            OBX|8|NM|8462-4^Diastolic Blood Pressure^LOINC||5||||||F|||20230627120856+0000
            OBX|9|NM|8478-0^Mean Blood Pressure^LOINC^^Mean arterial pressure||4.666667||||||F|||20230627120856+0000
            OBX|10|TX|80288-4^Level of Consciousness^LOINC||Alert||||||F|||20230627120856+0000
            OBX|11|NM|72514-3^Pain severity - 0-10 verbal numeric rating [Score] - Reported^LOINC||6||||||F|||20230627120856+0000
            OBX|12|NM|19891-1^Carbon dioxide [Partial pressure] in Exhaled gas --at end expiration^LOINC^^End-tidal carbon dioxide (ETCO2)||7||||||F|||20230627120856+0000
            OBX|13|NM|8310-5^Body temperature^LOINC||-12.833333|deg c^Centigrade^DHMSM-ICD|||||F|||20230627120856+0000
            OBX|14|NM|9187-6^Urine output^LOINC||12.0||||||F|||20230627120856+0000
            OBX|15|NM|44971-0^Capillary Refill Time^LOINC||111.1||||||F|||20230627120856+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.MHSG_T)}|xxx|${msh05ReceiveApplicationCode(Endpoint.MHSG_T)}|${msh06ReceivingFacilityCode(Endpoint.MHSG_T)}|zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.MHSG_T)}|${msh12VersionIDCode(Endpoint.MHSG_T)}
            ${pidSegment(hl7Data, EndpointType.MHSG_T)}
            ${pv1Segment(hl7Data)}
            OBR|1||${document.vitals.list[1].vitalId.asUuidString()}|8716-3^Vital signs^LOINC|||20230627120937+0000|||||||||||||||20230627120937+0000|||F
            ZUI|1|${document.vitals.list[1].vitalId.asUuidString()}|UNKNOWN
            OBX|1|NM|8867-4^Heart rate^LOINC||555||||||F|||20230627120937+0000
            OBX|2|TX|80288-4^Level of Consciousness^LOINC||Pain||||||F|||20230627120937+0000
            OBX|3|NM|72514-3^Pain severity - 0-10 verbal numeric rating [Score] - Reported^LOINC||8||||||F|||20230627120937+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.MHSG_T)}|xxx|${msh05ReceiveApplicationCode(Endpoint.MHSG_T)}|${msh06ReceivingFacilityCode(Endpoint.MHSG_T)}|zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.MHSG_T)}|${msh12VersionIDCode(Endpoint.MHSG_T)}
            ${pidSegment(hl7Data, EndpointType.MHSG_T)}
            ${pv1Segment(hl7Data)}
            OBR|1||${document.id.hashedWith("labresults").asUuidString()}|19146-0^Referral lab test results^LOINC|||ttt|||||||||||||||ttt||GLB|F
            ZUI|1|${document.id.hashedWith("labresults").asUuidString()}|UNKNOWN
            OBX|1|NM|2947-0^Sodium [Moles/volume] in Blood^LOINC||6.0||||||F|||20230627122108+0000
            OBX|2|NM|75940-7^Potassium [Mass/volume] in Blood^LOINC||7.0||||||F|||20230627122108+0000
            OBX|3|NM|38230-9^Calcium.ionized [Mass/volume] in Blood^LOINC||8.0||||||F|||20230627122108+0000
            OBX|4|NM|718-7^Hemoglobin [Mass/volume] in Blood^LOINC||10.0||||||F|||20230627122108+0000
            OBX|5|NM|20570-8^Hematocrit [Volume Fraction] of Blood^LOINC||11||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.MHSG_T)}|xxx|${msh05ReceiveApplicationCode(Endpoint.MHSG_T)}|${msh06ReceivingFacilityCode(Endpoint.MHSG_T)}|zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.MHSG_T)}|${msh12VersionIDCode(Endpoint.MHSG_T)}
            ${pidSegment(hl7Data, EndpointType.MHSG_T)}
            ${pv1Segment(hl7Data)}
            OBR|1||${document.bloodList.list[0].bloodId.asUuidString()}|79569-0^Blood product given [Type]^LOINC|||20230627122108+0000|||||||||||||||20230627122108+0000|||F
            ZUI|1|${document.bloodList.list[0].bloodId.asUuidString()}|UNKNOWN
            OBX|1|NM|51876-1^Whole blood units given [#]^LOINC||500|ml^Milliliters^DHMSM-ICD|||||F|||20230627122108+0000
            OBX|2|TS|935-7^Blood product unit expiration [Date and time]^LOINC||20230918000000+0000||||||F|||20230627122108+0000
            OBX|3|TX|933-2^Blood product type^LOINC||AB+||||||F|||20230627122108+0000
            OBX|4|NM|49546-5^Age of Blood specimen^LOINC||90|days^Days^DHMSM-ICD|||||F|||20230627122108+0000
            OBX|5|TX|936-5^Blood product unit [Identifier]^LOINC||blood unit||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.MHSG_T)}|xxx|${msh05ReceiveApplicationCode(Endpoint.MHSG_T)}|${msh06ReceivingFacilityCode(Endpoint.MHSG_T)}|zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.MHSG_T)}|${msh12VersionIDCode(Endpoint.MHSG_T)}
            ${pidSegment(hl7Data, EndpointType.MHSG_T)}
            ${pv1Segment(hl7Data)}
            OBR|1||${hl7Data.document!!.bloodList.list[1].bloodId.asUuidString()}|79569-0^Blood product given [Type]^LOINC|||20230627122108+0000|||||||||||||||20230627122108+0000|||F
            ZUI|1|${hl7Data.document!!.bloodList.list[1].bloodId.asUuidString()}|UNKNOWN
            OBX|1|NM|51876-1^Whole blood units given [#]^LOINC||500|ml^Milliliters^DHMSM-ICD|||||F|||20230627122108+0000
            OBX|2|TX|933-2^Blood product type^LOINC||O+||||||F|||20230627122108+0000
            OBX|3|NM|49546-5^Age of Blood specimen^LOINC||90|days^Days^DHMSM-ICD|||||F|||20230627122108+0000
            OBX|4|TX|936-5^Blood product unit [Identifier]^LOINC||blood unit||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.MHSG_T)}|xxx|${msh05ReceiveApplicationCode(Endpoint.MHSG_T)}|${msh06ReceivingFacilityCode(Endpoint.MHSG_T)}|zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.MHSG_T)}|${msh12VersionIDCode(Endpoint.MHSG_T)}
            ${pidSegment(hl7Data, EndpointType.MHSG_T)}
            ${pv1Segment(hl7Data)}
            OBR|1||${document.ventValues.ventSettingsList[0].id.asUuidString()}|69348-1^Respiratory assist status^LOINC|||20230627120104+0000|||||||||||||||20230627120104+0000|||F
            ZUI|1|${document.ventValues.ventSettingsList[0].id.asUuidString()}|UNKNOWN
            OBX|1|TX|20125-1^Ventilator type^LOINC||vent||||||F|||20230627120104+0000
            OBX|2|TX|20124-4^Ventilation mode Ventilator^LOINC||mode||||||F|||20230627120104+0000
            OBX|3|NM|19834-1^Breath rate setting Ventilator^LOINC||3|br/min^Breaths/minute^DHMSM-ICD|||||F|||20230627120104+0000
            OBX|4|NM|20112-9^Tidal volume setting Ventilator^LOINC||4|mL^Milliliters^DHMSM-ICD|||||F|||20230627120104+0000
            OBX|5|NM|20077-4^Positive end expiratory pressure setting Ventilator^LOINC||5.6|cmH20^Centimeters water^DHMSM-ICD|||||F|||20230627120104+0000
            OBX|6|NM|19996-8^Oxygen/Inspired gas Respiratory system --on ventilator^LOINC||1.2||||||F|||20230627120104+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.MHSG_T)}|xxx|${msh05ReceiveApplicationCode(Endpoint.MHSG_T)}|${msh06ReceivingFacilityCode(Endpoint.MHSG_T)}|zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.MHSG_T)}|${msh12VersionIDCode(Endpoint.MHSG_T)}
            ${pidSegment(hl7Data, EndpointType.MHSG_T)}
            ${pv1Segment(hl7Data)}
            OBR|1||${document.ventValues.ventSettingsList[1].id.asUuidString()}|69348-1^Respiratory assist status^LOINC|||20230627120104+0000|||||||||||||||20230627120104+0000|||F
            ZUI|1|${document.ventValues.ventSettingsList[1].id.asUuidString()}|UNKNOWN
            OBX|1|NM|19996-8^Oxygen/Inspired gas Respiratory system --on ventilator^LOINC||444.0||||||F|||20230627120104+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.MHSG_T)}|xxx|${msh05ReceiveApplicationCode(Endpoint.MHSG_T)}|${msh06ReceivingFacilityCode(Endpoint.MHSG_T)}|zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.MHSG_T)}|${msh12VersionIDCode(Endpoint.MHSG_T)}
            ${pidSegment(hl7Data, EndpointType.MHSG_T)}
            ${pv1Segment(hl7Data)}
            OBR|1||${document.id.hashedWith("allnotes").asUuidString()}|18761-7^Transfer summary note^LOINC^^BATDOK R1 to R3 Note|||ttt|||||||||||||||ttt|||F
            ZUI|1|${document.id.hashedWith("allnotes").asUuidString()}|UNKNOWN
            OBX|1|TX|18761-7^Transfer summary note^LOINC||Name: test j patient||||||F|||ttt
            OBX|2|TX|18761-7^Transfer summary note^LOINC||Provider: providerfirst providerlast||||||F|||ttt
            OBX|3|TX|18761-7^Transfer summary note^LOINC||Patient EDIPI: **********||||||F|||ttt
            OBX|4|TX|18761-7^Transfer summary note^LOINC||Injury Date/Time: 27/06/2023 12:01Z||||||F|||ttt
            OBX|5|TX|18761-7^Transfer summary note^LOINC||test note sub (subjective)||||||F|||20230627121733+0000
            OBX|6|TX|18761-7^Transfer summary note^LOINC||FAST Exam Result - Positive FAST: Suspected hemoperitoneum||||||F|||20230627121733+0000
            OBX|7|TX|18761-7^Transfer summary note^LOINC||test note plan (plan)||||||F|||20230627121744+0000
            OBX|8|TX|18761-7^Transfer summary note^LOINC||event linked to complaint *** (plan)||||||F|||20230627121744+0000
            OBX|9|TX|18761-7^Transfer summary note^LOINC||Assessment linked to complaint *** (assessment)||||||F|||20230627121744+0000
            OBX|10|TX|18761-7^Transfer summary note^LOINC||______________ Injuries Summary List ______________||||||F|||ttt
            OBX|11|TX|18761-7^Transfer summary note^LOINC||Total number of Injuries: 5||||||F|||ttt
            OBX|12|TX|18761-7^Transfer summary note^LOINC||Injury #1: Anterior Right Wrist Amputation||||||F|||ttt
            OBX|13|TX|18761-7^Transfer summary note^LOINC||Injury #2: Anterior Right Lower Leg Amputation||||||F|||ttt
            OBX|14|TX|18761-7^Transfer summary note^LOINC||Injury #3: Crush||||||F|||ttt
            OBX|15|TX|18761-7^Transfer summary note^LOINC||Injury #4: Posterior Left Upper Leg Burn||||||F|||ttt
            OBX|16|TX|18761-7^Transfer summary note^LOINC||Injury #5: Anterior Left Hand Amputation||||||F|||ttt
            OBX|17|TX|18761-7^Transfer summary note^LOINC||______________ Treatments Summary List ______________||||||F|||ttt
            OBX|18|TX|18761-7^Transfer summary note^LOINC||Total number of Treatments: 18||||||F|||ttt
            OBX|19|TX|18761-7^Transfer summary note^LOINC||Treatment #1: TQ (Location: Extremity, Sub-Location: LUE)||||||F|||ttt
            OBX|20|TX|18761-7^Transfer summary note^LOINC||Treatment #2: TQ (Location: Extremity, Sub-Location: RLE)||||||F|||ttt
            OBX|21|TX|18761-7^Transfer summary note^LOINC||Treatment #3: TQ (Location: Truncal)||||||F|||ttt
            OBX|22|TX|18761-7^Transfer summary note^LOINC||Treatment #4: Dressing (Type: Pressure)||||||F|||ttt
            OBX|23|TX|18761-7^Transfer summary note^LOINC||Treatment #5: INTACT||||||F|||ttt
            OBX|24|TX|18761-7^Transfer summary note^LOINC||Treatment #6: O2||||||F|||ttt
            OBX|25|TX|18761-7^Transfer summary note^LOINC||Treatment #7: CHEST_TUBE (Location: Right)||||||F|||ttt
            OBX|26|TX|18761-7^Transfer summary note^LOINC||Treatment #8: CHEST_SEAL (LeftFront)||||||F|||ttt
            OBX|27|TX|18761-7^Transfer summary note^LOINC||Treatment #9: SPLINT (Pulse present)||||||F|||ttt
            OBX|28|TX|18761-7^Transfer summary note^LOINC||Treatment #10: Escharotomy||||||F|||ttt
            OBX|29|TX|18761-7^Transfer summary note^LOINC||Treatment #11: Line (Type: Peripheral, SubType: EZ-IO, Location: Left Hand, Gauge: 18)||||||F|||ttt
            OBX|30|TX|18761-7^Transfer summary note^LOINC||Treatment #12: ET-Tube (Confirmation: Breath Sounds, Depth: 7)||||||F|||ttt
            OBX|31|TX|18761-7^Transfer summary note^LOINC||Treatment #13: Removed TQ at ttt (Location: Junctional)||||||F|||ttt
            OBX|32|TX|18761-7^Transfer summary note^LOINC||Treatment #14: Foley||||||F|||ttt
            OBX|33|TX|18761-7^Transfer summary note^LOINC||Treatment #15: Dressing Change||||||F|||ttt
            OBX|34|TX|18761-7^Transfer summary note^LOINC||Treatment #16: Breath Sounds (Left Wheezing, Right Rhonchi)||||||F|||ttt
            OBX|35|TX|18761-7^Transfer summary note^LOINC||Treatment #17: Respiratory Effort (Labored)||||||F|||ttt
            OBX|36|TX|18761-7^Transfer summary note^LOINC||Treatment #18: Chest Equal Rise \T\ Fall||||||F|||ttt
            OBX|37|TX|18761-7^Transfer summary note^LOINC||______________ Medications Summary List ______________||||||F|||ttt
            OBX|38|TX|18761-7^Transfer summary note^LOINC||Total number of Medications: 5||||||F|||ttt
            OBX|39|TX|18761-7^Transfer summary note^LOINC||Medication #1: 0.9% Sodium Chloride 5 gm administered via IM at 2023-06-27T12:21:08Z||||||F|||ttt
            OBX|40|TX|18761-7^Transfer summary note^LOINC||Medication #2: Ertapenem 1 gm administered via IV at 2023-06-27T12:21:08Z||||||F|||ttt
            OBX|41|TX|18761-7^Transfer summary note^LOINC||Medication #3: Acetaminophen 99 administered via PR at 2023-06-27T12:21:08Z||||||F|||ttt
            OBX|42|TX|18761-7^Transfer summary note^LOINC||Medication #4: Fresh blood 5 gm administered via IV at 2023-06-27T12:21:08Z||||||F|||ttt
            OBX|43|TX|18761-7^Transfer summary note^LOINC||Medication #5: 0.9% Sodium Chloride 5 gm administered via IM at 2023-06-27T12:21:08Z||||||F|||ttt
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.MHSG_T)}|xxx|${msh05ReceiveApplicationCode(Endpoint.MHSG_T)}|${msh06ReceivingFacilityCode(Endpoint.MHSG_T)}|zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.MHSG_T)}|${msh12VersionIDCode(Endpoint.MHSG_T)}
            ${pidSegment(hl7Data, EndpointType.MHSG_T)}
            ${pv1Segment(hl7Data)}
            OBR|1||${document.events.list.filter { it.eventType == KnownEventTypes.ORDERS.dataString }.first().id.asUuidString()}|18761-7^Transfer summary note^LOINC^^BATDOK Order|||20230627122108+0000|||||||||||||||20230627122108+0000||MDOC|F
            ZUI|1|${document.events.list.filter { it.eventType == KnownEventTypes.ORDERS.dataString }.first().id.asUuidString()}|UNKNOWN
            OBX|1|TX|18761-7^Transfer summary note^LOINC||Added Order:||||||F|||20230627122108+0000
            OBX|2|TX|18761-7^Transfer summary note^LOINC||Title: Administer Action 1||||||F|||20230627122108+0000
            OBX|3|TX|18761-7^Transfer summary note^LOINC||Instructions: When pain is greater than 7||||||F|||20230627122108+0000
            OBX|4|TX|18761-7^Transfer summary note^LOINC||Frequency: Per Instruction||||||F|||20230627122108+0000
            OBX|5|TX|18761-7^Transfer summary note^LOINC||Provider: Dr. Professorson||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.MHSG_T)}|xxx|${msh05ReceiveApplicationCode(Endpoint.MHSG_T)}|${msh06ReceivingFacilityCode(Endpoint.MHSG_T)}|zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.MHSG_T)}|${msh12VersionIDCode(Endpoint.MHSG_T)}
            ${pidSegment(hl7Data, EndpointType.MHSG_T)}
            ${pv1Segment(hl7Data)}
            OBR|1||${document.events.list.filter { it.eventType == KnownEventTypes.ORDERS.dataString }[1].id.asUuidString()}|18761-7^Transfer summary note^LOINC^^BATDOK Order|||20230627122108+0000|||||||||||||||20230627122108+0000||MDOC|F
            ZUI|1|${document.events.list.filter { it.eventType == KnownEventTypes.ORDERS.dataString }[1].id.asUuidString()}|UNKNOWN
            OBX|1|TX|18761-7^Transfer summary note^LOINC||Added Order:||||||F|||20230627122108+0000
            OBX|2|TX|18761-7^Transfer summary note^LOINC||Title: Administer Action 2||||||F|||20230627122108+0000
            OBX|3|TX|18761-7^Transfer summary note^LOINC||Instructions: When Eli makes the order||||||F|||20230627122108+0000
            OBX|4|TX|18761-7^Transfer summary note^LOINC||Frequency: Per Graham||||||F|||20230627122108+0000
            OBX|5|TX|18761-7^Transfer summary note^LOINC||Provider: Dr. Ezratty||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.MHSG_T)}|xxx|${msh05ReceiveApplicationCode(Endpoint.MHSG_T)}|${msh06ReceivingFacilityCode(Endpoint.MHSG_T)}|zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.MHSG_T)}|${msh12VersionIDCode(Endpoint.MHSG_T)}
            ${pidSegment(hl7Data, EndpointType.MHSG_T)}
            ${pv1Segment(hl7Data)}
            OBR|1||${document.events.list.filter { it.eventType == KnownEventTypes.ORDERS.dataString }[2].id.asUuidString()}|18761-7^Transfer summary note^LOINC^^BATDOK Order|||20230627122108+0000|||||||||||||||20230627122108+0000||MDOC|F
            ZUI|1|${document.events.list.filter { it.eventType == KnownEventTypes.ORDERS.dataString }[2].id.asUuidString()}|UNKNOWN
            OBX|1|TX|18761-7^Transfer summary note^LOINC||Added Custom Order:||||||F|||20230627122108+0000
            OBX|2|TX|18761-7^Transfer summary note^LOINC||Title: 0.9% Sodium Chloride, IM, 5 gm||||||F|||20230627122108+0000
            OBX|3|TX|18761-7^Transfer summary note^LOINC||Instructions: When pain is greater than 7||||||F|||20230627122108+0000
            OBX|4|TX|18761-7^Transfer summary note^LOINC||Frequency: Per Instruction||||||F|||20230627122108+0000
            OBX|5|TX|18761-7^Transfer summary note^LOINC||Provider: Dr. Professorson||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.MHSG_T)}|xxx|${msh05ReceiveApplicationCode(Endpoint.MHSG_T)}|${msh06ReceivingFacilityCode(Endpoint.MHSG_T)}|zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.MHSG_T)}|${msh12VersionIDCode(Endpoint.MHSG_T)}
            ${pidSegment(hl7Data, EndpointType.MHSG_T)}
            ${pv1Segment(hl7Data)}
            OBR|1||${document.notes.list[0].id.asUuidString()}|18761-7^Transfer summary note^LOINC^^BATDOK Order Administration and Notation|||20230627122108+0000|||||||||||||||20230627122108+0000||MDOC|F
            ZUI|1|${document.notes.list[0].id.asUuidString()}|UNKNOWN
            OBX|1|TX|18761-7^Transfer summary note^LOINC||Reference Order: 0.9% Sodium Chloride, IM, 5 gm||||||F|||20230627122108+0000
            OBX|2|TX|18761-7^Transfer summary note^LOINC||Added Note:||||||F|||20230627122108+0000
            OBX|3|TX|18761-7^Transfer summary note^LOINC||message2||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.MHSG_T)}|xxx|${msh05ReceiveApplicationCode(Endpoint.MHSG_T)}|${msh06ReceivingFacilityCode(Endpoint.MHSG_T)}|zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.MHSG_T)}|${msh12VersionIDCode(Endpoint.MHSG_T)}
            ${pidSegment(hl7Data, EndpointType.MHSG_T)}
            ${pv1Segment(hl7Data)}
            OBR|1||${document.notes.list[1].id.asUuidString()}|18761-7^Transfer summary note^LOINC^^BATDOK Order Administration and Notation|||20230627122108+0000|||||||||||||||20230627122108+0000||MDOC|F
            ZUI|1|${document.notes.list[1].id.asUuidString()}|UNKNOWN
            OBX|1|TX|18761-7^Transfer summary note^LOINC||Reference Order: Administer Action 2||||||F|||20230627122108+0000
            OBX|2|TX|18761-7^Transfer summary note^LOINC||Added Note:||||||F|||20230627122108+0000
            OBX|3|TX|18761-7^Transfer summary note^LOINC||*******MESSAGE*******||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.MHSG_T)}|xxx|${msh05ReceiveApplicationCode(Endpoint.MHSG_T)}|${msh06ReceivingFacilityCode(Endpoint.MHSG_T)}|zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.MHSG_T)}|${msh12VersionIDCode(Endpoint.MHSG_T)}
            ${pidSegment(hl7Data, EndpointType.MHSG_T)}
            ${pv1Segment(hl7Data)}
            OBR|1||${document.customActions.list.first().id.asUuidString()}|18761-7^Transfer summary note^LOINC^^BATDOK Order Administration and Notation|||ttt|||||||||||||||ttt||MDOC|F
            ZUI|1|${document.customActions.list.first().id.asUuidString()}|UNKNOWN
            OBX|1|TX|18761-7^Transfer summary note^LOINC||Reference Order: Administer Action 2||||||F|||ttt
            OBX|2|TX|18761-7^Transfer summary note^LOINC||Added Action:||||||F|||ttt
            OBX|3|TX|18761-7^Transfer summary note^LOINC||message||||||F|||ttt
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.MHSG_T)}|xxx|${msh05ReceiveApplicationCode(Endpoint.MHSG_T)}|${msh06ReceivingFacilityCode(Endpoint.MHSG_T)}|zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.MHSG_T)}|${msh12VersionIDCode(Endpoint.MHSG_T)}
            ${pidSegment(hl7Data, EndpointType.MHSG_T)}
            ${pv1Segment(hl7Data)}
            OBR|1||${document.intakeOutputs.list.first().id.asUuidString()}|18761-7^Transfer summary note^LOINC^^BATDOK Fluid Log|||20230627122108+0000|||||||||||||||20230627122108+0000||MDOC|F
            ZUI|1|${document.intakeOutputs.list.first().id.asUuidString()}|UNKNOWN
            OBX|1|TX|18761-7^Transfer summary note^LOINC||Logged Intake:||||||F|||20230627122108+0000
            OBX|2|TX|18761-7^Transfer summary note^LOINC||Oral Fluid 100.0 mL water||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.MHSG_T)}|xxx|${msh05ReceiveApplicationCode(Endpoint.MHSG_T)}|${msh06ReceivingFacilityCode(Endpoint.MHSG_T)}|zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.MHSG_T)}|${msh12VersionIDCode(Endpoint.MHSG_T)}
            ${pidSegment(hl7Data, EndpointType.MHSG_T)}
            ${pv1Segment(hl7Data)}
            OBR|1||${document.intakeOutputs.list[1].id.asUuidString()}|18761-7^Transfer summary note^LOINC^^BATDOK Fluid Log|||20230627122108+0000|||||||||||||||20230627122108+0000||MDOC|F
            ZUI|1|${document.intakeOutputs.list[1].id.asUuidString()}|UNKNOWN
            OBX|1|TX|18761-7^Transfer summary note^LOINC||Logged Intake:||||||F|||20230627122108+0000
            OBX|2|TX|18761-7^Transfer summary note^LOINC||Parenteral Fluid 200.0 mL saline||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.MHSG_T)}|xxx|${msh05ReceiveApplicationCode(Endpoint.MHSG_T)}|${msh06ReceivingFacilityCode(Endpoint.MHSG_T)}|zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.MHSG_T)}|${msh12VersionIDCode(Endpoint.MHSG_T)}
            ${pidSegment(hl7Data, EndpointType.MHSG_T)}
            ${pv1Segment(hl7Data)}
            OBR|1||${document.intakeOutputs.list[2].id.asUuidString()}|18761-7^Transfer summary note^LOINC^^BATDOK Fluid Log|||20230627122108+0000|||||||||||||||20230627122108+0000||MDOC|F
            ZUI|1|${document.intakeOutputs.list[2].id.asUuidString()}|UNKNOWN
            OBX|1|TX|18761-7^Transfer summary note^LOINC||Logged Output:||||||F|||20230627122108+0000
            OBX|2|TX|18761-7^Transfer summary note^LOINC||Urine 300.0 mL||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.MHSG_T)}|xxx|${msh05ReceiveApplicationCode(Endpoint.MHSG_T)}|${msh06ReceivingFacilityCode(Endpoint.MHSG_T)}|zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.MHSG_T)}|${msh12VersionIDCode(Endpoint.MHSG_T)}
            ${pidSegment(hl7Data, EndpointType.MHSG_T)}
            ${pv1Segment(hl7Data)}
            OBR|1||${document.id.asUuidString()}|18761-7^Transfer summary note^LOINC^^DNBI Log|||20230627120104+0000|||||||||||||||20230627120104+0000||MDOC|F
            ZUI|1|${document.id.asUuidString()}|UNKNOWN
            OBX|1|TX|18761-7^Transfer summary note^LOINC||Chief Complaint: Sore Throat/Hoarseness||||||F|||20230627120104+0000
            OBX|2|TX|18761-7^Transfer summary note^LOINC||Subjective||||||F|||20230627120104+0000
            OBX|3|TX|18761-7^Transfer summary note^LOINC||History of Present Illness:No history of present illness||||||F|||20230627120104+0000
            OBX|4|TX|18761-7^Transfer summary note^LOINC||Review of Systems:Sore Throat/Hoarseness: SYSTEM REVIEW HERE||||||F|||20230627120104+0000
            OBX|5|TX|18761-7^Transfer summary note^LOINC||Past Medical / Surgical / Family History:SurgHx: Appendectomy last year||||||F|||20230627120104+0000
            OBX|6|TX|18761-7^Transfer summary note^LOINC||Assessment and Plan||||||F|||20230627120104+0000
            OBX|7|TX|18761-7^Transfer summary note^LOINC||Red Flags: Wheezing||||||F|||20230627120104+0000
            OBX|8|TX|18761-7^Transfer summary note^LOINC||Sore Throat/Hoarseness: event linked to complaint ***||||||F|||20230627120104+0000
            OBX|9|TX|18761-7^Transfer summary note^LOINC||Sore Throat/Hoarseness: Assessment linked to complaint ***||||||F|||20230627120104+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.MHSG_T)}|xxx|${msh05ReceiveApplicationCode(Endpoint.MHSG_T)}|${msh06ReceivingFacilityCode(Endpoint.MHSG_T)}|zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.MHSG_T)}|${msh12VersionIDCode(Endpoint.MHSG_T)}
            ${pidSegment(hl7Data, EndpointType.MHSG_T)}
            ${pv1Segment(hl7Data)}
            OBR|1||${document.observations.list[0].id.asUuidString()}|18761-7^Transfer summary note^LOINC^^BATDOK Observation|||20230627122108+0000|||||||||||||||20230627122108+0000||MDOC|F
            ZUI|1|${document.observations.list[0].id.asUuidString()}|UNKNOWN
            OBX|1|TX|18761-7^Transfer summary note^LOINC||Breath Sounds||||||F|||20230627122108+0000
            OBX|2|TX|18761-7^Transfer summary note^LOINC||Description: Left Wheezing, Right Rhonchi||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.MHSG_T)}|xxx|${msh05ReceiveApplicationCode(Endpoint.MHSG_T)}|${msh06ReceivingFacilityCode(Endpoint.MHSG_T)}|zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.MHSG_T)}|${msh12VersionIDCode(Endpoint.MHSG_T)}
            ${pidSegment(hl7Data, EndpointType.MHSG_T)}
            ${pv1Segment(hl7Data)}
            OBR|1||${document.observations.list[1].id.asUuidString()}|18761-7^Transfer summary note^LOINC^^BATDOK Observation|||20230627122108+0000|||||||||||||||20230627122108+0000||MDOC|F
            ZUI|1|${document.observations.list[1].id.asUuidString()}|UNKNOWN
            OBX|1|TX|18761-7^Transfer summary note^LOINC||Respiratory Effort||||||F|||20230627122108+0000
            OBX|2|TX|18761-7^Transfer summary note^LOINC||Description: Labored||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.MHSG_T)}|xxx|${msh05ReceiveApplicationCode(Endpoint.MHSG_T)}|${msh06ReceivingFacilityCode(Endpoint.MHSG_T)}|zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.MHSG_T)}|${msh12VersionIDCode(Endpoint.MHSG_T)}
            ${pidSegment(hl7Data, EndpointType.MHSG_T)}
            ${pv1Segment(hl7Data)}
            OBR|1||${document.observations.list[2].id.asUuidString()}|18761-7^Transfer summary note^LOINC^^BATDOK Observation|||20230627122108+0000|||||||||||||||20230627122108+0000||MDOC|F
            ZUI|1|${document.observations.list[2].id.asUuidString()}|UNKNOWN
            OBX|1|TX|18761-7^Transfer summary note^LOINC||Chest Equal Rise And Fall||||||F|||20230627122108+0000
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.MHSG_T)}|xxx|${msh05ReceiveApplicationCode(Endpoint.MHSG_T)}|${msh06ReceivingFacilityCode(Endpoint.MHSG_T)}|zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.MHSG_T)}|${msh12VersionIDCode(Endpoint.MHSG_T)}
            ${pidSegment(hl7Data, EndpointType.MHSG_T)}
            ${pv1Segment(hl7Data)}
            OBR|1||${document.observations.list[3].id.asUuidString()}|18761-7^Transfer summary note^LOINC^^BATDOK Observation|||20230627122108+0000|||||||||||||||20230627122108+0000||MDOC|F
            ZUI|1|${document.observations.list[3].id.asUuidString()}|UNKNOWN
            OBX|1|TX|18761-7^Transfer summary note^LOINC||Wheezing||||||F|||20230627122108+0000
            OBX|2|TX|18761-7^Transfer summary note^LOINC||Description: Difficult to breathe||||||F|||20230627122108+0000
        """.trimIndent().replace("\n", "\r")

        // Get current time from the message (this always generates with the current time here)
        
        val noteTime = result.substringAfter("18761-7^Transfer summary note^LOINC^^BATDOK R1 to R3 Note|||")  // After note tag
            .substringBefore('\r')  // Before newline
            .substringBefore("||||")  // Before the empty pipes
        val removedTreatmentTimeStamp = Regex("""Removed TQ at (.*?) \(Location: Junctional\)""")
            .find(result)
            ?.groupValues
            ?.getOrNull(1)
        val processor = HL7Processor()
        val cleanResult = result
            .let { processor.replaceTimestamp(it, "PV1", 45, "ttt")}
            .replace(noteTime, "ttt")
            .let { if (removedTreatmentTimeStamp != null) it.replace(removedTreatmentTimeStamp, "ttt") else it }
        
        Assert.assertEquals(expected, cleanResult)

        // Make sure the time in the note is relatively recent (within last second)
        val tsHandler = CommonTS()
        tsHandler.value = noteTime
        Assert.assertTrue(tsHandler.valueAsDate.time > Date().time - 5000)
    }

    override fun testORUZB2(){
        //ARRANGE
        //ACT
        val result = ORU_ZB2Builder(Endpoint.MHSG_T).populateAll(hl7Data).build().toResult(Endpoint.MHSG_T,false, true)

        //ASSERT
        val expected = """
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.MHSG_T)}|xxx|${msh05ReceiveApplicationCode(Endpoint.MHSG_T)}|${msh06ReceivingFacilityCode(Endpoint.MHSG_T)}|zzz||ORU^ZB2|yyy|${msh11ProcessingIDCode(Endpoint.MHSG_T)}|${msh12VersionIDCode(Endpoint.MHSG_T)}
            ${pidSegment(hl7Data, EndpointType.MHSG_T)}
            ${pv1Segment(hl7Data)}
            OBR|1||${hl7Data.document!!.id.hashedWith("blood").asUuidString()}|1^Blood products^LOINC|||ttt|||||||||||||||ttt||BB|F
            NTE|1||test|47
            ZUI|1|${hl7Data.document!!.id.hashedWith("blood").asUuidString()}|UNKNOWN
            ZBP|1|123456|654321|55555|Fresh Whole Blood(FWB)^description^alt description|TRANSFUSED|20241118143430|20241218143430|O|Rh-Pos|||Tacos^^147258369||||N|500|300|100|1||mLs
        """.trimIndent().replace("\n", "\r")
        val cleanResult = result.lines().joinToString("\r") { line ->
            if (line.startsWith("OBR|")){
                line.replace(Regex("""\d{14}\+\d{4}"""), "ttt")
            }else {
                line
            }
        }

        Assert.assertEquals(expected, cleanResult)
    }

    override fun testORMO01() {
        // ARRANGE
        MedicationConverter.map.putAll(mapOf(
            buildMapEntry("0.9% Sodium Chloride", "1159317", RxNorm),
            buildMapEntry("Ertapenem", "325642", RxNorm),
            buildMapEntry("Acetaminophen","161", RxNorm),
            buildMapEntry("Fresh blood","","")
        ))

        UnitConverter.map.putAll(mapOf(
            buildMapEntry("g", "Gram", DHMSM_ICD),
            buildMapEntry("mg", "Milligrams", DHMSM_ICD),
        ))

        RouteConverter.map.putAll(mapOf(
            buildMapEntry("IM", "IntraMuscular", DHMSM_ICD),
            buildMapEntry("NS", "Nostril-Both", DHMSM_ICD),
            buildMapEntry("IVPU", "IV Push", DHMSM_ICD),
            buildMapEntry("PO", "Oral", DHMSM_ICD),
            buildMapEntry("PR", "Rectal", DHMSM_ICD),
        ))

        // ACT
        val result_doc = ORM_O01Builder(Endpoint.MHSG_T).populateAll(hl7Data).joinToString("\r"){it.toResult(Endpoint.MHSG_T,false, true)}

        // ASSERT
        val expected_doc = """
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.MHSG_T)}|xxx|${msh05ReceiveApplicationCode(Endpoint.MHSG_T)}|${msh06ReceivingFacilityCode(Endpoint.MHSG_T)}|zzz||ORM^O01|yyy|${msh11ProcessingIDCode(Endpoint.MHSG_T)}|${msh12VersionIDCode(Endpoint.MHSG_T)}
            ${pidSegment(hl7Data, EndpointType.MHSG_T)}
            ${pv1Segment(hl7Data)}
            ORC|NW|RE^^${hl7Data.document!!.medicines.list[0].id.asUuidString()}|||CM||||20230627122108+0000
            RXO|1234^0.9% Sodium Chloride^RxNorm|5.0||g^Gram^DHMSM-ICD
            RXR|IM^IntraMuscular^DHMSM-ICD
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.MHSG_T)}|xxx|${msh05ReceiveApplicationCode(Endpoint.MHSG_T)}|${msh06ReceivingFacilityCode(Endpoint.MHSG_T)}|zzz||ORM^O01|yyy|${msh11ProcessingIDCode(Endpoint.MHSG_T)}|${msh12VersionIDCode(Endpoint.MHSG_T)}
            ${pidSegment(hl7Data, EndpointType.MHSG_T)}
            ${pv1Segment(hl7Data)}
            ORC|NW|RE^^${hl7Data.document!!.medicines.list[1].id.asUuidString()}|||CM||||20230627122108+0000
            RXO|325642^Ertapenem^RxNorm|1.0||g^Gram^DHMSM-ICD
            RXR|IVPU^IV Push^DHMSM-ICD
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.MHSG_T)}|xxx|${msh05ReceiveApplicationCode(Endpoint.MHSG_T)}|${msh06ReceivingFacilityCode(Endpoint.MHSG_T)}|zzz||ORM^O01|yyy|${msh11ProcessingIDCode(Endpoint.MHSG_T)}|${msh12VersionIDCode(Endpoint.MHSG_T)}
            ${pidSegment(hl7Data, EndpointType.MHSG_T)}
            ${pv1Segment(hl7Data)}
            ORC|NW|RE^^${hl7Data.document!!.medicines.list[2].id.asUuidString()}|||CM||||20230627122108+0000
            RXO|161^Acetaminophen^RxNorm|99.0
            RXR|PR^Rectal^DHMSM-ICD
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.MHSG_T)}|xxx|${msh05ReceiveApplicationCode(Endpoint.MHSG_T)}|${msh06ReceivingFacilityCode(Endpoint.MHSG_T)}|zzz||ORM^O01|yyy|${msh11ProcessingIDCode(Endpoint.MHSG_T)}|${msh12VersionIDCode(Endpoint.MHSG_T)}
            ${pidSegment(hl7Data, EndpointType.MHSG_T)}
            ${pv1Segment(hl7Data)}
            ORC|NW|RE^^${hl7Data.document!!.medicines.list[4].id.asUuidString()}|||CM||||20230627122108+0000
            RXO|1159317^0.9% Sodium Chloride^RxNorm|5.0||g^Gram^DHMSM-ICD
            RXR|IM^IntraMuscular^DHMSM-ICD
        """.trimIndent().replace("\n", "\r")
        Assert.assertEquals(expected_doc, result_doc)

        // TODO: Add back in orders to the end of this message if they come back into the encounters
        //  Should look something like this:
        //  ORC|NW||||IP||^PRN||20230627122108+0000
        //  RXO|161^Acetaminophen^CNUM|1000.0||mg^Milligrams
        //  RXR|PO^Oral
    }

    override fun testADTA03() {
        // ARRANGE

        // ACT
        val result = ADT_A03Builder(Endpoint.MHSG_T).populateAll(hl7Data).build().toResult(Endpoint.MHSG_T,hasPv1Discharge = true)

        // ASSERT
        val expected = """
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.MHSG_T)}|xxx|${msh05ReceiveApplicationCode(Endpoint.MHSG_T)}|${msh06ReceivingFacilityCode(Endpoint.MHSG_T)}|zzz||ADT^A03|yyy|${msh11ProcessingIDCode(Endpoint.MHSG_T)}|${msh12VersionIDCode(Endpoint.MHSG_T)}
            ${evnSegment("A03")}
            ${pidSegment(hl7Data, EndpointType.MHSG_T)}
            ${pv1Segment(hl7Data)}
        """.trimIndent().replace("\n", "\r")
        Assert.assertEquals(expected, result)
    }
    
    @Test
    fun testClinicalNote_Meds() = assertClinicalNoteData(
        buildLogMedicineCommand(
            Medicine(
                "Acetaminophen/Aspirin/Caffeine",
                ndc = null,
                rxcui = "",
                administrationTime = Instant.ofEpochMilli(1687868185003),
                route = "IM",
                volume = 25f,
                unit = "cc",
                type = "Analgesic"
            )
        ), """
            OBX|5|TX|18761-7^Transfer summary note^LOINC||______________ Medications Summary List ______________||||||F|||ttt
            OBX|6|TX|18761-7^Transfer summary note^LOINC||Total number of Medications: 1||||||F|||ttt
            OBX|7|TX|18761-7^Transfer summary note^LOINC||Medication #1: Acetaminophen/Aspirin/Caffeine 25 cc administered via IM at 2023-06-27T12:21:08Z||||||F|||ttt
        """
    )
    private fun assertClinicalNoteData(command: Message, expectedClinicalNoteSegments: String){
        //ARRANGE
        hl7Data.commandsByEncounter = emptyMap()
        val commandList = listOf(
            command,
            buildChangeOpenCloseEncounterCommand(open = true)
        ).map(::buildCommandDataFromCommand)
        hl7Data.commandsByEncounter = mapOf(hl7Data.activeEncounterId!! to commandList)
        hl7Data.providerInfo = ProviderInfo("providerfirst providerlast", "123456")
        hl7Data.commandsList = buildSingletonCommandDataList(command)
        hl7Data.externalIds = mapOf(oidIPI to "testid")

        val document = hl7Data.getDocumentForEncounter(hl7Data.activeEncounterId!!)
        
        //ACT
        val result = ORU_R01Builder(Endpoint.MHSG_T).populateAll(hl7Data).joinToString("\r") { it.toResult(
            Endpoint.MHSG_T, false) }
        
        //ASSERT
        val expected = """
            MSH|^~\&|${msh03SendApplicationCode(Endpoint.MHSG_T)}|xxx|${msh05ReceiveApplicationCode(Endpoint.MHSG_T)}|${msh06ReceivingFacilityCode(Endpoint.MHSG_T)}|zzz||ORU^R01|yyy|${msh11ProcessingIDCode(Endpoint.MHSG_T)}|${msh12VersionIDCode(Endpoint.MHSG_T)}
            ${pidSegmentSimpleNoPatient(hl7Data)}
            PV1|1|E|^^^THEATER||||123456^providerlast^providerfirst|||||||||||HX|${hl7Data.document!!.id.asUuidString()}|||||||||||||||||||||||||ttt|ttt
            OBR|1||${document!!.id.hashedWith("allnotes").asUuidString()}|18761-7^Transfer summary note^LOINC^^BATDOK R1 to R3 Note|||ttt|||||||||||||||ttt|||F
            ZUI|1|${document.id.hashedWith("allnotes").asUuidString()}|UNKNOWN
            OBX|1|TX|18761-7^Transfer summary note^LOINC||Name: Unknown||||||F|||ttt
            OBX|2|TX|18761-7^Transfer summary note^LOINC||Provider: providerfirst providerlast||||||F|||ttt
            OBX|3|TX|18761-7^Transfer summary note^LOINC||Patient EDIPI: Unknown||||||F|||ttt
            OBX|4|TX|18761-7^Transfer summary note^LOINC||Injury Date/Time: Unknown||||||F|||ttt
            ${expectedClinicalNoteSegments.trim()}
        """.trimIndent().replace("\n","\r")
        val processor = HL7Processor()
        val noteTime = result.substringAfter("18761-7^Transfer summary note^LOINC^^BATDOK R1 to R3 Note|||")  // After note tag
            .substringBefore('\r')  // Before newline
            .substringBefore("||||")  // Before the empty pipes
        val cleanResult = result.replace(noteTime, "ttt")
            .let { processor.replaceTimestamp(it, "PV1", 45, "ttt")}
            .let { processor.replaceTimestamp(it, "PV1", 44, "ttt")}
        Assert.assertEquals(expected, cleanResult)
    }
}
