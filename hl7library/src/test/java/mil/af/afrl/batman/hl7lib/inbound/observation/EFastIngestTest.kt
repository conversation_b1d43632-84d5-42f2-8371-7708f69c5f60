package mil.af.afrl.batman.hl7lib.inbound.observation

import gov.afrl.batdok.encounter.observation.EFastExamData
import kotlinx.coroutines.test.runTest
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.message.MessageReceiver
import org.junit.Assert
import org.junit.Test
import java.io.ByteArrayInputStream

class EFastIngestTest {

    @Test
    fun ingestEFast_BATDOK() = runTest {
        // We will ingest our own data coming from OMDS
        // ARRANGE
        val msg = """
            MSH|^~\&|BATDOK||||20250603171645+0000||ORU^R01|9cf0f8fc-2e7f-4c9f-b753-a3bb61b21234|P|2.3
            PID|1|||47bfe63c-c363-36e7-a8f5-a4610d39e22f^^^2.16.840.1.113883.3.42.10033.100001.13^BATDOK|UB-NKKIX^CHEN^^^^^L||19000101|||||||||||85a0f500-5c04-37b5-8149-d66bde4f5f0e
            PV1|1|E|^^^THEATER HX||||1111111118^Doe^John|||||||||||HX|||||||||||||||||||||||D|||20250603131214+0000|20250603171645+0000
            ORC|CM
            OBR|1|||123320627^PoC US E-FAST Scan|||20250603141148+0000|||||||||||||||20250603141148+0000|||F
            ZUI|1|b408d28c-45ef-3a53-a8f0-a61a35c6f141|UNKNOWN
            OBX|1|TX|24949005^Pericardial sac structure (body structure)^SCT|1|Negative||||||F|||20250603141148+0000
            OBX|2|TX|260385009^Negative (qualifier)^SCT|1|Negative||||||F|||20250603141148+0000
            OBX|3|TX|50519007^Structure of right upper quadrant of abdomen (body structure)^SCT|2|Positive||||||F|||20250603141148+0000
            OBX|4|TX|10828004^Positive (qualifier)^SCT|2|Positive||||||F|||20250603141148+0000
            OBX|5|TX|86367003^Structure of left upper quadrant of abdomen (body structure)^SCT|3|Equivocal||||||F|||20250603141148+0000
            OBX|6|TX|42425007^Equivocal (qualifier)^SCT|3|Equivocal||||||F|||20250603141148+0000
            OBX|7|TX|11708003^Hypogastric region structure (body structure)^SCT|4|Negative||||||F|||20250603141148+0000
            OBX|8|TX|260385009^Negative (qualifier)^SCT|4|Negative||||||F|||20250603141148+0000
            OBX|9|TX|Report|5|Positive||||||F|||20230627122108+0000
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))

        // ASSERT
        val doc = hl7Data.document!!
        Assert.assertEquals(1, doc.observations.size)
        Assert.assertTrue(doc.observations.list.first().observationData is EFastExamData)
        val efastData = doc.observations.list.first().getData<EFastExamData>()!!
        Assert.assertEquals(EFastExamData.Type.NEGATIVE.dataString, efastData.pericardialFluid)
        Assert.assertEquals(EFastExamData.Type.POSITIVE.dataString, efastData.rightUpperQuadrant)
        Assert.assertEquals(EFastExamData.Type.EQUIVOCAL.dataString, efastData.leftUpperQuadrant)
        Assert.assertEquals(EFastExamData.Type.NEGATIVE.dataString, efastData.suprapubicFluid)
        Assert.assertEquals(EFastExamData.Type.POSITIVE.dataString, efastData.interpretation)
    }

    @Test
    fun ingestEFast_CDP() = runTest {
        // ARRANGE
        val msg = """
            MSH|^~\&|CDP|AAC8726A-2AC5-492D-BE86-0470C533FBC7^T6_Test_Hospital|BATDOK||20250603174951+0000||ORU^R01|3629b03e-9fc8-4bea-ae5d-c54ae04f5c85|T|2.3
            PID|1|||D2235556-B19D-41E0-82CC-417E47114718^^^2.16.840.1.113883.3.42.10032.100001.13^TAC|UT-DBDEC^OTES||19270603|M
            PV1|1|E||||||||||||||||||||||||||||||||||||||||||20250603174836+0000
            ORC|RE
            OBR|1|||123320627^PoC US E-FAST Scan^GENESISAlias|||20250603174913+0000||||||||RAD||||||||||F
            ZUI|1|F4BDFA41-356D-44FE-86F5-02DE556B9D44|UNKNOWN
            OBX|1|TX|44029006^Left lung structure (body structure)^SCT|1|Negative||||||F|||20250603174901+0000
            OBX|2|ST|260385009^Negative (qualifier)^SCT|1|Negative
            ZUI|2|5A107C25-3102-451C-8558-6E5F0EDB0B8D|UNKNOWN
            OBX|3|TX|24949005^Pericardial sac structure (body structure)^SCT|2|Equivocal||||||F|||20250603174903+0000
            OBX|4|ST|42425007^Equivocal (qualifier)^SCT|2|Equivocal
            ZUI|3|18A68EF3-815D-4DDA-8427-0DE878D68D39|UNKNOWN
            OBX|5|TX|3341006^Right lung structure (body structure)^SCT|3|Positive||||||F|||20250603174859+0000
            OBX|6|ST|10828004^Positive (qualifier)^SCT|3|Positive
            ZUI|4|2A7B0919-1130-46A4-ABF9-87D811E4599C|UNKNOWN
            OBX|7|TX|50519007^Structure of right upper quadrant of abdomen (body structure)^SCT|4|Positive||||||F|||20250603174905+0000
            OBX|8|ST|10828004^Positive (qualifier)^SCT|4|Positive
            ZUI|5|2264BDF6-A81E-4E9C-A5F3-52AFE1B5E62B|UNKNOWN
            OBX|9|TX|11708003^Hypogastric region structure (body structure)^SCT|5|Equivocal||||||F|||20250603174907+0000
            OBX|10|ST|42425007^Equivocal (qualifier)^SCT|5|Equivocal
            ZUI|6|B73E6EEF-14CE-424D-AB18-1D1A4D51EE26|UNKNOWN
            OBX|11|TX|86367003^Structure of left upper quadrant of abdomen (body structure)^SCT|6|Negative||||||F|||20250603174906+0000
            OBX|12|ST|260385009^Negative (qualifier)^SCT|6|Negative
            ZUI|7|DB2CC98D-930A-440B-890A-86CF0EB503FA|UNKNOWN
            OBX|13|TX|Report|7|Positive||||||F|||20250603174859+0000
            ZUI|8|5772E7CF-954A-42F1-8EB4-1F4A5E9BF6FF|UNKNOWN
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))

        // ASSERT
        val doc = hl7Data.document!!
        Assert.assertEquals(1, doc.observations.size)
        Assert.assertTrue(doc.observations.list.first().observationData is EFastExamData)
        val efastData = doc.observations.list.first().getData<EFastExamData>()!!
        Assert.assertEquals(EFastExamData.Type.NEGATIVE.dataString, efastData.leftLungSliding)
        Assert.assertEquals(EFastExamData.Type.POSITIVE.dataString, efastData.rightLungSliding)
        Assert.assertEquals(EFastExamData.Type.EQUIVOCAL.dataString, efastData.pericardialFluid)
        Assert.assertEquals(EFastExamData.Type.POSITIVE.dataString, efastData.rightUpperQuadrant)
        Assert.assertEquals(EFastExamData.Type.NEGATIVE.dataString, efastData.leftUpperQuadrant)
        Assert.assertEquals(EFastExamData.Type.EQUIVOCAL.dataString, efastData.suprapubicFluid)
        Assert.assertEquals(EFastExamData.Type.POSITIVE.dataString, efastData.interpretation)

    }

}
