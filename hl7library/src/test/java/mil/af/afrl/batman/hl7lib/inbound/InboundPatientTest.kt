package mil.af.afrl.batman.hl7lib.inbound

import com.google.protobuf.kotlin.isA
import gov.afrl.batdok.commands.proto.InfoCommands
import gov.afrl.batdok.commands.proto.TreatmentCommands.TQ
import gov.afrl.batdok.encounter.ids.EncounterVitalId
import gov.afrl.batdok.encounter.ids.EventId
import gov.afrl.batdok.encounter.ids.LabId
import gov.afrl.batdok.encounter.ids.MedicineId
import gov.afrl.batdok.encounter.ids.VentId
import gov.afrl.batdok.encounter.panel.KnownLabs
import gov.afrl.batdok.encounter.treatment.GastricTubeData
import gov.afrl.batdok.encounter.treatment.PelvicBinderData
import gov.afrl.batdok.encounter.treatment.TqData
import gov.afrl.batdok.encounter.treatment.TubeData
import gov.afrl.batdok.encounter.vitals.Avpu
import gov.afrl.batdok.encounter.vitals.HR
import gov.afrl.batdok.encounter.vitals.Pain
import gov.afrl.batdok.encounter.vitals.Temp
import io.mockk.coEvery
import io.mockk.mockkObject
import kotlinx.coroutines.test.runTest
import mil.af.afrl.batman.hl7lib.converter.ClinicalNoteConverter
import mil.af.afrl.batman.hl7lib.converter.DBAllergyConverter
import mil.af.afrl.batman.hl7lib.converter.LocationInjuryConverter
import mil.af.afrl.batman.hl7lib.converter.MedicationConverter
import mil.af.afrl.batman.hl7lib.converter.MoiConverter
import mil.af.afrl.batman.hl7lib.converter.TreatmentConverter
import mil.af.afrl.batman.hl7lib.data.models.v231.field.CNUM
import mil.af.afrl.batman.hl7lib.data.models.v231.field.CPT4
import mil.af.afrl.batman.hl7lib.data.models.v231.field.DCW
import mil.af.afrl.batman.hl7lib.data.models.v231.field.DNUM
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ICD10
import mil.af.afrl.batman.hl7lib.data.models.v231.field.RxNorm
import mil.af.afrl.batman.hl7lib.data.models.v231.field.SNOMED_CT
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.message.MessageReceiver
import mil.af.afrl.batman.hl7lib.outbound.buildMapEntry
import mil.af.afrl.batman.hl7lib.util.MockkRule
import mil.af.afrl.batman.hl7lib.util.generateDomainIdFromString
import mil.af.afrl.batman.hl7lib.util.hashedWith
import mil.af.afrl.batman.hl7lib.util.nonBloodMeds
import org.junit.Assert
import org.junit.Ignore
import org.junit.Rule
import org.junit.Test
import java.io.ByteArrayInputStream
import java.time.Instant

class InboundPatientTest {

    @get:Rule val mockkRule = MockkRule(this, relaxed = true)

    @Test
    fun testInboundADT_A04_injuryTime_OMDS() = runTest {
        // ARRANGE
        val msg = """
            MSH|^~\&|DOD_OMDS_THEATER||BATDOK||20240312121501+0000||ADT^A04|000001|P|2.3
            EVN|A04|20240215162356+0000|
            PID|1||**********^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI||Shirin^Rustam||19830203|M||||||||||||||||||||USA
            PV1|1|||||||||||||||||||||||||||||||||||||||||||20240123164900+0000
            ACC|20240123164900+0000
        """.trimIndent().replace("\n", "\r")

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals(1706028540L, document.info.timeInfo?.epochSecond)  // Jan 23, 2024 @ 16:49:00 UTC
    }

    @Test
    fun testInboundADT_A28_basicDemographics() = runTest {
        // ARRANGE
        val msgdata = """
            MSH|^~\&|DOD_OMDS_THEATER||BATDOK||20240312121501+0000||ADT^A28|000001|P|2.3
            EVN|A28|20240312121501+0000
            PID|1||123456^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI|84573E76-8C37-3C1A-B9FB-9F512BA70B92^^^2.16.840.1.113883.3.42.10033.100001.13^MPID^DODMOBDEVICETHEATER|Test^Teddy^J^^^^L||19891112|M||W||||||||987654321|123456789|||2186-5
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msgdata.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertTrue(hl7Data.commandsList.any { it.data.isA<InfoCommands.ChangeDodIdCommand>() })
        Assert.assertEquals("123456", document.info.dodId ?: "")
        Assert.assertEquals("Teddy", document.info.name?.first ?: "")
        Assert.assertEquals("J", document.info.name?.middle ?: "")
        Assert.assertEquals("Test", document.info.name?.last ?: "")
        Assert.assertEquals("1989-11-12", document.info.dob?.toString() ?: "")
        Assert.assertEquals("123456789", document.info.ssn ?: "")
        Assert.assertEquals("Male", document.info.gender ?: "")
        Assert.assertEquals("", document.info.nationality?.dataString ?: "")
        val extractedExternalIds = hl7Data.externalIds
        Assert.assertNotNull(extractedExternalIds)
        Assert.assertEquals(2, extractedExternalIds!!.size)  // Contains EDIPI and the other ID
        Assert.assertTrue(extractedExternalIds.containsKey("2.16.840.1.113883.3.42.10033.100001.13"))
        Assert.assertEquals("84573E76-8C37-3C1A-B9FB-9F512BA70B92", extractedExternalIds["2.16.840.1.113883.3.42.10033.100001.13"])
    }

    @Test
    fun testInboundADT_A28_MHSGT() = runTest {
        // ARRANGE
        val msgdata =
            """MSH|^~\&|THEATER|THEATER|BATDOK|BATDOK|20240325182935+0000||ADT^A28|Q2681344588T3135453344X0||2.3||||||8859/1
            EVN|A28|20240325182935+0000
            PID|1||3401078499902^^^MRN^MRN^""~016203099902000009YH^^^2.16.840.1.113883.3.42.10001.100001.228^IPI^""||test^eli^^^^^CURRENT||19960427|M||2106-3|||||""|M|""|||||2186-5||""|""|""|AD-AF|||""
            PD1|""|""||2^CERNER^CERNER^JUNIOR|""||""|""
            ZPI|1||||||||Y|2^CERNER^CERNER^JUNIOR||""|""|""|HUMAN|||||||""||||||||""|""||""||13434214321^^^^^^^543|20240320135945+0000|||||||||""|""|||||||||||||||||||||||||||||||||||||||||||||||||""|""
            OBX|1|CE|NOP_STATUS||40132671
            OBX|2|CE|CONSENT||40132671
            OBX|3|CE|ADD_AFFIL||100
            OBX|4|CE|AUTOREMINDER||Y
            OBX|5|CE|REMINDERTYPE||CALL
            OBX|6|CE|CONF_DEERS||111910255"""

        // ACT
        val hl7Data =
            MessageReceiver().processHL7Data(ByteArrayInputStream(msgdata.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertTrue(hl7Data.commandsList.none { it.data.isA<InfoCommands.ChangeDodIdCommand>() })
        Assert.assertEquals("", document.info.dodId ?: "")
        Assert.assertEquals("eli", document.info.name?.first ?: "")
        Assert.assertEquals("", document.info.name?.middle ?: "")
        Assert.assertEquals("test", document.info.name?.last ?: "")
        Assert.assertEquals("1996-04-27", document.info.dob?.toString() ?: "")
        Assert.assertEquals("", document.info.ssn ?: "")
        Assert.assertEquals("Male", document.info.gender ?: "")
        Assert.assertEquals("Masked by external application", document.info.maskingJustification)
        Assert.assertEquals("", document.info.nationality?.dataString ?: "")
        // TODO: Add ZEI examples if we get some of those too
    }

    @Test
    fun testInbound_A28_OMDS() = runTest {
        mockkObject(DBAllergyConverter)
        coEvery { DBAllergyConverter.nameToBatdokData(any()) } returns null

        // ARRANGE
        val msg = """
            MSH|^~\&|MIP|MIP|MSH-T|THEATER|20240730093012||ADT^A28|1E77FA2B-8F5E-4CC0-E063-1D028C0A400A|P|2.3
            PID|1|34013106000001^^^MRN^MRN|2114651225^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI^DEERS~162202C0630A000018I1^^^2.16.840.1.113883.3.42.10001.100001.228^IPI||McConnell^Lucy^Charlotte^^^^L||20010515|F||2054-5|2001 N Central Ave^^Marshfield^WI^54449-8337^USA^HP~2001 N Central Ave^^Marshfield^WI^54449-8337^USA^PST|||||||54206526^^^FIN NBR^FIN NBR|011941423
            EVN|A28|20240730093012
            AL1|1|FA|d09513^peanut allergen extract^MULTUMDRUG|SV
            ZAL|ADD||85907595|85907595|ALLERGY|Active|82530^Anaphylaxis^ALLERGYREACTION||||20230818060354|tstacct^BROWN^KARL^L.^^MD^^^EXTERNAL ID^PRSNL^^^EXTID|0
            AL1|1|EA|d00651^Latrix^MULTUMDRUG|MO
            ZAL|ADD||85907589|85907589|ALLERGY|Active|82559^Rash^ALLERGYREACTION||||20230818060320|tstacct^BROWN^KARL^L.^^MD^^^EXTERNAL ID^PRSNL^^^EXTID|0
            AL1|1|DA|d00116^penicillin^MULTUMDRUG|MO
            ZAL|ADD||85911711|85911711|ALLERGY|Active|107005^Cough, chronic^ALLERGYREACTION||||20230818060446|tstacct^BROWN^KARL^L.^^MD^^^EXTERNAL ID^PRSNL^^^EXTID|0
            AL1|1|FA|14783659^Milk^ALLERGY|SV
            ZAL|ADD||85935583|85935583|ALLERGY|Active|82530^Anaphylaxis^ALLERGYREACTION||||20230830052840|tstacct^Bartula^Leldon^^^^^^EXTERNAL ID^PRSNL^^^EXTID|0
            AL1|1|DA|d00049^Tylenol 8 Hour Extended Relief^MULTUMDRUG|MO
            ZAL|ADD||85939611|85939611|ALLERGY|Active|30446^Headache^ALLERGYREACTION||||20230830052933|tstacct^Bartula^Leldon^^^^^^EXTERNAL ID^PRSNL^^^EXTID|0
            NTE|1||Allergic to dogs
            AL1|1|EA|526066319^Hay^ALLERGY|MO
            ZAL|ADD||85935589|85935589|ALLERGY|Active|30956^Sneezing^ALLERGYREACTION||||20230830053116|tstacct^Bartula^Leldon^^^^^^EXTERNAL ID^PRSNL^^^EXTID|0
            AL1|1|EA|3000554539^Flowers^ALLERGY|MO
            ZAL|ADD||100503573|100503573|ALLERGY|Active|30956^Sneezing^ALLERGYREACTION||||20230928052657|tstacct^Baumgarten^Harvey^^^^^^EXTERNAL ID^PRSNL^^^EXTID|0
            AL1|1|DA|d04168^Tylenol Allergy Complete NightTime^MULTUMDRUG|MI
            ZAL|ADD||100507555|100507555|ALLERGY|Active|82530^Anaphylaxis^ALLERGYREACTION||||20230928052756|tstacct^Baumgarten^Harvey^^^^^^EXTERNAL ID^PRSNL^^^EXTID|0
            AL1|1|FA|14783659^Milk^ALLERGY|SV
            ZAL|ADD||100503579|100503579|ALLERGY|Active|107006^Cough after eating^ALLERGYREACTION||||20230928052838|tstacct^Baumgarten^Harvey^^^^^^EXTERNAL ID^PRSNL^^^EXTID|0
            PR1|1||^Repair Tendon Ankle/Foot (Right)||20240712125300||88||19971308^GENERAL||113|||0
            ZPR|1||2
            PR1|2||^Arthroscopy Ankle (Right)||20240712125300||88||19971308^GENERAL||113|||0
            ZPR|1||2
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals(8, document.info.allergies.size)
        Assert.assertEquals("peanut allergen extract", document.info.allergies.first())
        Assert.assertEquals(2, document.treatments.size)
        Assert.assertEquals("Repair Tendon Ankle/Foot (Right)", document.treatments.list.first().name)
    }

    @Test
    fun testInbound_A31_OMDS_procedureAttrs() = runTest {
        // ARRANGE/
        val msg = """
            MSH|^~\&|BATDOK||||20250501145336+0000||ADT^A31|3fedfa66-a548-48d2-9c42-8192fb07b83c|P|2.3
            EVN|A31|20250501145336+0000
            PID|1||1011353535^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI|29a90334-dc84-30de-ad45-d49f46047768^^^2.16.840.1.113883.3.42.10033.100001.13^BATDOK|UB-QPXNS^KORE^^^^^L||19000101|||||||||||e8f94564-069b-3681-82b6-e93c1885eed2
            PV1|1|E|^^^THEATER HX||||1111111118^Doe^John|||||||||||HX|||||||||||||||||||||||D|||20250501124323+0000|20250501145336+0000
            PR1|1||735346000^Control of bleeding by application of direct pressure^SCT|Direct Pressure|20250501124326+0000|P
            PR1|2||3895009^Application of dressing (procedure)^SCT|Dressing (Type: Hemostatic - Location: Junctional - Right Lower)|20250501124333+0000|P
            OBX|1|TX|261365000^Hemostatic gauze (physical object)^SCT|1|Hemostatic||||||F|||20250501124333+0000
            OBX|2|TX|1481000124102^Wound dressing observable (observable entity)^SCT|2|Junctional - Right Lower||||||F|||20250501124333+0000
            PR1|3||767693007^Pelvic binder (physical object)^SCT|Pelvic Binder (hhh)|20250501124337+0000
            OBX|1|TX|410657003^Type (attribute)^SCT|1|hhh||||||F|||20250501124337+0000
            PR1|4||3895009^Application of dressing (procedure)^SCT|Dressing (Type: Pressure - Location: RUE)|20250501124339+0000|P
            OBX|1|TX|118414001^Pressure dressing, device (physical object)^SCT|1|Pressure||||||F|||20250501124339+0000
            OBX|2|TX|1481000124102^Wound dressing observable (observable entity)^SCT|2|RUE||||||F|||20250501124339+0000
            PR1|5||20655006^Application of tourniquet (procedure)^SCT|TQ (Location: Extremity, Sub-Location: LUE)|20250501124341+0000|P
            OBX|1|TX|66019005^Limb structure (body structure)^SCT|1|Extremity||||||F|||20250501124341+0000
            OBX|2|TX|368208006^Left upper arm structure (body structure)^SCT|2|LUE||||||F|||20250501124341+0000
            PR1|6||20655006^Application of tourniquet (procedure)^SCT|TQ (Location: Truncal)|20250501124342+0000|P
            OBX|1|TX|22943007^Trunk structure (body structure)^SCT|1|Truncal||||||F|||20250501124342+0000
            PR1|7||241019003^Packing of wound (procedure)^SCT|Wound Packing (Type: Non Hemostatic, Location: LLE)|20250501124350+0000
            OBX|1|TX|1581000124103^Wound dressing type (observable entity)^SCT|1|Non Hemostatic||||||F|||20250501124350+0000
            OBX|2|TX|1481000124102^Wound dressing observable (observable entity)^SCT|2|LLE||||||F|||20250501124350+0000
            PR1|8||398142004^Emergency cricothyroidotomy^SCT|CRIC (Size: 55 mm)|20250501124356+0000|P
            PR1|9||112798008^Insertion of endotracheal tube (procedure)^SCT|ET-Tube (Confirmation: Visual, Depth: 553 cm, Size: 1 mm)|20250501124403+0000|P
            OBX|1|NM|131197000^Depth (qualifier value)^SCT|1|553|cm^Centimeter^DHMSM-ICD|||||F|||20250501124403+0000
            PR1|10||425696007^Manual respiratory assistance using bag and mask (procedure)^SCT|O2 (Source: BVM)|20250501124407+0000|P
            PR1|11||1290622004^Needle chest decompression for tension pneumothorax (procedure)^SCT|Needle-D (L Mid-Clav)|20250501124415+0000|P
            OBX|1|TX|7771000^Left (qualifier value)^SCT|1|L Mid-Clav||||||F|||20250501124415+0000
            OBX|2|TX|279013009^Midclavicular line (body structure)^SCT|2|L Mid-Clav||||||F|||20250501124415+0000
            PR1|12||446847002^Drainage of pleural cavity via chest tube (procedure)^SCT|Chest Tube (Location: Right side)|20250501124417+0000|P
            OBX|1|TX|24028007^Right (qualifier value)^SCT|1|Right side||||||F|||20250501124417+0000
            OBX|2|TX|91199000^Anterior axillary line structure (body structure)^SCT|2|Anterior Axillary||||||F|||20250501124417+0000
            PR1|13||4080807^Application of bandage^SCT|Band Aid|20250501124426+0000|P
            PR1|14||398041008^Cervical spine immobilization^SCT|Immobilization (C-Collar)|20250501124430+0000|P
            PR1|15||767693007^Pelvic binder (physical object)^SCT|Pelvic Binder (hj)|20250501124435+0000
            OBX|1|TX|410657003^Type (attribute)^SCT|1|hj||||||F|||20250501124435+0000
            PR1|16||467221000^Cervical spine immobilization frame (physical object)^SCT|Immobilization (C-Spine)|20250501124435+0000|P
            PR1|17||235425002^Insertion of orogastric tube (procedure)^SCT|Gastric Tube (Type: Oral)|20250501124443+0000|P
            PR1|18||52653008^Respiratory sounds (observable entity)^SCT|Breath Sounds (Left Normal, Right Normal, Left Absent)|20250501124411+0000|P
            OBX|1|TX|22803001L^Normal respiratory function (finding) - Left^SCT|1|Left Normal||||||F|||20250501124411+0000
            OBX|2|TX|22803001R^Normal respiratory function (finding) - Right^SCT|2|Right Normal||||||F|||20250501124411+0000
            OBX|3|TX|65503000L^Absent breath sounds (finding) - Left^SCT|3|Left Absent||||||F|||20250501124411+0000
            PR1|19||268925001^Examination of respiratory system (procedure)^SCT|Chest Equal Rise and Fall (R > L)|20250501124413+0000|P
            OBX|1|TX|248560005^Left side of chest moves less than right (finding)^SCT|1|R > L||||||F|||20250501124413+0000
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        // Validate all 19 treats appear somehow... chest equal is an observation
        Assert.assertEquals(18, document.treatments.size)
        Assert.assertEquals(1, document.observations.size)
        // Validate a few attributes... these are validated in detail in the treatment ingest tests
        // PR1 #5, LUE TQ
        val tq = document.treatments.list.filter { it.treatmentData is TqData }.find { it.getData<TqData>()!!.subLocation == "LUE" }
        Assert.assertNotNull(tq)
        // PR1 #15, Pelvic Binder - hj
        val pelvicBinder = document.treatments.list.findLast { it.treatmentData is PelvicBinderData }
        Assert.assertNotNull(pelvicBinder)
        Assert.assertEquals("hj", pelvicBinder!!.getData<PelvicBinderData>()!!.type)
        // PR1 #17, Orogastric Tube
        val tube = document.treatments.list.find { it.treatmentData is GastricTubeData }
        Assert.assertNotNull(tube)
        Assert.assertEquals("Oral", tube!!.getData<GastricTubeData>()!!.type)
    }

    @Test
    fun testInbound_A28_OMDS_stripNka() = runTest {
        mockkObject(DBAllergyConverter)
        coEvery { DBAllergyConverter.nameToBatdokData(any()) } returns null

        // ARRANGE
        val msg = """
            MSH|^~\&|MIP|MIP|MSH-T|THEATER|20240730093012||ADT^A28|1E77FA2B-8F5E-4CC0-E063-1D028C0A400A|P|2.3
            PID|1|34013106000001^^^MRN^MRN|2114651225^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI^DEERS~162202C0630A000018I1^^^2.16.840.1.113883.3.42.10001.100001.228^IPI||McConnell^Lucy^Charlotte^^^^L||20010515|F||2054-5|2001 N Central Ave^^Marshfield^WI^54449-8337^USA^HP~2001 N Central Ave^^Marshfield^WI^54449-8337^USA^PST|||||||54206526^^^FIN NBR^FIN NBR|011941423
            EVN|A28|20240730093012
            AL1|1|FA|d09513^peanut allergen extract^MULTUMDRUG|SV
            ZAL|ADD||85907595|85907595|ALLERGY|Active|82530^Anaphylaxis^ALLERGYREACTION||||20230818060354|tstacct^BROWN^KARL^L.^^MD^^^EXTERNAL ID^PRSNL^^^EXTID|0
            AL1|1|EA|NKA^No Known Allergy^MULTUMDRUG|MO
            ZAL|ADD||85907589|85907589|ALLERGY|Active|82559^Rash^ALLERGYREACTION||||20230818060320|tstacct^BROWN^KARL^L.^^MD^^^EXTERNAL ID^PRSNL^^^EXTID|0
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals(1, document.info.allergies.size)
        Assert.assertEquals("peanut allergen extract", document.info.allergies.first())
    }

    @Test
    fun testInbound_A28_OMDS_keepNka() = runTest {
        mockkObject(DBAllergyConverter)
        coEvery { DBAllergyConverter.nameToBatdokData(any()) } returns null

        // ARRANGE
        val msg = """
            MSH|^~\&|MIP|MIP|MSH-T|THEATER|20240730093012||ADT^A28|1E77FA2B-8F5E-4CC0-E063-1D028C0A400A|P|2.3
            PID|1|34013106000001^^^MRN^MRN|2114651225^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI^DEERS~162202C0630A000018I1^^^2.16.840.1.113883.3.42.10001.100001.228^IPI||McConnell^Lucy^Charlotte^^^^L||20010515|F||2054-5|2001 N Central Ave^^Marshfield^WI^54449-8337^USA^HP~2001 N Central Ave^^Marshfield^WI^54449-8337^USA^PST|||||||54206526^^^FIN NBR^FIN NBR|011941423
            EVN|A28|20240730093012
            AL1|1|EA|NKA^No Known Allergy^MULTUMDRUG|MO
            ZAL|ADD||85907589|85907589|ALLERGY|Active|82559^Rash^ALLERGYREACTION||||20230818060320|tstacct^BROWN^KARL^L.^^MD^^^EXTERNAL ID^PRSNL^^^EXTID|0
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals(1, document.info.allergies.size)
        Assert.assertEquals("No Known Allergy", document.info.allergies.first())
    }

    @Test
    fun testInboundADT_ZPR_procedures() = runTest {
        // ARRANGE
        TreatmentConverter.therapeuticProcedures.putAll(
            mapOf(
                buildMapEntry("Stent placement", "167491013", CPT4),
                buildMapEntry("Band Aid","4080807", SNOMED_CT)
                )
        )

        val msgdata = """
            MSH|^~\&|THEATER|THEATER|BATDOK|BATDOK|20240325182937+0000||ADT^ZPR|Q2681344614T3135453382||2.3||||||8859/1
            PID|1|3401078499902^^^MRN^MRN|3401078499902^^^MRN^MRN~016203099902000009YH^^^2.16.840.1.113883.3.42.10001.100001.228^IPI||test^eli^^^^^CURRENT||19960427|M||2106-3|||||""|M|""|5420382599902^^^FIN NBR^FIN NBR||||2186-5||""|""|""|AD-AF|""||""
            PV1|1|O|CD:118346297^^^THEATER^^^CD:118346291|C|||**********^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^NPI^PRSNL^^^^""~SN=668^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^**********^PRSNL^^^^""~**********^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^EDIPI^PRSNL^^^^""~2^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^PROVIDERMESSAGING^PRSNL^^^^""~US ARMY - **********^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^DEA^PRSNL^^^^""~668^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^364958627^PRSNL^^^^""~1982356^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^938068731^PRSNL^^^^""|||PT|""|""|""|23|""|Y||O||09||""||||||||||||||01|""|""|THEATER||D|||20240321181343+0000|20240321235959+0000
            PR1|1||167491013^Stent placement^SCT||20140320000000+0000|""|||""|||||0
            PR1|2||^some treatment||20140320000000+0000|""|||""|||||0
            PR1|3||4080807^Application of bandage^SCT||20140320000000+0000|""|||""|||||0
            ZPR|1|""|2
            """.trimIndent().replace("\n", "\r")

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msgdata.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        // MARCH procedures
        val treatList = document.treatments.list
        Assert.assertEquals(3, treatList.size)
        Assert.assertEquals("Stent placement", treatList[0].name)
        Assert.assertEquals(1395273600L, treatList[0].timestamp?.epochSecond)
        Assert.assertEquals("some treatment", treatList[1].name)
        Assert.assertEquals(1395273600L, treatList[1].timestamp?.epochSecond)
        Assert.assertEquals("Application of bandage", treatList[2].name)
        Assert.assertEquals(1395273600L, treatList[2].timestamp?.epochSecond)
        Assert.assertEquals("Masked by external application", document.info.maskingJustification)

        // Provider info
        val providerInfo = hl7Data.providerInfo
        Assert.assertNotNull(providerInfo)
        Assert.assertEquals("**********", providerInfo!!.dodId)
        Assert.assertEquals("CERNER JUNIOR CERNER", providerInfo.name)

        // PAMPI procedures
        val procedures = document.info.procedures
        Assert.assertEquals(3, procedures.size)
        Assert.assertEquals("Stent placement", procedures[0].name)
        Assert.assertEquals(1395273600L, procedures[0].date?.epochSecond)
        Assert.assertEquals("some treatment", procedures[1].name)
        Assert.assertEquals(1395273600L, procedures[1].date?.epochSecond)
        Assert.assertEquals("Application of bandage", procedures[2].name)
        Assert.assertEquals(1395273600L, procedures[2].date?.epochSecond)
    }

    @Test
    fun testInboundADT_A31() = runTest {
        // ARRANGE
        mockkObject(DBAllergyConverter)
        coEvery { DBAllergyConverter.nameToBatdokData(any()) } returns null
        val msgdata =
            """MSH|^~\&|DOD_OMDS_THEATER||BATDOK||20240312121501+0000||ADT^A31|000003|P|2.3
        EVN|A28|20240312121501+0000
        PID|1||123456^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI||Test^Teddy^J^^^^L||19891112|M||W||||||||987654321|123456789|||2186-5
        PV1|1||^^^THEATER|||||||||||||||HX|||||||||||||||||||||THEATER||D|||20230627120104+0000
        AL1|1|FA|91930004^PEANUTS^SCT|NE
        AL1|2|DA|294505008^Amoxicillin^SCT|NE
        AL1|3|EA|300916003^Pollen Allergy^SCT|NE
        AL1|4|OTH|OTH^test Other^SCT|NE"""

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msgdata.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        //Assert.assertEquals("123456",comList.info.dodId?: "")
        Assert.assertEquals("123456", document.info.dodId ?: "")
        Assert.assertEquals("Teddy", document.info.name?.first ?: "")
        Assert.assertEquals("J", document.info.name?.middle ?: "")
        Assert.assertEquals("Test", document.info.name?.last ?: "")
        Assert.assertEquals("1989-11-12", document.info.dob?.toString() ?: "")
        Assert.assertEquals("123456789", document.info.ssn ?: "")
        Assert.assertEquals("Male", document.info.gender ?: "")
        Assert.assertEquals("[PEANUTS, Amoxicillin, Pollen Allergy, test Other]", document.info.allergies.toString())
    }

    @Test
    fun testInboundADT_A31_injuries() = runTest {
        // ARRANGE
        val testInjuryCodes = listOf(
            "Name\tDefault\t",
            "AMPUTATION\tZ89.9",
            "BURN\tT24.012",
            "FIRE\tY26",
            "CRUSH\tT14.8",
            "GSW\tS51.841",
            "PUNCTURE_WOUND\tS41.141"
        ).joinToString("\n")
        LocationInjuryConverter.loadData(testInjuryCodes.byteInputStream(), ICD10)
        val msgdata =
            """MSH|^~\&|DOD_OMDS_THEATER||BATDOK||20240312121501+0000||ADT^A31|000003|P|2.3
        EVN|A28|20240312121501+0000
        PID|1||123456^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI||Test^Teddy^J^^^^L||19891112|M||W||||||||987654321|123456789|||2186-5
        PV1|1||^^^THEATER|||||||||||||||HX|||||||||||||||||||||THEATER||D|||20230627120104+0000
        DG1|1||Y26^Fire^ICD10|Fire|20230627120104+0000|A
        DG1|2||Z89.9^Anterior Right Wrist Amputation^ICD10|Amputation (Anterior Right Wrist)|20230627120104+0000|A
        DG1|3||Z89.9^Anterior Right Lower Leg Amputation^ICD10|Amputation (Anterior Right Lower Leg)|20230627120104+0000|A
        DG1|4||T14.8^Crush^ICD10|Crush|20230627120104+0000|A
        DG1|5||T24.012^Posterior Left Upper Leg Burn^ICD10|Burn (Posterior Left Upper Leg)|20230627120104+0000|A
        DG1|6||Z89.9^Anterior Left Hand Amputation^ICD10|Amputation|20230627120104+0000|A
        """

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msgdata.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        val injury = document.injuries.injuries
        val drawingPoint = document.injuries.drawingPoints
        Assert.assertNotNull(injury)
        Assert.assertEquals(0, document.injuries.mechanismsOfInjury.size)
        Assert.assertEquals(4, injury[null]!!.size)
        Assert.assertEquals(3, drawingPoint.size)
        Assert.assertEquals("DrawingPoint(label=AMP, x=0.09, y=0.48)", drawingPoint.first().toString())
        Assert.assertEquals("DrawingPoint(label=BU, x=0.71, y=0.61)", drawingPoint.last().toString())
        Assert.assertEquals("Fire", injury.values.first().first().injury)
        Assert.assertEquals("Burn", injury.values.last().last().injury)
    }

    @Test
    fun testInboundADT_A31_mois() = runTest {
        // ARRANGE
        MoiConverter.map.putAll(mapOf(
            buildMapEntry("Aircraft Incident", "V95.9", ICD10),
            buildMapEntry("Animal Bite/Mauling", "W55.81", ICD10),
            buildMapEntry("Asphyxiation", "R09.01", ICD10),
            buildMapEntry("Assault", "Y09", ICD10),
            buildMapEntry("CBRN", "Z77.29", ICD10),
            buildMapEntry("Blast", "Y37.2", ICD10),
            buildMapEntry("Fall", "W19", ICD10),
            buildMapEntry("Impalement", "W45.8", ICD10),
            buildMapEntry("Motor Vehicle Collision", "Y32", ICD10),
            buildMapEntry("Stab Wound", "Y28.9", ICD10),
            buildMapEntry("Sports", "Y93.79", ICD10),
            buildMapEntry("Parachute Incident/Airborne Operations", "V95.9", ICD10),
            buildMapEntry("Crush", "T14.8", ICD10),
            buildMapEntry("Burn", "T30.0", ICD10),
            buildMapEntry("Gunshot Wound", "Y24.9", ICD10)
        ))
        val msgdata =
            """MSH|^~\&|BATDOK||||20240312121501+0000||ADT^A31|000003|P|2.3
            EVN|A31|20240312121501+0000
            PID|1||123456^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI||Test^Teddy^J^^^^L||19891112|M||W||||||||987654321|123456789|||2186-5
            PV1|1|E|^^^THEATER HX||||1111111118^Doe^John|||||||||||HX|||||||||||||||||||||||D|||20241017171517+0000
            DG1|1||V95.9^Unspecified aircraft accident injuring occupant^ICD10|Aircraft Incident|20241017171517+0000|A
            DG1|2||W55.81^Bitten by other mammals^ICD10|Animal Bite/Mauling|20241017171517+0000|A
            DG1|3||R09.01^Asphyxia^ICD10|Asphyxiation|20241017171517+0000|A
            DG1|4||Y09^Assault by unspecified means^ICD10|Assault|20241017171517+0000|A
            DG1|5||Z77.29^Contact with and (suspected) exposure to other hazardous substances^ICD10|CBRN|20241017171517+0000|A
            DG1|6||Y37.2^Military operations involving other explosions and fragments^ICD10|Blast|20241017171517+0000|A
            DG1|7||W19^Unspecified fall^ICD10|Fall|20241017171517+0000|A
            DG1|8||W45.8^Other foreign body or object entering through skin^ICD10|Impalement|20241017171517+0000|A
            DG1|9||Y32^Crashing of motor vehicle, undetermined intent^ICD10|Motor Vehicle Collision|20241017171517+0000|A
            DG1|10||Y28.9^Contact with unspecified sharp object, undetermined intent^ICD10|Stab Wound|20241017171517+0000|A
            DG1|11||Y93.79^Activity, other specified sports and athletics^ICD10|Sports|20241017171517+0000|A
            DG1|12||V95.9^Unspecified aircraft accident injuring occupant^ICD10|Parachute Incident/Airborne Operations|20241017171517+0000|A
            DG1|13||T14.8^Other injury of unspecified body region^ICD10|Crush|20241017171517+0000|A
            DG1|14||T30.0^Burn of unspecified body region, unspecified degree^ICD10|Burn|20241017171517+0000|A
            DG1|15||Y24.9^Unspecified firearm discharge, undetermined intent^ICD10|Gunshot Wound|20241017171517+0000|A
            DG1|16||Z89.9^Acquired absence of limb, unspecified^ICD10|Amputation|20241017134344+0000|A
            """

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msgdata.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        val injury = document.injuries.injuries
        val mois = document.injuries.mechanismsOfInjury[null]
        Assert.assertNotNull(injury)
        Assert.assertNotNull(mois)
        Assert.assertEquals(15, mois!!.size)
        Assert.assertEquals(1, injury[null]!!.size)
        Assert.assertEquals("Amputation", injury.values.last().last().injury)
        Assert.assertEquals("Aircraft Incident", mois.first())
        Assert.assertEquals("Gunshot Wound", mois.last())
    }

    @Test
    fun testInboundADT_A31_InjuryAsMoi() = runTest {
        // ARRANGE
        MoiConverter.map.putAll(mapOf(
            buildMapEntry("Gunshot Wound", "Y24.9", ICD10)
        ))
        val testInjuryCodes = listOf(
            "Name\tDefault\t",
            "GSW\tY24.9",
        ).joinToString("\n")
        LocationInjuryConverter.loadData(testInjuryCodes.byteInputStream(), ICD10)
        val msgdata =
            """MSH|^~\&|BATDOK||||20240312121501+0000||ADT^A31|000003|P|2.3
            EVN|A31|20240312121501+0000
            PID|1||123456^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI||Test^Teddy^J^^^^L||19891112|M||W||||||||987654321|123456789|||2186-5
            PV1|1|E|^^^THEATER HX||||1111111118^Doe^John|||||||||||HX|||||||||||||||||||||||D|||20241017171517+0000
            DG1|1||Y24.9^Unspecified firearm discharge, undetermined intent^ICD10|Gunshot Wound|20241017171517+0000|A
            DG1|2||Y24.9^Unspecified firearm discharge, undetermined intent^ICD10|Gunshot Wound (Location Unknown)|20241017171517+0000|A
            """

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msgdata.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        val injury = document.injuries.injuries
        val mois = document.injuries.mechanismsOfInjury[null]
        Assert.assertNotNull(injury)
        Assert.assertNotNull(mois)
        Assert.assertEquals(1, mois!!.size)
        Assert.assertEquals(1, injury[null]!!.size)
        Assert.assertEquals("Gunshot Wound", injury.values.last().last().injury)
        Assert.assertEquals("Gunshot Wound", mois.last())
    }

    @Test
    fun testInboundADT_A31_MSHGT() = runTest {
        // ARRANGE
        mockkObject(DBAllergyConverter)
        coEvery { DBAllergyConverter.nameToBatdokData(any()) } returns null
        val msgdata =
            """MSH|^~\&|THEATER|THEATER|BATDOK|BATDOK|20240325182937+0000||ADT^A31|Q2681344604T3135453368||2.3||||||8859/1
            EVN|A31|20240325182937+0000
            PID|1|3401078499902^^^MRN^MRN|3401078499902^^^MRN^MRN~016203099902000009YH^^^2.16.840.1.113883.3.42.10001.100001.228^IPI||test^eli^^^^^CURRENT||19960427|M||2106-3|||||""|M|""|5420381599902^^^FIN NBR^FIN NBR||||2186-5||""|""|""|AD-AF|""||""
            PV1|1|O|CD:118346273^^^THEATER^^^CD:118346267|C|||**********^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^NPI^PRSNL^^^^""~SN=668^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^**********^PRSNL^^^^""~**********^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^EDIPI^PRSNL^^^^""~2^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^PROVIDERMESSAGING^PRSNL^^^^""~US ARMY - **********^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^DEA^PRSNL^^^^""~668^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^364958627^PRSNL^^^^""~1982356^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^938068731^PRSNL^^^^""|||CAR|""|""|""|23|""|""||O||09||""||||||||||||||68|""|""|THEATER||D|||20240320140242+0000|20240320170000+0000
            AL1|1|FA|^Peanuts|MI
            AL1|2|DA|d07697^Tydemy^MULTUMDRUG|MI|428699015"""

        // ACT
        val hl7Data =
            MessageReceiver().processHL7Data(ByteArrayInputStream(msgdata.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals("[Peanuts, Tydemy]", document.info.allergies.toString())
        Assert.assertEquals(1710943362L, document.info.timeInfo?.epochSecond)  // March 20, 2024 @ 14:02:42 UTC
    }

    @Test
    fun testInboundADT_A31_PR1_Removed() = runTest {
        // ARRANGE
        // Ensure non-removed instance of treatment stays, even if others are removed
        // Ensure non-removed treatments stay
        val msgdata = """MSH|^~\&|BATDOK||||20240924135016+0000||ADT^A31|6a0fbe27-1cfa-43da-bed7-2320d6cf587b|P|2.3
            EVN|A31|20240924135016+0000
            PID|1||2113192527^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI|8179ddf7-bdac-3c7f-93d0-b2674229b3de^^^2.16.840.1.113883.3.42.10033.100001.13^BATDOK|Meese^Trong^Arriola^^^^L||19980528|M||||||||||fbde66f1-90a4-3ba9-8ecb-8406fc5ace03
            PV1|1|E|^^^THEATER HX|||||||||||||||HX|||||||||||||||||||||||D|||20240924133527+0000
            PR1|1||12001^Simple repair of superficial wounds of scalp, neck, axillae, external genitalia, trunk and/or extremities (including hands and feet); 2.5 cm or less^CPT4|Dressing (Type: Abdominal - Location: LUE)|20240924134726+0000|P
            PR1|2||12001^Simple repair of superficial wounds of scalp, neck, axillae, external genitalia, trunk and/or extremities (including hands and feet); 2.5 cm or less^CPT4|Dressing (Type: Abdominal - Location: LUE)|20240924134731+0000|P
            PR1|3||12001^Simple repair of superficial wounds of scalp, neck, axillae, external genitalia, trunk and/or extremities (including hands and feet); 2.5 cm or less^CPT4|Dressing (Type: Abdominal - Location: LUE)|20240924134731+0000|P
            PR1|4||12001^Simple repair of superficial wounds of scalp, neck, axillae, external genitalia, trunk and/or extremities (including hands and feet); 2.5 cm or less^CPT4|AZT (Type: Abdominal - Location: LUE)|20240924134731+0000|P
            PR1|5||12001^Simple repair of superficial wounds of scalp, neck, axillae, external genitalia, trunk and/or extremities (including hands and feet); 2.5 cm or less^CPT4^^REMOVED|Dressing (Type: Abdominal - Location: LUE)|20240924134733+0000|P
            PR1|6||12001^Simple repair of superficial wounds of scalp, neck, axillae, external genitalia, trunk and/or extremities (including hands and feet); 2.5 cm or less^CPT4^^REMOVED|Dressing (Type: Abdominal - Location: LUE)|20240924134735+0000|P
            """.trimIndent().replace('\n', '\r')

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msgdata.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        // MARCH treatments
        val treatments = document.treatments.list
        Assert.assertEquals(4, treatments.size)
        Assert.assertEquals("Dressing", treatments[0].name)
        Assert.assertEquals("AZT", treatments[1].name)
        Assert.assertEquals("REMOVED Dressing", treatments[2].name)
        Assert.assertEquals("REMOVED Dressing", treatments[3].name)

        // PAMPI procedures
        val procedures = document.info.procedures
        Assert.assertEquals(2, procedures.size)
        Assert.assertEquals("Simple repair of superficial wounds of scalp, neck, axillae, external genitalia, trunk and/or extremities (including hands and feet); 2.5 cm or less", procedures[0].name)
        Assert.assertEquals("Simple repair of superficial wounds of scalp, neck, axillae, external genitalia, trunk and/or extremities (including hands and feet); 2.5 cm or less", procedures[1].name)
    }

    @Test
    fun testInboundORM_O01_cnum() = runTest {
        //Arrange
        MedicationConverter.map.putAll(
            mapOf(
                buildMapEntry("Sodium Chloride, 0.9%", "615106", CNUM),
                buildMapEntry("Ertapenem", "325642", CNUM),
                buildMapEntry("Acetaminophen", "161", CNUM),
            )
        )
        val msgdata = """
            MSH|^~\&|DOD_OMDS_THEATER||BATDOK||20240312121501+0000||ORM^O01|000002|P|2.3
            EVN|A28|20240312121501+0000
            PID|1||123456^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI||Test^Teddy^J^^^^L||19891112|M||W||||||||987654321|123456789|||2186-5
            PV1|1||^^^THEATER|||||||||||||||HX|||||||||||||||||||||THEATER||D|||20230627120104+0000|20240318120105+0000
            ORC|NW|12345|||NW||||20230627122108+0000
            RXO|615106^Sodium Chloride, 0.9%^CNUM|5.0||g^Gram
            RXR|IM^IntraMuscular
        """.trimIndent().replace('\n', '\r')

        //ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msgdata.toByteArray()))
        val document = hl7Data.document!!
        val meds = document.medicines.list.nonBloodMeds()

        // ASSERT
        Assert.assertEquals(1, meds.size)
        val sodiumChloride = meds[0]
        Assert.assertEquals(generateDomainIdFromString<MedicineId>("12345"), sodiumChloride.medId)
        Assert.assertEquals("Sodium Chloride, 0.9%", sodiumChloride.name)
        Assert.assertEquals("5.0", sodiumChloride.volume?.toString() ?: "")
        Assert.assertEquals("g", sodiumChloride.unit ?: "")
        Assert.assertEquals("IM", sodiumChloride.route ?: "")
        Assert.assertEquals("2023-06-27T12:21:08Z", sodiumChloride.administrationTime.toString())
    }

    @Test
    fun testInboundORM_O01_RxNorm() = runTest {
        //Arrange
        val msgdata = """
            MSH|^~\&|DOD_OMDS_THEATER||BATDOK||20240312121501+0000||ORM^O01|000002|P|2.3
            EVN|A28|20240312121501+0000
            PID|1||123456^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI||Test^Teddy^J^^^^L||19891112|M||W||||||||987654321|123456789|||2186-5
            PV1|1||^^^THEATER|||||||||||||||HX|||||||||||||||||||||THEATER||D|||20230627120104+0000|20240318120105+0000
            ORC|NW|12345|||NW||||20230627122108+0000
            RXO|615106^Sodium Chloride, 0.9%^RxNorm|5.0||g^Gram
            RXR|IM^IntraMuscular
        """.trimIndent().replace('\n', '\r')

        //ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msgdata.toByteArray()))
        val document = hl7Data.document!!
        val meds = document.medicines.list.nonBloodMeds()

        // ASSERT
        Assert.assertEquals(1, meds.size)
        val sodiumChloride = meds[0]
        Assert.assertEquals(generateDomainIdFromString<MedicineId>("12345"), sodiumChloride.medId)
        Assert.assertEquals("Sodium Chloride, 0.9%", sodiumChloride.name)
        Assert.assertEquals("5.0", sodiumChloride.volume?.toString() ?: "")
        Assert.assertEquals("g", sodiumChloride.unit ?: "")
        Assert.assertEquals("IM", sodiumChloride.route ?: "")
        Assert.assertEquals("2023-06-27T12:21:08Z", sodiumChloride.administrationTime.toString())
        Assert.assertEquals("615106", sodiumChloride.rxcui)
    }

    @Test
    fun testInboundORM_O01_noCode() = runTest {
        // ARRANGE
        val msgdata = """
            MSH|^~\&|DOD_OMDS_THEATER||BATDOK||20240312121501+0000||ORM^O01|000002|P|2.3
            EVN|A28|20240312121501+0000
            PID|1||123456^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI||Test^Teddy^J^^^^L||19891112|M||W||||||||987654321|123456789|||2186-5
            PV1|1||^^^THEATER|||||||||||||||HX|||||||||||||||||||||THEATER||D|||20230627120104+0000|20240318120105+0000
            ORC|NW|12345|||NW||||20230627122108+0000
            RXO|^unknown med|5.0||g^Gram
            RXR|IM^IntraMuscular
        """.trimIndent().replace('\n', '\r')

        //ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msgdata.toByteArray()))
        val document = hl7Data.document!!
        val meds = document.medicines.list.nonBloodMeds()

        // ASSERT
        Assert.assertEquals(1, meds.size)
        val med = meds[0]
        Assert.assertEquals(generateDomainIdFromString<MedicineId>("12345"), med.medId)
        Assert.assertEquals("unknown med", med.name)
        Assert.assertEquals("5.0", med.volume?.toString() ?: "")
        Assert.assertEquals("g", med.unit ?: "")
        Assert.assertEquals("IM", med.route ?: "")
        Assert.assertEquals("2023-06-27T12:21:08Z", med.administrationTime.toString())
        Assert.assertNull(med.rxcui)
    }

    @Test
    fun testInboundORM_O01_noMedName() = runTest {
        // ARRANGE
        val msgdata = """
            MSH|^~\&|DOD_OMDS_THEATER||BATDOK||20240312121501+0000||ORM^O01|000002|P|2.3
            EVN|A28|20240312121501+0000
            PID|1||123456^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI||Test^Teddy^J^^^^L||19891112|M||W||||||||987654321|123456789|||2186-5
            PV1|1||^^^THEATER|||||||||||||||HX|||||||||||||||||||||THEATER||D|||20230627120104+0000|20240318120105+0000
            ORC|NW|12345|||NW||||20230627122108+0000
            RXO|^^RxNorm|5.0||g^Gram
            RXR|IM^IntraMuscular
        """.trimIndent().replace('\n', '\r')

        //ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msgdata.toByteArray()))
        val document = hl7Data.document!!
        val meds = document.medicines.list.nonBloodMeds()

        // ASSERT
        Assert.assertEquals(1, meds.size)
        val med = meds[0]
        Assert.assertEquals(generateDomainIdFromString<MedicineId>("12345"), med.medId)
        Assert.assertEquals("", med.name)
        Assert.assertEquals("5.0", med.volume?.toString() ?: "")
        Assert.assertEquals("g", med.unit ?: "")
        Assert.assertEquals("IM", med.route ?: "")
        Assert.assertEquals("2023-06-27T12:21:08Z", med.administrationTime.toString())
        Assert.assertEquals("", med.rxcui)
    }

    @Test
    fun testInboundORM_O01_NoRXR1_1() = runTest {
        //Arrange
        val msgdata = """
            MSH|^~\&|DOD_OMDS_THEATER||BATDOK||20240312121501+0000||ORM^O01|000002|P|2.3
            EVN|A28|20240312121501+0000
            PID|1||123456^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI||Test^Teddy^J^^^^L||19891112|M||W||||||||987654321|123456789|||2186-5
            PV1|1||^^^THEATER|||||||||||||||HX|||||||||||||||||||||THEATER||D|||20230627120104+0000|20240318120105+0000
            ORC|NW|12345|||NW||||20230627122108+0000
            RXO|615106^Sodium Chloride, 0.9%^RxNorm|5.0||g^Gram
            RXR|^IntraMuscular
        """.trimIndent().replace('\n', '\r')

        //ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msgdata.toByteArray()))
        val document = hl7Data.document!!
        val meds = document.medicines.list.nonBloodMeds()

        // ASSERT
        Assert.assertEquals(1, meds.size)
        val sodiumChloride = meds[0]
        Assert.assertEquals(generateDomainIdFromString<MedicineId>("12345"), sodiumChloride.medId)
        Assert.assertEquals("Sodium Chloride, 0.9%", sodiumChloride.name)
        Assert.assertEquals("5.0", sodiumChloride.volume?.toString() ?: "")
        Assert.assertEquals("g", sodiumChloride.unit ?: "")
        Assert.assertEquals("IntraMuscular", sodiumChloride.route ?: "")
        Assert.assertEquals("2023-06-27T12:21:08Z", sodiumChloride.administrationTime.toString())
        Assert.assertEquals("615106", sodiumChloride.rxcui)
    }

    @Test
    fun testInboundORM_O01_NoRXO4_1() = runTest {
        //Arrange
        val msgdata = """
            MSH|^~\&|DOD_OMDS_THEATER||BATDOK||20240312121501+0000||ORM^O01|000002|P|2.3
            EVN|A28|20240312121501+0000
            PID|1||123456^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI||Test^Teddy^J^^^^L||19891112|M||W||||||||987654321|123456789|||2186-5
            PV1|1||^^^THEATER|||||||||||||||HX|||||||||||||||||||||THEATER||D|||20230627120104+0000|20240318120105+0000
            ORC|NW|12345|||NW||||20230627122108+0000
            RXO|615106^Sodium Chloride, 0.9%^RxNorm|5.0||^Gram
            RXR|IM^IntraMuscular
        """.trimIndent().replace('\n', '\r')

        //ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msgdata.toByteArray()))
        val document = hl7Data.document!!
        val meds = document.medicines.list.nonBloodMeds()

        // ASSERT
        Assert.assertEquals(1, meds.size)
        val sodiumChloride = meds[0]
        Assert.assertEquals(generateDomainIdFromString<MedicineId>("12345"), sodiumChloride.medId)
        Assert.assertEquals("Sodium Chloride, 0.9%", sodiumChloride.name)
        Assert.assertEquals("5.0", sodiumChloride.volume?.toString() ?: "")
        Assert.assertEquals("Gram", sodiumChloride.unit ?: "")
        Assert.assertEquals("IM", sodiumChloride.route ?: "")
        Assert.assertEquals("2023-06-27T12:21:08Z", sodiumChloride.administrationTime.toString())
        Assert.assertEquals("615106", sodiumChloride.rxcui)
    }

    @Test
    fun testInboundORM_O01_mixedCodes() = runTest {
        // ARRANGE
        MedicationConverter.map.putAll(mapOf(
            buildMapEntry("Sodium Chloride, 0.9%", "615106", DNUM),
            buildMapEntry("CNUM med", "123", CNUM),
            buildMapEntry("RxNorm med", "456", RxNorm),
        ))

        val msgdata = """
            MSH|^~\&|DOD_OMDS_THEATER||BATDOK||20240312121501+0000||ORM^O01|000002|P|2.3
            EVN|A28|20240312121501+0000
            PID|1||123456^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI||Test^Teddy^J^^^^L||19891112|M||W||||||||987654321|123456789|||2186-5
            PV1|1||^^^THEATER|||||||||||||||HX|||||||||||||||||||||THEATER||D|||20230627120104+0000|20240318120105+0000
            ORC|NW|12345|||NW||||20230627122108+0000
            RXO|615106^Sodium Chloride, 0.9%^DNUM|5.0||g^Gram
            RXR|IM^IntraMuscular
            ORC|NW|67890|||NW||||20230627122110+0000
            RXO|123^med^CNUM|5.0||g^Gram
            RXR|IO^Intraosseous
            ORC|NW|67891|||NW||||20230627122110+0000
            RXO|456^med2^RxNorm|5.0||g^Gram
            RXR|IO^Intraosseous
        """.trimIndent().replace('\n', '\r')

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msgdata.toByteArray()))
        val document = hl7Data.document!!
        val meds = document.medicines.list.nonBloodMeds()

        // ASSERT
        Assert.assertEquals(3, meds.size)

        val sodiumChloride = meds[0]
        Assert.assertEquals(generateDomainIdFromString<MedicineId>("12345"), sodiumChloride.medId)
        Assert.assertEquals("Sodium Chloride, 0.9%", sodiumChloride.name)
        Assert.assertEquals("5.0", sodiumChloride.volume?.toString() ?: "")
        Assert.assertEquals("g", sodiumChloride.unit ?: "")
        Assert.assertEquals("IM", sodiumChloride.route ?: "")
        Assert.assertEquals("2023-06-27T12:21:08Z", sodiumChloride.administrationTime.toString())

        val cnumMed = meds[1]
        Assert.assertEquals(generateDomainIdFromString<MedicineId>("67890"), cnumMed.medId)
        Assert.assertEquals("CNUM med", cnumMed.name)
        Assert.assertEquals("5.0", cnumMed.volume?.toString() ?: "")
        Assert.assertEquals("g", cnumMed.unit ?: "")
        Assert.assertEquals("IO", cnumMed.route ?: "")
        Assert.assertEquals("2023-06-27T12:21:10Z", cnumMed.administrationTime.toString())

        val rxNormMed = meds[2]
        Assert.assertEquals(generateDomainIdFromString<MedicineId>("67891"), rxNormMed.medId)
        Assert.assertEquals("RxNorm med", rxNormMed.name)
        Assert.assertEquals("5.0", rxNormMed.volume?.toString() ?: "")
        Assert.assertEquals("g", rxNormMed.unit ?: "")
        Assert.assertEquals("IO", rxNormMed.route ?: "")
        Assert.assertEquals("2023-06-27T12:21:10Z", rxNormMed.administrationTime.toString())
        Assert.assertEquals("456", rxNormMed.rxcui)
    }

    @Test
    fun testInboundORM_O01_obxNM() = runTest {
        // ARRANGE
        val msgdata = """
            MSH|^~\&|DHMSM_THEATER|99904|BATDOK-J|BATDOK-J|20240610193007+0000||ORM^O01|Q4430718291T5183813397|P|2.3||||||8859/1
            PID|1|6430727299904^^^MRN^MRN^DODMOBDEVICETHEATER|01620309990400000A5C^^^2.16.840.1.113883.3.42.10001.100001.228^IPI^DODMOBDEVICETHEATER~6430727299904^^^MRN^MRN^DODMOBDEVICETHEATER|84573E76-8C37-3C1A-B9FB-9F512BA70B92^^^2.16.840.1.113883.3.42.10033.100001.13^MPID^DODMOBDEVICETHEATER|Batdok^Barney^^^^^CURRENT||19000101|NA||||||||||9633442399904^^^FIN NBR^FIN NBR^DODMOBDEVICETHEATER|||||||0
            PV1|1|I|Ward Delta^3^A^THEATER^^^Inpt Nurse|C|||**********^Provider7^Test^^^^^^EDIPI^PRSNL|||IM||||23|||**********^Provider7^Test^^^^^^EDIPI^PRSNL|I||09|||||||||||||||||||THEATER||A|||20240607165000+0000
            ORC|NW|11542049591^HNAM_ORDERID|||CM||^^^20240610160100+0000^20240610170100+0000||20240610160100+0000|**********^Provider7^Test^^^^^^HCPT^PRSNL~**********^Provider7^Test^^^^^^DEA^PRSNL~**********^Provider7^Test^^^^^^NPI^PRSNL~**********^Provider7^Test^^^^^^EDIPI^PRSNL~**********^Provider7^Test^^^^^^PROVIDERMESSAGING^PRSNL|||||20240610161831+0000|^^^ADHOC_1
            RXO|5205^lactated ringers injection 1000 mL^CD:4005032^^lactated ringers drip (ANES) 1000 mL|1000||mL|3874311^Solution-IV||||||0||0
            RXR|IV^IV Drip (Intravenous)|||IV
            OBX|1|IS|12714^Stop Type||DRSTOP
            OBX|2|NM|12720^Total Volume||1,000
        """.trimIndent().replace('\n', '\r')

        //ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msgdata.toByteArray()))
        val document = hl7Data.document!!
        val meds = document.medicines.list.nonBloodMeds()

        // ASSERT
        //message should not fail if in obx2 has nm and obx5 has 1,000
        Assert.assertEquals(1, meds.size)
        val med = meds[0]
        Assert.assertEquals(generateDomainIdFromString<MedicineId>("11542049591"), med.medId)
        Assert.assertEquals("lactated ringers injection 1000 mL", med.name)
        Assert.assertEquals("1000.0", med.volume?.toString() ?: "")
        Assert.assertEquals("mL", med.unit ?: "")
        Assert.assertEquals("IV", med.route ?: "")
        Assert.assertEquals("2024-06-10T16:01:00Z", med.administrationTime.toString())
    }

    @Test
    fun testInboundORM_O01_noRXO_2() = runTest {
        // ARRANGE
        val msgdata = """
            MSH|^~\&|DHMSM_THEATER|99904|BATDOK-J|BATDOK-J|20240610201034+0000||ORM^O01|Q4430718547T5183813736|P|2.3||||||8859/1
            PID|1|6430724199904^^^MRN^MRN^DODMOBDEVICETHEATER|016203099904000009RL^^^2.16.840.1.113883.3.42.10001.100001.228^IPI^DODMOBDEVICETHEATER~6430724199904^^^MRN^MRN^DODMOBDEVICETHEATER|DF2CA7C1-8B1F-38C6-A167-DFAFD1008443^^^2.16.840.1.113883.3.42.10033.100001.13^MPID^DODMOBDEVICETHEATER|UB-UXDGT^WEYU_Tablet1^^^^^CURRENT||19000101|NA||||||||||9633437799904^^^FIN NBR^FIN NBR^DODMOBDEVICETHEATER|||||||0
            PV1|1|E|ER^ER-15^A^THEATER^^^Emergency|E||||||ER||||||||E|||||||||||||||||||||THEATER||A|||20240529204000+0000
            ORC|NW|11542050305^HNAM_ORDERID|||NW||^^^20240610000000+0000^20241206235959+0000||20240610200849+0000|**********^Pharmacist1^Test^^^^^^PROVIDERMESSAGING^PRSNL~**********^Pharmacist1^Test^^^^^^EDIPI^PRSNL|||||20240610200849+0000|^^^638955
            RXO|d00168^ALPRAZolam^DNUM^^ALPRAZolam 0.25 mg tablet|||^See Instructions|||||0||1|EA|2
            RXR|PO^Oral|||MED
            OBX|1|ID|12696^DAW||Y
            OBX|2|IS|12714^Stop Type||HARD
        """.trimIndent().replace('\n', '\r')

        //ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msgdata.toByteArray()))
        val document = hl7Data.document!!
        val meds = document.medicines.list.nonBloodMeds()

        // ASSERT
        Assert.assertEquals(1, meds.size)
        val med = meds[0]
        Assert.assertEquals(generateDomainIdFromString<MedicineId>("11542050305"), med.medId)
        Assert.assertEquals("ALPRAZolam", med.name)
        Assert.assertEquals("0.0", med.volume?.toString() ?: "")
        Assert.assertEquals("See Instructions", med.unit ?: "")
        Assert.assertEquals("PO", med.route ?: "")
        Assert.assertEquals("2024-06-10T20:08:49Z", med.administrationTime.toString())
    }



    @Test
    fun testInboundORU_R01_singleVital() = runTest {
        // ARRANGE
        val msgData = """
            MSH|^~\&|THEATER|THEATER|BATDOK|BATDOK|20240325182935+0000||ORU^R01|Q2681344586T3135453342||2.3||||||8859/1
            PID|1|3401078499902^^^MRN^MRN|3401078499902^^^MRN^MRN~016203099902000009YH^^^2.16.840.1.113883.3.42.10001.100001.228^IPI||test^eli^^^^^CURRENT||19960427|M||2106-3|||||""|M|""|5420381599902^^^FIN NBR^FIN NBR||||2186-5||""|""|""|AD-AF|""||""
            PV1|1|O|CD:118346273^^^THEATER^^^CD:118346267|C|||**********^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^NPI^PRSNL^^^^""~SN=668^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^**********^PRSNL^^^^""~**********^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^EDIPI^PRSNL^^^^""~2^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^PROVIDERMESSAGING^PRSNL^^^^""~US ARMY - **********^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^DEA^PRSNL^^^^""~668^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^364958627^PRSNL^^^^""~1982356^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^938068731^PRSNL^^^^""|||CAR|""|""|""|23|""|""||O||09||""||||||||||||||68|""|""|THEATER||D|||20240320140242+0000|20240320170000+0000
            ORC|RE
            OBR|1||e552a601-e6dd-47a6-b78e-7d3e6e2d8648-17880362336-ESOiView^HNAM_RESULT_GROUP_ID|VITALSIGNS|||20240320150000+0000|||||||||^McCarthy^Kyle^^^^^^^PRSNL||||||20240320153310+0000|||F
            ZUI|1|e552a601-e6dd-47a6-b78e-7d3e6e2d8648-17880362336-ESOiView|GRP
            OBX|1|NM|703558^Temperature Oral|**********|36|Deg C^Deg C|36-38^36^38|N||""|F|||20240320153310+0000||^McCarthy^Kyle^^^^^^^PRSNL
            ZUI|1|e552a601-e6dd-47a6-b78e-7d3e6e2d8648-**********-2024032015331000|NUM
            OBX|2|NM|2700541^Heart Rate Monitored|**********|80|bpm^bpm|60-110^60^110|N||""|F|||20240320153310+0000||^McCarthy^Kyle^^^^^^^PRSNL
            ZUI|1|e552a601-e6dd-47a6-b78e-7d3e6e2d8648-**********-2024032015331000|NUM
        """.trimIndent().replace("\n", "\r")

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msgData.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals(1, document.vitals.vitalList.size)
        val encounterVital = document.vitals.vitalList.first()
        Assert.assertEquals(80, encounterVital[HR::class]?.pulse)
        Assert.assertEquals(96.799995f, encounterVital[Temp::class]?.temp)
        Assert.assertEquals(1710946800L, encounterVital.timestamp.epochSecond)
        // MAP not ingested
        val expectedId = generateDomainIdFromString<EncounterVitalId>("e552a601-e6dd-47a6-b78e-7d3e6e2d8648-17880362336-ESOiView")
        Assert.assertEquals(expectedId, encounterVital.vitalId)
    }

    @Test
    fun testInboundOR_R01_multipleHR() = runTest {
        // ARRANGE
        val msgData = """
            MSH|^~\&|THEATER|THEATER|BATDOK|BATDOK|20240325182935+0000||ORU^R01|Q2681344586T3135453342||2.3||||||8859/1
            PID|1|3401078499902^^^MRN^MRN|3401078499902^^^MRN^MRN~016203099902000009YH^^^2.16.840.1.113883.3.42.10001.100001.228^IPI||test^eli^^^^^CURRENT||19960427|M||2106-3|||||""|M|""|5420381599902^^^FIN NBR^FIN NBR||||2186-5||""|""|""|AD-AF|""||""
            PV1|1|O|CD:118346273^^^THEATER^^^CD:118346267|C|||**********^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^NPI^PRSNL^^^^""~SN=668^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^**********^PRSNL^^^^""~**********^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^EDIPI^PRSNL^^^^""~2^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^PROVIDERMESSAGING^PRSNL^^^^""~US ARMY - **********^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^DEA^PRSNL^^^^""~668^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^364958627^PRSNL^^^^""~1982356^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^938068731^PRSNL^^^^""|||CAR|""|""|""|23|""|""||O||09||""||||||||||||||68|""|""|THEATER||D|||20240320140242+0000|20240320170000+0000
            ORC|RE
            OBR|1||e552a601-e6dd-47a6-b78e-7d3e6e2d8648-17880362336-ESOiView^HNAM_RESULT_GROUP_ID|VITALSIGNS|||20240320150000+0000|||||||||^McCarthy^Kyle^^^^^^^PRSNL||||||20240320153310+0000|||F
            ZUI|1|e552a601-e6dd-47a6-b78e-7d3e6e2d8648-17880362336-ESOiView|GRP
            OBX|1|NM|2700541^Heart Rate Monitored|**********|80|bpm^bpm|60-110^60^110|N||""|F|||20240320153310+0000||^McCarthy^Kyle^^^^^^^PRSNL
            ZUI|1|e552a601-e6dd-47a6-b78e-7d3e6e2d8648-**********-2024032015331000|NUM
            OBX|2|NM|703511^Peripheral Pulse Rate|11319663319|100|bpm^bpm|60-110^60^110|N||""|F|||20240320153310+0000||^McCarthy^Kyle^^^^^^^PRSNL
            ZUI|1|e552a601-e6dd-47a6-b78e-7d3e6e2d8648-**********-2024032015331000|NUM
            OBX|3|NM|703550^Apical Heart Rate|11319663321|60|bpm^bpm|50-110^50^110|N||""|F|||20240320153310+0000||^McCarthy^Kyle^^^^^^^PRSNL
            ZUI|1|e552a601-e6dd-47a6-b78e-7d3e6e2d8648-**********-2024032015331000|NUM
        """.trimIndent().replace("\n", "\r")

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msgData.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals(1, document.vitals.vitalList.size)
        val encounterVital = document.vitals.vitalList.first()
        Assert.assertEquals(60, encounterVital[HR::class]?.pulse)
        // MAP not ingested
        val expectedId = generateDomainIdFromString<EncounterVitalId>("e552a601-e6dd-47a6-b78e-7d3e6e2d8648-17880362336-ESOiView")
        Assert.assertEquals(expectedId, encounterVital.vitalId)
    }

    @Test
    fun testInboundORU_R01_multipleVitals() = runTest {
        // ARRANGE
        val msgData = """
            MSH|^~\&|DOD_OMDS_THEATER||BATDOK||20240312121501+0000||ORU^R01|000004|P|2.3
            PID|1||123456^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI||Test^Teddy^J^^^^L||19891112|M||W||||||||987654321|123456789|||2186-5
            PV1|1||^^^THEATER|||||||||||||||HX|||||||||||||||||||||THEATER||D|||20230627120104+0000|20240318120105+0000
            OBR|1|||VITALSIGNS|||20230627120856+0000|||||||||||||||20230627120856+0000|||F
            ZUI|1|50d70824-01ba-4ac2-9665-46651b4d12d6|UNKNOWN
            OBX|1|NM|703511^Peripheral Pulse Rate||1||||||F|||20230627120856+0000
            OBR|2|||VITALSIGNS|||20230627120857+0000|||||||||||||||20230627120857+0000|||F
            ZUI|1|50d70824-01ba-4ac2-9665-46651b4d12e6|UNKNOWN
            OBX|1|NM|703511^Peripheral Pulse Rate||222||||||F|||20230627120857+0000
            ZUI|1|50d70824-01ba-4ac2-9665-46651b4d12e6|UNKNOWN
            OBX|4|CE|243171919^DVPRS Pain Scale|11319663709|8 - Awful, hard to do anything^8 - Awful, hard to do any^PATIENTCARE||||||F|||20241004160332+0000
            ZUI|1|50d70824-01ba-4ac2-9665-46651b4d12e6|UNKNOWN
            OBX|12|NM|368507685^DVPRS Pain Score|11319665433|0||||||F|||20241004210057+0000
        """.trimIndent().replace("\n", "\r")

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msgData.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals(2, document.vitals.vitalList.size)
        val vital1 = document.vitals.vitalList.first()
        Assert.assertEquals(1, vital1[HR::class]?.pulse)
        Assert.assertEquals(1687867736L, vital1.timestamp.epochSecond)  // Jun 27 2023 12:08:56 GMT+0000
        val vital2 = document.vitals.vitalList.last()
        Assert.assertEquals(222, vital2[HR::class]?.pulse)
        Assert.assertEquals(1687867737L, vital2.timestamp.epochSecond) // Jun 27 2023 12:08:57 GMT+0000
        Assert.assertEquals(0, vital2[Pain::class]?.pain)
    }

    @Test
    fun testInboundORU_R01_Pain_String() = runTest {
        // ARRANGE
        val msgData = """
            MSH|^~\&|DOD_OMDS_THEATER||BATDOK||20240312121501+0000||ORU^R01|000004|P|2.3
            PID|1||123456^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI||Test^Teddy^J^^^^L||19891112|M||W||||||||987654321|123456789|||2186-5
            PV1|1||^^^THEATER|||||||||||||||HX|||||||||||||||||||||THEATER||D|||20230627120104+0000|20240318120105+0000
            OBR|1|||VITALSIGNS|||20230627120856+0000|||||||||||||||20230627120856+0000|||F
            ZUI|1|50d70824-01ba-4ac2-9665-46651b4d12e6|UNKNOWN
            OBX|1|CE|368507685^DVPRS Pain Scale|11319663709|8 - Awful, hard to do anything^8 - Awful, hard to do any^PATIENTCARE||||||F|||20241004160332+0000
        """.trimIndent().replace("\n", "\r")

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msgData.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals(1, document.vitals.vitalList.size)
        val pain = document.vitals.vitalList.last()[Pain::class]?.pain
        Assert.assertEquals(8, pain)
    }

    @Test
    fun testInboundORU_R01_AVPU_GT() = runTest {
        // ARRANGE
        val msgData = """
            MSH|^~\&|THEATER|THEATER|BATDOK|BATDOK|20240325182935+0000||ORU^R01|Q2681344586T3135453342||2.3||||||8859/1
            PID|1|3401078499902^^^MRN^MRN|3401078499902^^^MRN^MRN~016203099902000009YH^^^2.16.840.1.113883.3.42.10001.100001.228^IPI||test^eli^^^^^CURRENT||19960427|M||2106-3|||||""|M|""|5420381599902^^^FIN NBR^FIN NBR||||2186-5||""|""|""|AD-AF|""||""
            PV1|1|O|CD:118346273^^^THEATER^^^CD:118346267|C|||**********^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^NPI^PRSNL^^^^""~SN=668^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^**********^PRSNL^^^^""~**********^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^EDIPI^PRSNL^^^^""~2^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^PROVIDERMESSAGING^PRSNL^^^^""~US ARMY - **********^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^DEA^PRSNL^^^^""~668^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^364958627^PRSNL^^^^""~1982356^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^938068731^PRSNL^^^^""|||CAR|""|""|""|23|""|""||O||09||""||||||||||||||68|""|""|THEATER||D|||20240320140242+0000|20240320170000+0000
            ORC|RE
            OBR|1||e552a601-e6dd-47a6-b78e-7d3e6e2d8648-17880362336-ESOiView^HNAM_RESULT_GROUP_ID|VITALSIGNS|||20240320150000+0000|||||||||^McCarthy^Kyle^^^^^^^PRSNL||||||20240320153310+0000|||F
            ZUI|1|e552a601-e6dd-47a6-b78e-7d3e6e2d8648-17880362336-ESOiView|GRP
            OBX|1|CE|36800381^Level of Consciousness AVPU|11319630385|Alert and responsive^Alert and responsive^PATIENTCARE||||||F|||20241011190304+0000
        """.trimIndent().replace("\n", "\r")

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msgData.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals(1, document.vitals.vitalList.size)
        val vital = document.vitals.vitalList.first()
        Assert.assertEquals("Alert", vital[Avpu::class]?.avpu)
        Assert.assertEquals(1710946800L, vital.timestamp.epochSecond)  // Jun 27 2023 12:08:56 GMT+0000
    }

    @Test
    fun testInboundORU_R01_clinicalNote() = runTest {
        // ARRANGE
        ClinicalNoteConverter.map.putAll(mapOf(
            buildMapEntry("name", "381603267", DCW)
        ))
        val msg = """
            MSH|^~\&|THEATER|THEATER|BATDOK|BATDOK|20240327161300+0000||ORU^R01|Q2681344729T3135453486||2.3||||||8859/1
            PID|1|3401078499902^^^MRN^MRN|3401078499902^^^MRN^MRN~016203099902000009YH^^^2.16.840.1.113883.3.42.10001.100001.228^IPI||test^eli^^^^^CURRENT||19960427|M||2106-3|||||""|M|""|5420386099902^^^FIN NBR^FIN NBR||||2186-5||""|""|""|AD-AF|""||""
            PV1|1|O|CD:118346273^^^THEATER^^^CD:118346267|C|||**********^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^NPI^PRSNL^^^^""~SN=668^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^**********^PRSNL^^^^""~**********^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^EDIPI^PRSNL^^^^""~2^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^PROVIDERMESSAGING^PRSNL^^^^""~US ARMY - **********^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^DEA^PRSNL^^^^""~668^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^364958627^PRSNL^^^^""~1982356^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^938068731^PRSNL^^^^""|||CD:647803115|""|""|""|23|""|""||O||09||""||||||||||||||01|""|""|THEATER||D|||20240325172116+0000|20240325235959+0000
            ORC|RE||**********^HNAM_CEREF~**********^HNAM_EVENTID||||||20240327161245+0000|^McCarthy^Kyle^^^^^^^PRSNL
            OBR|1||**********^HNAM_CEREF~**********^HNAM_EVENTID|381603267^Administrative Note^^^Subject Line|||20240327161200+0000|20240327161200+0000||||||||||||||20240327161245+0000||MDOC|F|||||||&McCarthy&Kyle||&McCarthy&Kyle
            ZDS|PERFORM|^McCarthy^Kyle^^^^^^^PRSNL|20240327161245+0000|CM
            ZDS|SIGN|^McCarthy^Kyle^^^^^^^PRSNL|20240327161245+0000|CM
            ZDS|VERIFY|^McCarthy^Kyle^^^^^^^PRSNL|20240327161245+0000|CM
            ZUI|1|e552a601-e6dd-47a6-b78e-7d3e6e2d8648-**********-2024032716124600|MDOC
            OBX|1|FT|381603267^Administrative Note||Test document\.br\\.br\Showing what will go outbound.|""||""||""|F|||20240327161245+0000||^McCarthy^Kyle^^^^^^^PRSNL
            ZUI|1|e552a601-e6dd-47a6-b78e-7d3e6e2d8648-6395795089-2024032716124600|DOC
        """.trimIndent().replace("\n", "\r")

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        val events = document.events.list.filter { it.isCustom }
        Assert.assertEquals(1, events.size)
        Assert.assertEquals("Administrative Note (Subject Line): Test document\\.br\\\\.br\\Showing what will go outbound.", events.first().event)
        val expectedId = generateDomainIdFromString<EventId>("e552a601-e6dd-47a6-b78e-7d3e6e2d8648-**********-2024032716124600")
        Assert.assertEquals(EventId(expectedId.hashedWith("Administrative Note (Subject Line): Test document\\.br\\\\.br\\Showing what will go outbound.").unique), events.first().id)
    }

    @Test
    fun testInboundORU_R01_clinicalNote_Bad_Example() = runTest {
        // ARRANGE
        ClinicalNoteConverter.map.putAll(mapOf(
            buildMapEntry("name", "2820532", DCW)
        ))
        val msg = """
            MSH|^~\&|DHMSM_THEATER|99986|BATDOK-J|BATDOK-J|20240924183424+0000||ORU^R01|Q4430717653T5183814722|P|2.3||||||8859/1
            PID|1|9599986^^^MRN^MRN^DODOMDSTHEATER|2112338397^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI^DODOMDSTHEATER~9599986^^^MRN^MRN^DODOMDSTHEATER||COLLER^Alanys^Glennis^^^^CURRENT||19900425|F||||||||D||HX2112338397^^^FIN NBR^FIN NBR^DODOMDSTHEATER|666245651||||||||AD-AF
            PV1|1|O|^^^THEATER|||||||||||||||HX|||||||||||||||||||||THEATER||D|||19000101000100+0000|19000101000100+0000
            ORC|RE||c25cdac8-485f-40cc-9cc3-7730d8f85f35HNAM_CEREFPROOUTPNT0^HNAM_CEREF||||||20240802144231+0000|^CONTRIBUTOR_SYSTEM^DOD_OMDS_THEATER^^^^^^^PRSNL
            OBR|1||c25cdac8-485f-40cc-9cc3-7730d8f85f35HNAM_CEREFPROOUTPNT0^HNAM_CEREF|2820532^Physician Outpt Note^^^Office Clinic Note|||20240802144231+0000|20240802144231+0000||||||||||||||20240802144231+0000||MDOC|F||||||||&CONTRIBUTOR_SYSTEM&DOD_OMDS_THEATER|203002303&Bevan&Sheikh&&&&&DODOMDSTHEATER&690107
            ZDS|PERFORM|203002303^Bevan^Sheikh^^^^^^690107^PRSNL^^^^DODOMDSTHEATER|20240802144231+0000|CM
            ZDS|VERIFY|203002303^Bevan^Sheikh^^^^^^690107^PRSNL^^^^DODOMDSTHEATER|20240802144231+0000|CM
            ZDS|ASSIST|^CONTRIBUTOR_SYSTEM^DOD_OMDS_THEATER^^^^^^^PRSNL|20240802144231+0000|CM
            OBX|1|TX|2820532^Physician Outpt Note||Review of Systems~ Pain~  No qualifying data available.~Problem List/Past Medical History~  Ongoing~   No qualifying data~  Historical~   No qualifying data~Procedure/Surgical History~ No procedure history documented~  ~Medications~ No active medications~Allergies~ No active allergies~Social History~ No social history documented~Family History~ No family history documented~Recommendations~ Health Maintenance~    Pending (in the next year)~   There are no current recommendations pending~   Satisfied (in the past
        """.trimIndent().replace("\n", "\r")

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        val events = document.events.list.filter { it.isCustom }
        Assert.assertEquals(1, events.size)
        Assert.assertEquals("Physician Outpt Note (Office Clinic Note): Review of Systems\n" +
                " Pain\n" +
                "  No qualifying data available.\n" +
                "Problem List/Past Medical History\n" +
                "  Ongoing\n" +
                "   No qualifying data\n" +
                "  Historical\n" +
                "   No qualifying data\n" +
                "Procedure/Surgical History\n" +
                " No procedure history documented\n" +
                "\n" +
                "Medications\n" +
                " No active medications\n" +
                "Allergies\n" +
                " No active allergies\n" +
                "Social History\n" +
                " No social history documented\n" +
                "Family History\n" +
                " No family history documented\n" +
                "Recommendations\n" +
                " Health Maintenance\n" +
                "    Pending (in the next year)\n" +
                "   There are no current recommendations pending\n" +
                "   Satisfied (in the past", events.first().event)
    }


    @Test
    fun testInboundLabORU_R01() = runTest {
        //Arrange
        val msgdata =
            """MSH|^~\&|DOD_OMDS_THEATER||BATDOK||20240312121501+0000||ORU^R01|000004|P|2.3
            PID|1||123456^^^2.16.840.1.113883.3.42.10001.100001.12^BATDOK||Test^Teddy^J^^^^L||19891112|M||W||||||||987654321|123456789|||2186-5
            PV1|1||^^^THEATER|||||||||||||||HX|||||||||||||||||||||THEATER||D|||20230627120104+0000|20240318120105+0000
            OBR|1|||19146-0^Referral lab test results|||20230627122108+0000|||||||||||||||20230627122108+0000||GLB|F
            ZUI|1|**********|UNKNOWN
            OBX|1|NM|110118289^POC pH (Ven)||1.0||||||F|||20230627122108+0000
            ZUI|1|1a|UNKNOWN
            OBX|2|NM|110115709^POC pO2 (Ven)||3.0||||||F|||20230627122108+0000
            ZUI|1|2a|UNKNOWN
            OBX|3|NM|110117803^POC Sodium (Ven)||6.0||||||F|||20230627122108+0000
            ZUI|1|3a|UNKNOWN
            OBX|4|NM|639975181^POC Hgb (Ven)||10.0||||||F|||20230627122108+0000
            ZUI|1|4a|UNKNOWN
            OBX|5|NM|110112247^POC Calcium Ionized (Ven)||8.0||||||F|||20230627122108+0000
            ZUI|1|5a|UNKNOWN
            OBX|6|NM|110116477^POC BE (Ven)||5.0||||||F|||20230627122108+0000
            ZUI|1|6a|UNKNOWN
            OBX|7|NM|110113147^POC pCO2 (Ven)||2.0||||||F|||20230627122108+0000
            ZUI|1|7a|UNKNOWN
            OBX|8|NM|110114461^POC Potassium (Ven)||7.0||||||F|||20230627122108+0000
            ZUI|1|8a|UNKNOWN
            OBX|9|NM|639975311^POC Hct (Ven)||11.0||||||F|||20230627122108+0000
            ZUI|1|9a|UNKNOWN
            OBX|10|NM|110115559^POC HCO3 (Ven)||4.0||||||F|||20230627122108+0000
            ZUI|1|10a|UNKNOWN
            OBX|11|NM|110112721^POC Glucose (Ven)||9.0||||||F|||20230627122108+0000
            ZUI|1|11a|UNKNOWN"""

        //ACT
        val hl7Data =
            MessageReceiver().processHL7Data(ByteArrayInputStream(msgdata.toByteArray()))
        val document = hl7Data.document!!
        val labs = document.labs.list


        //ASSERT
        Assert.assertEquals(11, labs.size)
        Assert.assertEquals("1.0", labs[0].value)
        Assert.assertEquals("3.0", labs[1].value)
        Assert.assertEquals("6.0", labs[2].value)
        Assert.assertEquals("10.0", labs[3].value)
        Assert.assertEquals("8.0", labs[4].value)
        Assert.assertEquals("5.0", labs[5].value)
        Assert.assertEquals("2.0", labs[6].value)
        Assert.assertEquals("7.0", labs[7].value)
        Assert.assertEquals("11.0", labs[8].value)
        Assert.assertEquals("4.0", labs[9].value)
        Assert.assertEquals("9.0", labs[10].value)
    }

    @Test
    fun testInboundLabORU_R01_MHSGT() = runTest {
        //Arrange
        val msgdata =
            """MSH|^~\&|THEATER|THEATER|BATDOK|BATDOK|20240325182937+0000||ORU^R01|Q2681344612T3135453379||2.3||||||8859/1
            PID|1|3401078499902^^^MRN^MRN|3401078499902^^^MRN^MRN~016203099902000009YH^^^2.16.840.1.113883.3.42.10001.100001.228^IPI||test^eli^^^^^CURRENT||19960427|M||2106-3|||||""|M|""|5420386099902^^^FIN NBR^FIN NBR||||2186-5||""|""|""|AD-AF|""||""
            PV1|1|O|CD:118346273^^^THEATER^^^CD:118346267|C|||**********^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^NPI^PRSNL^^^^""~SN=668^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^**********^PRSNL^^^^""~**********^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^EDIPI^PRSNL^^^^""~2^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^PROVIDERMESSAGING^PRSNL^^^^""~US ARMY - **********^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^DEA^PRSNL^^^^""~668^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^364958627^PRSNL^^^^""~1982356^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^938068731^PRSNL^^^^""|||CD:647803115|""|""|""|23|""|""||O||09||""||||||||||||||""|""|""|THEATER||A|||20240325172116+0000
            OBR|1|**********^HNAM_ORDERID||19146-0^Referral lab test results|||20240325181417+0000|||||||20240325181400+0000|312646&Swab|**********^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^NPI^PRSNL^^^^""~SN=668^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^**********^PRSNL^^^^""~**********^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^EDIPI^PRSNL^^^^""~2^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^PROVIDERMESSAGING^PRSNL^^^^""~US ARMY - **********^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^DEA^PRSNL^^^^""~668^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^364958627^PRSNL^^^^""~1982356^CERNER^CERNER^JUNIOR^^Cerner Managed Acct^^^938068731^PRSNL^^^^""||||999022024085000001^HNA_ACCN~32548838^HNA_ACCNID||20240325181551+0000||GLB|F||1^^^20240325175400+0000^^R~^^^^^R|||||||||20240325175600+0000
            ZUI|1|e552a601-e6dd-47a6-b78e-7d3e6e2d8648-**********-2024032518155100|GRP
            OBX|1|NM|110118289^POC pH (Ven)||1.0|unit||N||U|F|||20240325181549+0000||^Perry^Josh^^^^^^^PRSNL|^^^118462517
            ZUI|1|e552a601-e6dd-47a6-b78e-7d3e6e2d8648-**********-2024032518155100|TXT
            OBX|2|CE|102598429^Flu A Rapid Ag||Negative^Negative|""||N||U|F|||20240325181549+0000||^Perry^Josh^^^^^^^PRSNL|^^^118462517
            ZUI|1|e552a601-e6dd-47a6-b78e-7d3e6e2d8648-**********-2024032518155100|TXT"""

        //ACT
        val hl7Data =
            MessageReceiver().processHL7Data(ByteArrayInputStream(msgdata.toByteArray()))
        val document = hl7Data.document!!
        val labs = document.labs.list

        //ASSERT
        Assert.assertEquals(2, labs.size)
        Assert.assertEquals("1.0", labs[0].value)
        Assert.assertEquals("POC pH (Ven)", labs[0].name)
        Assert.assertEquals("unit", labs[0].unit)
        Assert.assertEquals("Negative", labs[1].value)
        Assert.assertEquals("Flu A Rapid Ag", labs[1].name)
    }

    @Ignore("Figure out if/how OMDS would send us vent data")
    @Test
    fun testInboundVentORU_R01() = runTest {
        //Arrange
        val msgdata =
            """MSH|^~\&|DOD_OMDS_THEATER||BATDOK||20240312121501+0000||ORU^R01|000004|P|2.3
            PID|1||123456^^^2.16.840.1.113883.3.42.10001.100001.12^BATDOK||Test^Teddy^J^^^^L||19891112|M||W||||||||987654321|123456789|||2186-5
            PV1|1||^^^THEATER HX||||123456^providerlast^providerfirst|||||||||||HX|||||||||||||||||||||THEATER HX||D|||20230627120104+0000
            OBR|1|||69348-1^Respiratory assist status|||20230627122108+0000|||||||||||||||20230627122108+0000|||F
            ZUI|1|123456789|UNKNOWN
            OBX|1|TX|20125-1^Ventilator type^LOINC||vent||||||F|||20230627122108+0000
            OBX|2|TX|20124-4^Ventilation mode Ventilator^LOINC||mode||||||F|||20230627122108+0000
            OBX|3|NM|19834-1^Breath rate setting Ventilator^LOINC||3|br/min^Breaths/minute^CS54|||||F|||20230627122108+0000
            OBX|4|NM|20112-9^Tidal volume setting Ventilator^LOINC||4|mL^Milliliters^CS54|||||F|||20230627122108+0000
            OBX|5|NM|20077-4^Positive end expiratory pressure setting Ventilator^LOINC||5.6|cmH20^Centimeters water^CS54|||||F|||20230627122108+0000
            OBX|6|NM|19996-8^Oxygen/Inspired gas Respiratory system --on ventilator^LOINC||1.2||||||F|||20230627122108+0000"""

        //ACT
        val hl7Data =
            MessageReceiver().processHL7Data(ByteArrayInputStream(msgdata.toByteArray()))
        val document = hl7Data.document!!
        val vent = document.ventValues.ventSettingsList


        //ASSERT
        Assert.assertEquals(1, vent.size)
        val expectedId = generateDomainIdFromString<VentId>("123456789")
        Assert.assertEquals(expectedId, vent[0].id)
        Assert.assertEquals("vent", vent[0].ventilator)
        Assert.assertEquals("mode", vent[0].mode)
        Assert.assertEquals(3, vent[0].rate)
        Assert.assertEquals(4, vent[0].tv)
        Assert.assertEquals(5.6, vent[0].peep)
        Assert.assertEquals(1.2, vent[0].fio2)
    }

    @Test
    fun testInboundORU_ZB2_Blood() = runTest {
        //ARRANGE

        val msg = """
            MSH|^~\&|DHMSM_THEATER|99904|BATDOK-J|BATDOK-J|20240610201032+0000||ORU^ZB2|Q4430718535T5183813717|P|2.3||||||8859/1
            PID|1|6430724199904^^^MRN^MRN^DODMOBDEVICETHEATER|016203099904000009RL^^^2.16.840.1.113883.3.42.10001.100001.228^IPI^DODMOBDEVICETHEATER~6430724199904^^^MRN^MRN^DODMOBDEVICETHEATER|DF2CA7C1-8B1F-38C6-A167-DFAFD1008443^^^2.16.840.1.113883.3.42.10033.100001.13^MPID^DODMOBDEVICETHEATER|UB-UXDGT^WEYU_Tablet1^^^^^CURRENT||19000101|NA||||||||||9633437799904^^^FIN NBR^FIN NBR^DODMOBDEVICETHEATER
            PV1|1|E|ER^ER-15^A^THEATER^^^Emergency|E||||||ER||||||||E|||||||||||||||||||||THEATER||A|||20240529204000+0000
            OBR|1||52cf22a3-1be2-39de-adfd-c542d46d5876|1^Blood products^LOINC|||20240529204000+0000|||||||||||||||20240529204000+0000||BB|F
            NTE|1||test|47
            ZUI|1|52cf22a3-1be2-39de-adfd-c542d46d5876|TRANS
            ZBP|1|123456|654321|55555|Fresh Whole Blood(FWB)^description^alt description|TRANSFUSED|20241118143430|20241218143430|O|Rh-Pos|||Tacos^^147258369||||N|500|300|100|1||mLs
        """.trimIndent().replace("\n","\r")
        //ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!
        //ASSERT
        //todo add asserts after figure out commands
        //Assert.assertEquals("123456","" ) //BPU unique ID
    }

    @Test
    fun testInboundPPR_PC1() = runTest {
        // ARRANGE
        val msg = """
            MSH|^~\&|MIP|DHMSM|MSH-T|THEATER|20230928162318||PPR^PC1|Q3582759739T4190924808|P|2.3
            PID|1||2113883831^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI~47917727000001^^^MRN^MRN|55795661^^^MSG^MSG~URN:CERNER:IDENTITY-FEDERATION:REALM:8227254F-64BA-4802-A4CE-535AE1EBCEF2:PRINCIPAL:2113883831^^^FPP^FEDERATEDPERSONPRINCIPAL|Citfour^Richard^Oliver^^^^L||19960601|M||2106-3|||(414)355-0892^HP||ENG|S|||537873065
            PRB|ADD|20230928162318|ARZHHwEnBdMyXoiKCgEBXQ^RAD w/o status asthmaticus^SCT||||20230928162318|||MED|||PROBABLE|ACTIVE|20230928|||||0
            ZPB|1|557A5512-5E1B-11EE-8D56-C7123D86A5FC|||RAD w/o status asthmaticus
            ROL|863864873|ADD|RECORDER|tstacct^^Leldon^^^^^^EXTERNAL ID^PRSNL^^^EXTID|20230928162318
            ROL|863864875|ADD|RESPONSIBLEMANAGING|tstacct^^Leldon^^^^^^EXTERNAL ID^PRSNL^^^EXTID|20230928162318
            MSH|^~\&|DHMSM_THEATER|99911|TMDS||20241119173744+0000||PPR^PC1|Q5477740004T6407199912|P|2.3||||||8859/1
            PID|1||2113883831^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI^DODOMDSTHEATER~6699911^^^MRN^MRN^DODOMDSTHEATER||Citfour^Richard^Oliver^^^L^CURRENT||19960601|M|||10330 W SILVER SPRING DR^^MILWAUKEE^WI^532253248^USA^HP~10330 W Silver Spring Dr^^Milwaukee^WI^532253248^USA^PST||**********^HP||||||537873065||||||0
            PRB|ADD|20241119173744+0000|AZEGxwEmL07HeZ3NwKgAAg^Abnormal bleeding tests^SCT|FC1EB168-A69C-11EF-8DB8-8EA6AA17004F|||20241119173744+0000|||MED|||CONFIRMED|Active|20230630|||||0
            ZPB|1|FC1EB168-A69C-11EF-8DB8-8EA6AA17004F|||Abnormal bleeding tests
            ROL|**********|ADD|RECORDER|1^SYSTEM^SYSTEM^Cerner^^Cerner Managed Acct^^^PROVIDERMESSAGING^PRSNL|20241119173744+0000
        """.trimIndent()

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        // ASSERT
        Assert.assertEquals(2, document.info.problems.size)
        val radProblem = document.info.problems[0]
        Assert.assertEquals("RAD w/o status asthmaticus", radProblem.name)
        Assert.assertEquals("ACTIVE", radProblem.status)
        Assert.assertEquals(1695918198L, radProblem.date?.epochSecond)
        val bleedingProblem = document.info.problems[1]
        Assert.assertEquals("Abnormal bleeding tests", bleedingProblem.name)
        Assert.assertEquals("Active", bleedingProblem.status)
        Assert.assertEquals(1732037864L, bleedingProblem.date?.epochSecond)
    }

    @Test
    fun testInboundVXUV04() = runTest {
        // ARRANGE
        val msg = """
            MSH|^~\&|MIP|0125|MSH-T|THEATER|20240611070242||VXU^V04|Q4670416721T5464149237|T|2.5.1|||AL|NE
            PID|1||2105060648^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI^DENTRIX~666063043^^^2.16.840.1.113883.4.1^SSN^DENTRIX~***********^^^2.16.840.1.113883.3.42.10001.100001.326^DBN^DENTRIX~403004M0630M0000175L^^^2.16.840.1.113883.3.42.10001.100001.228^IPI^DENTRIX||Kaup^Alois^Alphonsus^^^^L||19700710|M||2106-3^White^HL70005^2106-3^White|10585 TIERRASANTA BLVD APT 455^^SAN DIEGO^CA^92124-2604^US^H~^^^CT^^US^N~<EMAIL>^<EMAIL>~10585 TIERRASANTA BLVD APT 455^^SAN DIEGO^CA^92124-2604^US||**********^PRN^PH~**********^PRN^PH~^NET^Internet^<EMAIL>|**********^WPH^PH~^NET^Internet^<EMAIL>|eng^English^ISO639|D^Divorced^HL70002^D^Divorced|CHR^Christian^HL70006^1013^Christian|102462971^^^FIN NBR&2.16.840.1.113883.3.8901.1&ISO^FIN NBR|||||||||AD-AR
            PV1|1|O|0125A-MRP-CL^MRP^^0125^^WAITINGROOMS^0125-0125|C|||tstacct^ABURTO^MYLES^^^^^^EXTERNAL ID^PRSNL^^^EXTID~4375926^ABURTO^MYLES^^^^^^PROVIDERMESSAGING^PRSNL^^^MSG~**********^ABURTO^MYLES^^^^^^EDIPI^PRSNL^^^EDIPI|||||||||||HX||Commercial|||||||||||||||||||||D|||20240611063825
            ORC|RE||{5BC6E68B-5A10-4F94-B16A-2407031FD01A}^^2.16.840.1.113883.3.42.10024.100001.337.28.999762^ISO|||||||tstacct^Qrgjfl^Egqlms^^^^^^EXTERNAL ID^PRSNL^^^EXTID|tstacct^Qrgjfl^Egqlms^^^^^^EXTERNAL ID^PRSNL^^^EXTID|tstacct^ABURTO^MYLES^^^^^^EXTERNAL ID^PRSNL^^^EXTID
            RXA|0|1|20240611070000||37^yellow fever^CVX^49281-0915-01^YF-Vax^NDC|0.5|mL^Milliliters^UCUM^mL^Milliliters||00^New immunization record^NIP001|tstacct^Qrgjfl^Egqlms^^^^^^EXTERNAL ID^PRSNL^^^EXTID~**********^Qrgjfl^Egqlms^^^^^^EDIPI^PRSNL^^^EDIPI|^^^&2.16.840.1.113883.3.42.10024.100001.337.28.999322.108416659&ISO^^^^^HSGA CI^^IQMFRQ^WA^92758^^B||||3000567|20240627210000|PMC^sanofi pasteur^MVX|||F|A|20240611070212
            RXR|SC^SubCutaneous^HL70162|LA^Arm, Left Upper^HL70163
            MSH|^~\&|MIP|0125|MSH-T|THEATER|20240611070242||VXU^V04|Q4670416720T5464149236|T|2.5.1|||AL|NE
            PID|1||2105060648^^^2.16.840.1.113883.3.42.10001.100001.12^EDIPI^DENTRIX~666063043^^^2.16.840.1.113883.4.1^SSN^DENTRIX~***********^^^2.16.840.1.113883.3.42.10001.100001.326^DBN^DENTRIX~403004M0630M0000175L^^^2.16.840.1.113883.3.42.10001.100001.228^IPI^DENTRIX||Kaup^Alois^Alphonsus^^^^L||19700710|M||2106-3^White^HL70005^2106-3^White|10585 TIERRASANTA BLVD APT 455^^SAN DIEGO^CA^92124-2604^US^H~^^^CT^^US^N~<EMAIL>^<EMAIL>~10585 TIERRASANTA BLVD APT 455^^SAN DIEGO^CA^92124-2604^US||**********^PRN^PH~**********^PRN^PH~^NET^Internet^<EMAIL>|**********^WPH^PH~^NET^Internet^<EMAIL>|eng^English^ISO639|D^Divorced^HL70002^D^Divorced|CHR^Christian^HL70006^1013^Christian|102462971^^^FIN NBR&2.16.840.1.113883.3.8901.1&ISO^FIN NBR|||||||||AD-AR
            PV1|1|O|0125A-MRP-CL^MRP^^0125^^WAITINGROOMS^0125-0125|C|||tstacct^ABURTO^MYLES^^^^^^EXTERNAL ID^PRSNL^^^EXTID~4375926^ABURTO^MYLES^^^^^^PROVIDERMESSAGING^PRSNL^^^MSG~**********^ABURTO^MYLES^^^^^^EDIPI^PRSNL^^^EDIPI|||||||||||HX||Commercial|||||||||||||||||||||D|||20240611063825
            ORC|RE||{02606A48-D4CB-4342-A790-2214C99DB0A4}^^2.16.840.1.113883.3.42.10024.100001.337.28.999762^ISO|||||||tstacct^Qrgjfl^Egqlms^^^^^^EXTERNAL ID^PRSNL^^^EXTID|tstacct^Qrgjfl^Egqlms^^^^^^EXTERNAL ID^PRSNL^^^EXTID|tstacct^ABURTO^MYLES^^^^^^EXTERNAL ID^PRSNL^^^EXTID
            RXA|0|1|20240611||115^Tdap^CVX^58160-0842-52^Boostrix (Tdap)^NDC|0.5|mL||00^New immunization record^NIP001|tstacct^Qrgjfl^Egqlms^^^^^^EXTERNAL ID^PRSNL^^^EXTID~**********^Qrgjfl^Egqlms^^^^^^EDIPI^PRSNL^^^EDIPI|^^^&2.16.840.1.113883.3.42.10024.100001.337.28.999322.108416659&ISO^^^^^HSGA CI^^IQMFRQ^WA^92758^^B||||B32NG|20250612210000|SKB^GlaxoSmithKline^MVX|||F|A|20240611065951
            RXR|IM^IntraMuscular^HL70162|LD^Deltoid, Left^HL70163
        """.trimIndent()

        //ACT
        val hl7Data = MessageReceiver().processHL7Data(ByteArrayInputStream(msg.toByteArray()))
        val document = hl7Data.document!!

        //ASSERT
        Assert.assertEquals(2, document.info.immunizations.size)
        val yellowFever = document.info.immunizations[0]
        Assert.assertEquals("yellow fever", yellowFever.name)
        Assert.assertEquals(0.5f, yellowFever.volume)
        Assert.assertEquals("Milliliters", yellowFever.unit)
        Assert.assertEquals(1718089200L, yellowFever.date?.epochSecond)
        val tdap = document.info.immunizations[1]
        Assert.assertEquals("Tdap", tdap.name)
        Assert.assertEquals(0.5f, tdap.volume)
        Assert.assertEquals("mL", tdap.unit)
        // Should be null
        Assert.assertTrue(tdap.date?.epochSecond == null)
    }

    @Test
    fun testInboundOBXTempTympanic()  = runTest {
            // ARRANGE
            val msgData = """
            MSH|^~\&|THEATER|THEATER|BATDOK|BATDOK|20240325182935+0000||ORU^R01|Q2681344586T3135453342||2.3||||||8859/1
            PID|1|3401078499902^^^MRN^MRN|3401078499902^^^MRN^MRN~016203099902000009YH^^^2.16.840.1.113883.3.42.10001.100001.228^IPI||test^eli^^^^^CURRENT||19960427|M||2106-3|||||""|M|""|5420381599902^^^FIN NBR^FIN NBR||||2186-5||""|""|""|AD-AF|""||""
            OBR|1||e552a601-e6dd-47a6-b78e-7d3e6e2d8648-17880362336-ESOiView^HNAM_RESULT_GROUP_ID|VITALSIGNS|||20240320150000+0000|||||||||^McCarthy^Kyle^^^^^^^PRSNL||||||20240320153310+0000|||F
            ZUI|1|e552a601-e6dd-47a6-b78e-7d3e6e2d8648-17880362336-ESOiView|GRP
            OBX|1|NM|703526^Temperature Tympanic|14352409772|36|Deg C^Deg C|36.6-38^36.6^38|N|||F|||20250517161737+0000
        """.trimIndent().replace("\n", "\r")

            // ACT
            val hl7Data =
                MessageReceiver().processHL7Data(ByteArrayInputStream(msgData.toByteArray()))
            val document = hl7Data.document!!

            // ASSERT
            Assert.assertEquals(1, document.vitals.vitalList.size)
            val encounterVital = document.vitals.vitalList.first()
            Assert.assertEquals(96.799995f, encounterVital[Temp::class]?.temp)
        }

    @Test
    fun testFullBatchMessage_MHSGT() = testFullBatchMessage("gt_inbound.hl7")

    @Test
    fun testFullBatchMessage_OMDS() = testFullBatchMessage("omds_inbound.hl7")

    private fun testFullBatchMessage(fileName: String) = runTest {
        // ARRANGE
        mockkObject(DBAllergyConverter)
        coEvery { DBAllergyConverter.nameToBatdokData(any()) } returns null
        val msgData = javaClass.classLoader?.getResourceAsStream(fileName)
        if (msgData == null) {
            Assert.fail()
            return@runTest
        }

        // ACT
        val hl7Data = MessageReceiver().processHL7Data(msgData)

        // ASSERT
        // Just validate the batch was processed, the particular commands are checked above
        Assert.assertTrue(hl7Data.commandsList.isNotEmpty())
    }
}
