package mil.af.afrl.batman.hl7lib.util

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.slot
import io.mockk.verify
import org.junit.Assert
import org.junit.Rule
import org.junit.Test
import java.security.KeyStore
import java.security.cert.X509Certificate
import javax.net.ssl.SSLContext
import javax.net.ssl.TrustManager
import javax.net.ssl.X509TrustManager

class HttpClientTest {

    @MockK lateinit var sslContextMock: SSLContext

    @get:Rule val mockkRule = MockkRule(this, relaxed = true)

    @Test
    fun testCreateSslSocketFactory_allExpectedCertsAdded() {
        // ARRANGE
        val androidKeyStore = KeyStore.getInstance(KeyStore.getDefaultType()).apply { load(null, null) }
        val androidCertMock = mockk<X509Certificate>().apply {
            every { type } returns "android"
        }
        androidKeyStore.setCertificateEntry("android", androidCertMock)

        mockkStatic(KeyStore::class)
        every { KeyStore.getInstance("AndroidCAStore") } returns androidKeyStore

        // This allows us to capture the TrustManagers used to build the SSLContext
        mockkStatic(SSLContext::class)
        every { SSLContext.getInstance(any()) } returns sslContextMock

        // ACT
        HttpClient.createSslSocketFactory()

        // ASSERT
        // Get the trust managers that were built
        val slot = slot<Array<TrustManager>>()
        verify { sslContextMock.init(any(), capture(slot), any()) }
        Assert.assertTrue(slot.isCaptured)

        // Verify our certs were used. We should have all the Android certs
        val validCerts = (slot.captured.first() as X509TrustManager).acceptedIssuers
        Assert.assertEquals(1, validCerts.size)
        val certNames = validCerts.map { it.type }
        Assert.assertTrue(certNames.contains("android"))
    }

}
