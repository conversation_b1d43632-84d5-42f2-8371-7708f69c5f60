package mil.af.afrl.batman.hl7lib.util

import com.github.scribejava.core.builder.ServiceBuilder
import com.github.scribejava.core.builder.api.DefaultApi20
import com.github.scribejava.core.model.DeviceAuthorization
import com.github.scribejava.core.model.OAuth2AccessToken
import com.github.scribejava.core.oauth.OAuth20Service
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.mockkConstructor
import io.mockk.mockkObject
import kotlinx.coroutines.test.advanceUntilIdle
import kotlinx.coroutines.test.runTest
import mil.af.afrl.batman.hl7lib.Endpoint
import org.json.JSONObject
import org.junit.Assert
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import java.time.Instant

class AuthTokenRequestorTest {

    @MockK lateinit var apiMock: OAuth20Service

    @get:Rule
    val mockkRule = MockkRule(this, relaxed = true)

    private lateinit var classUnderTest: AuthTokenRequestor

    @Before
    fun setup() {
        mockkObject(HttpClient)
        every { HttpClient.createSslSocketFactory() } returns mockk(relaxed = true)

        mockkConstructor(ServiceBuilder::class)
        every { anyConstructed<ServiceBuilder>().build(any<DefaultApi20>()) } returns apiMock

        classUnderTest = AuthTokenRequestor()
    }

    @Test
    fun testSubmitTokenRequest_goodRequest() = runTest {
        // ARRANGE
        every { apiMock.deviceAuthorizationCodesAsync.get() } returns DeviceAuthorization(
            "device_code", "user_code", "verification_uri", 0
        )

        every { apiMock.getAccessTokenDeviceAuthorizationGrant(any()) } returns OAuth2AccessToken(
            "access_token", "", 0, "refresh_token", "", ""
        )

        // ACT/ASSERT
        classUnderTest.submitTokenRequest(
            Endpoint.LOCAL_TEST,
            onCodeRequestComplete = { uri, code ->
                Assert.assertEquals("verification_uri", uri)
                Assert.assertEquals("user_code", code)
            },
            onRefreshTokenReady = {
                val tokenData = JSONObject(it)
                Assert.assertEquals("refresh_token", tokenData["refresh_token"])
                Assert.assertEquals("device_code", tokenData["device_code"])
                Assert.assertTrue(tokenData.has("issue_time"))
            },
            onError = {
                Assert.fail()
            },
        )

        advanceUntilIdle()
    }

    @Test
    fun testSubmitTokenRequest_authCodeFail() = runTest {
        // ARRANGE
        every { apiMock.deviceAuthorizationCodesAsync.get() } throws Exception("test exception")

        // ACT/ASSERT
        classUnderTest.submitTokenRequest(
            Endpoint.LOCAL_TEST,
            onCodeRequestComplete = { _, _ ->
                Assert.fail()
            },
            onRefreshTokenReady = {
                Assert.fail()
            },
            onError = {
                Assert.assertEquals("test exception", it.message)
            },
        )

        advanceUntilIdle()
    }

    @Test
    fun testSubmitTokenRequest_tokenFail() = runTest {
        // ARRANGE
        every { apiMock.deviceAuthorizationCodesAsync.get() } returns DeviceAuthorization(
            "device_code", "user_code", "verification_uri", 0
        )

        every { apiMock.getAccessTokenDeviceAuthorizationGrant(any()) } throws Exception("test exception")

        // ACT/ASSERT
        classUnderTest.submitTokenRequest(
            Endpoint.LOCAL_TEST,
            onCodeRequestComplete = { uri, code ->
                Assert.assertEquals("verification_uri", uri)
                Assert.assertEquals("user_code", code)
            },
            onRefreshTokenReady = {
                Assert.fail()
            },
            onError = {
                Assert.assertEquals("test exception", it.message)
            },
        )

        advanceUntilIdle()
    }

    @Test
    fun testGetAuthToken_goodRequest() = runTest {
        // ARRANGE
        val tokenInfo = JSONObject().apply {
            put("device_code", "device_code")
            put("refresh_token", "refresh_token")
            put("issue_time", Instant.now().epochSecond)
        }.toString()

        every { apiMock.refreshAccessTokenAsync(any()).get() } returns OAuth2AccessToken(
            "access_token", "", 0, "refresh_token", "", ""
        )

        // ACT/ASSERT
        classUnderTest.getAuthToken(
            Endpoint.LOCAL_TEST,
            tokenInfo,
            onTokenAvailable = {
                Assert.assertEquals("access_token", it)
            },
            onTokenRefresh = {
                val tokenData = JSONObject(it)
                Assert.assertEquals("refresh_token", tokenData["refresh_token"])
            },
            onError = {
                Assert.fail()
            }
        )

        advanceUntilIdle()
    }

    @Test
    fun testGetAuthToken_tokenRequestFailure() = runTest {
        // ARRANGE
        val tokenInfo = JSONObject().apply {
            put("device_code", "device_code")
            put("refresh_token", "refresh_token")
            put("issue_time", Instant.now().epochSecond)
        }.toString()

        every { apiMock.refreshAccessTokenAsync(any()).get() } throws Exception("test exception")

        // ACT/ASSERT
        classUnderTest.getAuthToken(
            Endpoint.LOCAL_TEST,
            tokenInfo,
            onTokenAvailable = { Assert.fail() },
            onTokenRefresh = { Assert.fail() },
            onError = {
                Assert.assertEquals("test exception", it.message)
            }
        )

        advanceUntilIdle()
    }
}
