package mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure

import gov.afrl.batdok.encounter.treatment.FingerThorData
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup

fun fingerThorCode(endpoint: EndpointType) = when (endpoint){
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("182705007","Tension pneumothorax relief (procedure)")
    else -> null
}

fun fingerThorSideCode(side: String) = when(side){
    FingerThorData.Location.LEFT.dataString -> CodeNameGroup("1290343009","Structure of left half of anterior chest wall (body structure)")
    FingerThorData.Location.RIGHT.dataString -> CodeNameGroup("1290342004","Structure of right half of anterior chest wall (body structure)")
    else -> null
}

fun fingerThorSideFromCode(code: String) = when(code){
    "1290343009" -> FingerThorData.Location.LEFT.dataString
    "1290342004" -> FingerThorData.Location.RIGHT.dataString
    else -> null
}