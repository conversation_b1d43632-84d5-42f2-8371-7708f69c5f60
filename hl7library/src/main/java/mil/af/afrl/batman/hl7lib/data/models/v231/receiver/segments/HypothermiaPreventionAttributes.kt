package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments

import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.treatment.HypothermiaPreventionData
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.hypothermiaPreventionFromCode
import org.openehealth.ipf.modules.hl7.kotlin.value

fun hypothermiaPreventionDataAttributes(attributes: List<OBX>, code: String) : HypothermiaPreventionData{
    var type: String = hypothermiaPreventionFromCode(code).toString()
    
    if (type == "Other"){
        for (attr in attributes){
            type = attr.obx5_ObservationValue.firstOrNull()?.value ?: continue
        }
    }
    return HypothermiaPreventionData(type = type)
}