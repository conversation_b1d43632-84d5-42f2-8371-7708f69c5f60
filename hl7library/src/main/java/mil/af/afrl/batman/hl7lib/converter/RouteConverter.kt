package mil.af.afrl.batman.hl7lib.converter

import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.v231.datatype.CE
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.data.models.v231.field.DHMSM_ICD
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ExtendedCodedElement

object RouteConverter : BatdokDataConverter<String>() {

    override fun String.toName() = this

    override fun buildType(name: String) = name

    // BATDOK uses different abbreviations for some of the items
    private val conversionMap = mapOf(
        "IN" to "NS",
        "SQ" to "SC",
        "IO" to "IntraO",
        "IV" to "IVPU",  // IV in the code is "IV Drip", Batdok intends a single push
        "IV Bolus" to "IVB",
        "IV Continuous Infusion" to "IV",
        "Rectal" to "PR",
        "IV: <50kg" to "IVPU",
        "IV: >=50kg" to "IVPU",
        "IVP" to "IVPU",
        "IV Push" to "IVPU",
        "IV Bolus (Half)" to "IVB",
        "IV Bolus (Full)" to "IVB",
        "IV Continuous" to "IV",
        "IV Infusion" to "IV",
        "NG/OG" to "NG",
        "IM AtroPen" to "IM",
        "Oral" to "PO",
        "Metered-Dose Inhaler" to "IH",
        "Nebulization" to "NI",
        "IO Bolus" to "IVB",
        "IO Push" to "IVB",
        "SubQ" to "SC",
        "IV Push Induction" to "IVPU",
        "IV Push Maintenance" to "IVPU",
        "PO (Sublingual)" to "SL",
        "PO (Translingual)" to "PO",
        "IV Drip" to "IV",
        "PO: Low" to "PO",
        "PO: High" to "PO",
        "Rectal: Low" to "PR",
        "Rectal: High" to "PR",
        "Loading Dose" to "IV",  // This is based on usage in SMOG PDF
        "Maintenance Dose" to "IV",  // This is based on usage in SMOG PDF
        "IM AtroPen: Mild" to "IM",
        "IM AtroPen: Severe" to "IM",
        "Mouth/ nose/ sputum" to "IVPU"  // Based on usage in SMOG PDF
    )

    // BATDOK should send us the "coded" value (the abbreviation). Use this to look up the full
    // name to send in the coded element.
    override fun batdokDataToCodedElement(message: AbstractMessage, data: String, endpoint: Endpoint): CE {
        val codeSystem = codeTypeForEndpoint(endpoint)
        val correctedData = conversionMap[data] ?: data
        val codedData = batdokDataToCodeData(correctedData, codeSystem)
        return if (codedData != null) {
            // If the lookup found a name, send the Batdok data as the code
            ExtendedCodedElement(message, correctedData, codedData.code, codeSystem)
        } else {
            // If no code exists, send the Batdok data as the text
            ExtendedCodedElement(message, "", correctedData, "")
        }
    }

    override fun codeTypeForEndpoint(endpoint: Endpoint): String {
        return DHMSM_ICD
    }
}
