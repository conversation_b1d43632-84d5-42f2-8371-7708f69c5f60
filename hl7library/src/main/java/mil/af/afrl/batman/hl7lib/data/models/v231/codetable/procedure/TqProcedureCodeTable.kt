package mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure

import gov.afrl.batdok.encounter.treatment.TqData
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup

//region Outbound
fun tqCode(endpoint: EndpointType) = when (endpoint) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("20655006", "Application of tourniquet (procedure)")
    else -> null
}

fun cdpTqLocationCode(location: String) = when (location) {
    TqData.Location.EXTREMITY.dataString -> CodeNameGroup("66019005", "Limb structure (body structure)")
    TqData.Location.TRUNCAL.dataString -> CodeNameGroup("22943007", "Trunk structure (body structure)")
    TqData.Location.JUNCTIONAL.dataString -> CodeNameGroup("50974003", "Junctional (qualifier value)")
    else -> null
}

fun cdpTqSubLocationCode(location: String?) = when (location) {
    TqData.SubLocation.LUE.dataString -> CodeNameGroup("368208006", "Left upper arm structure (body structure)")
    TqData.SubLocation.RUE.dataString -> CodeNameGroup("368209003", "Right upper arm structure (body structure)")
    TqData.SubLocation.LLE.dataString -> CodeNameGroup("32153003", "Structure of left lower limb (body structure)")
    TqData.SubLocation.RLE.dataString -> CodeNameGroup("62175007", "Structure of right lower limb (body structure)")
    else -> null
}
//endregion

//region Inbound
fun cdpTqLocationFromCode(code: String) = when (code) {
    "66019005" -> TqData.Location.EXTREMITY.dataString
    "22943007" -> TqData.Location.TRUNCAL.dataString
    "50974003" -> TqData.Location.JUNCTIONAL.dataString
    else -> null
}

fun cdpTqSubLocationFromCode(code: String) = when (code) {
    "368208006" -> TqData.SubLocation.LUE.dataString
    "368209003" -> TqData.SubLocation.RUE.dataString
    "32153003" -> TqData.SubLocation.LLE.dataString
    "62175007" -> TqData.SubLocation.RLE.dataString
    else -> null
}
//endregion
