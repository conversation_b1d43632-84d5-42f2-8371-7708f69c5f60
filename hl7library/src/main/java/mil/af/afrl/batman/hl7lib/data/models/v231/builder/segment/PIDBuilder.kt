package mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment

import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.v231.datatype.CX
import ca.uhn.hl7v2.model.v231.segment.PID
import gov.afrl.batdok.encounter.CountryData
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.converter.TimeConverter
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.oidBATDOK
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.oidEDIPI
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.oidTAC
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.pid045IdentifierTypeCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.pid08SexCode
import mil.af.afrl.batman.hl7lib.util.HL7Data
import mil.af.afrl.batman.hl7lib.util.EscapeUtil
import mil.af.afrl.batman.hl7lib.util.asUuidString
import org.openehealth.ipf.modules.hl7.kotlin.nrp
import java.time.Instant
import java.time.ZoneId

/**
 * Note: The PID segment contains a repeating PID.3 field to specify different patient identifiers.
 * There will always be two instances of PID.3 field.
 * The first will contain a unique ID for the patient, defined as MRN.
 * The second instance will contain either the EDIPI or the IPI of the patient.
 * If the EDIPI is available, it will be defined as MILITARYID.
 * Otherwise, the second instance will contain the IPI for the patient, defined as INTERIMPID.
 */
class PIDBuilder(private val endpoint: Endpoint): SegmentBuilder<PID> {
    override fun populateFromBatdokPatient(parent: AbstractMessage, hl7Data: HL7Data): PID {
        val document = hl7Data.document!!
        val cleanedPatientId = document.info.patientId.asUuidString()

        // Set PID - Patient Information
        val pid = PID(parent, parent.modelClassFactory)
        pid.pid1_SetIDPID.parse("1")

        // Set internal ID to DOD ID if present, as we do not know Medical Records Number MRN
        val dodId = document.info.dodId
        // If DOD ID is available, set the second PID.3 field to EDIPI
        if (!dodId.isNullOrEmpty()) {
            val internalID = pid.nrp(3) as CX
            internalID.id.parse(dodId)
            internalID.assigningAuthority.parse(oidEDIPI) // Magic number from ICD
            internalID.identifierTypeCode.parse("EDIPI")
        }

        val ssn = document.info.ssn?.filter { it.isDigit() }
        if (!ssn.isNullOrEmpty() && endpoint.format != EndpointType.TAC) {
            pid.pid19_SSNNumberPatient.parse(ssn)
        }

        val alternateIdEntry = pid.nrp(4) as CX
        alternateIdEntry.id.parse(cleanedPatientId)
        alternateIdEntry.assigningAuthority.parse(oidBATDOK)
        alternateIdEntry.cx5_IdentifierTypeCode.parse(pid045IdentifierTypeCode())
        // Add any other external system IDs (minus those we already added to the message)
        for (id in hl7Data.externalIds?.filter { it.key !in listOf(oidBATDOK, oidEDIPI) } ?: emptyMap()) {
            (pid.nrp(4) as CX).apply {
                this.id.parse(id.value)
                assigningAuthority.parse(id.key)
                if (id.key == oidTAC) {
                    cx5_IdentifierTypeCode.parse("TAC")
                }
            }
        }

        if (document.info.name != null) {
            val pidName = pid.insertPatientName(0)
            pidName.givenName.parse(EscapeUtil.escape(document.info.name?.first ?: ""))
            if(document.info.name?.middle != null){
                pidName.middleInitialOrName.parse(EscapeUtil.escape(document.info.name?.middle!!))
            }
            pidName.familyLastName.parse(EscapeUtil.escape(document.info.name?.last ?: ""))
                // Set the name type to L for Legal
            pidName.nameTypeCode.parse("L")
        }

        val dob = document.info.dateOfBirth.dob
        if (dob != null) {
            val instant: Instant = dob.atStartOfDay(ZoneId.of("UTC")).toInstant()
            pid.pid7_DateTimeOfBirth.parse(TimeConverter.timestampToFormattedDate(instant))
        } else {
            pid.pid7_DateTimeOfBirth.parse("********")
        }
        // Note: ICD supports sex values M, F, NA. BATDOK only supports M and F. HL7 defines U instead of NA.
        pid.pid8_Sex.parse(pid08SexCode(document.info.gender))

        // Doc ID is our encounter ID
        pid.pid18_PatientAccountNumber.parse(document.id.asUuidString())


        if (endpoint.format == EndpointType.TAC) {
            val nationality = document.info.nationality ?: ""
            if (nationality != "") {
                val countryData = getCountryData(nationality.toString())
                if (countryData != null){
                    pid.pid28_Nationality.ce1_Identifier.parse(countryData.abbreviation)//abbreviation
                    pid.pid28_Nationality.ce2_Text.parse(countryData.dataString)//dataString
                    pid.pid28_Nationality.ce3_NameOfCodingSystem.parse("ISO 3166")
                }
            }
        }
        return pid
    }

    fun getCountryData(countryName: String): CountryData? {
        return try {
            CountryData.valueOf(countryName)
        } catch (e: IllegalArgumentException) {
            null
        }
    }

}
