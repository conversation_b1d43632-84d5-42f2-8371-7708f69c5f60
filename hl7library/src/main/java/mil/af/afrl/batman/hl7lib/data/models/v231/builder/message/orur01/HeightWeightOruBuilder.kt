package mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.orur01

import ca.uhn.hl7v2.model.v231.group.ORU_R01_OBXNTE
import ca.uhn.hl7v2.model.v231.message.ORU_R01
import ca.uhn.hl7v2.model.v231.segment.OBX
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.converter.TimeConverter
import mil.af.afrl.batman.hl7lib.converter.UnitConverter
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.heightCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.obr04VitalCodingScheme
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.obx033CodingScheme
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.tbsaCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.vitalObrCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.weightCode
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ExtendedCodedElement
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup
import mil.af.afrl.batman.hl7lib.util.HL7Data
import mil.af.afrl.batman.hl7lib.util.hashedWith
import org.openehealth.ipf.modules.hl7.kotlin.from
import org.openehealth.ipf.modules.hl7.kotlin.nrp
import java.time.Instant

class HeightWeightOruBuilder(
    hl7Data: HL7Data,
    defaultTimestamp: Instant
) : BaseORU_R01Builder(hl7Data, defaultTimestamp) {

    override fun buildOrus(): List<ORU_R01> {
        val document = hl7Data.document ?: return emptyList()
        val msg = buildBaseOruR01(
            hl7Data,
            defaultTimestamp,
            vitalObrCode(endpoint),
            document.id.hashedWith("heightandweight"),
            obr04VitalCodingScheme(endpoint)
        )
        val observationGroups = mutableListOf<ORU_R01_OBXNTE>()
        var obxIdx = 1

        fun buildObx(codeNameGroup: CodeNameGroup, value: Float?, unit: String): OBX {
            return OBX(msg, msg.modelClassFactory).apply {
                setIDOBX.from(obxIdx++)
                valueType.parse("NM")
                observationResultStatus.parse("F")
                observationIdentifier.from(
                    ExtendedCodedElement(
                        msg,
                        codeNameGroup,
                        obx033CodingScheme(endpoint)
                    )
                )
                nrp(5).parse(value?.toString() ?: "")
                // OMDS just wants the raw unit string here instead of a coded value
                val exportUnit = if (endpoint.format == EndpointType.OMDS) {
                    unit
                } else {
                    UnitConverter.batdokDataToCodedElement(msg, unit, endpoint)
                }
                nrp(6).from(exportUnit)
                dateTimeOfTheObservation.parse(
                    TimeConverter.timestampToFormattedDateTime(
                        defaultTimestamp
                    )
                )
            }
        }

        if (document.info.height != null) {
            val heightObx = buildObx(heightCode(endpoint), document.info.height, "cm")
            observationGroups.add(ORU_R01_OBXNTE(msg, msg.modelClassFactory).also {
                it.obx.from(heightObx)
            })
        }

        if (document.info.weight != null) {
            val weightObx = buildObx(weightCode(endpoint), document.info.weight, "kg")
            observationGroups.add(ORU_R01_OBXNTE(msg, msg.modelClassFactory).also {
                it.obx.from(weightObx)
            })
        }

        if (document.injuries.tbsa != null && tbsaCode(endpoint) != null) {
            val tbsaObx = buildObx(tbsaCode(endpoint)!!, document.injuries.tbsa!!.toFloat(), "%")
            observationGroups.add(ORU_R01_OBXNTE(msg, msg.modelClassFactory).also {
                it.obx.from(tbsaObx)
            })
        }

        return if (observationGroups.isNotEmpty()) {
            msg.getObservationGroup().apply {
                this.obr.from(obr)
                observationGroups.mapIndexed { idx, item -> insertOBXNTE(item, idx) }
            }
            listOf(msg)
        } else emptyList()
    }

}
