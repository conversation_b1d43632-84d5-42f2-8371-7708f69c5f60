package mil.af.afrl.batman.hl7lib.data.models.v231.segment

import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.Group
import ca.uhn.hl7v2.model.v231.datatype.CE
import ca.uhn.hl7v2.model.v231.datatype.NM
import ca.uhn.hl7v2.model.v231.datatype.ST
import ca.uhn.hl7v2.parser.ModelClassFactory
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.zui03qualifierCode
import mil.af.afrl.batman.hl7lib.data.models.v231.customSegments.ExtendedAbstractSegment
import mil.af.afrl.batman.hl7lib.util.HL7Data
import org.openehealth.ipf.modules.hl7.kotlin.get

class ZUI(parent: Group?, factory: ModelClassFactory? = null) : ExtendedAbstractSegment(parent, factory) {

    init {
        addSingleField(true, 4, "Set ID", NM::class.java)
        addSingleField(true, 36, "Identifier", ST::class.java)
        addSingleField(true, 4, "Qualifier", CE::class.java)
    }

    val zui1_setId: NM
        get() = get(1) as NM
    val zui2_identifier: ST
        get() = get(2) as ST
    val zui3_qualifier: CE
        get() = get(3) as CE

    override fun populateFromBatdokPatient(parent: AbstractMessage, hl7Data: HL7Data): ZUI {
        get(1).parse("1")
        get(3).parse(zui03qualifierCode())

        return this
    }

}
