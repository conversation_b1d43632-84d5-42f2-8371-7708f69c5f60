package mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment

import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.Event
import gov.afrl.batdok.encounter.orders.IOrderLine
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.obx033NoteCodingScheme
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.transferNoteCode
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ExtendedCodedElement
import mil.af.afrl.batman.hl7lib.util.EscapeUtil
import org.openehealth.ipf.modules.hl7.kotlin.from
import org.openehealth.ipf.modules.hl7.kotlin.nrp

fun populateObxFromEvent(
    parent: AbstractMessage,
    event: Event,
    endpoint: Endpoint,
    referenceOrder: IOrderLine? = null,
    ) : List<OBX> {

    val obxList = mutableListOf<OBX>()
    var idx = 1

    fun addObx(value: String?, type: String = "TX") {
        buildBaseOBX(parent, event.timestamp).apply {
            obx3_ObservationIdentifier.from(ExtendedCodedElement(parent, transferNoteCode(endpoint), obx033NoteCodingScheme(endpoint)))
            obx2_ValueType.from(type)
            nrp(5).parse(EscapeUtil.escape(value ?: return))
            obx1_SetIDOBX.from(idx++)
            obx11_ObservationResultStatus.parse("F")
            obxList.add(this)
        }
    }

    if (referenceOrder != null) {
        addObx("Reference Order: ${referenceOrder.title}")
    }
    event.event.lines().forEach { addObx(it) }
    return obxList
}
