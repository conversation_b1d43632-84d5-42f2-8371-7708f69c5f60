package mil.af.afrl.batman.hl7lib

import android.content.Context
import android.os.Environment
import android.util.Base64
import android.util.Log
import kotlinx.coroutines.runBlocking
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.message.MessageReceiver
import mil.af.afrl.batman.hl7lib.util.HL7Data
import mil.af.afrl.batman.hl7lib.util.HttpClient
import mil.af.afrl.batman.hl7lib.util.InboundHL7Data
import mil.af.afrl.batman.hl7lib.util.NetworkResponse
import mil.af.afrl.batman.hl7lib.util.buildSenderId
import okhttp3.Call
import okhttp3.Callback
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import org.json.JSONObject
import java.io.File
import java.io.IOException
import java.net.URLEncoder
import java.nio.charset.StandardCharsets
import java.time.Instant
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit

object MessageRequester {

    var requestCompletedCallback: ((HL7Data, NetworkResponse) -> Unit)? = null
    private lateinit var client: OkHttpClient

    // Return thread so that caller can join and await response if needed
    fun requestMessages(hl7Data: InboundHL7Data, context: Context) : Thread {
        val requesterThread = Thread {
            client = HttpClient.getClient()

            if (hl7Data.endpoint?.format == EndpointType.OMDS) {
                splitRequests(hl7Data, context)
            } else if (hl7Data.endpoint is MhsgtNetworkEndpoint) {
                requestMessagesFromEndpoint(
                    context,
                    hl7Data,
                    (hl7Data.endpoint as MhsgtNetworkEndpoint).inboundUrl
                ) {
                    try {
                        val parsedData = if (it.isSuccess() && hl7Check(it.message)) {
                            // This will just block the network thread spawned in this method
                            // Non-null validated by hl7Check
                            runBlocking { MessageReceiver(context).processHL7Data(it.message!!.byteInputStream()) }
                        } else {
                            NetworkLogger.logToFile("Rejected HL7 message: Status=${it.code}, Content=${it.message}")
                            requestCompletedCallback?.invoke(hl7Data, it)
                            return@requestMessagesFromEndpoint
                        }
                        requestCompletedCallback?.invoke(parsedData, it)
                    } catch (e: Exception) {
                        NetworkLogger.logToFile("Exception while processing HL7 message: ${e.message}")
                        requestCompletedCallback?.invoke(
                            hl7Data,
                            NetworkResponse(error = e)
                        )
                    }
                }
            }
        }
        requesterThread.start()
        return requesterThread
    }

    fun cancelRequests() {
        client.dispatcher.cancelAll()
    }

    private fun splitRequests(hl7Data: InboundHL7Data, context: Context) {
        var a28Response: NetworkResponse? = null
        var pampiResponse: NetworkResponse? = null
        var documentResponse: NetworkResponse? = null
        val processLock = Any()

        fun processHL7Data() {
            val responses = listOf(a28Response, pampiResponse, documentResponse)

            // If null, we are missing a response. Ensures all responses past here are non-null.
            if (responses.any { it == null }) return

            // If none of the responses gave success codes, we have some sort of error
            if (responses.none { it!!.isSuccess()}) {
                // 200 is success, any error should be higher. I can only report one of them though.
                val maxErrorCode = responses.maxBy { it!!.code ?: 1000 }
                requestCompletedCallback?.invoke(hl7Data, maxErrorCode!!)
                return
            }

            // Concat only responses with a 200-series code for success
            val fullResponse = responses.filter { it!!.isSuccess() }
                .joinToString("\r") { it?.message ?: "" }

            // Otherwise, we can concat both together and process into BATDOK commands
            try {
                // This will just block our local network thread
                val parsedData = runBlocking { MessageReceiver(context).processHL7Data(fullResponse.byteInputStream()) }
                requestCompletedCallback?.invoke(parsedData, NetworkResponse(code = 200))
            } catch (e: Exception) {
                requestCompletedCallback?.invoke(hl7Data, NetworkResponse(error = e))
            }
        }

        val endpoint = hl7Data.endpoint

        if (endpoint !is OmdsNetworkEndpoint) {
            requestCompletedCallback?.invoke(hl7Data, NetworkResponse(message = "Invalid endpoint selection"))
            return
        }

        fun performRequest(endpointAddr: String, onResult: (NetworkResponse) -> Unit) {
            requestMessagesFromEndpoint(context, hl7Data, endpointAddr) {
                synchronized(processLock) {
                    onResult(it)
                    processHL7Data()
                }
            }
        }

        performRequest(endpoint.a28Url) { a28Response = it }
        performRequest(endpoint.pampiUrl) { pampiResponse = it }
        performRequest(endpoint.documentsUrl) { documentResponse = it }
    }

    private fun requestMessagesFromEndpoint(
        context: Context,
        inboundHL7Data: InboundHL7Data,
        url: String,
        responseCallback: (NetworkResponse) -> Unit
    ) {
        val uri = encodeUrlParameters(context, inboundHL7Data, url)
        if (uri == null) {
            responseCallback(NetworkResponse(message = "Invalid endpoint URL"))
            return
        }
        val requestBuilder = Request.Builder().url(uri)
        if (inboundHL7Data.authToken != null) {
            requestBuilder.addHeader("Authorization", "Bearer ${inboundHL7Data.authToken}")
        }
        val request = requestBuilder.build()

        try {
            client.newCall(request).enqueue(object : Callback {
                override fun onFailure(call: Call, e: IOException) {
                    NetworkLogger.logToFile("Request to ${call.request().url} failed with exception: ${e.message}")
                    responseCallback(NetworkResponse(error = e))
                }

                override fun onResponse(call: Call, response: Response) {
                    val responseBody = response.body?.string()
                    val urlData = call.request().url.toString()
                    
                    NetworkLogger.logHttpCall(call.request(), response)
                    
                    response.close()
                    try {
                        val jsonObject = JSONObject(responseBody.toString())

                        if (jsonObject.has("Message")) {
                            val message = jsonObject.getString("Message")
                            if (message.isEmpty()) {
                                Log.e("MessageRequester", "Empty 'Message' field in successful response from $urlData")
                                responseCallback(NetworkResponse(message = "Empty 'Message' field in response"))
                                return
                            }

                            val decodedBytes = Base64.decode(message, Base64.DEFAULT)
                            val decodedMessage = String(decodedBytes, StandardCharsets.UTF_8)

                            responseCallback(NetworkResponse(response.code, message = decodedMessage))
                        } else if (jsonObject.has("Messages")) {
                            val encodedMessage = jsonObject.getString("Messages")
                            val decodedBytes = Base64.decode(encodedMessage, Base64.DEFAULT)
                            val decodedMessage = String(decodedBytes, StandardCharsets.UTF_8)

                            responseCallback(NetworkResponse(response.code, message = decodedMessage))
                        } else if (jsonObject.has("Documents")) {
                            val decodedMessages = StringBuilder()
                            val documentArray = jsonObject.getJSONArray("Documents")
                            for (i in 0 until documentArray.length()) {
                                val documentObject = documentArray.getJSONObject(i)
                                if (documentObject.getString("DocType").uppercase() != "HL7") continue
                                val encodedMessage = documentObject.getString("Document")
                                val decodedBytes = Base64.decode(encodedMessage, Base64.DEFAULT)
                                decodedMessages.append(String(decodedBytes, StandardCharsets.UTF_8)).append('\r')
                            }

                            responseCallback(NetworkResponse(response.code, message = decodedMessages.toString()))
                        } else {
                            Log.e("MessageRequester", "No valid JSON payload in response from $urlData")
                            responseCallback(NetworkResponse(message = "No valid JSON payload in response from $urlData"))
                        }
                    } catch (e: Exception) {
                        if (inboundHL7Data.endpoint?.format == EndpointType.OMDS || responseBody == null) {
                            // OMDS should have sent us a real JSON object response, so send back a parse error
                            // This is the case where the response code is valid but the HL7 message is invalid
                            NetworkLogger.logHttpCall(call.request(), response, "Exception while parsing response from $urlData: ${e.message}")
                            responseCallback(NetworkResponse(error = e))
                        } else {
                            // GT (and possibly others in the future) just send us a raw HL7 batch, so send it along
                            // If this is not actually valid HL7, it will throw an error in the parser later on
                            responseCallback(NetworkResponse(response.code, message = responseBody))
                        }
                    }
                }
            })
        } catch (exception: Exception) {
            NetworkLogger.logToFile("Request timeout failed with exception: ${exception.message}")
            responseCallback(NetworkResponse(error = exception))
        }
    }

    private fun encodeUrlParameters(context: Context, inboundHL7Data: InboundHL7Data, url: String): String? {
        val patientId = inboundHL7Data.patientId ?: return null
        val idType = if (patientId.matches("\\d{10}".toRegex())) "edipi" else "ipi"
        return when (inboundHL7Data.endpoint?.format) {
            EndpointType.MHSG_T -> {
                // Needs ID and type (EDIPI or IPI)
                "$url?patientid=${
                    URLEncoder.encode(
                        patientId,
                        StandardCharsets.UTF_8.toString()
                    )
                }&patientidtype=$idType"
            }

            EndpointType.OMDS -> {
                val startDate = Instant.now().minus(210240, ChronoUnit.HOURS)

                // Get formatted StartDate
                val formattedStartDate = formatDateForOMDS(startDate)

                // Get formatted StopDate with the current time
                val formattedStopDate = formatDateForOMDS()

                val jsonParam =
                    "[{\"id_type\": \"${idType.uppercase()}\", \"id_value\": \"$patientId\"}]"
                val idParams = URLEncoder.encode(jsonParam, StandardCharsets.UTF_8.toString())

                val facilityId =
                    buildSenderId(context, inboundHL7Data.batdokInstallationId) ?: "batdok"

                if (url.contains("a28")) {
                    "$url?ids=$idParams&FacilityId=$facilityId"
                } else {
                    "$url?ids=$idParams&FacilityId=$facilityId&StartDate=$formattedStartDate&StopDate=$formattedStopDate"
                }

            }

            else -> null
        }
    }

    private fun hl7Check(message: String?): Boolean {
        //looks for MSH| at the start of a line to confirm if its a hl7 message
        val mshFinder = Regex("""(?m)^MSH\|""")
        return mshFinder.containsMatchIn(message ?: return false)
    }

    private fun formatDateForOMDS(date: Instant = Instant.now()): String {
        val zonedDateTime = date.atZone(ZoneOffset.UTC)
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
        return formatter.format(zonedDateTime)
    }
}

private object NetworkLogger {
    private const val LOG_FILE_NAME = "http_logs.txt"

    fun logToFile(message: String){
        try {
            val httpDir = File(Environment.getExternalStorageDirectory().absolutePath + "/Batdok/HTTP/")
            if (!httpDir.exists()) httpDir.mkdirs()

            val logFile = File(httpDir, LOG_FILE_NAME)
            logFile.appendText("${getTimestamp()} $message\n")
        } catch (e: Exception){
            Log.e("NetworkLogger", "Failed to write to log file", e)
        }
    }

    fun logHttpCall(
        request: Request,
        response: Response,
        note: String? = null
    ) {
        val logMessage = buildString {
            appendLine("==== HTTP CALL ====")
            appendLine("URL: ${request.url}")
            appendLine("Method: ${request.method}")
            appendLine("Request Headers:")
            request.headers.forEach { appendLine(" ${it.first}: ${it.second}") }

            appendLine("Response Code: ${response.code}")
            appendLine("Response Message: ${response.message}")
            appendLine("Response Headers:")
            response.headers.forEach { appendLine(" ${it.first}: ${it.second}") }

            if (!note.isNullOrEmpty()) {
                appendLine("Note: $note")
            }
            
            appendLine("==================\n")
        }

        logToFile(logMessage)
    }

    private fun getTimestamp(): String {
        return DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
            .withZone(ZoneOffset.UTC)
            .format(Instant.now())
    }
}