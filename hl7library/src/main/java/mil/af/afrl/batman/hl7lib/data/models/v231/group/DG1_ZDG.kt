package mil.af.afrl.batman.hl7lib.data.models.v231.group

import ca.uhn.hl7v2.HL7Exception
import ca.uhn.hl7v2.model.AbstractGroup
import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.Group
import ca.uhn.hl7v2.model.v231.segment.DG1
import ca.uhn.hl7v2.parser.ModelClassFactory
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.converter.InjuryConverter
import mil.af.afrl.batman.hl7lib.converter.MoiConverter
import mil.af.afrl.batman.hl7lib.converter.TimeConverter
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.DG1Builder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.getInjuryData
import mil.af.afrl.batman.hl7lib.data.models.v231.segment.ZDG
import mil.af.afrl.batman.hl7lib.util.HL7Data
import org.openehealth.ipf.modules.hl7.kotlin.from

class DG1_ZDG(parent: Group, factory: ModelClassFactory) : AbstractGroup(parent, factory) {
    init {
        try {
            this.add(DG1::class.java, true, false)
            this.add(ZDG::class.java, false, false)
        } catch (e: HL7Exception) {
            throw RuntimeException("Unable to create DG1_ZDG group", e)
        }
    }

    val dg1: DG1
        get() = get("DG1") as DG1

    val zdg: ZDG
        get() = get("ZDG") as ZDG

    operator fun component1(): DG1 = dg1
    operator fun component2(): ZDG = zdg

    companion object{
        fun dg1ZdgData(hl7Data: HL7Data, endpoint: Endpoint, msg: AbstractMessage):List<DG1_ZDG>{
            val dg1ZdgGroupList = mutableListOf<DG1_ZDG>()
            val document = hl7Data.document
            val mois = document?.injuries?.mechanismsOfInjury

            val injuryLocationMapping =
                hl7Data.let { getInjuryData(it.document?.injuries!!, it.mode.toString()) }.toMutableList()

            var dg1Count = 0

            if (mois != null) {
                for (locationItemPair in mois) {
                    for (moi in locationItemPair.value) {
                        val codedValue = MoiConverter.batdokDataToCodedElement(msg, moi, endpoint)
                        val dg1 = DG1Builder().populateFromBatdokPatient(msg, hl7Data)
                        dg1.dg11_SetIDDG1.parse((dg1Count + 1).toString())
                        dg1.dg13_DiagnosisCodeDG1.from(codedValue)
                        if (endpoint.format == EndpointType.TAC){
                            if (dg1.dg13_DiagnosisCodeDG1.ce1_Identifier.value == "NA"){
                                dg1.dg13_DiagnosisCodeDG1.ce1_Identifier.parse("")
                            }

                            if (dg1.dg13_DiagnosisCodeDG1.ce1_Identifier.value.length <= 2){
                                dg1.dg13_DiagnosisCodeDG1.ce3_NameOfCodingSystem.parse("INJURY")
                            }
                            dg1.dg13_DiagnosisCodeDG1.ce4_AlternateIdentifier.parse("MOI")
                        }
                        if (endpoint.format == EndpointType.OMDS) {
                            dg1.dg14_DiagnosisDescription.parse(moi)
                        }
                        dg1.dg15_DiagnosisDateTime.parse(document.info.timeInfo?.let { TimeConverter.timestampToFormattedDateTime(it) })
                        dg1.dg16_DiagnosisType.parse("A")
                        val dg1ZdgGroup = DG1_ZDG(msg, msg.modelClassFactory)
                        dg1ZdgGroup.dg1.from(dg1)
                        dg1ZdgGroupList.add(dg1ZdgGroup)
                        dg1Count ++
                    }
                }
            }

            injuryLocationMapping.forEach { injuryData ->
                val dg1ZdgGroup = DG1_ZDG(msg, msg.modelClassFactory)
                val dg1 = DG1Builder().populateFromBatdokPatient(msg, hl7Data)

                val injuryLocation = if (endpoint.format == EndpointType.TAC) "Default" else injuryData.location
                val codedValue = InjuryConverter(injuryLocation).batdokDataToCodedElement(msg, injuryData.injury, endpoint)

                dg1.dg11_SetIDDG1.parse((dg1Count + 1).toString())
                dg1.dg13_DiagnosisCodeDG1.from(codedValue)
                if (endpoint.format == EndpointType.TAC){
                    if (dg1.dg13_DiagnosisCodeDG1.ce1_Identifier.value == "NA"){
                        dg1.dg13_DiagnosisCodeDG1.ce1_Identifier.parse("")
                    }

                    if (dg1.dg13_DiagnosisCodeDG1.ce1_Identifier.value.length <= 2){
                        dg1.dg13_DiagnosisCodeDG1.ce3_NameOfCodingSystem.parse("INJURY")
                    }
                    dg1.dg13_DiagnosisCodeDG1.ce4_AlternateIdentifier.parse("Injury")
                }
                val locationData = injuryData.location
                val splitResult = locationData?.split(" ", limit = 2)
                val frontBack = splitResult?.get(0)
                val bodyPart = if (frontBack == "Posterior" || frontBack == "Anterior") {
                    if (splitResult.size > 1) splitResult[1] else null
                } else {
                    null
                }

                if (endpoint.format == EndpointType.OMDS) {
                    val injuryName = injuryData.injury.injury
                    val injurySite = if (frontBack != "Default") {
                        " ($frontBack $bodyPart)"
                    } else " (Location Unknown)"
                    val injuryDescription = "$injuryName$injurySite"
                    dg1.dg14_DiagnosisDescription.parse(injuryDescription)
                }

                if (document != null) {
                    dg1.dg15_DiagnosisDateTime.parse(document.info.timeInfo?.let {
                        TimeConverter.timestampToFormattedDateTime(it)
                    })
                }
                dg1.dg16_DiagnosisType.parse("A")
                dg1ZdgGroup.dg1.from(dg1)

                if (endpoint.format == EndpointType.TAC && frontBack != "Default"){
                    val zdg =
                        ZDG(msg, msg.modelClassFactory).populateFromBatdokPatient(msg, hl7Data).apply {
                            zdg1_frontBack.parse(frontBack)
                            zdg2_injuryBodyLocation.parse(bodyPart)
                        }
                    dg1ZdgGroup.zdg.from(zdg)
                }
                dg1ZdgGroupList.add(dg1ZdgGroup)
                dg1Count++
            }
            return dg1ZdgGroupList
        }
    }
}