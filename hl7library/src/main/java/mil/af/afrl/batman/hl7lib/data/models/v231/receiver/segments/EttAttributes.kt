package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments

import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.treatment.TubeData
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.ettDepthCode
import org.openehealth.ipf.modules.hl7.kotlin.value

fun buildEttData(attributes: List<OBX>) : TubeData {
    for (attr in attributes) {
        // We only care for depth at teeth from CDP
        val code = attr.obx3_ObservationIdentifier.ce1_Identifier?.value ?: continue
        val value = attr.obx5_ObservationValue.firstOrNull()?.value ?: continue
        if (code == ettDepthCode(Endpoint.TAC)?.code) {
            return TubeData(depth = value.toIntOrNull())
        }
    }
    return TubeData()
}
