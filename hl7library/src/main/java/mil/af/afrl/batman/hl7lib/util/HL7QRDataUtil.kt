package mil.af.afrl.batman.hl7lib.util

import android.content.Context
import android.widget.Toast
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import mil.af.afrl.batman.hl7lib.MessageExporter
import mil.af.afrl.batman.hl7lib.util.QrCodeGenerator

suspend fun getHl7QRDataList(context: Context, hL7Data: OutboundHL7Data): List<Array<BooleanArray>>{
    val qrCodeGenerator = QrCodeGenerator()

    val hL7Messages = if (hL7Data.document != null) {
        qrCodeGenerator.currentPatient = hL7Data.document!!
        MessageExporter.exportPatientQrString(hL7Data, context)
    } else {
        withContext(Dispatchers.Main) {
            Toast.makeText(context, "Patient data is not ready to send", Toast.LENGTH_SHORT).show()
        }
        return emptyList()
    }

    return qrCodeGenerator.generatePatientQr(chunkSize = 150, data = hL7Messages, dataType = hL7Data.qrDataType)
}