package mil.af.afrl.batman.hl7lib.data.models.v231.builder.message

import android.content.Context
import android.os.Environment
import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.v231.segment.BHS
import ca.uhn.hl7v2.model.v231.segment.BTS
import ca.uhn.hl7v2.model.v231.segment.FHS
import ca.uhn.hl7v2.model.v231.segment.FTS
import ca.uhn.hl7v2.model.v231.segment.MSH
import ca.uhn.hl7v2.parser.DefaultModelClassFactory
import ca.uhn.hl7v2.parser.EncodingCharacters
import ca.uhn.hl7v2.parser.PipeParser
import gov.afrl.batdok.encounter.Name
import gov.afrl.batdok.util.NameFormatter
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.converter.TimeConverter
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.msh03SendApplicationCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.msh05ReceiveApplicationCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.msh06ReceivingFacilityCode
import mil.af.afrl.batman.hl7lib.initializeTables
import mil.af.afrl.batman.hl7lib.util.HL7Data
import mil.af.afrl.batman.hl7lib.util.OutboundHL7Data
import mil.af.afrl.batman.hl7lib.util.nonBloodMeds
import org.openehealth.ipf.modules.hl7.kotlin.from
import org.openehealth.ipf.modules.hl7.kotlin.nrp
import java.io.BufferedOutputStream
import java.io.File
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale


class MessageGenerator(private val deviceID: String?,
                       private val endpoint: Endpoint,
                       private val writeToFile: Boolean = false,
                       private val context: Context? = null) {

    private var batchFile: File? = null
    private val buildBatch = true

    init {
        if (context != null) {
            initializeTables(context)
        }
    }

    // This only returns the generated message for the unit tests
    fun writeMessagesForEncounter(document: OutboundHL7Data) : String {
        return buildMessagesForEncounter(document).joinToString("\r")
    }

    fun buildMessagesForEncounter(hL7Data: OutboundHL7Data) : List<String> {
        // TODO: Make this just part of the HL7Data, remove from MessageGenerator to eliminate redundancy
        hL7Data.endpoint = endpoint
        val document = hL7Data.document ?: return emptyList()
        if (buildBatch && writeToFile) {
            batchFile = createOutputFile(document.info.name)
            hL7Data.historicalHL7.takeIf { it.isNotEmpty() }?.let {
                batchFile?.writeText(it.trim() + '\r')
            }
        }
        val messageList = listOfNotNull(
            writeBatchHeaders(),
            writeAdtA02(hL7Data),
            writeAdtA04(hL7Data),
            writeAdtA28(hL7Data),

        ) +
            writeAdtA31(hL7Data) +
            writeOruR01(hL7Data) +
            writeOrmO01(hL7Data) +
        listOfNotNull(
            //writeOruZb2(hL7Data),
            writeAdtA03(hL7Data),
        )
        // Add in any historical HL7 data we received from BATDOK when generating the messages
        // Subtract out header message from list size (batch trailers does nothing unless headers also written)
        val allMessages =
            listOf(hL7Data.historicalHL7) +  // Historical messages
                messageList +  // Current HL7
                listOfNotNull(writeBatchTrailers(messageList.size - 1))  // Trailer segments
        return if (buildBatch) {
            listOf(allMessages.joinToString(""))
        } else {
            allMessages
        }
    }

    private fun writeBatchHeaders() : String? {
        if (buildBatch) {
            // HAPI still wants the header segments associated with a message, even though that
            //  doesn't make sense. Build this generic message here to handle that.
            val genericMessage = BatchMessage()

            // The FHS segment is technically optional since we're sending one batch now, but
            //  I'm adding it for possible future changes
            val fhs = FHS(genericMessage, DefaultModelClassFactory()).apply {
                fileFieldSeparator.parse("|")
                fileEncodingCharacters.value = "^~\\&"
                fileSendingApplication.parse(msh03SendApplicationCode(endpoint))
                fileReceivingApplication.parse(msh05ReceiveApplicationCode(endpoint))
                if (endpoint.format == EndpointType.MHSG_T) {
                    fileSendingFacility.parse(deviceID)
                }
                fileReceivingFacility.parse(msh06ReceivingFacilityCode(endpoint))
                fileCreationDateTime.parse(TimeConverter.timestampToFormattedDateTime())
            }
            genericMessage.addNonstandardSegment("FHS")
            genericMessage["FHS"].from(fhs)

            val bhs = BHS(genericMessage, DefaultModelClassFactory()).apply {
                batchFieldSeparator.parse("|")
                batchEncodingCharacters.value = "^~\\&"
                batchSendingApplication.parse(msh03SendApplicationCode(endpoint))
                batchReceivingApplication.parse(msh05ReceiveApplicationCode(endpoint))
                if (endpoint.format == EndpointType.MHSG_T) {
                    batchSendingFacility.parse(deviceID)
                }
                batchReceivingFacility.parse(msh06ReceivingFacilityCode(endpoint))
                batchCreationDateTime.parse(TimeConverter.timestampToFormattedDateTime())
            }
            genericMessage.addNonstandardSegment("BHS")
            genericMessage["BHS"].from(bhs)

            genericMessage.finalize()
            return genericMessage.encode()
        }

        return null
    }

    private fun writeAdtA02(hL7Data: HL7Data) : String? {
        if (endpoint.format == EndpointType.MHSG_T && (hL7Data.document!!.movement.pickupLocation != null || hL7Data.document!!.movement.dropoffLocation != null)) {
            return ADT_A02Builder(endpoint).populateAll(hL7Data).build().finalize("ADT", "A02", hL7Data.document!!.info.name)
        }
        return null
    }

    private fun writeAdtA04(hL7Data: HL7Data) : String {
        return ADT_A04Builder(endpoint).populateAll(hL7Data).build().finalize("ADT", "A04", hL7Data.document!!.info.name)
    }

    private fun writeAdtA28(hl7Data: HL7Data) : String {
        return ADT_A28Builder(endpoint).populateAll(hl7Data).build().finalize("ADT", "A28", hl7Data.document!!.info.name)
    }

    private fun writeAdtA31(hl7Data: HL7Data) : List<String> {
        val allergies = hl7Data.document!!.info.allergies
        val injuries = hl7Data.document!!.injuries.injuries
        val mois = hl7Data.document!!.injuries.mechanismsOfInjury
        val treatments = hl7Data.document!!.treatments.list
        val removedTreats = hl7Data.document!!.treatments.onProperRemovedItems
        val observations = hl7Data.document!!.observations.list
        val procedures = hl7Data.document!!.metadata.proceduresDone

        if (endpoint.format != EndpointType.MHSG_T && (allergies.isNotEmpty()|| injuries.isNotEmpty() || mois.isNotEmpty()
                    || treatments.isNotEmpty() || removedTreats.isNotEmpty() || observations.isNotEmpty() || procedures.isNotEmpty())){
            return ADT_A31Builder(endpoint).populateAll(hl7Data).map { it.finalize("ADT", "A31", hl7Data.document!!.info.name) }
        }
        return emptyList()
    }

    private fun writeOruR01(hl7Data: HL7Data) : List<String> {
        return ORU_R01Builder(endpoint).populateAll(hl7Data)
            .map { it.finalize("ORU", "R01", hl7Data.document!!.info.name) }
    }

    private fun writeOruZb2(hl7Data: HL7Data) : String {
        return ORU_ZB2Builder(endpoint,).populateAll(hl7Data).build().finalize("ORU", "ZB2", hl7Data.document!!.info.name)
    }

    private fun writeOrmO01(hl7Data: HL7Data) : List<String> {
        // TODO: Add orders when they are part of the encounter
        if (hl7Data.document!!.medicines.list.nonBloodMeds(endpoint.format).isNotEmpty()) {//  || getOrdersByType(document.orders, true).isNotEmpty()) {
            return ORM_O01Builder(endpoint).populateAll(hl7Data).map { it.finalize("ORM", "O01", hl7Data.document!!.info.name) }
        }
        return emptyList()
    }

    private fun writeAdtA03(hl7Data: HL7Data) : String {
        return ADT_A03Builder(endpoint).populateAll(hl7Data).build().finalize("ADT", "A03", hl7Data.document!!.info.name)
    }

    private fun writeBatchTrailers(msgCount: Int) : String? {
        if (buildBatch) {
            // HAPI still wants the header segments associated with a message, even though that
            //  doesn't make sense. Build this generic message here to handle that.
            val genericMessage = BatchMessage()

            val bts = BTS(genericMessage, DefaultModelClassFactory()).apply {
                batchMessageCount.from(msgCount)
                nrp(3).from(msgCount)  // Batch total
            }
            genericMessage.addNonstandardSegment("BTS")
            genericMessage["BTS"].from(bts)

            // FTS would be optional if we exclude FHS in the header
            val fts = FTS(genericMessage, DefaultModelClassFactory()).apply {
                fileBatchCount.from(1)  // Always one batch per file for us
            }
            genericMessage.addNonstandardSegment("FTS")
            genericMessage["FTS"].from(fts)

            genericMessage.finalize()
            return genericMessage.encode()
        }

        return null
    }

    private fun AbstractMessage.finalize(messageType: String? = null, triggerEvent: String? = null, patientName: Name? = null) : String {
        // We use a BatchMessage to build the batch headers/footers, which don't include this stuff
        if (this !is BatchMessage) {
            val config = parser.parserConfiguration
            config.isValidating = false
            if (endpoint.format == EndpointType.MHSG_T) {
                (this["MSH"] as MSH).sendingFacility.parse(deviceID)
            }
        }

        if (writeToFile) {
            val outputFile = if (buildBatch) {
                // Write to a single long-running file
                batchFile
            } else {
                // Write to individual files
                createOutputFile(patientName, messageType, triggerEvent)
            }

            try {
                BufferedOutputStream(FileOutputStream(outputFile, buildBatch)).use {
                    it.write(encode().toByteArray())
                }
            } catch (e: Exception) {
                // It the file output fails, just skip it. Probably didn't grant permission.
            }
        }

        return encode()
    }

    private fun createOutputFile(patientName: Name?, messageType: String? = null, triggerEvent: String? = null) : File {
        val downloadsDir = File(Environment.getExternalStorageDirectory().absolutePath + "/Batdok/HL7/")
        downloadsDir.mkdirs()
        val timestamp = SimpleDateFormat("MMddyyyyHHmmss", Locale.ROOT).format(Date())
        val patientNameString = if (patientName == null) "" else "_${patientName.toString(NameFormatter.Format.FIRST_MIDDLE_LAST).replace(" ", "_")}"
        val baseFileName = if (messageType == null || triggerEvent == null) {
            "BATDOK_${deviceID}_batch_$timestamp$patientNameString.txt"
        } else {
            "BATDOK_${deviceID}_${messageType}_${triggerEvent}_$timestamp$patientNameString.txt"
        }
        var idx = 1
        var fileName = baseFileName
        while (File(downloadsDir, fileName).exists()) {
            fileName = baseFileName.substringBefore('.') + "_${idx++}.txt"
        }
        return File(downloadsDir, fileName)
    }

    private class BatchMessage : AbstractMessage(DefaultModelClassFactory()) {
        override fun getFieldSeparatorValue() = '|'
        override fun getEncodingCharactersValue() = "^~\\&"
        override fun encode() : String {
            // The standard PipeParser tries to validate that there's an MSH
            // We need to ignore that here
            return PipeParser.encode(this, EncodingCharacters(fieldSeparatorValue, encodingCharactersValue))
        }
    }
}
