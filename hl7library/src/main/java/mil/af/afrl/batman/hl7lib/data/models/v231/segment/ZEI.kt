package mil.af.afrl.batman.hl7lib.data.models.v231.segment

import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.Group
import ca.uhn.hl7v2.model.v21.datatype.CM
import ca.uhn.hl7v2.model.v231.datatype.ID
import ca.uhn.hl7v2.model.v231.datatype.SI
import ca.uhn.hl7v2.parser.ModelClassFactory
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.converter.ServiceConverter
import mil.af.afrl.batman.hl7lib.converter.getRankFromGradeAndService
import mil.af.afrl.batman.hl7lib.converter.gradeCodeFromString
import mil.af.afrl.batman.hl7lib.data.models.v231.customSegments.ExtendedAbstractSegment
import mil.af.afrl.batman.hl7lib.util.HL7Data
import org.openehealth.ipf.modules.hl7.kotlin.get

class ZEI(parent: Group?, factory: ModelClassFactory? = null) : ExtendedAbstractSegment(parent, factory) {

    init {
        addSingleField(true, 4, "ZEI Set ID", SI::class.java)
        // Refer to Appendix C, Table C-43: Transmitted Values for ZEI-2 – Employment Status Code
        addSingleField(10, "Employment Status Code", ID::class.java)
        addSingleField( 100, "Employer Name") // Service as freetext from BATDOK
        addBlankFields(7) // Skip fields [4-10] Employer: Address; Phone; Contact Name. Employee ID Number. Occupation. Employment Start/Stop
        addSingleField(200, "Employer Name - Coded", CM::class.java) // Patient US Government Agency Code - Table C-10
        addBlankFields(1) // Skip field 12, Employer Tax Number
        addSingleField(20, "Job Code^Class", CM::class.java) // 13.1 – Patient Affiliation Category Code; 13.2 Patient Pay Category
        addBlankFields(1) // Skip field 14, Position
        addSingleField(20, "Job Title") // Free text job title (Rank)
        addBlankFields(2) // Skip fields 16 and 17, Contact Title, Retired Date
        addSingleField(20, "Job Title Code", CM::class.java) // Employment Title code; Coded job title (Rank)
    }

    override fun populateFromBatdokPatient(parent: AbstractMessage, hl7Data: HL7Data): ExtendedAbstractSegment {
        val document = hl7Data.document!!
        get(1).parse("1")
        val service = document.info.service
        val patcatService = document.info.patcat?.service
        if (patcatService != null) {
            get(3).parse(patcatService.abbreviation)
            get(11).parse(ServiceConverter.batdokServiceToEmployerCode(document, hl7Data.endpoint))
        } else if (service != null) {
            get(3).parse(service)
            get(11).parse(ServiceConverter.batdokServiceToEmployerCode(document, hl7Data.endpoint))
        }

        if (hl7Data.endpoint?.format != EndpointType.TAC) {
            val batdokRank = if (!document.info.grade.isNullOrEmpty()) {
                gradeCodeFromString(document.info.grade!!)
            } else if (!document.info.rank.isNullOrEmpty()) {
                // Rank is deprecated, leaving for any straggling usages
                document.info.rank!!
            } else null
            if (batdokRank != null) {
                get(15).parse(batdokRank)
                val codedRank = getRankFromGradeAndService(document)
                if (batdokRank != codedRank) {
                    get(18).parse(codedRank)
                }
            }
        }
        return this
    }
}
