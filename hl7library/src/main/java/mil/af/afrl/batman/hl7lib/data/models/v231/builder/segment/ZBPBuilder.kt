package mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment

import ca.uhn.hl7v2.model.AbstractMessage
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.data.models.v231.segment.ZBP
import mil.af.afrl.batman.hl7lib.util.HL7Data

class ZBPBuilder(private val endpoint: Endpoint): SegmentBuilder<ZBP> {
    override fun populateFromBatdokPatient(parent: AbstractMessage, hl7Data: HL7Data): ZBP {
        val document = hl7Data.document!!

        val bpuID = "123456"
        val unitLot = "654321"
        val subUnit = "55555"

        val productID = "Fresh Whole Blood(FWB)"
        val productDesc = "description"
        val productAltDesc = "alt description"

        val product = "$productID^$productDesc^$productAltDesc test2"

        //Potential fields: Received, Stored, Transfused, Donated [Received vs Transfused - ensure that.]
        val status = "TRANSFUSED"
        val eventDT = 20241118143430
        val expireDT = 20241218143430

        // ABO Codes Expected
        // values: O, A, B, AB, OH, CR, Undetermined, Pooled ABO
        val abo = "O"

        //RH codes expected
        //Values: Rh-Pos, POS, NEG,Undetermined, CR, Pooled Rh
        val rh = "Rh-Pos"
        val antigen = ""
        val attribute = ""

        val supplierName = "Tacos"
        val supplierID = "147258369"

        val pooledProduct = "N"
        val originalVolume = 500
        val transfusedVolume = 300
        val transfusedTotalUnits = 100
        val transfusedQuantity = 1
        val volumeUnits =  "mLs"
        val isbtFlag = ""
        val productFormat = ""
        val quantityUnits = ""
        val strengthUnits = ""

        //set ZBP - BPU information
        val zbp = ZBP(parent, parent.modelClassFactory)
        zbp.ZBP1_SetID.parse("1")
        zbp.ZBP2_BPU_UniqueID.parse(bpuID)
        zbp.ZBP3_BPU_UnitLotNumber.parse(unitLot)
        zbp.ZBP4_BPU_SubUnit.parse(subUnit)
        zbp.ZBP5_BPU_Product.getComponent(0).parse(productID)
        zbp.ZBP5_BPU_Product.getComponent(1).parse(productDesc)
        zbp.ZBP5_BPU_Product.getComponent(2).parse(productAltDesc)
        //zbp.ZBP5_BPU_Product.parse(product)
        zbp.ZBP6_BPU_Status.parse(status)
        zbp.ZBP7_BPU_EventDateTime.parse(eventDT.toString())
        zbp.ZBP8_BPU_ExpireDateTime.parse(expireDT.toString())
        zbp.ZBP9_BPU_ABO.parse(abo)
        zbp.ZBP10_BPU_RH.parse(rh)
        zbp.ZBP11_BPU_Antigen.parse(antigen)
        zbp.ZBP12_BPU_Attributes.parse(attribute)
        zbp.ZBP13_BPU_Supplier.xon1_OrganizationName.parse(supplierName)
        zbp.ZBP13_BPU_Supplier.xon3_IDNumber.parse(supplierID)
        zbp.ZBP17_PooledProductID.parse(pooledProduct)
        zbp.ZBP18_OrginalVolume.parse(originalVolume.toString())
        zbp.ZBP19_AssumedTransferredVolume.parse(transfusedVolume.toString())
        zbp.ZBP20_AssumedTransfusedTotalUnits.parse(transfusedTotalUnits.toString())
        zbp.ZBP21_AssumedTransfusedQuantity.parse(transfusedQuantity.toString())
        zbp.ZBP23_VolumeUnitsOfMeasure.parse(volumeUnits)
        zbp.ZBP24_ISBTFlag.parse(isbtFlag)
        zbp.ZBP25_ProductFormatNum.parse(productFormat)
        zbp.ZBP26_QuantityUnits.parse(quantityUnits)
        zbp.ZBP27_StrengthUnits.parse(strengthUnits)

        return zbp
    }
}