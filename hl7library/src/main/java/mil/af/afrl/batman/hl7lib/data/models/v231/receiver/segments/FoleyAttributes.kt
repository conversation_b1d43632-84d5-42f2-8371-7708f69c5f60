package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments

import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.treatment.FoleyCatheterData
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.foleySizeCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.foleyTypeFromCode
import org.openehealth.ipf.modules.hl7.kotlin.value

fun foleyAttributes(attributes: List<OBX>, endpoint: EndpointType) : FoleyCatheterData {
    var type: String? = null
    var size: Float? = null
    
    attributes.forEach {
        val code = it.obx3_ObservationIdentifier.ce1_Identifier?.value ?: return@forEach
        val value = it.obx5_ObservationValue.firstOrNull()?.value ?: return@forEach
        if (type == null){
            type = foleyTypeFromCode(code)
        }

        if (size == null) {
            when (code){
                foleySizeCode(endpoint)?.code -> size = value.toFloat()
            }
        }
    }
    
    return FoleyCatheterData(size = size, color = type)
}