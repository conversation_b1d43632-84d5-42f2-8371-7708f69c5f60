package mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.orur01

import ca.uhn.hl7v2.model.v231.message.ORU_R01
import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.EncounterVital
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.populateListFromBatdokVital
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.obr04VitalCodingScheme
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.vitalObrCode
import mil.af.afrl.batman.hl7lib.util.HL7Data
import java.time.Instant

class VitalOruBuilder(
    hl7Data: HL7Data,
    defaultTimestamp: Instant
) : BaseRepeatableORU_R01Builder<EncounterVital>(hl7Data, defaultTimestamp) {

    override fun buildOrus(): List<ORU_R01> {
        val vitals = if (hl7Data.endpoint?.format == EndpointType.TAC) {
            hl7Data.document!!.vitals.list.takeLast(3)
        } else {
            hl7Data.document!!.vitals.list
        }
        return vitals.mapNotNull { buildRepeatableDataOru(hl7Data, it) }
    }

    override fun getObrDetails(item: EncounterVital) = ItemData(
        item.timestamp,
        vitalObrCode(endpoint),
        item.vitalId,
        obr04VitalCodingScheme(endpoint)
    )

    override fun buildObservationList(msg: ORU_R01, item: EncounterVital): List<OBX> {
        return populateListFromBatdokVital(msg, item, endpoint)
    }

}
