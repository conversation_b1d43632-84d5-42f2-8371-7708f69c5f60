package mil.af.afrl.batman.hl7lib.util

import okhttp3.ConnectionSpec
import okhttp3.OkHttpClient
import java.security.KeyStore
import java.util.concurrent.TimeUnit
import javax.net.ssl.SSLContext
import javax.net.ssl.SSLSocketFactory
import javax.net.ssl.TrustManagerFactory
import javax.net.ssl.X509TrustManager

object HttpClient {

    fun getClient(): OkHttpClient {
        // Build and return an OkHttpClient that uses our SSLContext
        return OkHttpClient.Builder()
            .sslSocketFactory(
                createSslSocketFactory(),
                createTrustManager()
            )
            .connectionSpecs(
                listOf(
                    ConnectionSpec.MODERN_TLS,
                    ConnectionSpec.COMPATIBLE_TLS,
                    ConnectionSpec.CLEARTEXT
                )
            )
            .hostnameVerifier { _, _ -> true }
            .readTimeout(180, TimeUnit.SECONDS)
            .writeTimeout(180, TimeUnit.SECONDS)
            .connectTimeout(180, TimeUnit.SECONDS)
            .callTimeout(180, TimeUnit.SECONDS)
            .build()
    }

    fun createSslSocketFactory(): SSLSocketFactory {
        val sslContext = SSLContext.getInstance("TLSv1.2")
        sslContext.init(null, arrayOf(createTrustManager()), null)
        return sslContext.socketFactory
    }

    private fun createTrustManager(): X509TrustManager {
        // Android system CA store (with all the user and default certs)
        val keyStore = KeyStore.getInstance("AndroidCAStore").apply {
            safeInitialize()
        }

        // Create a TrustManager that trusts the certificates in the KeyStore
        val trustManagerFactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm())
        trustManagerFactory.init(keyStore)
        val trustManagers = trustManagerFactory.trustManagers
        return trustManagers.first { it is X509TrustManager } as X509TrustManager
    }

    private fun KeyStore.safeInitialize() {
        // There's no good way to tell if a KeyStore is initialized already...
        try {
            size()
        } catch (e: Exception) {
            // Keystore is not initialized
            // It only should be initialized already in a unit test
            load(null, null) // Initialize the KeyStore with no password
        }
    }
}
