package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments

import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.observation.PupilDilationData
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.leftPupilDilationCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.rightPupilDilationCode
import org.openehealth.ipf.modules.hl7.kotlin.value

fun perrlaDataAttributes(attributes: List<OBX>) : PupilDilationData{
    var leftSize: Int? = null
    var rightSize: Int? = null
   
    for (attr in attributes){
        val code = attr.obx3_ObservationIdentifier.ce1_Identifier?.value ?: continue
        val value = attr.obx5_ObservationValue.firstOrNull()?.value ?: continue
        if (code == leftPupilDilationCode(EndpointType.TAC)?.code){
            leftSize = value.toIntOrNull()
        }
        if (code == rightPupilDilationCode(EndpointType.TAC)?.code){
            rightSize = value.toIntOrNull()
        }
    }
    
    return PupilDilationData(perrlaSizeRight = rightSize, perrlaSizeLeft = leftSize)
}