package mil.af.afrl.batman.hl7lib.converter

import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.v231.datatype.CE
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ExtendedCodedElement
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup
import java.io.InputStream

// Outer Key: Normalized data item name
// Outer Value:
//   pair.first: Raw data item name
//   pair.second:
//     Inner Key: Name of codeset
//     Inner Value: Code alias and description for this item and codeset
typealias BatdokCodeData = MutableMap<String, Pair<String, MutableMap<String, CodeNameGroup>>>

abstract class BatdokDataConverter<T> {
    companion object {
        // Only keeps alphabetic and numeric characters
        private val NORMALIZER_REGEX = Regex("[^a-zA-Z0-9]")
        // this map corrects known typos present in current or previously shipped BATDOK versions
        private val batdokTypos = mapOf(
            "ERTHYROMYCIN" to "ERYTHROMYCIN",
            "TRIMETHROPRIM" to "TRIMETHOPRIM",
            "HYMENOPTRA" to "HYMENOPTERA",
            "BISMOUTH_IMPREGNATED_PETROLEUM_GAUZE" to "BISMUTH_IMPREGNATED_PETROLEUM_GAUZE",
            "HETHASTARCH_IN_LACTATED_ELECTROLYTE_SOLUTION" to "HETASTARCH_IN_LACTATED_ELECTROLYTE_SOLUTION",
            "ODANASTERON" to "ONDANSETRON",
            "TERBENIFNE" to "TERBINAFINE",
            "METHYLPRESDNISOLONE" to "METHYLPREDNISOLONE"
        )
        fun normalize(input: String?): String? {
            val normalized = input?.replace(NORMALIZER_REGEX, "_")?.uppercase()
            return batdokTypos.getOrDefault(normalized, normalized)
        }
        fun buildDefaultTableEntry(name: String, codedValue: String, system: String, desc: String? = null) = normalize(name)!! to CodeNameGroup(codedValue, desc ?: "")
    }
    var fullTableLoaded = false
    abstract fun T.toName() : String?
    open fun T.toKey() = normalize(toName())
    protected abstract fun buildType(name: String) : T?

    open val map: BatdokCodeData = mutableMapOf()
    open fun loadCsvData(dataStream: InputStream, mapToLoad: BatdokCodeData = map) : Boolean {
        mapToLoad.clear()  // If called again, reset the map
        dataStream.bufferedReader(Charsets.UTF_8).useLines { lineSequence ->
            // Every line should be structured as
            //  ITEM_NAME, CODE_NAME_1, CODE_VAL_1, CODE_DESC_1, CODE_NAME_2, CODE_VAL_2, ...
            // If there is a header row, skip it
            val itemMap = lineSequence.filterNot { it.startsWith("name", true) }.associate { line ->
                val lineSplits = line.split('\t')
                if ((lineSplits.size - 1) % 3 != 0) {
                    return false  // We have bad data
                }
                // First item will be key to the outer map
                val rawItemName = lineSplits.first()
                val itemName = normalize(rawItemName)!!
                // All other items will be put into the inner map
                val innerMap = mutableMapOf<String, CodeNameGroup>()
                val itemCodeInfos = lineSplits.drop(1)
                for ((codeType, code, desc) in itemCodeInfos.chunked(3)) {
                    innerMap[codeType] = CodeNameGroup(code, desc)
                }
                itemName to Pair(rawItemName, innerMap)
            }
            mapToLoad.putAll(itemMap)
        }
        return true
    }

    protected abstract fun codeTypeForEndpoint(endpoint: Endpoint) : String
    protected open fun displayCodeTypeForEndpoint(endpoint: Endpoint) : String = codeTypeForEndpoint(endpoint)

    protected open fun getDefaultCode(endpoint: Endpoint): CodeNameGroup = CodeNameGroup("", "")
    protected fun batdokDataToCodeData(data: T, codeSystem: String): CodeNameGroup? = map[data.toKey()]?.second?.get(codeSystem)

    open fun batdokDataToCodedElement(message: AbstractMessage, data: T, endpoint: Endpoint): CE {
        val codeSystem = codeTypeForEndpoint(endpoint)
        val codedData = batdokDataToCodeData(data, codeSystem) ?: getDefaultCode(endpoint)
        return if (codedData.code.isNotEmpty()) {
            ExtendedCodedElement(message, codedData, displayCodeTypeForEndpoint(endpoint))
        } else {
            ExtendedCodedElement(message, "", data.toName() ?: "", "")
        }
    }

    fun codeToBatdokData(code: String, endpoint: Endpoint) = codeToBatdokData(code, codeTypeForEndpoint(endpoint))

    fun codeToBatdokData(code: String, codeType: String): T? {
        // Find a value in the map where the code system maps to the correct code value
        val filteredMap = map.filterValues { it.second[codeType]?.code == code }
        // There should really only be one key remaining in the filtered map (if it was found), so use it
        val itemName = filteredMap.keys.firstOrNull()
        // Convert that item to whatever type we need to return
        // Pull the raw item name string from the map
        return map[itemName]?.first?.let { buildType(it) }
    }
}
