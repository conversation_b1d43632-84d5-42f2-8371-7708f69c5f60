package mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.orur01

import ca.uhn.hl7v2.model.v231.message.ORU_R01
import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.observation.Blood
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.populateObxFromBloodMed
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.bloodObrCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.obr04CodingScheme
import mil.af.afrl.batman.hl7lib.util.HL7Data
import java.time.Instant

class BloodOruBuilder(
    hl7Data: HL7Data,
    defaultTimestamp: Instant
) : BaseRepeatableORU_R01Builder<Blood>(hl7Data, defaultTimestamp) {

    override fun buildOrus(): List<ORU_R01> {
        return hl7Data.document!!.bloodList.list.mapNotNull {
            buildRepeatableDataOru(hl7Data, it)
        }
    }

    override fun getObrDetails(item: Blood) = ItemData(
        item.timestamp,
        bloodObrCode(endpoint),
        item.bloodId,
        obr04CodingScheme(endpoint)
    )

    override fun buildObservationList(msg: ORU_R01, item: Blood): List<OBX> {
        return populateObxFromBloodMed(msg, item, endpoint)
    }

}
