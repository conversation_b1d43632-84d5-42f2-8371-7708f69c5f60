package mil.af.afrl.batman.hl7lib.converter

import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ICD10
import mil.af.afrl.batman.hl7lib.data.models.v231.field.TACICD10

object MoiConverter : BatdokDataConverter<String>() {
    override fun String.toName() = this
    override fun buildType(name: String) = name

    override fun codeTypeForEndpoint(endpoint: Endpoint): String {
        return when (endpoint.format) {
            EndpointType.TAC -> TACICD10
            EndpointType.OMDS -> ICD10
            EndpointType.MHSG_T -> ICD10
        }
    }

    override fun displayCodeTypeForEndpoint(endpoint: Endpoint) = ICD10
}