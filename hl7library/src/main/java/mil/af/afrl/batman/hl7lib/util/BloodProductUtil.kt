package mil.af.afrl.batman.hl7lib.util

import gov.afrl.batdok.encounter.medicine.Medicine
import mil.af.afrl.batman.hl7lib.EndpointType
import java.time.Instant
import java.time.temporal.ChronoUnit

fun List<Medicine>.nonBloodMeds(endpointType: EndpointType? = null) : List<Medicine> {
    val baseMeds = filterNot { it.type?.contains("blood", true) == true }
    return if (endpointType == EndpointType.TAC) {
        // TAC wants IV meds and regular meds (non-blood products) from the last 12 hours
        baseMeds
            .filter{it.administrationTime!=null}
            .filter{ it.administrationTime!!.isAfter(Instant.now().minus(12, ChronoUnit.HOURS)) }
    } else baseMeds
}

fun List<Medicine>.bloodMeds() = filter { it.type?.contains("blood", true) == true }
