package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.message

import ca.uhn.hl7v2.model.v231.message.VXU_V04
import ca.uhn.hl7v2.model.v231.segment.PID
import ca.uhn.hl7v2.model.v231.segment.PV1
import gov.afrl.batdok.commands.proto.DocumentCommands.CommandData
import gov.afrl.batdok.encounter.pampi.Immunization
import gov.afrl.batdok.encounter.commands.buildUpdateImmunizationCommand
import mil.af.afrl.batman.hl7lib.EndpointType
import org.openehealth.ipf.modules.hl7.kotlin.value

class VXU_V04Receiver(message: VXU_V04, endpointType: EndpointType) : BaseReceiver<VXU_V04>(message, endpointType) {

    override val pid: PID = message.pid
    override val pv1: PV1 = message.pV1PV2.pV1

    override fun getMessageCommands(): List<CommandData> {
        val immunizations = mutableListOf<Immunization>()

        for (rxa in message.orcrxarxrobxnteAll.map { it.rxa }) {
            val vaxName = rxa.rxa5_AdministeredCode.ce2_Text.value ?: ""
            val dosage = rxa.rxa6_AdministeredAmount.value.toFloatOrNull() ?: 0f
            val dosageField = rxa.rxa7_AdministeredUnits
            val dosageUnit = dosageField.ce2_Text.value?.ifEmpty { null } ?: dosageField.ce1_Identifier.value ?: ""
            val adminTimestamp = parseInstant(rxa.rxa3_DateTimeStartOfAdministration.value)

            immunizations += Immunization(vaxName, dosageUnit, dosage, adminTimestamp)
        }

        return mutableListOf<CommandData>().apply {
            addCommand {
                buildUpdateImmunizationCommand(immunizations, emptyList())
            }
        }
    }

}
