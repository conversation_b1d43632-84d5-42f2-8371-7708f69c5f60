package mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure

import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup

fun ettCode(endpoint: EndpointType) = when (endpoint) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("112798008", "Insertion of endotracheal tube (procedure)")
    else -> null
}

fun ettDepthCode(endpoint: Endpoint) = when (endpoint.format) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("131197000", "Depth (qualifier value)")
    else -> null
}
