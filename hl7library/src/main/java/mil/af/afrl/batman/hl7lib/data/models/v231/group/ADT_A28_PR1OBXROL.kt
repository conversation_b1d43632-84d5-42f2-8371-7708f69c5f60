package mil.af.afrl.batman.hl7lib.data.models.v231.group

import ca.uhn.hl7v2.HL7Exception
import ca.uhn.hl7v2.model.Group
import ca.uhn.hl7v2.model.v231.group.ADT_A28_PR1ROL
import ca.uhn.hl7v2.model.v231.group.ADT_AXX_PR1ROL
import ca.uhn.hl7v2.model.v231.segment.OBX
import ca.uhn.hl7v2.model.v231.segment.PR1
import ca.uhn.hl7v2.parser.ModelClassFactory

// Hacky custom group used to send OBX segments in a non-standard HL7 format for CDP for procedure attributes
class ADT_A28_PR1OBXROL(parent: Group, factory: ModelClassFactory) : ADT_A28_PR1ROL(parent, factory), AbstractPR1OBXROL {
    
    init {
        add(OBX::class.java, false, true, 1)
    }

    override val pr1: PR1
        get() = pR1
    override val obxs: List<OBX>
        get() = getOBXAll()

    fun getOBX(rep: Int) : OBX {
        return getTyped("OBX", rep, OBX::class.java)
    }

    fun getOBXAll() : List<OBX> {
        return getAllAsList("OBX", OBX::class.java)
    }

    @Throws(HL7Exception::class)
    fun insertOBX(structure: OBX, rep: Int) {
        super.insertRepetition("OBX", structure, rep)
    }

    fun getPR1(rep: Int): PR1 {
        return getTyped("PR1", rep, PR1::class.java)
    }
}
