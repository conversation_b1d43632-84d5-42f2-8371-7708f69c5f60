package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments

import gov.afrl.batdok.encounter.treatment.O2Data

fun o2Attributes(treatmentCode: String) : O2Data {
    val deliveryMethod: String? = when(treatmentCode) {
        "425696007" -> O2Data.DeliveryMethod.BVM.dataString
        "427591007" -> O2Data.DeliveryMethod.NRB.dataString
        "336623009" -> O2Data.DeliveryMethod.NC.dataString
        "1258985005" -> O2Data.DeliveryMethod.VENT.dataString
        else -> null
    }
    return O2Data(deliveryMethod = deliveryMethod)
}
