package mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment

import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.v231.datatype.XCN
import ca.uhn.hl7v2.model.v231.segment.PV1
import gov.afrl.batdok.commands.proto.DocumentCommands.CommandData
import gov.afrl.batdok.commands.proto.InfoCommands
import gov.afrl.batdok.encounter.Document
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.converter.TimeConverter
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.pv102PatientClassCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.pv1034FacilityIDCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.pv118PatientTypeCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.pv139ServicingFacilityCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.pv141AccountStatusCode
import mil.af.afrl.batman.hl7lib.util.HL7Data
import mil.af.afrl.batman.hl7lib.util.OutboundHL7Data
import mil.af.afrl.batman.hl7lib.util.ProviderInfo
import mil.af.afrl.batman.hl7lib.util.EscapeUtil
import mil.af.afrl.batman.hl7lib.util.asUuidString
import org.openehealth.ipf.modules.hl7.kotlin.from
import java.time.Instant

class PV1Builder(private val endpoint: Endpoint, private val providerInfo: ProviderInfo?): SegmentBuilder<PV1> {
    override fun populateFromBatdokPatient(parent: AbstractMessage, hl7Data: HL7Data): PV1 {
        val document = hl7Data.document!!
        val pv1 = PV1(parent, parent.modelClassFactory)
        pv1.pv11_SetIDPV1.parse("1")
        pv1.pv12_PatientClass.parse(pv102PatientClassCode(endpoint))
        pv1.pv13_AssignedPatientLocation.pl4_Facility.parse(pv1034FacilityIDCode(endpoint))
        pv1.pv118_PatientType.parse(pv118PatientTypeCode())
        if (endpoint.format == EndpointType.MHSG_T) {
            pv1.pv119_VisitNumber.parse(document.id.asUuidString())
        }
        pv1.pv139_ServicingFacility.parse(pv139ServicingFacilityCode(endpoint))
        pv1.pv141_AccountStatus.parse(pv141AccountStatusCode(endpoint))
        
        // For most endpoints, PV1.44 should be the time the encounter was opened
        val outbound = hl7Data as? OutboundHL7Data
        val activeCommands: List<CommandData> = outbound
            ?.commandsByEncounter
            ?.get(outbound.activeEncounterId)
            .takeUnless { it.isNullOrEmpty() }
            ?: outbound?.commandsList
            ?: emptyList()

        val openTime: Instant? = activeCommands.firstNotNullOfOrNull { cmdData ->
            if (cmdData.data.`is`(InfoCommands.ChangeOpenCloseEncounterCommand::class.java)) {
                val cmd =
                    cmdData.data.unpack(InfoCommands.ChangeOpenCloseEncounterCommand::class.java)
                // cmdData.timestamp is a Long? — convert when open
                if (cmd.open) Instant.ofEpochSecond(cmdData.timestamp) else null
            } else null
        }

        val injuryTime: Instant? = document.info.timeInfo

        pv1.pv144_AdmitDateTime.parse(TimeConverter.timestampToFormattedDateTime(openTime))
        
        // For GT, PV1.44 should be the injury time, or nowtime if we have nothing else (they need something)
        if (endpoint.format == EndpointType.MHSG_T) {
            if (injuryTime != null) {
                pv1.pv144_AdmitDateTime.parse(TimeConverter.timestampToFormattedDateTime(injuryTime))
            } else if (pv1.pv144_AdmitDateTime.isEmpty) {
                pv1.pv144_AdmitDateTime.parse(TimeConverter.timestampToFormattedDateTime())
            }
        }

        // Always consider discharge the time of export
        pv1.pv145_DischargeDateTime.parse(TimeConverter.timestampToFormattedDateTime())
        if(document.info.maskingJustification != null){
            pv1.pv116_VIPIndicator.parse("Y")
        }
        if (providerInfo != null) {
            val doctorInfo = XCN(parent).also {
                // The first/last name is all one string. BATDOK logic is to treat everything
                //  before the first space as a first name and the rest as a last name.
                if (endpoint.format == EndpointType.MHSG_T || providerInfo.dodId.isNotEmpty()) {
                    it.idNumber.parse(providerInfo.dodId)
                    it.familyLastName.parse(EscapeUtil.escape(providerInfo.name.substringAfter(" ")))
                    it.givenName.parse(EscapeUtil.escape(providerInfo.name.substringBefore(" ")))
                }
            }
            pv1.insertAttendingDoctor(0).from(doctorInfo)
        }

        return pv1
    }

    // TODO: Need better understanding of Evac to properly utilize here
    fun populateFromEvac(pv1: PV1, document: Document): PV1 {
        val pickupLocation = document.movement.pickupLocation
        var pickupLocRegion = pickupLocation?.region
        if (pickupLocation?.region == null){
            pickupLocRegion = ""
        }

        if (pickupLocation != null) {
            val priorLocation = pv1.pv16_PriorPatientLocation
            priorLocation.pl9_LocationDescription.parse(EscapeUtil.escape("$pickupLocRegion ${pickupLocation.location}"))
        }

        val dropoffLocation = document.movement.dropoffLocation
        var dropoffLocRegion = dropoffLocation?.region
        if (dropoffLocation?.region == null){
            dropoffLocRegion = ""
        }
        if (dropoffLocation != null) {
            val currentLocation = pv1.pv13_AssignedPatientLocation
            currentLocation.pl9_LocationDescription.parse(EscapeUtil.escape("$dropoffLocRegion ${dropoffLocation.location}"))
        }
        return pv1
    }
}
