package mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment

import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.Complaint
import gov.afrl.batdok.encounter.Complaints
import gov.afrl.batdok.encounter.Document
import gov.afrl.batdok.encounter.Event
import gov.afrl.batdok.encounter.History
import gov.afrl.batdok.encounter.HistoryType
import gov.afrl.batdok.encounter.KnownEventTypes
import gov.afrl.batdok.encounter.Observations
import gov.afrl.batdok.encounter.observation.CommonObservations
import gov.afrl.batdok.encounter.observation.PhysicalExamData
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.obx033NoteCodingScheme
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.transferNoteCode
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ExtendedCodedElement
import mil.af.afrl.batman.hl7lib.util.EscapeUtil
import org.openehealth.ipf.modules.hl7.kotlin.from
import org.openehealth.ipf.modules.hl7.kotlin.nrp

    fun populateObxFromDnbi(
        parent: AbstractMessage,
        document: Document,
        endpoint: Endpoint,
        ): List<OBX> {

    val obxList = mutableListOf<OBX>()
    var idx = 1

    fun addObx(value: String?, type: String = "TX") {
        buildBaseOBX(parent, document.info.timeInfo).apply {
            obx3_ObservationIdentifier.from(
                ExtendedCodedElement(
                    parent,
                    transferNoteCode(endpoint),
                    obx033NoteCodingScheme(endpoint)
                )
            )
            obx2_ValueType.from(type)
            nrp(5).parse(EscapeUtil.escape(value ?: return))
            obx1_SetIDOBX.from(idx++)
            obx11_ObservationResultStatus.parse("F")
            obxList.add(this)
        }
    }

    fun getPastMedHistory(history: History): String {
        fun getHistory(type: HistoryType) =
            history.histories[type.dataString]?.takeUnless { it.isBlank() }

        val medHx = getHistory(HistoryType.MEDICAL)?.let { "MedHx: $it" }
        val surgHx = getHistory(HistoryType.SURGICAL)?.let { "SurgHx: $it" }
        val famHx = getHistory(HistoryType.FAMILY)?.let { "FamHx: $it" }

        return listOfNotNull(medHx, surgHx, famHx).joinToString("\n", postfix = "\n")
    }


    fun getAssessmentAndPlanString(document: Document): String {
        val groupedTexts = document.subjective.complaints.list.mapNotNull { complaint ->
            complaint.getRelatedItems(document)
                .asSequence()
                .map { it.first }
                .filterIsInstance<Event>()
                .filter { it.eventType == KnownEventTypes.PLANNING.dataString || it.eventType == KnownEventTypes.ASSESSMENT.dataString }
                .joinToString("\n") { "${complaint.complaint}: ${it.event}" }
                .takeUnless { it.isEmpty() }
        }
        return groupedTexts.joinToString("\n")
    }

    val redFlagStr = StringBuilder()
    fun checkRedFlags(complaints: Complaints, document: Document) {
        for (complaint in complaints.list) {
            val redFlagsList = complaint.getRedFlags(document).mapNotNull { it.name }
            redFlagStr.append(redFlagsList.firstOrNull() ?: "None")
        }
    }

    val dnbiStr = StringBuilder()
    fun createDnbiStr(document: Document) {

        val complaints = document.subjective.complaints
        val history = document.history
        dnbiStr.append("Chief Complaint: ${complaints.list.joinToString("; ") { it.complaint }}\n")
        dnbiStr.append("Subjective\n")
        dnbiStr.append("History of Present Illness:")
        dnbiStr.append(complaints.list.joinToString("\n", postfix = "\n") { it.history })
        dnbiStr.append("Review of Systems:")
        dnbiStr.append(complaints.list.joinToString("\n", postfix = "\n") { it.complaint + ": " + it.reviewOfSystems })
        dnbiStr.append("Past Medical / Surgical / Family History:")
        dnbiStr.append(getPastMedHistory(history))
        dnbiStr.append("Assessment and Plan\n")
        checkRedFlags(complaints, document)
        dnbiStr.append("Red Flags: $redFlagStr\n")
        dnbiStr.append(getAssessmentAndPlanString(document))
    }
    createDnbiStr(document)
    dnbiStr.lines().forEach { addObx(it) }

    return obxList
}
