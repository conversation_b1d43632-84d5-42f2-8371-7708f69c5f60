package mil.af.afrl.batman.hl7lib.data.models.v231.group

import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.v231.group.ORU_R01_ORCOBRNTEOBXNTECTI
import ca.uhn.hl7v2.parser.ModelClassFactory
import mil.af.afrl.batman.hl7lib.data.models.v231.segment.ZUI

// Hacky workaround group to make the ZUI appear where it should instead
//  of at the end of the group
class ORU_R01_ORCOBRNTEZUIOBXNTECTI(msg: AbstractMessage, modelClassFactory: ModelClassFactory = msg.modelClassFactory) : ORU_R01_ORCOBRNTEOBXNTECTI(msg, modelClassFactory) {
    init {
        add(ZUI::class.java, false, false, 3)
    }

    val zui = getTyped("ZUI", ZUI::class.java)
}
