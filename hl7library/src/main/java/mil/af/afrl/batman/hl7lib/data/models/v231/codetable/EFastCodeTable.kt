package mil.af.afrl.batman.hl7lib.data.models.v231.codetable

import gov.afrl.batdok.encounter.observation.EFastExamData
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup

fun efastCode(endpointType: EndpointType) = when (endpointType) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("123320627", "PoC US E-FAST Scan")
    else -> null
}

fun rightLungSlidingCode(endpointType: EndpointType) = when (endpointType) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("3341006", "Right lung structure (body structure)")
    else -> null
}

fun leftLungSlidingCode(endpointType: EndpointType) = when (endpointType) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("44029006", "Left lung structure (body structure)")
    else -> null
}

fun pericardialFluidCode(endpointType: EndpointType) = when (endpointType) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("24949005", "Pericardial sac structure (body structure)")
    else -> null
}

fun rightUpperQuadrantCode(endpointType: EndpointType) = when (endpointType) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("50519007", "Structure of right upper quadrant of abdomen (body structure)")
    else -> null
}

fun leftUpperQuadrantCode(endpointType: EndpointType) = when (endpointType) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("86367003", "Structure of left upper quadrant of abdomen (body structure)")
    else -> null
}

fun suprapubicFluidCode(endpointType: EndpointType) = when (endpointType) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("11708003", "Hypogastric region structure (body structure)")
    else -> null
}

fun interpretationCode(endpointType: EndpointType) = when (endpointType) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("Report", "")
    else -> null
}

fun efastResultCode(result: String?) = when (result) {
    EFastExamData.Type.POSITIVE.dataString -> CodeNameGroup("10828004", "Positive (qualifier)")
    EFastExamData.Type.NEGATIVE.dataString -> CodeNameGroup("260385009", "Negative (qualifier)")
    EFastExamData.Type.EQUIVOCAL.dataString -> CodeNameGroup("42425007", "Equivocal (qualifier)")
    else -> null
}
