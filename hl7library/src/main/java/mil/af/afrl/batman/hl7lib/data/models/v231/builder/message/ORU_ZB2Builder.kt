package mil.af.afrl.batman.hl7lib.data.models.v231.builder.message

import ca.uhn.hl7v2.model.v231.group.ORU_R01_PIDPD1NK1NTEPV1PV2ORCOBRNTEOBXNTECTI
import ca.uhn.hl7v2.model.v231.segment.NTE
import ca.uhn.hl7v2.model.v231.segment.OBR
import ca.uhn.hl7v2.parser.DefaultModelClassFactory
import ca.uhn.hl7v2.parser.ModelClassFactory
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.converter.TimeConverter
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.PIDBuilder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.PV1Builder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.ZBPBuilder
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.bloodProductObrCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.obr25ResultStatusCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.zb2obr04CodingScheme
import mil.af.afrl.batman.hl7lib.data.models.v231.segment.ZUI
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ExtendedCodedElement
import mil.af.afrl.batman.hl7lib.data.models.v231.group.ORU_R01_ORCOBRNTEZUIOBXNTECTI
import mil.af.afrl.batman.hl7lib.data.models.v231.message.ORU_ZB2
import mil.af.afrl.batman.hl7lib.util.HL7Data
import mil.af.afrl.batman.hl7lib.util.asUuidString
import mil.af.afrl.batman.hl7lib.util.hashedWith
import org.openehealth.ipf.modules.hl7.kotlin.from
import org.openehealth.ipf.modules.hl7.kotlin.get

class ORU_ZB2Builder(private val endpoint: Endpoint, private val factory: ModelClassFactory? = DefaultModelClassFactory()): AbstractMessageBuilder<ORU_ZB2>() {
//look at oru_r01 builder will only need to change the MSH and add the ZBP segment
    final override val msg = ORU_ZB2(factory)

    override fun populateAll(hl7Data: HL7Data): AbstractMessageBuilder<ORU_ZB2> {
        if (hl7Data.document == null) return  this
        val document = hl7Data.document
        val id = document?.id?.hashedWith("blood")
        val codeNameGroup = bloodProductObrCode(endpoint)
        val bloodComment = "test"
        
        fun buildObrSegment(msg: ORU_ZB2): OBR {
            return OBR(msg, msg.modelClassFactory).apply {
                obr1_SetIDOBR.parse("1")
                if (endpoint.format == EndpointType.MHSG_T) {
                    obr3_FillerOrderNumber.from(id?.asUuidString() ?: "")
                }
                obr4_UniversalServiceID.from(ExtendedCodedElement(msg, codeNameGroup, zb2obr04CodingScheme(endpoint)))
                obr7_ObservationDateTime.parse(TimeConverter.timestampToFormattedDateTime())
                obr22_ResultsRptStatusChngDateTime.parse(TimeConverter.timestampToFormattedDateTime())
                obr24_DiagnosticServSectID.parse("BB")
                obr25_ResultStatus.parse(obr25ResultStatusCode(endpoint))
            }
        }

        fun buildNTESegment(msg : ORU_ZB2, comment : String): NTE {
            return NTE(msg, msg.modelClassFactory).apply {
                nte1_SetIDNTE.parse("1")
                getComment(0).parse(comment)
                //nte.4 codes 46 - Blood Bank
                //47 - Blood Bank Product
                //614355 - Blood Bank Shipping
                //52 - Donation
                //53 - Donor
                //678508 - Location History
                //Outcome – Expectation is that 47, 52, and 53 are to be primary codes aligning with current requirements.
                nte4_CommentType.parse("47")
            }
        }

        fun buildZuiSegment(msg: ORU_ZB2) : ZUI {
            return ZUI(msg, msg.modelClassFactory).populateFromBatdokPatient(msg, hl7Data).apply {
                this[2].from(id.asUuidString())  // ZUI.2: Identifier
            }
        }
        populateMsh("ORU", "ZB2", endpoint)
        with(ORU_R01_PIDPD1NK1NTEPV1PV2ORCOBRNTEOBXNTECTI(msg, msg.modelClassFactory)) {
            pidpD1NK1NTEPV1PV2.pid.from(PIDBuilder(endpoint).populateFromBatdokPatient(msg, hl7Data))
            pidpD1NK1NTEPV1PV2.pV1PV2.pV1.from(PV1Builder(endpoint, hl7Data.providerInfo).populateFromBatdokPatient(msg, hl7Data))
            msg.insertPIDPD1NK1NTEPV1PV2ORCOBRNTEOBXNTECTI(this, 0)
        }

        val obsGroup = ORU_R01_ORCOBRNTEZUIOBXNTECTI(msg, msg.modelClassFactory).also {
            it.obr.from(buildObrSegment(msg))
            if (bloodComment.isNotEmpty()){
                it.insertNTE(buildNTESegment(msg,bloodComment),0)
            }
            it.zui.from(buildZuiSegment(msg))
        }
        msg.pidpD1NK1NTEPV1PV2ORCOBRNTEOBXNTECTI.insertORCOBRNTEOBXNTECTI(obsGroup, 0)

        val zbp = ZBPBuilder(endpoint).populateFromBatdokPatient(msg,hl7Data)
        msg.insertZBP(zbp,0)
        return this
    }
}