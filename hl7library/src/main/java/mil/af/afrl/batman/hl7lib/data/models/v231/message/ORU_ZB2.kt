package mil.af.afrl.batman.hl7lib.data.models.v231.message

import ca.uhn.hl7v2.HL7Exception
import ca.uhn.hl7v2.model.v231.message.ORU_R01
import ca.uhn.hl7v2.parser.ModelClassFactory
import mil.af.afrl.batman.hl7lib.data.models.v231.segment.ZBP

class ORU_ZB2(factory: ModelClassFactory?): ORU_R01(factory) {
    init {
        try {
            this.add(ZBP::class.java,true,false)
        }catch (e: HL7Exception) {
            throw RuntimeException("Unable to create CustomORU_ZB2 message", e)
        }
    }

    @Throws(HL7Exception::class)
    fun insertZBP(structure: ZBP?, rep: Int){
        super.insertRepetition("ZBP",structure,rep)
    }
}