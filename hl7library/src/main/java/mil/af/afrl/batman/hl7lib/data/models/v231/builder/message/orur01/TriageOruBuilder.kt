package mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.orur01

import ca.uhn.hl7v2.model.v231.group.ORU_R01_OBXNTE
import ca.uhn.hl7v2.model.v231.message.ORU_R01
import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.movement.TriageCategory
import mil.af.afrl.batman.hl7lib.converter.TimeConverter
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.triageObrCode
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ExtendedCodedElement
import mil.af.afrl.batman.hl7lib.data.models.v231.field.LOINC
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup
import mil.af.afrl.batman.hl7lib.util.HL7Data
import mil.af.afrl.batman.hl7lib.util.hashedWith
import org.openehealth.ipf.modules.hl7.kotlin.from
import org.openehealth.ipf.modules.hl7.kotlin.nrp
import java.time.Instant

class TriageOruBuilder(
    hl7Data: HL7Data,
    defaultTimestamp: Instant
) : BaseORU_R01Builder(hl7Data, defaultTimestamp) {

    override fun buildOrus(): List<ORU_R01> {
        val document = hl7Data.document ?: return emptyList()
        val evacObrCode = triageObrCode(endpoint) ?: return emptyList()
        val msg = buildBaseOruR01(
            hl7Data,
            defaultTimestamp,
            evacObrCode,
            document.id.hashedWith("triage")
        )

        val evacCode = when (document.info.triage) {
            TriageCategory.IMMEDIATE.dataString -> CodeNameGroup("LA17696-8", "Immediate")
            TriageCategory.DELAYED.dataString -> CodeNameGroup("LA17695-0", "Delayed")
            TriageCategory.MINIMAL.dataString -> CodeNameGroup("LA17694-3", "Minimal")
            TriageCategory.EXPECTANT.dataString -> CodeNameGroup("LA17697-6", "Expectant")
            else -> return emptyList()
        }

        val obx = OBX(msg, msg.modelClassFactory).apply {
            setIDOBX.from(1)
            valueType.parse("TX")
            observationResultStatus.parse("F")
            observationIdentifier.from(ExtendedCodedElement(msg, evacCode, LOINC))
            nrp(5).parse(evacCode.name)
            dateTimeOfTheObservation.parse(
                TimeConverter.timestampToFormattedDateTime(
                    defaultTimestamp
                )
            )
        }

        msg.getObservationGroup().apply {
            this.obr.from(obr)
            insertOBXNTE(ORU_R01_OBXNTE(msg, msg.modelClassFactory).also { it.obx.from(obx) }, 0)
        }
        return listOf(msg)
    }

}
