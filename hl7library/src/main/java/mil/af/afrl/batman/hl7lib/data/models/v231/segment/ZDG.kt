package mil.af.afrl.batman.hl7lib.data.models.v231.segment

import android.util.Log
import ca.uhn.hl7v2.HL7Exception
import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.Group
import ca.uhn.hl7v2.model.v231.datatype.CE
import ca.uhn.hl7v2.model.v231.datatype.ST
import ca.uhn.hl7v2.parser.ModelClassFactory
import mil.af.afrl.batman.hl7lib.data.models.v231.customSegments.ExtendedAbstractSegment
import mil.af.afrl.batman.hl7lib.util.HL7Data
import org.openehealth.ipf.modules.hl7.kotlin.get


class ZDG(parent: Group?, factory: ModelClassFactory? = null) : ExtendedAbstractSegment(parent, factory) {

    init {
       try {
           addSingleField(true,10, "Front Back", CE::class.java)
           addSingleField(true,60, "Injury Body Location", ST::class.java)
       } catch (e: HL7Exception){
           Log.e("HL7Library", "Unexpected error creating ZDG", e)
       }
    }

    val zdg1_frontBack: CE
        get() = get(1) as CE
    val zdg2_injuryBodyLocation: ST
        get() = get(2) as ST

    override fun populateFromBatdokPatient(parent: AbstractMessage, hl7Data: HL7Data): ZDG {
        return ZDG(parent, parent.modelClassFactory)
    }
}