package mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment

import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.Segment
import gov.afrl.batdok.encounter.Document
import mil.af.afrl.batman.hl7lib.util.HL7Data

interface RepeatedSegmentBuilder<T: Segment> {
    // This is for segments like AL1 that have repetitions
    fun populateListFromBatdokPatient(parent: AbstractMessage, document: Document): List<T>
}

interface SegmentBuilder<T: Segment>{
    fun populateFromBatdokPatient(parent: AbstractMessage, hl7Data: HL7Data): T

}
