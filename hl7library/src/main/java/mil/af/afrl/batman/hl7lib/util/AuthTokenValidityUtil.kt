package mil.af.afrl.batman.hl7lib.util

import android.util.Log
import org.json.JSONObject
import java.time.Instant
import java.time.temporal.ChronoUnit

object AuthTokenValidityUtil {

    fun isTokenInfoValid(tokenInfo: String?) : Boolean {
        if (tokenInfo.isNullOrEmpty()) return false
        try {
            val json = JSONObject(tokenInfo)
            if (!json.has("device_code") || json.isNull("device_code")) return false
            if (!json.has("refresh_token") || json.isNull("refresh_token")) return false
            
            val issueTime = json.getLong("issue_time")
            val issuedInstant = Instant.ofEpochSecond(issueTime)
            // Should be valid for 30 days, do 29 just to be safe
            return Instant.now().minus(29, ChronoUnit.DAYS).isBefore(issuedInstant)
        }
        catch (e: Exception) {
            Log.e("AuthTokenValidityUtil", "Error checking token validity", e)
            return false
        }
    }

}
