package mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment

import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.VentSettings
import gov.afrl.batdok.encounter.vitals.EtCO2
import gov.afrl.batdok.encounter.vitals.PIP
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.converter.UnitConverter
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.breathRateCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.fio2Code
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getEtco2Code
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getPIPCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.peepCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.tidalCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.ventCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.ventModeCode
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ExtendedCodedElement
import mil.af.afrl.batman.hl7lib.data.models.v231.field.LOINC
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup
import org.openehealth.ipf.modules.hl7.kotlin.from
import org.openehealth.ipf.modules.hl7.kotlin.nrp

fun populateObxFromVent(parent: AbstractMessage, vent: VentSettings,endpoint: Endpoint) : List<OBX> {
    val obxList = mutableListOf<OBX>()
    var idx = 1

    fun addObx(value: String?, codeNameGroup: CodeNameGroup?, unit: String? = null, type: String = "NM") {
        buildBaseOBX(parent, vent.time).apply {
            obx3_ObservationIdentifier.from(ExtendedCodedElement(parent, codeNameGroup ?: return,
                if (endpoint.format == EndpointType.OMDS) "" else LOINC))
            obx2_ValueType.from(type)
            nrp(5).parse(value ?: return)
            obx1_SetIDOBX.from(idx++)
            unit?.let { obx6_Units.from(UnitConverter.batdokDataToCodedElement(parent, it,endpoint)) }
            obxList.add(this)
        }
    }

    addObx(vent.ventilator?.ifEmpty { null }, ventCode(endpoint), type = "TX")
    addObx(vent.mode?.ifEmpty { null }, ventModeCode(endpoint), type = "TX")
    addObx(vent.rate?.toString(), breathRateCode(endpoint), unit = "br/min")
    addObx(vent.tv?.toString(), tidalCode(endpoint), unit = "mL")
    addObx(vent.peep?.toString(), peepCode(endpoint), unit = "cmH20")
    addObx(vent.fio2?.toString(), fio2Code(endpoint))
    addObx(vent.associatedVital?.get<PIP>()?.pip?.toString(), getPIPCode(endpoint))
    addObx(vent.associatedVital?.get<EtCO2>()?.etco2?.toString(), getEtco2Code(endpoint))

    return obxList
}
