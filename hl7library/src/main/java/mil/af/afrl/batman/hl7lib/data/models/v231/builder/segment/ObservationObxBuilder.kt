package mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment

import gov.afrl.batdok.encounter.Observations
import gov.afrl.batdok.encounter.observation.Observation


import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.observation.CommonObservations
import gov.afrl.batdok.encounter.observation.ObservationData
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.obx033NoteCodingScheme
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.transferNoteCode
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ExtendedCodedElement
import mil.af.afrl.batman.hl7lib.util.EscapeUtil
import org.openehealth.ipf.modules.hl7.kotlin.from
import org.openehealth.ipf.modules.hl7.kotlin.nrp


fun populateObxFromObservation(
        parent: AbstractMessage,
        observation: Observation,
        endpoint: Endpoint,
    ) : List<OBX> {

    val obxList = mutableListOf<OBX>()
    var idx = 1

    fun addObx(value: String?, type: String = "TX") {
        buildBaseOBX(parent, observation.timestamp).apply {
            obx3_ObservationIdentifier.from(ExtendedCodedElement(parent, transferNoteCode(endpoint), obx033NoteCodingScheme(endpoint)))
            obx2_ValueType.from(type)
            nrp(5).parse(EscapeUtil.escape(value ?: return))
            obx1_SetIDOBX.from(idx++)
            obx11_ObservationResultStatus.parse("F")
            obxList.add(this)
        }
    }

    val observationStr = StringBuilder()

    fun obsvNameStr(observation: Observation) {
        if (observation.name == CommonObservations.CHEST_EQUAL_RISE_FALL.dataString) {
            observationStr.append("Chest Equal Rise And Fall")
        } else {
            observationStr.append("${observation.name}")
        }
    }

    fun obsvDescriptionStr(observation: Observation) {
        if (observation.observationData?.toEventText() != null) {
            observationStr.append("\nDescription: ${observation.observationData?.toEventText()}")
        }
    }

    fun createObsvStr(observation: Observation) {
        obsvNameStr(observation)
        obsvDescriptionStr(observation)
    }
    createObsvStr(observation)
    observationStr.lines().forEach { addObx(it) }
    return obxList
}
