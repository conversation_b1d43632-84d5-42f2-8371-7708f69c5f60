package mil.af.afrl.batman.hl7lib.data.models.v231.codetable

import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup

fun ventCode(endpoint: Endpoint) =
    when (endpoint.format){
        EndpointType.OMDS -> CodeNameGroup("3623806", "Ventilator Model" )
        EndpointType.MHSG_T -> CodeNameGroup("20125-1", "Ventilator type" )
        EndpointType.TAC -> CodeNameGroup("TAC", "TODO")
    }

val ventilatorInboundCodes = listOf("20125-1", "3623806")


fun ventModeCode(endpoint: Endpoint) =
    when (endpoint.format){
        EndpointType.OMDS -> CodeNameGroup("2796635", "Ventilator Mode" )
        EndpointType.MHSG_T -> CodeNameGroup("20124-4", "Ventilation mode Ventilator" )
        EndpointType.TAC -> CodeNameGroup("TAC", "TODO")
    }

val modeInboundCodes = listOf("20124-4", "2796635")

fun breathRateCode(endpoint: Endpoint) =
    when (endpoint.format){
        EndpointType.OMDS -> null // CodeNameGroup("19834-1", "Breath rate setting Ventilator" )
        EndpointType.MHSG_T -> CodeNameGroup("19834-1", "Breath rate setting Ventilator" )
        EndpointType.TAC -> CodeNameGroup("TAC", "TODO")
    }

val rateInboundCodes = listOf("19834-1")

fun tidalCode(endpoint: Endpoint) =
    when (endpoint.format){
        EndpointType.OMDS -> CodeNameGroup("2796645", "Tidal Volume, Delivered" )
        EndpointType.MHSG_T -> CodeNameGroup("20112-9", "Tidal volume setting Ventilator" )
        EndpointType.TAC -> CodeNameGroup("TAC", "TODO")
    }

val tidalInboundCodes = listOf("20112-9", "2796645")

fun peepCode(endpoint: Endpoint) =
    when (endpoint.format){
        EndpointType.OMDS -> CodeNameGroup("2796666", "Positive End Expiratory Pressure" )
        EndpointType.MHSG_T -> CodeNameGroup("20077-4", "Positive end expiratory pressure setting Ventilator" )
        EndpointType.TAC -> CodeNameGroup("TAC", "TODO")
    }

val peepInboundCodes = listOf("20077-4", "2796666")

fun fio2Code(endpoint: Endpoint) =
    when (endpoint.format){
        EndpointType.OMDS -> CodeNameGroup("2700657", "FIO2")
        EndpointType.MHSG_T -> CodeNameGroup("19996-8", "Oxygen/Inspired gas Respiratory system --on ventilator" )
        EndpointType.TAC -> CodeNameGroup("TAC", "TODO")
    }

val fio2InboundCodes = listOf("19996-8", "2700657")