package mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment

import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.v231.segment.DG1
import mil.af.afrl.batman.hl7lib.util.HL7Data

/**
 * DG1 is a repeatable segment for diagnoses. This is populated using patient injuries and MOIs
 */
class DG1Builder: SegmentBuilder<DG1> {

    override fun populateFromBatdokPatient(parent: AbstractMessage, hl7Data: HL7Data): DG1 {
        return DG1(parent,parent.modelClassFactory )
    }
}
