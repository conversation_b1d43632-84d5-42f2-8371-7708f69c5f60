package mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure

import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup

fun pelvicBinderCode(endpointType: EndpointType) = when (endpointType) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("767693007", "Pelvic binder (physical object)")
    else -> null
}

fun pelvicBinderTypeCode(endpointType: EndpointType) = when (endpointType) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("410657003", "Type (attribute)")
    else -> null
}
