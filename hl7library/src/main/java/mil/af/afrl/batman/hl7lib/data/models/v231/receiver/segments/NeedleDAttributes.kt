package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments

import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.treatment.NeedleDData
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.needleDSideFromCode

fun needleDAttributes(attributes: List<OBX>) : NeedleDData {
    var side : String? = null

    for (code in attributes.map { it.obx3_ObservationIdentifier.ce1_Identifier.value }) {
        val decodedSide = needleDSideFromCode(code)
        decodedSide?.let { side = it }
    }
    return NeedleDData(location = side)
}