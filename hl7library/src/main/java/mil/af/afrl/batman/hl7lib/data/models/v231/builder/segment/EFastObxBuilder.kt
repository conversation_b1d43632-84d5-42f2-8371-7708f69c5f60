package mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment

import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.observation.EFastExamData
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.efastResultCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.interpretationCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.leftLungSlidingCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.leftUpperQuadrantCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.pericardialFluidCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.rightLungSlidingCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.rightUpperQuadrantCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.suprapubicFluidCode
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ExtendedCodedElement
import mil.af.afrl.batman.hl7lib.data.models.v231.field.SNOMED_CT
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup
import org.openehealth.ipf.modules.hl7.kotlin.from
import org.openehealth.ipf.modules.hl7.kotlin.nrp
import java.time.Instant

fun buildEFastObxList(parent: AbstractMessage, efast: EFastExamData, timestamp: Instant, endpoint: Endpoint) : List<OBX> {
    val obxList = mutableListOf<OBX>()

    var idx = 1
    fun addObx(value: String?, codeNameGroup: CodeNameGroup?, codeSystem: String = SNOMED_CT) {
        fun buildEFastObxItem(value: String?, codeNameGroup: CodeNameGroup?) {
            if (value != null && codeNameGroup != null) {
                buildBaseOBX(parent, timestamp).apply {
                    obx1_SetIDOBX.from(idx++)
                    this.valueType.parse("TX")
                    observationIdentifier.from(ExtendedCodedElement(parent, codeNameGroup, codeSystem))
                    obx4_ObservationSubID.from(idx / 2)
                    nrp(5).parse(value)
                    obxList.add(this)
                }
            }
        }

        // Need a code for each result type, followed by an OBX for the result itself,
        //  EXCEPT for the interpretation, which just gets one OBX (dumb CDP format...)
        buildEFastObxItem(value, codeNameGroup)
        if (codeNameGroup?.code != interpretationCode(endpoint.format)?.code) {
            buildEFastObxItem(value, efastResultCode(value))
        }
    }

    if (endpoint.format == EndpointType.TAC) {
        addObx(efast.rightLungSliding, rightLungSlidingCode(endpoint.format))
        addObx(efast.leftLungSliding, leftLungSlidingCode(endpoint.format))
        addObx(efast.pericardialFluid, pericardialFluidCode(endpoint.format))
        addObx(efast.rightUpperQuadrant, rightUpperQuadrantCode(endpoint.format))
        addObx(efast.leftUpperQuadrant, leftUpperQuadrantCode(endpoint.format))
        addObx(efast.suprapubicFluid, suprapubicFluidCode(endpoint.format))
    }
    addObx(efast.interpretation, interpretationCode(endpoint.format), "")

    return obxList
}
