package mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure

import gov.afrl.batdok.encounter.treatment.ChestTubeData
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup

fun chestTubeCode(endpoint: EndpointType) = when (endpoint) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("446847002", "Drainage of pleural cavity via chest tube (procedure)")
    else -> null
}

fun cdpChestTubeSideCode(side: String) = when (side) {
    ChestTubeData.Location.LEFT.dataString -> CodeNameGroup("7771000", "Left (qualifier value)")
    ChestTubeData.Location.RIGHT.dataString -> CodeNameGroup("24028007", "Right (qualifier value)")
    else -> null
}

fun cdpAnteriorAxillaryCode() = CodeNameGroup("91199000", "Anterior axillary line structure (body structure)")

fun cdpChestTubeSideFromCode(code: String) = when (code){
    "7771000" -> ChestTubeData.Location.LEFT.dataString
    "24028007" -> ChestTubeData.Location.RIGHT.dataString
    else -> null
}
