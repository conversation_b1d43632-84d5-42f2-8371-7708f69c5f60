package mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.orur01

import ca.uhn.hl7v2.model.v231.message.ORU_R01
import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.Event
import gov.afrl.batdok.encounter.orders.IOrderLine
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.populateObxFromEvent
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.noteObr24DiagnosticServiceSectionId
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.obx033NoteCodingScheme
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.transferNoteCode
import mil.af.afrl.batman.hl7lib.util.HL7Data
import java.time.Instant

data class EventWithReferenceOrder(val event: Event, val order: IOrderLine)

class OrderOruBuilder(
    hl7Data: HL7Data,
    defaultTimestamp: Instant
) : BaseRepeatableORU_R01Builder<EventWithReferenceOrder>(hl7Data, defaultTimestamp) {

    override fun buildOrus(): List<ORU_R01> {
        val orders = hl7Data.document!!.orders.list
        val adminAndNoteArr = orders.flatMap { order ->
            order.getOrderHistory(hl7Data.document!!).map { EventWithReferenceOrder(it, order) }
        }
        val adminAndNoteORUs = adminAndNoteArr.mapNotNull {
            buildRepeatableDataOru(
                hl7Data,
                it,
                "BATDOK Order Administration and Notation"
            )
        }
        return adminAndNoteORUs.apply {
            map {
                it.getObservationGroup().obr.obr24_DiagnosticServSectID.parse(
                    noteObr24DiagnosticServiceSectionId(endpoint)
                )
            }
        }
    }

    override fun getObrDetails(item: EventWithReferenceOrder) = ItemData(
        item.event.timestamp,
        transferNoteCode(endpoint),
        item.event.referencedItem,
        obx033NoteCodingScheme(endpoint)
    )

    override fun buildObservationList(msg: ORU_R01, item: EventWithReferenceOrder): List<OBX> {
        return populateObxFromEvent(msg, item.event, endpoint, item.order)
    }

}
