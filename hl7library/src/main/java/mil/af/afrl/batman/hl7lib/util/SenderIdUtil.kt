package mil.af.afrl.batman.hl7lib.util

import android.annotation.SuppressLint
import android.content.Context
import android.provider.Settings

fun buildSenderId(context: Context, batdokId: String?) : String? {
    // BATDOK ID should only be null in tests
    batdokId ?: return null
    // Receiving systems would like to identify the BATDOK installation, not just the device sending
    //  the message. Since this is just a five character alphabetic string, there is a relatively
    //  high chance of collisions. Combine with the device ID to reduce that chance.
    @SuppressLint("HardwareIds")
    val deviceID = Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
    return deviceID + batdokId
}
