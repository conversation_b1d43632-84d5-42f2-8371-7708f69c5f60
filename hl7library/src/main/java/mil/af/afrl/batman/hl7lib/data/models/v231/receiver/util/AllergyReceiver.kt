package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.util

import ca.uhn.hl7v2.model.v231.segment.AL1
import gov.afrl.batdok.commands.proto.InfoCommands.AddRemoveAllergyCommand
import gov.afrl.batdok.encounter.commands.buildAddRemoveAllergyListCommand
import kotlinx.coroutines.runBlocking
import mil.af.afrl.batman.hl7lib.converter.DBAllergyConverter

fun parseAllergyCommand(al1Segments: List<AL1>) : AddRemoveAllergyCommand {
    val allergyList = mutableListOf<String>()

    for (al1 in al1Segments) {
        // Sometimes we get an NKA item along with other allergies. Ignore it if so.
        val allergyCode = al1.al13_AllergyCodeMnemonicDescription.ce1_Identifier.value
        if (allergyCode == "NKA" &&
            al1Segments.any { it.al13_AllergyCodeMnemonicDescription.ce1_Identifier.value != "NKA" }) {
            // If any other allergy exists whose code is not NKA, we must have real allergies in the list
            // Ignore the NKA item
            continue
        }
        // We really don't need to look up inbound codes from the DB. If the sender is using the
        //  Multum dataset then this should always just match the description already. If an
        //  allergy is from outside the set, it won't matter because we'll just want the
        //  description to display anyway.
        val rawAllergyName = al1.al13_AllergyCodeMnemonicDescription.ce2_Text.value
        val batdokAllergy = runBlocking { DBAllergyConverter.nameToBatdokData(rawAllergyName) }
        allergyList.add(batdokAllergy ?: rawAllergyName)
    }

    return buildAddRemoveAllergyListCommand(addedAllergies = allergyList)
}
