package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.util

import gov.afrl.batdok.encounter.DrawingPoint
import gov.afrl.batdok.encounter.Injury
import gov.afrl.batdok.encounter.commands.buildAddPointWithLabelCommand
import gov.afrl.batdok.encounter.commands.buildChangeInjuryCommand
import gov.afrl.batdok.encounter.commands.buildChangeMoiCommand
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.converter.LocationInjuryConverter
import mil.af.afrl.batman.hl7lib.converter.MoiConverter
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.Coordinates
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.avatarMap
import mil.af.afrl.batman.hl7lib.data.models.v231.group.DG1_ZDG
import org.openehealth.ipf.modules.hl7.kotlin.value
import java.util.Locale

fun parseInjuryInfo(dg1Zdgs: List<DG1_ZDG>, endpointType: EndpointType) : List<com.google.protobuf.Message> {
    data class InjuriesData(val type:String, val injury: String, val abbrev: String? ,val location: String?)
    val dg1ZdgCommands = mutableListOf<com.google.protobuf.Message>()
    val injurySet = mutableSetOf<InjuriesData>()

    for (item in dg1Zdgs) {
        val diagnosisData = item.dg1.dg13_DiagnosisCodeDG1

        // If we have a known MOI, none of the ZDG info matters, so short-circuit here
        val moi = MoiConverter.codeToBatdokData(diagnosisData.ce1_Identifier.value, Endpoint(endpointType, ""))
        if (moi != null) {
            // Some codes are overloaded for multiple items, so if we have BATDOK hints, use that as the name
            val name = item.dg1.dg14_DiagnosisDescription?.value?.ifEmpty { null } ?: moi
            if (!name.endsWith(" (Location Unknown)")){
                dg1ZdgCommands.add(buildChangeMoiCommand(name, true, null))
                continue
            }
        }

        var dg1TypeAndLocation = LocationInjuryConverter.codeToBatdokData(diagnosisData.ce1_Identifier.value ?: "", endpointType)
            ?: Pair(diagnosisData.ce2_Text.value, null)
        var abbreviation: String

        // Get extra BATDOK hint data if available
        if (!item.dg1.dg14_DiagnosisDescription?.value.isNullOrEmpty()) {
            val dg14Split = item.dg1.dg14_DiagnosisDescription.value.split(" (")
            if (dg14Split.size > 1){
                val dg14Injury = dg14Split[0]
                val dg14Location = dg14Split[1]
                val location = dg14Location.dropLast(1).lowercase(Locale.getDefault())
                val locationCoordinates = getAvatarCoordinates(location)
                abbreviation = Injury.Type.entries.find { it.dataString == dg14Injury }?.abbrev
                    ?: dg14Injury.take(3).uppercase(Locale.getDefault())
                if (locationCoordinates != null) {
                    dg1ZdgCommands.add(
                        buildAddPointWithLabelCommand(
                            DrawingPoint(label = abbreviation, x = (locationCoordinates.x).toFloat(), y = (locationCoordinates.y).toFloat())
                        ))
                }
                dg1TypeAndLocation = Pair(dg14Injury,location.replace(" ","_").uppercase(Locale.getDefault()))
            }
            if (dg14Split.size == 1){
                //dg1.4 with no location but does have injury/moi
                dg1TypeAndLocation = Pair(dg14Split.first(), null)
            }
        }

        abbreviation = Injury.Type.entries.find { it.dataString == dg1TypeAndLocation.first }?.abbrev
            ?: dg1TypeAndLocation.first.take(3).uppercase(Locale.getDefault())
        // Shunt all injuries to the "null" location. Locations are really determined by the Drawing Points.
        injurySet.add(InjuriesData("Injury", dg1TypeAndLocation.first,abbreviation, null))

        if (!item.zdg.zdg1_frontBack.isEmpty && !item.zdg.zdg2_injuryBodyLocation.isEmpty && abbreviation.isNotEmpty()) {
            val injuryDescription = "${item.zdg.zdg1_frontBack.value} ${item.zdg.zdg2_injuryBodyLocation.value}".lowercase(Locale.getDefault())
            val injuryCoordinates = getAvatarCoordinates(injuryDescription)
            if (injuryCoordinates != null) {
                dg1ZdgCommands.add(buildAddPointWithLabelCommand(
                    DrawingPoint(label = abbreviation, x = (injuryCoordinates.x).toFloat(), y = (injuryCoordinates.y).toFloat())
                ))
            }
        }
    }

    for (injury in injurySet){
        dg1ZdgCommands.add(buildChangeInjuryCommand(injury.injury, true, injury.abbrev.toString(), injury.location))
    }

    return dg1ZdgCommands
}

fun getAvatarCoordinates(injuryDescription: String): Coordinates?{
    return avatarMap[injuryDescription]
}
