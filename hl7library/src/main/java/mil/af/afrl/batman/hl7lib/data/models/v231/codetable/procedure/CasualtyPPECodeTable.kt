package mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure

import gov.afrl.batdok.encounter.observation.CasualtyPPEData
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup

fun casualtyPPECode(endpoint: EndpointType) = when (endpoint) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("449399005","Application of personal protective equipment (procedure)")
    else -> null
}

fun casualtyPPETypeCode(type: String) = when (type) {
    CasualtyPPEData.Type.HELMET_BALLISTIC.dataString -> CodeNameGroup("285695004","Helmet (physical object)")
    CasualtyPPEData.Type.TACTICAL_VEST_IOTV.dataString -> CodeNameGroup("58878002","Protective vest, device (physical object)")
    CasualtyPPEData.Type.EYE_PROTECTION.dataString -> CodeNameGroup("38126007","Protective glasses, device (physical object)")
    CasualtyPPEData.Type.EAR_PROTECTION.dataString -> CodeNameGroup("42380001","Ear plug, device (physical object)")
    CasualtyPPEData.Type.PLATE_FRONT.dataString -> CodeNameGroup("48473008F","Protective body armor, device (physical object)")
    CasualtyPPEData.Type.PLATE_BACK.dataString -> CodeNameGroup("48473008B","Protective body armor, device (physical object)")
    CasualtyPPEData.Type.PLATE_LEFT.dataString -> CodeNameGroup("48473008L","Protective body armor, device (physical object)")
    CasualtyPPEData.Type.PLATE_RIGHT.dataString -> CodeNameGroup("48473008R","Protective body armor, device (physical object)")
    CasualtyPPEData.Type.NECK_PROTECTOR_BACK.dataString -> CodeNameGroup("NPB","Neck Protector - Back")
    CasualtyPPEData.Type.THROAT_PROTECTOR_FRONT.dataString -> CodeNameGroup("TPF","Throat Protector - Front")
    CasualtyPPEData.Type.DELTOID_RIGHT.dataString -> CodeNameGroup("DR","Deltoid - Right")
    CasualtyPPEData.Type.DELTOID_LEFT.dataString -> CodeNameGroup("DL","Deltoid - Left")
    CasualtyPPEData.Type.GROIN_SHIELD.dataString -> CodeNameGroup("GS","Groin Shield")
    CasualtyPPEData.Type.PELVIC_UNDERGARMENT_1.dataString -> CodeNameGroup("PU1","Pelvic Undergarment - Tier 1")
    CasualtyPPEData.Type.PELVIC_UNDERGARMENT_2.dataString -> CodeNameGroup("PU2","Pelvic undergarment - Tier 2")
    CasualtyPPEData.Type.BLAST_GAUGE.dataString -> CodeNameGroup("BG","Blast Gauge")
    CasualtyPPEData.Type.BLAST_SENSOR_OTHER.dataString -> CodeNameGroup("BSO","Blast Sensor - Other")
    CasualtyPPEData.Type.BLAST_SENSOR_HELMET.dataString -> CodeNameGroup("BSH","Blast Sensor - Helmet")
    else -> null
}

fun casualtyPPETypeFromCode(code: String) = when(code){
    "285695004" -> CasualtyPPEData.Type.HELMET_BALLISTIC.dataString
    "58878002" -> CasualtyPPEData.Type.TACTICAL_VEST_IOTV.dataString
    "38126007" -> CasualtyPPEData.Type.EYE_PROTECTION.dataString
    "42380001" -> CasualtyPPEData.Type.EAR_PROTECTION.dataString
    "48473008F" -> CasualtyPPEData.Type.PLATE_FRONT.dataString
    "48473008B" -> CasualtyPPEData.Type.PLATE_BACK.dataString
    "48473008L" -> CasualtyPPEData.Type.PLATE_LEFT.dataString
    "48473008R" -> CasualtyPPEData.Type.PLATE_RIGHT.dataString
    "NPB" -> CasualtyPPEData.Type.NECK_PROTECTOR_BACK.dataString
    "TPF" -> CasualtyPPEData.Type.THROAT_PROTECTOR_FRONT.dataString
    "DR" -> CasualtyPPEData.Type.DELTOID_RIGHT.dataString
    "DL" -> CasualtyPPEData.Type.DELTOID_LEFT.dataString
    "GS" -> CasualtyPPEData.Type.GROIN_SHIELD.dataString
    "PU1" -> CasualtyPPEData.Type.PELVIC_UNDERGARMENT_1.dataString
    "PU2" -> CasualtyPPEData.Type.PELVIC_UNDERGARMENT_2.dataString
    "BG" -> CasualtyPPEData.Type.BLAST_GAUGE.dataString
    "BSO" -> CasualtyPPEData.Type.BLAST_SENSOR_OTHER.dataString
    "BSH" -> CasualtyPPEData.Type.BLAST_SENSOR_HELMET.dataString
    else -> null
}