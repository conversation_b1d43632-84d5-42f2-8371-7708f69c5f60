package mil.af.afrl.batman.hl7lib.converter

import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.data.models.v231.field.DCW

// This will mainly be used for reverse lookups from MHSG-T, since we
//  don't have different types of clinical notes to try to encode
object ClinicalNoteConverter : BatdokDataConverter<String>() {
    override fun String.toName() = this
    override fun buildType(name: String) = name
    override fun codeTypeForEndpoint(endpoint: Endpoint) = DCW
}
