package mil.af.afrl.batman.hl7lib.data.models.v231.codetable

fun pid045IdentifierTypeCode() = "BATDOK"
fun pid08SexCode(patient: String?) =
    when (patient) {
        "Male" -> genderMale()
        "Female" -> genderFemale()
        else -> ""

    }

private fun genderMale() = "M"
private fun genderFemale() = "F"

val oidEDIPI = "2.16.840.1.113883.3.42.10001.100001.12"
val oidIPI = "2.16.840.1.113883.3.42.10001.100001.228"
val oidBATDOK = "2.16.840.1.113883.3.42.10033.100001.13"
val oidTAC = "2.16.840.1.113883.3.42.10032.100001.13"