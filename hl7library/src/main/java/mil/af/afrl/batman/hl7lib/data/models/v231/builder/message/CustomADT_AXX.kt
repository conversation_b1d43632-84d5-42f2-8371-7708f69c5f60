package mil.af.afrl.batman.hl7lib.data.models.v231.builder.message

import ca.uhn.hl7v2.HL7Exception
import ca.uhn.hl7v2.model.v231.message.ADT_AXX
import ca.uhn.hl7v2.parser.ModelClassFactory
import mil.af.afrl.batman.hl7lib.data.models.v231.group.DG1_ZDG

class CustomADT_AXX(factory: ModelClassFactory?) : ADT_AXX(factory) {
    init {
        try {
            this.add(DG1_ZDG::class.java,false,true,11)
        }catch (e: HL7Exception) {
            throw RuntimeException("Unable to create CustomADT_AXX message", e)
        }
    }

    @Throws(HL7Exception::class)
    fun insertDG1ZDG(structure: DG1_ZDG?, rep: Int) {
        super.insertRepetition("DG1_ZDG", structure, rep)
    }
}