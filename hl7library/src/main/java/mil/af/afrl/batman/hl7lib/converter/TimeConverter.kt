package mil.af.afrl.batman.hl7lib.converter

import ca.uhn.hl7v2.model.primitive.CommonTS
import java.time.Instant
import java.util.GregorianCalendar
import java.util.TimeZone

object TimeConverter {

    fun timestampToFormattedDateTime(timestamp: Instant? = Instant.now()): String {
        if (timestamp == null) {
            return ""
        }
        val calendar = GregorianCalendar(TimeZone.getTimeZone("UTC"))
        calendar.timeInMillis = timestamp.toEpochMilli()
        // We need to remove the subseconds (after the ., before the +) from the standard timestamp
        return CommonTS.toHl7TSFormat(calendar).replace("\\.\\d+\\+".toRegex(), "+")
    }

    fun timestampToFormattedDate(timestamp: Instant = Instant.now()): String {
        val dateTimeString = timestampToFormattedDateTime(timestamp)
        // Only the first 8 characters are the date (yyyyMMdd), the rest are time values
        return dateTimeString.substring(0, 8)
    }
}
