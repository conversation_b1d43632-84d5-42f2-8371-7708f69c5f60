package mil.af.afrl.batman.hl7lib.data.models.v231.customSegments

import ca.uhn.hl7v2.model.AbstractSegment
import ca.uhn.hl7v2.model.Group
import ca.uhn.hl7v2.model.Type
import ca.uhn.hl7v2.model.v231.datatype.ST
import ca.uhn.hl7v2.parser.ModelClassFactory
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.SegmentBuilder

// Simple class used to add repeatedly called functions that can't be defined as extensions
abstract class ExtendedAbstractSegment(parent: Group?, factory: ModelClassFactory? = null) : AbstractSegment(parent, factory),
                                                                                             SegmentBuilder<ExtendedAbstractSegment> {

    protected fun addSingleField(required: Boolean, length: Int, name: String, type: Class<out Type> = ST::class.java) {
        add(type, required, 1, length, arrayOf(message), name)
    }
    protected fun addSingleField(length: Int, name: String, type: Class<out Type> = ST::class.java) =
        addSingleField(false, length, name, type)
    protected fun addBlankFields(num: Int) {
        for (i in 1..num) {
            addSingleField(false, 1, "")
        }
    }

    override fun createNewTypeWithoutReflection(field: Int): Type? {
        return null
    }

}
