package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments

import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.treatment.WoundPackingData
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.cdpDressingTypeFromCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.dressingLocationCode
import org.openehealth.ipf.modules.hl7.kotlin.value

fun woundPackingDataAttributes(attributes: List<OBX>, endpointType: EndpointType) : WoundPackingData {
    var location: String? = null
    var type: String? = null
    for (attr in attributes) {
        val code = attr.obx3_ObservationIdentifier.ce1_Identifier?.value ?: continue
        val value = attr.obx5_ObservationValue.firstOrNull()?.value ?: continue
        if (code == dressingLocationCode(endpointType)?.code && value.isNotEmpty()) {
            location = value
            continue
        }

        type = cdpDressingTypeFromCode(code)
    }

    return WoundPackingData(location = location, type = type)
}
