package mil.af.afrl.batman.hl7lib.converter

import gov.afrl.batdok.encounter.Document
import gov.afrl.batdok.encounter.Grade

// See https://docs.google.com/spreadsheets/d/1QJLL03tnpke1HJ1Kay5_DpkkMICYunrD
fun getRankFromGradeAndService(document: Document) : String? {
    val rank = if (!document.info.grade.isNullOrEmpty()) {
        gradeCodeFromString(document.info.grade!!) ?: return null
    } else if (!document.info.rank.isNullOrEmpty()) {
        document.info.rank!!
    } else return null

    val service = if (document.info.patcat?.service != null) {
        document.info.patcat?.service?.abbreviation!!
    } else if (!document.info.service.isNullOrEmpty()) {
        document.info.service!!
    } else return null

    if (rank.startsWith("E", true)) {
        return when (service) {
            "USA" -> convertArmyEnlisted(rank)
            "USMC" -> convertMarinesEnlisted(rank)
            "USAF" -> convertAirForceEnlisted(rank)
            "USN" -> convertNavyEnlisted(rank)
            "USCG" -> convertNavyEnlisted(rank)
            else -> rank
        }
    } else if (rank.startsWith("O", true)) {
        return when (service) {
            "USA" -> convertArmyOfficer(rank)
            "USMC" -> convertAfMarinesOfficer(rank)
            "USAF" -> convertAfMarinesOfficer(rank)
            "USN" -> convertNavyCgOfficer(rank)
            "USCG" -> convertNavyCgOfficer(rank)
            else -> rank
        }
    } else if (rank.startsWith("W", true)) {
        return when (service) {
            "USA" -> convertArmyWarrantOfficer(rank)
            "USN" -> convertNavyCoastGuardWarrantOfficer(rank)
            "USCG" -> convertNavyCoastGuardWarrantOfficer(rank)
            "USMC" -> convertMarinesWarrantOfficer(rank)
            "USAF" -> convertAirForceWarrantOfficer(rank)
            else -> rank
        }
    }

    return rank
}

//region Enlisted
private fun convertArmyEnlisted(rank: String) : String {
    return when {
        rank.endsWith("1") -> rank
        rank.endsWith("2") -> "PV2"
        rank.endsWith("3") -> "PFC"
        rank.endsWith("4") -> rank
        rank.endsWith("5") -> rank
        rank.endsWith("6") -> rank
        rank.endsWith("7") -> rank
        rank.endsWith("8") -> rank
        rank.endsWith("9") -> rank
        else -> rank
    }
}

private fun convertMarinesEnlisted(rank: String) : String {
    return when {
        rank.endsWith("1") -> rank
        rank.endsWith("2") -> "PFC"
        rank.endsWith("3") -> "LCPL"
        rank.endsWith("4") -> "CPL"
        rank.endsWith("5") -> "SGT"
        rank.endsWith("6") -> "SSGT"
        rank.endsWith("7") -> "GYSGT"
        rank.endsWith("8") -> rank
        rank.endsWith("9") -> rank
        else -> rank
    }
}

private fun convertAirForceEnlisted(rank: String) : String {
    return when {
        rank.endsWith("1") -> rank
        rank.endsWith("2") -> "AMN"
        rank.endsWith("3") -> "A1C"
        rank.endsWith("4") -> rank
        rank.endsWith("5") -> "SSGT"
        rank.endsWith("6") -> "TSGT"
        rank.endsWith("7") -> "MSGT"
        rank.endsWith("8") -> "SMSGT"
        rank.endsWith("9") -> rank
        else -> rank
    }
}

private fun convertNavyEnlisted(rank: String) : String {
    return when {
        rank.endsWith("1") -> rank
        rank.endsWith("2") -> "SA"
        rank.endsWith("3") -> "SN"
        rank.endsWith("4") -> "PO3"
        rank.endsWith("5") -> "PO2"
        rank.endsWith("6") -> "PO1"
        rank.endsWith("7") -> "CPO"
        rank.endsWith("8") -> "SCPO"
        rank.endsWith("9") -> rank
        else -> rank
    }
}
//endregion

//region Officer
private fun convertArmyOfficer(rank: String) : String {
    return when {
        rank.endsWith("1") -> "2LT"
        rank.endsWith("2") -> "1LT"
        rank.endsWith("3") -> "CPT"
        rank.endsWith("4") -> "MAJ"
        rank.endsWith("5") -> "LTC"
        rank.endsWith("6") -> "COL"
        rank.endsWith("7") -> "BG"
        rank.endsWith("8") -> "MG"
        rank.endsWith("9") -> "LTG"
        rank.endsWith("10") -> "GEN"
        else -> rank
    }
}

private fun convertAfMarinesOfficer(rank: String) : String {
    return when {
        rank.endsWith("1") -> "2NDLT"
        rank.endsWith("2") -> "1STLT"
        rank.endsWith("3") -> "CAPT"
        rank.endsWith("4") -> "MAJ"
        rank.endsWith("5") -> "LTCOL"
        rank.endsWith("6") -> "COL"
        rank.endsWith("7") -> "BGEN"
        rank.endsWith("8") -> "MAJGEN"
        rank.endsWith("9") -> "LTGEN"
        rank.endsWith("10") -> "GEN"
        else -> rank
    }
}

private fun convertNavyCgOfficer(rank: String) : String {
    return when {
        rank.endsWith("1") -> "ENS"
        rank.endsWith("2") -> "LTJG"
        rank.endsWith("3") -> "LT"
        rank.endsWith("4") -> "LCDR"
        rank.endsWith("5") -> "CDR"
        rank.endsWith("6") -> "CAPT"
        rank.endsWith("7") -> "RADM"
        rank.endsWith("8") -> "RADM"
        rank.endsWith("9") -> "VADM"
        rank.endsWith("10") -> "ADM"
        else -> rank
    }
}
//endregion

//region Warrant Officer
private fun convertArmyWarrantOfficer(rank: String) : String {
    return when {
        rank.endsWith("1") -> "WO1"
        rank.endsWith("2") -> "CW2"
        rank.endsWith("3") -> "CW3"
        rank.endsWith("4") -> "CW4"
        rank.endsWith("5") -> "CW5"
        else -> rank
    }
}

private fun convertNavyCoastGuardWarrantOfficer(rank: String) : String {
    return when {
        rank.endsWith("1") -> "WO-1"
        rank.endsWith("2") -> "CWO-2"
        rank.endsWith("3") -> "CWO-3"
        rank.endsWith("4") -> "CWO-4"
        rank.endsWith("5") -> "CWO-5"
        else -> rank
    }
}

private fun convertMarinesWarrantOfficer(rank: String) : String {
    return when {
        rank.endsWith("1") -> "WO"
        rank.endsWith("2") -> "CWO2"
        rank.endsWith("3") -> "CWO3"
        rank.endsWith("4") -> "CWO4"
        rank.endsWith("5") -> "CWO5"
        else -> rank
    }
}

// I don't think AF has warrant officers, but this is on the sheet...
private fun convertAirForceWarrantOfficer(rank: String) : String {
    return when {
        rank.endsWith("1") -> "WO"
        rank.endsWith("2") -> "CWO-2"
        rank.endsWith("3") -> "CWO-3"
        rank.endsWith("4") -> "CWO-4"
        rank.endsWith("5") -> "CWO-5"
        else -> rank
    }
}
//endregion

fun gradeCodeFromString(gradeString: String) : String {
    // BATDOK has a really nice grade enum, but for some reason, we choose
    //  to only store its dataString value...
    return Grade.entries.find { it.dataString == gradeString }?.shortString ?: gradeString
}
