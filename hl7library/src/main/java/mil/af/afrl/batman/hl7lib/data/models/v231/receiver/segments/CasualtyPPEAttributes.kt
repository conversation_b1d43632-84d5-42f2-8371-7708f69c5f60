package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments

import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.observation.CasualtyPPEData
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.casualtyPPETypeFromCode

fun casualtyPPEAttributes(attributes: List<OBX>) : CasualtyPPEData{
    val ppeList = mutableListOf<String>()
    
    for (code in attributes.map { it.obx3_ObservationIdentifier.ce1_Identifier.value }){
        ppeList += casualtyPPETypeFromCode(code)!!
    }
    
    return CasualtyPPEData(ppeList)
}