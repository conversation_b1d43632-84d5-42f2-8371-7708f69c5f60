package mil.af.afrl.batman.hl7lib

import android.content.Context
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.security.KeyStore
import java.security.KeyStoreException
import java.security.NoSuchAlgorithmException
import java.security.cert.CertificateException
import javax.net.ssl.KeyManager
import javax.net.ssl.KeyManagerFactory

object KeyProvider {

    private var keystore: KeyStore = KeyStore.getInstance("PKCS12")
    private var keyManagerFactory = KeyManagerFactory.getInstance("X509")
    val keyManagers: Array<KeyManager>
        get() = keyManagerFactory.keyManagers
    private const val DEFAULT_CERT_NAME = "cert.p12"
    private const val OLD_CERT_NAME = "cert.p12.old"

    fun isInitialized() : <PERSON><PERSON><PERSON> {
        // No great way to tell if this is initialized from what I can see, so do this
        return try {
            keystore.size()
            true
        } catch (e: KeyStoreException) {
            false
        }
    }

    fun hasDefaultPkcs12Cert(context: Context) = File(context.filesDir, DEFAULT_CERT_NAME).exists()

    @Throws(CertificateException::class, IOException::class, NoSuchAlgorithmException::class)
    fun loadDefaultPkcs12Cert(context: Context, password: String) {
        val certFile = File(context.filesDir, DEFAULT_CERT_NAME)
        if (certFile.exists()) {
            reset()
            keystore.load(FileInputStream(certFile), password.toCharArray())
            keyManagerFactory.init(keystore, password.toCharArray())
            // When a key is loaded, export any queued messages
            MessageExporter.exportAllQueuedPatients(context)
        }
    }

    @Throws(CertificateException::class, IOException::class, NoSuchAlgorithmException::class)
    fun loadNewPkcs12Cert(context: Context, certStream: InputStream, password: String) {
        // Need to re-build the object to clear out the old keys
        reset()

        // If an existing cert exists, move it to the OLD_CERT file so we can recover it if needed
        val certFile = File(context.filesDir, DEFAULT_CERT_NAME)
        if (certFile.exists()) {
            certFile.copyTo(File(context.filesDir, OLD_CERT_NAME), true)
        }

        // Make this cert the new default
        certStream.copyTo(FileOutputStream(certFile))

        // Reload the default cert
        loadDefaultPkcs12Cert(context, password)
    }

    fun revertToLastCert(context: Context) {
        val currentCert = File(context.filesDir, DEFAULT_CERT_NAME)
        currentCert.delete()
        val oldCertFile = File(context.filesDir, OLD_CERT_NAME)
        if (oldCertFile.exists()) {
            oldCertFile.copyTo(currentCert, true)
            oldCertFile.delete()
        }
    }

    fun reset() {
        // Every get should return a new instance
        keystore = KeyStore.getInstance("PKCS12")
        keyManagerFactory = KeyManagerFactory.getInstance("X509")
    }

}
