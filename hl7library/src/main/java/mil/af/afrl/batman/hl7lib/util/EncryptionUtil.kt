package mil.af.afrl.batman.hl7lib.util

import java.nio.ByteBuffer
import java.security.SecureRandom
import javax.crypto.Cipher
import javax.crypto.SecretKey
import javax.crypto.SecretKeyFactory
import javax.crypto.spec.GCMParameterSpec
import javax.crypto.spec.PBEKeySpec
import javax.crypto.spec.SecretKeySpec

object EncryptionUtil {

    fun decryptWithPassword(input: ByteArray, password: String): ByteArray {
        val iv = ByteArray(12)
        val salt = ByteArray(16)
        val encrypted = ByteArray(input.size - iv.size - salt.size)
        System.arraycopy(input, 0, iv, 0, iv.size)
        System.arraycopy(input, iv.size, salt, 0, salt.size)
        System.arraycopy(input, iv.size + salt.size, encrypted, 0, encrypted.size)
        val aesKeyFromPassword = getAESKeyFromPassword(password.toCharArray(), salt)
        val cipher = Cipher.getInstance("AES/GCM/NoPadding")
        cipher.init(Cipher.DECRYPT_MODE, aesKeyFromPassword, GCMParameterSpec(128, iv))
        return cipher.doFinal(encrypted)
    }

    fun encryptHl7(pText: ByteArray, password: String): ByteArray {
        val salt = getRandomNonce(16)
        val iv = getRandomNonce(12)

        val aesKeyFromPassword = getAESKeyFromPassword(password.toCharArray(), salt)
        val cipher = Cipher.getInstance("AES/GCM/NoPadding")
        cipher.init(Cipher.ENCRYPT_MODE, aesKeyFromPassword, GCMParameterSpec(128, iv))
        val cipherText = cipher.doFinal(pText)
        return ByteBuffer.allocate(iv.size + salt.size + cipherText.size)
            .put(iv)
            .put(salt)
            .put(cipherText)
            .array()

    }

    private fun getRandomNonce(numBytes: Int): ByteArray {
        val nonce = ByteArray(numBytes)
        SecureRandom().nextBytes(nonce)
        return nonce
    }

    private fun getAESKeyFromPassword(password: CharArray, salt: ByteArray): SecretKey {
        val factory = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA256")
        val spec = PBEKeySpec(password, salt, 65536, 256)
        return SecretKeySpec(factory.generateSecret(spec).encoded, "AES")
    }
}