package mil.af.afrl.batman.hl7lib.dataexchanger

import android.content.Context
import mil.af.afrl.batman.hl7lib.MessageExporter
import mil.af.afrl.batman.hl7lib.MessageRequester
import mil.af.afrl.batman.hl7lib.util.HL7Data
import mil.af.afrl.batman.hl7lib.util.InboundHL7Data
import mil.af.afrl.batman.hl7lib.util.NetworkResponse
import mil.af.afrl.batman.hl7lib.util.OutboundHL7Data

object MhsgtDataExchanger {

    fun exchangeData(context: Context, hl7Data: HL7Data,
                     onStatusUpdate: (NetworkResponse) -> Unit,
                     onImportComplete: ((HL7Data) -> Unit)? = null) {
        if (hl7Data.exporting) {
            MessageExporter.exportCompletedCallback = {
                onStatusUpdate(it)
                MessageExporter.exportCompletedCallback = null
            }
            MessageExporter.sendPatientMessages(hl7Data as OutboundHL7Data, context)
        } else {
            MessageRequester.requestCompletedCallback = { hl7Data, response ->
                onStatusUpdate(response)
                if (response.isSuccess()) {  // HTTP success
                    onImportComplete?.invoke(hl7Data)
                }
                MessageRequester.requestCompletedCallback = null
            }
            MessageRequester.requestMessages(
                hl7Data = hl7Data as InboundHL7Data,
                context = context
            )
        }
    }
}
