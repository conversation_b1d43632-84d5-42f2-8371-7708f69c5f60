package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments

import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.treatment.DressingData
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.cdpDressingTypeFromCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.dressingLocationCode
import org.openehealth.ipf.modules.hl7.kotlin.value

fun dressingDataAttributes(attributes: List<OBX>, topLevelCode: String, endpointType: EndpointType) : DressingData {
    var type: String? = when(topLevelCode) {
        "118414001" -> DressingData.Type.PRESSURE.dataString
        else -> null
    }
    var location: String? = null

    for (attr in attributes) {
        val code = attr.obx3_ObservationIdentifier.ce1_Identifier?.value ?: continue
        val value = attr.obx5_ObservationValue.firstOrNull()?.value ?: continue
        if (type == null) {
            // Don't want to overwrite the Pressure type if applied already
            type = cdpDressingTypeFromCode(code)
        }

        if (code == dressingLocationCode(endpointType)?.code && value.isNotEmpty()) {
            location = value
            continue
        }
    }

    return DressingData(
        // No code for non-hemostatic dressings, so make that the default
        type = type ?: DressingData.Type.NONHEMOSTATIC.dataString,
        location = location
    )
}