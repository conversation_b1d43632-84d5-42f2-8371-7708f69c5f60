package mil.af.afrl.batman.hl7lib

import java.io.Serializable

enum class EndpointType(val displayName: String) {
    OMDS("OMDS"), MHSG_T("MHSG-T"), TAC("CDP");

    companion object {
        fun fromDisplayName(displayName: String): EndpointType? {
            return values().find { it.displayName == displayName }
        }

        fun fromHL7SystemName(systemName: String): EndpointType? {
            return when (systemName) {
                in listOf("CDP", "TAC") -> TAC
                in listOf("DHMSM_THEATER", "THEATER") -> MHSG_T
                in listOf("DOD_OMDS_THEATER", "MIP", "DHMSM", "DHMSM-ALLERGY", "BATDOK") -> OMDS
                else -> null
            }
        }
    }
}

open class Endpoint(
    val format: EndpointType,
    val displayName: String  // Make these unique!
) : Serializable {
    companion object {
        val TAC = Endpoint(EndpointType.TAC, "CDP")

        val OMDS_DELTA = OmdsNetworkEndpoint(
                "Integration",
                "https://delta.omds-u.test.cce.af.mil/ommr/batdokj",
                "https://delta.omds-u.test.cce.af.mil/query/a28",
                "https://delta.omds-u.test.cce.af.mil/query/pampi",
                "https://delta.omds-u.test.cce.af.mil/query/documents",
                "https://delta.omds-u.test.cce.af.mil/login/realms/baby-yoda/protocol/openid-connect/token",
                "https://delta.omds-u.test.cce.af.mil/login/realms/baby-yoda/protocol/openid-connect/auth/device"
            )

        val OMDS_THETA = OmdsNetworkEndpoint(
                "Test",
                "https://omds-u.test.cce.af.mil/ommr/batdokj",
                "https://omds-u.test.cce.af.mil/query/a28",
                "https://omds-u.test.cce.af.mil/query/pampi",
                "https://omds-u.test.cce.af.mil/query/documents",
                "https://omds-u.test.cce.af.mil/login/realms/baby-yoda/protocol/openid-connect/token",
                "https://omds-u.test.cce.af.mil/login/realms/baby-yoda/protocol/openid-connect/auth/device"
            )

        val OMDS_SIGMA = OmdsNetworkEndpoint(
                "Pre-Prod",
                "https://sigma.omds-u.test.cce.af.mil/ommr/batdokj",
                "https://sigma.omds-u.test.cce.af.mil/query/a28",
                "https://sigma.omds-u.test.cce.af.mil/query/pampi",
                "https://sigma.omds-u.test.cce.af.mil/query/documents",
                "https://sigma.omds-u.test.cce.af.mil/login/realms/baby-yoda/protocol/openid-connect/token",
                "https://sigma.omds-u.test.cce.af.mil/login/realms/baby-yoda/protocol/openid-connect/auth/device"
            )
        
        val OMDS_PRODUCTION = OmdsNetworkEndpoint(
            "Production",
            "https://omds.health.mil/ommr/batdokj",
            "https://omds.health.mil/query/a28",
            "https://omds.health.mil/query/pampi",
            "https://omds.health.mil/query/documents",
            "https://omds.health.mil/login/realms/baby-yoda/protocol/openid-connect/token",
            "https://omds.health.mil/login/realms/baby-yoda/protocol/openid-connect/auth/device"
        )

        val MHSG_T = MhsgtNetworkEndpoint(
                "MHSG-T",
                "mirth.dod.local",
                "mirth.dod.local"
            )

        val LOCAL_TEST = OmdsNetworkEndpoint(
                "Local Test",
                "https://omds.batdok.org:8443/submit",
                "https://omds.batdok.org:8443/querya28",
                "https://omds.batdok.org:8443/querypampi",
                "https://omds.batdok.org:8443/querydocuments",
                "https://omds.batdok.org:8443/oauth/token",
                "https://omds.batdok.org:8443/oauth/device_authorization"
            )
    }

    fun copy(format: EndpointType = this.format, displayName: String = this.displayName) : Endpoint {
        return Endpoint(format, displayName)
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as Endpoint

        if (format != other.format) return false
        if (displayName != other.displayName) return false

        return true
    }

    override fun hashCode(): Int {
        var result = format.hashCode()
        result = 31 * result + displayName.hashCode()
        return result
    }

}

// Tag for network-capable endpoints
abstract class NetworkEndpoint(format: EndpointType, displayName: String) : Endpoint(format, displayName)

open class MhsgtNetworkEndpoint(
    displayName: String,
    val outboundHost: String,
    val inboundHost: String
) : NetworkEndpoint(EndpointType.MHSG_T, displayName) {

    val outboundUrl: String
        get() = "https://$outboundHost:3443/batdok/mhsgt/erc/"

    val inboundUrl: String
        get() = "https://$inboundHost:4443/mhsgt/batdok/erc/"

    fun copy(displayName: String = this.displayName, outboundHost: String = this.outboundHost,
             inboundHost: String = this.inboundHost) : MhsgtNetworkEndpoint {
        return MhsgtNetworkEndpoint(displayName, outboundHost, inboundHost)
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        if (!super.equals(other)) return false

        other as MhsgtNetworkEndpoint

        if (outboundUrl != other.outboundUrl) return false
        if (inboundUrl != other.inboundUrl) return false

        return true
    }

    override fun hashCode(): Int {
        var result = super.hashCode()
        result = 31 * result + outboundUrl.hashCode()
        result = 31 * result + inboundUrl.hashCode()
        return result
    }

}

open class OmdsNetworkEndpoint(
    displayName: String,
    val outboundUrl: String,
    val a28Url: String,
    val pampiUrl: String,
    val documentsUrl: String,
    val tokenUrl: String? = null,
    val deviceUrl: String? = null
) : NetworkEndpoint(EndpointType.OMDS, displayName) {
    fun copy(displayName: String = this.displayName, outboundUrl: String = this.outboundUrl,
             a28Url: String = this.a28Url, pampiUrl: String = this.pampiUrl,
             documentsUrl: String = this.documentsUrl, tokenUrl: String? = this.tokenUrl,
             deviceUrl: String? = this.deviceUrl) : OmdsNetworkEndpoint {
        return OmdsNetworkEndpoint(displayName, outboundUrl, a28Url, pampiUrl, documentsUrl, tokenUrl, deviceUrl)
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        if (!super.equals(other)) return false

        other as OmdsNetworkEndpoint

        if (outboundUrl != other.outboundUrl) return false
        if (a28Url != other.a28Url) return false
        if (pampiUrl != other.pampiUrl) return false
        if (documentsUrl != other.documentsUrl) return false
        if (tokenUrl != other.tokenUrl) return false
        if (deviceUrl != other.deviceUrl) return false

        return true
    }

    override fun hashCode(): Int {
        var result = super.hashCode()
        result = 31 * result + outboundUrl.hashCode()
        result = 31 * result + a28Url.hashCode()
        result = 31 * result + pampiUrl.hashCode()
        result = 31 * result + documentsUrl.hashCode()
        result = 31 * result + (tokenUrl?.hashCode() ?: 0)
        result = 31 * result + (deviceUrl?.hashCode() ?: 0)
        return result
    }

}
