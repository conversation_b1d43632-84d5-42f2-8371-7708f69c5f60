package mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.orur01

import ca.uhn.hl7v2.model.v231.message.ORU_R01
import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.observation.Observation
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.populateObxFromObservation
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.noteObr24DiagnosticServiceSectionId
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.obx033NoteCodingScheme
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.transferNoteCode
import mil.af.afrl.batman.hl7lib.util.HL7Data
import java.time.Instant

class ObservationOruBuilder(
    hl7Data: HL7Data,
    defaultTimestamp: Instant
) : BaseRepeatableORU_R01Builder<Observation>(hl7Data, defaultTimestamp) {

    override fun buildOrus(): List<ORU_R01> {
        val observations = hl7Data.document!!.observations.list
        val observationORUs = observations.mapNotNull {
            buildRepeatableDataOru(
                hl7Data,
                it,
                "BATDOK Observation"
            )
        }
        return observationORUs.apply {
            map {
                it.getObservationGroup().obr.obr24_DiagnosticServSectID.parse(
                    noteObr24DiagnosticServiceSectionId(endpoint)
                )
            }
        }
    }

    override fun getObrDetails(item: Observation) = ItemData(
        item.timestamp,
        transferNoteCode(endpoint),
        item.id,
        obx033NoteCodingScheme(endpoint)
    )

    override fun buildObservationList(msg: ORU_R01, item: Observation): List<OBX> {
        return populateObxFromObservation(msg, item, endpoint)
    }

}
