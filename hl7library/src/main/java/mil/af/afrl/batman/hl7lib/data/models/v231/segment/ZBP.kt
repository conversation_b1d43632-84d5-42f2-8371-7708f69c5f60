package mil.af.afrl.batman.hl7lib.data.models.v231.segment

import android.util.Log
import ca.uhn.hl7v2.HL7Exception
import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.Group
import ca.uhn.hl7v2.model.v21.datatype.CM
import ca.uhn.hl7v2.model.v231.datatype.ID
import ca.uhn.hl7v2.model.v231.datatype.IS
import ca.uhn.hl7v2.model.v231.datatype.NM
import ca.uhn.hl7v2.model.v231.datatype.SI
import ca.uhn.hl7v2.model.v231.datatype.ST
import ca.uhn.hl7v2.model.v231.datatype.TS
import ca.uhn.hl7v2.model.v231.datatype.XON
import ca.uhn.hl7v2.parser.ModelClassFactory
import mil.af.afrl.batman.hl7lib.data.models.v231.customSegments.ExtendedAbstractSegment
import mil.af.afrl.batman.hl7lib.util.HL7Data
import org.openehealth.ipf.modules.hl7.kotlin.get

class ZBP(parent: Group?, factory: ModelClassFactory? = null) : ExtendedAbstractSegment(parent, factory) {
    init {
        try {
            addSingleField(true,4, "Set ID", SI::class.java)//1
            addSingleField(true, 40,"BPU Unique Identifier", ST::class.java)//2
            addSingleField(true,20,"BPU Unit or Lot Number", ST::class.java)//3
            addSingleField(false,5,"BPU Sub Unit Number", ST::class.java)//4
            addSingleField(true,120,"BPU Product", CM::class.java)//5
            addSingleField(true,10,"BPU Status", IS::class.java)//6
            addSingleField(true,26,"BPU Event Date and time", TS::class.java)//7
            addSingleField(true,26,"BPU Expire Date and time", TS::class.java)//8
            addSingleField(true,20,"BPU ABO", IS::class.java)//9
            addSingleField(true,20,"BPU RH", IS::class.java)//10
            addSingleField(false,40,"BPU Antigen", IS::class.java)//11
            addSingleField(false,40,"BPU Attributes", IS::class.java)//12
            addSingleField(true,120,"BPU Supplier", XON::class.java)//13
            addBlankFields(3)//14,15,16
            addSingleField(false,1,"Pooled Product Indicator", ID::class.java)//17
            addSingleField(false,16,"Original Volume", NM::class.java)//18
            addSingleField(false,16,"Assumed Transferred Volume",NM::class.java)//19
            addSingleField(false,16,"Assumed Transfused Total International Units",NM::class.java)//20
            addSingleField(false,16,"Assumed Transfused Quantity",NM::class.java)//21
            addSingleField(false,5,"BPU Supplier Prefix",ST::class.java)//22
            addSingleField(false,5,"Volume Units of Measure",ID::class.java)//23
            addSingleField(false,20,"ISBT Flag Characters",ST::class.java)//24
            addSingleField(false,20,"Product Number Format",IS::class.java)//25
            addSingleField(false,40,"Quantity Units of Measure",ID::class.java)//26
            addSingleField(false,40,"Strength Units of Measure",ID::class.java)//27
        } catch (e: HL7Exception){
            Log.e("HL7Library", "Unexpected error creating ZBP", e)
        }
    }

    val ZBP1_SetID: SI
        get() = get(1) as SI
    val ZBP2_BPU_UniqueID: ST
        get() = get(2) as ST
    val ZBP3_BPU_UnitLotNumber: ST
        get() = get(3) as ST
    val ZBP4_BPU_SubUnit: ST
        get() = get(4) as ST
    val ZBP5_BPU_Product: CM
        get() = get(5) as CM
    val ZBP6_BPU_Status: IS
        get() = get(6) as IS
    val ZBP7_BPU_EventDateTime: TS
        get() = get(7) as TS
    val ZBP8_BPU_ExpireDateTime: TS
        get() = get(8) as TS
    val ZBP9_BPU_ABO: IS
        get() = get(9) as IS
    val ZBP10_BPU_RH: IS
        get() = get(10) as IS
    val ZBP11_BPU_Antigen: IS
        get() = get(11) as IS
    val ZBP12_BPU_Attributes: IS
        get() = get(12) as IS
    val ZBP13_BPU_Supplier: XON
        get() = get(13) as XON
    val ZBP17_PooledProductID: ID
        get() = get(17) as ID
    val ZBP18_OrginalVolume: NM
        get() = get(18) as NM
    val ZBP19_AssumedTransferredVolume: NM
        get() = get(19) as NM
    val ZBP20_AssumedTransfusedTotalUnits: NM
        get() = get(20) as NM
    val ZBP21_AssumedTransfusedQuantity: NM
        get() = get(21) as NM
    val ZBP22_BPU_SupplierPrefix: ST
        get() = get(22) as ST
    val ZBP23_VolumeUnitsOfMeasure: ID
        get() = get(23) as ID
    val ZBP24_ISBTFlag: ST
        get() = get(24) as ST
    val ZBP25_ProductFormatNum: IS
        get() = get(25) as IS
    val ZBP26_QuantityUnits: ID
        get() = get(26) as ID
    val ZBP27_StrengthUnits: ID
        get() = get(27) as ID

    override fun populateFromBatdokPatient(parent: AbstractMessage, hl7Data: HL7Data): ZBP {
        return ZBP(parent, parent.modelClassFactory)
    }
}