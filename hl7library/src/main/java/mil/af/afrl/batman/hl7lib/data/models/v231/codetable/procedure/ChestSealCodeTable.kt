package mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure

import gov.afrl.batdok.encounter.treatment.ChestSealData
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup

fun chestSealCode(endpoint: EndpointType) = when (endpoint){
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("464094004","Pneumothorax dressing (physical object)")
    else -> null
}

fun chestSealLocationCode(location: String) = when (location){
    ChestSealData.Location.LEFT_FRONT.dataString -> CodeNameGroup("1290343009","Structure of left half of anterior chest wall (body structure)")
    ChestSealData.Location.LEFT_BACK.dataString -> CodeNameGroup("788647001","Structure of left half of posterior chest wall (body structure)")
    ChestSealData.Location.RIGHT_FRONT.dataString -> CodeNameGroup("1290342004","Structure of right half of anterior chest wall (body structure)")
    ChestSealData.Location.RIGHT_BACK.dataString -> CodeNameGroup("788648006","Structure of right half of posterior chest wall (body structure)")
    else -> null
}

fun chestSealLocationFromCode(code: String) = when (code) {
    "1290343009" -> ChestSealData.Location.LEFT_FRONT.dataString
    "788647001" -> ChestSealData.Location.LEFT_BACK.dataString
    "1290342004" -> ChestSealData.Location.RIGHT_FRONT.dataString
    "788648006" -> ChestSealData.Location.RIGHT_BACK.dataString
    else -> null
}
