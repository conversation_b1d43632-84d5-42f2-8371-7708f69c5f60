package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.message

import ca.uhn.hl7v2.model.v231.message.ORM_O01
import ca.uhn.hl7v2.model.v231.segment.PID
import ca.uhn.hl7v2.model.v231.segment.PV1
import gov.afrl.batdok.commands.proto.DocumentCommands.CommandData
import gov.afrl.batdok.encounter.commands.buildUpdateMedicineCommand
import gov.afrl.batdok.encounter.ids.MedicineId
import gov.afrl.batdok.encounter.medicine.Medicine
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.converter.MedicationConverter
import mil.af.afrl.batman.hl7lib.data.models.v231.field.RxNorm
import mil.af.afrl.batman.hl7lib.util.generateDomainIdFromString
import org.openehealth.ipf.modules.hl7.kotlin.value
import java.time.Instant

class ORM_O01Receiver(message: ORM_O01, endpointType: EndpointType) : BaseReceiver<ORM_O01>(message, endpointType) {

    override val pid: PID = message.pidpD1NTEPV1PV2IN1IN2IN3GT1AL1.pid
    override val pv1: PV1 = message.pidpD1NTEPV1PV2IN1IN2IN3GT1AL1.pV1PV2.pV1

    override fun getMessageCommands(): List<CommandData> {
        val list = mutableListOf<CommandData>()

        for (orderRep in message.orcobrrqdrQ1ODSODTRXONTEDG1RXRRXCNTEOBXNTECTIBLGAll) {
            val orc = orderRep.orc
            val rxo = orderRep.obrrqdrQ1ODSODTRXONTEDG1RXRRXCNTEOBXNTE.rxo
            val rxr = orderRep.obrrqdrQ1ODSODTRXONTEDG1RXRRXCNTEOBXNTE.rxr

            // Some systems send the ID in ORC.2.3 (which is technically correct), while others just send in ORC.2
            val medIdString = orc.orc2_PlacerOrderNumber.ei3_UniversalID?.value?.ifEmpty { null } ?: orc.orc2_PlacerOrderNumber.value
            val medId = generateDomainIdFromString<MedicineId>(medIdString)
            val medTime = parseInstant(orc.orc9_DateTimeOfTransaction.value)

            val code = rxo.rxo1_RequestedGiveCode.ce1_Identifier?.value ?: ""
            val medCodingSystem = rxo.rxo1_RequestedGiveCode.ce3_NameOfCodingSystem?.value ?: ""
            val batdokMed = MedicationConverter.codeToBatdokData(code, medCodingSystem)
            val medName = batdokMed?.name ?: rxo.rxo1_RequestedGiveCode.ce2_Text.value ?: ""

            val giveAmount = rxo.rxo2_RequestedGiveAmountMinimum?.value?.toFloat() ?: 0.0f
            val medUnit = rxo.rxo4_RequestedGiveUnits.ce1_Identifier.value ?: rxo.rxo4_RequestedGiveUnits.ce2_Text.value
            val medRoute = rxr.rxr1_Route.ce1_Identifier.value ?: rxr.rxr1_Route.ce2_Text.value

            list.addCommand(medTime) {
                // UpdateMedCommand is a workaround here. LogMedCommand won't handle custom IDs the way we need it to.
                val med = Medicine(medName, route = medRoute, volume = giveAmount, unit = medUnit, medId = medId,
                    administrationTime = medTime, rxcui = code.takeIf { medCodingSystem == RxNorm })
                buildUpdateMedicineCommand(med)
            }
        }

        return list
    }
}