package mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure

import gov.afrl.batdok.encounter.observation.BreathSoundsData
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup

//region Outbound
fun breathSoundsCode(endpoint: EndpointType) = when (endpoint) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("52653008", "Respiratory sounds (observable entity)")
    else -> null
}

fun cdpBreathSoundsCode(type: String) = when(type){
    BreathSoundsData.Type.LEFT_ABSENT.dataString -> CodeNameGroup("65503000L","Absent breath sounds (finding) - Left")
    BreathSoundsData.Type.LEFT_DIMINISHED.dataString -> CodeNameGroup("58840004L","Decreased breath sounds (finding) - Left")
    BreathSoundsData.Type.LEFT_NORMAL.dataString -> CodeNameGroup("22803001L","Normal respiratory function (finding) - Left")
    BreathSoundsData.Type.RIGHT_ABSENT.dataString -> CodeNameGroup("65503000R","Absent breath sounds (finding) - Right")
    BreathSoundsData.Type.RIGHT_DIMINISHED.dataString -> CodeNameGroup("58840004R","Decreased breath sounds (finding) - Right")
    BreathSoundsData.Type.RIGHT_NORMAL.dataString -> CodeNameGroup("22803001R","Normal respiratory function (finding) - Right")
    else -> null
}

fun breathSoundsTypeFromCode(code: String) = when (code) {
    "65503000L" -> BreathSoundsData.Type.LEFT_ABSENT.dataString
    "58840004L" -> BreathSoundsData.Type.LEFT_DIMINISHED.dataString
    "22803001L" -> BreathSoundsData.Type.LEFT_NORMAL.dataString
    "65503000R" -> BreathSoundsData.Type.RIGHT_ABSENT.dataString
    "58840004R" -> BreathSoundsData.Type.RIGHT_DIMINISHED.dataString
    "22803001R" -> BreathSoundsData.Type.RIGHT_NORMAL.dataString
    else -> null
}

//endregion