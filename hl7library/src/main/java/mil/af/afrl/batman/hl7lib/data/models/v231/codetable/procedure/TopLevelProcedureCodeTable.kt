package mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure

import gov.afrl.batdok.encounter.observation.BreathSoundsData
import gov.afrl.batdok.encounter.observation.CasualtyPPEData
import gov.afrl.batdok.encounter.observation.CommonObservations
import gov.afrl.batdok.encounter.observation.Observation
import gov.afrl.batdok.encounter.observation.PupilDilationData
import gov.afrl.batdok.encounter.treatment.ChestSealData
import gov.afrl.batdok.encounter.treatment.ChestTubeData
import gov.afrl.batdok.encounter.treatment.CommonTreatments
import gov.afrl.batdok.encounter.treatment.EyeShieldData
import gov.afrl.batdok.encounter.treatment.FingerThorData
import gov.afrl.batdok.encounter.treatment.FoleyCatheterData
import gov.afrl.batdok.encounter.treatment.GastricTubeData
import gov.afrl.batdok.encounter.treatment.HypothermiaPreventionData
import gov.afrl.batdok.encounter.treatment.ImmobilizationData
import gov.afrl.batdok.encounter.treatment.LineData
import gov.afrl.batdok.encounter.treatment.NeedleDData
import gov.afrl.batdok.encounter.treatment.O2Data
import gov.afrl.batdok.encounter.treatment.PericardiocentesisData
import gov.afrl.batdok.encounter.treatment.PelvicBinderData
import gov.afrl.batdok.encounter.treatment.TqData
import gov.afrl.batdok.encounter.treatment.Treatment
import gov.afrl.batdok.encounter.treatment.WoundPackingData
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.data.models.v231.field.CPT4
import mil.af.afrl.batman.hl7lib.data.models.v231.field.SNOMED_CT
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup

fun getTopLevelProcedureCode(endpoint: Endpoint, treatment: Treatment) : CodeNameGroup? {
    val endpointType = endpoint.format
    return when {
        treatment.treatmentData is LineData -> {
            when ((treatment.treatmentData as LineData).type) {
                LineData.Type.ARTERIAL.dataString -> arterialLineCode(endpointType)
                LineData.Type.PERIPHERAL.dataString -> peripheralLineCode(endpointType)
                LineData.Type.CENTRAL.dataString -> centralLineCode(endpointType)
                LineData.Type.IO.dataString -> ioAccessCode(endpointType)
                else -> null
            }
        }
        treatment.treatmentData is TqData -> tqCode(endpoint.format)
        treatment.name == CommonTreatments.ET_TUBE.dataString -> ettCode(endpoint.format)
        treatment.name == CommonTreatments.DRESSING.dataString -> dressingCode(endpoint.format)
        treatment.treatmentData is ChestTubeData -> chestTubeCode(endpoint.format)
        treatment.treatmentData is O2Data -> {
            // DeliveryMethod is deprecated in favor of Route, so let it take precedence
            val data = treatment.getData<O2Data>()
            val source = data?.route ?: data?.deliveryMethod
            // TODO: Update to O2Route options once BATDOK switches to those
            when (source) {
                O2Data.DeliveryMethod.BVM.dataString -> bvmCode(endpointType)
                O2Data.DeliveryMethod.NRB.dataString -> nrbCode(endpointType)
                O2Data.DeliveryMethod.NC.dataString -> ncCode(endpointType)
                O2Data.DeliveryMethod.VENT.dataString -> ventO2Code(endpointType)
                else -> null
            }
        }
        treatment.treatmentData is ChestSealData -> chestSealCode(endpoint.format)
        treatment.treatmentData is FingerThorData -> fingerThorCode(endpoint.format)
        treatment.treatmentData is NeedleDData -> needleDCode(endpoint.format)
        treatment.treatmentData is FoleyCatheterData -> foleyCode(endpoint.format)
        treatment.treatmentData is GastricTubeData -> {
            when (treatment.getData<GastricTubeData>()?.type) {
                GastricTubeData.Type.ORAL.dataString -> gastricTubeOralCode(endpoint.format)
                GastricTubeData.Type.NASAL.dataString -> gastricTubeNasalCode(endpoint.format)
                else -> gastricTubeCode(endpoint.format)
            }
        }
        treatment.treatmentData is ImmobilizationData -> {
            when (val splintData = treatment.getData<ImmobilizationData>()?.type) {
                // Simple treatments, these should be fine
                ImmobilizationData.Type.C_COLLAR.dataString -> cCollarCode(endpointType)
                ImmobilizationData.Type.C_SPINE.dataString -> cSpineCode(endpointType)
                ImmobilizationData.Type.SPINE_BOARD.dataString -> spineBoardCode(endpointType)
                ImmobilizationData.Type.SWATH.dataString -> swathCode(endpointType)
                // These are no longer needing to be handled
//                ImmobilizationData.Type.PELVIC_SPLINT.dataString -> pelvicSplintCode(endpointType)
//                ImmobilizationData.Type.PELVIC_BINDER.dataString -> pelvicBinderCode(endpointType)
                // This is likely a splint, which has to be handled special
                else -> cdpSplintImmobilizationCode()
            }
        }
        treatment.treatmentData is EyeShieldData -> eyeShieldCode(endpoint.format)
        treatment.treatmentData is HypothermiaPreventionData -> {
            // Although multiple Hypothermia Preventions can be selected in BATDOK,
            //  they should be split by the time they get here
            when (treatment.getData<HypothermiaPreventionData>()?.type) {
                HypothermiaPreventionData.Type.BLANKET.dataString -> blanketCode(endpoint.format)
                // Nobody receives other types of warming natively, so send them all as other for now
                else -> otherHypothermiaPreventionCode(endpoint.format)
            }
        }
        treatment.treatmentData is PericardiocentesisData -> pericardiocentesisCode(endpoint.format)
        treatment.treatmentData is WoundPackingData -> woundPackingCode(endpointType)
        treatment.treatmentData is PelvicBinderData -> pelvicBinderCode(endpointType)
        else -> null
    }
}

fun getTopLevelObservationsCode(endpoint: Endpoint, observation: Observation) : CodeNameGroup? {
    val endpointType = endpoint.format
    return when {
        observation.observationData is BreathSoundsData -> breathSoundsCode(endpointType)
        observation.name == CommonObservations.CHEST_EQUAL_RISE_FALL.dataString -> chestRiseFallCode(endpointType)
        observation.name == CommonObservations.RHYTHM.dataString -> rhythmEctopyCode(endpoint.format)
        // not needed for cdp yet
        // observation.name == CommonObservations.PULSE_VALUES.dataString -> pulseCode(endpointType)
        observation.observationData is PupilDilationData -> pupilDilationCode(endpoint.format)
        observation.name == CommonObservations.CHEST_EQUAL_RISE_FALL.dataString -> chestRiseFallCode(endpoint.format)
        observation.observationData is CasualtyPPEData -> casualtyPPECode(endpoint.format)
        observation.name == "Visual Acuity Test" -> visualAcuityTestCode(endpointType)
        else -> null
    }
}

fun getProcedureFromTopLevelCode(endpoint: EndpointType, code: String) : String? {
    return when (code) {
        ioAccessCode(endpoint)?.code -> CommonTreatments.LINE.dataString
        arterialLineCode(endpoint)?.code -> CommonTreatments.LINE.dataString
        peripheralLineCode(endpoint)?.code -> CommonTreatments.LINE.dataString
        centralLineCode(endpoint)?.code -> CommonTreatments.LINE.dataString
        tqCode(endpoint)?.code -> CommonTreatments.TQ.dataString
        chestTubeCode(endpoint)?.code -> CommonTreatments.CHEST_TUBE.dataString
        ettCode(endpoint)?.code -> CommonTreatments.ET_TUBE.dataString
        bvmCode(endpoint)?.code -> CommonTreatments.O2.dataString
        nrbCode(endpoint)?.code -> CommonTreatments.O2.dataString
        ncCode(endpoint)?.code -> CommonTreatments.O2.dataString
        ventO2Code(endpoint)?.code -> CommonTreatments.O2.dataString
        dressingCode(endpoint)?.code -> CommonTreatments.DRESSING.dataString
        pressureDressingCode(endpoint)?.code -> CommonTreatments.DRESSING.dataString
        woundPackingCode(endpoint)?.code -> CommonTreatments.WOUND_PACKING.dataString
        eyeShieldCode(endpoint)?.code -> CommonTreatments.EYE_SHIELD.dataString
        blanketCode(endpoint)?.code -> CommonTreatments.HYPOTHERMIA_PREVENTION.dataString
        otherHypothermiaPreventionCode(endpoint)?.code -> CommonTreatments.HYPOTHERMIA_PREVENTION.dataString
        pupilDilationCode(endpoint)?.code -> CommonObservations.PUPIL_DILATION.dataString
        casualtyPPECode(endpoint)?.code -> CommonObservations.CASUALTY_PPE.dataString
        foleyCode(endpoint)?.code -> CommonTreatments.FOLEY_CATHETER.dataString
        visualAcuityTestCode(endpoint)?.code -> "Visual Acuity Test"
        gastricTubeNasalCode(endpoint)?.code -> CommonTreatments.GASTRIC_TUBE.dataString
        gastricTubeOralCode(endpoint)?.code -> CommonTreatments.GASTRIC_TUBE.dataString
        chestSealCode(endpoint)?.code -> CommonTreatments.CHEST_SEAL.dataString
        fingerThorCode(endpoint)?.code -> CommonTreatments.FINGER_THOR.dataString
        needleDCode(endpoint)?.code -> CommonTreatments.NEEDLE_D.dataString
        pericardiocentesisCode(endpoint)?.code -> CommonTreatments.PERICARDIOCENTESIS.dataString
        pelvicBinderCode(endpoint)?.code -> CommonTreatments.PELVIC_BINDER.dataString
        cCollarCode(endpoint)?.code -> CommonTreatments.IMMOBILIZATION.dataString
        cSpineCode(endpoint)?.code -> CommonTreatments.IMMOBILIZATION.dataString
        spineBoardCode(endpoint)?.code -> CommonTreatments.IMMOBILIZATION.dataString
        splintCode(endpoint)?.code -> CommonTreatments.IMMOBILIZATION.dataString
        chestRiseFallCode(endpoint)?.code -> CommonObservations.CHEST_EQUAL_RISE_FALL.dataString
        else -> null
    }
}

fun getTopLevelProcedureCodeType(endpoint: Endpoint) = when (endpoint.format) {
    EndpointType.MHSG_T -> CPT4
    else -> SNOMED_CT
}