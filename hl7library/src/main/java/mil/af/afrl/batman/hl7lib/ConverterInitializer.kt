package mil.af.afrl.batman.hl7lib

import android.content.Context
import mil.af.afrl.batman.hl7lib.converter.ClinicalNoteConverter
import mil.af.afrl.batman.hl7lib.converter.DBAllergyConverter
import mil.af.afrl.batman.hl7lib.converter.LocationInjuryConverter
import mil.af.afrl.batman.hl7lib.converter.MedicationConverter
import mil.af.afrl.batman.hl7lib.converter.MoiConverter
import mil.af.afrl.batman.hl7lib.converter.ObservationConverter
import mil.af.afrl.batman.hl7lib.converter.ProcedureConverter
import mil.af.afrl.batman.hl7lib.converter.RouteConverter
import mil.af.afrl.batman.hl7lib.converter.TreatmentConverter
import mil.af.afrl.batman.hl7lib.converter.UnitConverter
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ICD10
import mil.af.afrl.batman.hl7lib.data.models.v231.field.TACICD10
import mil.af.afrl.batman.hl7lib.util.ICD10DescriptionUtil

@Synchronized
fun initializeTables(context: Context) {
    if (!TreatmentConverter.fullTableLoaded) {
        TreatmentConverter.loadCsvData(context.assets.open("Therapeutic-Procedures-Table.csv"), TreatmentConverter.therapeuticProcedures)
        TreatmentConverter.loadCsvData(context.assets.open("Diagnostic-Procedures-Table.csv"), TreatmentConverter.diagnosticProcedures)
        TreatmentConverter.fullTableLoaded = true
    }
    if (!ProcedureConverter.fullTableLoaded) {
        ProcedureConverter.loadCsvData(context.assets.open("Therapeutic-Procedures-Table.csv"), ProcedureConverter.therapeuticProcedures)
        ProcedureConverter.loadCsvData(context.assets.open("Diagnostic-Procedures-Table.csv"), ProcedureConverter.diagnosticProcedures)
        ProcedureConverter.fullTableLoaded = true
    }
    if (!ObservationConverter.fullTableLoaded) {
        ObservationConverter.loadCsvData(
            context.assets.open("Therapeutic-Procedures-Table.csv"),
            ObservationConverter.therapeuticProcedures
        )
        ObservationConverter.loadCsvData(
            context.assets.open("Diagnostic-Procedures-Table.csv"),
            ObservationConverter.diagnosticProcedures
        )
        ObservationConverter.fullTableLoaded = true
    }
    if (!MedicationConverter.fullTableLoaded) {
        MedicationConverter.loadCsvData(context.assets.open("drugs-table.csv"))
        MedicationConverter.fullTableLoaded = true
    }
    if (!RouteConverter.fullTableLoaded) {
        RouteConverter.loadCsvData(context.assets.open(("route-data.csv")))
        RouteConverter.fullTableLoaded = true
    }
    if (!UnitConverter.fullTableLoaded) {
        UnitConverter.loadCsvData(context.assets.open(("unit-data.csv")))
        UnitConverter.fullTableLoaded = true
    }
    if (!ClinicalNoteConverter.fullTableLoaded){
        ClinicalNoteConverter.loadCsvData(context.assets.open("clinical-notes-data.csv"))
        ClinicalNoteConverter.fullTableLoaded = true
    }
    if (!MoiConverter.fullTableLoaded) {
        MoiConverter.loadCsvData(context.assets.open("moi-table.csv"))
        MoiConverter.fullTableLoaded = true
    }
    if (!LocationInjuryConverter.hasCodeset(ICD10)) {
        LocationInjuryConverter.loadData(context.assets.open("injuries-with-locations-standard.csv"), ICD10)
    }
    if (!LocationInjuryConverter.hasCodeset(TACICD10)) {
        LocationInjuryConverter.loadData(context.assets.open("injuries-with-locations-cdp.csv"), TACICD10)
    }
    DBAllergyConverter.initialize(context)
}
