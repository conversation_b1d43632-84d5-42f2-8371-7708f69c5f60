package mil.af.afrl.batman.hl7lib.data.models.v231.segment

import android.util.Log
import ca.uhn.hl7v2.HL7Exception
import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.Group
import ca.uhn.hl7v2.model.v231.datatype.NM
import ca.uhn.hl7v2.model.v231.datatype.SI
import ca.uhn.hl7v2.model.v231.datatype.ST
import ca.uhn.hl7v2.parser.ModelClassFactory
import mil.af.afrl.batman.hl7lib.data.models.v231.customSegments.ExtendedAbstractSegment
import mil.af.afrl.batman.hl7lib.util.HL7Data

class ZPR(parent: Group?, factory: ModelClassFactory? = null) :
    ExtendedAbstractSegment(parent, factory) {
    init {
        try {
            //http://hl7api.sourceforge.net/base/apidocs/ca/uhn/hl7v2/model/AbstractSegment.html#add%28java.lang.Class,%20boolean,%20int,%20int,%20java.lang.Object[],%20java.lang.String%29
            addSingleField(true, 4, "ZPR1", SI::class.java)
            addSingleField(false, 100, "ZPR2", ST::class.java)
            addSingleField(true, 4, "ZPR3", NM::class.java)
        } catch (e: HL7Exception) {
            Log.e("HL7Library", "Unexpected error creating ZPR", e)
        }
    }

    val zpr1: SI
        get() = getTypedField(1, 0)
    val zpr2: ST
        get() = getTypedField(2, 0)
    val zpr3: NM
        get() = getTypedField(3, 0)

    override fun populateFromBatdokPatient(parent: AbstractMessage, hl7Data: HL7Data): ZPR {
        TODO("Not yet implemented")
    }
}