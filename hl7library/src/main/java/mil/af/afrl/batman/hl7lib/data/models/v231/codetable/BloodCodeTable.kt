package mil.af.afrl.batman.hl7lib.data.models.v231.codetable

import gov.afrl.batdok.encounter.observation.Blood
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup

fun getBloodProductCode(blood: Blood, endpoint: Endpoint) : CodeNameGroup? {
    return when (endpoint.format) {
        EndpointType.OMDS -> CodeNameGroup("162324421", "Blood Products Given")
        EndpointType.MHSG_T -> getGtObxValue(blood)
        EndpointType.TAC -> getTacObxValue(blood)
    }
}

private fun getGtObxValue(blood: Blood) : CodeNameGroup? {
    return when (blood.bloodProduct) {
        "Cryoprecipitate" -> CodeNameGroup("51882-9", "Cryoprecipitate units given [#]")
        "Freeze Dried Plasma (FDP)" -> CodeNameGroup("10411-7", "Plasma given [Volume]")
        "Fresh Frozen Plasma (FFP)" -> CodeNameGroup("10411-7", "Plasma given [Volume]")
        "Whole Blood (WB)" -> CodeNameGroup("51876-1", "Whole blood units given [#]")
        "Fresh" -> CodeNameGroup("51876-1", "Whole blood units given [#]")
        "Fresh - low titer group O (LTOWB)" -> CodeNameGroup("51876-1", "Blood product units given [#]")
        "Cold Stored" -> CodeNameGroup("51876-1", "Whole blood units given [#]")
        "Cold Stored - Low Titer Group O (LTOWB)" -> CodeNameGroup("51876-1", "Blood product units given [#]")
        "Packed Red Blood Cells (PRBC)" -> CodeNameGroup("51880-3", "Packed erythrocytes units given [#]")
        "Platelets (plt)" -> CodeNameGroup("51873-8", "Platelet concentrate units given [#]")
        else -> return null
    }
}

private fun getTacObxValue(blood: Blood) : CodeNameGroup? {
    return when (blood.bloodProduct) {
        "Cryoprecipitate" -> CodeNameGroup("E5165", "Cryoprecipitate")
        "Freeze Dried Plasma (FDP)" -> CodeNameGroup("E0701", "Freeze dried plasma (FDP)")
        "Fresh Frozen Plasma (FFP)" -> CodeNameGroup("E0900", "Fresh frozen plasma (FFP)")
        "Whole Blood (WB)" -> CodeNameGroup("E0112", "Whole blood (WB)")
        "Fresh" -> CodeNameGroup("1", "Fresh")
        "Fresh - low titer group O (LTOWB)" -> CodeNameGroup("2", "Fresh - low titer group O (LTOWB)")
        "Cold Stored" -> CodeNameGroup("3", "Cold stored")
        "Cold Stored - Low Titer Group O (LTOWB)" -> CodeNameGroup("4", "Cold stored - low titer group O (LTOWB)")
        "Packed Red Blood Cells (PRBC)" -> CodeNameGroup("E0669", "Packed red blood cells (PRBC)")
        "Platelets (plt)" -> CodeNameGroup("E3077", "Platelets (plt)")
        else -> return null
    }
}

fun getBloodProductFromTacCode(code: String) : String? {
    return when (code) {
        "E5165" -> "Cryoprecipitate"
        "E0701" -> "Freeze Dried Plasma (FDP)"
        "E0900" -> "Fresh Frozen Plasma (FFP)"
        "E0112" -> "Whole Blood (WB)"
        "1" -> "Fresh"
        "2" -> "Fresh - low titer group O (LTOWB)"
        "3" -> "Cold Stored"
        "4" -> "Cold Stored - Low Titer Group O (LTOWB)"
        "E0669" -> "Packed Red Blood Cells (PRBC)"
        "E3077" -> "Platelets (plt)"
        else -> return null
    }
}

fun getBloodProductFromGtOmdsCode(code: String) : String? {
    return when (code) {
        "51882-9" -> "Cryoprecipitate"
        "10411-7" -> "Freeze Dried Plasma (FDP)"
        "10411-7" -> "Fresh Frozen Plasma (FFP)"
        "51876-1" -> "Whole Blood (WB)"
        "51876-1" -> "Fresh"
        "51876-1" -> "Fresh - low titer group O (LTOWB)"
        "51876-1" -> "Cold Stored"
        "51876-1" -> "Cold Stored - Low Titer Group O (LTOWB)"
        "51880-3" -> "Packed Red Blood Cells (PRBC)"
        "51873-8" -> "Platelets (plt)"
        else -> return null
    }
}

fun getBloodVolumeCode(endpoint: Endpoint) : CodeNameGroup? {
    return when (endpoint.format) {
        EndpointType.MHSG_T, EndpointType.TAC -> null
        EndpointType.OMDS -> CodeNameGroup("19182882", "Blood Unit Volume Infused:")
    }
}

fun getBloodExpirationCode(endpoint: Endpoint) : CodeNameGroup? {
    return when (endpoint.format) {
        EndpointType.TAC -> null
        EndpointType.MHSG_T -> CodeNameGroup("935-7", "Blood product unit expiration [Date and time]")
        EndpointType.OMDS -> null
    }
}

val inboundBloodExpCode = listOf("935-7")

fun getBloodTypeCode(endpoint: Endpoint) : CodeNameGroup? {
    return when (endpoint.format) {
        EndpointType.MHSG_T, EndpointType.TAC -> CodeNameGroup("933-2", "Blood product type")
        EndpointType.OMDS -> CodeNameGroup("709176", "Blood Unit Type")
    }
}

val inboundBloodTypeCodes = listOf("933-2", "709176")

fun getBloodAgeCode(endpoint: Endpoint) : CodeNameGroup? {
    return when (endpoint.format) {
        EndpointType.TAC, EndpointType.OMDS -> null
        EndpointType.MHSG_T -> CodeNameGroup("49546-5", "Age of Blood specimen")
    }
}

val inboundBloodAgeCodes = listOf("49546-5")

fun getBloodDINCode(endpoint: Endpoint) : CodeNameGroup? {
    return when (endpoint.format) {
        EndpointType.TAC -> null
        EndpointType.MHSG_T  -> CodeNameGroup("936-5", "Blood product unit [Identifier]")
        EndpointType.OMDS -> CodeNameGroup("3016970", "Blood Donor Nbr")
    }
}

val inboundBloodDinCodes = listOf("936-5", "3016970")
