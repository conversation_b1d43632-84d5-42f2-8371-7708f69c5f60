package mil.af.afrl.batman.hl7lib.util

import android.util.Log
import com.github.scribejava.core.builder.ServiceBuilder
import com.github.scribejava.core.builder.api.DefaultApi20
import com.github.scribejava.core.httpclient.HttpClientConfig
import com.github.scribejava.core.model.DeviceAuthorization
import com.github.scribejava.core.model.OAuth2AccessTokenErrorResponse
import com.github.scribejava.core.model.OAuthRequest
import com.github.scribejava.core.oauth.OAuth20Service
import com.github.scribejava.core.oauth2.OAuth2Error
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.supervisorScope
import kotlinx.coroutines.withContext
import mil.af.afrl.batman.hl7lib.OmdsNetworkEndpoint
import org.json.JSONObject
import java.io.OutputStream
import java.nio.charset.StandardCharsets
import java.security.MessageDigest
import java.security.SecureRandom
import java.time.Instant
import java.util.Base64
import javax.net.ssl.HttpsURLConnection

class AuthTokenRequestor {
    private val CLIENT_ID = "omds"

    private val random = SecureRandom()
    private val messageDigest = MessageDigest.getInstance("SHA-256")

    private var cancelled = false

    init {
        // Prefer to not use global here
        HttpsURLConnection.setDefaultSSLSocketFactory(HttpClient.createSslSocketFactory())
    }

    suspend fun submitTokenRequest(
        endpoint: OmdsNetworkEndpoint,
        onCodeRequestComplete: (String, String) -> Unit,
        onRefreshTokenReady: (String) -> Unit,
        onError: (NetworkResponse) -> Unit,
        dispatcher: CoroutineDispatcher = Dispatchers.IO
    ) = supervisorScope {
        if (endpoint.deviceUrl.isNullOrEmpty() || endpoint.tokenUrl.isNullOrEmpty()){
            Log.w("AuthTokenRequestor", "Malformed endpoint")
            onError(NetworkResponse(message = "Endpoint is invalid"))
            return@supervisorScope
        }

        cancelled = false
        val verifierCode = getVerifierCode()
        val challengeCode = getChallengeCode(verifierCode)

        val omdsApi = OmdsAuthApi(endpoint).also {
            it.verifierCode = verifierCode
            it.challengeCode = challengeCode
        }

        val apiService = ServiceBuilder(CLIENT_ID).build(omdsApi)

        val userCodeResponse = try {
            val userCodeResponseFuture = apiService.deviceAuthorizationCodesAsync
            withContext(dispatcher) {
                userCodeResponseFuture.get()
            }
        } catch (e: OAuth2AccessTokenErrorResponse) {
            Log.e("AuthTokenRequestor", "Error pulling auth code", e)
            onError(NetworkResponse(e.response.code, e, e.response.message))
            return@supervisorScope
        } catch (t: Throwable) {
            Log.e("AuthTokenRequestor", "Error pulling auth code", t)
            onError(NetworkResponse(error = t))
            return@supervisorScope
        }

        // Show the code to enter on the OMDS website to the user
        onCodeRequestComplete(userCodeResponse.verificationUri, userCodeResponse.userCode)

        val accessTokenResponse = withContext(dispatcher) {
            // Library doesn't have a great way to poll... polling method runs indefinitely
            for (i in 0 until 60) {
                if (cancelled) return@withContext null

                try {
                    return@withContext apiService.getAccessTokenDeviceAuthorizationGrant(userCodeResponse)
                } catch (e: OAuth2AccessTokenErrorResponse) {
                    if (e.error != OAuth2Error.AUTHORIZATION_PENDING) {
                        Log.e("AuthTokenRequestor", "Failed to get token!", e)
                        onError(NetworkResponse(e.response.code, e, e.response.message))
                        return@withContext null
                    }
                } catch (t: Throwable) {
                    Log.e("AuthTokenRequestor", "Failed to get token!", t)
                    onError(NetworkResponse(error = t))
                    return@withContext null
                }
                Thread.sleep(5000)
            }
            null
        } ?: return@supervisorScope

        val encodedTokenInfo = encodeTokenInformation(
            userCodeResponse.deviceCode,
            accessTokenResponse.refreshToken
        )
        onRefreshTokenReady(encodedTokenInfo)
    }

    suspend fun getAuthToken(
        endpoint: OmdsNetworkEndpoint,
        tokenInfo: String,
        onTokenAvailable: (String) -> Unit,
        onTokenRefresh: (String) -> Unit,
        onError: (NetworkResponse) -> Unit,
        dispatcher: CoroutineDispatcher = Dispatchers.IO
    ) = supervisorScope {
        if (endpoint.deviceUrl.isNullOrEmpty() || endpoint.tokenUrl.isNullOrEmpty()){
            Log.w("AuthTokenRequestor", "Malformed endpoint")
            onError(NetworkResponse(message = "Endpoint is invalid"))
            return@supervisorScope
        }

        if (!isTokenInfoValid(tokenInfo)) {
            onError(NetworkResponse(message = "Token Info is Invalid"))
            return@supervisorScope
        }

        val omdsApi = OmdsAuthApi(endpoint).apply {
            deviceCode = getDeviceCodeFromTokenInfo(tokenInfo)
        }
        val apiService = ServiceBuilder(CLIENT_ID).build(omdsApi)

        val refreshTokenResponse = try {
            val refreshTokenFuture = apiService.refreshAccessTokenAsync(getRefreshTokenFromTokenInfo(tokenInfo))
            withContext(dispatcher) {
                refreshTokenFuture.get()
            }
        } catch (e: OAuth2AccessTokenErrorResponse) {
            Log.e("AuthTokenRequestor", "Failed to get token!", e)
            onError(NetworkResponse(e.response.code, e, e.response.message))
            return@supervisorScope
        } catch (t: Throwable) {
            Log.e("AuthTokenRequestor", "Failed to get token!", t)
            onError(NetworkResponse(error = t))
            return@supervisorScope
        }

        onTokenAvailable(refreshTokenResponse.accessToken)

        val encodedTokenInfo = encodeTokenInformation(
            "refresh",
            refreshTokenResponse.refreshToken
        )
        onTokenRefresh(encodedTokenInfo)
    }

    fun cancelRequest() {
        cancelled = true
    }

    private fun getVerifierCode(): String {
        val codeVerifierSeed = random.generateSeed(40)
        return urlEncode(codeVerifierSeed)
    }

    private fun getChallengeCode(verifierCode: String): String {
        val codeChallengeSeed =
            messageDigest.digest(verifierCode.toByteArray(StandardCharsets.UTF_8))
        return urlEncode(codeChallengeSeed)
    }

    private fun urlEncode(bytes: ByteArray): String {
        return Base64.getUrlEncoder()
            .encodeToString(bytes)
            .replace("=", "")
    }

    private fun encodeTokenInformation(deviceCode: String, refreshToken: String): String {
        return JSONObject().apply {
            put("device_code", deviceCode)
            put("refresh_token", refreshToken)
            put("issue_time", Instant.now().epochSecond)
        }.toString()
    }

    private fun getDeviceCodeFromTokenInfo(tokenInfo: String): String {
        return JSONObject(tokenInfo).getString("device_code")
    }

    private fun getRefreshTokenFromTokenInfo(tokenInfo: String): String {
        return JSONObject(tokenInfo).getString("refresh_token")
    }

    private fun isTokenInfoValid(tokenInfo: String): Boolean {
        return AuthTokenValidityUtil.isTokenInfoValid(tokenInfo)
    }

    private inner class OmdsAuthApi(val endpoint: OmdsNetworkEndpoint) : DefaultApi20() {
        var challengeCode: String? = null
        var verifierCode: String? = null
        var deviceCode: String? = null

        override fun getAccessTokenEndpoint() = endpoint.tokenUrl ?: ""
        override fun getAuthorizationBaseUrl() = ""  // Unused
        override fun getDeviceAuthorizationEndpoint() = endpoint.deviceUrl ?: ""

        override fun createService(apiKey: String?, apiSecret: String?, callback: String?, defaultScope: String?, responseType: String?, debugStream: OutputStream?, userAgent: String?, httpClientConfig: HttpClientConfig?, httpClient: com.github.scribejava.core.httpclient.HttpClient?): OAuth20Service {
            return object : OAuth20Service(this, apiKey, apiSecret, callback, defaultScope, responseType, debugStream, userAgent, httpClientConfig, httpClient) {
                override fun createDeviceAuthorizationCodesRequest(scope: String?): OAuthRequest? {
                    // HACK
                    return super.createDeviceAuthorizationCodesRequest(scope).apply {
                        challengeCode?.let { addParameter("code_challenge", it) }
                        addParameter("code_challenge_method", "S256")
                        addParameter("scope", "openid offline_access")
                    }
                }

                override fun createAccessTokenDeviceAuthorizationGrantRequest(deviceAuthorization: DeviceAuthorization?): OAuthRequest? {
                    // MORE HACK
                    return super.createAccessTokenDeviceAuthorizationGrantRequest(deviceAuthorization).apply {
                        addParameter("client_id", CLIENT_ID)
                        verifierCode?.let { addParameter("code_verifier", it) }
                        addParameter("scope", "openid offline_access")
                    }
                }

                override fun createRefreshTokenRequest(refreshToken: String?, scope: String?): OAuthRequest {
                    return super.createRefreshTokenRequest(refreshToken, scope).apply {
                        addParameter("client_id", CLIENT_ID)
                        deviceCode?.let { addParameter("device_code", it) }
                    }
                }
            }
        }
    }

}
