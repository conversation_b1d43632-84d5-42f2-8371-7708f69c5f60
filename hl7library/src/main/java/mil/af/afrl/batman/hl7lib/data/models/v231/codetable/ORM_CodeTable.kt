package mil.af.afrl.batman.hl7lib.data.models.v231.codetable

import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.data.models.v231.field.DHMSM_ICD

//ORC.1 Order control
//NW – New order
//CA – Cancel
//XO – Modified
//SC – Status change
//CM – Complete
//VD - Void
//DC – Discontinue

fun ormOrc01OrderControlCode(endpoint: Endpoint) = "NW"

//Order Status Codes:
//NW – New Order
//CM – Complete
//CA – Cancel
//IP – In Process
//DC – Discontinue
//D – Voided
//S - Suspended
fun ormOrc05OrderStatusCode(endpoint: Endpoint) = "CM"

fun ormRxo04NameOfCodingSystem(endpoint: Endpoint) : String {
    return when(endpoint.format) {
        EndpointType.MHSG_T -> DHMSM_ICD
        EndpointType.OMDS -> ""
        EndpointType.TAC -> ""
    }
}

fun ormRxr01NameOfCodingSystem(endpoint: Endpoint) : String {
    return when(endpoint.format) {
        EndpointType.MHSG_T -> DHMSM_ICD
        EndpointType.OMDS -> ""
        EndpointType.TAC -> ""
    }
}
