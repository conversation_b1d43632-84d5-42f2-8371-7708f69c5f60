package mil.af.afrl.batman.hl7lib.converter

import gov.afrl.batdok.encounter.Document
import gov.afrl.batdok.encounter.PatcatService
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType

object ServiceConverter {
    fun batdokServiceToEmployerCode(document: Document, endpoint: Endpoint?) : String {
        if (document.info.patcat?.service != null) {
            return if (endpoint?.format == EndpointType.TAC) {
                cdpPatcatServiceMap[document.info.patcat?.service] ?: ""
            } else {
                baseBatcatServiceMap[document.info.patcat?.service] ?: ""
            }
        }

        // TODO: Remove this block when service is removed
        return if (endpoint?.format == EndpointType.TAC) {
            cdpServiceMap[document.info.service] ?: ""
        } else {
            baseServiceMap[document.info.service] ?: ""
        }
    }
    
    private val baseServiceMap = mapOf(
        "U.S. Army" to "AR00",
        "U.S. Navy" to "NV00",
        "U.S. Coast Guard" to "HSAC",
        "U.S. Marines" to "NV27",
        "U.S. Air Force" to "AF00",
    )

    // For now, other systems aren't expecting these values, so don't send them
    //  to anyone but CDP
    private val cdpServiceMap = baseServiceMap + mapOf(
        "U.S. Space Force" to "AF4G",
        "U.S. NOAA" to "CM54",
        "U.S. Public Health Service" to "HED1",
        "NATO Military" to "113",
        "Non-NATO Military" to "112",
        "U.S. Civilian" to "122",
        "U.S. DOD Contractor" to "E00",
        "DOS" to "ST00",
        "U.S. Civilian" to "105",
        "Local National" to "116",
        "Prisoner of War (POW)" to "108"
    )

    private val baseBatcatServiceMap = mapOf(
        PatcatService.ARMY to "AR00",
        PatcatService.COAST_GUARD to "HSAC",
        PatcatService.AIR_FORCE to "AF00",
        PatcatService.MARINE_CORPS to "NV27",
        PatcatService.NAVY to "NV00",
    )

    private val cdpPatcatServiceMap = baseBatcatServiceMap + mapOf(
        PatcatService.NOAA to "CM54",
        PatcatService.CIVILIAN to "122",
        PatcatService.USPHS to "HED1",
        PatcatService.SPACE_FORCE to "AF4G"
    )

}
