package mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.orur01

import ca.uhn.hl7v2.model.v231.message.ORU_R01
import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.Event
import gov.afrl.batdok.encounter.KnownEventTypes
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.populateObxFromEvent
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.noteObr24DiagnosticServiceSectionId
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.obx033NoteCodingScheme
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.transferNoteCode
import mil.af.afrl.batman.hl7lib.util.HL7Data
import java.time.Instant

class OrderEventOruBuilder(
    hl7Data: HL7Data,
    defaultTimestamp: Instant
) : BaseRepeatableORU_R01Builder<Event>(hl7Data, defaultTimestamp) {

    override fun buildOrus(): List<ORU_R01> {
        val orderEvents =
            hl7Data.document!!.events.list.filter { it.eventType == KnownEventTypes.ORDERS.dataString }
        val orderORUs =
            orderEvents.mapNotNull { buildRepeatableDataOru(hl7Data, it, "BATDOK Order") }
        return orderORUs.apply {
            map {
                it.getObservationGroup().obr.obr24_DiagnosticServSectID.parse(
                    noteObr24DiagnosticServiceSectionId(endpoint)
                )
            }
        }
    }

    override fun getObrDetails(item: Event) = ItemData(
        item.timestamp,
        transferNoteCode(endpoint),
        item.id,
        obx033NoteCodingScheme(endpoint)
    )

    override fun buildObservationList(msg: ORU_R01, item: Event): List<OBX> {
        return populateObxFromEvent(msg, item, endpoint)
    }

}
