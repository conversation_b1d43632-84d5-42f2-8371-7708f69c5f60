package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.message

import ca.uhn.hl7v2.model.v231.message.ADT_A04
import ca.uhn.hl7v2.model.v231.segment.PID
import ca.uhn.hl7v2.model.v231.segment.PV1
import gov.afrl.batdok.commands.proto.DocumentCommands.CommandData
import gov.afrl.batdok.encounter.commands.buildChangeInjuryTimeCommand
import mil.af.afrl.batman.hl7lib.EndpointType
import org.openehealth.ipf.modules.hl7.kotlin.value

class ADT_A04Receiver(message: ADT_A04, endpointType: EndpointType) : BaseReceiver<ADT_A04>(message, endpointType) {
    override val pid: PID = message.pid
    override val pv1: PV1 = message.pV1

    override fun getMessageCommands(): List<CommandData> {
        val commands = mutableListOf<CommandData>()

        // Grab injury time from ACC if present
        if (!message.acc.acc1_AccidentDateTime.isEmpty) {
            val time = message.acc.acc1_AccidentDateTime.value
            commands.addCommand { buildChangeInjuryTimeCommand(parseInstant(time)) }
        }

        return commands
    }
}
