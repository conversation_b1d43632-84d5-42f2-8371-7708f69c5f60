package mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure

import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup

fun gastricTubeCode(endpoint: EndpointType) = when (endpoint) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("33120003","Intubation of stomach (procedure)")
    else -> null
}

fun gastricTubeNasalCode (endpoint: EndpointType) = when (endpoint) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("87750000","Insertion of nasogastric tube (procedure)")
    else -> null
}

fun gastricTubeOralCode (endpoint: EndpointType) = when (endpoint) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("235425002","Insertion of orogastric tube (procedure)")
    else -> null
}
