package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.message

import ca.uhn.hl7v2.model.Structure
import ca.uhn.hl7v2.model.v231.segment.AL1
import ca.uhn.hl7v2.model.v231.segment.PID
import ca.uhn.hl7v2.model.v231.segment.PV1
import gov.afrl.batdok.commands.proto.DocumentCommands.CommandData
import gov.afrl.batdok.encounter.*
import gov.afrl.batdok.encounter.commands.buildChangeBloodTypeCommand
import gov.afrl.batdok.encounter.commands.buildChangeGradeCommand
import gov.afrl.batdok.encounter.commands.buildChangePatcatCommand
import gov.afrl.batdok.encounter.commands.buildChangeRankCommand
import gov.afrl.batdok.encounter.commands.buildChangeServiceCommand
import gov.afrl.batdok.encounter.commands.buildChangeUnitCommand
import gov.afrl.batdok.encounter.commands.buildPatientMaskingCommand
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.data.models.v231.group.ADT_A28_PR1OBXROL
import mil.af.afrl.batman.hl7lib.data.models.v231.message.ADT_A28
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.util.parseAllergyCommand
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.util.parseInjuryInfo
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.util.parsePr1ObxInfo
import mil.af.afrl.batman.hl7lib.util.HL7Data
import mil.af.afrl.batman.hl7lib.util.EscapeUtil
import org.openehealth.ipf.modules.hl7.kotlin.get
import org.openehealth.ipf.modules.hl7.kotlin.value

class ADT_A28Receiver(message: ADT_A28, endpointType: EndpointType) : BaseReceiver<ADT_A28>(message, endpointType) {

    override val pid: PID = message.pid
    override val pv1: PV1 = message.pV1

    override fun getMessageCommands(): List<CommandData> {
        val commands = mutableListOf<CommandData>()

        val dg1ZdgCommands = parseInjuryInfo(message.getDg1ZdgAll(), endpointType)
        if (dg1ZdgCommands.isNotEmpty()){
            for (command in dg1ZdgCommands){
                commands.addCommand { command }
            }
        }

        // The message doesn't always index the AL1 segments correctly, so the al1All method
        //  won't always work
        val al1s = message.encode().split('\r')
            .filter { it.startsWith("AL1") }
            .map { AL1(message, message.modelClassFactory).apply { parse(it) } }
        commands.addCommand { parseAllergyCommand(al1s) }
        
        parsePr1ObxInfo(message.getAll("PR1OBXROL").mapNotNull { it as? ADT_A28_PR1OBXROL },endpointType,::parseInstant)
            .map { commands.addCommand { it }}

        if (message.nonStandardNames.contains("ZPI")) {
            val zpis = message.getAll("ZPI")
            commands.addAll(parseZpiInfo(zpis.first()))
        }

        if (message.nonStandardNames.contains("ZEI")) {
            val zeis = message.getAll("ZEI")
            commands.addAll(parseZeiInfo(zeis.first()))
        }

        if (message.nonStandardNames.contains("ZMI")) {
            val zmis = message.getAll("ZMI")
            commands.addAll(parseZmiInfo(zmis.first()))
        }

        return commands
    }

    override fun setExtraData(hl7Data: HL7Data) {
        super.setExtraData(hl7Data)
        if (message.nonStandardNames.contains("ZPI")) {
            val zpis = message.getAll("ZPI")
            if (zpis.first()[15].value == "DOG") {
                // We might eventually pull the protobuf library in here to get the
                //  enum string, but that doesn't seem worth it for this one value
                hl7Data.mode = "TCCC K9"
            }
        }
    }

    private fun parseZpiInfo(zpi: Structure) : List<CommandData> {
        // ZPI will contain a confidentiality indicator and species
        val zpiCommands = mutableListOf<CommandData>()

        val bloodType = zpi[5].value
        if (!bloodType.isNullOrEmpty()) {
            val bloodEnum = BloodType.entries.find { it.dataString == bloodType }
            bloodEnum?.let { zpiCommands.addCommand { buildChangeBloodTypeCommand(it) } }
        }

        if (zpi[9].value == "Y") {
            zpiCommands.addCommand { buildPatientMaskingCommand(true, "Masked by external application") }
        }
        return zpiCommands
    }

    private fun parseZeiInfo(zei: Structure) : List<CommandData> {
        val zeiCommands = mutableListOf<CommandData>()
        // Branch of service
        val branch = zei[11].value ?: ""
        // TODO: Remove `service` when the old service stuff is removed
        val service = when {
            branch == "AF4G" -> Service.USSF
            branch.contains("AF") -> Service.AIR_FORCE
            branch == "NV27" || branch.contains("MC") -> Service.MARINES
            branch.contains("NV") -> Service.NAVY
            branch.contains("AR") -> Service.ARMY
            branch == "HSAC" || branch.contains("CG") -> Service.USCG
            branch == "HED1" || branch.contains("PH") -> Service.USPHS
            // CDP may send these codes in ZEI.11, others might send them in ZEI.13
            branch in listOf("113", "114") -> Service.NATO
            branch == "112" -> Service.NON_NATO
            branch == "115" -> Service.PARTNER_FORCE
            branch == "T00" -> Service.NONCOALITION_FORCE
            branch == "117" -> Service.EPW
            branch in listOf("O00", "E00") -> Service.CONTRACTOR
            branch in listOf( "107", "122", "105") -> Service.US_CIVILIAN  // Really CIV-LOCAL
            branch == "CM54" -> Service.NOAA
            branch == "ST00" -> Service.DOS
            branch == "116" -> Service.LOCAL_NATIONAL
            branch == "108" -> Service.POW
            else -> null
        }
        val patcatService = when {
            branch == "AF4G" -> PatcatService.SPACE_FORCE
            branch.contains("AF") -> PatcatService.AIR_FORCE
            branch == "NV27" || branch.contains("MC")-> PatcatService.MARINE_CORPS
            branch.contains("NV") -> PatcatService.NAVY
            branch.contains("AR") -> PatcatService.ARMY
            branch == "HSAC" || branch.contains("CG") -> PatcatService.COAST_GUARD
            branch == "HED1" || branch.contains("PH") -> PatcatService.USPHS
            branch == "CM54" -> PatcatService.NOAA
            branch in listOf("116", "107", "122") -> PatcatService.CIVILIAN
            else -> null
        }

        if(service != null ){
            if(service == Service.DOS){
                zeiCommands.addCommand { buildChangePatcatCommand(Patcat( PatcatService.CIVILIAN, PatcatStatus.STATE_DEPARTMENT_EMPLOYEE_OUTSIDE_US))}
            }else{
                service.let { zeiCommands.addCommand { buildChangeServiceCommand(service) } }
            }
        }

        patcatService?.let { zeiCommands.addCommand { buildChangePatcatCommand(Patcat(it, null)) } }

        // Job code (basically non-military branch codes)
        val jobCodeData = zei[13].value?.split("^")
        val jobCode = jobCodeData?.firstOrNull()
        // TODO: Remove this when service goes away
        // What is difference between non-NATO coalition, partner force, non-coalition force
        val batdokJob = when (jobCode) {
            in listOf("113", "114") -> Pair(PatcatService.CIVILIAN,PatcatStatus.NATO_MILITARY)
            "112" -> Pair(PatcatService.CIVILIAN, PatcatStatus.NON_NATO_MILITARY)
            "115" -> Service.PARTNER_FORCE
            "T00" -> Service.NONCOALITION_FORCE
            "117" -> Service.EPW
            in listOf("O00", "E00") -> Pair(PatcatService.CIVILIAN,PatcatStatus.CONTRACT_EMPLOYEE_OR_FAMILY_MEMBER)
            in listOf("107", "122", "105") -> PatcatStatus.OTHER_US_GOVERNMENT_BENEFICIARY
            "108" -> Pair(PatcatService.CIVILIAN, PatcatStatus.FEDERAL_PRISONER)
            "116" -> Pair(PatcatService.CIVILIAN, PatcatStatus.FOREIGN_CIVILIAN)
            "CM54" -> Service.NOAA
            "ST00" -> Pair(PatcatService.CIVILIAN,PatcatStatus.STATE_DEPARTMENT_EMPLOYEE_OUTSIDE_US)
            "122" -> Pair(PatcatService.CIVILIAN, PatcatStatus.OTHER_US_GOVERNMENT_BENEFICIARY)
            else -> Service.OTHER
        }
        // PatCat doesn't have a lot of these as options
        val patcatJob = if (jobCode in listOf( "107")) PatcatService.CIVILIAN else null
        patcatJob?.let { zeiCommands.addCommand { buildChangePatcatCommand(Patcat(patcatJob, null)) } }

        if (service == null) {
            if (batdokJob is Pair<*, *>) {
                Patcat("","")
                zeiCommands.addCommand { buildChangePatcatCommand(Patcat(batdokJob.first as PatcatService,batdokJob.second as PatcatStatus)) }
            } else {
                zeiCommands.addCommand { buildChangeServiceCommand(batdokJob as Service) }
            }
        }

        // Pay grade (only valid if there's a real military service found)
        val payGrade = jobCodeData?.lastOrNull() ?: ""
        if (payGrade.startsWith("M") && !payGrade.startsWith("MC")) {
            // GT ranks will be structured like MO-07, which we need to convert to O-7
            val cleanGrade = payGrade.drop(1).replace("-0", "-")
            zeiCommands.addCommand { buildChangeRankCommand(cleanGrade) }
            gradeStringToPatcatGrade(cleanGrade)?.let {
                zeiCommands.addCommand { buildChangeGradeCommand(it) }
            }
        }

        return zeiCommands
    }

    private fun parseZmiInfo(zmi: Structure) : List<CommandData> {
        val zmiCommands = mutableListOf<CommandData>()
        zmi[4].value?.let {
            val unescapedUnit = EscapeUtil.unescape(it)
            zmiCommands.addCommand { buildChangeUnitCommand(unescapedUnit) }
        }
        return zmiCommands
    }

    private fun gradeStringToPatcatGrade(gradeString: String) : Grade? {
        return Grade.entries.find { it.shortString == gradeString }
    }
}
