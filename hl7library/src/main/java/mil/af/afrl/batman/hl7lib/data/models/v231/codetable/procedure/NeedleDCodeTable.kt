package mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure


import gov.afrl.batdok.encounter.treatment.NeedleDData
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup

fun needleDCode(endpoint: EndpointType) = when (endpoint){
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("1290622004","Needle chest decompression for tension pneumothorax (procedure)")
    else -> null
}

fun needleDLocationCode(location: String) = when (location) {
    NeedleDData.Location.L_ANT_AX.dataString -> CodeNameGroup("91199000","Anterior axillary line structure (body structure)")
    NeedleDData.Location.R_ANT_AX.dataString -> CodeNameGroup("91199000","Anterior axillary line structure (body structure)")
    NeedleDData.Location.L_MID_CLAV.dataString -> CodeNameGroup("279013009","Midclavicular line (body structure)")
    NeedleDData.Location.R_MID_CLAV.dataString -> CodeNameGroup("279013009","Midclavicular line (body structure)")
    else -> null
}

fun needleDSideCode(side: String) = when (side){
    NeedleDData.Location.L_ANT_AX.dataString -> CodeNameGroup("7771000","Left (qualifier value)")
    NeedleDData.Location.R_ANT_AX.dataString -> CodeNameGroup("24028007","Right (qualifier value)")
    NeedleDData.Location.L_MID_CLAV.dataString -> CodeNameGroup("7771000","Left (qualifier value)")
    NeedleDData.Location.R_MID_CLAV.dataString -> CodeNameGroup("24028007","Right (qualifier value)")
    else -> null
}

fun needleDLocationFromCode(code: String) = when (code) {
    "91199000" -> NeedleDData.Location.L_ANT_AX.dataString
    "279013009" -> NeedleDData.Location.L_MID_CLAV.dataString
    else -> null
}

fun needleDSideFromCode(code: String) = when (code) {
    "7771000" -> NeedleDData.Location.L_ANT_AX.dataString
    "24028007" -> NeedleDData.Location.R_ANT_AX.dataString
    "7771000" -> NeedleDData.Location.L_MID_CLAV.dataString
    "24028007" -> NeedleDData.Location.R_MID_CLAV.dataString
    else -> null
}