package mil.af.afrl.batman.hl7lib.data.models.v231.builder.message

import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.AbstractSegment
import ca.uhn.hl7v2.model.v231.segment.EVN
import ca.uhn.hl7v2.model.v231.segment.MSH
import ca.uhn.hl7v2.util.idgenerator.UUIDGenerator
import gov.afrl.batdok.encounter.Document
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.RepeatedSegmentBuilder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.SegmentBuilder
import mil.af.afrl.batman.hl7lib.data.models.v231.customSegments.ExtendedAbstractSegment
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.converter.TimeConverter
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.msh11ProcessingIDCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.msh12VersionIDCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.msh03SendApplicationCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.msh05ReceiveApplicationCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.msh06ReceivingFacilityCode
import mil.af.afrl.batman.hl7lib.util.HL7Data
import org.openehealth.ipf.modules.hl7.kotlin.from
import org.openehealth.ipf.modules.hl7.kotlin.get

abstract class AbstractMessageBuilder<T: AbstractMessage> {
    protected abstract val msg: T
    fun populateBoilerplate(messageCode: String, triggerEvent: String, endpoint: Endpoint): AbstractMessageBuilder <T> {
        populateMsh(messageCode, triggerEvent, endpoint)
        populateEvn(triggerEvent)
        return this
    }

    fun populateEvn(triggerEvent: String): AbstractMessageBuilder <T> {
        val msh = msg.get("MSH") as MSH
        val evn = msg.get("EVN") as EVN
        evn.evn1_EventTypeCode.parse(triggerEvent)
        evn.evn2_RecordedDateTime.from(msh.dateTimeOfMessage)
        return this
    }

    fun populateMsh(messageCode: String, triggerEvent: String, endpoint: Endpoint): AbstractMessageBuilder <T>{
        msg.initQuickstart(messageCode, triggerEvent, msh11ProcessingIDCode(endpoint))
        val msh = msg.get("MSH") as MSH
        msh.msh3_SendingApplication.parse(msh03SendApplicationCode(endpoint))
        msh.msh5_ReceivingApplication.parse(msh05ReceiveApplicationCode(endpoint))
        msh.msh6_ReceivingFacility.parse(msh06ReceivingFacilityCode(endpoint))
        msh.msh7_DateTimeOfMessage.parse(TimeConverter.timestampToFormattedDateTime())
        msh.msh10_MessageControlID.parse(UUIDGenerator().id)
        msh.msh12_VersionID.parse(msh12VersionIDCode(endpoint))
        return this
    }

    fun populateSegment(builder: SegmentBuilder<out AbstractSegment>, hl7Data: HL7Data): AbstractMessageBuilder <T>{
        val constructed = builder.populateFromBatdokPatient(msg, hl7Data)
        (msg[constructed.name] as AbstractSegment).from(constructed)
        return this
    }

    fun populateRepeatedSegment(builder: RepeatedSegmentBuilder<out AbstractSegment>, document: Document): AbstractMessageBuilder <T> {
        builder.populateListFromBatdokPatient(msg, document).forEachIndexed { index, segment ->
            val repetition = msg.insertRepetition(segment.name, index) as AbstractSegment
            repetition.from(segment)
        }
        return this
    }

    fun populateCustomSegments(hl7Data: HL7Data, segments: Collection<ExtendedAbstractSegment>): AbstractMessageBuilder <T>  {
        segments.forEach {
            msg.addNonstandardSegment(it.name)
            populateSegment(it, hl7Data)
            // Don't send if we only have the ID field
            if (it.isEmpty || (it.encode().equals("${it.name}|${it[1]}"))) {
                msg.removeRepetition(it.name, 0)
            }
        }
        return this
    }

    abstract fun populateAll(hl7Data: HL7Data): AbstractMessageBuilder <T>
    fun build(): T = msg
}
