package mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure

import gov.afrl.batdok.encounter.observation.ChestRiseFallData
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup

fun chestRiseFallCode(endpoint: EndpointType) = when (endpoint){
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("268925001","Examination of respiratory system (procedure)")
    else -> null
}

fun chestLeftGreaterCode(endpoint: EndpointType) = when (endpoint) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("248561009", "Right side of chest moves less than left (finding)")
    else -> null
}

fun chestRightGreaterCode(endpoint: EndpointType) = when (endpoint) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("248560005", "Left side of chest moves less than right (finding)")
    else -> null
}

fun chestMovementSymmetricalCode(endpoint: EndpointType) = when (endpoint) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("366128006", "Finding of symmetry of chest movement (finding)")
    else -> null
}

fun chestRiseFallTypeFromCode(code: String) = when (code) {
    "248561009" -> ChestRiseFallData.Type.L_R.dataString
    "248560005" -> ChestRiseFallData.Type.R_L.dataString
    "366128006" -> ChestRiseFallData.Type.SYMMETRICAL.dataString
    else -> null
}
