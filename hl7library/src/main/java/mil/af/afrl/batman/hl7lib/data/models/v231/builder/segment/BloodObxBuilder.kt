package mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment

import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.observation.Blood
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.converter.TimeConverter.timestampToFormattedDateTime
import mil.af.afrl.batman.hl7lib.converter.UnitConverter
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getBloodAgeCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getBloodDINCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getBloodExpirationCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getBloodProductCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getBloodTypeCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getBloodVolumeCode
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ExtendedCodedElement
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ISBT
import mil.af.afrl.batman.hl7lib.data.models.v231.field.LOINC
import mil.af.afrl.batman.hl7lib.data.models.v231.field.TACBLOOD
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup
import org.openehealth.ipf.modules.hl7.kotlin.from
import org.openehealth.ipf.modules.hl7.kotlin.nrp
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter

fun populateObxFromBloodMed(parent: AbstractMessage, blood: Blood, endpoint: Endpoint) : List<OBX> {
    val obxList = mutableListOf<OBX>()
    var idx = 1

    fun addObx(value: String?, codeNameGroup: CodeNameGroup?, unit: String? = null, type: String = "NM",
               codeSystem: String = if (endpoint.format == EndpointType.OMDS) "" else LOINC) {
        buildBaseOBX(parent, blood.timestamp).apply {
            observationIdentifier.from(ExtendedCodedElement(parent, codeNameGroup ?: return, codeSystem))
            valueType.from(type)
            nrp(5).parse(value ?: return)
            setIDOBX.from(idx++)
            unit?.let { nrp(6).from(UnitConverter.batdokDataToCodedElement(parent, it, endpoint)) }
            obxList.add(this)
        }
    }

    // Will need separate OBX for name, type, expiration date, etc.
    val (bloodCode, codeSystem) = when (endpoint.format) {
        EndpointType.TAC -> {
            val cdpCode = getBloodProductCode(blood, endpoint)
            if (cdpCode?.code?.startsWith("E") == true) {
                Pair(cdpCode, ISBT)
            } else {
                Pair(cdpCode, TACBLOOD)
            }
        }
        EndpointType.OMDS -> Pair(getBloodProductCode(blood, endpoint), "")
        EndpointType.MHSG_T -> Pair(getBloodProductCode(blood, endpoint), LOINC)
    }
    if (bloodCode == null) return emptyList()
    if (endpoint.format == EndpointType.OMDS) {
        addObx(blood.bloodProduct, bloodCode, type = "TX")
        addObx("500", getBloodVolumeCode(endpoint), "ml")
    } else {
        // We need to add an actual blood volume into BATDOK. Default to 500 mL for now.
        addObx("500", bloodCode, "ml", codeSystem = codeSystem)
    }

    addObx(formatBloodExpDate(blood.expirationDate)?.ifEmpty { null }, getBloodExpirationCode(endpoint), type = "TS")
    addObx(blood.bloodType?.ifEmpty { null }, getBloodTypeCode(endpoint), type = "TX")
    // Pretty sure blood age is always days, change if needed
    addObx(blood.bloodAge?.toString(), getBloodAgeCode(endpoint), "days")
    addObx(blood.donationIdNumber?.ifEmpty { null }, getBloodDINCode(endpoint), type = "TX")

    return obxList
}

private fun formatBloodExpDate(date: String?) : String? {
    if (date.isNullOrEmpty()) return null
    // Formatted yyyy-MM-dd
    // Hacky adding time to the string to make sure it's always midnight UTC
    val instant = LocalDateTime.parse("$date 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
        .toInstant(ZoneOffset.UTC)
    return timestampToFormattedDateTime(instant)
}

private fun getBloodAgeParts(age: String?) : Pair<String?, String?> {
    val ageParts = age?.split(" ")
    if (ageParts == null || ageParts.size < 2) {
        return Pair(ageParts?.firstOrNull(), null)
    }
    // Change Batdok abbreviations to unit converter abbreviations
    val unitAbbreviation = when (ageParts[1].lowercase()) {
        "days" -> "days"
        "hrs" -> "hr"
        "mins" -> "minutes"
        else -> return Pair(ageParts.firstOrNull(), null)
    }
    return Pair(ageParts.firstOrNull(), unitAbbreviation)
}
