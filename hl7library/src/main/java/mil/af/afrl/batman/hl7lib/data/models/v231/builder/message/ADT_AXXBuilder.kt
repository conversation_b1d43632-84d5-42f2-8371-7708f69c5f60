package mil.af.afrl.batman.hl7lib.data.models.v231.builder.message

import ca.uhn.hl7v2.parser.DefaultModelClassFactory
import ca.uhn.hl7v2.parser.ModelClassFactory
import gov.afrl.batdok.encounter.Document
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.group.PR1OBXROLBuilder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.PIDBuilder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.PV1Builder
import mil.af.afrl.batman.hl7lib.util.HL7Data

abstract class ADT_AXXBuilder(messageCode: String, triggerEvent: String, private val endpoint: Endpoint, factory: ModelClassFactory? = DefaultModelClassFactory()): AbstractMessageBuilder<CustomADT_AXX>() {

    final override val msg = CustomADT_AXX(factory)

    init {
        populateBoilerplate(messageCode, triggerEvent, endpoint = endpoint)
        msg.name = "${messageCode}_$triggerEvent"
    }

    override fun populateAll(hl7Data: HL7Data): AbstractMessageBuilder<CustomADT_AXX> {
        if (hl7Data.document == null) return this
        populateSegment(PIDBuilder(endpoint), hl7Data)
        populateSegment(PV1Builder(endpoint, hl7Data.providerInfo), hl7Data)
        return this
    }

    fun populateProcedures(document: Document): AbstractMessageBuilder <CustomADT_AXX>  {
        PR1OBXROLBuilder(endpoint).buildPR1OBXROLFromPatient(msg, document).mapIndexed { idx, group ->
            msg.insertPR1ROL(group, idx)
        }
        return this
    }

}
