package mil.af.afrl.batman.hl7lib.data.models.v231.message

import ca.uhn.hl7v2.HL7Exception
import ca.uhn.hl7v2.model.v231.message.ADT_A31
import ca.uhn.hl7v2.parser.ModelClassFactory
import mil.af.afrl.batman.hl7lib.data.models.v231.group.ADT_A31_PR1OBXROL
import mil.af.afrl.batman.hl7lib.data.models.v231.group.DG1_ZDG

class ADT_A31(factory: ModelClassFactory) : ADT_A31(factory) {
    init {
        try {
            add(DG1_ZDG::class.java, false, true, 10)
            add(ADT_A31_PR1OBXROL::class.java, false, true, 12)
        } catch (e: HL7Exception) {
            throw RuntimeException("Failed to add custom groups to ADT_A31 message", e)
        }
    }

    fun getDg1ZdgAll() : List<DG1_ZDG> {
        return getAll("DG1_ZDG").filterIsInstance<DG1_ZDG>()
    }
}