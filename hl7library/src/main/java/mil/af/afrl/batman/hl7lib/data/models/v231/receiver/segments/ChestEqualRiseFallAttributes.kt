package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments

import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.observation.ChestRiseFallData
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.chestRiseFallTypeFromCode

fun chestEqualRiseFallAttributes(attributes: List<OBX>) : ChestRiseFallData {
    // Take the last item we receive, though there should really only ever be one
    var type: String? = null
    for (code in attributes.map { it.obx3_ObservationIdentifier.ce1_Identifier.value }) {
        type = chestRiseFallTypeFromCode(code) ?: continue
    }
    return ChestRiseFallData(type)
}
