package mil.af.afrl.batman.hl7lib.dataexchanger

import android.content.Context
import mil.af.afrl.batman.hl7lib.OmdsNetworkEndpoint
import mil.af.afrl.batman.hl7lib.MessageExporter
import mil.af.afrl.batman.hl7lib.util.OutboundHL7Data
import mil.af.afrl.batman.hl7lib.util.AuthTokenRequestor
import mil.af.afrl.batman.hl7lib.util.NetworkResponse

object OmdsDataExporter {

    suspend fun exportOmdsData(
        context: Context, hL7Data: OutboundHL7Data,
        refreshTokenInfo: String,
        onExportComplete: (NetworkResponse) -> Unit,
        endpoint: OmdsNetworkEndpoint,
        onTokenRefresh: ((String) -> Unit)
    ) {
        AuthTokenRequestor().getAuthToken(
            endpoint, refreshTokenInfo,
            onTokenAvailable = {
                hL7Data.authToken = it
                setExporterCallback(onExportComplete)
                MessageExporter.sendPatientMessages(hL7Data, context)
            },
            onTokenRefresh = { refreshToken ->
                onTokenRefresh.invoke(refreshToken)
            },
            onError = onExportComplete  // Just pass on the error here
        )
    }

    private fun setExporterCallback(
        onNetworkResponse: (NetworkResponse) -> Unit
    ) {
        MessageExporter.exportCompletedCallback = {
            onNetworkResponse(it)
            MessageExporter.exportCompletedCallback = null
        }
    }

}
