package mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.orur01

import ca.uhn.hl7v2.model.v231.group.ORU_R01_OBXNTE
import ca.uhn.hl7v2.model.v231.message.ORU_R01
import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.commands.proto.InfoCommands.ChangeOpenCloseEncounterCommand
import gov.afrl.batdok.encounter.Treatments
import gov.afrl.batdok.encounter.medicine.Medicine
import gov.afrl.batdok.encounter.metadata.Procedure
import gov.afrl.batdok.encounter.observation.Observation
import gov.afrl.batdok.util.NameFormatter
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.converter.TimeConverter
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.getInjuryData
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.adtObservations
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.noteObr24DiagnosticServiceSectionId
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.obx033NoteCodingScheme
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.transferNoteCode
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ExtendedCodedElement
import mil.af.afrl.batman.hl7lib.util.EscapeUtil
import mil.af.afrl.batman.hl7lib.util.HL7Data
import mil.af.afrl.batman.hl7lib.util.hashedWith
import org.openehealth.ipf.modules.hl7.kotlin.from
import org.openehealth.ipf.modules.hl7.kotlin.nrp
import java.time.Instant
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.TimeZone

class NoteOruBuilder(
    hl7Data: HL7Data,
    defaultTimestamp: Instant
) : BaseORU_R01Builder(hl7Data, defaultTimestamp) {

    override fun buildOrus(): List<ORU_R01> {
        val document = hl7Data.document!!
        val oru = buildBaseOruR01(
            hl7Data,
            defaultTimestamp,
            transferNoteCode(endpoint),
            document.id.hashedWith("allnotes"),
            obx033NoteCodingScheme(endpoint)
        )
        var idx = 0
        if (endpoint.format == EndpointType.OMDS) {
            oru.getObservationGroup().obr.obr24_DiagnosticServSectID.parse(
                noteObr24DiagnosticServiceSectionId(endpoint)
            )
        }

        fun buildObx(text: String, time: Instant?): OBX {
            return OBX(oru, oru.modelClassFactory).apply {
                obx1_SetIDOBX.from(idx + 1)
                obx2_ValueType.parse("TX")
                obx3_ObservationIdentifier.from(
                    ExtendedCodedElement(
                        oru,
                        transferNoteCode(endpoint),
                        obx033NoteCodingScheme(endpoint)
                    )
                )
                nrp(5).parse(EscapeUtil.escape(text))
                obx11_ObservationResultStatus.parse("F")
                obx14_DateTimeOfTheObservation.parse(
                    TimeConverter.timestampToFormattedDateTime(
                        time ?: defaultTimestamp
                    )
                )
            }
        }

        // Basic demographics and info
        val injuryTimestamp = if (document.info.timeInfo != null) {
            val dtFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")
            val injuryTime = LocalDateTime.ofInstant(
                document.info.timeInfo,
                TimeZone.getTimeZone("UTC").toZoneId()
            )
            injuryTime.format(dtFormatter) + "Z"
        } else null

        fun buildDemographicObx(title: String, data: Any?) {
            val obx = buildObx("$title: " + EscapeUtil.escape(data?.toString() ?: "Unknown"), null)
            val group = ORU_R01_OBXNTE(oru, oru.modelClassFactory).also { it.obx.from(obx) }
            oru.getObservationGroup().insertOBXNTE(group, idx++)
        }

        fun buildGenericObx(textValue: String) {
            val obx = buildObx(textValue, null)
            val group = ORU_R01_OBXNTE(oru, oru.modelClassFactory).also { it.obx.from(obx) }
            oru.getObservationGroup().insertOBXNTE(group, idx++)
        }

        if (endpoint.format != EndpointType.TAC) {
            buildDemographicObx(
                "Name",
                document.info.name?.toString(NameFormatter.Format.FIRST_MIDDLE_LAST)
            )
            buildDemographicObx("Provider", hl7Data.providerInfo?.name)
            buildDemographicObx("Patient EDIPI", document.info.dodId)
            buildDemographicObx("Injury Date/Time", injuryTimestamp)
            // TODO: Add PCL here when ready
        }
        for (event in document.events.list.filter { it.isCustom || it.eventType == "OTHER" }) {
            // Expand type abbreviations so we aren't sending them "ass"
            val expandedType = when (event.eventType?.lowercase()) {
                "sub" -> "subjective"
                "obj" -> "objective"
                "ass" -> "assessment"
                "plan" -> "plan"
                else -> null
            }
            val type = if (expandedType == null) "" else " ($expandedType)"
            val displayText = "${event.event}$type"
            val obx = buildObx(displayText, event.timestamp)
            val group = ORU_R01_OBXNTE(oru, oru.modelClassFactory).also { it.obx.from(obx) }
            oru.getObservationGroup().insertOBXNTE(group, idx++)
        }

        if (endpoint.format != EndpointType.TAC) {
            clinicalNoteSummaries(hl7Data).forEach { note -> buildGenericObx(note) }
        }

        oru.getObservationGroup().obr.universalServiceID.alternateText.parse("BATDOK R1 to R3 Note")

        return listOf(oru)
    }

    private fun clinicalNoteSummaries(hl7Data: HL7Data): List<String> {
        val stringList = mutableListOf<String>()
        val activeEncounterId = hl7Data.activeEncounterId ?: return emptyList()
        val activeDocument = hl7Data.getDocumentForEncounter(activeEncounterId) ?: return emptyList()
        val injuries = activeDocument.injuries
        val meds = activeDocument.medicines.list

        val historicalDocuments = hl7Data.documentsByEncounter
            .filterKeys { it != activeEncounterId }

        if (injuries.injuries.isNotEmpty()) {
            val injuryList = getInjuryData(injuries, hl7Data.mode.toString())
            stringList.addAll(buildSummaryList(
                header = "Injuries",
                total = injuryList.size,
                items = injuryList.mapIndexed { index, injury ->
                    val injuryLocation =
                        if (injury.location != "Default") "${injury.location} " else ""
                    "Injury #${index + 1}: $injuryLocation${injury.injury.injury}"
                }
            ))
        }

        val treatments = activeDocument.treatments
        val procedures = activeDocument.metadata.proceduresDone
        val observations = activeDocument.observations.list

        stringList.addAll(generateTreatmentSummary(treatments, procedures, observations))
        stringList.addAll(generateMedicationSummary(meds))

        /*if (historicalDocuments.isNotEmpty()) {
            stringList.addAll(
                buildHistoricalSummaryList(
                    hl7Data
                )
            )
        }*/

        return stringList
    }

    private fun buildSummaryList(header: String, total: Int, items: List<String>): List<String> {
        val summary = mutableListOf<String>()
        summary.add("______________ $header Summary List ______________")
        summary.add("Total number of $header: $total")
        summary.addAll(items)

        return summary
    }

    private fun buildHistoricalSummaryList(hl7Data: HL7Data): List<String> {
        val summary = mutableListOf<String>()
        summary.add("______________ Historical Summary List ______________")

        val historicalDocument = hl7Data.documentsByEncounter.map { (id, document) ->
            val commands = hl7Data.commandsByEncounter[id] ?: return@map null

            val lastOpenCloseCommand = commands
                .filter { it.data.`is`(ChangeOpenCloseEncounterCommand::class.java) }
                .map {
                    val command = it.data.unpack(ChangeOpenCloseEncounterCommand::class.java)
                    command to it.timestamp
                }
                .lastOrNull()

            if (lastOpenCloseCommand?.first?.open != false) {
                //if the encounter is still open or unknown, skip the encounter so that we only have closed encounters
                return@map null
            }

            val openTimestamp = commands
                .filter { it.data.`is`(ChangeOpenCloseEncounterCommand::class.java) }
                .firstNotNullOfOrNull {
                    val command = it.data.unpack(ChangeOpenCloseEncounterCommand::class.java)
                    it.timestamp.takeIf { command.open }
                }
            Pair(openTimestamp, document)
        }.filterNotNull()

        historicalDocument
            .sortedBy { (openTimestamp, _) -> openTimestamp }
            .forEach { (openTimestamp, document) ->
                val encounterTimestamp = document.info.timeInfo?.let {
                    val dtFormatter = DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm")
                    val encounterTime =
                        LocalDateTime.ofInstant(it, TimeZone.getTimeZone("UTC").toZoneId())
                    encounterTime.format(dtFormatter) + "Z"
                } ?: openTimestamp ?: "Unknown Time"

                summary.add("Encounter Date: $encounterTimestamp")

                val meds = document.medicines.list
                summary.addAll(generateMedicationSummary(meds, true))
                summary.addAll(
                    generateTreatmentSummary(
                        document.treatments,
                        document.metadata.proceduresDone,
                        document.observations.list,
                        true
                    )
                )
            }

        return summary
    }

    private fun generateMedicationSummary(
        meds: List<Medicine>,
        historical: Boolean = false
    ): List<String> {
        if (meds.isEmpty()) return emptyList()

        val medList = meds.mapIndexed { index, med ->
            val line = "Medication #${index + 1}: ${med.name} ${med.getVolumeString()} administered via ${med.route ?: "unknown route"} at ${med.administrationTime}"

            if (historical) "        - $line" else line
        }

        return if (historical) {
            listOf("    Medications:") + medList
        } else {
            buildSummaryList(
                header = "Medications",
                total = medList.size,
                items = medList
            )
        }
    }

    private fun generateTreatmentSummary(
        treatments: Treatments,
        procedures: List<Procedure>,
        observations: List<Observation>,
        historical: Boolean = false
    ): List<String> {
        val treatmentRemovals = treatments.onProperRemovedItems
        val filteredObservations = observations.filter { observation -> observation.name in adtObservations }

        val treatSize = treatments.list.size + treatmentRemovals.size + procedures.size + filteredObservations.size
        if (treatSize == 0) return emptyList()

        val treatmentDetails = mutableListOf<String>()

        treatmentDetails.addAll(treatments.list.mapIndexed { index, treatment ->
            val eventText = treatment.treatmentData?.toEventString()?.let { " ($it)" } ?: ""
            val line = "Treatment #${index + 1}: ${treatment.name}$eventText"
            if (historical) "        - $line" else line
        })

        treatmentDetails.addAll(treatmentRemovals.mapIndexed { index, treatment ->
            val eventText = treatment.second.treatmentData?.toEventString()?.let { " ($it)" } ?: ""
            val line = "Treatment #${index + treatments.list.size + 1}: Removed ${treatment.second.name} at ${treatment.first}$eventText"
            if (historical) "        - $line" else line
        })

        treatmentDetails.addAll(procedures.mapIndexed { index, procedure ->
            val eventText = procedure.data?.toEventString()?.let { " ($it)" } ?: ""
            val line = "Treatment #${index + treatments.list.size + treatmentRemovals.size + 1}: ${procedure.name}$eventText"
            if (historical) "        - $line" else line
        })

        treatmentDetails.addAll(filteredObservations.mapIndexed { index, observation ->
            val eventText = observation.observationData?.toEventText()?.let { " ($it)" } ?: ""
            val line = "Treatment #${index + treatments.list.size + treatmentRemovals.size + procedures.size + 1}: ${observation.name}$eventText"
            if (historical) "        - $line" else line
        })

        return if (historical) {
            listOf("    Treatments:") + treatmentDetails
        } else {
            buildSummaryList(
                header = "Treatments",
                total = treatSize,
                items = treatmentDetails
            )
        }
    }
}
