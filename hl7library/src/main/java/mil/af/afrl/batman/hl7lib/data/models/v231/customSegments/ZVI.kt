package mil.af.afrl.batman.hl7lib.data.models.v231.customSegments

import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.Group
import ca.uhn.hl7v2.model.v21.datatype.CM
import ca.uhn.hl7v2.model.v231.datatype.CE
import ca.uhn.hl7v2.model.v231.datatype.TS
import ca.uhn.hl7v2.parser.ModelClassFactory
import gov.afrl.batdok.encounter.Document
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.RepeatedSegmentBuilder
import mil.af.afrl.batman.hl7lib.util.HL7Data

typealias AdditionalVisitInformation = ZVI
class ZVI(parent: Group?, factory: ModelClassFactory? = null) : ExtendedAbstractSegment(parent, factory), RepeatedSegmentBuilder<ZVI> {
    init {
        addSingleField(20, "Trauma")
        addSingleField(26, "Trauma Date and Time", TS::class.java)
        addSingleField(26, "Last Trauma Date and Time", TS::class.java)
        addSingleField(200, "Referring Comment")
        addSingleField(255, "Chart Location", CM::class.java)
        addSingleField(20, "Service Category", CE::class.java)
        addBlankFields(2) // Skip fields 7 and 8
        addSingleField(20, "Admit Mode", CE::class.java)
        addBlankFields(2) // Skip fields 10 and 11
        addSingleField(20, "Triage Code", CE::class.java)
        addSingleField(26, "Triage Date and Time", TS::class.java)
    }

   /* override fun populateFromBatdokPatient(parent: AbstractMessage, patient: BatdokPatient): ZVI {
        if (patient.mechanismsOfInjury.isNotEmpty()) {
            val moiText = patient.mechanismsOfInjury.joinToString("&") { traumaTextFromMOI(it)}
            get(1).parse(moiText)
        }
        populateTime(patient)
        return this
    }*/

    override fun populateFromBatdokPatient(parent: AbstractMessage, hl7Data: HL7Data): ExtendedAbstractSegment {
        TODO("Not yet implemented")
    }

    override fun populateListFromBatdokPatient(parent: AbstractMessage, document: Document): List<ZVI> {
        TODO("Not yet implemented")
    }

    // The ICD doesn't use this as repeated, but I'm unsure if multiple MOIs should be compressed in one segment as above
    /*override fun populateListFromBatdokPatient(parent: AbstractMessage, patient: BatdokPatient): List<ZVI> {
        return patient.mechanismsOfInjury.map { moi ->
            val zvi = ZVI(parent, parent.modelClassFactory)
            zvi[1].parse(traumaTextFromMOI(moi))
            populateTime(patient)
            zvi
        }
    }*/

    /*private fun populateTime(patient: BatdokPatient) {
        val timestamp = patient.info.injuryTime
        if (timestamp != null) {
            get(2).parse(TimeConverter.timestampToFormattedDateTime(timestamp))
        }
    }*/

//    private fun traumaTextFromMOI(moi: MechanismOfInjury): String {
//        return if (!moi.name.isNullOrEmpty()) {
//            if (moi.location == null) {
//                "${moi.name}"
//            } else {
//                "${moi.name} - ${moi.location}"
//            }
//        } else {
//            ""
//        }
//    }
}
