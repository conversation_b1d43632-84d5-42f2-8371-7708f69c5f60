package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments

import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.treatment.LineData
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.getLineLocationFromCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.getLineSizeFromCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.getLineSubtypeFromCode
import org.openehealth.ipf.modules.hl7.kotlin.value

fun lineAttributes(treatmentCode: String, attributes: List<OBX>): LineData {
    var lineSide: String? = null
    val lineType: String? = when(treatmentCode) {
        "440009003" -> LineData.Type.IO.dataString
        "233527006" -> LineData.Type.CENTRAL.dataString
        "392231009" -> LineData.Type.PERIPHERAL.dataString
        "392247006" -> LineData.Type.ARTERIAL.dataString
        else -> null
    }
        
    var lineSubtype: String? = null
    var lineLocation: String? = null
    var lineSize: Float? = null
    var lineSizeUnit: String? = null
    var lineGauge: Float? = null
    var lineGaugeUnit: String? = null
    

    attributes.forEach {
        val code = it.obx3_ObservationIdentifier.ce1_Identifier?.value ?: return@forEach
        val value = it.obx5_ObservationValue.firstOrNull()?.value ?: return@forEach
        val subtype = getLineSubtypeFromCode(code)
        if (subtype != null) {
            lineSubtype = subtype
        }

        val sideLocation = lineType?.let { getLineLocationFromCode(code, it) }
        if (sideLocation != null) {
            lineSide = sideLocation.first
            lineLocation = sideLocation.second
        }

        val sizeGauge = getLineSizeFromCode(code)
        if (sizeGauge != null) {
            //todo add value if null or something
            if (lineType == LineData.Type.IO.dataString) {
                lineSize = if (sizeGauge == 0f) {
                    value.toFloat()
                } else sizeGauge
                lineSizeUnit = "mm"
            } else {
                lineGauge = if (sizeGauge == 0f){
                    value.toFloat()
                }else sizeGauge
                lineGaugeUnit = "mm"
            }
        }
    }

    return LineData(
        type = lineType,
        subtype = lineSubtype,
        side = lineSide,
        location = lineLocation,
        size = lineSize,
        sizeUnit = lineSizeUnit,
        gauge = lineGauge,
        gaugeUnit = lineGaugeUnit
    )
}