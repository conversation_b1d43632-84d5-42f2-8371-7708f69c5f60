package mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.orur01

import ca.uhn.hl7v2.model.v231.group.ORU_R01_OBXNTE
import ca.uhn.hl7v2.model.v231.message.ORU_R01
import gov.afrl.batdok.encounter.IndividualLab
import mil.af.afrl.batman.batdokid.DomainId
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.populateListFromBatdokLab
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.labObr04UniversalServiceCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.labObr24DiagnosticServiceCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.labObr4CodingScheme
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.labsByPanelCode
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup
import mil.af.afrl.batman.hl7lib.util.HL7Data
import mil.af.afrl.batman.hl7lib.util.hashedWith
import org.openehealth.ipf.modules.hl7.kotlin.from
import java.time.Instant

class LabOruBuilder(
    hl7Data: HL7Data,
    defaultTimestamp: Instant
) : BaseORU_R01Builder(hl7Data, defaultTimestamp) {

    override fun buildOrus(): List<ORU_R01> {
        val document = hl7Data.document ?: return emptyList()
        if (document.labs.list.isEmpty()) return emptyList()

        if (hl7Data.endpoint?.format != EndpointType.MHSG_T) {
            // Split ORU up by group...
            val panels = labsByPanelCode(document.labs.list)
            val orus = mutableListOf<ORU_R01>()
            for (panel in panels) {
                val code = panel.key
                val labs = panel.value
                orus += buildSingleLabOru(
                    hl7Data,
                    document.id.hashedWith(code.name),
                    code,
                    labs
                )
            }
            return orus
        }

        return listOf(
            buildSingleLabOru(
                hl7Data,
                document.id.hashedWith("labresults"),
                labObr04UniversalServiceCode(endpoint),
                document.labs.list
            )
        )
    }

    private fun buildSingleLabOru(hl7Data: HL7Data, id: DomainId, obr4Code: CodeNameGroup,
                                  labs: List<IndividualLab>) : ORU_R01 {
        val oru = buildBaseOruR01(hl7Data, defaultTimestamp, obr4Code, id, labObr4CodingScheme(hl7Data.endpoint?.format ?: EndpointType.MHSG_T))
        val obsGroupList = populateListFromBatdokLab(oru, labs, endpoint).map { obx ->
            ORU_R01_OBXNTE(oru, oru.modelClassFactory).also { it.obx.from(obx) }
        }
        obsGroupList.forEachIndexed { obsIdx, obsGroup ->
            oru.getObservationGroup().insertOBXNTE(obsGroup, obsIdx)
        }
        oru.getObservationGroup().obr.obr24_DiagnosticServSectID.parse(
            labObr24DiagnosticServiceCode(endpoint)
        )
        return oru
    }

}
