package mil.af.afrl.batman.hl7lib.data.models.v231.builder.message

import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.util.HL7Data

/**
 * Patient Discharge: https://hl7-definition.caristix.com/v2/HL7v2.3.1/TriggerEvents/ADT_A03
 */
class ADT_A03Builder(endpoint: Endpoint): ADT_AXXBuilder("ADT", "A03", endpoint) {

    override fun populateAll(hl7Data: HL7Data): AbstractMessageBuilder<CustomADT_AXX> {
        super.populateAll(hl7Data)
        return this
    }
}
