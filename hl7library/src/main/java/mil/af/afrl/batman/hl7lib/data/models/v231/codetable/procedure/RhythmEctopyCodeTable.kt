package mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure


import gov.afrl.batdok.encounter.observation.RhythmData
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup

fun rhythmEctopyCode(endpoint: EndpointType) = when(endpoint){
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("70878000","Electrocardiogram, rhythm (procedure)")
    else -> null 
}

//need the codes from cdp
fun rhythmTypeCode(type: String) = when (type){
    RhythmData.Type.NSR.dataString -> CodeNameGroup("64730000","Normal sinus rhythm (finding)")
    RhythmData.Type.ST.dataString -> CodeNameGroup("11092001","Sinus tachycardia (finding)")
    RhythmData.Type.SB.dataString -> CodeNameGroup("49710005","Sinus bradycardia (disorder)")
    RhythmData.Type.PEA.dataString -> CodeNameGroup("234172002","Electromechanical dissociation (disorder)")
    RhythmData.Type.PACED.dataString -> CodeNameGroup("10370003","Rhythm from artificial pacing (finding)")
    RhythmData.Type.ASYSTOLE.dataString -> CodeNameGroup("397829000","Asystole (disorder)")
    RhythmData.Type.AFIB.dataString -> CodeNameGroup("49436004","Atrial fibrillation (disorder)")
    RhythmData.Type.AFLUT.dataString -> CodeNameGroup("5370000"," Atrial flutter (disorder)")
    RhythmData.Type.SVT.dataString -> CodeNameGroup("6456007","Supraventricular tachycardia (disorder)")
    RhythmData.Type.VT.dataString -> CodeNameGroup("25569003","Ventricular tachycardia (disorder)")
    RhythmData.Type.VF.dataString -> CodeNameGroup("71908006","Ventricular fibrillation (disorder)")
    else -> null
}