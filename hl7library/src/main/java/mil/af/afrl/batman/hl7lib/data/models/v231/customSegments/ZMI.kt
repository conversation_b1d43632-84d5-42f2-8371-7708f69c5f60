package mil.af.afrl.batman.hl7lib.data.models.v231.customSegments

import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.Group
import ca.uhn.hl7v2.model.v231.datatype.NM
import ca.uhn.hl7v2.parser.ModelClassFactory
import mil.af.afrl.batman.hl7lib.util.HL7Data
import mil.af.afrl.batman.hl7lib.util.EscapeUtil
import org.openehealth.ipf.modules.hl7.kotlin.get

/* Person Military Information Segment defined in TheaterSync HL7 ICD
    1       ZMI Set ID                      R       Set to “1”
    2       Flying Status                   O       Yes, if patient has Work status of Flying Status
    3       Command Security Code Name      O       This field is not used
    4       Assigned Unit                   O       Patient's Unit
    5       Attached Unit                   O       This field is not used
    6       Assigned UIC                    O       Patient's UIC
    7       Attached UIC                    O       This field is not used

    Sample segment: "ZMI|1|||Awesome||ABC123"
 */
typealias PersonMilitaryInformation = ZMI
class ZMI(parent: Group?, factory: ModelClassFactory? = null) : ExtendedAbstractSegment(parent, factory) {

    init {
        addSingleField(true, 1, "ZMI Set ID", NM::class.java)
        addSingleField(false, 10, "Flying Status")
        addSingleField(false, 10, "Command Security Code")
        addSingleField(false, 10, "Assigned Unit")
        addSingleField(false, 10, "Attached Unit")
        addSingleField(false, 10, "Assigned UIC")
        addSingleField(false, 10, "Attached UIC")
    }

    override fun populateFromBatdokPatient(parent: AbstractMessage, hl7Data: HL7Data): ExtendedAbstractSegment {
        get(1).parse("1")
        get(4).parse(EscapeUtil.escape(hl7Data.document!!.info.unit ?: ""))
        return this
    }

}
