package mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure

import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup

fun pupilDilationCode(endpointType: EndpointType) = when (endpointType) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("363953003", "Size of pupil (observable entity)")
    else -> null
}

fun leftPupilDilationCode(endpointType: EndpointType) = when (endpointType) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("16089004", "Structure of pupil of left eye (body structure)")
    else -> null
}

fun rightPupilDilationCode(endpointType: EndpointType) = when (endpointType) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("52378001", "Structure of pupil of right eye (body structure)")
    else -> null
}

