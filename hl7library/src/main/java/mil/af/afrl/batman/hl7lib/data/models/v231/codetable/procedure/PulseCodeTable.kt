package mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure

import gov.afrl.batdok.encounter.observation.PulseValuesData
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup

fun pulseCode(endpoint: EndpointType) = when(endpoint){
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("410196005","Pulse taking management (procedure)")
    else -> null
}

fun pulseValueCode(value: String) = when (value){
    "brachial" -> CodeNameGroup("420340005","Brachial pulse, function (observable entity)")
    "carotid" -> CodeNameGroup("91214002","Carotid arterial pulse, function (observable entity)")
    "femoral" -> CodeNameGroup("67316000","Femoral pulse, function (observable entity)")
    "pedal" -> CodeNameGroup("421811005","Dorsalis pedis pulse, function (observable entity)")
    "radial" -> CodeNameGroup("65452004","Radial pulse, function (observable entity)")
    "temperature" -> CodeNameGroup("","")
    else -> null
}

fun pulseQualityCode(quality: String) = when (quality){
    PulseValuesData.Quality.A.dataString -> CodeNameGroup("","A")
    PulseValuesData.Quality.D.dataString -> CodeNameGroup("","D")
    PulseValuesData.Quality.ONE.dataString -> CodeNameGroup("","+1")
    PulseValuesData.Quality.TWO.dataString -> CodeNameGroup("","+2")
    PulseValuesData.Quality.THREE.dataString -> CodeNameGroup("","+3")
    else -> null
}