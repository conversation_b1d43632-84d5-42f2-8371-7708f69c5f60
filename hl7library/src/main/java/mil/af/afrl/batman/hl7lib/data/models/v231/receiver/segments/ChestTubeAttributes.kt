package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments

import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.treatment.ChestTubeData
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.cdpChestTubeSideFromCode

fun chestTubeAttributes(attributes: List<OBX>) : ChestTubeData {
    var location: String? = null
    
    for (code in attributes.map { it.obx3_ObservationIdentifier.ce1_Identifier.value }){
        val decodedLocation = cdpChestTubeSideFromCode(code)
        decodedLocation?.let { location = it }
    }
    
    return ChestTubeData(location = location)
}