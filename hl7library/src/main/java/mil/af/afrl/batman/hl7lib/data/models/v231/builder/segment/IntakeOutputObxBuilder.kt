package mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment

import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.IntakeOutput
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.obx033NoteCodingScheme
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.transferNoteCode
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ExtendedCodedElement
import org.openehealth.ipf.modules.hl7.kotlin.from
import org.openehealth.ipf.modules.hl7.kotlin.nrp


    fun populateObxFromIntakeOutput(
        parent: AbstractMessage,
        intakeOutput: IntakeOutput,
        endpoint: Endpoint
    ): List<OBX> {
        val obxList = mutableListOf<OBX>()
        var idx = 1

    fun addObx(value: String?, type: String = "TX") {
        buildBaseOBX(parent, intakeOutput.timestamp).apply {
            obx1_SetIDOBX.from(idx++)
            obx2_ValueType.from(type)
            obx3_ObservationIdentifier.from(
                ExtendedCodedElement(
                    parent,
                    transferNoteCode(endpoint),
                    obx033NoteCodingScheme(endpoint)
                )
            )
            nrp(5).parse(value ?: return)
            obx11_ObservationResultStatus.parse("F")
            obxList.add(this)
        }
    }

    val intakeOrOutput = if (intakeOutput.isIntake) "Intake" else "Output"
    val intakeOutputEventStr = "Logged $intakeOrOutput:\n${intakeOutput.toEventString()}"

    intakeOutputEventStr.lines().forEach { addObx(it) }

    return obxList
}