package mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment

import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.v231.segment.PV2
import mil.af.afrl.batman.hl7lib.util.HL7Data
import mil.af.afrl.batman.hl7lib.util.EscapeUtil

class PV2Builder : SegmentBuilder<PV2> {

    override fun populateFromBatdokPatient(parent: AbstractMessage, hl7Data: HL7Data): PV2 {
        val document = hl7Data.document
        val injuryNames = document?.injuries?.injuries?.values?.flatten()?.mapNotNull { it.injury } ?: emptyList()

        val fullChiefComplaint = injuryNames
            .filter { it.isNotEmpty() }
            .distinctBy { it }
            .joinToString { it }
            .ifEmpty { "Trauma" }
        // They can only take 60 characters for this field
        val truncatedChiefComplaint = if (fullChiefComplaint.length > 60) {
            fullChiefComplaint.take(57) + "..."
        } else {
            fullChiefComplaint
        }
        return PV2(parent, parent.modelClassFactory).apply {
            pv23_AdmitReason.ce2_Text.parse(EscapeUtil.escape(truncatedChiefComplaint))
        }
    }
}
