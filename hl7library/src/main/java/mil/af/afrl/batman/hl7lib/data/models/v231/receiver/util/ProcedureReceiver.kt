package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.util

import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.Group
import ca.uhn.hl7v2.model.v231.segment.PR1
import com.google.protobuf.Message
import gov.afrl.batdok.encounter.commands.buildAddTreatmentCommand
import gov.afrl.batdok.encounter.commands.buildLogObservationCommand
import gov.afrl.batdok.encounter.commands.buildUpdateProceduresCommand
import gov.afrl.batdok.encounter.observation.CommonObservations
import gov.afrl.batdok.encounter.pampi.Procedure
import gov.afrl.batdok.encounter.treatment.CommonTreatments
import gov.afrl.batdok.encounter.treatment.InboundHL7Data
import gov.afrl.batdok.encounter.treatment.TreatmentData
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.converter.BatdokDataConverter
import mil.af.afrl.batman.hl7lib.converter.TreatmentConverter
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.getProcedureFromTopLevelCode
import mil.af.afrl.batman.hl7lib.data.models.v231.field.SNOMED_CT
import mil.af.afrl.batman.hl7lib.data.models.v231.group.ADT_A28_PR1OBXROL
import mil.af.afrl.batman.hl7lib.data.models.v231.group.AbstractPR1OBXROL
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments.breathSoundsAttributes
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments.buildEttData
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments.buildEyeShieldData
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments.buildTqData
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments.casualtyPPEAttributes
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments.chestEqualRiseFallAttributes
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments.chestSealAttributes
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments.chestTubeAttributes
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments.dressingDataAttributes
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments.fingerThorAttributes
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments.foleyAttributes
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments.gastricTubeAttributes
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments.hypothermiaPreventionDataAttributes
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments.immobilizationAttributes
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments.lineAttributes
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments.needleDAttributes
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments.o2Attributes
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments.pelvicBinderAttributes
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments.pericardiocentesisAttributes
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments.perrlaDataAttributes
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments.visualAcuityTestAttributes
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments.woundPackingDataAttributes
import org.openehealth.ipf.modules.hl7.kotlin.from
import org.openehealth.ipf.modules.hl7.kotlin.value
import java.time.Instant

fun parsePr1Info(pr1s: List<PR1>, endpointType: EndpointType, instantBuilder: (String?) -> Instant?) : List<Message> {
    fun findMessage(child: Group) : AbstractMessage {
        return if (child is AbstractMessage) {
            child
        } else {
            findMessage(child.parent)
        }
    }
    val pr1Obxs = pr1s.map {
        val parentMessage = findMessage(it.parent)
        ADT_A28_PR1OBXROL(parentMessage, parentMessage.modelClassFactory).apply { pR1.from(it) }
    }
    return parsePr1ObxInfo(pr1Obxs, endpointType, instantBuilder)
}

fun parsePr1ObxInfo(pr1Obxs: List<AbstractPR1OBXROL>, endpointType: EndpointType, instantBuilder: (String?) -> Instant?) : List<Message> {
    val commands = mutableListOf<Message>()
    val addedRawProcedures = mutableListOf<Procedure>()
    val removedRawProcedures = mutableListOf<Procedure>()
    // If any treatments have been removed by BATDOK, PR1.3.5 will read "REMOVED"
    // We need to filter any treatments marked removed, then remove the original application of that
    //  treatment from the message. Only "REMOVED (treatment)" should appear in BATDOK.
    val removedTreats = pr1Obxs.filter { it.pr1.pr13_ProcedureCode.ce5_AlternateText.value == "REMOVED" }
    val mutablePr1Obxs = pr1Obxs.toMutableList()
    // Remove the first instance of any treatment that has been removed from the list of PR1s
    for (treat in removedTreats) {
        val treatDesc = treat.pr1.getTreatmentDescription()
        // Match the removed treatment with its applied counterpart by comparing
        //  procedure descriptions and making sure it isn't also the removed treatment
        val appliedTreatment = mutablePr1Obxs.find { it.pr1.getTreatmentDescription() != null
                && it.pr1.getTreatmentDescription() == treatDesc
                && it.pr1.pr13_ProcedureCode.ce5_AlternateText.value != "REMOVED"
        }
        mutablePr1Obxs.remove(appliedTreatment)
    }
    
    for (pr1Group in mutablePr1Obxs) {
        // CDP sends us meaningful SNOMED codes, so we can try to match on those
        val pr1 = pr1Group.pr1
        val treatCode = pr1.pr13_ProcedureCode.ce1_Identifier.value ?: ""

        val rawProcName = pr1.pr13_ProcedureCode.ce2_Text.value ?: "Unknown procedure"
        val timestamp = instantBuilder(pr1.pr15_ProcedureDateTime.value)
        val rawProcedure = Procedure(rawProcName, timestamp)
        if (pr1.pr13_ProcedureCode.ce5_AlternateText.value == "REMOVED") {
            removedRawProcedures += rawProcedure
        } else {
            addedRawProcedures += rawProcedure
        }

        if (endpointType != EndpointType.MHSG_T) {
            val obxSegments = pr1Group.obxs

            val batdokName = getProcedureFromTopLevelCode(endpointType, treatCode)
            val treatCommandDetails = when (batdokName) {
                CommonTreatments.LINE.dataString -> lineAttributes(treatCode, obxSegments)
                CommonTreatments.TQ.dataString -> buildTqData(obxSegments)
                CommonTreatments.CHEST_TUBE.dataString -> chestTubeAttributes(obxSegments)
                CommonTreatments.ET_TUBE.dataString -> buildEttData(obxSegments)
                CommonTreatments.O2.dataString -> o2Attributes(treatCode)
                CommonTreatments.DRESSING.dataString -> dressingDataAttributes(obxSegments, treatCode, endpointType)
                CommonTreatments.EYE_SHIELD.dataString -> buildEyeShieldData(obxSegments)
                CommonTreatments.HYPOTHERMIA_PREVENTION.dataString -> hypothermiaPreventionDataAttributes(obxSegments, treatCode)
                CommonTreatments.FOLEY_CATHETER.dataString -> foleyAttributes(obxSegments, endpointType)
                CommonTreatments.WOUND_PACKING.dataString -> woundPackingDataAttributes(obxSegments, endpointType)
                CommonTreatments.GASTRIC_TUBE.dataString -> gastricTubeAttributes(treatCode,endpointType)
                CommonTreatments.IMMOBILIZATION.dataString -> immobilizationAttributes(obxSegments, endpointType, treatCode)
                CommonTreatments.CHEST_SEAL.dataString -> chestSealAttributes(obxSegments)
                CommonTreatments.FINGER_THOR.dataString -> fingerThorAttributes(obxSegments)
                CommonTreatments.NEEDLE_D.dataString -> needleDAttributes(obxSegments)
                CommonTreatments.PERICARDIOCENTESIS.dataString -> pericardiocentesisAttributes(obxSegments, endpointType)
                CommonTreatments.PELVIC_BINDER.dataString -> pelvicBinderAttributes(obxSegments, endpointType)
                else -> null
            }
            
            val obsCommandDetails = when (batdokName){
                CommonObservations.PUPIL_DILATION.dataString -> perrlaDataAttributes(obxSegments)
                CommonObservations.CASUALTY_PPE.dataString -> casualtyPPEAttributes(obxSegments)
                "Visual Acuity Test" -> visualAcuityTestAttributes(obxSegments, endpointType)
                CommonObservations.BREATH_SOUNDS.dataString -> breathSoundsAttributes(obxSegments)
                CommonObservations.CHEST_EQUAL_RISE_FALL.dataString -> chestEqualRiseFallAttributes(obxSegments)
                else -> null
            }

            if (batdokName != null) {
                if (CommonObservations.entries.map { it.dataString }.contains(batdokName) || batdokName == "Visual Acuity Test"){
                    commands += buildLogObservationCommand(batdokName,obsCommandDetails, timestamp = instantBuilder(pr1.pr15_ProcedureDateTime.value))
                    continue
                }
                val name = if (pr1.pr13_ProcedureCode.ce5_AlternateText.value == "REMOVED") {
                    "REMOVED $batdokName"
                } else batdokName
                commands += buildAddTreatmentCommand(
                    name,
                    treatCommandDetails,
                    instantBuilder(pr1.pr15_ProcedureDateTime.value)
                )
                continue
            }

            val treat = TreatmentConverter.codeToBatdokData(treatCode, SNOMED_CT)
            if (treat != null) {
                val name = if (pr1.pr13_ProcedureCode.ce5_AlternateText.value == "REMOVED") {
                    "REMOVED ${treat.name}"
                } else treat.name
                commands += buildAddTreatmentCommand(treat.copy(name = name, timestamp = instantBuilder(pr1.pr15_ProcedureDateTime.value)))
                continue
            }
        }

        // I really don't care about the code that gets sent in here, because the CPT4 is so
        //  meaningless. Parse the textual info from the PR1.4 to build our treatment.
        val treatData = pr1.pr14_ProcedureDescription.value
        val treatName = pr1.pr14_ProcedureDescription.value?.substringBefore('(')?.trim()
            ?: pr1.pr13_ProcedureCode.ce2_Text.value
            ?: continue
        var batdokName = TreatmentConverter.map[BatdokDataConverter.normalize(treatName)]?.first ?: treatName
        if (pr1.pr13_ProcedureCode.ce5_AlternateText.value == "REMOVED") {
            batdokName = "REMOVED $batdokName"
        }
        val treatDetails = treatData?.replace(treatName, "")
            ?.substringAfter('(')?.substringBefore(')')?.trim() ?: ""
        val treatCommandDetails: TreatmentData? = if (treatDetails.isEmpty()) null else InboundHL7Data(treatDetails)

        commands += buildAddTreatmentCommand(batdokName, treatCommandDetails, instantBuilder(pr1.pr15_ProcedureDateTime.value))
    }
    return commands + buildUpdateProceduresCommand(addedRawProcedures, removedRawProcedures)
}

private fun PR1.getTreatmentDescription() : String? {
    return pr14_ProcedureDescription.value
        ?: pr13_ProcedureCode.ce2_Text.value
        ?: null
}
