package mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.orur01

import ca.uhn.hl7v2.model.v231.group.ORU_R01_PIDPD1NK1NTEPV1PV2ORCOBRNTEOBXNTECTI
import ca.uhn.hl7v2.model.v231.message.ORU_R01
import ca.uhn.hl7v2.model.v231.segment.OBR
import ca.uhn.hl7v2.model.v231.segment.ORC
import ca.uhn.hl7v2.parser.DefaultModelClassFactory
import mil.af.afrl.batman.batdokid.DomainId
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.converter.TimeConverter
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.AbstractMessageBuilder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.PIDBuilder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.PV1Builder
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.obr04CodingScheme
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.obr25ResultStatusCode
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ExtendedCodedElement
import mil.af.afrl.batman.hl7lib.data.models.v231.group.ORU_R01_ORCOBRNTEZUIOBXNTECTI
import mil.af.afrl.batman.hl7lib.data.models.v231.segment.ZUI
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup
import mil.af.afrl.batman.hl7lib.util.HL7Data
import mil.af.afrl.batman.hl7lib.util.asUuidString
import org.openehealth.ipf.modules.hl7.kotlin.from
import org.openehealth.ipf.modules.hl7.kotlin.get
import java.time.Instant

abstract class BaseORU_R01Builder(
    protected val hl7Data: HL7Data,
    protected val defaultTimestamp: Instant
) {

    protected val endpoint = hl7Data.endpoint ?: throw IllegalArgumentException("Endpoint must not be null")
    private val factory = DefaultModelClassFactory()

    abstract fun buildOrus() : List<ORU_R01>

    protected fun buildBaseOruR01(
        hl7Data: HL7Data, time: Instant, codeNameGroup: CodeNameGroup, id: DomainId?,
        obrCodingScheme: String = obr04CodingScheme(endpoint)
    ): ORU_R01 {
        // Basic builder to make it easier to build new ORUs
        class ORUBuilder : AbstractMessageBuilder<ORU_R01>() {
            override val msg = ORU_R01(factory)
            override fun populateAll(hl7Data: HL7Data) = this
        }

        fun buildOrcSegment(msg: ORU_R01): ORC {
            return ORC(msg, msg.modelClassFactory).apply {
                orc1_OrderControl.parse("CM")
            }
        }

        fun buildObrSegment(msg: ORU_R01): OBR {
            return OBR(msg, msg.modelClassFactory).apply {
                obr1_SetIDOBR.parse("1")
                if (endpoint.format == EndpointType.MHSG_T) {
                    obr3_FillerOrderNumber.from(id?.asUuidString() ?: "")
                }
                obr4_UniversalServiceID.from(
                    ExtendedCodedElement(
                        msg,
                        codeNameGroup,
                        obrCodingScheme
                    )
                )
                obr7_ObservationDateTime.parse(TimeConverter.timestampToFormattedDateTime(time))
                obr22_ResultsRptStatusChngDateTime.parse(
                    TimeConverter.timestampToFormattedDateTime(
                        time
                    )
                )
                obr25_ResultStatus.parse(obr25ResultStatusCode(endpoint))
            }
        }

        fun buildZuiSegment(msg: ORU_R01): ZUI {
            return ZUI(msg, msg.modelClassFactory).populateFromBatdokPatient(msg, hl7Data).apply {
                this[2].from(id.asUuidString())  // ZUI.2: Identifier
            }
        }

        return ORUBuilder()
            .populateMsh("ORU", "R01", endpoint)
            .build()
            .apply {
                // Adds PV1 and PID segments
                buildPatientResultGroup(hl7Data)
                // Adds observation group
                val obsGroup = ORU_R01_ORCOBRNTEZUIOBXNTECTI(this, modelClassFactory).also {
                    if (endpoint.format == EndpointType.OMDS) {
                        it.orc.from(buildOrcSegment(this@apply))
                    }
                    it.obr.from(buildObrSegment(this@apply))
                    it.zui.from(buildZuiSegment(this@apply))
                }
                pidpD1NK1NTEPV1PV2ORCOBRNTEOBXNTECTI.insertORCOBRNTEOBXNTECTI(obsGroup, 0)
            }
    }

    private fun ORU_R01.buildPatientResultGroup(hl7Data: HL7Data) {
        if (hl7Data.document == null) return
        with(ORU_R01_PIDPD1NK1NTEPV1PV2ORCOBRNTEOBXNTECTI(this, modelClassFactory)) {
            pidpD1NK1NTEPV1PV2.pid.from(
                PIDBuilder(endpoint).populateFromBatdokPatient(
                    this@buildPatientResultGroup,
                    hl7Data
                )
            )
            pidpD1NK1NTEPV1PV2.pV1PV2.pV1.from(
                PV1Builder(
                    endpoint,
                    hl7Data.providerInfo
                ).populateFromBatdokPatient(this@buildPatientResultGroup, hl7Data)
            )
            insertPIDPD1NK1NTEPV1PV2ORCOBRNTEOBXNTECTI(this, 0)
        }
    }

    protected fun ORU_R01.getObservationGroup() =
        pidpD1NK1NTEPV1PV2ORCOBRNTEOBXNTECTI.orcobrnteobxntecti

}
