package mil.af.afrl.batman.hl7lib.util

import android.util.Log
import batdok.batman.encryptionlibrary.A2AEncryptionTool
import com.google.protobuf.kotlin.toByteString
import com.google.zxing.BarcodeFormat
import gov.afrl.batdok.encounter.Document
import gov.afrl.batdok.util.NameFormatter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext
import mil.af.afrl.batman.hl7lib.proto.BeamOuterClass
import mil.af.afrl.batman.hl7lib.proto.BeamOuterClass.Integration
import mil.af.afrl.batman.hl7lib.proto.Gt
import gov.afrl.batdok.encounter.summary.AtmistSummaryGenerator
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.Base64
import java.util.Date
import java.util.UUID
import kotlin.math.min

class QrCodeGenerator {
    private var chunks = ArrayList<ByteArray>()
    lateinit var currentPatient: Document
    private val _processingPercentage = MutableStateFlow(0f)
    val processingPercentage = _processingPercentage.asStateFlow()

    companion object{
        val HL7_QR_HEADER = "HL7"
        fun GetBeamFromBytes(bytes: ByteArray): BeamOuterClass.Beam {
            val decodedBytes = Base64.getDecoder().decode(bytes)
            return BeamOuterClass.Beam.parseFrom(decodedBytes)
        }
    }

    suspend fun generatePatientQr(
        chunkSize: Int,
        data: String,
        dataType: Integration? = Integration.INTEGRATION_UNKNOWN
    ): List<Array<BooleanArray>> {

        chunks.clear()

        var isEncrypted = false
        var isCompressed = false

        val payloadData = when(dataType){
            Integration.INTEGRATION_GT -> {
                isEncrypted = true
                isCompressed = true
                val patientName = if (currentPatient.info.name != null) getFirstMiddleLastNames(
                    currentPatient.info.name!!.toString(NameFormatter.Format.FIRST_MIDDLE_LAST)
                ) else emptyList()

                val firstName = if (patientName.isEmpty()) "" else patientName.first()
                val lastName = if (patientName.isEmpty()) "" else patientName.last()
                val callsign = ""
                val ptDob =
                    if (currentPatient.info.dob != null) DateTimeFormatter.ofPattern("MM/dd/yyyy").withZone(ZoneId.of("UTC"))
                        .format(currentPatient.info.dob) else ""

                val messageBytes = Gt.GT.newBuilder()
                    .setMessagePayload(data)
                    .setMeta(
                        Gt.Meta.newBuilder().setPatientNameFirst(firstName)
                            .setPatientNameLast(lastName)
                            .setPatientCallsign(callsign)
                            .setPatientDob(ptDob)
                    ).build().toByteArray()

                A2AEncryptionTool(A2AEncryptionTool.DEFAULT_A2A_ENCRYPTION_KEY).encryptMessage(zipString(messageBytes))
            }
            Integration.INTEGRATION_CDP -> {
                isEncrypted = true
                isCompressed = true
                EncryptionUtil.encryptHl7(zipString(data.toByteArray(Charsets.UTF_8))!!, "opmed1776")
            }
            Integration.INTEGRATION_OMDS -> {
                isEncrypted = true
                isCompressed = true
                A2AEncryptionTool(A2AEncryptionTool.DEFAULT_A2A_ENCRYPTION_KEY).encryptMessage(zipString(data.toByteArray(Charsets.UTF_8)))

            }
            Integration.INTEGRATION_HALO -> {
                isEncrypted = true
                isCompressed = true
                A2AEncryptionTool(A2AEncryptionTool.DEFAULT_A2A_ENCRYPTION_KEY).encryptMessage(zipString(data.toByteArray(Charsets.UTF_8)))
            }
            Integration.INTEGRATION_ATMIST -> {
                // Generate ATMIST summary as raw JSON data (unencrypted, uncompressed)
                Log.d("QR_ATMIST", "Generating ATMIST summary for patient: ${currentPatient.id}")
                println("QR_ATMIST: Generating ATMIST summary for patient: ${currentPatient.id}")
                isEncrypted = false
                isCompressed = false
                val atmistGenerator = AtmistSummaryGenerator()
                val atmistSummary = atmistGenerator.generateAtmistSummary(currentPatient)
                val jsonData = atmistSummary.toJson()
                Log.d("QR_ATMIST", "Generated ATMIST JSON: $jsonData")
                println("QR_ATMIST: Generated ATMIST JSON: $jsonData")
                jsonData.toByteArray(Charsets.UTF_8)
            }
            else -> {
                data.toByteArray()
            }
        }


        if (payloadData!!.size > chunkSize) {
            for (chunk in payloadData.indices step chunkSize) {
                chunks.add(
                    payloadData.copyOfRange(
                        chunk,
                        min(chunk + chunkSize, payloadData.size)
                    )
                )
            }
        } else {
            chunks.add(payloadData)
        }

        val transferId = UUID.randomUUID().toString()
        Log.d("QR Gen", "total chunks: ${chunks.size}")

        val qrCodes = withContext(Dispatchers.IO) {
            val threadedQrs = mutableListOf<Array<BooleanArray>>()
            chunks.forEachIndexed { index, bytes ->

                val beam = BeamOuterClass.Beam.newBuilder()
                    .setIntegration(dataType)
                    .setTransferId(transferId)
                    .setIndex(index)
                    .setCount(chunks.size)
                    .setCreated(Date().time)
                    .setEncrypted(isEncrypted)
                    .setCompressed(isCompressed)
                    .setPayload(bytes.toByteString())
                    .build()

                val encodedBeam = Base64.getEncoder().encodeToString(beam.toByteArray())

                val encodedWithHeader = HL7_QR_HEADER + encodedBeam

                val qrEncoder =
                    createBarCode(encodedWithHeader.toByteArray(), BarcodeFormat.QR_CODE)
                threadedQrs.add(qrEncoder!!)
                Log.d("QR Gen", "Encoded $index of ${chunks.size}")
                _processingPercentage.value = index / chunks.size.toFloat()
            }
            threadedQrs
        }
        return qrCodes
    }


    private fun getFirstMiddleLastNames(name: String): List<String> {
        val names = name.split(" ")
        val firstMiddleLast = mutableListOf<String>()
        firstMiddleLast.add(names.first())
        if (names.size > 2) {
            val middleInitial = names[1].first().toString()
            firstMiddleLast.add(middleInitial)
        }
        if (names.size > 1) {
            firstMiddleLast.add(names.last())
        }
        return firstMiddleLast
    }
}
