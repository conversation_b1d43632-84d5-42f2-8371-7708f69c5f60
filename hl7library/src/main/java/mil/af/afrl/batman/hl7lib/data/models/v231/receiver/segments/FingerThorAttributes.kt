package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments

import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.treatment.FingerThorData
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.fingerThorSideFromCode

fun fingerThorAttributes(attributes: List<OBX>) : FingerThorData {
    var side: String? = null

    for (code in attributes.map { it.obx3_ObservationIdentifier.ce1_Identifier.value }) {
        side = fingerThorSideFromCode(code)
    }
    return FingerThorData(location = side)
}