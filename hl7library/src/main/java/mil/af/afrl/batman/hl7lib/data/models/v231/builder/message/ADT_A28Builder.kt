package mil.af.afrl.batman.hl7lib.data.models.v231.builder.message

import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.AL1Builder
import mil.af.afrl.batman.hl7lib.data.models.v231.group.DG1_ZDG
import mil.af.afrl.batman.hl7lib.data.models.v231.segment.ZEI
import mil.af.afrl.batman.hl7lib.data.models.v231.customSegments.ZMI
import mil.af.afrl.batman.hl7lib.data.models.v231.segment.ZPI
import mil.af.afrl.batman.hl7lib.util.HL7Data

class ADT_A28Builder(private val endpoint: Endpoint): ADT_AXXBuilder("ADT", "A28", endpoint) {
    override fun populateAll(hl7Data: HL7Data): AbstractMessageBuilder<CustomADT_AXX> {
        if (hl7Data.document == null) return this
        super.populateAll(hl7Data)

        populateCommonEncounterDataA28(hl7Data)
        msg.pV1.clear()
        return this
    }

    private fun populateCommonEncounterDataA28(hl7Data: HL7Data): AbstractMessageBuilder<CustomADT_AXX>  {
        val document = hl7Data.document ?: return this
        val procedures = document.treatments.list
        val treatments = hl7Data.document!!.treatments.list
        val removedTreats = hl7Data.document!!.treatments.onProperRemovedItems
        val observations = document.observations.list
        if (endpoint.format == EndpointType.MHSG_T) {
            populateRepeatedSegment(AL1Builder(endpoint), document)
        }

        if (endpoint.format != EndpointType.OMDS){
            val dg1ZdgGroups = dg1ZdgData(hl7Data)
            dg1ZdgGroups.forEachIndexed {index, group ->
                msg.insertDG1ZDG(group, index)
            }
            if (procedures.isNotEmpty() || observations.isNotEmpty()
                || treatments.isNotEmpty() || removedTreats.isNotEmpty()) {
                populateProcedures(document)
            }
        }

        populateCustomSegments(hl7Data, listOfNotNull(
            ZPI(msg, msg.modelClassFactory),
            ZEI(msg, msg.modelClassFactory),
            ZMI(msg, msg.modelClassFactory)
        ))
        return this
    }
    private fun dg1ZdgData(hl7Data: HL7Data): List<DG1_ZDG> {
        return DG1_ZDG.dg1ZdgData(hl7Data, endpoint, msg)
    }
}
