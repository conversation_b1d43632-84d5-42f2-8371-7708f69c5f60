package mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure

import gov.afrl.batdok.encounter.treatment.DressingData
import gov.afrl.batdok.encounter.treatment.WoundPackingData
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup

fun dressingCode(endpoint: EndpointType) = when (endpoint){
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("3895009", "Application of dressing (procedure)")
    else -> null
}

fun woundPackingCode(endpointType: EndpointType) = when (endpointType) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("241019003", "Packing of wound (procedure)")
    else -> null
}

fun pressureDressingCode(endpoint: EndpointType) = when (endpoint){
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("118414001","Pressure dressing, device (physical object)")
    else -> null
}

fun cdpDressingTypeCode(type: String?) = when (type){
    DressingData.Type.HEMOSTATIC.dataString, WoundPackingData.Type.HEMOSTATIC.dataString -> CodeNameGroup("261365000","Hemostatic gauze (physical object)")
    DressingData.Type.PRESSURE.dataString -> CodeNameGroup("118414001","Pressure dressing, device (physical object)")
    else -> if (!type.isNullOrEmpty()){
        CodeNameGroup("1581000124103", "Wound dressing type (observable entity)")
    }else null
}

fun cdpDressingTypeFromCode(code: String) = when (code){
    "261365000" -> DressingData.Type.HEMOSTATIC.dataString
    "118414001" -> DressingData.Type.PRESSURE.dataString
    else -> null
}

fun dressingLocationCode(endpoint: EndpointType) = when (endpoint){
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("1481000124102","Wound dressing observable (observable entity)")
    else -> null
}