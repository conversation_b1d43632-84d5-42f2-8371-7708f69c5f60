package mil.af.afrl.batman.hl7lib.converter

import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.v231.datatype.CE
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.data.models.v231.field.CS54
import mil.af.afrl.batman.hl7lib.data.models.v231.field.DHMSM_ICD
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ExtendedCodedElement

object UnitConverter : BatdokDataConverter<String>() {

    override fun String.toName() = this

    override fun buildType(name: String) = name

    // BATDOK uses different abbreviations for some of the items
    private val conversionMap = mapOf(
        "cc" to "cm3",
        "gm" to "g",
        "mcg/kg" to "mcg/kg/dose",
        "mu/ml" to "munit/ml",
        "u/min" to "unit/min",
        "U/min" to "unit/min"
    )

    // BATDOK should send us the "coded" value (the abbreviation). Use this to look up the full
    // name to send in the coded element.
    override fun batdokDataToCodedElement(message: AbstractMessage, data: String, endpoint: Endpoint): CE {
        val codeSystem = codeTypeForEndpoint(endpoint)
        val correctedData = conversionMap[data] ?: data
        val codedData = batdokDataToCodeData(correctedData, codeSystem)
        return if (codedData != null) {
            // If the lookup found a name, send the Batdok data as the code
            ExtendedCodedElement(message, correctedData, codedData.code, codeSystem)
        } else {
            // If no code exists, send the Batdok data as the text
            ExtendedCodedElement(message, "", correctedData, "")
        }
    }

    override fun codeTypeForEndpoint(endpoint: Endpoint): String {
        return when (endpoint.format) {
            EndpointType.MHSG_T -> DHMSM_ICD
            EndpointType.OMDS -> DHMSM_ICD
            EndpointType.TAC -> CS54
        }
    }

}
