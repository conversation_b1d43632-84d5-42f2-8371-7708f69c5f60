package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.message

import ca.uhn.hl7v2.model.Structure
import ca.uhn.hl7v2.model.v231.group.ORU_R01_ORCOBRNTEOBXNTECTI
import ca.uhn.hl7v2.model.v231.message.ORU_R01
import ca.uhn.hl7v2.model.v231.segment.OBX
import ca.uhn.hl7v2.model.v231.segment.PID
import ca.uhn.hl7v2.model.v231.segment.PV1
import gov.afrl.batdok.commands.proto.DocumentCommands.CommandData
import gov.afrl.batdok.encounter.EncounterVital
import gov.afrl.batdok.encounter.IndividualLab
import gov.afrl.batdok.encounter.IndividualVital
import gov.afrl.batdok.encounter.KnownEventTypes
import gov.afrl.batdok.encounter.VentSettings
import gov.afrl.batdok.encounter.commands.addVital
import gov.afrl.batdok.encounter.commands.buildAddEventCommand
import gov.afrl.batdok.encounter.commands.buildChangeHeightCommand
import gov.afrl.batdok.encounter.commands.buildChangeTriageCommand
import gov.afrl.batdok.encounter.commands.buildChangeWeightCommand
import gov.afrl.batdok.encounter.commands.buildEditEventCommand
import gov.afrl.batdok.encounter.commands.buildLogBloodCommand
import gov.afrl.batdok.encounter.commands.buildLogLabCommand
import gov.afrl.batdok.encounter.commands.buildLogObservationCommand
import gov.afrl.batdok.encounter.commands.buildLogVentCommand
import gov.afrl.batdok.encounter.commands.buildUpdateDispatchedEvacCommand
import gov.afrl.batdok.encounter.commands.buildUpdateMedicineCommand
import gov.afrl.batdok.encounter.commands.buildUpdateVitalCommand
import gov.afrl.batdok.encounter.ids.EncounterVitalId
import gov.afrl.batdok.encounter.ids.EventId
import gov.afrl.batdok.encounter.ids.VentId
import gov.afrl.batdok.encounter.medicine.Medicine
import gov.afrl.batdok.encounter.movement.EvacStatus
import gov.afrl.batdok.encounter.movement.TriageCategory
import gov.afrl.batdok.encounter.observation.Blood
import gov.afrl.batdok.encounter.observation.CommonObservations
import gov.afrl.batdok.encounter.observation.EFastExamData
import gov.afrl.batdok.encounter.panel.KnownLabs
import gov.afrl.batdok.encounter.vitals.Avpu
import gov.afrl.batdok.encounter.vitals.BloodPressure
import gov.afrl.batdok.encounter.vitals.CapRefill
import gov.afrl.batdok.encounter.vitals.EtCO2
import gov.afrl.batdok.encounter.vitals.GCS
import gov.afrl.batdok.encounter.vitals.HR
import gov.afrl.batdok.encounter.vitals.InvasiveBloodPressure
import gov.afrl.batdok.encounter.vitals.Output
import gov.afrl.batdok.encounter.vitals.Pain
import gov.afrl.batdok.encounter.vitals.Resp
import gov.afrl.batdok.encounter.vitals.SpO2
import gov.afrl.batdok.encounter.vitals.Temp
import gov.afrl.batdok.util.Patterns
import gov.afrl.batdok.util.format
import mil.af.afrl.batman.batdokid.DomainId
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.converter.ClinicalNoteConverter
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.absGranulocyteInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.absLymphocyteInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.absMonocycteInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.anionGapInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.arterialBeInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.arterialHco3InboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.arterialLacticAcidInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.arterialPaO2InboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.arterialPco2InboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.arterialPhInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.avpuInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.bloodObrCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.bnpInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.bpLocationInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.bunInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.capRefillInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.ckmbInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.clInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.co2InboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.creatInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.dbpInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.dibpInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.efastCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.etco2InboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.etohInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.evacPrecedenceObrCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.fio2InboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.g6pdInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.gcsEyeInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.gcsMotorInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.gcsVerbalInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getBatdokRouteFromInboundCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getBloodProductFromGtOmdsCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getBloodProductFromTacCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getGTAVPUResponse
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.gfapInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.glucometerInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.glucoseElectrolyteInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.granulocyteInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.hctInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.heartRateInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.heightInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.hgbInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.hrLocationInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.iCaInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.inboundBloodAgeCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.inboundBloodDinCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.inboundBloodExpCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.inboundBloodTypeCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.inboundTransferNoteCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.inrInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.interpretationCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.kInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.leftLungSlidingCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.leftUpperQuadrantCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.lymphocyteInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.mcvInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.modeInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.monocyteInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.mpvInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.naInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.outputInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.painInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.peepInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.pericardialFluidCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.pltInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.ptInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.rateInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.rbcInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.respInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.rightLungSlidingCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.rightUpperQuadrantCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.saO2InboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.sbpInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.sibpInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.spo2InboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.suprapubicFluidCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.tempInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.tempRouteInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.tidalInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.triageObrCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.troponin1InboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.uaBilirubinInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.uaBloodInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.uaGlucoseInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.uaKetonesInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.uaLeukocytesInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.uaNitriteInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.uaPhInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.uaProteinInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.uaSpecificGravityInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.uaUrobilinogenInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.uchl1InboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.venousBeInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.venousHco3InboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.venousLacticAcidInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.venousO2SatInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.venousPaO2InboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.venousPco2InboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.venousPhInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.ventObrCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.ventilatorInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.vitalObrCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.wbcInboundCodes
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.weightInboundCodes
import mil.af.afrl.batman.hl7lib.util.EscapeUtil
import mil.af.afrl.batman.hl7lib.util.generateDomainIdFromString
import mil.af.afrl.batman.hl7lib.util.hashedWith
import org.openehealth.ipf.modules.hl7.kotlin.get
import org.openehealth.ipf.modules.hl7.kotlin.value
import java.time.Instant
import kotlin.reflect.KClass

class ORU_R01Receiver(message: ORU_R01, endpointType: EndpointType) : BaseReceiver<ORU_R01>(message, endpointType) {
    override val pid: PID = message.pidpD1NK1NTEPV1PV2ORCOBRNTEOBXNTECTI.pidpD1NK1NTEPV1PV2.pid
    override val pv1: PV1 = message.pidpD1NK1NTEPV1PV2ORCOBRNTEOBXNTECTI.pidpD1NK1NTEPV1PV2.pV1PV2.pV1

    override fun getMessageCommands(): List<CommandData> {
        val allCommandsList = mutableListOf<CommandData>()

        // Should really only be one Patient Group (PID...CTI)
        val patientGroup = message.pidpD1NK1NTEPV1PV2ORCOBRNTEOBXNTECTI

        // We might still get multiple OBRs in a single ORU, so account for those
        for (orderGroup in patientGroup.orcobrnteobxntectiAll) {
            // Split processing from here based on OBR code
            val obrCode = orderGroup.obr.obr4_UniversalServiceID.ce1_Identifier.value
            val oruTime = parseInstant(orderGroup.obr.obr7_ObservationDateTime.value)

            val obr24 = orderGroup.obr.obr24_DiagnosticServSectID.value
            val commandSublist = when {
                obr24 == "GLB" -> processLabMessages(orderGroup, oruTime)
                obrCode == vitalObrCode(Endpoint.OMDS_DELTA).code -> processVitalsMessage(orderGroup, oruTime)
                obrCode in inboundTransferNoteCodes || ClinicalNoteConverter.codeToBatdokData(obrCode, Endpoint.OMDS_DELTA) != null -> processClinicalNoteMessage(orderGroup)
                obrCode == ventObrCode(Endpoint.OMDS_DELTA).code -> processVentMessages(orderGroup,oruTime)
                obrCode == evacPrecedenceObrCode(endpointType)?.code -> processEvacPrecedenceMessages(orderGroup)
                obrCode == triageObrCode(endpointType)?.code -> processTriageMessages(orderGroup)
                obrCode == bloodObrCode(Endpoint.TAC).code -> processBloodMessage(orderGroup, oruTime)
                obrCode == "8975-5" -> processCdpFluidMessage(orderGroup, oruTime)
                obrCode == efastCode(endpointType)?.code -> processEFastMessages(orderGroup, oruTime)
                // TODO: Update this. Seems that the right tag is now RAD in OBR.15
                // This has to happen after processing EFAST. It's also a rad, but we
                //  process it differently
                obr24 == "XR" || obrCode == "RADIOLOGY" -> processRadMessages(orderGroup, oruTime)
                else -> emptyList()
            }
            allCommandsList.addAll(commandSublist)
        }
        return allCommandsList
    }

    private fun processVitalsMessage(vitalData: ORU_R01_ORCOBRNTEOBXNTECTI, oruTime: Instant?): List<CommandData> {
        // TODO: Update codes here when we see what they actually send to us
        //  Right now using what was sent to us in the DCWs as filled into the coding table
        val vitalsCommands = mutableListOf<CommandData>()

        val vitalMap = mutableMapOf<KClass<out IndividualVital>, IndividualVital>()

        for (obx in vitalData.obxnteAll.map { it.obx }) {
            val obxData = getValueAndTime(obx)
            when (obx.obx3_ObservationIdentifier.ce1_Identifier.value) {
                in heightInboundCodes -> {
                    vitalsCommands.addCommand(obxData.timestamp) { buildChangeHeightCommand(obxData.data?.toFloat()) }
                }
                in weightInboundCodes -> {
                    vitalsCommands.addCommand(obxData.timestamp) { buildChangeWeightCommand(obxData.data?.toFloat()) }
                }
                in heartRateInboundCodes -> {
                    val currentHr = vitalMap[HR::class] as? HR
                    vitalMap[HR::class] = HR(obxData.data?.toInt(), currentHr?.pulseLocation)
                }
                in hrLocationInboundCodes -> {
                    val currentHr = vitalMap[HR::class] as? HR
                    vitalMap[HR::class] = HR(currentHr?.pulse, obxData.data)
                }
                in spo2InboundCodes -> vitalMap[SpO2::class] = SpO2(obxData.data?.toInt())
                in respInboundCodes -> vitalMap[Resp::class] = Resp(obxData.data?.toInt())
                in sbpInboundCodes -> {
                    val currentBp = vitalMap[BloodPressure::class] as? BloodPressure
                    vitalMap[BloodPressure::class] = BloodPressure(
                        obxData.data?.toInt(), currentBp?.diastolic, currentBp?.location)
                }
                in dbpInboundCodes -> {
                    val currentBp = vitalMap[BloodPressure::class] as? BloodPressure
                    vitalMap[BloodPressure::class] = BloodPressure(
                        currentBp?.systolic, obxData.data?.toInt(), currentBp?.location)
                }
                in bpLocationInboundCodes -> {
                    val currentBp = vitalMap[BloodPressure::class] as? BloodPressure
                    vitalMap[BloodPressure::class] =
                        BloodPressure(currentBp?.systolic, currentBp?.diastolic, obxData.data)
                }
                // MAP not tracked inbound, derived from BP
                in avpuInboundCodes -> {
                    val avpuResponse = getGTAVPUResponse(obxData.data)
                    vitalMap[Avpu::class] = Avpu(avpuResponse ?: obxData.data)
                }
                in painInboundCodes -> {
                    vitalMap[Pain::class] = when {
                        obxData.data == "NT" ->{
                            Pain(-1)
                        }
                        obxData.data?.toIntOrNull() != null -> {
                            Pain(obxData.data.toInt())
                        }
                        else -> {
                            Pain (extractNumber(obxData.data.toString()))
                        }
                    }
                }
                in etco2InboundCodes -> vitalMap[EtCO2::class] = EtCO2(obxData.data?.toInt())
                in tempInboundCodes -> {
                    val currentTemp = vitalMap[Temp::class] as? Temp
                    val unit = obx.obx6_Units.ce1_Identifier.value
                    var newTemp = obxData.data?.toFloat()
                    // BATDOK always records temperature in Fahrenheit
                    if (newTemp != null && listOf("deg c", "c", "celsius", "centigrade").any { unit.equals(it, true) }) {
                        newTemp = (newTemp * 1.8f) + 32
                    }
                    vitalMap[Temp::class] = Temp(newTemp, currentTemp?.measurementMethod)
                }
                in tempRouteInboundCodes -> {
                    val currentTemp = vitalMap[Temp::class] as? Temp
                    val batdokRoute = getBatdokRouteFromInboundCode(obx.obx3_ObservationIdentifier.ce1_Identifier.value)
                    vitalMap[Temp::class] = Temp(currentTemp?.temp, batdokRoute)
                }
                in sibpInboundCodes -> {
                    val currentBp = vitalMap[InvasiveBloodPressure::class] as? InvasiveBloodPressure
                    vitalMap[InvasiveBloodPressure::class] = InvasiveBloodPressure(
                        obxData.data?.toInt(), currentBp?.diastolic)
                }
                in dibpInboundCodes -> {
                    val currentBp = vitalMap[InvasiveBloodPressure::class] as? InvasiveBloodPressure
                    vitalMap[InvasiveBloodPressure::class] = InvasiveBloodPressure(
                        currentBp?.systolic, obxData.data?.toInt())
                }
                // No location for invasive blood pressure
                in outputInboundCodes -> vitalMap[Output::class] = Output(obxData.data?.toFloat())
                in capRefillInboundCodes -> vitalMap[CapRefill::class] = CapRefill(obxData.data?.toFloat())
                in gcsEyeInboundCodes -> {
                    val currentGcs = vitalMap[GCS::class] as? GCS
                    vitalMap[GCS::class] = GCS(obxData.data?.toIntOrNull() ?: 0, currentGcs?.verbal ?: 0, currentGcs?.motor ?: 0, 0)
                }
                in gcsMotorInboundCodes -> {
                    val currentGcs = vitalMap[GCS::class] as? GCS
                    vitalMap[GCS::class] = GCS(currentGcs?.eye ?: 0, currentGcs?.verbal ?: 0, obxData.data?.toIntOrNull() ?: 0, 0)
                }
                in gcsVerbalInboundCodes -> {
                    val currentGcs = vitalMap[GCS::class] as? GCS
                    vitalMap[GCS::class] = GCS(currentGcs?.eye ?: 0, obxData.data?.toIntOrNull() ?: 0, currentGcs?.motor ?: 0, 0)
                }
                // TODO: Add others if we have codes for them
            }
        }

        val vitalId = getDomainIdFromZui<EncounterVitalId>(vitalData.getAll("ZUI").firstOrNull())
        if (vitalMap.isNotEmpty()) {
            vitalsCommands.addCommand(oruTime) {
                buildUpdateVitalCommand(vitalId, oruTime) {
                    vitalMap.values.map(::addVital)
                }
            }
        }

        return vitalsCommands
    }

    private fun processClinicalNoteMessage(noteData: ORU_R01_ORCOBRNTEOBXNTECTI): List<CommandData> {
        val obr = noteData.obr
        val domainId =  when{
            noteData.nonStandardNames.contains("ZUI") -> {
                getDomainIdFromZui<EventId>(noteData.getAll("ZUI").firstOrNull())
            }
            !obr.obr3_FillerOrderNumber.value.isNullOrEmpty() -> {
                generateDomainIdFromString(obr.obr3_FillerOrderNumber.value)
            } else -> {
                DomainId.create<EventId>()
            }
        }

        // Need to add some additional info from the OBR at the start of the note text if present
        val noteHeaderBuilder = StringBuilder()
        val noteDescription = obr.obr4_UniversalServiceID.ce2_Text.value
        if (!noteDescription.isNullOrEmpty()) {
            noteHeaderBuilder.append(noteDescription)
        }
        val noteTitle = obr.obr4_UniversalServiceID.ce5_AlternateText.value
        if (!noteTitle.isNullOrEmpty()) {
            noteHeaderBuilder.append(" ($noteTitle)")
        }
        val noteHeader = noteHeaderBuilder.toString()

        val commands = mutableListOf<CommandData>()
        for (obxnte in noteData.obxnteAll) {
            val noteAndTime = getValueAndTime(obxnte.obx)
            noteAndTime.data ?: continue
            val noteText = if (noteHeader.isNotEmpty()) {
                "$noteHeader: ${noteAndTime.data}"
            } else {
                noteAndTime.data
            }.replace("~", "\n").trim()
            // Use EditEventCommand instead of AddEventCommand so we can put the right ID in
            //  the encounter event (in case we pull the same data again later)
            // Hash with noteText so that there are unique IDs for each event in the message
            commands.addCommand(noteAndTime.timestamp) { buildEditEventCommand(EventId(domainId.hashedWith(noteText).unique), noteAndTime.timestamp, EscapeUtil.unescape(noteText), true, KnownEventTypes.OTHER.name, true) }
        }
        return commands
    }

    private inline fun <reified T : DomainId> getDomainIdFromZui(zui: Structure?) : T {
        // Note I can't actually use the ZUI object we built here, since we can't cast the
        //  Structure to a ZUI unless they follow our exact HL7 model and we use that when
        //  we parse the inbound message. (It chokes on casting `zui as ZUI`.)
        val id = zui?.get(2)?.value
        return generateDomainIdFromString(id)
    }

    private fun processLabMessages(
        labData: ORU_R01_ORCOBRNTEOBXNTECTI,
        oruTime: Instant?
    ): List<CommandData> {
        val labCommands = mutableListOf<CommandData>()

        for (obxGroup in labData.obxnteAll) {
            val labComponents = mutableListOf<IndividualLab>()
            val obx = obxGroup.obx
            val labOBX = getValueAndTime(obx)
            val unit = obx.obx6_Units.ce1_Identifier.value?.let { obx.obx6_Units.ce1_Identifier.value }
            // Each OBX group should have a ZUI segment attached, but
            //  some systems don't send a ZUI
            val zui = if ("ZUI" in obxGroup.nonStandardNames) {
                obxGroup.getAll("ZUI").getOrNull(0)
            } else null
            val labType = when (obx.obx3_ObservationIdentifier.ce1_Identifier.value) {
                in wbcInboundCodes -> KnownLabs.WBC.dataString
                in hgbInboundCodes -> KnownLabs.HGB.dataString
                in hctInboundCodes -> KnownLabs.HCT.dataString
                in pltInboundCodes -> KnownLabs.PLT.dataString
                in rbcInboundCodes -> KnownLabs.RBC.dataString
                in mcvInboundCodes -> KnownLabs.MCV.dataString
                in mpvInboundCodes -> KnownLabs.MPV.dataString
                in absLymphocyteInboundCodes -> KnownLabs.ABS_LYMPHOCYTE.dataString
                in absMonocycteInboundCodes -> KnownLabs.ABS_MONOCYCLE.dataString
                in absGranulocyteInboundCodes -> KnownLabs.ABS_GRANULOCYTE.dataString
                in lymphocyteInboundCodes -> KnownLabs.LYMPHOCYTE.dataString
                in monocyteInboundCodes -> KnownLabs.MONOCYTE.dataString
                in granulocyteInboundCodes -> KnownLabs.GRANULOCYTE.dataString
                in naInboundCodes -> KnownLabs.NA.dataString
                in kInboundCodes -> KnownLabs.K.dataString
                in clInboundCodes -> KnownLabs.CL.dataString
                in iCaInboundCodes -> KnownLabs.ICA.dataString
                in glucoseElectrolyteInboundCodes -> KnownLabs.GLUCOSE_ELECTROLYTE_PANEL.dataString
                in anionGapInboundCodes -> KnownLabs.ANION_GAP.dataString
                in co2InboundCodes -> KnownLabs.TOTAL_CO2.dataString
                in bunInboundCodes -> KnownLabs.BUN.dataString
                in creatInboundCodes -> KnownLabs.CR.dataString
                in glucometerInboundCodes -> KnownLabs.GLUCOSE_FINGERSTICK.dataString
                in inrInboundCodes ->  KnownLabs.INR.dataString
                in ptInboundCodes -> KnownLabs.PT.dataString
                in arterialPhInboundCodes -> KnownLabs.ARTERIAL_PH.dataString
                in arterialPco2InboundCodes -> KnownLabs.ARTERIAL_PACO2.dataString
                in arterialPaO2InboundCodes -> KnownLabs.ARTERIAL_PAO2.dataString
                in arterialHco3InboundCodes -> KnownLabs.ARTERIAL_HCO3.dataString
                in saO2InboundCodes -> KnownLabs.SAO2.dataString
                in arterialBeInboundCodes -> KnownLabs.ARTERIAL_BE.dataString
                in arterialLacticAcidInboundCodes -> KnownLabs.ARTERIAL_LACTATE.dataString
                in venousPhInboundCodes -> KnownLabs.VENOUS_PH.dataString
                in venousPco2InboundCodes -> KnownLabs.VENOUS_PACO2.dataString
                in venousPaO2InboundCodes -> KnownLabs.VENOUS_PAO2.dataString
                in venousHco3InboundCodes -> KnownLabs.VENOUS_HCO3.dataString
                in venousO2SatInboundCodes -> KnownLabs.SVO2.dataString
                in venousBeInboundCodes -> KnownLabs.VENOUS_BE.dataString
                in venousLacticAcidInboundCodes -> KnownLabs.VENOUS_LACTATE.dataString
                in ckmbInboundCodes ->  KnownLabs.CKMB.dataString
                in bnpInboundCodes -> KnownLabs.BNP.dataString
                in troponin1InboundCodes -> KnownLabs.TROPONIN_1.dataString
                in gfapInboundCodes -> KnownLabs.GFAP.dataString
                in uchl1InboundCodes ->  KnownLabs.UCHL1.dataString
                in g6pdInboundCodes -> KnownLabs.G6PD.dataString
                in etohInboundCodes -> KnownLabs.ETOH.dataString
                in uaBloodInboundCodes -> KnownLabs.UA_BLOOD.dataString
                in uaKetonesInboundCodes -> KnownLabs.UA_KETONES.dataString
                in uaGlucoseInboundCodes -> KnownLabs.UA_GLUCOSE.dataString
                in uaLeukocytesInboundCodes -> KnownLabs.UA_LEUKOCYTES.dataString
                in uaProteinInboundCodes -> KnownLabs.UA_PROTEIN.dataString
                in uaNitriteInboundCodes -> KnownLabs.UA_NITRITES.dataString
                in uaBilirubinInboundCodes -> KnownLabs.UA_BILIRUBIN.dataString
                in uaUrobilinogenInboundCodes -> KnownLabs.UA_UROBILINOGEN.dataString
                in uaPhInboundCodes -> KnownLabs.UA_PH.dataString
                in uaSpecificGravityInboundCodes -> KnownLabs.UA_SPECIFIC_GRAVITY.dataString
                else -> {
                    // Unknown lab, take it in as a custom lab item
                    val labType = obx.obx3_ObservationIdentifier.ce2_Text.value
                    labType
                }
            }
            labComponents += IndividualLab(labType, labOBX.data, unit)
            labCommands.addCommand(oruTime) { buildLogLabCommand(labComponents) }
        }
        return labCommands
    }

    private fun processVentMessages(ventData: ORU_R01_ORCOBRNTEOBXNTECTI, oruTime: Instant?): List<CommandData>{
        val ventCommands = mutableListOf<CommandData>()
        val ventMap = mutableMapOf<String, VentSettings>()
        val ventId = getDomainIdFromZui<VentId>(ventData.getAll("ZUI").firstOrNull())
        var ventilator: String? = null
        var ventMode: String? = null
        var ventFio2: Double? = null
        var ventRate: Int? = null
        var ventTV: Int? = null
        var ventPEEP: Double? = null
        var associateVital: EncounterVital? = null

        for (obx in ventData.obxnteAll.map { it.obx }) {
            val obxData = getValueAndTime(obx)
            when (obx.obx3_ObservationIdentifier.ce1_Identifier.value) {
                in ventilatorInboundCodes -> {
                    ventilator = obxData.data
                }
                in modeInboundCodes -> {
                    ventMode = obxData.data
                }
                in rateInboundCodes -> {
                    ventRate = obxData.data?.toInt()
                }
                in tidalInboundCodes -> {
                    ventTV = obxData.data?.toInt()
                }
                in peepInboundCodes -> {
                    ventPEEP = obxData.data?.toDouble()
                }
                in fio2InboundCodes -> {
                    ventFio2 = obxData.data?.toDouble()
                }
            }
        }
        ventCommands.addCommand(oruTime) {
            buildLogVentCommand(VentSettings(ventId, oruTime, ventilator = ventilator, mode = ventMode, rate = ventRate, tv = ventTV, peep = ventPEEP, fio2 = ventFio2))
        }
        return ventCommands
    }

    private fun processRadMessages(radData: ORU_R01_ORCOBRNTEOBXNTECTI, oruTime: Instant?) : List<CommandData> {
        // Just concatenate the code description (OBX.3.2) with its value (OBX.5). This should handle
        //  findings and any sorts of comments they put on the rad read.
        // Sample: OBX|1|TX|Report^TSpineXRResults||Abnormal|||A|||F|||20240130175209+0000 -> TSpineXRResults: Abnormal
        val radStrings = mutableListOf<String>()
        for (obx in radData.obxnteAll.map { it.obx }) {
            val readingDescription = obx.obx3_ObservationIdentifier.ce2_Text.value ?: continue
            val readingResult = getValueAndTime(obx).data ?: continue
            radStrings += "$readingDescription: $readingResult"
        }

        return mutableListOf<CommandData>().apply {
            addCommand(oruTime) {
                buildAddEventCommand(oruTime, "Radiology report: " + radStrings.joinToString("; "),
                    true, KnownEventTypes.OTHER, true)
            }
        }
    }

    private fun processEvacPrecedenceMessages(evacData: ORU_R01_ORCOBRNTEOBXNTECTI) : List<CommandData> {
        // There should really only be one OBX in this message, but take the last value if more than one
        var evacStatus: EvacStatus? = null
        for (obx in evacData.obxnteAll.map { it.obx }) {
            val evacCode = obx.obx3_ObservationIdentifier.ce1_Identifier.value ?: continue
            evacStatus = when (evacCode) {
                "A" -> EvacStatus.URGENT
                "B" -> EvacStatus.URGENTSURGICAL
                "C" -> EvacStatus.PRIORITY
                "D" -> EvacStatus.ROUTINE
                "E" -> EvacStatus.CONVENIENCE
                else -> null
            }
        }

        val commandList = mutableListOf<CommandData>()
        if (evacStatus != null) {
            commandList.addCommand { buildUpdateDispatchedEvacCommand(evacStatus) }
        }
        return commandList
    }

    private fun processTriageMessages(evacData: ORU_R01_ORCOBRNTEOBXNTECTI) : List<CommandData> {
        // There should really only be one OBX in this message, but take the last value if more than one
        var evacCat: TriageCategory? = null
        for (obx in evacData.obxnteAll.map { it.obx }) {
            val evacCode = obx.obx3_ObservationIdentifier.ce1_Identifier.value ?: continue
            evacCat = when (evacCode) {
                "LA17696-8" -> TriageCategory.IMMEDIATE
                "LA17695-0" -> TriageCategory.DELAYED
                "LA17694-3" -> TriageCategory.MINIMAL
                "LA17697-6" -> TriageCategory.EXPECTANT
                else -> null
            }
        }

        val commandList = mutableListOf<CommandData>()
        if (evacCat != null) {
            // This actually selects the DIME value in BATDOK, which matches these codes
            commandList.addCommand { buildChangeTriageCommand(evacCat) }
        }
        return commandList
    }

    private fun processBloodMessage(bloodData: ORU_R01_ORCOBRNTEOBXNTECTI, oruTime: Instant?) : List<CommandData> {
        var bloodName = ""
        var bloodType = ""
        var bloodAge = ""
        var bloodDin = ""
        var bloodExpDate = ""
        var adminTime = oruTime
        val commands = mutableListOf<CommandData>()
        for (obx in bloodData.obxnteAll.map { it.obx }) {
            val code = obx.obx3_ObservationIdentifier.ce1_Identifier.value ?: continue
            val valueAndTime = getValueAndTime(obx)
            if (bloodName.isEmpty()) {
                bloodName = getBloodProductFromTacCode(code) ?: getBloodProductFromGtOmdsCode(code) ?: ""
                adminTime = valueAndTime.timestamp
            }
            if (code in inboundBloodTypeCodes) {
                bloodType = valueAndTime.data ?: continue
            }
            if (code in inboundBloodAgeCodes) {
                bloodAge = valueAndTime.data ?: continue
            }
            if (code in inboundBloodDinCodes) {
                bloodDin = valueAndTime.data ?: continue
            }
            if (code in inboundBloodExpCode) {
                bloodExpDate = parseInstant(valueAndTime.data)?.format(Patterns.ymd)?:""
            }
        }
        commands.addCommand(timestamp = adminTime) {
            buildLogBloodCommand(
                Blood(administrationTime = adminTime, bloodProduct = bloodName,
                    bloodType = bloodType, donationIdNumber = bloodDin, expirationDate = bloodExpDate,
                    bloodAge = bloodAge.toIntOrNull(), volume = 500L, unit = "mL"
                )
            )
        }
        return commands
    }

    private fun processCdpFluidMessage(fluidGroup: ORU_R01_ORCOBRNTEOBXNTECTI, oruTime: Instant?) : List<CommandData> {
        val commands = mutableListOf<CommandData>()
        for (obx in fluidGroup.obxnteAll.map { it.obx }) {
            val fluidName = when (obx.obx3_ObservationIdentifier.ce1_Identifier.value) {
                "1159317" -> "Normal saline (NS)"
                "847630" -> "Lactated ringers (LR)"
                "801112" -> "Plasmalyte"
                "798104" -> "rFVIIa"
                "1741421" -> "Albumin 5%"
                "1741377" -> "Albumin 25%"
                "1795616" -> "D5W"
                "1795609" -> "D5W"
                "1795610" -> "D5W"
                "1795607" -> "D5W"
                "1795346" -> "D5W"
                "1795250" -> "D51/2NS"
                "1863605" -> "D51/2NS+20meq KCL"
                "1807552" -> "0.45% saline"
                "847627" -> "D5LR"
                else -> continue
            }
            val dosage = obx.obx5_ObservationValue.firstOrNull()?.value?.toFloatOrNull()
            val unit = obx.obx6_Units.ce1_Identifier.value
            commands.addCommand {
                val med = Medicine(fluidName, volume = dosage, unit = unit, administrationTime = oruTime)
                buildUpdateMedicineCommand(med)
            }
        }
        return commands
    }

    private fun processEFastMessages(radData: ORU_R01_ORCOBRNTEOBXNTECTI, oruTime: Instant?) : List<CommandData> {
        // Each EFAST result is sent in two pieces, but the first contains the data from the seocnd
        // Only look at the first code and ignore the second...
        // Sample:
        // OBX|1|TX|3341006^Right lung structure (body structure)^SCT|1|Equivocal||||||F|||20250529191229+0000
        // OBX|2|ST|42425007^Equivocal (qualifier)^SCT|1|Equivocal
        var leftLungSliding: String? = null
        var rightLungSliding: String? = null
        var pericardialFluid: String? = null
        var rightUpperQuadrant: String? = null
        var leftUpperQuadrant: String? = null
        var suprapubicFluid: String? = null
        var interpretation: String? = null

        for (obx in radData.obxnteAll.map { it.obx }) {
            val code = obx.obx3_ObservationIdentifier?.ce1_Identifier?.value ?: continue
            val value = obx.obx5_ObservationValue.firstOrNull()?.value ?: continue
            when (code) {
                rightLungSlidingCode(endpointType)?.code -> rightLungSliding = value
                leftLungSlidingCode(endpointType)?.code -> leftLungSliding = value
                pericardialFluidCode(endpointType)?.code -> pericardialFluid = value
                rightUpperQuadrantCode(endpointType)?.code -> rightUpperQuadrant = value
                leftUpperQuadrantCode(endpointType)?.code -> leftUpperQuadrant = value
                suprapubicFluidCode(endpointType)?.code -> suprapubicFluid = value
                interpretationCode(endpointType)?.code -> interpretation = value
            }
        }

        return mutableListOf<CommandData>().apply {
            addCommand(oruTime) {
                buildLogObservationCommand(
                    name = CommonObservations.EFAST_EXAM.dataString,
                    data = EFastExamData(
                        leftLungSliding = leftLungSliding,
                        rightLungSliding = rightLungSliding,
                        pericardialFluid = pericardialFluid,
                        rightUpperQuadrant = rightUpperQuadrant,
                        leftUpperQuadrant = leftUpperQuadrant,
                        suprapubicFluid = suprapubicFluid,
                        interpretation = interpretation,
                    ),
                    timestamp = oruTime
                )
            }
        }
    }

    private fun getValueAndTime(obx: OBX): DataWithTimestamp {
        val value = obx.obx5_ObservationValue.joinToString(separator = "~") { it.value.toString() }
        val timestamp = parseInstant(obx.obx14_DateTimeOfTheObservation.value)

        return DataWithTimestamp(value, timestamp)
    }

    private fun extractNumber(field: String): Int? {
        //strips out a digit from a text string
        //example string "8 - this is a string text" will return a int of 8
        val regex = "\\d+".toRegex()
        val fieldResult =  regex.find(field)

        return fieldResult?.value?.toIntOrNull()
    }
}