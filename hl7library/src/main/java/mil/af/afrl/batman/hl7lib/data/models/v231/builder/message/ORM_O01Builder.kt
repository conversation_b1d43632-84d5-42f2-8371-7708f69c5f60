package mil.af.afrl.batman.hl7lib.data.models.v231.builder.message

import ca.uhn.hl7v2.model.v231.datatype.TQ
import ca.uhn.hl7v2.model.v231.message.ORM_O01
import ca.uhn.hl7v2.parser.DefaultModelClassFactory
import ca.uhn.hl7v2.parser.ModelClassFactory
import gov.afrl.batdok.encounter.medicine.Medicine
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.converter.MedicationConverter
import mil.af.afrl.batman.hl7lib.converter.RouteConverter
import mil.af.afrl.batman.hl7lib.converter.TimeConverter
import mil.af.afrl.batman.hl7lib.converter.UnitConverter
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.PIDBuilder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.PV1Builder
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.ormOrc01OrderControlCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.ormRxo04NameOfCodingSystem
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.ormRxr01NameOfCodingSystem
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ExtendedCodedElement
import mil.af.afrl.batman.hl7lib.data.models.v231.field.RxNorm
import mil.af.afrl.batman.hl7lib.util.HL7Data
import mil.af.afrl.batman.hl7lib.util.asUuidString
import mil.af.afrl.batman.hl7lib.util.nonBloodMeds
import org.openehealth.ipf.modules.hl7.kotlin.from
import java.time.Instant

class ORM_O01Builder(private val endpoint: Endpoint,private val factory: ModelClassFactory? = DefaultModelClassFactory()) {

    /*
    Definition: https://hl7-definition.caristix.com/v2/HL7v2.3/TriggerEvents/ORM_O01

    Fields filled (for administered meds):
    MSH
    Patient/PID
    Patient/Patient Visit/PV1
    Order/ORC (field 1, 5 (if administered), 7 (for true orders))
    Order/Order Detail/NTE (field 3) (for custom orders)
    Order/Order Detail/Order Detail Segment/OBR (field 4) (for custom orders)
    Order/Order Detail/Order Detail Segment/RXO (field 1, 2, 3, 4) (for administered meds and med orders)
    Order/Order Detail/Order Detail Segment/RXR (field 1) (non-standard for ORM_O01) (for administered meds and med orders)

    We could fill more data for the actual orders when we have those in A2A
     */

    fun populateAll(hl7Data: HL7Data): List<ORM_O01> {

        // TODO: Filter with getEncodableMeds if needed for certain endpoints.
        //  We risk losing med data if we do that and there is a med we don't
        //  have a code for though. It also risks losing med data if a route
        //  and dosage weren't filled at all.

        //val encodableOrders = getEncodableOrders(getOrdersByType(document.orders, true))
        //if (encodableMeds.isEmpty() && encodableOrders.isEmpty()) return null

        return buildMedicationMessageList(hl7Data)
    }

    private fun buildBaseOrmO01(hl7Data: HL7Data, med: Medicine) : ORM_O01{
        class ORMBuilder : AbstractMessageBuilder<ORM_O01>(){
            override val msg = ORM_O01(factory)
            override fun populateAll(hl7Data: HL7Data) = this
        }
        return ORMBuilder()
            .populateMsh("ORM", "O01", endpoint)
            .build()
            .apply {
                populatePatientData(hl7Data, this)
                populateMedicationData(med, this)
            }
    }

    private fun buildMedicationMessageList(hl7Data: HL7Data) : List<ORM_O01>{
        val document = hl7Data.document ?: return emptyList()
        val orms = mutableListOf<ORM_O01>()
        val encodableMeds = document.medicines.list.nonBloodMeds(hl7Data.endpoint?.format)

        for (med in encodableMeds) {
            val msg = buildBaseOrmO01(hl7Data, med)
            val rxo = msg.orcobrrqdrQ1ODSODTRXONTEDG1RXRRXCNTEOBXNTECTIBLG.obrrqdrQ1ODSODTRXONTEDG1RXRRXCNTEOBXNTE.rxo
            if (endpoint.format == EndpointType.MHSG_T && rxo.rxo1_RequestedGiveCode.ce1_Identifier.value.isEmpty()){
                continue
            }else{
                orms.add(msg)
            }
        }
        return orms
    }

    private fun populatePatientData(hl7Data: HL7Data, msg :ORM_O01){
        val patientDataGroup = msg.pidpD1NTEPV1PV2IN1IN2IN3GT1AL1
        patientDataGroup.pid.from(PIDBuilder(endpoint).populateFromBatdokPatient(msg, hl7Data))
        patientDataGroup.pV1PV2.pV1.from(PV1Builder(endpoint, hl7Data.providerInfo).populateFromBatdokPatient(msg, hl7Data))
    }

    private fun populateMedicationData(med: Medicine, msg: ORM_O01){
        fillOrc(med.administrationTime, med.id.asUuidString(), msg = msg)
        fillRxo(med, msg)
        fillRxr(med, msg)
    }

    private fun getEncodableMeds(meds: List<Medicine>, msg: ORM_O01) : List<Medicine> {
        // Need to be able to encode the med, route, and med unit to be encodable
        return meds.filter {
            MedicationConverter.batdokDataToCodedElement(msg, it, endpoint).identifier.toString() != "NOCODE"
                    && !UnitConverter.batdokDataToCodedElement(msg, it.unit ?: "", endpoint).identifier.isEmpty
                    && !RouteConverter.batdokDataToCodedElement(msg, it.route ?: "", endpoint).identifier.isEmpty
        }
    }

    private fun fillOrc(time: Instant?, medId: String? = null, interval: String? = null, msg: ORM_O01) {
        val orc = msg.orcobrrqdrQ1ODSODTRXONTEDG1RXRRXCNTEOBXNTECTIBLG.orc
        orc.apply {
            // This will just be a new order (NW)
            // See code table at https://hl7-definition.caristix.com/v2/HL7v2.3/Tables/0119
            orc.orc1_OrderControl.parse(ormOrc01OrderControlCode(endpoint))
            // Needed as per ICD
            orc.orc2_PlacerOrderNumber.ei1_EntityIdentifier.parse("RE")
            // Mark the order as in process (if it is an actual active order) or complete
            // (if it's a one-and-done med administered)
            // Code table https://hl7-definition.caristix.com/v2/HL7v2.3/Tables/0038
            // This value has a dependency on the interval, so it would be weird to pull
            //  into the code table file
            //todo need a new meckinisum for interval
            orc.orc5_OrderStatus.parse(if (interval == null) "CM" else "IP")
            if(medId != null){
                orc.orc2_PlacerOrderNumber.ei3_UniversalID.parse(medId)
            }
            if (interval != null) {
                // Repeat interval for the medication
                val tq = TQ(msg).also { it.tq2_Interval.parse(interval) }
                orc.orc7_QuantityTiming.from(tq)
            }
            if (time != null) {
                orc.orc9_DateTimeOfTransaction.from(TimeConverter.timestampToFormattedDateTime(time))
            }
        }
    }

    private fun fillRxo(med: Medicine, msg: ORM_O01) {
        val rxo = msg.orcobrrqdrQ1ODSODTRXONTEDG1RXRRXCNTEOBXNTECTIBLG.obrrqdrQ1ODSODTRXONTEDG1RXRRXCNTEOBXNTE.rxo
        rxo.apply {

            // Give code
            val giveCode = if (!med.rxcui.isNullOrEmpty()) {
                ExtendedCodedElement(msg, med.rxcui!!, med.exportName ?: med.name, RxNorm)
            } else {
                MedicationConverter.batdokDataToCodedElement(msg, med, endpoint)
            }

            if (giveCode.identifier.value == "NOCODE") {
                rxo.rxo1_RequestedGiveCode.ce2_Text.parse(med.name)
            } else {
                rxo.rxo1_RequestedGiveCode.from(giveCode)
            }
            // Give amount (min)--use this to handle the dosage volume
            // Min is supposed to be used for single drug weights
            rxo.rxo2_RequestedGiveAmountMinimum.from(med.volume)
            // Give units
            val cleanedUnit = when {
                med.unit.isNullOrEmpty() -> if (endpoint.format == EndpointType.OMDS) "unknown unit" else ""
                else -> med.unit
            }
            val unitCode = UnitConverter.batdokDataToCodedElement(msg, cleanedUnit!!, endpoint)
            rxo.rxo4_RequestedGiveUnits.apply {
                ce1_Identifier.from(unitCode.ce1_Identifier)
                ce2_Text.from(unitCode.text)
                ce3_NameOfCodingSystem.parse(ormRxo04NameOfCodingSystem(endpoint).takeIf { !unitCode.text.isEmpty })
            }
        }
    }

    private fun fillRxr(med: Medicine, msg : ORM_O01) {
        // This is required by the DHMSM ICD, but isn't listed in the published standard
        // Route
        val rxr = msg.orcobrrqdrQ1ODSODTRXONTEDG1RXRRXCNTEOBXNTECTIBLG.obrrqdrQ1ODSODTRXONTEDG1RXRRXCNTEOBXNTE.rxr
        val routeCode = RouteConverter.batdokDataToCodedElement(msg, med.route ?: "", endpoint)
        rxr.apply {
            rxr.route.apply {
                ce1_Identifier.from(routeCode.identifier)
                ce2_Text.from(routeCode.text)
                ce3_NameOfCodingSystem.from(ormRxr01NameOfCodingSystem(endpoint).takeIf { !routeCode.text.isEmpty })
            }
            if (endpoint.format == EndpointType.TAC) {
                rxr.rxr5_RoutingInstruction.parse(if (fluids.any { med.name.contains(it) }) "Fluid" else "Medication")
            }
        }
    }

    private val fluids = listOf(
        "3% Sodium Chloride",
        "Lactated Ringers",
        "Plasma-Lyte A",
        "0.9 % Sodium Chloride",
        "Sodium Chloride, 23.4%",
        "Dextrose 5% in Water"
    )
}
