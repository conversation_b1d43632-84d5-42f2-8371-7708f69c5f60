package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.message

import ca.uhn.hl7v2.model.v231.segment.PID
import ca.uhn.hl7v2.model.v231.segment.PV1
import gov.afrl.batdok.commands.proto.DocumentCommands.CommandData
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.data.models.v231.group.ADT_A31_PR1OBXROL
import mil.af.afrl.batman.hl7lib.data.models.v231.message.ADT_A31
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.util.parseAllergyCommand
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.util.parseInjuryInfo
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.util.parsePr1ObxInfo

class ADT_A31Receiver(message: ADT_A31, endpointType: EndpointType) : BaseReceiver<ADT_A31>(message, endpointType) {

    override val pid: PID = message.pid
    override val pv1: PV1 = message.pV1

    override fun getMessageCommands() : List<CommandData>{
        val commandsList = mutableListOf<CommandData>()

        commandsList.addCommand { parseAllergyCommand(message.aL1All) }

        val dg1ZdgCommands = parseInjuryInfo(message.getDg1ZdgAll(), endpointType)
        if (dg1ZdgCommands.isNotEmpty()){
            for (command in dg1ZdgCommands){
               commandsList.addCommand { command }
            }
        }

        parsePr1ObxInfo(message.getAll("PR1OBXROL").mapNotNull { it as? ADT_A31_PR1OBXROL }, endpointType, ::parseInstant)
            .map { commandsList.addCommand { it }}

        return commandsList
    }
}
