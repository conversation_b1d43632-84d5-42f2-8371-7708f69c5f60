package mil.af.afrl.batman.hl7lib.data.models.v231.codetable

import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType

//pv1.2 Patient Class - Assumed to be E for Emergency. In future, this will be dynamically determined by patient mode (TCCC, DNBI, etc)
// E = Emergency
//HH = Home Health
//IM = Inbox Message
//I = Inpatient
//OB = Observation
//O = Outpatient
//PM = Phone Msg
//P = Preadmit
//PD = Private Duty
//R = Recurring RS Research
//SN = Skilled Nursing
//WL = Wait List

fun pv102PatientClassCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.MHSG_T -> "E"
        EndpointType.OMDS -> "E"
        EndpointType.TAC -> "E"
    }

fun pv1034FacilityIDCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> "THEATER"
        EndpointType.OMDS -> "THEATER HX"
        EndpointType.TAC -> ""
    }

fun pv118PatientTypeCode() = "HX"

fun pv139ServicingFacilityCode(endpoint: Endpoint) = ""

fun pv141AccountStatusCode(endpoint: Endpoint)=
    when(endpoint.format){
        EndpointType.OMDS -> "D"
        EndpointType.MHSG_T -> ""
        EndpointType.TAC -> ""
    }
