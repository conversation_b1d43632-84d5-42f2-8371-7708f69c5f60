package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.message

import ca.uhn.hl7v2.model.v231.message.PPR_PC1
import ca.uhn.hl7v2.model.v231.segment.PID
import ca.uhn.hl7v2.model.v231.segment.PV1
import gov.afrl.batdok.commands.proto.DocumentCommands.CommandData
import gov.afrl.batdok.encounter.commands.buildUpdateProblemsCommand
import gov.afrl.batdok.encounter.pampi.Problem
import mil.af.afrl.batman.hl7lib.EndpointType
import org.openehealth.ipf.modules.hl7.kotlin.value

class PPR_PC1Receiver(message: PPR_PC1, endpointType: EndpointType) : BaseReceiver<PPR_PC1>(message, endpointType) {

    override val pid: PID = message.pid
    override val pv1: PV1 = message.pV1PV2.pV1

    override fun getMessageCommands(): List<CommandData> {
        val problems = mutableListOf<Problem>()

        for (prb in message.prbntevarrolvarpthvarobxntegolntevarrolvarobxnteorcobrrxontevarobxntevarAll.map { it.prb }) {
            val problemName = prb.prb3_ProblemID.ce2_Text.value ?: ""
            // This is technically a Coded Element, but I've only ever seen it have a single value
            val problemStatusField = prb.prb14_ProblemLifeCycleStatus
            val problemStatus = problemStatusField.ce2_Text.value ?: problemStatusField.ce1_Identifier.value ?: ""
            // PRB.7 is technically optional, but I've seen it in every sample message and it's the same as PRB.2
            val rawTime = prb.prb7_ProblemEstablishedDateTime.value ?: prb.prb2_ActionDateTime.value
            val problemInstant = parseInstant(rawTime)

            problems += Problem(problemName, problemStatus, problemInstant)
        }

        return mutableListOf<CommandData>().apply {
            addCommand {
                buildUpdateProblemsCommand(problems, emptyList())
            }
        }
    }

}
