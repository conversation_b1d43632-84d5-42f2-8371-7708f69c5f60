package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments

import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.treatment.PelvicBinderData
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.pelvicBinderTypeCode
import org.openehealth.ipf.modules.hl7.kotlin.value

fun pelvicBinderAttributes(attributes: List<OBX>, endpointType: EndpointType) : PelvicBinderData {
    var type = ""
    for (attr in attributes) {
        val code = attr.obx3_ObservationIdentifier.ce1_Identifier?.value ?: continue
        val value = attr.obx5_ObservationValue.firstOrNull()?.value ?: continue
        // Iterate through them all, take the last item if we get multiple
        if (code == pelvicBinderTypeCode(endpointType)?.code && value.isNotEmpty()) {
            type = value
        }
    }
    return PelvicBinderData(type)
}
