package mil.af.afrl.batman.hl7lib.util

import gov.afrl.batdok.commands.proto.DocumentCommands.CommandData
import gov.afrl.batdok.commands.proto.InfoCommands.ChangeOpenCloseEncounterCommand
import gov.afrl.batdok.encounter.Document
import gov.afrl.batdok.encounter.ids.DocumentId
import mil.af.afrl.batdokdata.id.EncounterId
import mil.af.afrl.batman.batdokid.DomainId
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.proto.BeamOuterClass.Integration
import kotlin.apply
import kotlin.collections.mapValues
import kotlin.collections.set
import kotlin.collections.toList
import kotlin.collections.toMutableList
import kotlin.collections.toMutableMap

class ProviderInfo(var name: String, var dodId: String)

abstract class HL7Data {
    abstract val exporting: Boolean
    private var documentOutdated = true
    private var documentByEncounterOutdated = true
    @Deprecated("deprecating for a multiple encounter solution") var commandsList: List<CommandData> =
        emptyList()
        set(value) {
            field = value
            documentOutdated = true
        }
    
    var commandsByEncounter: Map<EncounterId, List<CommandData>> = emptyMap()
        set(value) {
            field = value
            documentByEncounterOutdated = true
        }
    
    private var _activeEncounterId: EncounterId? = null
    var activeEncounterId: EncounterId?
        get() = _activeEncounterId ?: getOpenEncounterId() ?: commandsByEncounter.keys.firstOrNull()
        set(value) {
            _activeEncounterId = value
        }
    
    @Deprecated("deprecating for a multiple encounter solution")
    var document: Document? = null
        get() {
            if (this is OutboundHL7Data) {
                if (activeEncounterId == null) {
                    activeEncounterId =
                        getOpenEncounterId() ?: commandsByEncounter.keys.firstOrNull()
                }

                println("HL7Data: Active encounter ID: $activeEncounterId")
                println("HL7Data: Commands by encounter keys: ${commandsByEncounter.keys}")
                val commands = activeEncounterId?.let { commandsByEncounter[it] }
                println("HL7Data: Commands for encounter: ${commands?.size ?: 0} commands")
                println("HL7Data: Commands list: $commands")
                return Document(activeEncounterId?.copy() ?: DomainId.create()).apply {
                    handle(commands ?: emptyList())
                }
            }
            if (commandsList.isNotEmpty()) {
                if (documentOutdated) {
                    field = Document(DomainId.create()).apply { handle(commandsList) }
                    documentOutdated = false
                }
                return field
            }
            return null
        }
        private set

    var documentsByEncounter: Map<EncounterId, Document> = emptyMap()
        get() {
            if (documentByEncounterOutdated) {
                field = commandsByEncounter.mapValues { (encounterId, commandList) ->
                    val docID = DocumentId(encounterId.unique)
                    Document(docID).apply { handle(commandList) }
                }
                documentByEncounterOutdated = false
            }
            return field
        }
        private set
    
    var providerInfo: ProviderInfo? = null
    var endpoint: Endpoint? = null
    var batdokInstallationId: String? = null
    var mode: String? = null
    var externalIds: Map<String, String>? = null
    var authToken: String? = null
    var qrDataType: Integration = Integration.INTEGRATION_UNKNOWN
    
    fun getDocumentForEncounter(id:EncounterId): Document? {
        return documentsByEncounter[id]
    }
    fun getAllDocuments(id: EncounterId): List<Document> {
        return documentsByEncounter.values.toList()
    }
    
    //helper function to better handle new or existing encounterIDs
    fun addCommand(encounterId: EncounterId, command: CommandData){
        val update = commandsByEncounter.toMutableMap() // copy map so that it can modify
        val existing = update[encounterId]?.toMutableList() ?: mutableListOf() // get the current list otherwise create a new list
        existing.add(command)
        update[encounterId] = existing // updates the map entry
        commandsByEncounter = update // assign the updated map back to the map
    }

    fun getOpenEncounterId(): EncounterId? {
        return commandsByEncounter.entries.firstOrNull { (_, commands) ->
            val lastOpenCloseCommand = commands.sortedBy { it.timestamp }
                .filter { it.data.`is`(ChangeOpenCloseEncounterCommand::class.java) }
                .map { it.data.unpack(ChangeOpenCloseEncounterCommand::class.java) }
                .lastOrNull()
            lastOpenCloseCommand?.open == true
        }?.key
    }
}

class OutboundHL7Data : HL7Data() {
    override val exporting = true
    @Deprecated("deprecating for a multiple encounter solution") var encounterId: DocumentId = DomainId.create()
    var historicalHL7: String = ""
}

class InboundHL7Data : HL7Data() {
    override val exporting = false
    var patientId: String? = null
}
