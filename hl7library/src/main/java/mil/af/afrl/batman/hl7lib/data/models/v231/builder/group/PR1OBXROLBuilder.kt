package mil.af.afrl.batman.hl7lib.data.models.v231.builder.group

import androidx.annotation.VisibleForTesting
import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.v231.segment.PR1
import gov.afrl.batdok.encounter.Document
import gov.afrl.batdok.encounter.metadata.Procedure
import gov.afrl.batdok.encounter.observation.Observation
import gov.afrl.batdok.encounter.treatment.GastricTubeData
import gov.afrl.batdok.encounter.treatment.HypothermiaPreventionData
import gov.afrl.batdok.encounter.treatment.ImmobilizationData
import gov.afrl.batdok.encounter.treatment.Treatment
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.converter.ObservationConverter
import mil.af.afrl.batman.hl7lib.converter.ObservationConverter.procedureType
import mil.af.afrl.batman.hl7lib.converter.ProcedureConverter
import mil.af.afrl.batman.hl7lib.converter.ProcedureConverter.procedureType
import mil.af.afrl.batman.hl7lib.converter.TimeConverter
import mil.af.afrl.batman.hl7lib.converter.TreatmentConverter
import mil.af.afrl.batman.hl7lib.converter.TreatmentConverter.procedureType
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.ProcedureAttributeObxBuilder
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.adtObservations
import mil.af.afrl.batman.hl7lib.data.models.v231.group.ADT_AXX_PR1OBXROL
import mil.af.afrl.batman.hl7lib.util.EscapeUtil
import org.openehealth.ipf.modules.hl7.kotlin.from
import java.time.Instant

class PR1OBXROLBuilder(private val endpoint: Endpoint) {
    private val defaultTimestamp = Instant.now()

    fun buildPR1OBXROLFromPatient(parent: AbstractMessage, document: Document) : List<ADT_AXX_PR1OBXROL> {
        val attrBuilder = ProcedureAttributeObxBuilder(parent, endpoint)

        val treatmentPR1s = if (endpoint.format == EndpointType.OMDS) {
            val treatmentApplications = (document.treatments.list + document.treatments.onProperRemovedItems.map { it.second })
                .flatMap(::splitMultiTreatment)
            val treatmentRemovals = document.treatments.onProperRemovedItems
                .flatMap { splitMultiTreatment(it.second).map { splitTreat -> Pair(it.first, splitTreat) } }
            val allTreatments = (treatmentApplications + treatmentRemovals).sortedBy {
                when {
                    it is Treatment -> it.timestamp
                    (it is Pair<*, *> && it.first is Instant) -> it.first as Instant
                    else -> throw UnsupportedOperationException("Invalid treatment data")  // Shouldn't happen
                }
            }
            allTreatments.mapIndexed { idx, treatmentData ->
                when (treatmentData) {
                    is Treatment -> {
                        val obxs = attrBuilder.addTreatmentAttributes(treatmentData)
                        ADT_AXX_PR1OBXROL(parent, parent.modelClassFactory).apply {
                            pR1.from(buildTreatmentPR1(parent, idx, treatmentData))
                            obxs.mapIndexed { obx, idx -> this.insertOBX(idx, obx) }
                        }
                    }
                    is Pair<*, *> -> {
                        val obxs = attrBuilder.addTreatmentAttributes(treatmentData.second as Treatment)
                        ADT_AXX_PR1OBXROL(parent, parent.modelClassFactory).apply {
                            pR1.from(buildTreatmentPR1(parent, idx, treatmentData.second as Treatment, treatmentData.first as Instant))
                            obxs.mapIndexed { obx, idx -> this.insertOBX(idx, obx) }
                        }
                    }
                    else -> throw UnsupportedOperationException("Invalid treatment data")  // Shouldn't happen
                }
            }
        } else {
            document.treatments.list.flatMap(::splitMultiTreatment).mapIndexed { idx, treatment ->
                val obxs = attrBuilder.addTreatmentAttributes(treatment)
                ADT_AXX_PR1OBXROL(parent, parent.modelClassFactory).apply {
                    pR1.from(buildTreatmentPR1(parent, idx, treatment))
                    obxs.mapIndexed { obx, idx -> this.insertOBX(idx, obx) }
                }
            }
        }
        val procedurePR1s = document.metadata.proceduresDone
            .mapIndexed { idx, procedure ->
                buildProcedurePR1(parent, idx + treatmentPR1s.size, procedure)
            }
            .map {
                ADT_AXX_PR1OBXROL(parent, parent.modelClassFactory).apply { pR1.from(it) }
            }

        val observationPR1s = document.observations.list
            .mapIndexedNotNull { idx, observation ->
                val acceptedObservations = adtObservations
                if (observation.name in acceptedObservations){
                    val obxs = attrBuilder.addObservationAttributes(observation)
                    ADT_AXX_PR1OBXROL(parent,parent.modelClassFactory).apply {
                        pR1.from(buildObservationPR1(parent,idx + treatmentPR1s.size + procedurePR1s.size ,observation))
                        obxs.forEachIndexed { idx, obx -> this.insertOBX(obx, idx)  } 
                    }
                } else null
            }

        return treatmentPR1s + procedurePR1s + observationPR1s
    }

    // BATDOK needs to not log multiple sub-treatments on a single treatment...
    // Break these up while we wait for those to get fixed
    // TODO: Make this private again once the ImmobilizationData is fixed
    //   I just want test coverage of the ImmobilizationData break-up, the
    //   rest is sufficiently tested by our treatment mapping tests
    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    fun splitMultiTreatment(treatment: Treatment) : List<Treatment> {
        return when (treatment.treatmentData) {
            is HypothermiaPreventionData -> {
                // All the types are jammed into one comma-delimited list for some reason
                return treatment.getData<HypothermiaPreventionData>()?.type
                    ?.split(", ")
                    ?.map { treatment.copy(treatmentData = HypothermiaPreventionData(it)) }
                    ?: emptyList()
            }
            is GastricTubeData -> {
                return treatment.getData<GastricTubeData>()?.type
                    ?.split(", ")
                    ?.map {treatment.copy(treatmentData = GastricTubeData(it))}
                    ?: emptyList()
            }
            else -> listOf(treatment)
        }
    }


    private fun buildTreatmentPR1(parent: AbstractMessage, idx: Int, treatment: Treatment, removalTime: Instant? = null) : PR1 {
        return PR1(parent, parent.modelClassFactory).apply {
            pr11_SetIDPR1.parse((idx + 1).toString())
            pr13_ProcedureCode.from(TreatmentConverter.batdokDataToCodedElement(parent, treatment, endpoint))
            if (removalTime != null) {
                pr13_ProcedureCode.ce5_AlternateText.value = "REMOVED"
            }
            val eventText = if (treatment.treatmentData != null) " (${treatment.treatmentData?.toEventString()})" else ""
            val treatmentDescription = "${treatment.name}$eventText"
            pr14_ProcedureDescription.parse(EscapeUtil.escape(treatmentDescription))
            val timestamp = removalTime ?: treatment.timestamp
            pr15_ProcedureDateTime.parse(timestamp.let { TimeConverter.timestampToFormattedDateTime(it) })
            pr16_ProcedureFunctionalType.parse(treatment.procedureType())
        }
    }

    private fun buildProcedurePR1(parent: AbstractMessage, idx: Int, procedure: Procedure) : PR1 {
        return PR1(parent, parent.modelClassFactory).apply {
            pr11_SetIDPR1.parse((idx + 1).toString())
            pr13_ProcedureCode.from(ProcedureConverter.batdokDataToCodedElement(parent, procedure, endpoint))
            val eventText = if (procedure.data != null) " (${procedure.data?.toEventString()})" else ""
            val treatmentDescription = "${procedure.name}$eventText"
            pr14_ProcedureDescription.parse(EscapeUtil.escape(treatmentDescription))
            // No timestamp attached to these procedures, say they are done now instead
            pr15_ProcedureDateTime.parse(TimeConverter.timestampToFormattedDateTime(defaultTimestamp))
            pr16_ProcedureFunctionalType.parse(procedure.procedureType())
        }
    }

    private fun buildObservationPR1(parent: AbstractMessage, idx: Int, observation: Observation) : PR1 {
        return PR1(parent, parent.modelClassFactory).apply {
            pr11_SetIDPR1.parse((idx + 1).toString())
            pr13_ProcedureCode.from(ObservationConverter.batdokDataToCodedElement(parent, observation, endpoint))
            val eventText = observation.observationData?.toEventText()?.let { " ($it)" } ?: ""
            val cleanedText =  "${observation.name?.replace("&", "and")}$eventText"
            pr14_ProcedureDescription.parse(EscapeUtil.escape(cleanedText))
            pr15_ProcedureDateTime.parse(observation.timestamp.let { TimeConverter.timestampToFormattedDateTime(it) })
            pr16_ProcedureFunctionalType.parse(observation.procedureType())
        }
    }

}
