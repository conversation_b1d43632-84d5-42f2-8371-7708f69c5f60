package mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure

import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup

fun visualAcuityTestCode(endpoint: EndpointType) = when (endpoint) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("16830007","Visual acuity testing (procedure)")
    else -> null
}

fun visualAcuityTestTextDataCode(endpoint: EndpointType) = when (endpoint){
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("260246004","Visual acuity finding (finding)")
    else -> null
}