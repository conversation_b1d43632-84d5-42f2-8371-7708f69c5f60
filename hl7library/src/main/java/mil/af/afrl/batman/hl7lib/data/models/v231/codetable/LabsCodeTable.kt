package mil.af.afrl.batman.hl7lib.data.models.v231.codetable

import gov.afrl.batdok.encounter.IndividualLab
import gov.afrl.batdok.encounter.panel.KnownLabs
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.data.models.v231.field.GENESIS_ALIAS
import mil.af.afrl.batman.hl7lib.data.models.v231.field.LOINC
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup

fun labObr04UniversalServiceCode(endpoint: Endpoint) = CodeNameGroup("19146-0", "Referral lab test results")
fun labObr4CodingScheme(endpointType: EndpointType) = when (endpointType) {
    EndpointType.MHSG_T -> LOINC
    EndpointType.TAC -> GENESIS_ALIAS
    EndpointType.OMDS -> ""
}
fun labObr24DiagnosticServiceCode(endpoint: Endpoint) = "GLB"
fun labObr25ResultStatus(endpoint: Endpoint) = "F"

// @return Pair of OBR.4 code with the list of assoc'd labs for that particular group
fun labsByPanelCode(labs: List<IndividualLab>) : Map<CodeNameGroup, List<IndividualLab>> {
    val panelMap = mutableMapOf<CodeNameGroup, List<IndividualLab>>()
    for (lab in labs) {
        // Find the correct OBR.4 code for the lab... ignore it if none found
        val obr4Code = getObr4ForLab(lab.name) ?: continue
        // Put it in the map for that particular code
        panelMap[obr4Code] = (panelMap[obr4Code] ?: emptyList()).plus(listOf(lab))
    }
    return panelMap
}

private fun getObr4ForLab(lab: String) : CodeNameGroup? {
    return when (lab) {
        in listOf(KnownLabs.WBC.dataString, KnownLabs.HGB.dataString, KnownLabs.HCT.dataString,
            KnownLabs.PLT.dataString, KnownLabs.RBC.dataString, KnownLabs.MCV.dataString,
            KnownLabs.MPV.dataString) -> {
                CodeNameGroup("2921414", "CBC w/ Diff")
        }
        in listOf(KnownLabs.ABS_LYMPHOCYTE.dataString, KnownLabs.ABS_MONOCYCLE.dataString,
            KnownLabs.ABS_GRANULOCYTE.dataString, KnownLabs.LYMPHOCYTE.dataString,
            KnownLabs.MONOCYTE.dataString, KnownLabs.GRANULOCYTE.dataString) -> {
                CodeNameGroup("101239539", "*Differential Automated")
            }
        in listOf(KnownLabs.NA.dataString, KnownLabs.K.dataString, KnownLabs.CL.dataString,
            KnownLabs.ICA.dataString, KnownLabs.GLUCOSE_ELECTROLYTE_PANEL.dataString,
            KnownLabs.ANION_GAP.dataString, KnownLabs.TOTAL_CO2.dataString, KnownLabs.BUN.dataString,
            KnownLabs.CR.dataString) -> {
                CodeNameGroup("2921288", "Basic Metabolic Panel")
            }
        KnownLabs.GLUCOSE_FINGERSTICK.dataString -> {
            CodeNameGroup("2921212", "Glucose Level, Random")
        }
        KnownLabs.INR.dataString, KnownLabs.PT.dataString -> {
            CodeNameGroup("112472257", "Prothrombin Time (PT) LC005199")
        }
        KnownLabs.ARTERIAL_PH.dataString, KnownLabs.ARTERIAL_PACO2.dataString, KnownLabs.ARTERIAL_PAO2.dataString,
            KnownLabs.ARTERIAL_HCO3.dataString, KnownLabs.SAO2.dataString, KnownLabs.ARTERIAL_BE.dataString,
            KnownLabs.ARTERIAL_LACTATE.dataString -> {
                CodeNameGroup("38568495", "Arterial Blood Gas")
            }
        KnownLabs.VENOUS_PH.dataString, KnownLabs.VENOUS_PACO2.dataString, KnownLabs.VENOUS_PAO2.dataString,
            KnownLabs.VENOUS_HCO3.dataString, KnownLabs.SVO2.dataString, KnownLabs.VENOUS_BE.dataString,
            KnownLabs.VENOUS_LACTATE.dataString -> {
                CodeNameGroup("2921402", "Venous Blood Gas")
            }
        KnownLabs.CKMB.dataString -> {
            CodeNameGroup("111923143", "Creatine Kinase (CK) MB LC120816")
        }
        KnownLabs.BNP.dataString -> {
            CodeNameGroup("21979275", "Pro B-Type Natriuretic Peptide (ProBNP")
        }
        KnownLabs.TROPONIN_1.dataString -> {
            CodeNameGroup("22348059", "Cardiac Panel")
        }
        KnownLabs.GFAP.dataString -> {
            CodeNameGroup("2807943289", "Traumatic Brain Injury Biomarker")
        }
        KnownLabs.UCHL1.dataString -> {
            null  // no code, not sent
        }
        KnownLabs.G6PD.dataString -> {
            CodeNameGroup("31715371", "Glucose-6-Phosphate Dehydrogenase A80135")
        }
        KnownLabs.ETOH.dataString -> {
            CodeNameGroup("101276993", "Ethanol Level")
        }
        KnownLabs.UA_BLOOD.dataString, KnownLabs.UA_KETONES.dataString, KnownLabs.UA_GLUCOSE.dataString,
            KnownLabs.UA_LEUKOCYTES.dataString, KnownLabs.UA_PROTEIN.dataString, KnownLabs.UA_NITRITES.dataString,
            KnownLabs.UA_BILIRUBIN.dataString, KnownLabs.UA_UROBILINOGEN.dataString, KnownLabs.UA_PH.dataString,
            KnownLabs.UA_SPECIFIC_GRAVITY.dataString -> {
                CodeNameGroup("2921830", "UA Dipstick")
            }
        else -> null
    }
}

fun labObx03WBCCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> CodeNameGroup("804-5", "Leukocytes [#/volume] in Blood by Manual count")
        EndpointType.OMDS -> CodeNameGroup("102597493", "WBC")
        EndpointType.TAC -> CodeNameGroup("102597493", "WBC")
    }

val wbcInboundCodes = listOf("804-5", "102597493")

fun labObx03HgbCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> CodeNameGroup("718-7", "Hemoglobin [Mass/volume] in Blood")
        EndpointType.OMDS -> CodeNameGroup("102599089","Hemoglobin")
        EndpointType.TAC -> CodeNameGroup("102599089", "Hgb")
    }

val hgbInboundCodes = listOf("718-7", "296167475", "102599089","316660")

fun labObx03HcTCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> CodeNameGroup("20570-8", "Hematocrit [Volume Fraction] of Blood")
        EndpointType.OMDS -> CodeNameGroup("102606671", "Hematocrit")
        EndpointType.TAC -> CodeNameGroup("102606671", "Hematocrit")
    }

val hctInboundCodes = listOf("20570-8", "102606671", "110112991", "102606671")

fun labObx03PLTCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> CodeNameGroup("778-1", "Platelets [#/volume] in Blood by Manual count")
        EndpointType.OMDS -> CodeNameGroup("102595669", "Platelets")
        EndpointType.TAC -> CodeNameGroup("102595669", "PLT")
    }

val pltInboundCodes = listOf("778-1", "102595669")

fun rbcCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.MHSG_T -> null // CodeNameGroup("789-8", "Erythrocytes [#/volume] in Blood by Automated count")
        EndpointType.TAC -> CodeNameGroup("102593461", "RBC")
        EndpointType.OMDS -> CodeNameGroup("102593461", "RBC")
    }

val rbcInboundCodes = listOf("789-8", "102593461")

fun mcvCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.MHSG_T -> null // CodeNameGroup("48706-6", "Reticulocyte mean volume [Entitic volume] in Reticulocytes")
        EndpointType.TAC -> CodeNameGroup("102596851", "MCV")
        EndpointType.OMDS -> CodeNameGroup("102596851", "MCV")
    }

val mcvInboundCodes = listOf("48706-6", "102596851")

fun mpvCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.MHSG_T -> null // CodeNameGroup("28542-9", "Platelet mean volume [Entitic volume] in Blood")
        EndpointType.TAC -> CodeNameGroup("102594769", "MPV")
        EndpointType.OMDS -> CodeNameGroup("102594769", "MPV")
    }

val mpvInboundCodes = listOf("28542-9", "102594769")

fun absLymphocyteCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.MHSG_T -> null // CodeNameGroup("731-0", "Lymphocytes [#/volume] in Blood by Automated count")
        EndpointType.TAC -> CodeNameGroup("102593677", "DiffLYMPHAbsolute")
        EndpointType.OMDS -> CodeNameGroup("102593677", "Lymph Absolute")
    }

val absLymphocyteInboundCodes = listOf("731-0", "102593677")

fun absMonocycteCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.MHSG_T -> null // CodeNameGroup("742-7", "Monocytes [#/volume] in Blood by Automated count")
        EndpointType.TAC -> CodeNameGroup("102596353", "Mono Absolute")
        EndpointType.OMDS -> CodeNameGroup("102596353", "Mono Absolute")
    }

val absMonocycteInboundCodes = listOf("742-7", "102596353")

fun absGranulocyteCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.MHSG_T -> null // CodeNameGroup("742-7", "Monocytes [#/volume] in Blood by Automated count")
        EndpointType.TAC -> CodeNameGroup("122771511", "Granulocytes, Abs")
        EndpointType.OMDS -> CodeNameGroup("122771511", "Granulocytes, Abs")
    }

val absGranulocyteInboundCodes = listOf("122771511")

fun lymphocyteCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.MHSG_T -> null // CodeNameGroup("736-9", "Lymphocytes/100 leukocytes in Blood by Automated count")
        EndpointType.TAC -> CodeNameGroup("102597277", "DiffLYMPHPercent")
        EndpointType.OMDS -> CodeNameGroup("102597277", "Lymphocyte % Auto")
    }

val lymphocyteInboundCodes = listOf("736-9", "102597277")

fun monocyteCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.MHSG_T -> null // CodeNameGroup("5905-5", "Monocytes/100 leukocytes in Blood by Automated count\tLab ")
        EndpointType.TAC -> CodeNameGroup("102599689", "DiffMONOPercent")
        EndpointType.OMDS -> CodeNameGroup("102599689", "Monocyte % Auto")
    }

val monocyteInboundCodes = listOf("5905-5", "102599689")

fun granulocyteCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.MHSG_T -> null // CodeNameGroup("5905-5", "Monocytes/100 leukocytes in Blood by Automated count\tLab ")
        EndpointType.TAC -> CodeNameGroup("121871521", "Imm. Granulocyte %")
        EndpointType.OMDS -> CodeNameGroup("121871521", "Imm. Granulocyte %")
    }

val granulocyteInboundCodes = listOf("5905-5", "121871521")

fun labObx03NaCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> CodeNameGroup("2947-0", "Sodium [Moles/volume] in Blood")
        EndpointType.OMDS -> CodeNameGroup("102593623","Sodium")
        EndpointType.TAC -> CodeNameGroup("102593623", "Sodium")
    }

val naInboundCodes = listOf("2947-0", "110117803", "102596167", "102593623")

fun labObx03KCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> CodeNameGroup("75940-7", "Potassium [Mass/volume] in Blood")
        EndpointType.OMDS -> CodeNameGroup("102598663", "Potassium Lvl")
        EndpointType.TAC -> CodeNameGroup("102598663", "Potassium Lvl")
    }

val kInboundCodes = listOf("75940-7", "110114461", "102599983", "102598663")

fun labObx03ClCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> CodeNameGroup("2069-3", "Chloride [Moles/volume] in Blood")
        EndpointType.OMDS -> CodeNameGroup("21705582", "Chloride")
        EndpointType.TAC -> CodeNameGroup("21705582","Chloride")
    }

val clInboundCodes = listOf("2069-3", "21705582")

fun labObx03iCaCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> CodeNameGroup("38230-9", "Calcium.ionized [Mass/volume] in Blood")
        EndpointType.OMDS -> CodeNameGroup("102595417", "Calcium")
        EndpointType.TAC -> CodeNameGroup("102595417", "Calcium")
    }

val iCaInboundCodes = listOf("38230-9", "110112247", "110114539", "102595417")

fun labGlucoseElectrolyteCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> null // CodeNameGroup("38230-9", "Calcium.ionized [Mass/volume] in Blood")
        EndpointType.OMDS -> CodeNameGroup("102606625", "Glucose Lvl")
        EndpointType.TAC -> CodeNameGroup("102606625", "Glucose Lvl")
    }

val glucoseElectrolyteInboundCodes = listOf("38230-9", "110112247", "110114539", "102606625")

fun anionGapCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> null
        EndpointType.OMDS -> CodeNameGroup("102594019", "AGAP")
        EndpointType.TAC -> CodeNameGroup("102594019", "AGAP")
    }

val anionGapInboundCodes = listOf("102594019")

fun labObx03CO2Code(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> CodeNameGroup("20565-8", "Carbon dioxide, total [Moles/volume] in Blood")
        EndpointType.OMDS -> CodeNameGroup("102594007", "POC CO2")
        EndpointType.TAC -> CodeNameGroup("102594007", "TCO2")
    }

val co2InboundCodes = listOf("20565-8", "102594007")

fun labObx03BunCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> CodeNameGroup("6299-2", "Urea nitrogen [Mass/volume] in Blood")
        EndpointType.OMDS -> CodeNameGroup("102596677", "BUN")
        EndpointType.TAC -> CodeNameGroup("102596677", "BUN")
    }

val bunInboundCodes = listOf("6299-2", "102596677")

fun labObx03CreatCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> CodeNameGroup("2148-5", "Creatine [Mass/volume] in Serum or Plasma")
        EndpointType.OMDS -> CodeNameGroup("102606593", "Creatinine")
        EndpointType.TAC -> CodeNameGroup("102606593","Creatinine")
    }

val creatInboundCodes = listOf("2148-5", "107947815", "102606593")

fun glucometerCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> null //CodeNameGroup("2339-0", "Glucose [Mass/volume] in Blood")
        EndpointType.OMDS -> CodeNameGroup("102594901", "POC Glucose")
        EndpointType.TAC -> CodeNameGroup("102594901", "POC Glucose")
    }

val glucometerInboundCodes = listOf("102594901")

fun inrCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.MHSG_T -> null // CodeNameGroup("34714-6", "INR in Blood by Coagulation assay")
        EndpointType.TAC -> CodeNameGroup("113203781", "INR LC")
        EndpointType.OMDS -> CodeNameGroup("113203781", "INR LC")
    }

val inrInboundCodes = listOf("34714-6", "20136458", "113203781")

fun ptCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.MHSG_T -> null // CodeNameGroup("5902-2", "Prothrombin time (PT)")
        EndpointType.TAC -> CodeNameGroup("113203925", "Prothrombin Time LC")
        EndpointType.OMDS -> CodeNameGroup("113203925", "Prothrombin Time LC")
    }

val ptInboundCodes = listOf("5902-2", "113203925")

fun arterialPhCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> null // CodeNameGroup("11558-4","pH of Blood")
        EndpointType.OMDS -> CodeNameGroup("102599251","pH (Art)")
        EndpointType.TAC -> CodeNameGroup("102599251", "pH (Art)")
    }

val arterialPhInboundCodes = listOf("102599251")

fun arterialPco2Code(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> null //CodeNameGroup("11557-6", "Carbon dioxide [Partial pressure] in Blood")
        EndpointType.OMDS -> CodeNameGroup("112091159", "pCO2 tc (Art)")
        EndpointType.TAC -> CodeNameGroup("112091159", "pCO2 tc (Art)")
    }

val arterialPco2InboundCodes = listOf("112091159")

fun arterialPaO2Code(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> null // CodeNameGroup("11556-8","Oxygen [Partial pressure] in Blood")
        EndpointType.OMDS -> CodeNameGroup("102593515","pO2 (Art)")
        EndpointType.TAC -> CodeNameGroup("102593515", "pO2 (Art)")
    }

val arterialPaO2InboundCodes = listOf("102593515")

fun arterialHCO3Code(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> null // CodeNameGroup("1959-6", "Bicarbonate [Moles/volume] in Blood")
        EndpointType.OMDS -> CodeNameGroup("102593749", "HCO3 (Art)")
        EndpointType.TAC -> CodeNameGroup("102593749", "HCO3 (Art)")
    }

val arterialHco3InboundCodes = listOf("102593749")

fun saO2Code(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.MHSG_T -> null // CodeNameGroup("2708-6", "Oxygen saturation in Arterial blood")
        EndpointType.TAC -> CodeNameGroup("1967222979", "O2 Sat (Art)")
        EndpointType.OMDS -> CodeNameGroup("1967222979", "O2 Sat (Art)")
    }

val saO2InboundCodes = listOf("1967222979")


fun arterialBECode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> null // CodeNameGroup("11555-0", "Base excess in Blood by calculation")
        EndpointType.OMDS -> CodeNameGroup("1060823585", "ABE (Art)")
        EndpointType.TAC -> CodeNameGroup("1060823585", "ABE (Art)")
    }

val arterialBeInboundCodes = listOf("1060823585")

fun arterialLacticAcidCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> null // CodeNameGroup("11555-0", "Base excess in Blood by calculation")
        EndpointType.OMDS -> CodeNameGroup("112091117", "Lactic Acid (Art)")
        EndpointType.TAC -> CodeNameGroup("112091117", "Lactic Acid (Art)")
    }

val arterialLacticAcidInboundCodes = listOf("112091117")

fun venousPhCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> null // CodeNameGroup("11558-4","pH of Blood")
        EndpointType.OMDS -> CodeNameGroup("102594775","Venous pH")
        EndpointType.TAC -> CodeNameGroup("102594775", "Venous pH")
    }

val venousPhInboundCodes = listOf("102594775")

fun venousPCO2Code(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> null // CodeNameGroup("11557-6", "Carbon dioxide [Partial pressure] in Blood")
        EndpointType.OMDS -> CodeNameGroup("112091189", "Venous PaCO2")
        EndpointType.TAC -> CodeNameGroup("112091189", "Venous PaCO2")
    }

val venousPco2InboundCodes = listOf("112091189")

fun venousPaO2Code(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> null //CodeNameGroup("11556-8","Oxygen [Partial pressure] in Blood")
        EndpointType.OMDS -> CodeNameGroup("102598489","POC pO2 (Ven)")
        EndpointType.TAC -> CodeNameGroup("102598489", "Venous PaO2")
    }

val venousPaO2InboundCodes = listOf("102598489")

fun venousHCO3Code(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> null // CodeNameGroup("1959-6", "Bicarbonate [Moles/volume] in Blood")
        EndpointType.OMDS -> CodeNameGroup("102582299", "HCO3 Ven")
        EndpointType.TAC -> CodeNameGroup("102582299", "HCO3 Ven")
    }

val venousHco3InboundCodes = listOf("102582299")

fun venousO2SatCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> null // CodeNameGroup("1959-6", "Bicarbonate [Moles/volume] in Blood")
        EndpointType.OMDS -> CodeNameGroup("21705878", "zzzO2 Saturation Venous")
        EndpointType.TAC -> CodeNameGroup("21705878", "zzzO2 Saturation Venous")
    }

val venousO2SatInboundCodes = listOf("21705878")

fun venousBECode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> null // CodeNameGroup("11555-0", "Base excess in Blood by calculation")
        EndpointType.OMDS -> CodeNameGroup("102596077", "BE (Ven)")
        EndpointType.TAC -> CodeNameGroup("102596077", "BE (Ven)")
    }

val venousBeInboundCodes = listOf("102596077")

fun venousLacticAcidCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> null // CodeNameGroup("11555-0", "Base excess in Blood by calculation")
        EndpointType.OMDS -> CodeNameGroup("112091147", "Lactic Acid (Ven)")
        EndpointType.TAC -> CodeNameGroup("112091147", "Lactic Acid (Ven)")
    }

val venousLacticAcidInboundCodes = listOf("112091147")

fun ckmbCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> null
        EndpointType.OMDS -> CodeNameGroup("113197191", "CK-MB.LC")
        EndpointType.TAC -> CodeNameGroup("113197191", "CK-MB.LC")
    }

val ckmbInboundCodes = listOf("113197191")

fun bnpCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> null
        EndpointType.OMDS -> CodeNameGroup("102597871", "ProBNP")
        EndpointType.TAC -> CodeNameGroup("102597871", "ProBNP")
    }

val bnpInboundCodes = listOf("102597871")

fun troponin1Code(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> null
        EndpointType.OMDS -> CodeNameGroup("102596947", "Troponin-I")
        EndpointType.TAC -> CodeNameGroup("102596947", "Troponin-I")
    }

val troponin1InboundCodes = listOf("102596947")

fun gfapCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> null
        EndpointType.OMDS -> CodeNameGroup("**********", "GFAP")
        EndpointType.TAC -> CodeNameGroup("**********", "GFAP")
    }

val gfapInboundCodes = listOf("**********")

fun uchl1Code(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> null
        EndpointType.OMDS -> null//CodeNameGroup("**********", "GFAP")
        EndpointType.TAC -> null//CodeNameGroup("**********", "GFAP")
    }

val uchl1InboundCodes = listOf<String>()

fun g6pdCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> null
        EndpointType.OMDS -> CodeNameGroup("102598861", "G6PD")
        EndpointType.TAC -> CodeNameGroup("102598861", "G6PD")
    }

val g6pdInboundCodes = listOf("102598861")

fun etohCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> null
        EndpointType.OMDS -> CodeNameGroup("102598789", "ETOH Ur LC")
        EndpointType.TAC -> CodeNameGroup("102598789", "ETOH Ur LC")
    }

val etohInboundCodes = listOf("102598789")

fun uaBloodCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> null
        EndpointType.OMDS -> CodeNameGroup("102607209", "UA Blood")
        EndpointType.TAC -> CodeNameGroup("102607209", "UA Blood")
    }

val uaBloodInboundCodes = listOf("102607209")

fun uaKetonesCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> null
        EndpointType.OMDS -> CodeNameGroup("102596899", "UA Ketones")
        EndpointType.TAC -> CodeNameGroup("102596899", "UA Ketones")
    }

val uaKetonesInboundCodes = listOf("102596899")

fun uaGlucoseCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> null
        EndpointType.OMDS -> CodeNameGroup("102607189", "UA Glucose")
        EndpointType.TAC -> CodeNameGroup("102607189", "UA Glucose")
    }

val uaGlucoseInboundCodes = listOf("102607189")

fun uaLeukocytesCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> null
        EndpointType.OMDS -> CodeNameGroup("102607269", "UA Leuk Esterase")
        EndpointType.TAC -> CodeNameGroup("102607269", "UA Leuk Esterase")
    }

val uaLeukocytesInboundCodes = listOf("102607269")

fun uaProteinCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> null
        EndpointType.OMDS -> CodeNameGroup("102607241", "UA Protein")
        EndpointType.TAC -> CodeNameGroup("102607241", "UA Protein")
    }

val uaProteinInboundCodes = listOf("102607241")

fun uaNitriteCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> null
        EndpointType.OMDS -> CodeNameGroup("102607255", "UA Nitrite")
        EndpointType.TAC -> CodeNameGroup("102607255", "UA Nitrite")
    }

val uaNitriteInboundCodes = listOf("102607255")

fun uaBilirubinCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> null
        EndpointType.OMDS -> CodeNameGroup("102596041", "UA Bili")
        EndpointType.TAC -> CodeNameGroup("102596041", "UA Bili")
    }

val uaBilirubinInboundCodes = listOf("102596041")

fun uaUrobilinogenCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> null
        EndpointType.OMDS -> CodeNameGroup("102596011", "UA Urobilinogen")
        EndpointType.TAC -> CodeNameGroup("102596011", "UA Urobilinogen")
    }

val uaUrobilinogenInboundCodes = listOf("102596011")

fun uaPhCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> null
        EndpointType.OMDS -> CodeNameGroup("102607007", "UA pH")
        EndpointType.TAC -> CodeNameGroup("102607007", "UA pH")
    }

val uaPhInboundCodes = listOf("102607007")

fun uaSpecificGravityCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> null
        EndpointType.OMDS -> CodeNameGroup("102607153", "UA Specific Gravity")
        EndpointType.TAC -> CodeNameGroup("102607153", "UA Specific Gravity")
    }

val uaSpecificGravityInboundCodes = listOf("102607153")
