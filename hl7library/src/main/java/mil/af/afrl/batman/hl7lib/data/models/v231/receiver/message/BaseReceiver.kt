package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.message

import androidx.annotation.CallSuper
import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.v231.segment.MSH
import ca.uhn.hl7v2.model.v231.segment.PID
import ca.uhn.hl7v2.model.v231.segment.PV1
import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.DocumentCommands.CommandData
import gov.afrl.batdok.encounter.CountryData
import gov.afrl.batdok.encounter.Gender
import gov.afrl.batdok.encounter.Name
import gov.afrl.batdok.encounter.commands.buildChangeDOBCommand
import gov.afrl.batdok.encounter.commands.buildChangeDodIdCommand
import gov.afrl.batdok.encounter.commands.buildChangeGenderCommand
import gov.afrl.batdok.encounter.commands.buildChangeInjuryTimeCommand
import gov.afrl.batdok.encounter.commands.buildChangeNameCommand
import gov.afrl.batdok.encounter.commands.buildChangeNationalityCommand
import gov.afrl.batdok.encounter.commands.buildChangePatientIdCommand
import gov.afrl.batdok.encounter.commands.buildChangeSsnCommand
import gov.afrl.batdok.encounter.commands.buildPatientMaskingCommand
import gov.afrl.batdok.util.NameFormatter
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.oidBATDOK
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.oidEDIPI
import mil.af.afrl.batman.hl7lib.util.HL7Data
import mil.af.afrl.batman.hl7lib.util.ProviderInfo
import mil.af.afrl.batman.hl7lib.util.generateDomainIdFromString
import org.openehealth.ipf.modules.hl7.kotlin.value
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter

val abbreviationToCountryData: Map<String,CountryData> = CountryData.entries.associateBy { it.abbreviation }
abstract class BaseReceiver<T : AbstractMessage>(protected val message: T, protected val endpointType: EndpointType) {
    protected val vendor = (message["MSH"] as MSH).msh3_SendingApplication.value
    protected abstract val pid: PID
    protected abstract val pv1: PV1

    fun getAllCommands() = getPidCommands() + getPV1Commands() + getMessageCommands()


    abstract fun getMessageCommands() : List<CommandData>

    @CallSuper
    open fun setExtraData(hl7Data: HL7Data) {
        // Intended for things that we'd have to send as metadata because there are no
        //  commands for them or another way to attach the info to a Document

        // This should be the same for all of the messages that come in a single batch
        // Take the first doctor's info, with an EDIPI (if one exists)
        val doctorInfo = pv1.attendingDoctor.find { it.xcn9_AssigningAuthority?.value == "EDIPI" }
        // Only take the doctor's info if they have a valid DOD ID
        if (doctorInfo != null) {
            val dodId = doctorInfo.xcn1_IDNumber.value
            val firstName = doctorInfo.xcn3_GivenName.value
            val middleName = doctorInfo.xcn4_MiddleInitialOrName.value
            val lastName = doctorInfo.xcn2_FamilyLastName.value
            val fullName = Name(firstName, middleName, lastName).toString(NameFormatter.Format.FIRST_MIDDLE_LAST)
            hl7Data.providerInfo = ProviderInfo(fullName, dodId)
        }
    }

    protected fun MutableList<CommandData>.addCommand(timestamp: Instant? = Instant.now(), commandBuilder: () -> Message) {
        add(buildCommandData(commandBuilder(), timestamp = timestamp))
    }

    protected fun parseDate(value: String): LocalDate {
        val dateFormat = DateTimeFormatter.ofPattern("yyyyMMdd")
        return LocalDate.parse(value, dateFormat)
    }

    protected fun parseInstant(value: String?): Instant? {
        value ?: return null
        return try {
            val dtFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss[X]")
            val timeConverter = LocalDateTime.parse(value, dtFormatter)
            timeConverter.toInstant(ZoneOffset.UTC)
        } catch (e: Exception) {
            null
        }
    }

    protected class DataWithTimestamp(val data: String?, val timestamp: Instant? = Instant.now())

    fun getPidCommands(): List<CommandData> {
        val commandList = mutableListOf<CommandData>()
        
        val idMap = getPidIds()
        val dodId = idMap[oidEDIPI]
        if (!dodId.isNullOrEmpty()) {
            commandList.addCommand { buildChangeDodIdCommand(dodId) }
        }
        val batdokID = idMap[oidBATDOK]
        if (batdokID != null) {
            commandList.addCommand { buildChangePatientIdCommand(generateDomainIdFromString(batdokID)) }
        }

        val family = pid.getPid5_PatientName(0).xpn1_FamilyLastName.value
        val middle = pid.getPid5_PatientName(0).xpn3_MiddleInitialOrName.value
        val given = pid.getPid5_PatientName(0).xpn2_GivenName.value
        commandList.addCommand { buildChangeNameCommand(given, middle, family) }

        val dob = pid.pid7_DateTimeOfBirth.value
        // Jan 1, 1900 is a default date for some systems, so ignore that
        if (dob != null && dob != "19000101") {
            val dobFormatter = DateTimeFormatter.ofPattern("yyyyMMdd")
            val dobFormatted = LocalDate.parse(dob, dobFormatter)
            commandList.addCommand { buildChangeDOBCommand(dobFormatted) }
        }

        if(!pid.pid19_SSNNumberPatient.isEmpty){
            val ssn = pid.pid19_SSNNumberPatient.value
            commandList.addCommand { buildChangeSsnCommand(ssn) }
        }

        val sex = pid.pid8_Sex.value
        if (sex == "M"){
            commandList.addCommand { buildChangeGenderCommand(Gender.MALE) }
        } else if (sex == "F"){
            commandList.addCommand { buildChangeGenderCommand(Gender.FEMALE) }
        }

        val nationality = pid.pid28_Nationality.ce1_Identifier.value ?: ""
        if (nationality != ""){
            val countryName = getCountryName(nationality) ?: ""
            if (countryName != "") {
                commandList.addCommand { buildChangeNationalityCommand(countryName) }
            }
        }

        return commandList
    }

    private fun getPidIds() : Map<String, String> {
        val idMap = mutableMapOf<String, String>()

        val allIds = pid.pid3_PatientIdentifierList + pid.pid4_AlternatePatientIDPID
        for (id in allIds) {
            val patientID = id.cx1_ID.value ?: continue
            val idType = id.cx4_AssigningAuthority.value ?: continue
            idMap[idType] = patientID
        }

        return idMap
    }

    fun getPV1Commands() : List<CommandData> {
        val commands = mutableListOf<CommandData>()
        if (pv1.pv116_VIPIndicator.value == "Y") {
            commands.addCommand {
                buildPatientMaskingCommand(
                    true,
                    "Masked by external application"
                )
            }
        }

        // Only take this value as injury time for GT
        // Others should send in an ACC on the A04
        if (!pv1.pv144_AdmitDateTime.isEmpty && endpointType == EndpointType.MHSG_T) {
            val time = pv1.pv144_AdmitDateTime.value
            commands.addCommand { buildChangeInjuryTimeCommand(parseInstant(time)) }
        }

        return commands
    }

    private fun getCountryData(abbreviation: String): CountryData?{
        return abbreviationToCountryData[abbreviation]
    }

    private fun getCountryName(abbreviation: String):String?{
        val countryData = getCountryData(abbreviation)
        return countryData?.dataString
    }
}
