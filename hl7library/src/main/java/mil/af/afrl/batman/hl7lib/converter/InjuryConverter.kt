package mil.af.afrl.batman.hl7lib.converter

import androidx.annotation.VisibleForTesting
import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.v231.datatype.CE
import gov.afrl.batdok.encounter.Injury
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ExtendedCodedElement
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ICD10
import mil.af.afrl.batman.hl7lib.data.models.v231.field.SNOMED_CT
import mil.af.afrl.batman.hl7lib.data.models.v231.field.TACICD10
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup
import mil.af.afrl.batman.hl7lib.util.ICD10DescriptionUtil
import java.io.InputStream

abstract class LocationInjuryConverter<T> {
    private val defaultCode = CodeNameGroup("T14.8", ICD10DescriptionUtil.getDescForCode("T14.8") ?: "") // Other injury of unspecified body region

    companion object {
        private val NORMALIZER_REGEX = Regex("[- &]")
        private fun normalize(input: String?): String? {
            return input?.replace(NORMALIZER_REGEX, "_")?.uppercase()
        }

        // This is disgusting, but I don't know of a better way to do it
        // Outermost Key: Format type
        // Outermost value: Map of MOIs and injuries to metadata
        //   Middle Key: Normalized data item name
        //   Middle Value:
        //     pair.first: Raw data item name
        //     pair.second:
        //       Inner Key: Name of codeset
        //       Inner Value: Code alias and description for this item and codeset
        var map: MutableMap<String, Map<String, Pair<String, Map<String, String>>>> = mutableMapOf()

        fun hasCodeset(codesetName: String) = map.containsKey(codesetName)

        fun loadData(dataStream: InputStream, codesetName: String) {
            val codeMap = mutableMapOf<String, Pair<String, Map<String, String>>>()
            val lines = dataStream.bufferedReader(Charsets.UTF_8).readLines()
            if (lines.isEmpty()) return
            // Header format should be "Name, Default, Batdok Locations...". We need to read those locations for the inner map.
            val batdokLocations = if (lines[0].startsWith("Name\tDefault\t")) {
                lines[0].split('\t')
                    .drop(1)  // Drop "name" column
                    .map { normalize(it)!! }
            } else return

            // All other lines, we need to add to the base map
            for (line in lines.drop(1)) {
                // First column should be name
                val rawName = line.substringBefore('\t')
                val name = normalize(rawName) ?: rawName
                // Remaining columns should be the codes themselves
                // These should line up with the locations pulled from the header
                val codes = line.substringAfter('\t').split('\t')
                val locationsToCodes = batdokLocations.zip(codes)
                    .filter { it.second.isNotEmpty() }
                    .toMap()
                codeMap[name] = Pair(rawName, locationsToCodes)
            }

            map[codesetName] = codeMap
        }

        // Returns the found item with its body location, or null
        fun codeToBatdokData(code: String, endpoint: EndpointType) : Pair<String, String?>? {
            // From the endpoint, get the codeset that we need
            // Super inefficient implementation... maps don't work in reverse
            val codeData = map[getCodesetForEndpoint(endpoint)]

            if (codeData != null) {
                // codeData is map of Injury Name to (Map of Location to Code)
                // Iterate through each Injury Name map to find one where the code might appear
                for (injuryMapItem in codeData) {
                    // Iterate through the location map items to see what location this code might be in
                    for (locationMapItem in injuryMapItem.value.second) {
                        if (code == locationMapItem.value) {
                            // Pair of item name and location
                            return Pair(injuryMapItem.value.first, locationMapItem.key)
                        }
                    }
                }
            }

            return null
        }

        private fun getCodesetForEndpoint(endpoint: Endpoint) : String {
            return getCodesetForEndpoint(endpoint.format)
        }

        private fun getCodesetForEndpoint(endpoint: EndpointType) : String {
            return when (endpoint) {
                // default should be SNOMED set
                EndpointType.MHSG_T -> ICD10
                EndpointType.OMDS -> ICD10
                EndpointType.TAC -> TACICD10
            }
        }

        @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
        fun clearData() = map.clear()
    }

    abstract fun T.getName() : String?
    abstract fun T.getLocation() : String?
    fun T.normalizedName() = normalize(getName())
    fun T.normalizedLocation() = normalize(getLocation())

    private fun batdokDataToCodeData(data: T, codeset: String): CodeNameGroup {
        // Look for the injury in the map
        val locationMap = map[codeset]?.get(data.normalizedName())

        if (locationMap != null) {
            // Injury exists, look for its location
            // If the location does not exist, try to find one with a default non-location-specific code
            val code = locationMap.second[normalize(data.normalizedLocation())] ?: locationMap.second["DEFAULT"]

            // If the code for that location is valid, return it
            if (code != null) {
                val name = data.getName() ?: ""
                val location = data.getLocation().takeUnless { it == "Default" } ?: ""
                val description = listOfNotNull(location, name).joinToString(separator = " ").trim()
                return CodeNameGroup(code, ICD10DescriptionUtil.getDescForCode(code) ?: description)
            }
        }

        return defaultCode
    }

    fun batdokDataToCodedElement(message: AbstractMessage, data: T, endpoint: Endpoint): CE {
        val codedData = batdokDataToCodeData(data, getCodesetForEndpoint(endpoint))
        // This isn't set up well for the stupid mixed codesets CDP wants us to use...
        val codeName = if (codedData.code.matches("[a-zA-Z]\\d\\d.*".toRegex())) ICD10 else SNOMED_CT
        return ExtendedCodedElement(message, codedData, codeName)
    }

    private fun getCodeNameForEndpoint(endpoint: Endpoint) : String {
        // TODO: Return SNOMED here for default HL7 format
        return ICD10
    }
}

class InjuryConverter(private val location: String?) : LocationInjuryConverter<Injury>() {
    override fun Injury.getName() = injury
    override fun Injury.getLocation() = location
}
