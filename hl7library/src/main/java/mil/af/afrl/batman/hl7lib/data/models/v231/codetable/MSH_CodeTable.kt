package mil.af.afrl.batman.hl7lib.data.models.v231.codetable

import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType

fun msh03SendApplicationCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> "DOD_BATDOK-J_THEATER"
        else -> "BATDOK"
    }

fun msh05ReceiveApplicationCode(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.OMDS -> ""
        else -> endpoint.format.displayName
    }


fun msh06ReceivingFacilityCode(endpoint: Endpoint)=
    when(endpoint.format){
        EndpointType.MHSG_T -> "THEATER"
        else -> ""
    }

fun msh11ProcessingIDCode(endpoint: Endpoint)="P"

fun msh12VersionIDCode(endpoint: Endpoint) = "2.3"
