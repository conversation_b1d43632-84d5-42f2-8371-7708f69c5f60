package mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment

import gov.afrl.batdok.encounter.Injuries
import gov.afrl.batdok.encounter.Injury
import gov.afrl.batdok.util.DD1380InjuryBoundingBox

data class InjuryData(val injury: Injury, val location: String?)

fun getInjuryData (injuriesDocument: Injuries, mode: String): List<InjuryData>{
    val injuryLocationMapping = mutableListOf<InjuryData>()
    val injuries = injuriesDocument.injuries
    val drawingPoint = injuriesDocument.drawingPoints

    //Goes through each injury location and corresponding injury list
    injuries.forEach { (batdokLocation, injuryList) ->
        //If location is null or empty, process the injuries
        if (batdokLocation.isNullOrEmpty()) {
            //Go through each injury in the injury list
            injuryList.forEach { injury ->
                //Filter drawing points that match the injury abbreviation
                val matchingPointList = drawingPoint.filter { it.label == injury.abbreviation }
                //If there are matching drawing points, map each one to an InjuryData object
                if (matchingPointList.isNotEmpty()) {
                    matchingPointList.forEach { drawingPoint ->
                        //Get fine location based on the drawing point and patient mode
                        injuryLocationMapping.add(InjuryData(injury, DD1380InjuryBoundingBox.getFineLocation(drawingPoint, mode.equals("TCCC K9", true))))
                    }
                } else {
                    //If no matching drawing points, map the injury to default
                    injuryLocationMapping.add(InjuryData(injury, "Default"))
                }
            }
        } else {
            //If batdokLocation is not null or empty, map each injury to the given Location
            injuryList.forEach { injury ->
                injuryLocationMapping.add(InjuryData(injury, batdokLocation))
            }
        }
    }
    return injuryLocationMapping
}