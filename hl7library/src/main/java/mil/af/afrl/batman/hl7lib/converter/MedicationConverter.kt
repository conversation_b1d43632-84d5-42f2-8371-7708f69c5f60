package mil.af.afrl.batman.hl7lib.converter

import gov.afrl.batdok.encounter.medicine.Medicine
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.data.models.v231.field.RxNorm

object MedicationConverter: BatdokDataConverter<Medicine>() {
    override fun Medicine.toName() : String {
        val regex = ".+ \\((.+)\\)".toRegex()
        val match = regex.find(name)
        // First group is apparently always the full match, second is the
        //  group within the parentheses
        return match?.groupValues?.last() ?: name
    }
    override fun buildType(name: String) = Medicine(name)
    override fun codeTypeForEndpoint(endpoint: Endpoint) : String {
        return RxNorm
    }
}
