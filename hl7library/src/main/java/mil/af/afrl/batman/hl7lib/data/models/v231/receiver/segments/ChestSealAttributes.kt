package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments

import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.treatment.ChestSealData
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.chestSealLocationFromCode

fun chestSealAttributes(attributes: List<OBX>) : ChestSealData? {
    var location: String? = null

    for (code in attributes.map { it.obx3_ObservationIdentifier.ce1_Identifier.value }){
        location = chestSealLocationFromCode(code)
    }

    return ChestSealData(location = location)
}
