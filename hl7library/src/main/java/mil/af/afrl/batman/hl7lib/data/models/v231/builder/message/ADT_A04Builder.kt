package mil.af.afrl.batman.hl7lib.data.models.v231.builder.message

import ca.uhn.hl7v2.model.v231.segment.ACC
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.converter.TimeConverter
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.PV2Builder
import mil.af.afrl.batman.hl7lib.util.HL7Data
import org.openehealth.ipf.modules.hl7.kotlin.from


class ADT_A04Builder(private val endpoint: Endpoint): ADT_AXXBuilder("ADT", "A04", endpoint) {
    override fun populateAll(hl7Data: HL7Data): AbstractMessageBuilder<CustomADT_AXX> {
        if (hl7Data.document == null) return this
        super.populateAll(hl7Data)
        if (endpoint.format != EndpointType.OMDS) {
            populateSegment(PV2Builder(), hl7Data)
        }

        val injuryTime = hl7Data.document!!.info.timeInfo
        if (endpoint.format != EndpointType.MHSG_T && injuryTime != null) {
            val acc = ACC(msg, msg.modelClassFactory).apply {
                acc1_AccidentDateTime.parse(TimeConverter.timestampToFormattedDateTime(injuryTime))
            }
            msg.acc.from(acc)
        }

        return this
    }

}
