package mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure

import gov.afrl.batdok.encounter.treatment.EyeShieldData
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup

fun eyeShieldCode(endpointType: EndpointType) = when (endpointType) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("225696009", "Applying eye shield (procedure)")
    else -> null
}

fun eyeShieldLeftCode(endpointType: EndpointType) = when (endpointType) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("7771000", "Left (qualifier value)")
    else -> null
}

fun eyeShieldRightCode(endpointType: EndpointType) = when (endpointType) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("24028007", "Right (qualifier value)")
    else -> null
}

fun eyeShieldBothCode(endpointType: EndpointType) = when (endpointType) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("51440002", "Right and left (qualifier value)")
    else -> null
}

//region Inbound
fun cdpEyeShieldFromCode (code: String) = when (code){
    "7771000" -> EyeShieldData(
        left = true,
        right = false
    ) //left
    "24028007" -> EyeShieldData(
        left = false,
        right = true
    )//right
    "51440002" -> EyeShieldData(
        left = true,
        right = true
    )//both
    else -> null
}
//endregion
