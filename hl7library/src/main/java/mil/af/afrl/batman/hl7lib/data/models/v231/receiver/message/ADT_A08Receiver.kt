package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.message

import ca.uhn.hl7v2.model.v231.message.ADT_A08
import ca.uhn.hl7v2.model.v231.segment.PID
import ca.uhn.hl7v2.model.v231.segment.PV1
import gov.afrl.batdok.commands.proto.DocumentCommands.CommandData
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.util.parsePr1Info
import org.openehealth.ipf.modules.hl7.kotlin.value

class ADT_A08Receiver(message: ADT_A08, endpointType: EndpointType) : BaseReceiver<ADT_A08>(message, endpointType) {

    override val pid: PID = message.pid
    override val pv1: PV1 = message.pV1
    override fun getMessageCommands(): List<CommandData> {
        val commandsList = mutableListOf<CommandData>()

        parsePr1Info(message.pR1ROLAll.mapNotNull { it.pR1 }, endpointType, ::parseInstant)
            .map {commandsList.addCommand { it }}
        return commandsList
    }
}