package mil.af.afrl.batman.hl7lib.util

import android.graphics.Bitmap
import android.graphics.Color
import com.google.zxing.BarcodeFormat
import com.google.zxing.EncodeHintType
import com.google.zxing.Writer
import com.google.zxing.WriterException
import com.google.zxing.common.BitMatrix
import com.google.zxing.common.CharacterSetECI
import com.google.zxing.qrcode.QRCodeWriter
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel
import java.io.ByteArrayOutputStream
import java.io.IOException
import java.util.Hashtable
import java.util.zip.GZIPOutputStream

@Throws(IOException::class)
fun zipString(data: ByteArray?): ByteArray? {
    val baos = ByteArrayOutputStream()
    val gzos = GZIPOutputStream(baos)
    gzos.write(data)
    gzos.finish()
    gzos.close()
    val compressedData = baos.toByteArray()
    baos.close()
    return compressedData

}


/**
 * Creates a QR code bitmap
 * @param codeData String to encode
 * @param barcodeFormat BarcodeFormat how to format barcode
 * @param codeHeight Int height of image
 * @param codeWidth Int width of image
 * @return Bitmap? generated from QR code
 */
fun createBarCode(
    codeData: ByteArray,
    barcodeFormat: BarcodeFormat,
): Array<BooleanArray>? {
    try {
        //Low error correction because we don't need more space taken up in the QR
        val hintMap = Hashtable<EncodeHintType, Any>()
        hintMap[EncodeHintType.ERROR_CORRECTION] = ErrorCorrectionLevel.L
        hintMap[EncodeHintType.QR_VERSION] = 11

        //If you change this, make sure the Link Desktop app is also set to use the same encoding
        hintMap[EncodeHintType.CHARACTER_SET] = CharacterSetECI.UTF8

        val codeWriter: Writer = QRCodeWriter()

        val byteMatrix = codeWriter.encode(
            String(codeData),
            barcodeFormat, 0, 0, hintMap
        ).toBooleanArray()

        return byteMatrix



    } catch (e: WriterException) {
        e.printStackTrace()
        return null
    }

}

private fun BitMatrix.toBooleanArray(): Array<BooleanArray> {
    val array = Array(width) { BooleanArray(height) }
    for (x in 0 until width) {
        for (y in 0 until height) {
            array[x][y] = get(x, y)
        }
    }
    return array
}

