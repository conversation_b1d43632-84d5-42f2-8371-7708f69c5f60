package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.message

import ca.uhn.hl7v2.model.v231.segment.PID
import ca.uhn.hl7v2.model.v231.segment.PV1
import gov.afrl.batdok.commands.proto.DocumentCommands.CommandData
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.data.models.v231.message.ORU_ZB2
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.util.parseNTEInfo
import mil.af.afrl.batman.hl7lib.data.models.v231.segment.ZBP
import org.openehealth.ipf.modules.hl7.kotlin.value

class ORU_ZB2Receiver(message: ORU_ZB2, endpointType: EndpointType) : BaseReceiver<ORU_ZB2>(message, endpointType) {
    override val pid: PID = message.pidpD1NK1NTEPV1PV2ORCOBRNTEOBXNTECTI.pidpD1NK1NTEPV1PV2.pid
    override val pv1: PV1 = message.pidpD1NK1NTEPV1PV2ORCOBRNTEOBXNTECTI.pidpD1NK1NTEPV1PV2.pV1PV2.pV1

    override fun getMessageCommands(): List<CommandData> {
        val allCommandsList = mutableListOf<CommandData>()

        val patientGroup = message.pidpD1NK1NTEPV1PV2ORCOBRNTEOBXNTECTI
        for (orderGroup in patientGroup.orcobrnteobxntectiAll) {
            val obrCode = orderGroup.obr.obr4_UniversalServiceID.ce1_Identifier.value
            val oruTime = parseInstant(orderGroup.obr.obr7_ObservationDateTime.value)
            val nte =orderGroup.nte

            val nteCommands = parseNTEInfo(nte)
        }

        val lines = message.toString().lines()
        lines.forEach{ line ->
            if(line.startsWith("ZUI")){
                val fields = line.split("|")
                if (fields.size >= 4){
                    val zuiID = fields[2]
                }
            }
            if (line.startsWith("ZBP")){
                val zbpCommands = parseZBP(message)
                //allCommandsList.add(zbpCommands)

            }
        }


        return allCommandsList
    }
}

fun parseZBP(message: ORU_ZB2): List<String> {
    val commands = mutableListOf<String>()
    val zbp = message.get("ZBP") as ZBP

    val bpuUniqueID = zbp.ZBP2_BPU_UniqueID.value
    val bpuUnitLotNumber = zbp.ZBP3_BPU_UnitLotNumber.value
    val bpuSubUnit = zbp.ZBP4_BPU_SubUnit.value
    val zbp5_1 = zbp.ZBP5_BPU_Product.value//subfields
    val zbp5_2 = zbp.ZBP5_BPU_Product.getComponent(1).value
    val zbp5_3 = zbp.ZBP5_BPU_Product.getComponent(2).value
    val bpuProduct = "$zbp5_1^$zbp5_2^$zbp5_3"
    val bpuStatus = zbp.ZBP6_BPU_Status.value
    val bpuEventDateTime = zbp.ZBP7_BPU_EventDateTime.value
    val bpuExpireDateTime = zbp.ZBP8_BPU_ExpireDateTime.value
    val bpuABO = zbp.ZBP9_BPU_ABO.value
    val bpuRH = zbp.ZBP10_BPU_RH.value
    val bpuAntigen = zbp.ZBP11_BPU_Antigen.value
    val bpuAttributes = zbp.ZBP12_BPU_Attributes.value
    val bpuSupplier = zbp.ZBP13_BPU_Supplier.value//subfields
    val pooledProductID = zbp.ZBP17_PooledProductID.value
    val originalVolume = zbp.ZBP18_OrginalVolume.value
    val assumedTransferredVolume = zbp.ZBP19_AssumedTransferredVolume.value
    val assumedTransfusedTotalUnits = zbp.ZBP20_AssumedTransfusedTotalUnits.value
    val assumedTransfusedQuantity = zbp.ZBP21_AssumedTransfusedQuantity.value
    val volumeUnitsOfMeasure = zbp.ZBP23_VolumeUnitsOfMeasure.value
    val isbtFlag = zbp.ZBP24_ISBTFlag.value
    val productFormatNum = zbp.ZBP25_ProductFormatNum.value
    val quantityUnits = zbp.ZBP26_QuantityUnits.value
    val strengthUnits = zbp.ZBP27_StrengthUnits.value

    val stringBuilder = StringBuilder().apply {
        appendLine("bpuUniqueID: $bpuUniqueID")
        appendLine("bpuUnitLotNumber: $bpuUnitLotNumber")
        appendLine("bpuSubUnit: $bpuSubUnit")
        appendLine("bpuProduct: $bpuProduct")
        appendLine("bpuStatus: $bpuStatus")
        appendLine("bpuEventDateTime: $bpuEventDateTime")
        appendLine("bpuExpireDateTime: $bpuExpireDateTime")
        appendLine("bpuABO: $bpuABO")
        appendLine("bpuRH: $bpuRH")
        appendLine("bpuAntigen: $bpuAntigen")
        appendLine("bpuAttributes: $bpuAttributes")
        appendLine("bpuSupplier: $bpuSupplier")
        appendLine("pooledProductID: $pooledProductID")
        appendLine("originalVolume: $originalVolume")
        appendLine("assumedTransferredVolume: $assumedTransferredVolume")
        appendLine("assumedTransfusedTotalUnits: $assumedTransfusedTotalUnits")
        appendLine("assumedTransfusedQuantity: $assumedTransfusedQuantity")
        appendLine("volumeUnitsOfMeasure: $volumeUnitsOfMeasure")
        appendLine("isbtFlag: $isbtFlag")
        appendLine("productFormatNum: $productFormatNum")
        appendLine("quantityUnits: $quantityUnits")
        appendLine("strengthUnits: $strengthUnits")
    }
    println(stringBuilder)

    commands.add(stringBuilder.toString())

    return commands
}