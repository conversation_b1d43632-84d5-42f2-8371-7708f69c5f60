package mil.af.afrl.batman.hl7lib.util

object ICD10DescriptionUtil {

    private val map = mapOf(
        "Y37.250A" to "Military operations involving fragments from munitions, military personnel",
        "T30.0" to "Burn of unspecified body region, unspecified degree",
        "T23.001" to "Burn of unspecified degree of right hand, unspecified site",
        "T23.002" to "Burn of unspecified degree of left hand, unspecified site",
        "T23.062" to "Burn of unspecified degree of back of left hand",
        "T23.061" to "Burn of unspecified degree of back of right hand",
        "T25.021" to "Burn of unspecified degree of right foot",
        "T25.022" to "Burn of unspecified degree of left foot",
        "T25.011" to "Burn of unspecified degree of right ankle",
        "T25.012" to "Burn of unspecified degree of left ankle",
        "T24.001" to "Burn of unspecified degree of unspecified site of right lower limb, except ankle and foot",
        "T24.002" to "Burn of unspecified degree of unspecified site of left lower limb, except ankle and foot",
        "T24.021" to "Burn of unspecified degree of right knee",
        "T24.022" to "Burn of unspecified degree of left knee",
        "T24.011" to "Burn of unspecified degree of right thigh",
        "T24.012" to "Burn of unspecified degree of left thigh",
        "T21.02" to "Burn of unspecified degree of abdominal wall",
        "T21.04" to "Burn of unspecified degree of lower back",
        "T23.071" to "Burn of unspecified degree of right wrist",
        "T23.072" to "Burn of unspecified degree of left wrist",
        "T22.011" to "Burn of unspecified degree of right forearm",
        "T22.012" to "Burn of unspecified degree of left forearm",
        "T22.031" to "Burn of unspecified degree of right upper arm",
        "T22.032" to "Burn of unspecified degree of left upper arm",
        "T22.051" to "Burn of unspecified degree of right shoulder",
        "T22.052" to "Burn of unspecified degree of left shoulder",
        "T21.01" to "Burn of unspecified degree of chest wall",
        "T21.03" to "Burn of unspecified degree of upper back",
        "T20.07" to "Burn of unspecified degree of neck",
        "T20.00" to "Burn of unspecified degree of head, face, and neck, unspecified site",
        "Y37.290" to "Military operations involving other explosions and fragments, military personnel",
        "Y37.290A" to "Military operations involving other explosions and fragments, military personnel",
        "Y29.XXXA" to "Contact with blunt object, undetermined intent",
        "W19" to "Unspecified fall",
        "Y24.9" to "Unspecified firearm discharge, undetermined intent",
        "S61.441" to "Puncture wound with foreign body of right hand",
        "S61.442" to "Puncture wound with foreign body of left hand",
        "S91.341" to "Puncture wound with foreign body, right foot",
        "S91.342" to "Puncture wound with foreign body, left foot",
        "S91.041" to "Puncture wound with foreign body, right ankle",
        "S91.042" to "Puncture wound with foreign body, left ankle",
        "S81.841" to "Puncture wound with foreign body, right lower leg",
        "S81.842" to "Puncture wound with foreign body, left lower leg",
        "S81.041" to "Puncture wound with foreign body, right knee",
        "S81.042" to "Puncture wound with foreign body, left knee",
        "S71.141" to "Puncture wound with foreign body, right thigh",
        "S71.142" to "Puncture wound with foreign body, left thigh",
        "S71.041" to "Puncture wound with foreign body, right hip",
        "S71.042" to "Puncture wound with foreign body, left hip",
        "S31.149" to "Puncture wound of abdominal wall with foreign body, unspecified quadrant without penetration into peritoneal cavity",
        "S31.040" to "Puncture wound with foreign body of lower back and pelvis without penetration into retroperitoneum",
        "S61.541" to "Puncture wound with foreign body of right wrist",
        "S61.542" to "Puncture wound with foreign body of left wrist",
        "S51.841" to "Puncture wound with foreign body of right forearm",
        "S51.842" to "Puncture wound with foreign body of left forearm",
        "S41.141" to "Puncture wound with foreign body of right upper arm",
        "S41.142" to "Puncture wound with foreign body of left upper arm",
        "S41.041" to "Puncture wound with foreign body of right shoulder",
        "S41.042" to "Puncture wound with foreign body of left shoulder",
        "S21.14" to "Puncture wound with foreign body of front wall of thorax without penetration into thoracic cavity",
        "S21.24" to "Puncture wound with foreign body of back wall of thorax without penetration into thoracic cavity",
        "S11.94" to "Puncture wound with foreign body of unspecified part of neck",
        "S01.94" to "Puncture wound with foreign body of unspecified part of head",
        "Y32" to "Crashing of motor vehicle, undetermined intent",
        "Y37.23" to "Military operations involving explosion of improvised explosive device [IED]",
        "T14.8" to "Other injury of unspecified body region",
        "S61.431" to "Puncture wound without foreign body of right hand",
        "S61.432" to "Puncture wound without foreign body of left hand",
        "S91.331" to "Puncture wound without foreign body, right foot",
        "S91.332" to "Puncture wound without foreign body, left foot",
        "S91.031" to "Puncture wound without foreign body, right ankle",
        "S91.032" to "Puncture wound without foreign body, left ankle",
        "S81.831" to "Puncture wound without foreign body, right lower leg",
        "S81.832" to "Puncture wound without foreign body, left lower leg",
        "S81.031" to "Puncture wound without foreign body, right knee",
        "S81.032" to "Puncture wound without foreign body, left knee",
        "S71.131" to "Puncture wound without foreign body, right thigh",
        "S71.132" to "Puncture wound without foreign body, left thigh",
        "S71.031" to "Puncture wound without foreign body, right hip",
        "S71.032" to "Puncture wound without foreign body, left hip",
        "S31.139" to "Puncture wound of abdominal wall without foreign body, unspecified quadrant without penetration into peritoneal cavity",
        "S31.030" to "Puncture wound without foreign body of lower back and pelvis without penetration into retroperitoneum",
        "S61.531" to "Puncture wound without foreign body of right wrist",
        "S61.532" to "Puncture wound without foreign body of left wrist",
        "S51.831" to "Puncture wound without foreign body of right forearm",
        "S51.832" to "Puncture wound without foreign body of left forearm",
        "S41.131" to "Puncture wound without foreign body of right upper arm",
        "S41.132" to "Puncture wound without foreign body of left upper arm",
        "S41.031" to "Puncture wound without foreign body of right shoulder",
        "S41.032" to "Puncture wound without foreign body of left shoulder",
        "S21.13" to "Puncture wound without foreign body of front wall of thorax without penetration into thoracic cavity",
        "S21.23" to "Puncture wound without foreign body of back wall of thorax without penetration into thoracic cavity",
        "S11.93" to "Puncture wound without foreign body of unspecified part of neck",
        "S01.93" to "Puncture wound without foreign body of unspecified part of head",
        "T14.90" to "Injury, unspecified",
        "Z89.9" to "Acquired absence of limb, unspecified",
        "S68.411" to "Complete traumatic amputation of right hand at wrist level",
        "S68.412" to "Complete traumatic amputation of left hand at wrist level",
        "S98.911" to "Complete traumatic amputation of right foot, level unspecified",
        "S98.912" to "Complete traumatic amputation of left foot, level unspecified",
        "S98.011" to "Complete traumatic amputation of right foot at ankle level",
        "S98.012" to "Complete traumatic amputation of left foot at ankle level",
        "S88.011" to "Complete traumatic amputation at knee level, right lower leg",
        "S88.012" to "Complete traumatic amputation at knee level, left lower leg",
        "S78.911" to "Complete traumatic amputation of right hip and thigh, level unspecified",
        "S78.912" to "Complete traumatic amputation of left hip and thigh, level unspecified",
        "S38.221" to "Complete traumatic amputation of penis",
        "S58.911" to "Complete traumatic amputation of right forearm, level unspecified",
        "S58.912" to "Complete traumatic amputation of left forearm, level unspecified",
        "S48.911" to "Complete traumatic amputation of right shoulder and upper arm, level unspecified",
        "S48.912" to "Complete traumatic amputation of left shoulder and upper arm, level unspecified",
        "S08.89" to "Traumatic amputation of other parts of head",
        "R58" to "Hemorrhage, not elsewhere classified",
        "R29.898" to "Other symptoms and signs involving the musculoskeletal system",
        "M95.9" to "Acquired deformity of musculoskeletal system, unspecified",
        "M21.941" to "Unspecified acquired deformity of hand, right hand",
        "M21.942" to "Unspecified acquired deformity of hand, left hand",
        "M21.6X1" to "Other acquired deformities of right foot",
        "M21.6X2" to "Other acquired deformities of left foot",
        "M21.961" to "Unspecified acquired deformity of right lower leg",
        "M21.962" to "Unspecified acquired deformity of left lower leg",
        "M21.951" to "Unspecified acquired deformity of right thigh",
        "M21.952" to "Unspecified acquired deformity of left thigh",
        "M95.5" to "Acquired deformity of pelvis",
        "M43.8X6" to "Other specified deforming dorsopathies, lumbar region",
        "M21.931" to "Unspecified acquired deformity of right forearm",
        "M21.932" to "Unspecified acquired deformity of left forearm",
        "M21.821" to "Other specified acquired deformities of right upper arm",
        "M21.822" to "Other specified acquired deformities of left upper arm",
        "M95.4" to "Acquired deformity of chest and rib",
        "M43.8X2" to "Other specified deforming dorsopathies, cervical region",
        "M95.3" to "Acquired deformity of neck",
        "M95.2" to "Other acquired deformity of head",
        "S61.401" to "Unspecified open wound of right hand",
        "S61.402" to "Unspecified open wound of left hand",
        "S91.301" to "Unspecified open wound, right foot",
        "S91.302" to "Unspecified open wound, left foot",
        "S91.001" to "Unspecified open wound, right ankle",
        "S91.002" to "Unspecified open wound, left ankle",
        "S81.801" to "Unspecified open wound, right lower leg",
        "S81.802" to "Unspecified open wound, left lower leg",
        "S71.101" to "Unspecified open wound, right thigh",
        "S71.102" to "Unspecified open wound, left thigh",
        "S31.50" to "Unspecified open wound of unspecified external genital organs",
        "S31.000" to "Unspecified open wound of lower back and pelvis without penetration into retroperitoneum",
        "S61.501" to "Unspecified open wound of right wrist",
        "S61.502" to "Unspecified open wound of left wrist",
        "S51.801" to "Unspecified open wound of right forearm",
        "S51.802" to "Unspecified open wound of left forearm",
        "S41.101" to "Unspecified open wound of right upper arm",
        "S41.102" to "Unspecified open wound of left upper arm",
        "S41.001" to "Unspecified open wound of right shoulder",
        "S41.002" to "Unspecified open wound of left shoulder",
        "S31.100" to "Unspecified open wound of abdominal wall, right upper quadrant without penetration into peritoneal cavity",
        "S21.1" to "Open wound of front wall of thorax without penetration into thoracic cavity",
        "S11.90" to "Unspecified open wound of unspecified part of neck",
        "S01.90" to "Unspecified open wound of unspecified part of head",
        "M84" to "Disorder of continuity of bone",
        "S62.91" to "Unspecified fracture of right wrist and hand",
        "S62.92" to "Unspecified fracture of left wrist and hand",
        "S92.901" to "Unspecified fracture of right foot",
        "S92.902" to "Unspecified fracture of left foot",
        "S82.91" to "Unspecified fracture of right lower leg",
        "S82.92" to "Unspecified fracture of left lower leg",
        "S82.001" to "Unspecified fracture of right patella",
        "S82.002" to "Unspecified fracture of left patella",
        "S72.91" to "Unspecified fracture of right femur",
        "S72.92" to "Unspecified fracture of left femur",
        "S72.001" to "Fracture of unspecified part of neck of right femur",
        "S72.002" to "Fracture of unspecified part of neck of left femur",
        "S32.89" to "Fracture of other parts of pelvis",
        "S32.009" to "Unspecified fracture of unspecified lumbar vertebra",
        "S52.91" to "Unspecified fracture of right forearm",
        "S52.92" to "Unspecified fracture of left forearm",
        "S42.301" to "Unspecified fracture of shaft of humerus, right arm",
        "S42.302" to "Unspecified fracture of shaft of humerus, left arm",
        "S42.91" to "Fracture of right shoulder girdle, part unspecified",
        "S42.92" to "Fracture of left shoulder girdle, part unspecified",
        "S22.49" to "Multiple fractures of ribs, unspecified side",
        "S48.40" to "Fracture of upper torso region",
        "S12.9" to "Fracture of neck, unspecified",
        "S02.92" to "Unspecified fracture of facial bones",
        "S02.91" to "Unspecified fracture of skull",
        "S60.221" to "Contusion of right hand",
        "S60.222" to "Contusion of left hand",
        "S90.31" to "Contusion of right foot",
        "S90.32" to "Contusion of left foot",
        "S90.01" to "Contusion of right ankle",
        "S90.02" to "Contusion of left ankle",
        "S80.11" to "Contusion of right lower leg",
        "S80.12" to "Contusion of left lower leg",
        "S80.01" to "Contusion of right knee",
        "S80.02" to "Contusion of left knee",
        "S70.11" to "Contusion of right thigh",
        "S70.12" to "Contusion of left thigh",
        "S70.01" to "Contusion of right hip",
        "S70.02" to "Contusion of left hip",
        "S30.1" to "Contusion of abdominal wall",
        "S30.0" to "Contusion of lower back and pelvis",
        "S60.211" to "Contusion of right wrist",
        "S60.212" to "Contusion of left wrist",
        "S50.11" to "Contusion of right forearm",
        "S50.12" to "Contusion of left forearm",
        "S40.021" to "Contusion of right upper arm",
        "S40.022" to "Contusion of left upper arm",
        "S40.011" to "Contusion of right shoulder",
        "S40.012" to "Contusion of left shoulder",
        "S20.21" to "Contusion of front wall of thorax",
        "S10.83" to "Contusion of other specified part of neck",
        "S10.0" to "Contusion of throat",
        "S00.93" to "Contusion of unspecified part of head",
        "Y37.250" to "Military operations involving fragments from munitions, military personnel",
        "S61.421" to "Laceration with foreign body of right hand",
        "S61.422" to "Laceration with foreign body of left hand",
        "S91.321" to "Laceration with foreign body, right foot",
        "S91.322" to "Laceration with foreign body, left foot",
        "S91.021" to "Laceration with foreign body, right ankle",
        "S91.022" to "Laceration with foreign body, left ankle",
        "S81.821" to "Laceration with foreign body, right lower leg",
        "S81.822" to "Laceration with foreign body, left lower leg",
        "S81.021" to "Laceration with foreign body, right knee",
        "S81.022" to "Laceration with foreign body, left knee",
        "S71.121" to "Laceration with foreign body, right thigh",
        "S71.122" to "Laceration with foreign body, left thigh",
        "S71.021" to "Laceration with foreign body, right hip",
        "S71.022" to "Laceration with foreign body, left hip",
        "S31.129" to "Laceration of abdominal wall with foreign body, unspecified quadrant without penetration into peritoneal cavity",
        "S31.020" to "Laceration with foreign body of lower back and pelvis without penetration into retroperitoneum",
        "S61.521" to "Laceration with foreign body of right wrist",
        "S61.522" to "Laceration with foreign body of left wrist",
        "S51.821" to "Laceration with foreign body of right forearm",
        "S51.822" to "Laceration with foreign body of left forearm",
        "S41.121" to "Laceration with foreign body of right upper arm",
        "S41.122" to "Laceration with foreign body of left upper arm",
        "S41.021" to "Laceration with foreign body of right shoulder",
        "S41.022" to "Laceration with foreign body of left shoulder",
        "S21.12" to "Laceration with foreign body of front wall of thorax without penetration into thoracic cavity",
        "S21.22" to "Laceration with foreign body of back wall of thorax without penetration into thoracic cavity",
        "S11.92" to "Laceration with foreign body of unspecified part of neck",
        "S01.92" to "Laceration with foreign body of unspecified part of head",
        "S61.411" to "Laceration without foreign body of right hand",
        "S61.412" to "Laceration without foreign body of left hand",
        "S91.311" to "Laceration without foreign body, right foot",
        "S91.312" to "Laceration without foreign body, left foot",
        "S91.011" to "Laceration without foreign body, right ankle",
        "S91.012" to "Laceration without foreign body, left ankle",
        "S81.811" to "Laceration without foreign body, right lower leg",
        "S81.812" to "Laceration without foreign body, left lower leg",
        "S81.011" to "Laceration without foreign body, right knee",
        "S81.012" to "Laceration without foreign body, left knee",
        "S71.111" to "Laceration without foreign body, right thigh",
        "S71.112" to "Laceration without foreign body, left thigh",
        "S71.011" to "Laceration without foreign body, right hip",
        "S71.012" to "Laceration without foreign body, left hip",
        "S31.119" to "Laceration without foreign body of abdominal wall, unspecified quadrant without penetration into peritoneal cavity",
        "S31.010" to "Laceration without foreign body of lower back and pelvis without penetration into retroperitoneum",
        "S61.511" to "Laceration without foreign body of right wrist",
        "S61.512" to "Laceration without foreign body of left wrist",
        "S51.811" to "Laceration without foreign body of right forearm",
        "S51.812" to "Laceration without foreign body of left forearm",
        "S41.111" to "Laceration without foreign body of right upper arm",
        "S41.112" to "Laceration without foreign body of left upper arm",
        "S41.011" to "Laceration without foreign body of right shoulder",
        "S41.012" to "Laceration without foreign body of left shoulder",
        "S21.11" to "Laceration without foreign body of front wall of thorax without penetration into thoracic cavity",
        "S21.21" to "Laceration without foreign body of back wall of thorax without penetration into thoracic cavity",
        "S11.91" to "Laceration without foreign body of unspecified part of neck",
        "S01.91" to "Laceration without foreign body of unspecified part of head",
        "R52" to "Pain, unspecified",
        "M79.641" to "Pain in right hand",
        "M79.642" to "Pain in left hand",
        "M79.671" to "Pain in right foot",
        "M79.672" to "Pain in left foot",
        "M25.571" to "Pain in right ankle and joints of right foot",
        "M25.572" to "Pain in left ankle and joints of left foot",
        "M79.661" to "Pain in right lower leg",
        "M79.662" to "Pain in left lower leg",
        "M25.561" to "Pain in right knee",
        "M25.562" to "Pain in left knee",
        "M79.651" to "Pain in right thigh",
        "M79.652" to "Pain in left thigh",
        "M25.551" to "Pain in right hip",
        "M25.552" to "Pain in left hip",
        "R10.30" to "Lower abdominal pain, unspecified",
        "M54.50" to "Low back pain, unspecified",
        "M25.531" to "Pain in right wrist",
        "M25.532" to "Pain in left wrist",
        "M79.631" to "Pain in right forearm",
        "M79.632" to "Pain in left forearm",
        "M79.621" to "Pain in right upper arm",
        "M79.622" to "Pain in left upper arm",
        "M25.511" to "Pain in right shoulder",
        "M25.512" to "Pain in left shoulder",
        "R10.84" to "Generalized abdominal pain",
        "R07.89" to "Other chest pain",
        "M54.2" to "Cervicalgia",
        "R51.9" to "Headache, unspecified",
        "Y37.2" to "Military operations involving other explosions and fragments",
        "T79.7" to "Traumatic subcutaneous emphysema",
        "S06.9" to "Unspecified intracranial injury",
        "Y37.100" to "Military operations involving unspecified destruction of aircraft, military personnel",
        "S67.21" to "Crushing injury of right hand",
        "S67.22" to "Crushing injury of left hand",
        "S97.81" to "Crushing injury of right foot",
        "S97.82" to "Crushing injury of left foot",
        "S97.01" to "Crushing injury of right ankle",
        "S97.02" to "Crushing injury of left ankle",
        "S87.81" to "Crushing injury of right lower leg",
        "S87.82" to "Crushing injury of left lower leg",
        "S87.01" to "Crushing injury of right knee",
        "S87.02" to "Crushing injury of left knee",
        "S77.11" to "Crushing injury of right thigh",
        "S77.12" to "Crushing injury of left thigh",
        "S77.01" to "Crushing injury of right hip",
        "S77.02" to "Crushing injury of left hip",
        "S38.1" to "Crushing injury of abdomen, lower back, and pelvis",
        "S67.31" to "Crushing injury of right wrist",
        "S67.32" to "Crushing injury of left wrist",
        "S57.81" to "Crushing injury of right forearm",
        "S57.82" to "Crushing injury of left forearm",
        "S47.1" to "Crushing injury of right shoulder and upper arm",
        "S47.2" to "Crushing injury of left shoulder and upper arm",
        "S28.0" to "Crushed chest",
        "S17.9" to "Crushing injury of neck, part unspecified",
        "S07.9" to "Crushing injury of head, part unspecified",
        "Y26" to "Exposure to smoke, fire and flames, undetermined intent",
        "X58" to "Exposure to other specified factors",
        "W40.9" to "Explosion of unspecified explosive materials",
        "Y37.200" to "Military operations involving unspecified explosion and fragments, military personnel",
        "T75.1" to "Unspecified effects of drowning and nonfatal submersion",
        "S31.609" to "Unspecified open wound of abdominal wall, unspecified quadrant with penetration into peritoneal cavity",
        "V95.9" to "Unspecified aircraft accident injuring occupant",
        "W55.81" to "Bitten by other mammals",
        "R09.01" to "Asphyxia",
        "Y09" to "Assault by unspecified means",
        "Z77.29" to "Contact with and (suspected) exposure to other hazardous substances",
        "W45.8" to "Other foreign body or object entering through skin",
        "Y93.79" to "Activity, other specified sports and athletics",
        "Y28.9" to "Contact with unspecified sharp object, undetermined intent",
        "W45" to "Foreign body or object entering through skin",
        "M79.54" to "Residual foreign body in soft tissue Hand",
        "M79.57" to "Residual foreign body in soft tissue Ankle and foot",
        "M79.56" to "Residual foreign body in soft tissue Lower leg",
        "M79.53" to "Residual foreign body in soft tissue Forearm",
        "M79.52" to "Residual foreign body in soft tissue Upper arm",
        "M79.51" to "Residual foreign body in soft tissue Shoulder region",
        "W19.XXXA" to "Unspecified fall, initial encounter",
        "V89.9XXA" to "Person injured in unspecified vehicle accident",
        "W40.9XXA" to "Explosion of unspecified explosive materials",
        "T14.8XXA" to "Other injury of unspecified body region",
        "V95.9XXA" to "Unspecified aircraft accident injuring occupant",
        "W55.81XA" to "Bitten by other mammals",
        "W45.8XXA" to "Other foreign body or object entering through skin",
        "V97.29XA" to "Other parachutist accident",
    )

    fun getDescForCode(code: String) : String? {
        return map[code]
    }

}
