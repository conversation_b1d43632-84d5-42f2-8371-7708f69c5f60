package mil.af.afrl.batman.hl7lib.data.models.v231.codetable

import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.data.models.v231.field.GENESIS_ALIAS
import mil.af.afrl.batman.hl7lib.data.models.v231.field.LOINC
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup

//region common codes
fun obr25ResultStatusCode(endpoint: Endpoint) = "F"
//Labs use F = Final and A = Addendum
//everything else use F = final and C = Corrections
//
fun obr04CodingScheme(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> LOINC
        EndpointType.OMDS -> ""
        EndpointType.TAC -> LOINC
    }
fun obx033CodingScheme(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> LOINC
        EndpointType.OMDS -> ""
        EndpointType.TAC -> GENESIS_ALIAS
    }
fun obx033CodingSchemeLab(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> LOINC
        EndpointType.OMDS -> ""
        EndpointType.TAC -> GENESIS_ALIAS
    }
fun obx033NoteCodingScheme(endpoint: Endpoint) =
    if (endpoint.format == EndpointType.TAC) {
        GENESIS_ALIAS
    } else {
        obx033CodingScheme(endpoint)
    }
//endregion

//region Blood Codes
fun bloodObrCode(endpoint: Endpoint) : CodeNameGroup {
    return when (endpoint.format) {
        EndpointType.MHSG_T, EndpointType.TAC -> CodeNameGroup("79569-0", "Blood product given [Type]")
        EndpointType.OMDS -> CodeNameGroup("VITALSIGNS", "")
    }
}
//endregion

//region Vent Codes
fun ventObrCode(endpoint: Endpoint) : CodeNameGroup {
    return when (endpoint.format) {
        EndpointType.MHSG_T, EndpointType.TAC -> CodeNameGroup("69348-1", "Respiratory assist status")
        EndpointType.OMDS -> CodeNameGroup("VITALSIGNS", "")
    }

}
//endregion

//region Note Codes
fun traumaNoteCode(endpoint: Endpoint) = CodeNameGroup("6816206803", "Trauma Note")

fun transferNoteCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.OMDS, EndpointType.TAC -> CodeNameGroup("2820561", "Transfer Note")
        EndpointType.MHSG_T -> CodeNameGroup("18761-7", "Transfer summary note")
    }

val inboundTransferNoteCodes = listOf("18761-7", "2820561", "NotesGlobalComplex")

fun noteObr24DiagnosticServiceSectionId(endpoint: Endpoint) = "MDOC"
//endregion

fun triageObrCode(endpoint: Endpoint) = triageObrCode(endpoint.format)
fun triageObrCode(endpoint: EndpointType) =
    when (endpoint) {
        EndpointType.OMDS -> null
        EndpointType.MHSG_T -> null
        EndpointType.TAC -> CodeNameGroup("67493-7", "Initial patient acuity NEMSIS")
    }

fun evacPrecedenceObrCode(endpoint: Endpoint) = evacPrecedenceObrCode(endpoint.format)
fun evacPrecedenceObrCode(endpoint: EndpointType) =
    when (endpoint) {
        EndpointType.OMDS -> null
        EndpointType.MHSG_T -> null
        EndpointType.TAC -> CodeNameGroup("77153-5", "EMS Transport mode descriptors NEMSIS")
    }
