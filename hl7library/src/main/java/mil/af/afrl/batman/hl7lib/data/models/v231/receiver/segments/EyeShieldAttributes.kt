package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments

import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.treatment.EyeShieldData
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.cdpEyeShieldFromCode

fun buildEyeShieldData(attributes: List<OBX>) : EyeShieldData? {
    var eyeData: EyeShieldData? = null
    
    for (code in attributes.map { it.obx3_ObservationIdentifier.ce1_Identifier.value }){
        eyeData = cdpEyeShieldFromCode(code)
    }
    
    return eyeData
}