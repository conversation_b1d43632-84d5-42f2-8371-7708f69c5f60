package mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.orur01

import ca.uhn.hl7v2.model.v231.message.ORU_R01
import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.observation.EFastExamData
import gov.afrl.batdok.encounter.observation.Observation
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.buildEFastObxList
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.efastCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.obx033NoteCodingScheme
import mil.af.afrl.batman.hl7lib.util.HL7Data
import java.time.Instant

class EFastOruBuilder(
    hl7Data: HL7Data,
    defaultTimestamp: Instant
) : BaseRepeatableORU_R01Builder<Observation>(hl7Data, defaultTimestamp) {

    override fun buildOrus(): List<ORU_R01> {
        val efastItems = hl7Data.document!!.observations.list.filter { it.observationData is EFastExamData }
        return efastItems.mapNotNull {
            buildRepeatableDataOru(hl7Data, it)
                ?.apply { getObservationGroup().obr.obr24_DiagnosticServSectID.parse("RAD") }
        }
    }

    override fun getObrDetails(item: Observation) = ItemData(
        item.timestamp,
        efastCode(endpoint.format)!!,  // This should never be sent to GT, so non-null
        item.id,
        obx033NoteCodingScheme(endpoint)
    )

    override fun buildObservationList(msg: ORU_R01, item: Observation): List<OBX> {
        return buildEFastObxList(msg, item.getData<EFastExamData>()!!, item.timestamp, endpoint)
    }

}
