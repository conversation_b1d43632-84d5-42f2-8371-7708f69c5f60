package mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment

import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.Message
import ca.uhn.hl7v2.model.v231.datatype.CE
import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.observation.BreathSoundsData
import gov.afrl.batdok.encounter.observation.CasualtyPPEData
import gov.afrl.batdok.encounter.observation.ChestRiseFallData
import gov.afrl.batdok.encounter.observation.CommonObservations
import gov.afrl.batdok.encounter.observation.Observation
import gov.afrl.batdok.encounter.observation.PulseValuesData
import gov.afrl.batdok.encounter.observation.RhythmData
import gov.afrl.batdok.encounter.observation.PupilDilationData
import gov.afrl.batdok.encounter.observation.TextData
import gov.afrl.batdok.encounter.treatment.ChestSealData
import gov.afrl.batdok.encounter.treatment.ChestTubeData
import gov.afrl.batdok.encounter.treatment.CommonTreatments
import gov.afrl.batdok.encounter.treatment.FingerThorData
import gov.afrl.batdok.encounter.treatment.DressingData
import gov.afrl.batdok.encounter.treatment.FoleyCatheterData
import gov.afrl.batdok.encounter.treatment.ImmobilizationData
import gov.afrl.batdok.encounter.treatment.EyeShieldData
import gov.afrl.batdok.encounter.treatment.HypothermiaPreventionData
import gov.afrl.batdok.encounter.treatment.LineData
import gov.afrl.batdok.encounter.treatment.NeedleDData
import gov.afrl.batdok.encounter.treatment.PericardiocentesisData
import gov.afrl.batdok.encounter.treatment.PelvicBinderData
import gov.afrl.batdok.encounter.treatment.TqData
import gov.afrl.batdok.encounter.treatment.Treatment
import gov.afrl.batdok.encounter.treatment.TubeData
import gov.afrl.batdok.encounter.treatment.WoundPackingData
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.converter.TimeConverter
import mil.af.afrl.batman.hl7lib.converter.UnitConverter
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.casualtyPPETypeCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.cdpAnteriorAxillaryCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.cdpBreathSoundsCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.dressingLocationCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.cdpDressingTypeCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.cdpChestTubeSideCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.cdpGaugeCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.cdpIoSizeCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.cdpIvIoLocationCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.cdpPulsePresenceCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.cdpSplintLocationCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.cdpTqLocationCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.cdpTqSubLocationCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.cdpTypeCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.chestLeftGreaterCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.chestMovementSymmetricalCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.chestRightGreaterCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.chestSealLocationCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.ettDepthCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.eyeShieldBothCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.eyeShieldLeftCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.eyeShieldRightCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.fingerThorSideCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.foleyCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.foleySizeCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.foleyTypeCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.getTopLevelProcedureCodeType
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.leftPupilDilationCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.lineSizeCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.needleDLocationCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.needleDSideCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.pulseValueCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.rhythmTypeCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.otherHypothermiaPreventionDetailCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.pelvicBinderTypeCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.pericardiocentesisVolumeCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.rightPupilDilationCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.splintTypeCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.visualAcuityTestTextDataCode
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ExtendedCodedElement
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup
import mil.af.afrl.batman.hl7lib.util.EscapeUtil
import org.openehealth.ipf.modules.hl7.kotlin.from
import org.openehealth.ipf.modules.hl7.kotlin.nrp
import java.time.Instant

class ProcedureAttributeObxBuilder(private val parent: AbstractMessage, private val endpoint: Endpoint) {

    private var msgIdx = 1

    fun addTreatmentAttributes(treat: Treatment): List<OBX> {
        msgIdx = 1
        return when (treat.name) {
            CommonTreatments.LINE.dataString -> addIvIoAttributes(treat)
            CommonTreatments.TQ.dataString -> addTqAttributes(treat)
            CommonTreatments.ET_TUBE.dataString -> addEttAttributes(treat)
            CommonTreatments.DRESSING.dataString -> addDressingAttributes(treat)
            CommonTreatments.CHEST_TUBE.dataString -> addChestTubeAttributes(treat)
            CommonTreatments.CHEST_SEAL.dataString -> addChestSealAttributes(treat)
            CommonTreatments.FINGER_THOR.dataString -> addFingerThorAttributes(treat)
            CommonTreatments.NEEDLE_D.dataString -> addNeedleDAttributes(treat)
            CommonTreatments.EYE_SHIELD.dataString -> addEyeShieldAttributes(treat)
            CommonTreatments.HYPOTHERMIA_PREVENTION.dataString -> addHypothermiaPreventionAttributes(treat)
            CommonTreatments.IMMOBILIZATION.dataString -> addImmobilizationAttributes(treat)
            CommonTreatments.FOLEY_CATHETER.dataString -> addFoleyAttributes(treat)
            CommonTreatments.WOUND_PACKING.dataString -> addWoundPackingAttributes(treat)
            CommonTreatments.PELVIC_BINDER.dataString -> addPelvicBinderAttributes(treat)
            CommonTreatments.PERICARDIOCENTESIS.dataString -> addPericadiocentesisAttributes(treat)
            else -> emptyList()
        }
    }

    fun addObservationAttributes(observation: Observation): List<OBX>{
        msgIdx = 1
        return when (observation.name){
            CommonObservations.BREATH_SOUNDS.dataString -> addBreathSoundsAttributes(observation)
            /*Rhythm does not need to be discrate for now
            CommonObservations.RHYTHM.dataString -> addRhythmAttributes(observation)*/
            // not needed for cdp yet
            // CommonObservations.PULSE_VALUES.dataString -> addPulsesAttributes(observation)
            
            CommonObservations.PUPIL_DILATION.dataString -> addPupilDilationAttributes(observation)
            CommonObservations.CASUALTY_PPE.dataString -> addCasualtyPPEAttributes(observation)
            "Visual Acuity Test" -> addVisualAcuityTestAttributes(observation)
            CommonObservations.CHEST_EQUAL_RISE_FALL.dataString -> addChestEqualAttributes(observation)
            else -> emptyList()
        }
    }

    private fun addIvIoAttributes(treat: Treatment): List<OBX> {
        val obxs = mutableListOf<OBX?>()
        val data = treat.treatmentData as? LineData ?: return emptyList()

        if (endpoint.format != EndpointType.MHSG_T) {
            val location = listOfNotNull(data.side, data.location).joinToString(" ")
            obxs += buildAttributeObx(cdpIvIoLocationCode(location), location, timestamp = treat.timestamp)
            obxs += buildAttributeObx(cdpGaugeCode(data.gauge), data.gauge, timestamp = treat.timestamp)
            obxs += buildAttributeObx(cdpIoSizeCode(data.size), data.size, timestamp = treat.timestamp)
            obxs += buildAttributeObx(cdpTypeCode(data.subtype), data.subtype, timestamp = treat.timestamp)
        }
        return obxs.filterNotNull()
    }

    private fun addTqAttributes(treat: Treatment): List<OBX> {
        val obxs = mutableListOf<OBX?>()
        val data = treat.treatmentData as? TqData ?: return emptyList()

        if (endpoint.format != EndpointType.MHSG_T) {
            obxs += buildAttributeObx(cdpTqLocationCode(data.tqLocation), data.tqLocation, timestamp = treat.timestamp)
            obxs += buildAttributeObx(cdpTqSubLocationCode(data.subLocation), data.subLocation, timestamp = treat.timestamp)
        }

        return obxs.filterNotNull()
    }

    private fun addEttAttributes(treat: Treatment): List<OBX> {
        val data = treat.treatmentData as? TubeData ?: return emptyList()
        val depthUnitCode = data.depthUnit?.let { UnitConverter.batdokDataToCodedElement(parent, it, endpoint) }
        return listOfNotNull(buildAttributeObx(ettDepthCode(endpoint), data.depth, depthUnitCode, treat.timestamp))
    }

    private fun addDressingAttributes(treat: Treatment): List<OBX> {
        val obxs = mutableListOf<OBX?>()
        val data = treat.treatmentData as? DressingData ?: return emptyList()
        val knownType = DressingData.Type.entries.map { it.dataString }.toSet()
        val type = if (data.type !in knownType) "Other" else data.type
        
        if (endpoint.format != EndpointType.MHSG_T){
            obxs += buildAttributeObx(cdpDressingTypeCode(data.type), type, timestamp = treat.timestamp)
            obxs += buildAttributeObx(data.location?.ifEmpty { null }.let { it?.let {
                dressingLocationCode(endpoint.format)
            } }, data.location, timestamp = treat.timestamp)
        }
        
        return obxs.filterNotNull()
    }

    private fun addWoundPackingAttributes(treat: Treatment) : List<OBX> {
        val obxs = mutableListOf<OBX?>()
        val data = treat.treatmentData as? WoundPackingData ?: return emptyList()

        if (endpoint.format != EndpointType.MHSG_T){
            obxs += buildAttributeObx(cdpDressingTypeCode(data.type), data.type, timestamp = treat.timestamp)
            obxs += buildAttributeObx(data.location?.ifEmpty { null }.let { it?.let {
                dressingLocationCode(endpoint.format)
            } }, data.location, timestamp = treat.timestamp)
        }

        return obxs.filterNotNull()
    }

    private fun addChestTubeAttributes(treat: Treatment) : List<OBX> {
        val data = treat.treatmentData as? ChestTubeData ?: return emptyList()
        val side = data.location ?: return emptyList()
        if (endpoint.format != EndpointType.MHSG_T) {
            val sideSegment = buildAttributeObx(cdpChestTubeSideCode(side), side, timestamp = treat.timestamp)
            if (sideSegment != null) {
                return listOfNotNull(
                    sideSegment,
                    buildAttributeObx(cdpAnteriorAxillaryCode(), "Anterior Axillary", timestamp = treat.timestamp)
                ).apply {
                    // Set OBX.4 to 1 to "group" these segments for CDP
                    map { it.obx4_ObservationSubID.parse("1") }
                }
            }
        }
        return emptyList()
    }
    
    private fun addBreathSoundsAttributes(observation: Observation): List<OBX> {
        val obxs = mutableListOf<OBX?>()
        val data = observation.observationData as? BreathSoundsData ?: return emptyList()
        val typeList = data.typeList
        
        typeList.forEach { type ->
            if (endpoint.format != EndpointType.MHSG_T){
                obxs += buildAttributeObx(cdpBreathSoundsCode(type), type, timestamp = observation.timestamp)
            }
        }
        return obxs.filterNotNull()
    }

    private fun addChestSealAttributes(treat: Treatment) : List<OBX> {
        val data = treat.treatmentData as? ChestSealData ?: return emptyList()
        val location = data.location ?: return emptyList()
        if (endpoint.format != EndpointType.MHSG_T) {
            return listOfNotNull(buildAttributeObx(chestSealLocationCode(location), location, timestamp = treat.timestamp))
        }
        return emptyList()
    }

    private fun addFingerThorAttributes(treat: Treatment) : List<OBX> {
        val data = treat.treatmentData as? FingerThorData ?: return emptyList()
        val side = data.location ?: return emptyList()
        if (endpoint.format != EndpointType.MHSG_T) {
            return listOfNotNull(buildAttributeObx(fingerThorSideCode(side), side, timestamp = treat.timestamp))
        }
        return emptyList()
    }

    private fun addNeedleDAttributes(treat: Treatment): List<OBX> {
        val obxs = mutableListOf<OBX?>()
        val data = treat.treatmentData as? NeedleDData ?: return emptyList()

        if (endpoint.format != EndpointType.MHSG_T) {
            obxs += buildAttributeObx(data.location?.let { needleDSideCode(it) }, data.location, timestamp = treat.timestamp)
            obxs += buildAttributeObx(data.location?.let { needleDLocationCode(it) }, data.location, timestamp = treat.timestamp)
        }

        // Set OBX.4 to 1 to "group" these segments for CDP
        return obxs.filterNotNull().apply { map { it.obx4_ObservationSubID.parse("1") } }
    }

    private fun addCasualtyPPEAttributes(observation: Observation): List<OBX> {
        val obxs = mutableListOf<OBX?>()
        val data = observation.observationData as? CasualtyPPEData ?: return emptyList()
        val ppeList = data.ppeList
        ppeList.forEach { type ->
            if (endpoint.format != EndpointType.MHSG_T){
                obxs += buildAttributeObx(casualtyPPETypeCode(type), type, timestamp = observation.timestamp)
            }
        }
        return obxs.filterNotNull()
    }

    private fun addFoleyAttributes(treat: Treatment): List<OBX> {
        val obxs = mutableListOf<OBX?>()
        val data = treat.treatmentData as? FoleyCatheterData ?: return emptyList()

        if (endpoint.format != EndpointType.MHSG_T) {
            //batdok stores the foley type in the color field for now
            obxs += buildAttributeObx(foleyTypeCode(data.color.toString()), data.color, timestamp = treat.timestamp)
            obxs += buildAttributeObx(foleySizeCode(endpoint.format), data.size, timestamp = treat.timestamp)
        }
        return obxs.filterNotNull()
    }
    
    private fun addEyeShieldAttributes(treat: Treatment) : List<OBX> {
        val obxs = mutableListOf<OBX?>()
        val data = treat.getData<EyeShieldData>() ?: return emptyList()

        obxs += when {
            data.left && data.right -> buildAttributeObx(eyeShieldBothCode(endpoint.format), "Both", timestamp = treat.timestamp)
            data.left -> buildAttributeObx(eyeShieldLeftCode(endpoint.format), "Left", timestamp = treat.timestamp)
            data.right -> buildAttributeObx(eyeShieldRightCode(endpoint.format), "Right", timestamp = treat.timestamp)
            else -> null
        }

        return obxs.filterNotNull()
    }

    private fun addHypothermiaPreventionAttributes(treat: Treatment) : List<OBX> {
        val data = treat.getData<HypothermiaPreventionData>() ?: return emptyList()
        // Blanket is being coded differently. Everything else maps to "Other" hypothermia treatments
        // This attribute specifies which "other" type that is
        if (data.type != HypothermiaPreventionData.Type.BLANKET.dataString) {
            return listOfNotNull(
                buildAttributeObx(otherHypothermiaPreventionDetailCode(endpoint.format), data.type, timestamp = treat.timestamp)
            )
        }
        return emptyList()
    }

    private fun addPupilDilationAttributes(treat: Observation) : List<OBX> {
        val data = treat.getData<PupilDilationData>() ?: return emptyList()
        val obxs = mutableListOf<OBX?>()

        val mmCode = UnitConverter.batdokDataToCodedElement(parent, "mm", endpoint)
        obxs += buildAttributeObx(leftPupilDilationCode(endpoint.format), data.perrlaSizeLeft, mmCode, treat.timestamp)
        obxs += buildAttributeObx(rightPupilDilationCode(endpoint.format), data.perrlaSizeRight, mmCode, treat.timestamp)

        return obxs.filterNotNull()
    }

    private fun addRhythmAttributes(observation: Observation): List<OBX> {
        val obxs = mutableListOf<OBX?>()
        val data = observation.observationData as? RhythmData ?: return emptyList()
        val typeData = data.type
        
        val types = typeData?.split(";") ?: return emptyList()
        types.forEach { type ->
            if (endpoint.format != EndpointType.MHSG_T){
                obxs += buildAttributeObx(rhythmTypeCode(type), type, timestamp = observation.timestamp)
            }
        }
        return obxs.filterNotNull()
    }
    
    private fun addPulsesAttributes(observation: Observation): List<OBX>{
        val obxs = mutableListOf<OBX?>()
        val data = observation.observationData as? PulseValuesData ?: return emptyList()
        
        if (endpoint.format != EndpointType.MHSG_T) {
            val pulseMap = mapOf(
                "brachial" to data.brachial,
                "carotid" to data.carotid,
                "femoral" to data.carotid,
                "pedal" to data.pedal,
                "radial" to data.radial,
                "temperature" to data.temperature
            )

            pulseMap.filterValues { it != null }.forEach { (pulseName, quality) ->
                obxs += buildAttributeObx(
                    pulseValueCode(pulseName),
                    quality,
                    timestamp = observation.timestamp
                )
                //obxs += buildAttributeObx(quality?.let { pulseQualityCode(it) }, quality, timestamp = observation.timestamp)
            }
        }
        
        return obxs.filterNotNull()
    }

    private fun addVisualAcuityTestAttributes(observation: Observation): List<OBX> {
        val obxs = mutableListOf<OBX?>()
        val obsData = observation.observationData
        if (endpoint.format != EndpointType.MHSG_T){
            if (obsData is TextData) {
                val description = obsData.description
                obxs += buildAttributeObx(visualAcuityTestTextDataCode(endpoint.format), description, timestamp = observation.timestamp)
            }
        }
        return obxs.filterNotNull()
    }

    private fun addPericadiocentesisAttributes(treat: Treatment) : List<OBX> {
        val data = treat.getData<PericardiocentesisData>() ?: return emptyList()
        val obxs = mutableListOf<OBX?>()
        val unit =
            data.volumeUnit?.let { UnitConverter.batdokDataToCodedElement(parent, it, endpoint) }
        
        obxs += buildAttributeObx(pericardiocentesisVolumeCode(endpoint.format), data.volume,unit ,treat.timestamp)

        return obxs.filterNotNull()
    }

    private fun addPelvicBinderAttributes(treat: Treatment): List<OBX> {
        val pelvicBinderData = treat.getData<PelvicBinderData>() ?: return emptyList()
        return listOfNotNull(buildAttributeObx(pelvicBinderTypeCode(endpoint.format), pelvicBinderData.type, timestamp = treat.timestamp))
    }

    private fun addImmobilizationAttributes(treat: Treatment): List<OBX> {
        val immobilizationData = treat.getData<ImmobilizationData>() ?: return emptyList()
        val immobilizationType = immobilizationData.type ?: return emptyList()

        val attributes = mutableListOf<OBX?>()
        if (endpoint.format != EndpointType.MHSG_T) {
            val immobilizationPulse = immobilizationData.neurovascularAfter?.pulse?.dataString
            if(immobilizationPulse!=null) {
                attributes.add(
                    buildAttributeObx(
                        cdpPulsePresenceCode(immobilizationPulse),
                        immobilizationPulse,
                        timestamp = treat.timestamp
                    )
                )
            }
            val immobilizationLocation = immobilizationData.location
            if(immobilizationLocation!=null) {
                attributes.add(
                    buildAttributeObx(
                        cdpSplintLocationCode(immobilizationLocation),
                        immobilizationLocation,
                        timestamp = treat.timestamp
                    )
                )
            }
            if(immobilizationType != ImmobilizationData.Type.C_COLLAR.dataString
                && immobilizationType != ImmobilizationData.Type.C_SPINE.dataString
                && immobilizationType != ImmobilizationData.Type.SPINE_BOARD.dataString
                && immobilizationType != ImmobilizationData.Type.SWATH.dataString
            ) {
                attributes.add(buildAttributeObx(splintTypeCode(endpoint.format), immobilizationType, timestamp = treat.timestamp))
            }
        }
        return attributes.filterNotNull()
    }

    private fun addChestEqualAttributes(observation: Observation) : List<OBX> {
        val chestData = observation.getData<ChestRiseFallData>() ?: return emptyList()
        if (endpoint.format != EndpointType.MHSG_T) {
            val code = when (chestData.riseOrFall) {
                ChestRiseFallData.Type.L_R.dataString -> chestLeftGreaterCode(endpoint.format)
                ChestRiseFallData.Type.R_L.dataString -> chestRightGreaterCode(endpoint.format)
                ChestRiseFallData.Type.SYMMETRICAL.dataString -> chestMovementSymmetricalCode(endpoint.format)
                else -> return emptyList()
            }
            return listOfNotNull(buildAttributeObx(code, chestData.riseOrFall, timestamp = observation.timestamp))
        }
        return emptyList()
    }

    private fun buildAttributeObx(code: CodeNameGroup?, data: Any?, unit: CE? = null,
                                  timestamp: Instant? = Instant.now()): OBX? {
        code ?: return null
        data ?: return null
        return OBX(parent, parent.modelClassFactory).apply {
            obx1_SetIDOBX.from(msgIdx)
            obx2_ValueType.parse(if (data.toString().startsWith("+") || data.toString().toDoubleOrNull() == null) "TX" else "NM")
            obx3_ObservationIdentifier.from(ExtendedCodedElement(parent as Message, code, getTopLevelProcedureCodeType(endpoint)))
            obx4_ObservationSubID.from(msgIdx)  // Needed for CDP
            nrp(5).parse(EscapeUtil.escape(data.toString()))
            unit?.let(obx6_Units::from)
            obx11_ObservationResultStatus.parse("F")
            obx14_DateTimeOfTheObservation.parse(TimeConverter.timestampToFormattedDateTime(timestamp))
            msgIdx++
        }
    }
}
