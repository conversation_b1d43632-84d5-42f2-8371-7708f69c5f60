package mil.af.afrl.batman.hl7lib.dataexchanger

import android.content.Context
import mil.af.afrl.batman.hl7lib.OmdsNetworkEndpoint
import mil.af.afrl.batman.hl7lib.MessageRequester
import mil.af.afrl.batman.hl7lib.util.HL7Data
import mil.af.afrl.batman.hl7lib.util.InboundHL7Data
import mil.af.afrl.batman.hl7lib.util.AuthTokenRequestor
import mil.af.afrl.batman.hl7lib.util.NetworkResponse

object OmdsDataRequestor {

    suspend fun requestOmdsData(
        context: Context, hl7Data: InboundHL7Data,
        refreshTokenInfo: String,
        onStatusUpdate: (NetworkResponse) -> Unit,
        onImportComplete: ((HL7Data) -> Unit)? = null,
        onTokenRefresh: ((String) -> Unit)
    ) {
        if (hl7Data.endpoint !is OmdsNetworkEndpoint) return
        hl7Data.endpoint?.let {
            AuthTokenRequestor().getAuthToken(
                it as OmdsNetworkEndpoint, refreshTokenInfo,
                onTokenAvailable = {
                    hl7Data.authToken = it
                    setRequesterCallback(onStatusUpdate, onImportComplete)
                    MessageRequester.requestMessages(hl7Data, context)
                },
                onTokenRefresh = { refreshToken ->
                    onTokenRefresh.invoke(refreshToken)
                },
                onError = onStatusUpdate  // Just pass on the error here
            )
        }
    }

    private fun setRequesterCallback(
        onStatusUpdate: (NetworkResponse) -> Unit,
        onImportComplete: ((HL7Data) -> Unit)?
    ) {
        MessageRequester.requestCompletedCallback = { newData, response ->
            onStatusUpdate(response)
            if (response.isSuccess()) {  // HTTP success
                onImportComplete?.invoke(newData)
            }
            MessageRequester.requestCompletedCallback = null
        }
    }

}
