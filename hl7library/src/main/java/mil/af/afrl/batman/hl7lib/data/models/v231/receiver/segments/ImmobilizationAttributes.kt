package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments

import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.treatment.ImmobilizationData
import gov.afrl.batdok.encounter.treatment.NeurovascularStatus
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.cCollarCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.cSpineCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.locationFromSplintLocationCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.pulseNotPresentCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.pulsePresentCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.spineBoardCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.splintCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.splintTypeCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.swathCode
import org.openehealth.ipf.modules.hl7.kotlin.value

fun immobilizationAttributes(attributes: List<OBX>, endpointType: EndpointType, treatCode: String) : ImmobilizationData {
    var pulsePresent: NeurovascularStatus? = null
    var location: String? = null
    var type: String? = null

    when (treatCode) {
        cCollarCode(endpointType)?.code -> ImmobilizationData.Type.C_COLLAR.dataString
        cSpineCode(endpointType)?.code -> ImmobilizationData.Type.C_SPINE.dataString
        spineBoardCode(endpointType)?.code -> ImmobilizationData.Type.SPINE_BOARD.dataString
        swathCode(endpointType)?.code -> ImmobilizationData.Type.SWATH.dataString
        splintCode(endpointType)?.code -> "Unknown Splint Type"
        else -> null
    }?.let {
        type = it
    }

    for (attr in attributes) {
        val code = attr.obx3_ObservationIdentifier.ce1_Identifier?.value ?: continue
        val value = attr.obx5_ObservationValue.firstOrNull()?.value ?: continue

        if (code == pulsePresentCode(endpointType)?.code) {
            // Pulse presence is only ever the after status
            pulsePresent = NeurovascularStatus(ImmobilizationData.TernaryStatus.YES)
        }

        if (code == pulseNotPresentCode(endpointType)?.code) {
            pulsePresent = NeurovascularStatus(ImmobilizationData.TernaryStatus.NO)
        }

        locationFromSplintLocationCode(code)?.let {
            location = it
        }

        if (code == splintTypeCode(endpointType)?.code) {
            type = value
        }
    }

    return ImmobilizationData(type = type, location = location, neurovascularAfter = pulsePresent)
}
