package mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.orur01

import ca.uhn.hl7v2.model.v231.group.ORU_R01_OBXNTE
import ca.uhn.hl7v2.model.v231.message.ORU_R01
import ca.uhn.hl7v2.model.v231.segment.OBX
import mil.af.afrl.batman.batdokid.DomainId
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup
import mil.af.afrl.batman.hl7lib.util.HL7Data
import org.openehealth.ipf.modules.hl7.kotlin.from
import java.time.Instant

abstract class BaseRepeatableORU_R01Builder<T>(
    hl7Data: HL7Data,
    defaultTimestamp: Instant
) : BaseORU_R01Builder(hl7Data, defaultTimestamp) {

    protected data class ItemData(
        val time: Instant?,
        val codeNameGroup: CodeNameGroup,
        val id: DomainId?,
        val obrCodingScheme: String
    )

    protected abstract fun getObrDetails(item: T) : ItemData
    protected abstract fun buildObservationList(msg: ORU_R01, item: T) : List<OBX>

    protected fun buildRepeatableDataOru(
        hl7Data: HL7Data,
        item: T,
        alternateObrText: String = ""
    ): ORU_R01? {
        val (date, codeName, uuid, obr4Scheme) = getObrDetails(item)
        val oru = buildBaseOruR01(hl7Data, date ?: defaultTimestamp, codeName, uuid, obr4Scheme)
        oru.getObservationGroup().obr.obr4_UniversalServiceID.alternateText.parse(alternateObrText)

        val obsGroupList = buildObservationGroupList(oru, item)
        // Not all individual values will map to a code to export
        // In this case, return null so we don't have an ORU without OBXs
        return if (obsGroupList.isEmpty()) {
            null
        } else {
            obsGroupList.forEachIndexed { obsIdx, obsGroup ->
                oru.getObservationGroup().insertOBXNTE(obsGroup, obsIdx)
            }
            oru
        }
    }

    private fun buildObservationGroupList(msg: ORU_R01, item: T) : List<ORU_R01_OBXNTE> {
        return buildObservationList(msg, item).map { obx ->
            ORU_R01_OBXNTE(msg, msg.modelClassFactory).also { it.obx.from(obx) }
        }
    }
}
