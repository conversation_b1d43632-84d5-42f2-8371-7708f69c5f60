package mil.af.afrl.batman.hl7lib.data.models.v231.builder.message

import ca.uhn.hl7v2.model.AbstractSegment
import ca.uhn.hl7v2.model.v231.group.ADT_AXX_PR1ROL
import ca.uhn.hl7v2.parser.DefaultModelClassFactory
import ca.uhn.hl7v2.parser.ModelClassFactory
import gov.afrl.batdok.encounter.Document
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.group.PR1OBXROLBuilder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.AL1Builder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.PIDBuilder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.PV1Builder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.RepeatedSegmentBuilder
import mil.af.afrl.batman.hl7lib.data.models.v231.group.DG1_ZDG
import mil.af.afrl.batman.hl7lib.util.HL7Data
import org.openehealth.ipf.modules.hl7.kotlin.from

class ADT_A31Builder(private val endpoint: Endpoint, private val factory: ModelClassFactory? = DefaultModelClassFactory()) {

    private fun buildBaseAdtA31(hl7Data: HL7Data) : CustomADT_AXX {
        class A31Builder : AbstractMessageBuilder<CustomADT_AXX>() {
            override val msg = CustomADT_AXX(factory)
            override fun populateAll(hl7Data: HL7Data) = this
        }

        return A31Builder()
            .populateMsh("ADT", "A31", endpoint)
            .build()
            .apply {
                evn.evn1_EventTypeCode.parse("A31")
                evn.evn2_RecordedDateTime.from(msh.dateTimeOfMessage)
                pid.from(PIDBuilder(endpoint).populateFromBatdokPatient(this, hl7Data))
                pV1.from(PV1Builder(endpoint, hl7Data.providerInfo).populateFromBatdokPatient(this, hl7Data))
            }
    }

    fun populateAll(hl7Data: HL7Data): List<CustomADT_AXX> {
        if (hl7Data.document == null) return emptyList()
        val document = hl7Data.document!!
        val allergies = document.info.allergies
        val injuries = document.injuries.injuries
        val mois = document.injuries.mechanismsOfInjury
        val procedures = hl7Data.document!!.metadata.proceduresDone
        val treatments = hl7Data.document!!.treatments.list
        val removedTreats = hl7Data.document!!.treatments.onProperRemovedItems
        val observations = document.observations.list
        val a31List = mutableListOf<CustomADT_AXX>()

        if (allergies.isNotEmpty()) {
            val a31 = buildBaseAdtA31(hl7Data)
            populateRepeatedSegment(AL1Builder(endpoint), document, a31)
            a31List += a31
        }

        if (endpoint.format == EndpointType.OMDS) {
            if (injuries.isNotEmpty() || mois.isNotEmpty()) {
                val a31 = buildBaseAdtA31(hl7Data)
                val dg1ZdgGroups = populateDiagnoses(hl7Data, a31)
                dg1ZdgGroups.forEachIndexed { index, group ->
                    a31.insertDG1ZDG(group, index)
                }
                a31List += a31
            }

            if (procedures.isNotEmpty() || observations.isNotEmpty()
                || treatments.isNotEmpty() || removedTreats.isNotEmpty()) {
                val a31 = buildBaseAdtA31(hl7Data)
                populateProcedures(document, a31)
                a31List += a31
            }
        }

        return a31List
    }

    private fun populateRepeatedSegment(builder: RepeatedSegmentBuilder<out AbstractSegment>, document: Document, msg: CustomADT_AXX): CustomADT_AXX {
        builder.populateListFromBatdokPatient(msg, document).forEachIndexed { index, segment ->
            val repetition = msg.insertRepetition(segment.name, index) as AbstractSegment
            repetition.from(segment)
        }
        return msg
    }

    // PR1 segments get wrapped in message-specific groups, so it can't be populated as simply as other repeated segments
    private fun populateProcedures(document: Document, msg: CustomADT_AXX): CustomADT_AXX  {
        PR1OBXROLBuilder(endpoint).buildPR1OBXROLFromPatient(msg, document).mapIndexed { idx, group ->
            msg.insertPR1ROL(group, idx)
        }
        return msg
    }

    private fun populateDiagnoses(hl7Data: HL7Data, msg: CustomADT_AXX): List<DG1_ZDG>{
        return DG1_ZDG.dg1ZdgData(hl7Data, endpoint, msg)
    }

}
