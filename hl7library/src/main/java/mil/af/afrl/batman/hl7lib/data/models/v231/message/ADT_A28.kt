package mil.af.afrl.batman.hl7lib.data.models.v231.message

import ca.uhn.hl7v2.HL7Exception
import ca.uhn.hl7v2.model.v231.message.ADT_A28
import ca.uhn.hl7v2.parser.ModelClassFactory
import mil.af.afrl.batman.hl7lib.data.models.v231.group.ADT_A28_PR1OBXROL
import mil.af.afrl.batman.hl7lib.data.models.v231.group.DG1_ZDG

class ADT_A28(factory: ModelClassFactory) : ADT_A28(factory) {
    init {
        try {
            add(DG1_ZDG::class.java, false, true, 10)
            add(ADT_A28_PR1OBXROL::class.java, false, true, 12)
        } catch (e: HL7Exception) {
            throw RuntimeException("Failed to add custom groups to ADT_A28 message", e)
        }
    }

    fun getPr1ObxRol(): ADT_A28_PR1OBXROL {
        return get("PR1OBXROL") as ADT_A28_PR1OBXROL
    }

    fun getDg1ZdgAll() : List<DG1_ZDG> {
        return getAll("DG1_ZDG").filterIsInstance<DG1_ZDG>()
    }
}