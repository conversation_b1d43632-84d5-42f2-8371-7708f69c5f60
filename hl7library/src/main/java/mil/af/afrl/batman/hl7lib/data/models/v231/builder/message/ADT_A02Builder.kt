package mil.af.afrl.batman.hl7lib.data.models.v231.builder.message

import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.PV1Builder
import mil.af.afrl.batman.hl7lib.util.HL7Data
import org.openehealth.ipf.modules.hl7.kotlin.from

/**
 * Patient Transfer: https://hl7-definition.caristix.com/v2/HL7v2.3.1/TriggerEvents/ADT_A02
 *
 * TODO: This message is intended to use handoff or evac data in currently undefined ways.
 */
class ADT_A02Builder(private val endpoint: Endpoint): ADT_AXXBuilder("ADT", "A02", endpoint) {
    override fun populateAll(hl7Data: HL7Data): AbstractMessageBuilder<CustomADT_AXX> {
        if (hl7Data.document == null) return this
        super.populateAll(hl7Data)
        val pv1 = msg.pV1
        pv1.from(PV1Builder(endpoint, hl7Data.providerInfo).populateFromEvac(pv1, hl7Data.document!!))
        return this
    }
}
