package mil.af.afrl.batman.hl7lib.util

import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant
import java.util.UUID

// Derives an AppToAppId from this ID and another string
// You will usually want to use the default seedInstant here to make the IDs generated consistent
fun DomainId.hashedWith(otherString: String, seedInstant: Instant = Instant.ofEpochSecond(1702223462)) : DomainId {
    return DomainId.fromSeeded(seedInstant, unique + otherString.toByteArray(), DomainId::class.java)
}

fun DomainId?.asUuidString(): String {
    return this?.unique?.let { UUID.nameUUIDFromBytes(it).toString() } ?: ""
}

inline fun <reified T : DomainId> generateDomainIdFromString(id: String?): T {
    return if (id != null) {
        // Use a constant Instant here so that the IDs we generate here are reproducible
        DomainId.fromSeeded(Instant.ofEpochSecond(1712064020), id.toByteArray(), T::class.java)
    } else {
        DomainId.create()
    }
}
