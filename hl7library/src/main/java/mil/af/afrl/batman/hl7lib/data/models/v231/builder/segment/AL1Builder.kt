package mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment

import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.v231.segment.AL1
import gov.afrl.batdok.encounter.Document
import kotlinx.coroutines.runBlocking
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.converter.DBAllergyConverter
import org.openehealth.ipf.modules.hl7.kotlin.from

class AL1Builder(private val endpoint: Endpoint): RepeatedSegmentBuilder<AL1> {

    override fun populateListFromBatdokPatient(parent: AbstractMessage, document: Document): List<AL1> {
        return document.info.allergies.mapIndexed { idx, allergy ->
            val al1 = AL1(parent, parent.modelClassFactory)
            al1.al11_SetIDAL1.parse((idx + 1).toString())
            val codedAllergy = runBlocking { DBAllergyConverter.batdokDataToCodedElement(parent, allergy) }

            // Most allergies we have coded are drug allergies. However, BATDOK also reports some non-drug allergies that exist in the table
            val allergyType = if (codedAllergy?.ce3_NameOfCodingSystem?.value == "MULTUMDRUG") {
                "DA"
            } else "OTH"
            al1.al12_AllergyType.parse(allergyType)
            if (codedAllergy == null) {
                al1.al13_AllergyCodeMnemonicDescription.ce2_Text.parse(allergy)
            } else {
                al1.al13_AllergyCodeMnemonicDescription.from(codedAllergy)
            }
            al1.al14_AllergySeverity.parse("NE")  // Not Entered -- we don't track this data
            al1
        }
    }
}
