package mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment

import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.IndividualLab
import gov.afrl.batdok.encounter.panel.KnownLabs
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.converter.UnitConverter
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.*
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ExtendedCodedElement
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup
import mil.af.afrl.batman.hl7lib.util.EscapeUtil
import org.openehealth.ipf.modules.hl7.kotlin.from
import org.openehealth.ipf.modules.hl7.kotlin.nrp

fun populateListFromBatdokLab(parent: AbstractMessage, labs: List<IndividualLab>, endpoint: Endpoint): List<OBX> {
    val obxList = ArrayList<OBX>()
    var idx = 1

    fun addObx(lab: IndividualLab, code: CodeNameGroup?) {
        if (lab.value != null && code != null) {
            buildBaseOBX(parent, lab.timestamp).apply {
                setIDOBX.from(idx++)
                if (lab.value?.toDoubleOrNull() == null) valueType.parse("TX")
                observationIdentifier.from(ExtendedCodedElement(parent, code, obx033CodingSchemeLab(endpoint)))
                nrp(5).parse(lab.value)
                obx6_Units.from(lab.unit?.let { UnitConverter.batdokDataToCodedElement(parent, EscapeUtil.escape(it), endpoint) })
                obxList.add(this)
            }
        }
    }

    for (lab in labs) {
        addObx(lab, getCodeFromLabName(lab.name, endpoint))
    }

    return obxList
}

private fun getCodeFromLabName(name: String, endpoint: Endpoint) : CodeNameGroup? {
    return when (name) {
        KnownLabs.WBC.dataString -> labObx03WBCCode(endpoint)
        KnownLabs.HGB.dataString -> labObx03HgbCode(endpoint)
        KnownLabs.HCT.dataString -> labObx03HcTCode(endpoint)
        KnownLabs.PLT.dataString -> labObx03PLTCode(endpoint)
        KnownLabs.RBC.dataString -> rbcCode(endpoint)
        KnownLabs.MCV.dataString -> mcvCode(endpoint)
        KnownLabs.MPV.dataString -> mpvCode(endpoint)
        KnownLabs.ABS_LYMPHOCYTE.dataString -> absLymphocyteCode(endpoint)
        KnownLabs.ABS_MONOCYCLE.dataString -> absMonocycteCode(endpoint)
        KnownLabs.ABS_GRANULOCYTE.dataString -> absGranulocyteCode(endpoint)
        KnownLabs.LYMPHOCYTE.dataString -> lymphocyteCode(endpoint)
        KnownLabs.MONOCYTE.dataString -> monocyteCode(endpoint)
        KnownLabs.GRANULOCYTE.dataString -> granulocyteCode(endpoint)
        KnownLabs.NA.dataString -> labObx03NaCode(endpoint)
        KnownLabs.K.dataString -> labObx03KCode(endpoint)
        KnownLabs.CL.dataString -> labObx03ClCode(endpoint)
        KnownLabs.ICA.dataString -> labObx03iCaCode(endpoint)
        KnownLabs.GLUCOSE_ELECTROLYTE_PANEL.dataString -> labGlucoseElectrolyteCode(endpoint)
        KnownLabs.ANION_GAP.dataString -> anionGapCode(endpoint)
        KnownLabs.TOTAL_CO2.dataString -> labObx03CO2Code(endpoint)
        KnownLabs.BUN.dataString -> labObx03BunCode(endpoint)
        KnownLabs.CREAT.dataString -> labObx03CreatCode(endpoint)
        KnownLabs.CR.dataString -> labObx03CreatCode(endpoint)
        KnownLabs.GLUCOSE_FINGERSTICK.dataString -> glucometerCode(endpoint)
        KnownLabs.INR.dataString -> inrCode(endpoint)
        KnownLabs.PT.dataString  -> ptCode(endpoint)
        KnownLabs.ARTERIAL_PH.dataString -> arterialPhCode(endpoint)
        KnownLabs.ARTERIAL_PACO2.dataString -> arterialPco2Code(endpoint)
        KnownLabs.ARTERIAL_PAO2.dataString -> arterialPaO2Code(endpoint)
        KnownLabs.ARTERIAL_HCO3.dataString -> arterialHCO3Code(endpoint)
        KnownLabs.SAO2.dataString -> saO2Code(endpoint)
        KnownLabs.ARTERIAL_BE.dataString -> arterialBECode(endpoint)
        KnownLabs.ARTERIAL_LACTATE.dataString -> arterialLacticAcidCode(endpoint)
        KnownLabs.VENOUS_PH.dataString -> venousPhCode(endpoint)
        KnownLabs.VENOUS_PACO2.dataString -> venousPCO2Code(endpoint)
        KnownLabs.VENOUS_PAO2.dataString -> venousPaO2Code(endpoint)
        KnownLabs.VENOUS_HCO3.dataString -> venousHCO3Code(endpoint)
        KnownLabs.SVO2.dataString -> venousO2SatCode(endpoint)
        KnownLabs.VENOUS_BE.dataString -> venousBECode(endpoint)
        KnownLabs.VENOUS_LACTATE.dataString -> venousLacticAcidCode(endpoint)
        KnownLabs.CKMB.dataString -> ckmbCode(endpoint)
        KnownLabs.BNP.dataString -> bnpCode(endpoint)
        KnownLabs.TROPONIN_1.dataString -> troponin1Code(endpoint)
        KnownLabs.GFAP.dataString -> gfapCode(endpoint)
        KnownLabs.UCHL1.dataString -> uchl1Code(endpoint)
        KnownLabs.G6PD.dataString -> g6pdCode(endpoint)
        KnownLabs.ETOH.dataString -> etohCode(endpoint)
        KnownLabs.UA_BLOOD.dataString -> uaBloodCode(endpoint)
        KnownLabs.UA_KETONES.dataString -> uaKetonesCode(endpoint)
        KnownLabs.UA_GLUCOSE.dataString -> uaGlucoseCode(endpoint)
        KnownLabs.UA_LEUKOCYTES.dataString -> uaLeukocytesCode(endpoint)
        KnownLabs.UA_PROTEIN.dataString -> uaProteinCode(endpoint)
        KnownLabs.UA_NITRITES.dataString -> uaNitriteCode(endpoint)
        KnownLabs.UA_BILIRUBIN.dataString -> uaBilirubinCode(endpoint)
        KnownLabs.UA_UROBILINOGEN.dataString -> uaUrobilinogenCode(endpoint)
        KnownLabs.UA_PH.dataString -> uaPhCode(endpoint)
        KnownLabs.UA_SPECIFIC_GRAVITY.dataString -> uaSpecificGravityCode(endpoint)
        else -> {
            // Don't send unknown items to GT or CDP yet
            if (endpoint.format != EndpointType.OMDS) {
                null
            } else {
                // Custom lab, no code, but just send out the lab name
                CodeNameGroup("", name)
            }
        }
    }
}
