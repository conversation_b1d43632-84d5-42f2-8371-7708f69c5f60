package mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure

import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup

fun foleyCode(endpoint: EndpointType) = when (endpoint){
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("73368009","Foley catheter (physical object)")
    else -> null
}

fun foleyTypeCode(type: String) = when (type){
    "Indwelling" -> CodeNameGroup("23973005","Indwelling urinary catheter, device (physical object)")
    "Condom" -> CodeNameGroup("1230041002","Application of incontinence sheath (procedure)")
    "Intermittent" -> CodeNameGroup("469715007","Intermittent urethral drainage catheter, sterile (physical object)")
    else -> null
}

fun foleySizeCode (endpoint: EndpointType) = when (endpoint) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("260967009","Size of catheter (qualifier value)")
    else -> null
}

fun foleyTypeFromCode(code: String) = when (code) {
    "23973005" -> "Indwelling"
    "1230041002" -> "Condom"
    "469715007" -> "Intermittent"
    else -> null
}