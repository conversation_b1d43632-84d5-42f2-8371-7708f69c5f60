package mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment

import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.v231.segment.OBX
import mil.af.afrl.batman.hl7lib.converter.TimeConverter
import java.time.Instant

fun buildBaseOBX(parent: AbstractMessage, timestamp: Instant? = null): OBX {
    return OBX(parent, parent.modelClassFactory).apply {
        valueType.parse("NM")
        observationResultStatus.parse("F")
        if (timestamp != null) {
            dateTimeOfTheObservation.parse(TimeConverter.timestampToFormattedDateTime(timestamp))
        }
    }
}
