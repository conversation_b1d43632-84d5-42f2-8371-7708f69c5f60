package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments

import gov.afrl.batdok.encounter.treatment.GastricTubeData
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.gastricTubeNasalCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.gastricTubeOralCode

fun gastricTubeAttributes(treatmentCode: String, endpoint: EndpointType) : GastricTubeData{
    val type: String? = when (treatmentCode) {
        gastricTubeNasalCode(endpoint)?.code -> GastricTubeData.Type.NASAL.dataString
        gastricTubeOralCode(endpoint)?.code -> GastricTubeData.Type.ORAL.dataString
        else -> null
    }
    
    return GastricTubeData(type = type)
}