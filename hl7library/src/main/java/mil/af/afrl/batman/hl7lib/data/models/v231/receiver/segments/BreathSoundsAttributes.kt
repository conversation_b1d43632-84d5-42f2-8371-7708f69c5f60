package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments

import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.observation.BreathSoundsData
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.breathSoundsTypeFromCode


fun breathSoundsAttributes(attributes: List<OBX>) : BreathSoundsData {
    val typeList = mutableListOf<String>()

    for (code in attributes.map { it.obx3_ObservationIdentifier.ce1_Identifier.value }){
        typeList += breathSoundsTypeFromCode(code) ?: continue
    }
    return BreathSoundsData(typeList = typeList)
}