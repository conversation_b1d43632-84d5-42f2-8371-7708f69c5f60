package mil.af.afrl.batman.hl7lib.util

import android.content.Context
import gov.afrl.batdok.commands.proto.HL7Commands.HL7DataCommand
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.MessageGenerator

object HL7CommandBuilder {

    fun buildHL7DataCommand(context: Context, hl7Data: OutboundHL7Data) : HL7DataCommand? {
        val senderId = buildSenderId(context, hl7Data.batdokInstallationId)
        val hl7Messages = MessageGenerator(senderId, hl7Data.endpoint ?: return null, false, context)
            .buildMessagesForEncounter(hl7Data)
            .joinToString(prefix = "", postfix = "")
        return gov.afrl.batdok.encounter.commands.buildHL7DataCommand(hl7Messages)
    }

}
