package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments

import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.treatment.TqData
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.cdpTqSubLocationFromCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.cdpTqLocationFromCode

fun buildTqData(attributes: List<OBX>) : TqData {
    var location: String? = null
    var subLocation: String? = null

    for (code in attributes.map { it.obx3_ObservationIdentifier.ce1_Identifier.value }) {
        if (location == null) {
            location = cdpTqLocationFromCode(code)
        }

        if (subLocation == null) {
            subLocation = cdpTqSubLocationFromCode(code)
        }
    }

    return TqData(
        // Unless we know otherwise, assume it's Extremity. We need a location.
        tqLocation = location ?: TqData.Location.EXTREMITY.dataString,
        subLocation = subLocation
    )
}
