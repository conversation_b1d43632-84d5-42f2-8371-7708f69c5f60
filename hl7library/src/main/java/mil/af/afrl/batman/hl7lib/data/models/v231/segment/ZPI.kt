package mil.af.afrl.batman.hl7lib.data.models.v231.segment

import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.Group
import ca.uhn.hl7v2.model.v231.datatype.NM
import ca.uhn.hl7v2.model.v231.datatype.ST
import ca.uhn.hl7v2.parser.ModelClassFactory
import mil.af.afrl.batman.hl7lib.data.models.v231.customSegments.ExtendedAbstractSegment
import mil.af.afrl.batman.hl7lib.util.HL7Data
import mil.af.afrl.batman.hl7lib.util.OutboundHL7Data
import org.openehealth.ipf.modules.hl7.kotlin.get

/*
    1   ZPI Set ID                                          R   Set to “1”
    5   Blood Type See Table below for Blood Type Options   O   Patient blood type
    15  Species                                             O   Species

    Sample segment: "ZPI|1||||B-||||||||||HUMAN"
 */
class ZPI(parent: Group?, factory: ModelClassFactory? = null) : ExtendedAbstractSegment(parent, factory) {

    init {
        addSingleField(true, 1, "ZPI Set ID", NM::class.java)
        addBlankFields(3) // Skip fields [2-4]
        addSingleField(3, "Patient Blood Type")
        addBlankFields(9) // Skip fields [6-14]
        addSingleField(60, "Species", ST::class.java) // For working dogs
    }

    override fun populateFromBatdokPatient(parent: AbstractMessage, hl7Data: HL7Data): ExtendedAbstractSegment {
        get(1).parse("1")
        get(5).parse(hl7Data.document!!.info.bloodType)
        val species = if (hl7Data is OutboundHL7Data && hl7Data.mode?.contains("K9") == true) "DOG" else "HUMAN"
        get(15).parse(species)
        return this
    }
}
