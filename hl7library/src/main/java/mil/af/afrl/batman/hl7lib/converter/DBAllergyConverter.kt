package mil.af.afrl.batman.hl7lib.converter

import android.content.Context
import ca.uhn.hl7v2.model.AbstractMessage
import mil.af.afrl.batdokdata.models.allergy.AllergyDataStore
import mil.af.afrl.batdokstorage.allergy.AllergyDatabase
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ExtendedCodedElement
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup

object DBAllergyConverter {
    private lateinit var allergyDatastore: AllergyDataStore

    fun initialize(context: Context) {
        allergyDatastore = AllergyDatabase.getDatabase(context).allergyDao()
    }

    // Pair contains CodeNameGroup for code info and a String representing the allergy type tag
    suspend fun batdokDataToCodedElement(message: AbstractMessage, data: String) : ExtendedCodedElement? {
        // In this method, we should be able to only
        val allergyData = allergyDatastore.findAllergy(data)
        return if (allergyData == null) {
            null
        } else {
            val codeSystem = when (allergyData.vocab) {
                "Multum Drug" -> "MULTUMDRUG"
                "Multum Allergy Category" -> "MULTUMALG"
                "Allergy" -> "ALLERGY"
                else -> return null
            }
            ExtendedCodedElement(message, CodeNameGroup(allergyData.code, allergyData.term), codeSystem)
        }
    }

    suspend fun codeToBatdokData(code: String) : String? {
        val allergy = allergyDatastore.findAllergiesByCode(code).firstOrNull()
        return allergy?.term
    }

    suspend fun nameToBatdokData(name: String) : String? {
        val allergy = allergyDatastore.findAllergy(name)
        return allergy?.term
    }

}
