package mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure

import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup

fun cCollarCode(endpointType: EndpointType) = when (endpointType) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("398041008", "Cervical spine immobilization")
    else -> null
}

fun cSpineCode(endpointType: EndpointType) = when (endpointType) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("467221000", "Cervical spine immobilization frame (physical object)")
    else -> null
}

fun spineBoardCode(endpointType: EndpointType) = when (endpointType) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("1290630003", "Immobilization of spine using spine board (procedure)")
    else -> null
}

fun swathCode(endpointType: EndpointType) = when (endpointType) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("52037006","Application of sling")
    else -> null
}

fun splintCode(endpointType: EndpointType) = when (endpointType) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("79321009", "Application of splint")
    else -> null
}

fun cdpSplintImmobilizationCode() : CodeNameGroup? {
    return CodeNameGroup("79321009", "Application of splint")

}

fun cdpSplintLocationCode(location: String) : CodeNameGroup? {
    return when (location) {
        "LUE", "Left Upper Arm", "Left Arm" -> CodeNameGroup("368208006", "Left upper arm structure (body structure)")
        "RUE", "Right Upper Arm", "Right Arm" -> CodeNameGroup("368209003", "Right upper arm structure (body structure)")
        "LLE", "Left Lower Leg", "Left Leg" -> CodeNameGroup("32153003", "Structure of left lower limb (body structure)")
        "RLE", "Right Lower Leg", "Right Leg" -> CodeNameGroup("62175007", "Structure of right lower limb (body structure)")
        "Left Lower Arm" -> CodeNameGroup("66480008", "Structure of left forearm (body structure)")
        "Right Lower Arm" -> CodeNameGroup("64262003","Structure of right forearm (body structure)")
        "Right Upper Leg" -> CodeNameGroup("11207009", "Structure of right thigh (body structure)")
        "Left Upper Leg" -> CodeNameGroup("61396006", "Structure of left thigh (body structure)")
        "Neck" -> CodeNameGroup("45048000", "Neck structure (body structure)")
        "Front Torso" -> CodeNameGroup("22943007", "Trunk structure (body structure)")
        "Back Torso" -> CodeNameGroup("371426004", "Structure of soft tissue of back of trunk (body structure)")
        "Junctional" -> CodeNameGroup("50974003", "Junctional (qualifier value)")
        "Head" -> CodeNameGroup("69536005", "Head structure (body structure)")
        "Ribs" -> CodeNameGroup("24201007", "All ribs (body structure)")
        "Pelvis" -> CodeNameGroup("12921003", "Structure of pelvis (body structure)")
        else -> null
    }
}

fun locationFromSplintLocationCode(code: String) : String? {
    return when (code) {
        "368208006" -> "Left Upper Arm"
        "368209003" -> "Right Upper Arm"
        "32153003" -> "Left Lower Leg"
        "62175007" -> "Right Lower Leg"
        "45048000" -> "Neck"
        "22943007" -> "Front Torso"
        "371426004" -> "Back Torso"
        "50974003" -> "Junctional"
        "69536005" -> "Head"
        "24201007" -> "Ribs"
        "12921003" -> "Pelvis"
        "11207009" -> "Right Upper Leg"
        "61396006" -> "Left Upper Leg"
        "66480008" -> "Left Lower Arm"
        "64262003" -> "Right Lower Arm"
        else -> null
    }
}

fun cdpPulsePresenceCode(pulsePresence: String) : CodeNameGroup? {
    return when (pulsePresence) {
        "Yes" -> CodeNameGroup("301151001", "Peripheral pulse present (finding)")
        "No" -> CodeNameGroup("54518005", "Absent pulse (finding)")
        else -> null
    }
}

fun pulsePresentCode(endpointType: EndpointType) = when (endpointType) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("301151001", "Peripheral pulse present (finding)")
    else -> null
}

fun pulseNotPresentCode(endpointType: EndpointType) = when (endpointType) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("54518005", "Absent pulse (finding)")
    else -> null
}

fun splintTypeCode(endpointType: EndpointType) = when (endpointType) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("410657003", "Type (attribute)")
    else -> null
}
