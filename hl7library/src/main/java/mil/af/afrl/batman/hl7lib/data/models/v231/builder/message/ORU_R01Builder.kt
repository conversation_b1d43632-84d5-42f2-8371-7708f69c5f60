package mil.af.afrl.batman.hl7lib.data.models.v231.builder.message

import ca.uhn.hl7v2.model.v231.message.ORU_R01
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.orur01.BloodOruBuilder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.orur01.DnbiOruBuilder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.orur01.EFastOruBuilder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.orur01.HeightWeightOruBuilder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.orur01.IOOruBuilder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.orur01.LabOruBuilder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.orur01.NoteOruBuilder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.orur01.ObservationOruBuilder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.orur01.OrderEventOruBuilder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.orur01.OrderOruBuilder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.orur01.PrecedenceOruBuilder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.orur01.TriageOruBuilder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.orur01.VentOruBuilder
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.orur01.VitalOruBuilder
import mil.af.afrl.batman.hl7lib.util.HL7Data
import java.time.Instant

open class ORU_R01Builder(private val endpoint: Endpoint) {

    // Use same default time (time we started generating the message) throughout if needed
    private val defaultTimestamp = Instant.now()

    fun populateAll(hl7Data: HL7Data): List<ORU_R01> {
        return buildOrderObservationGroupList(hl7Data)
    }

    private fun buildOrderObservationGroupList(hl7Data: HL7Data): List<ORU_R01> {
        if (hl7Data.document == null) return emptyList()
        val orus = mutableListOf<ORU_R01>()

        // Patient evac status
        orus += TriageOruBuilder(hl7Data, defaultTimestamp).buildOrus()
        orus += PrecedenceOruBuilder(hl7Data, defaultTimestamp).buildOrus()

        // Height/weight
        orus += HeightWeightOruBuilder(hl7Data, defaultTimestamp).buildOrus()

        // Vitals
        orus += VitalOruBuilder(hl7Data, defaultTimestamp).buildOrus()

        // Labs
        orus += LabOruBuilder(hl7Data, defaultTimestamp).buildOrus()

        // Bloods
        orus += BloodOruBuilder(hl7Data, defaultTimestamp).buildOrus()

        // Vents
        orus += VentOruBuilder(hl7Data, defaultTimestamp).buildOrus()

        // Clinical Notes
        orus += NoteOruBuilder(hl7Data, defaultTimestamp).buildOrus()

        // Orders
        orus += OrderEventOruBuilder(hl7Data, defaultTimestamp).buildOrus()
        orus += OrderOruBuilder(hl7Data, defaultTimestamp).buildOrus()

        // IntakeOutput
        orus += IOOruBuilder(hl7Data, defaultTimestamp).buildOrus()

        if (endpoint.format != EndpointType.MHSG_T) {
            // EFAST
            orus += EFastOruBuilder(hl7Data, defaultTimestamp).buildOrus()
        }

        if (endpoint.format != EndpointType.TAC) {
            // DNBI
            orus += DnbiOruBuilder(hl7Data, defaultTimestamp).buildOrus()

            // Observations
            orus += ObservationOruBuilder(hl7Data, defaultTimestamp).buildOrus()
        }

        return orus
    }

}
