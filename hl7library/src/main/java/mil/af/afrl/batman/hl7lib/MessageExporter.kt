package mil.af.afrl.batman.hl7lib

import android.content.Context
import android.util.Log
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.MessageGenerator
import mil.af.afrl.batman.hl7lib.proto.BeamOuterClass
import mil.af.afrl.batman.hl7lib.util.HttpClient
import mil.af.afrl.batman.hl7lib.util.NetworkResponse
import mil.af.afrl.batman.hl7lib.util.OutboundHL7Data
import mil.af.afrl.batman.hl7lib.util.buildSenderId
import okhttp3.Call
import okhttp3.Callback
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import java.io.IOException

object MessageExporter {

    private val patientQueue = ArrayDeque<OutboundHL7Data>()
    private lateinit var client: OkHttpClient

    var exportCompletedCallback: ((NetworkResponse) -> Unit)? = null

    fun sendPatientMessages(hl7Data: OutboundHL7Data, context: Context) {
        client = HttpClient.getClient()
        patientQueue.addLast(hl7Data)

        // If we have a cert, immediately purge the queue and send the messages
        if (hl7Data.endpoint?.format != EndpointType.OMDS || !hl7Data.authToken.isNullOrEmpty()) {
            exportAllQueuedPatients(context)
        }
    }

    fun exportAllQueuedPatients(context: Context) {
        while (patientQueue.isNotEmpty()) {
            val patients = patientQueue.removeFirst()
            exportPatientBatch(patients, context)
        }
    }

    fun exportPatientQrString(hl7Data: OutboundHL7Data, context: Context): String {
        // For ATMIST, return empty string as we handle data generation in QrCodeGenerator
        if (hl7Data.qrDataType == BeamOuterClass.Integration.INTEGRATION_ATMIST) {
            return ""
        }

        val senderId = buildSenderId(context, hl7Data.batdokInstallationId)
        val endpoint = when (hl7Data.qrDataType) {
            BeamOuterClass.Integration.INTEGRATION_GT -> Endpoint.MHSG_T
            BeamOuterClass.Integration.INTEGRATION_CDP -> Endpoint.TAC
            BeamOuterClass.Integration.INTEGRATION_HALO -> Endpoint.MHSG_T
            else -> Endpoint.OMDS_DELTA
        }
        return MessageGenerator(senderId, endpoint, false, context)
            .buildMessagesForEncounter(hl7Data)
            .joinToString(prefix = "", postfix = "")
    }

    private fun exportPatientBatch(hl7Data: OutboundHL7Data, context: Context) {
        val senderId = buildSenderId(context, hl7Data.batdokInstallationId)

        // Build all messages for all endpoints so we know how many to expect (not same # for each endpoint)
        val endpoint = hl7Data.endpoint
        if (endpoint !is NetworkEndpoint) {
            exportCompletedCallback?.let { it(NetworkResponse(message = "Invalid endpoint selected")) }
            return
        }

        val hl7Batches = MessageGenerator(senderId, endpoint, false, context).buildMessagesForEncounter(hl7Data)

        Thread {
            // Right now, there is only ever a single batch (representing the single patient encounter)
            for (batch in hl7Batches) {
                val outboundUrl = when (endpoint) {
                    is MhsgtNetworkEndpoint -> endpoint.outboundUrl
                    is OmdsNetworkEndpoint -> endpoint.outboundUrl
                    else -> continue
                }
                sendMessageToEndpoint(outboundUrl, batch, hl7Data.authToken) { response ->
                    exportCompletedCallback?.let {
                        it(response)
                    }
                }
            }
        }.start()
    }

    private fun sendMessageToEndpoint(
        uri: String,
        message: String,
        authToken: String?,
        responseCallback: (NetworkResponse) -> Unit
    ) {
        val requestBody = message.toRequestBody("text/plain".toMediaTypeOrNull())

        val requestBuilder = Request.Builder()
            .url(uri)
            .post(requestBody)
            .addHeader("MessageType", "BATDOK")
            .addHeader("Accept", "application/json, text/plain, */*")

        authToken?.let {
            requestBuilder.addHeader("Authorization", "Bearer $it")
        }

        val request = requestBuilder.build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.e("MessageExporter", "$uri failed to post! Exception: ", e)
                responseCallback(NetworkResponse(error = e))
            }

            override fun onResponse(call: Call, response: Response) {
                val responseBody = response.body?.string()
                if (response.isSuccessful) {
                    Log.i("MessageExporter", "$uri $responseBody")
                } else {
                    Log.e(
                        "MessageExporter",
                        "$uri failed to post! Status: ${response.code} Response: $responseBody"
                    )
                }
                responseCallback(NetworkResponse(response.code, message = response.message))
            }
        })
    }
}
