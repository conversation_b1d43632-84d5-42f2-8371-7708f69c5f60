package mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure

import gov.afrl.batdok.encounter.treatment.HypothermiaPreventionData
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup

fun blanketCode(endpointType: EndpointType) = when (endpointType) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("19328000", "Blanket, device (physical object)")
    else -> null
}

fun otherHypothermiaPreventionCode(endpointType: EndpointType) = when (endpointType) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("309433007", "Patient warming therapy (procedure)")
    else -> null
}

fun otherHypothermiaPreventionDetailCode(endpointType: EndpointType) = when (endpointType) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("74964007", "Other (qualifier value)")
    else -> null
}

fun hypothermiaPreventionFromCode(code: String) = when (code){
        "19328000" -> HypothermiaPreventionData.Type.BLANKET.dataString
        //"241740008" -> "HPMK"
        "309433007" -> "Other"
        else -> null
    }
