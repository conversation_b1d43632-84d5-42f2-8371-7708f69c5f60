package mil.af.afrl.batman.hl7lib.data.models.v231.codetable

import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.data.models.v231.field.LOINC
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup

//region Vitals Codes
fun vitalObrCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.OMDS, EndpointType.TAC -> CodeNameGroup("VITALSIGNS", "")
        EndpointType.MHSG_T -> CodeNameGroup("8716-3", "Vital signs")
    }

fun heightWeightObrCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.OMDS, EndpointType.TAC -> CodeNameGroup("VITALSIGNS", "")
        EndpointType.MHSG_T -> CodeNameGroup("8716-3", "Height and weight")
    }

fun heightCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.OMDS, EndpointType.TAC -> CodeNameGroup("4154138", "Height/Length Estimated")
        EndpointType.MHSG_T -> CodeNameGroup("8302-2", "Body height")
    }
val heightInboundCodes = listOf("8302-2", "4154138", "4154126")


fun weightCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.OMDS, EndpointType.TAC -> CodeNameGroup("4154135", "Weight Estimated")
        EndpointType.MHSG_T -> CodeNameGroup("3141-9", "Body weight Measured")
    }
val weightInboundCodes = listOf("3141-9", "29463-7", "4154135", "4154120")

fun obr04VitalCodingScheme(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.OMDS, EndpointType.TAC -> ""
        EndpointType.MHSG_T -> LOINC
    }

fun getHrCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.OMDS, EndpointType.TAC -> CodeNameGroup("703511", "Peripheral Pulse Rate")  // This may not be the best mapping! We don't guarantee this is peripheral.
        EndpointType.MHSG_T -> CodeNameGroup("8867-4", "Heart rate")
    }

val heartRateInboundCodes = listOf("703511", "2700541", "8867-4", "703550","191981913", "191982579","191982273", "23378625","711535","374336621", "4347671")

fun getHrLocationCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.OMDS -> CodeNameGroup("34593195", "Pulse Site")
        EndpointType.MHSG_T -> CodeNameGroup("8885-6", "Heart rate measurement site")
        EndpointType.TAC -> null
    }

val hrLocationInboundCodes = listOf("8885-6","24877151")

fun getSpo2Code(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.OMDS, EndpointType.TAC -> CodeNameGroup("3623994", "SpO2")
        EndpointType.MHSG_T -> CodeNameGroup("59408-5", "Oxygen saturation in Arterial blood by Pulse oximetry")
    }

val spo2InboundCodes = listOf("3623994", "59408-5","3623994")

fun getRespRateCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.OMDS, EndpointType.TAC -> CodeNameGroup("703540", "Respiratory Rate")
        EndpointType.MHSG_T -> CodeNameGroup("9279-1", "Respiratory Rate")
    }

val respInboundCodes = listOf("703540", "9279-1")

fun getSBPCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.OMDS, EndpointType.TAC -> CodeNameGroup("703501", "Systolic Blood Pressure")
        EndpointType.MHSG_T -> CodeNameGroup("8480-6", "Systolic Blood Pressure")
    }

val sbpInboundCodes = listOf("703501", "8480-6")

fun getDBPCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.OMDS, EndpointType.TAC -> CodeNameGroup("703516", "Diastolic Blood Pressure")
        EndpointType.MHSG_T -> CodeNameGroup("8462-4", "Diastolic Blood Pressure")
    }

val dbpInboundCodes = listOf("703516", "8462-4")

fun getBPLocationCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.OMDS -> CodeNameGroup("473505875", "BP Site")
        EndpointType.MHSG_T -> null
        EndpointType.TAC -> null
    }

val bpLocationInboundCodes = listOf("473505875", "41904-4")

fun getMAPCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.OMDS -> CodeNameGroup("703306", "Mean Arterial Pressure, Calc")  // TODO: Figure out if this is cuff or calc, error in DCW
        EndpointType.MHSG_T -> CodeNameGroup("8478-0", "Mean Blood Pressure")
        EndpointType.TAC -> null
    }

fun getMAPAltText(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.OMDS, EndpointType.TAC -> ""
        EndpointType.MHSG_T -> "Mean arterial pressure"
    }

fun getAVPUCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.OMDS -> CodeNameGroup("36800381", "Level of Consciousness AVPU")
        EndpointType.MHSG_T -> CodeNameGroup("80288-4", "Level of Consciousness")
        EndpointType.TAC -> null
    }

val avpuInboundCodes = listOf("80288-4", "703827", "36800381")

fun getGTAVPUResponse(response: String?) : String? {
    return when (response) {
        "Alert and responsive" -> "Alert"
        "Responds to verbal stimuli" -> "Verbal"
        "Responds to painful stimuli" -> "Pain"
        "Unresponsive" -> "Unresponsive"
        else -> null
    }
}

fun getPainCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.OMDS, EndpointType.TAC -> CodeNameGroup("4247425", "Numeric Pain Scale")
        EndpointType.MHSG_T -> CodeNameGroup("72514-3", "Pain severity - 0-10 verbal numeric rating [Score] - Reported")
    }

val painInboundCodes = listOf("72514-3", "243171919","368507685", "4247425")

fun getEtco2Code(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.OMDS, EndpointType.TAC -> CodeNameGroup("2700544", "End Tidal CO2")
        EndpointType.MHSG_T -> CodeNameGroup("19891-1", "Carbon dioxide [Partial pressure] in Exhaled gas --at end expiration")
    }

val etco2InboundCodes = listOf("19891-1", "2700544")

fun getTempCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.OMDS, EndpointType.TAC -> CodeNameGroup("2820736", "Temperature (Route Not Specified)")
        EndpointType.MHSG_T -> CodeNameGroup("8310-5", "Body temperature")
    }

val tempInboundCodes = listOf("8310-5", "703558", "703526", "2820736")

fun getTempRouteCode(route: String?) : CodeNameGroup? {
    // TODO: Handle different endpoints if others ever request this info
    return when (route) {
        "TPRL" -> CodeNameGroup("29675107", "Body temperature - Temporal artery")
        "TYMP" -> CodeNameGroup("703526", "Tympanic membrane temperature")
        "ORAL" -> CodeNameGroup("703558", "Oral temperature")
        "AXIL" -> CodeNameGroup("703535", "Axillary temperature")
        "RECT" -> CodeNameGroup("703530", "Rectal temperature")
        else -> null
    }
}

val tempRouteInboundCodes = listOf("75539-7", "8333-7", "8331-1", "8328-7", "8332-9")

fun getBatdokRouteFromInboundCode(code: String?) : String? {
    return when (code) {
        "75539-7", "29675107" -> "TPRL"
        "8333-7", "703526" -> "TYMP"
        "8331-1", "703558" -> "ORAL"
        "8328-7", "703535" -> "AXIL"
        "8332-9", "703530" -> "RECT"
        else -> null
    }
}

fun getExpandedTempRouteName(route: String?) : String? {
    return when (route) {
        "TPRL" -> "Temporal"
        "TYMP" -> "Tympanic"
        "ORAL" -> "Oral"
        "AXIL" -> "Axillary"
        "RECT" -> "Rectal"
        else -> null
    }
}

fun getSIBPCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.OMDS -> CodeNameGroup("2808512", "Systolic Blood Pressure Invasive")
        EndpointType.MHSG_T -> CodeNameGroup("76215-3", "Invasive Systolic Blood Pressure")
        EndpointType.TAC -> CodeNameGroup("703501", "Systolic Blood Pressure")
    }

val sibpInboundCodes = listOf("76215-3", "67637217")

fun getDIBPCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.OMDS -> CodeNameGroup("2808516", "Diastolic Blood Pressure Invasive")
        EndpointType.MHSG_T -> CodeNameGroup("76213-8", "Invasive Diastolic Blood Pressure")
        EndpointType.TAC -> CodeNameGroup("703516", "Diastolic Blood Pressure")
    }

val dibpInboundCodes = listOf("76213-8", "67637243")

fun getIBPLocationCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.OMDS, EndpointType.TAC -> null  // TODO: Figure out if we should send something. Nothing invasive-specific
        EndpointType.MHSG_T -> CodeNameGroup("8359-2", "Peripheral artery measurement site")
    }

val ibpLocationInboundCodes = listOf("8359-2")

fun getOutputCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.OMDS, EndpointType.TAC -> CodeNameGroup("103344685", "Urine Output")
        EndpointType.MHSG_T -> CodeNameGroup("9187-6", "Urine output")
    }

val outputInboundCodes = listOf("9187-6", "710254", "103344685")

fun getCapRefillCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.OMDS, EndpointType.TAC -> CodeNameGroup("703608", "Capillary Refill")
        EndpointType.MHSG_T -> CodeNameGroup("44971-0", "Capillary Refill Time")
    }

val capRefillInboundCodes = listOf("44971-0", "703608")

fun getGCSEyeCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.OMDS, EndpointType.TAC -> CodeNameGroup("703584", "Eye Opening Response Glasgow")
        EndpointType.MHSG_T -> null // CodeNameGroup("9267-6", "Glasgow coma score eye opening")
    }

val gcsEyeInboundCodes = listOf("9267-6", "703584")

fun getGCSMotorCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.OMDS, EndpointType.TAC -> CodeNameGroup("703783", "Motor Response, Estimated Glasgow")
        EndpointType.MHSG_T -> null // CodeNameGroup("9268-4", "Glasgow coma score motor")
    }

val gcsMotorInboundCodes = listOf("9268-4", "703783")

fun getGCSVerbalCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.OMDS, EndpointType.TAC -> CodeNameGroup("703786", "Best Verbal Response Glasgow")
        EndpointType.MHSG_T -> null // CodeNameGroup("9270-0", "Glasgow coma score verbal")
    }

val gcsVerbalInboundCodes = listOf("9270-0", "703786")

fun getGCSTotalode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.OMDS -> CodeNameGroup("34601069", "Estimated Glasgow Coma Score")
        EndpointType.MHSG_T -> null // CodeNameGroup("9270-0", "Glasgow coma score verbal")
        EndpointType.TAC -> CodeNameGroup("703565", "Glasgow Coma Score")
    }

val gcsTotalInboundCodes = listOf("9269-2", "34601069", "703565")

fun tbsaCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.OMDS, EndpointType.TAC -> null
        EndpointType.MHSG_T -> null // CodeNameGroup("88097-1", "First degree burn area/Body surface area [Area/Area] Estimated") //Went with First Degree burn. Burn type isn't specified in Batdok
    }

val tbsaInboundCodes = listOf("88097-1")

fun getPIPCode(endpoint: Endpoint) =
    when (endpoint.format) {
        EndpointType.OMDS -> CodeNameGroup("2332508", "Peak inspiratory pressure")
        EndpointType.MHSG_T -> CodeNameGroup("20102-0", "Tidal volume expired/Peak inspiratory pressure --on ventilator")
        EndpointType.TAC -> null //CodeNameGroup("20102-0","Tidal volume expired/Peak inspiratory pressure --on ventilator")
    }

val pipInboundCodes = listOf("2332508", "20102-0")

//endregion
