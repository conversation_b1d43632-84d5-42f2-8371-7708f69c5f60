package mil.af.afrl.batman.hl7lib.converter

import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.v231.datatype.CE
import gov.afrl.batdok.encounter.metadata.Procedure
import gov.afrl.batdok.encounter.observation.Observation
import gov.afrl.batdok.encounter.treatment.Treatment
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.getTopLevelObservationsCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.getTopLevelProcedureCode
import mil.af.afrl.batman.hl7lib.data.models.v231.field.CPT4
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ExtendedCodedElement
import mil.af.afrl.batman.hl7lib.data.models.v231.field.SNOMED_CT
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup

abstract class BaseTreatmentConverter<T>: BatdokDataConverter<T>() {
    override fun getDefaultCode(endpoint: Endpoint): CodeNameGroup {
        return if (endpoint.format == EndpointType.MHSG_T) {
            CodeNameGroup("99291", "Critical care, evaluation and management of the critically ill or critically injured patient; first 30-74 minutes") // Unknown procedure
        } else {
            CodeNameGroup("69466000", "Unknown procedure (finding)")
        }
    }

    fun T.procedureType() =
        when {
            diagnosticProcedures.contains(toKey()) -> "D"
            therapeuticProcedures.contains(toKey()) -> "P"
            else -> "" // Unknown, for now
        }

    val therapeuticProcedures: MutableMap<String, Pair<String, MutableMap<String, CodeNameGroup>>> = mutableMapOf()
    val diagnosticProcedures: MutableMap<String, Pair<String, MutableMap<String, CodeNameGroup>>> = mutableMapOf()
    val unclassifiedProcedures: MutableMap<String, Pair<String, MutableMap<String, CodeNameGroup>>> = mutableMapOf()
    // Combination of each more specific map

    override var map: BatdokCodeData
        get() = therapeuticProcedures.plus(diagnosticProcedures).plus(unclassifiedProcedures).toMutableMap()
        set(value) = unclassifiedProcedures.putAll(value.minus(map.keys))

    override fun codeTypeForEndpoint(endpoint: Endpoint): String {
        return if (endpoint.format == EndpointType.MHSG_T) CPT4 else SNOMED_CT
    }

    override fun displayCodeTypeForEndpoint(endpoint: Endpoint): String {
        return when (endpoint.format) {
            EndpointType.MHSG_T -> CPT4
            else -> SNOMED_CT
        }
    }
}

object TreatmentConverter : BaseTreatmentConverter<Treatment>() {
    override fun Treatment.toName() = name
    override fun buildType(name: String) = Treatment(name)
    override fun batdokDataToCodedElement(message: AbstractMessage, data: Treatment, endpoint: Endpoint): CE {
        val attrBasedCode = getTopLevelProcedureCode(endpoint, data)
        return if (attrBasedCode != null) {
            ExtendedCodedElement(message, attrBasedCode, displayCodeTypeForEndpoint(endpoint))
        } else {
            super.batdokDataToCodedElement(message, data, endpoint)
        }
    }
}

object ProcedureConverter : BaseTreatmentConverter<Procedure>() {
    override fun Procedure.toName() = name
    override fun buildType(name: String) = Procedure(name)
}

object ObservationConverter : BaseTreatmentConverter<Observation>() {
    override fun Observation.toName() = name
    override fun buildType(name: String) = Observation(name)
    override fun batdokDataToCodedElement(message: AbstractMessage, data: Observation, endpoint: Endpoint): CE {
        val attrBasedCode = getTopLevelObservationsCode(endpoint, data)
        return if (attrBasedCode != null){
            ExtendedCodedElement(message,attrBasedCode, displayCodeTypeForEndpoint(endpoint))
        }else {
            super.batdokDataToCodedElement(message, data, endpoint)
        }
    }
}
