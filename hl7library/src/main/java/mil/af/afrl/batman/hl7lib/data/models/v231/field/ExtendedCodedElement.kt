package mil.af.afrl.batman.hl7lib.data.models.v231.field

import ca.uhn.hl7v2.model.Message
import ca.uhn.hl7v2.model.v231.datatype.CE
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup
import mil.af.afrl.batman.hl7lib.util.EscapeUtil

const val NDFRT = "NDF-RT-NUI"
const val SNOMED_CT = "SCT"
const val CPT4 = "CPT4"
const val RX_CUI = "RxCUI"
const val ICD10 = "ICD10"
const val TACICD10 = "TACICD10"
const val TACCPT4 = "CPT4CDP"
const val TACBLOOD = "BLOOD"
const val LOINC = "LOINC"
const val DHMSM_ICD = "DHMSM-ICD"
const val CNUM = "CNUM"
const val DNUM = "DNUM"
const val CS4001 = "CS4001"
const val CS54 = "CS54"
const val MULTUM = "MULTUM"
const val DCW = "DCW"
const val ISBT = "ISBT128"
const val RxNorm = "RxNorm"
const val GENESIS_ALIAS = "GENESISAlias"

class ExtendedCodedElement(message: Message?, codedValue: String, text: String, system: String): CE(message) {

    constructor(message: Message?, codeNameGroup: CodeNameGroup, system: String = "")
            : this(message, codeNameGroup.code, codeNameGroup.name, system)

    init {
        ce1_Identifier.parse(codedValue)
        ce2_Text.parse(EscapeUtil.escape(text))
        ce3_NameOfCodingSystem.parse(system)
    }
}
