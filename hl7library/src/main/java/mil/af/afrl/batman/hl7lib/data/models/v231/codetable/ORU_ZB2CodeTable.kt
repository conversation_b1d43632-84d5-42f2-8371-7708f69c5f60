package mil.af.afrl.batman.hl7lib.data.models.v231.codetable

import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.data.models.v231.field.LOINC
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup

fun zb2obr04CodingScheme(endpoint: Endpoint) =
    when(endpoint.format){
        EndpointType.MHSG_T -> LOINC
        EndpointType.OMDS -> ""
        EndpointType.TAC -> LOINC
    }

fun bloodProductObrCode(endpoint: Endpoint) : CodeNameGroup {
    return when (endpoint.format) {
        EndpointType.MHSG_T, EndpointType.TAC -> CodeNameGroup("1", "Blood products")
        EndpointType.OMDS -> CodeNameGroup("VITALSIGNS", "Blood products")
    }
}