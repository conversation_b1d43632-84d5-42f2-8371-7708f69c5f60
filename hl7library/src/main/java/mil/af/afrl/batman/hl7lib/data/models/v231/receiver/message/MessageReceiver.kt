package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.message

import android.content.Context
import android.util.Log
import ca.uhn.hl7v2.DefaultHapiContext
import ca.uhn.hl7v2.model.v231.message.ADT_A04
import ca.uhn.hl7v2.model.v231.message.ADT_A08
import ca.uhn.hl7v2.model.v231.message.ORM_O01
import ca.uhn.hl7v2.model.v231.message.ORU_R01
import ca.uhn.hl7v2.model.v231.message.PPR_PC1
import ca.uhn.hl7v2.model.v231.message.VXU_V04
import ca.uhn.hl7v2.model.v231.segment.PID
import ca.uhn.hl7v2.parser.CanonicalModelClassFactory
import ca.uhn.hl7v2.parser.CustomModelClassFactory
import gov.afrl.batdok.commands.proto.DocumentCommands.CommandData
import gov.afrl.batdok.encounter.commands.buildHL7DataCommand
import gov.afrl.batdok.util.buildCommandData
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineStart
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.withContext
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.data.models.v231.message.ORU_ZB2
import mil.af.afrl.batman.hl7lib.initializeTables
import mil.af.afrl.batman.hl7lib.util.HL7Data
import mil.af.afrl.batman.hl7lib.util.InboundHL7Data
import org.openehealth.ipf.modules.hl7.kotlin.eventType
import org.openehealth.ipf.modules.hl7.kotlin.triggerEvent
import org.openehealth.ipf.modules.hl7.kotlin.value
import java.io.InputStream

class MessageReceiver(context: Context? = null) {
    @Volatile private var pidCommandsParsed = false
    @Volatile private var pv1CommandsParsed = false

    private val hapiContext = DefaultHapiContext().apply {
        val customPackages = mapOf(
            "2.3.1" to arrayOf("mil.af.afrl.batman.hl7lib.data.models.v231"),
            "2.3" to arrayOf("mil.af.afrl.batman.hl7lib.data.models.v231")
        )
        modelClassFactory =
            CustomModelClassFactory(CanonicalModelClassFactory("2.3.1"), customPackages)
    }
    private val parser = hapiContext.pipeParser.apply {
        parserConfiguration.defaultObx2Type = "TX"
    }

    init {
        if (context != null) {
            initializeTables(context)
        }
    }

    suspend fun processHL7Data(inputStream: InputStream, coroutineDispatcher: CoroutineDispatcher = Dispatchers.Default): HL7Data = coroutineScope {
        pidCommandsParsed = false
        pv1CommandsParsed = false
        val hl7Data = InboundHL7Data()
        val fullMessage = StringBuilder()
        val commands = mutableListOf<CommandData>()
        val currentMessage = StringBuilder()
        val deferredResults = mutableListOf<Deferred<List<CommandData>>>()

        // Lazy start will prevent this from running until await gets called (happens after all the
        //  handlers get built). This is done so I can make sure that the demographics come from
        //  the first message in the batch, else there is no telling which order the messages
        //  will process
        fun buildMessageHandler(messageData: String) = async(coroutineDispatcher, start = CoroutineStart.LAZY) {
            try {
                handleMessage(messageData, hl7Data)
            } catch (e: Exception) {
                emptyList() //returns a empty list if there is a error
            }
        }

        // Read the message data from the stream
        inputStream.bufferedReader().use { reader ->
            reader.forEachLine { line ->
                val cleanLine = cleanUpRawMessage(line.trim())
                if (cleanLine.isNotEmpty()) {
                    // Process the message if we hit another MSH header or the end of the file
                    if ((cleanLine.startsWith("MSH") || cleanLine.startsWith("FTS")) && currentMessage.isNotEmpty()) {
                        val messageText = currentMessage.toString()
                        currentMessage.clear()
                        //spawn a coroutine for each message
                        deferredResults.add(buildMessageHandler(messageText))
                    }
                    // Need to process all the PID IDs in sequential order
                    if (cleanLine.startsWith("PID")) {
                        getPidIds(cleanLine, hl7Data)
                    }
                    currentMessage.append(cleanLine).append('\r')
                    fullMessage.append(line.trim()).append('\r')
                }
            }
        }

        // Handle any leftover data (likely the last message in the batch)
        if (currentMessage.isNotEmpty()) {
            val remainingMessage = currentMessage.toString()
            deferredResults.add(buildMessageHandler(remainingMessage))
        }

        // Our metadata (PV1 and PID info) should come from the first messages
        //  we receive. Parse in series until we find that data.
        while ((!pv1CommandsParsed || !pidCommandsParsed) && deferredResults.isNotEmpty()) {
            val messageData = deferredResults.removeAt(0)
            commands += messageData.await()
        }

        // Now that we have the metadata, parsing order doesn't matter.
        // Do the rest in parallel.
        commands.addAll(deferredResults.awaitAll().flatten())
        commands += buildCommandData(buildHL7DataCommand(fullMessage.toString()))
        hl7Data.commandsList = commands

        return@coroutineScope hl7Data
    }

    private fun getPidIds(line: String, hl7Data: InboundHL7Data) {
        val dummy = ORU_R01().apply { initQuickstart("ORU", "R01", "P") }
        val pid = PID(dummy, null).apply { parse(line) }
        val newIds = getPidIds(pid)
        hl7Data.externalIds = hl7Data.externalIds?.plus(newIds) ?: newIds
    }

    private fun getPidIds(pid: PID) : Map<String, String> {
        val idMap = mutableMapOf<String, String>()

        val allIds = pid.pid3_PatientIdentifierList + pid.pid4_AlternatePatientIDPID
        for (id in allIds) {
            val patientID = id.cx1_ID.value ?: continue
            val idType = id.cx4_AssigningAuthority.value ?: continue
            idMap[idType] = patientID
        }

        return idMap
    }

    private fun cleanUpRawMessage(line: String): String {
        val batchHeaders = setOf("FHS", "BHS", "BTS", "FTS")
        // Makes corrections to issues in the data we expect to receive from various systems
        if (line.startsWith("MSH") && line.contains("ADT^ZPR")) {
            // A ZPR message is really an A08 message, so process it as such
            return line.replace("ADT^ZPR", "ADT^A08")
        }

        if (line.startsWith("AL1") && line.split("|").size > 6) {
            // TAC sends a timestamp for AL1 segments in a bad format, but we don't use it
            //  anyway, so just get rid of it
            return line.replace(line.substringAfterLast("|"), "")
        }

        if (line.startsWithAny(batchHeaders)) {
            return ""
        }

        if (line.startsWith("ZAL|")) {
            return ""
        }

        if (line.startsWith("PID|")) {
            return clearBadPidFields(line)
        }

        if (line.startsWith("OBX|")) {
            return numericCheck(line)
        }

        return line
    }

    private fun numericCheck(line: String): String {
        val fields = line.split('|').toMutableList()
        if (fields[2] == "NM") {
            fields[5] = fields[5].replace(",", "")
        }
        return fields.joinToString("|")
    }

    private fun clearBadPidFields(line: String): String {
        val fields = line.split("|").toMutableList()

        // Clear out PID.13 and PID.14 (both are phone numbers,
        //   unused by our parser, but cause problems in validation)
        if (fields.size > 13) {
            fields[13] = ""  // Clear the content of PID.13
        }
        if (fields.size > 14) {
            fields[14] = ""  // Clear PID.14
        }

        // Join the fields back into a single string with '|' as the delimiter
        return fields.joinToString("|")
    }

    private fun String.startsWithAny(prefixes: Set<String>): Boolean {
        return prefixes.any { startsWith(it) }
    }

    private suspend fun handleMessage(messageText: String, hl7Data: HL7Data, dispatcher: CoroutineDispatcher = Dispatchers.Default): List<CommandData> {
        return withContext(dispatcher){
            try {
                val sendingSystemType = getSendingSystemType(messageText)
                val hasZBPSegment = messageText.split('\r').any { it.startsWith("ZBP") }
                val filteredMessages = if (!hasZBPSegment) {
                    messageText.split('\r')
                        .filterNot { it.startsWith("NTE|") }
                        .joinToString("\r")
                } else {
                    messageText
                }
                val parsedMessage = parser.parse(filteredMessages)
                val msgTrigger = ("${parsedMessage.eventType}^${parsedMessage.triggerEvent}")

                val receiver = when (msgTrigger) {
                    "ADT^A04" -> ADT_A04Receiver(parsedMessage as ADT_A04, sendingSystemType)
                    "ADT^A08" -> ADT_A08Receiver(parsedMessage as ADT_A08, sendingSystemType)
                    "ADT^A28" -> ADT_A28Receiver(parsedMessage as mil.af.afrl.batman.hl7lib.data.models.v231.message.ADT_A28, sendingSystemType)
                    "ADT^A31" -> ADT_A31Receiver(parsedMessage as mil.af.afrl.batman.hl7lib.data.models.v231.message.ADT_A31, sendingSystemType)
                    "ORM^O01" -> ORM_O01Receiver(parsedMessage as ORM_O01, sendingSystemType)
                    "ORU^R01" -> ORU_R01Receiver(parsedMessage as ORU_R01, sendingSystemType)
                    "ORU^ZB2" -> ORU_ZB2Receiver(parsedMessage as ORU_ZB2, sendingSystemType)
                    "PPR^PC1" -> PPR_PC1Receiver(parsedMessage as PPR_PC1, sendingSystemType)
                    "VXU^V04" -> VXU_V04Receiver(parsedMessage as VXU_V04, sendingSystemType)
                    else -> {
                        Log.w("MessageReceiver", "Unknown message: $msgTrigger")
                        null
                    }
                }

                if (receiver == null) {
                    return@withContext emptyList<CommandData>()
                }

                // Update metadata object with extra fields from the message
                receiver.setExtraData(hl7Data)

                val metadataCommands = mutableListOf<CommandData>()

                if (!pidCommandsParsed) {
                    metadataCommands.addAll(receiver.getPidCommands())
                    pidCommandsParsed = true
                }
                if (!pv1CommandsParsed) {
                    metadataCommands.addAll(receiver.getPV1Commands())
                    pv1CommandsParsed = true
                }

                receiver.getMessageCommands() + metadataCommands
            } catch (e: Exception){
                Log.e("MessageReceiver", "Error processing message: ${e.message}",e)
                emptyList()
            }
        }
    }

    private fun getSendingSystemType(messageText: String): EndpointType {
        val messageSender = messageText.split('\r')
            .find { it.startsWith("MSH") }
            ?.split('|')
            ?.get(2)
            ?: throw UnsupportedOperationException("Bad message format")

        return EndpointType.fromHL7SystemName(messageSender) ?: throw UnsupportedOperationException(
            "Unsupported message sender"
        )
    }
}
