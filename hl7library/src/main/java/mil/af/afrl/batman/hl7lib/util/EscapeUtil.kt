package mil.af.afrl.batman.hl7lib.util

import ca.uhn.hl7v2.parser.DefaultEscaping
import ca.uhn.hl7v2.parser.EncodingCharacters

object EscapeUtil : DefaultEscaping() {
    override fun escape(text: String, encChars: EncodingCharacters?) : String {
        val partialEscape = text.replace("\r", "\\X000D\\")
            .replace("\n", "\\X000A\\")
            .replace("\t", "\\X0009\\")
        return super.escape(partialEscape, encChars)
    }

    fun escape(text: String) : String {
        return escape(text, EncodingCharacters.defaultInstance())
    }

    override fun unescape(text: String, encChars: EncodingCharacters?): String {
        val partialEscape = text.replace("\\X000D\\", "\r")
            .replace("\\X000A\\", "\n")
            .replace("\\X0009\\", "\t")
        return super.unescape(partialEscape, encChars)
    }

    fun unescape(text: String) : String {
        return unescape(text, EncodingCharacters.defaultInstance())
    }
}
