package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments

import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.observation.TextData
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.visualAcuityTestTextDataCode
import org.openehealth.ipf.modules.hl7.kotlin.value

fun visualAcuityTestAttributes(attributes: List<OBX>, endpoint: EndpointType): TextData{
    var description: String? = null
    
    attributes.forEach {
        val code = it.obx3_ObservationIdentifier.ce1_Identifier?.value ?: return@forEach
        val value = it.obx5_ObservationValue.firstOrNull()?.value ?: return@forEach
        if (description == null){
            when (code){
                visualAcuityTestTextDataCode(endpoint)?.code -> description = value
            }
        }
    }
    
    return TextData(description = description)
}