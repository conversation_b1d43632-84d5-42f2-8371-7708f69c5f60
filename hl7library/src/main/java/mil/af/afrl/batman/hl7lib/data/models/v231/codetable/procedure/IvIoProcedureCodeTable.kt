package mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure

import gov.afrl.batdok.encounter.treatment.LineData
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup

//region Top-Level codes
fun peripheralLineCode(endpoint: EndpointType) = when (endpoint) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("392231009", "Intravenous cannulation")
    else -> null
}

fun arterialLineCode(endpoint: EndpointType) = when (endpoint) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("392247006", "Insertion of catheter into artery (procedure)")
    else -> null
}

fun centralLineCode(endpoint: EndpointType) = when (endpoint) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("233527006", "Central venous cannula insertion (procedure)")
    else -> null
}

fun ioAccessCode(endpoint: EndpointType) = when (endpoint) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("440009003", "Insertion of needle for intraosseous infusion (procedure)")
    else -> null
}
//endregion

//region Attribute codes
fun locationCode(endpoint: Endpoint) = when (endpoint.format) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("414611008", "Line placement technique (qualifier value)")
    else -> null
}

fun cdpIvIoLocationCode(location: String) : CodeNameGroup? {
    return when (location) {
        "Left Hand" -> CodeNameGroup("85151006", "Structure of left hand (body structure)")
        "Right Hand" -> CodeNameGroup("78791008", "Structure of right hand (body structure)")
        "Left Arm" -> CodeNameGroup("368208006", "Left upper arm structure (body structure)")
        "Right Arm" -> CodeNameGroup("368209003", "Right upper arm structure (body structure)")
        "Left EJ" -> CodeNameGroup("21834000", "Structure of surface region of left side of neck (body structure)")
        "Right EJ" -> CodeNameGroup("19801004", "Structure of surface region of right side of neck (body structure)")
        // Wrist mapped to forearm because CDP has no wrist option
        "Left Wrist" -> CodeNameGroup("66480008", "Structure of left forearm (body structure)")
        "Right Wrist" -> CodeNameGroup("64262003", "Structure of right forearm (body structure)")
        "Left Groin" -> CodeNameGroup("85119005", "Left inguinal region structure (body structure)")
        "Right Groin" -> CodeNameGroup("37117007", "Right inguinal region structure (body structure)")
        "Left Fem" -> CodeNameGroup("85119005", "Left inguinal region structure (body structure)")
        "Right Fem" -> CodeNameGroup("37117007", "Right inguinal region structure (body structure)")
        "Left IJ" -> CodeNameGroup("21834000", "Structure of surface region of left side of neck (body structure)")
        "Right IJ" -> CodeNameGroup("19801004", "Structure of surface region of right side of neck (body structure)")
        "Left Subclav" -> CodeNameGroup("1290343009", "Structure of left half of anterior chest wall (body structure)")
        "Right Subclav" -> CodeNameGroup("1290342004", "Structure of right half of anterior chest wall (body structure)")
        // These are legacy IO options that will be removed when we conform to the CDP IO location list
        "Left Humerus" -> CodeNameGroup("1303680005", "Structure of head of left humerus (body structure)")
        "Right Humerus" -> CodeNameGroup("466551000124105", "Structure of head of right humerus (body structure)")
        "Left Tibia" -> CodeNameGroup("719496004", "Bone structure of proximal left tibia (body structure)")
        "Right Tibia" -> CodeNameGroup("719495000", "Bone structure of proximal right tibia (body structure)")
        // These are the new IO placement options
        "Left Humeral Head" -> CodeNameGroup("1303680005", "Structure of head of left humerus (body structure)")
        "Right Humeral Head" -> CodeNameGroup("466551000124105", "Structure of head of right humerus (body structure)")
        "Left Proximal Tibia" -> CodeNameGroup("719496004", "Bone structure of proximal left tibia (body structure)")
        "Right Proximal Tibia" -> CodeNameGroup("719495000", "Bone structure of proximal right tibia (body structure)")
        "Left Distal Tibia" -> CodeNameGroup("719548001", "Structure of distal surface of left tibia (body structure)")
        "Right Distal Tibia" -> CodeNameGroup("719547006", "Structure of distal surface of right tibia (body structure)")
        "Left Foot" -> CodeNameGroup("22335008", "Structure of left foot (body structure)")
        "Right Foot" -> CodeNameGroup("7769000", "Structure of right foot (body structure)")
        "Left Leg" -> CodeNameGroup("32153003", "Structure of left lower limb (body structure)")
        "Right Leg" -> CodeNameGroup("62175007", "Structure of right lower limb (body structure)")
        "Sternum" -> CodeNameGroup("56873002", "Bone structure of sternum (body structure)")
        else -> null
    }
}

fun cdpGaugeCode(gauge: Float?) : CodeNameGroup? {
    return when (gauge) {
        14f -> CodeNameGroup("399997003", "14G (qualifier)")
        16f -> CodeNameGroup("277305006", "16G (qualifier)")
        18f -> CodeNameGroup("277306007", "18G (qualifier)")
        20f -> CodeNameGroup("264718009", "20G (qualifier)")
        22f -> CodeNameGroup("264719001", "22G (qualifier)")
        24f -> CodeNameGroup("264720007", "24G (qualifier)")
        else -> null
    }
}

fun cdpIoSizeCode(size: Float?) : CodeNameGroup? {
    return when (size) {
        15f -> CodeNameGroup("371240000", "Red color (qualifier value)")
        25f -> CodeNameGroup("405738005", "Blue color (qualifier value)")
        45f -> CodeNameGroup("371244009", "Yellow color (qualifier value)")
        else -> null
    }
}

fun gaugeCode(endpoint: Endpoint) = when (endpoint.format) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("277245004", "Gauges (qualifier value)")
    else -> null
}

fun lineTypeCode(endpoint: Endpoint) = when (endpoint.format) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("398008005", "Type of line (attribute)")
    else -> null
}

fun cdpTypeCode(type: String?) : CodeNameGroup? {
    return when (type) {
        // Cordis is a brand-name of single-lumen catheter, JOMIS approved mapping
        // These first two items are legacy catheters that will be going away soon
        "Cordis" -> CodeNameGroup("257279004", "Single lumen catheter (physical object)")
        "Triple Lumen" -> CodeNameGroup("397970004", "Triple lumen catheter (physical object)")
        // These are CDP Central Cath types that BATDOK will support
        "PA Catheter" -> CodeNameGroup("417747008", "Pulmonary artery flotation catheter (physical object)")
        "CVC (Single lumen)" -> CodeNameGroup("257279004", "Single lumen catheter (physical object)")
        "CVC (Double lumen)" -> CodeNameGroup("257280001", "Double lumen catheter (physical object)")
        "CVC (Triple lumen)" -> CodeNameGroup("397970004", "Triple lumen catheter (physical object)")
        "CVC (Quad lumen)" -> CodeNameGroup("398110005", "Quadruple lumen catheter (physical object)")
        "ECMO outflow" -> CodeNameGroup("716778006", "Implantable ventricular circulatory assist system outflow cannula (physical object)")
        "ECMO inflow venous" -> CodeNameGroup("467209004", "Cardiopulmonary bypass cannula, venous (physical object)")
        "ECMO inflow arterial" -> CodeNameGroup("467387002", "Cardiopulmonary bypass cannula, arterial (physical object)")
        "Tunneled" -> CodeNameGroup("445085009", "Tunneled central venous catheter (physical object)")
        // IO Fast-1, EZ-IO, Other types are NOT to be sent per JOMIS
        else -> null
    }
}

fun lineSizeCode(endpoint: Endpoint) = when (endpoint.format) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("410667008", "Length (attribute)")
    else -> null
}
//endregion

fun getLineSubtypeFromCode(code: String) : String? {
    return when (code){
        "257279004" -> LineData.Subtype.CORDIS.dataString
        "397970004" -> LineData.Subtype.TRIPLE_LUMEN.dataString
        // These are CDP Central Cath types that BATDOK will support
        "417747008" -> "PA Catheter"
        "257279004" -> "CVC (Single lumen)"
        "257280001" -> "CVC (Double lumen)"
        "397970004" -> "CVC (Triple lumen)"
        "398110005" -> "CVC (Quad lumen)"
        "716778006" -> "ECMO outflow"
        "467209004" -> "ECMO inflow venous"
        "467387002" -> "ECMO inflow arterial"
        "445085009" -> "Tunneled"
        else -> null
    }
}

fun getLineLocationFromCode(code: String, lineType: String) : Pair<String, String>? {
    return when (code){
        "85151006" -> when (lineType) {
            LineData.Type.ARTERIAL.dataString -> Pair(LineData.Side.LEFT.dataString, LineData.Location.WRIST.dataString)
            LineData.Type.PERIPHERAL.dataString -> Pair(LineData.Side.LEFT.dataString, LineData.Location.HAND.dataString)
            else -> null
        }
        "78791008" -> when (lineType) {
            LineData.Type.ARTERIAL.dataString -> Pair(LineData.Side.RIGHT.dataString, LineData.Location.WRIST.dataString)
            LineData.Type.PERIPHERAL.dataString -> Pair(LineData.Side.RIGHT.dataString, LineData.Location.HAND.dataString)
            else -> null
        }
        "368208006" ->Pair(LineData.Side.LEFT.dataString, LineData.Location.ARM.dataString)
        "368209003" -> Pair(LineData.Side.RIGHT.dataString, LineData.Location.ARM.dataString)
        
        "21834000" -> when (lineType){
            LineData.Type.PERIPHERAL.dataString -> Pair(LineData.Side.LEFT.dataString, LineData.Location.EJ.dataString)
            LineData.Type.CENTRAL.dataString -> Pair(LineData.Side.LEFT.dataString, "IJ")
            else -> null
        } 
        "19801004" -> when (lineType) {
            LineData.Type.PERIPHERAL.dataString -> Pair(LineData.Side.RIGHT.dataString, LineData.Location.EJ.dataString)
            LineData.Type.CENTRAL.dataString -> Pair(LineData.Side.RIGHT.dataString, "IJ")
            else -> null
        }
        
        "66480008" -> Pair(LineData.Side.LEFT.dataString, LineData.Location.ARM.dataString)
        "64262003" -> Pair(LineData.Side.RIGHT.dataString, LineData.Location.ARM.dataString)
        "85119005" -> when (lineType){
            LineData.Type.ARTERIAL.dataString -> Pair(LineData.Side.LEFT.dataString, LineData.Location.GROIN.dataString)
            LineData.Type.CENTRAL.dataString -> Pair(LineData.Side.LEFT.dataString, "Fem")
            else -> null
        }
        "37117007" -> when (lineType){
            LineData.Type.ARTERIAL.dataString -> Pair(LineData.Side.RIGHT.dataString, LineData.Location.GROIN.dataString)
            LineData.Type.CENTRAL.dataString -> Pair(LineData.Side.RIGHT.dataString, "Fem")
            else -> null
        }
        "1290343009" -> Pair(LineData.Side.LEFT.dataString, "Subclav")
        "1290342004" -> Pair(LineData.Side.RIGHT.dataString,"Subclav")
         
        // These are legacy IO options that will be removed when we conform to the CDP IO location list
        "1303680005" -> Pair(LineData.Side.LEFT.dataString, LineData.Location.HUMERUS.dataString)
        "466551000124105" -> Pair(LineData.Side.RIGHT.dataString, LineData.Location.HUMERUS.dataString)
        "719496004" -> Pair(LineData.Side.LEFT.dataString, LineData.Location.TIBIA.dataString)
        "719495000" -> Pair(LineData.Side.RIGHT.dataString, LineData.Location.TIBIA.dataString)
        // These are the new IO placement options
        "1303680005" -> Pair(LineData.Side.LEFT.dataString, "Humeral Head")
        "466551000124105" -> Pair(LineData.Side.RIGHT.dataString, "Humeral Head")
        "719496004" -> Pair(LineData.Side.LEFT.dataString, "Proximal Tibia")
        "719495000" -> Pair(LineData.Side.RIGHT.dataString, "Proximal Tibia")
        "719548001" -> Pair(LineData.Side.LEFT.dataString, "Distal Tibia")
        "719547006" -> Pair(LineData.Side.RIGHT.dataString, "Distal Tibia")
        "22335008" -> Pair(LineData.Side.LEFT.dataString, "Foot")
        "7769000" -> Pair(LineData.Side.RIGHT.dataString, "Foot")
        "32153003" -> Pair(LineData.Side.LEFT.dataString, "Leg")
        "62175007" -> Pair(LineData.Side.RIGHT.dataString, "Leg")
        "56873002" -> Pair("", LineData.Location.STERNUM.dataString)
        "41695006" -> Pair("","Scalp")
        "1255296008" -> Pair(LineData.Side.LEFT.dataString, LineData.Location.ARM.dataString) //bicep
        "1255295007" -> Pair(LineData.Side.RIGHT.dataString, LineData.Location.ARM.dataString) // bicep
        
        /* commiting out in case we need these
        "19801004" -> Pair(LineData.Side.RIGHT.dataString, "Neck")
        "21834000" -> Pair(LineData.Side.LEFT.dataString, "Neck")
        "1290342004" -> Pair(LineData.Side.RIGHT.dataString, "Chest")
        "1290343009" -> Pair(LineData.Side.LEFT.dataString, "Chest")
        "368209003" -> Pair(LineData.Side.LEFT.dataString, "Antecubital")
        "368208006" -> Pair(LineData.Side.RIGHT.dataString, "Antecubital")*/
        else -> null
    }
}

fun getLineSizeFromCode(code: String) : Float? {
    return when (code){
        "410667008" -> 0f
        "399997003" -> 14f
        "277305006" -> 16f
        "277306007" -> 18f
        "264718009" -> 20f
        "264719001" -> 22f
        "264720007" -> 24f
        // IO sizes
        "371240000" -> 15f
        "405738005" -> 25f
        "371244009" -> 45f

        else -> null
    }
}