package mil.af.afrl.batman.hl7lib.data.models.v231.receiver.segments

import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.treatment.PericardiocentesisData

import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure.pericardiocentesisVolumeCode
import org.openehealth.ipf.modules.hl7.kotlin.value

fun pericardiocentesisAttributes(attributes: List<OBX>, endpoint: EndpointType) : PericardiocentesisData{
    var volume: Float? = null
    var volumeUnit: String? = null
    
    attributes.forEach {
        val code = it.obx3_ObservationIdentifier.ce1_Identifier?.value ?: return@forEach
        val value = it.obx5_ObservationValue.firstOrNull()?.value ?: return@forEach
        val unit = it.obx6_Units?.ce1_Identifier?.value
        when (code) {
            pericardiocentesisVolumeCode(endpoint)?.code -> volume = value.toFloat()
        }
        if (unit != null){
            volumeUnit = unit
        }
    }
    return PericardiocentesisData(volume = volume, volumeUnit = volumeUnit)
}