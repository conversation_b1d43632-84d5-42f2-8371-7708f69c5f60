package mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.orur01

import ca.uhn.hl7v2.model.v231.message.ORU_R01
import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.VentSettings
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.populateObxFromVent
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.obr04CodingScheme
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.ventObrCode
import mil.af.afrl.batman.hl7lib.util.HL7Data
import java.time.Instant

class VentOruBuilder(
    hl7Data: HL7Data,
    defaultTimestamp: Instant
) : BaseRepeatableORU_R01Builder<VentSettings>(hl7Data, defaultTimestamp) {

    override fun buildOrus(): List<ORU_R01> {
        if (hl7Data.endpoint?.format != EndpointType.TAC) {
            return hl7Data.document!!.ventValues.ventSettingsList.mapNotNull {
                buildRepeatableDataOru(hl7Data, it)
            }
        }
        return emptyList()
    }

    override fun getObrDetails(item: VentSettings) = ItemData(
        item.time,
        ventObrCode(endpoint),
        item.id,
        obr04CodingScheme(endpoint)
    )

    override fun buildObservationList(msg: ORU_R01, item: VentSettings): List<OBX> {
        return populateObxFromVent(msg, item, endpoint)
    }

}
