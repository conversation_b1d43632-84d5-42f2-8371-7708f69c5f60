package mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.orur01

import ca.uhn.hl7v2.model.v231.message.ORU_R01
import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.Document
import mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment.populateObxFromDnbi
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.noteObr24DiagnosticServiceSectionId
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.obx033NoteCodingScheme
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.transferNoteCode
import mil.af.afrl.batman.hl7lib.util.HL7Data
import java.time.Instant

// TODO: Make this not use the entire Document as its source object
class DnbiOruBuilder(
    hl7Data: HL7Data,
    defaultTimestamp: Instant
) : BaseRepeatableORU_R01Builder<Document>(hl7Data, defaultTimestamp) {

    override fun buildOrus(): List<ORU_R01> {
        val dnbiInfo = hl7Data.document!!
        if (dnbiInfo.history.histories.isNotEmpty() || dnbiInfo.subjective.complaints.list.isNotEmpty()) {
            val dnbiORU = buildRepeatableDataOru(hl7Data, dnbiInfo, "DNBI Log")
            if (dnbiORU != null) {
                return listOf(dnbiORU).apply {
                    map {
                        it.getObservationGroup().obr.obr24_DiagnosticServSectID.parse(
                            noteObr24DiagnosticServiceSectionId(endpoint)
                        )
                    }
                }
            }
        }
        return emptyList()
    }

    override fun getObrDetails(item: Document) = ItemData(
        item.info.timeInfo,
        transferNoteCode(endpoint),
        item.id,
        obx033NoteCodingScheme(endpoint)
    )

    override fun buildObservationList(msg: ORU_R01, item: Document): List<OBX> {
        return populateObxFromDnbi(msg, item, endpoint)
    }

}
