package mil.af.afrl.batman.hl7lib.data.models.v231.builder.segment

import ca.uhn.hl7v2.model.AbstractMessage
import ca.uhn.hl7v2.model.v231.datatype.CE
import ca.uhn.hl7v2.model.v231.segment.OBX
import gov.afrl.batdok.encounter.EncounterVital
import gov.afrl.batdok.encounter.vitals.Avpu
import gov.afrl.batdok.encounter.vitals.BloodPressure
import gov.afrl.batdok.encounter.vitals.CapRefill
import gov.afrl.batdok.encounter.vitals.EtCO2
import gov.afrl.batdok.encounter.vitals.GCS
import gov.afrl.batdok.encounter.vitals.HR
import gov.afrl.batdok.encounter.vitals.InvasiveBloodPressure
import gov.afrl.batdok.encounter.vitals.Output
import gov.afrl.batdok.encounter.vitals.Pain
import gov.afrl.batdok.encounter.vitals.Resp
import gov.afrl.batdok.encounter.vitals.SpO2
import gov.afrl.batdok.encounter.vitals.Temp
import mil.af.afrl.batman.hl7lib.Endpoint
import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.converter.UnitConverter
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getAVPUCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getBPLocationCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getCapRefillCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getDBPCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getDIBPCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getEtco2Code
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getExpandedTempRouteName
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getGCSEyeCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getGCSMotorCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getGCSTotalode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getGCSVerbalCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getHrCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getHrLocationCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getMAPAltText
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getMAPCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getOutputCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getPainCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getRespRateCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getSBPCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getSIBPCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getSpo2Code
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getTempCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.getTempRouteCode
import mil.af.afrl.batman.hl7lib.data.models.v231.codetable.obx033CodingScheme
import mil.af.afrl.batman.hl7lib.data.models.v231.field.ExtendedCodedElement
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup
import org.openehealth.ipf.modules.hl7.kotlin.from
import org.openehealth.ipf.modules.hl7.kotlin.nrp

/*  This builder is used to transform BATDOK Vitals into OBX segments
    Each entry in Patient.vitals should be a separate mil.af.afrl.batman.hl7lib.data.models.v231.builder.message.R01ObservationGroup in one ORU_R01 message per patient
    Each field of the A2A Vital object is an OBX segment
 */
fun populateListFromBatdokVital(parent: AbstractMessage, vital: EncounterVital, endpoint: Endpoint): List<OBX> {
    val obxList = ArrayList<OBX>()
    var idx = 1

    fun addObx(value: String?, codeNameGroup: CodeNameGroup?, altText: String? = null, unit: CE? = null) {
        if (value != null && codeNameGroup != null) {
            buildBaseOBX(parent, vital.timestamp).apply {
                obx1_SetIDOBX.from(idx++)
                this.valueType.parse(if (value.toDoubleOrNull() == null) "TX" else "NM")
                val codeScheme = obx033CodingScheme(endpoint)
                
                observationIdentifier.from(ExtendedCodedElement(parent, codeNameGroup, codeScheme))
                altText?.let { observationIdentifier.alternateText.parse(it) }
                nrp(5).parse(value)
                unit?.let { obx6_Units.from(it) }
                obxList.add(this)
            }
        }
    }

    fun addObx(value: Int?, codeNameGroup: CodeNameGroup?) {
        value.takeIf { it != null && it > 0 }?.let {
            addObx(value.toString(), codeNameGroup)
        }
    }

    fun addObx(value: Float?, codeNameGroup: CodeNameGroup?) {
        value.takeIf { it != null && it > 0 }?.let {
            addObx(value.toString(), codeNameGroup)
        }
    }

    addObx(vital.get<HR>()?.pulse, getHrCode(endpoint))
    if (vital.get<HR>() != null && vital.get<HR>()?.pulseLocation?.isNotEmpty() == true) {
        addObx(vital.get<HR>()?.pulseLocation, getHrLocationCode(endpoint))
    }
    addObx(vital.get<SpO2>()?.spo2, getSpo2Code(endpoint))
    addObx(vital.get<Resp>()?.resp, getRespRateCode(endpoint))
    val isbp = vital.get<InvasiveBloodPressure>()?.systolic
    val idbp = vital.get<InvasiveBloodPressure>()?.diastolic
    addObx(isbp, getSIBPCode(endpoint))
    addObx(idbp, getDIBPCode(endpoint))
    // FIXME: There is no IBP location in the encounter anymore
//    if (isbp != -1 && idbp != -1 && vital.get<InvasiveBloodPressure>()?.location.isNotEmpty()) {
//        addObx(vital.invasiveBloodPressure?.location, getIBPLocationCode(endpoint), "TX", "Invasive blood pressure measurement site")
//    }
    if (endpoint.format != EndpointType.TAC || vital.get<InvasiveBloodPressure>() == null) {
        val sbp = vital.get<BloodPressure>()?.systolic
        val dbp = vital.get<BloodPressure>()?.diastolic
        addObx(sbp, getSBPCode(endpoint))
        addObx(dbp, getDBPCode(endpoint))
        if (vital.get<BloodPressure>() != null) {
            if (vital.get<BloodPressure>()?.location?.isNotEmpty() == true) {
                addObx(vital.get<BloodPressure>()?.location, getBPLocationCode(endpoint))
            }

            // Add MAP if it can be calculated
            if (sbp != null && dbp != null) {
                val map = (sbp * (1f / 3)) + (dbp * (2f / 3))
                addObx(map.toString(), getMAPCode(endpoint), altText = getMAPAltText(endpoint))
            }
        }
    }
    addObx(vital.get<Avpu>()?.avpu, getAVPUCode(endpoint),)
    addObx(vital.get<Pain>()?.painScore, getPainCode(endpoint))
    if (vital.get<EtCO2>()?.etco2 != null && vital.get<EtCO2>()?.etco2!! > 0) {
        addObx(vital.get<EtCO2>()?.etco2.toString(), getEtco2Code(endpoint), altText = "End-tidal carbon dioxide (ETCO2)")
    }
    val temp = vital.get<Temp>()?.temp
    if (temp != null) {
        // BATDOK always stores Fahrenheit temperature, we need Celsius
        addObx(((temp - 32) * 5/9).toString(), getTempCode(endpoint), unit = UnitConverter.batdokDataToCodedElement(parent, "deg c", endpoint))
    }
    if (endpoint.format == EndpointType.TAC) {
        val tempRoute = vital.get<Temp>()?.measurementMethod
        addObx(getExpandedTempRouteName(tempRoute), getTempRouteCode(tempRoute), "TX")
    }
    addObx(vital.get<Output>()?.output, getOutputCode(endpoint))
    addObx(vital.get<CapRefill>()?.capRefill, getCapRefillCode(endpoint))
    if (vital.get<GCS>()?.total != null) {  // Ensure we have a GCS to report
        addObx(vital.get<GCS>()?.eye.takeUnless { it == 0 }?.toString() ?: "NT", getGCSEyeCode(endpoint))
        addObx(vital.get<GCS>()?.motor.takeUnless { it == 0 }?.toString() ?: "NT", getGCSMotorCode(endpoint))
        addObx(vital.get<GCS>()?.verbal.takeUnless { it == 0 }?.toString() ?: "NT", getGCSVerbalCode(endpoint))
        addObx(vital.get<GCS>()?.total, getGCSTotalode(endpoint))
    }

    return obxList
}
