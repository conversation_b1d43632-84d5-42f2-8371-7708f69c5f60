package mil.af.afrl.batman.hl7lib.data.models.v231.codetable.procedure

import mil.af.afrl.batman.hl7lib.EndpointType
import mil.af.afrl.batman.hl7lib.util.CodeNameGroup

fun bvmCode(endpointType: EndpointType) = when (endpointType) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("425696007", "Manual respiratory assistance using bag and mask (procedure)")
    else -> null
}

fun nrbCode(endpointType: EndpointType) = when (endpointType) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("427591007", "Nonrebreather oxygen mask (physical object)")
    else -> null
}

fun ncCode(endpointType: EndpointType) = when (endpointType) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("336623009", "Oxygen nasal cannula (physical object)")
    else -> null
}

fun ventO2Code(endpointType: EndpointType) = when (endpointType) {
    EndpointType.TAC, EndpointType.OMDS -> CodeNameGroup("1258985005", "Invasive mechanical ventilation (regime/therapy)")
    else -> null
}
