package mil.af.afrl.batman.hl7lib.data.models.v231.codetable

data class Coordinates(val x: Double, val y: Double)

val avatarMap = mapOf(
    "anterior head" to Coordinates(.25,.11),
    "anterior face" to Coordinates(0.25, 0.14),
    "anterior neck" to Coordinates(0.25, 0.20),
    "anterior right shoulder" to Coordinate<PERSON>(0.13, 0.25),
    "anterior right upper arm" to Coordinates(0.13, 0.35),
    "anterior right lower arm" to Coordinates(0.11, 0.43),
    "anterior right wrist" to Coordinates(0.09, 0.48),
    "anterior right arm" to <PERSON><PERSON>inates(0.13, 0.29),
    "anterior right forearm" to Co<PERSON><PERSON>s(0.11, 0.43),
    "anterior right hand" to <PERSON><PERSON><PERSON><PERSON>(0.08, 0.53),
    "anterior left shoulder" to <PERSON><PERSON><PERSON>s(0.36, 0.25),
    "anterior left upper arm" to <PERSON><PERSON><PERSON>s(0.36, 0.35),
    "anterior left lower arm" to <PERSON><PERSON><PERSON><PERSON>(0.38, 0.43),
    "anterior left wrist" to <PERSON><PERSON><PERSON><PERSON>(0.4, 0.48),
    "anterior left arm" to <PERSON><PERSON><PERSON><PERSON>(0.36, 0.29),
    "anterior left forearm" to <PERSON><PERSON><PERSON><PERSON>(0.38, 0.43),
    "anterior left hand" to <PERSON>ordinates(0.42, 0.53),
    "anterior chest" to Coordinates(0.25, 0.27),
    "anterior upper torso" to Coordinates(0.25, 0.27),
    "anterior lower torso" to Coordinates(0.25, 0.38),
    "anterior abdomen" to Coordinates(0.25, 0.38),
    "anterior pelvis / gu" to Coordinates(0.25, 0.50),
    "anterior groin" to Coordinates(0.25, 0.50),
    "anterior right hip" to Coordinates(0.17, 0.47),
    "anterior right upper leg" to Coordinates(0.19, 0.61),
    "anterior right knee" to Coordinates(0.20, 0.69),
    "anterior right ankle" to Coordinates(0.21, 0.88),
    "anterior right thigh" to Coordinates(0.19, 0.61),
    "anterior right lower leg" to Coordinates(0.19, 0.79),
    "anterior right foot" to Coordinates(0.22, 0.91),
    "anterior left hip" to Coordinates(0.32, 0.47),
    "anterior left upper leg" to Coordinates(0.30, 0.61),
    "anterior left knee" to Coordinates(0.30, 0.69),
    "anterior left ankle" to Coordinates(0.28, 0.88),
    "anterior left thigh" to Coordinates(0.30, 0.61),
    "anterior left lower leg" to Coordinates(0.31, 0.79),
    "anterior left foot" to Coordinates(0.28, 0.91),
    "posterior head" to Coordinates(0.76, 0.10),
    "posterior occiput" to Coordinates(0.76, 0.10),
    "posterior neck" to Coordinates(0.76, 0.18),
    "posterior neck / c-spine (posterior)" to Coordinates(0.76, 0.18),
    "posterior right shoulder" to Coordinates(0.88, 0.25),
    "posterior right upper arm" to Coordinates(0.88, 0.35),
    "posterior right lower arm" to Coordinates(0.89, 0.43),
    "posterior right wrist" to Coordinates(0.92, 0.48),
    "posterior right arm" to Coordinates(0.88, 0.29),
    "posterior right forearm" to Coordinates(0.89, 0.43),
    "posterior right hand" to Coordinates(0.94, 0.53),
    "posterior left shoulder" to Coordinates(0.65, 0.25),
    "posterior left upper arm" to Coordinates(0.65, 0.35),
    "posterior left lower arm" to Coordinates(0.63, 0.43),
    "posterior left wrist" to Coordinates(0.61, 0.48),
    "posterior left arm" to Coordinates(0.65, 0.29),
    "posterior left forearm" to Coordinates(0.63, 0.43),
    "posterior left hand" to Coordinates(0.59, 0.54),
    "posterior upper torso" to Coordinates(0.76, 0.27),
    "posterior lower torso" to Coordinates(0.76, 0.38),
    "posterior upper back / t-spine" to Coordinates(0.76, 0.27),
    "posterior lower back / l-spine" to Coordinates(0.76, 0.38),
    "posterior groin" to Coordinates(0.76, 0.47),
    "posterior pelvis / gu" to Coordinates(76.0, 0.48),
    "posterior right hip" to Coordinates(0.83, 0.47),
    "posterior right upper leg" to Coordinates(0.82, 0.61),
    "posterior right knee" to Coordinates(0.81, 0.69),
    "posterior right ankle" to Coordinates(0.80, 0.88),
    "posterior right thigh" to Coordinates(0.82, 0.61),
    "posterior right lower leg" to Coordinates(0.81, 0.79),
    "posterior right foot" to Coordinates(0.79, 0.91),
    "posterior left hip" to Coordinates(0.69, 0.47),
    "posterior left upper leg" to Coordinates(0.71, 0.61),
    "posterior left knee" to Coordinates(0.72, 0.69),
    "posterior left ankle" to Coordinates(0.73, 0.88),
    "posterior left thigh" to Coordinates(0.71, 0.61),
    "posterior left lower leg" to Coordinates(0.71, 0.79),
    "posterior left foot" to Coordinates(0.73, 0.91),
)

