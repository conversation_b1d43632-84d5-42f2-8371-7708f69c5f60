// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    alias(libs.plugins.org.jetbrains.kotlin.android) apply false
    alias(libs.plugins.android.application) apply false
    alias(libs.plugins.com.google.devtools.ksp) apply false
    alias(libs.plugins.protobuf) apply false
    alias(libs.plugins.org.jetbrains.kotlin.jvm) apply false
}

project(":batdokdata").version = "9.1.0"
project(":batdokstorageroom").version = "11.1.0"
project(":batdokstoragetest").version = project(":batdokstorageroom").version
project(":exportlibrary").version = "5.0.2"
project(":pdfrobots").version = project(":exportlibrary").version
project(":batdokdocument").version = "7.1.0-rd-3"
project(":batdokprotobuf").version = "7.2.1"
project(":hl7library").version = "7.2.0-rd-22-SNAPSHOT"

allprojects {
    tasks.register("MavenPublishingInfo"){
        group = "custom_tasks"
        description = "Prints Publishing Info"
        doLast{
            if(project.extensions.findByType<PublishingExtension>() != null){
                project.extensions.findByType<PublishingExtension>()
                    ?.publications
                    ?.filterIsInstance<MavenPublication>()
                    ?.first()
                    ?.let { publication ->
                        val groupId = publication.groupId
                        val artifactId = publication.artifactId
                        val version = publication.version
                        println(groupId.replace(".", "/") + "/" + artifactId + "/" + version + "/" + artifactId + "-" + version + ".pom")
                    }
                }
            }
    }
}