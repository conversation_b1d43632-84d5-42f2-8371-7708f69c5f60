syntax = "proto3";

package protos;

option java_package = "com.batman.batdok.infrastructure.contactlesstransfer.proto";

import "EncounterNetworkCommands.proto";
import "NetworkCommands.proto";
import "MissionSupportCommands.proto";
import "document/TreatmentCommands.proto";
import "document/SharedProtobufObjects.proto";
import "mace/MaceCommands.proto";
import "Roster.proto";

message ContactlessTransferMessage {
    optional CreateEncounterMessage encounterCommand = 1;
    optional ContactlessTransferSensorMessage sensorMessage = 2;
    optional ContactlessTransferMedMessage medMessage = 3;
    optional bool newEncounterMessage = 4;
    optional ContactlessTransferTreatmentMessage treatmentMessage = 5;
    optional Roster rosterMessage = 6;
    optional MedBagMessage medBagMessage = 7;
    optional MaceMessage maceMessage = 8;
    optional NetworkKeyMessage networkKeyMessage = 10;
    repeated Roster multiRosterMessage = 13;
    repeated CreateEncounterMessage bulkEncounterMessage = 14;
    optional CompressedMultipartPacket  compressedMultipartPacket = 18;
    reserved 9;
    optional int32 messageIndex = 11;
    optional int32 messageCount = 12;
    optional ContactlessTransferSenderInfo senderInfo = 15;
    CompatibilityRange compatibleRange = 16;
    optional FastMedMessage fastMedMessage = 17;
}

message ContactlessTransferMedMessage {
    optional CompatibleEnum medType = 1;
    optional Medicine medicine = 2;
}

message ContactlessTransferSensorMessage {
    repeated SingleSensorInfo info = 2;
}

message ContactlessTransferSenderInfo {
    optional string callsign = 1;
}

message ContactlessTransferTreatmentMessage {
    AddTreatment treatmentCommand = 5;
}

message CompressedMultipartPacket {
    bytes data=4;
}

message NetworkKeyMessage{
    string networkKey = 1;
}