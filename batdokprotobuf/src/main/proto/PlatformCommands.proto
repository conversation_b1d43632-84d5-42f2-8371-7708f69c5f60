syntax = "proto3";

option java_package = "com.batman.batdok.infrastructure.network.commands";

import "FieldOptions.proto";
import "google/protobuf/any.proto";
import "document/SharedProtobufObjects.proto";

message PlatformMessage {
    int64 date = 1;
    string callsign = 2;
    google.protobuf.Any data = 3;
    CompatibilityRange compatibleRange = 4;
}

message PlatformFullUpdateMessage {
    repeated PlatformMessage message = 1;
}

message AddPatientsToPlatformMessage {
    bytes platformId = 1;
    repeated bytes encounterId = 2;
}

message ChangePlatformNameMessage {
    bytes platformId = 1;
    optional string name = 2;
}

message CreatePlatformMessage {
    optional bytes platformId = 1;
    optional string name = 2;
    optional bool isTutorialPlatform = 3;
}

message DeletePlatformsMessage {
    repeated bytes platformId = 1;
}

message RemovePatientsFromPlatformsMessage {
    repeated bytes encounterId = 1;
}

message UpdateRankMessage {
    optional bytes platformId = 1;
    bytes encounterId = 2;
    int32 newRank = 3;
    optional int32 oldRank = 4;
}

message ChangeOutTimeMessage {
    bytes platformId = 1;
    optional int64 time = 2;
}

message ChangePlatformStatusMessage {
    bytes platformId = 1;
    optional CompatibleEnum status = 2;
}

message ChangeDestinationMessage {
    bytes platformId = 1;
    optional string destination = 2;
}
