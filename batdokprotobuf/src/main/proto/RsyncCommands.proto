syntax = "proto3";

option java_package = "com.batman.util.rsync.commands";

import "FieldOptions.proto";

message Deltas {
    optional int32 blocksize = 1 [(introduced) = 2];
    repeated Delta deltas = 2 [(introduced) = 2];
}

message Delta {
    optional int32 index = 1 [(introduced) = 2];
    optional bytes data = 2 [(introduced) = 2];
}

message BlockChecksums {
    optional int32 blocksize = 1 [(introduced) = 2];
    repeated BlockChecksum blockchecksum = 2 [(introduced) = 2];
}

message BlockChecksum {
    optional int32 index = 1 [(introduced) = 2];
    optional int32 weak = 2 [(introduced) = 2];
    optional bytes strong = 3 [(introduced) = 2];
}