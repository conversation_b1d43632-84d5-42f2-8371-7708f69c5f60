syntax = "proto3";

option java_package = "com.batman.batdok.infrastructure.network.commands.mace";

import "mace/MaceMemoryObjects.proto";
import "mace/MaceCauseOfInjuryObjects.proto";
import "mace/MaceSymptomObjects.proto";
import "mace/MaceConcentrationObjects.proto";
import "mace/MaceOrientationObjects.proto";
import "FieldOptions.proto";

message MaceMessage {
    //Page 1 - Info
    bytes encounterId = 1;
    optional string patientName = 2;
    optional string patientSSN = 3;
    int64 injuryDateTime = 4;
    int64 evalDateTime = 5;
    optional string patientUnit = 6;
    optional string examiner = 7;

    //Page 2 - Incident Description
    optional string injuryDescription = 8;
    optional bool hitHead = 9;

    //Page 3
    CauseOfInjuryBools injuryBools = 10;

    //Page 4 - Alteration of Consciousness
    optional BoolWithMinSec aocMinSec = 12;
    optional BoolWithMinSec locMinSec = 13;
    optional BoolWithMinSec ptaMinSec = 14;
    optional BoolWithMinSec witnessMinSec = 15;

    //Page 5 - Symptoms
    SymptomBools symptomBools = 16;

    //Page 6 - History
    optional bool concussion = 18;
    optional bool migraine = 19;
    optional bool anxiety = 20;

    optional string concussionCount = 21;

    //Page 7 - Orientation
    OrientationBools orientationBools = 22;

    optional int32 listChoice = 24;

    //Page 8 - Immediate Memory
    MemoryBools memoryBools = 25;

    //Page 9 - Neuro Screening
    optional bool speechFluency = 27;
    optional bool wordFinding = 28;
    optional bool gripStrength = 29;
    optional bool pronatorDrift = 30;
    optional bool singleLegStance = 31;
    optional bool tandemGait = 32;
    optional bool pupilResponse = 33;
    optional bool eyeTracking = 34;

    // Page 10 - Concentration
    ConcentrationBools concentrationBools = 35;

    //Page 11 - Delayed Recall
    DelayedMemoryBools delayedMemoryBools = 37;

    //Page 12-18 - VOMS
    optional HDNF baseline = 39;
    optional HDNF smoothPursuit = 40;
    optional HDNF horizSaccade = 41;
    optional HDNF vertSaccade = 42;
    optional HDNF convergence = 43;
    optional HDNF horizVOR = 44;
    optional HDNF vertVOR = 45;
    optional HDNF vms = 46;
    optional int32 convergenceTrial1 = 54;
    optional int32 convergenceTrial2 = 55;
    optional int32 convergenceTrial3 = 56;

    optional bool shortened = 53;
}

message BoolWithMinSec {
    optional bool boolean = 1;
    optional string min = 2;
    optional string sec = 3;
}

message HDNF {
    optional int32 headache = 1;
    optional int32 dizziness = 2;
    optional int32 nausea = 3;
    optional int32 fogginess = 4;
    optional string comments = 5;
    optional bool notTested = 6;
}