syntax = "proto3";

option java_package = "com.batman.batdok.infrastructure.network.commands.mace";

message MemoryInts {
    optional int32 memoryBits = 1;
}

message MemoryBools {
    optional bool row1trial1 = 1;
    optional bool row1trial2 = 2;
    optional bool row1trial3 = 3;
    optional bool row2trial1 = 4;
    optional bool row2trial2 = 5;
    optional bool row2trial3 = 6;
    optional bool row3trial1 = 7;
    optional bool row3trial2 = 8;
    optional bool row3trial3 = 9;
    optional bool row4trial1 = 10;
    optional bool row4trial2 = 11;
    optional bool row4trial3 = 12;
    optional bool row5trial1 = 13;
    optional bool row5trial2 = 14;
    optional bool row5trial3 = 15;
}

message DelayedMemoryInts {
    optional int32 memoryBits = 1;
}

message DelayedMemoryBools {
    optional bool row1 = 1;
    optional bool row2 = 2;
    optional bool row3 = 3;
    optional bool row4 = 4;
    optional bool row5 = 5;
}