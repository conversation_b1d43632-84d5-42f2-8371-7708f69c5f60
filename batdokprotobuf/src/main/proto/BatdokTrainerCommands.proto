syntax = "proto3";

option java_package = "com.batman.batdok.infrastructure.network.commands";

message BatdokTrainerMessage{
    optional BatdokTrainerVitalMessage vitalMessage = 1;
}

message BatdokTrainerVitalMessage {
    repeated BatdokTrainerVitalItem vital = 1;
}

message BatdokTrainerVitalItem {
    optional bytes sensorId = 1;
    optional string sensorName = 2;

    optional int32 hr = 3;
    optional int32 spo2 = 4;
    optional int32 resp = 5;
    optional int32 bps = 6;
    optional int32 bpd = 7;
    optional int32 ibps = 8;
    optional int32 ibpd = 9;
    optional float temp = 10;
    optional int32 etco2 = 11;
}