syntax = "proto3";

option java_package = "com.batman.batdok.infrastructure.network.commands";

message Roster {
  RosterData data = 1;
}

message RosterData {
  optional string firstName = 1;
  optional string lastName = 2;
  optional string ssn = 3;
  optional string battleRosterNumber = 4;
  optional string gender = 5;
  optional string service = 6;
  optional string unit = 7;
  repeated string allergies = 8;
  optional string bloodType = 9;
  optional int64 bloodDate = 10;
  optional bool bloodLT = 11;
  optional int64 dob = 12;
  optional string dodId = 13;
  optional float height = 14;
  optional float weight = 15;
  optional string rank = 16;
  optional string nationality = 17;
  optional string status = 18;
  optional string unitPhoneNumber = 19;
}