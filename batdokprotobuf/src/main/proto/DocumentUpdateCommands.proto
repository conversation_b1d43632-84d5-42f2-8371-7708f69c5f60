syntax = "proto3";

option java_package = "gov.afrl.batdok.commands.proto";

import "DocumentCommands.proto";
import "RsyncCommands.proto";
import "document/SharedProtobufObjects.proto";


message DocumentUpdateMessage{
    oneof message{
        DocumentCommand documentCommand = 1;
        DocumentCommand fullUpdateCommand = 2;
        FullUpdateRequest fullUpdateRequest = 3;
        PostFullUpdateStatus postFullUpdateStatus = 4;
        DocumentDeltaMessage documentDeltaMessage = 5;
    }

    CompatibilityRange compatibleRange = 6;
}

message DocumentDeltaMessage {
    optional bytes encounterId = 1;
    optional int32 baselineId = 2;
    optional Deltas deltas = 3;
}

message FullUpdateRequest {
    bytes encounterId = 1;
    optional int32 checksum = 2;
    optional bytes owner = 3;
    reserved 4; // old snapshots
    optional RsyncCommandItem rsyncCommandItem = 5;
}
message RsyncCommandItem {
    optional int32 baselineId = 1;
    optional BlockChecksums blockChecksums= 2;

}

message PostFullUpdateStatus {
    optional bytes encounterId = 1;
    optional int32 checksum = 2;
    optional bytes owner = 3;
}