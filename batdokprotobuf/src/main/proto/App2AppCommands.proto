syntax = "proto3";

option java_package = "com.batman.batdok.infrastructure.network.commands";

import "document/SharedProtobufObjects.proto";
import "EncounterNetworkCommands.proto";
import "FieldOptions.proto";
import "Roster.proto";

message FromBatdokCommand {
  repeated A2ACreatePatientMessage a2aCreatePatientMessage = 1;
  optional int32 introduced = 2;
  optional int32 removed = 3;
  optional bool noPatients = 4;
}

message A2ACreatePatientMessage {
  optional CreateEncounterMessage createPatientMessage = 1 [(introduced) = 2];
  optional Location location = 2 [(introduced) = 2];
  optional CompatibleEnum patientType = 3 [(introduced) = 2];
  optional ProviderData providerData = 5;
  optional string patientMode = 6 [(introduced) = 3];
  repeated string imageUri = 7 [(introduced) = 3];
}

// todo move to another file
message ToBatdokCommand {
  optional RequestAllPatientsMessage requestAllMessage = 1;
  optional UpdateLocationMessage updateLocationMessage = 2;
  optional A2ACreatePatientMessage toBatdokCreatePatientMessage = 3;
  repeated Roster multiRosterMessage = 4;
  optional int32 introduced = 5;
  optional int32 removed = 6;
}

message RequestAllPatientsMessage { }

message UpdateLocationMessage {
  bytes encounterId = 1 [(introduced) = 2];
  Location location = 2 [(introduced) = 2];
}

message ProviderData {
optional string providerName = 1;
optional string providerCallsign = 2;
optional string providerCapability = 3;
optional string providerRank = 4;
optional string providerUnit = 5;
optional string providerDodId = 6;
optional string providerAFSC = 7;
}