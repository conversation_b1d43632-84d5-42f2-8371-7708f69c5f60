syntax = "proto3";

option java_package = "com.batman.batdok.infrastructure.network.commands";
import "EncounterNetworkCommands.proto";
import "MissionSupportCommands.proto";
import "DocumentCommands.proto";
import "PlatformCommands.proto";
import "BatdokTrainerCommands.proto";
import "BatdokChatCommands.proto";
import "UserData.proto";
import "mace/MaceCommands.proto";
import "DocumentUpdateCommands.proto";

message NetworkMessage{
    repeated EncounterMessage encounterMessage = 1;
    repeated DocumentUpdateMessage documentUpdateMessage = 2;
    PlatformFullUpdateMessage platformFullUpdateMessage = 3;
    repeated MiscMessage miscMessage = 4;
    repeated UserData userData = 5;
}

message MiscMessage{
  oneof message {
    RosterMessage rosterMessage = 1;
    MaceMessage maceMessage = 2;
    SensorAliasMessage sensorAliasMessage = 3;
    BatdokTrainerVitalMessage batdokTrainerVitalMessage = 4;
    BatdokChatMessage batdokChatMessage = 5;
  }
}