syntax = "proto3";

option java_package = "com.batman.batdok.infrastructure.network.commands";

message BatdokChatMessage {
    oneof data {
        ChatDirectMessage chatDirectMessage = 1;
        ChatAcknowledgmentMessage chatAcknowledgmentMessage = 2;
    }
}

message ChatDirectMessage {
    optional int64 timestamp = 1;
    optional bytes from = 2;
    optional bytes to = 3;
    optional string message = 4;
}

message ChatAcknowledgmentMessage {
    optional int32 checksum = 1;
}