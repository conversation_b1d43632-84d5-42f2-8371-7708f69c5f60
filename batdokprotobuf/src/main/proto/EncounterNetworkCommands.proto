syntax = "proto3";

option java_package = "com.batman.batdok.infrastructure.network.commands";

import "document/SharedProtobufObjects.proto";
import "Vital.proto";
import "DocumentCommands.proto";
import "FieldOptions.proto";

message EncounterMessage {
    oneof message{
        CreateEncounterMessage createMessage = 1;
        VitalsMessage vitalsMessage = 2;
        LocalEncountersIdListMessage localEncountersIdListMessage = 3;
        EncounterImageMessage encounterImageMessage = 4;
        RequestOwnershipMessage requestOwnershipMessage = 5;
        SensorTransferMessage sensorTransferMessage = 6;
        RequestEncounterHashesMessage requestEncounterHashesMessage = 7;
        RequestEncounterMessage requestEncounterMessage = 8;
        GeotagEncounterMessage geotagEncounterMessage = 12;
        HandoffDataMessage handoffDataMessage = 13;
        TransferOwnershipMessage transferOwnershipMessage = 15;
        OwnershipChangeResponse ownershipChangeResponse = 16;
        OwnershipTransferredMessage ownershipTransferredMessage = 17;
        AcknowledgeOwnershipChangeRequestMessage acknowledgeOwnershipChangeMessage = 18;
        RequestEncounterLocation requestEncounterLocation = 19;
        ChangeModeCommand changeModeCommand = 20;
        ExportStatusCommand exportStatusCommand = 21;
        ChangeGeneratedNameCommand changeGeneratedNameCommand = 23;
    }
    CompatibilityRange compatibleRange = 22;

}

message CreateEncounterMessage{
    bytes id = 1;
    optional int64 created = 2;
    optional string name = 3;
    DocumentCommand commandItems = 5;
    optional bytes owner = 6;
    optional GeotagEncounterMessage location = 7;
    optional CompatibleEnum ownerMode = 8;
    optional bytes patientId = 9;
    map<string, string> externalIds = 10;
    optional string alias = 11;
    optional int32  rank = 12;
}

message GeotagEncounterMessage {
    optional bytes encounterId = 1;
    optional Location location = 2;
    optional int64 markTime = 3;
}

message RequestEncounterLocation {
    optional bytes encounterId = 1;
}

message Location {
    optional double lat = 1;
    optional double long = 2;
    optional double altitude = 3;
}

message HandoffDataMessage {
    optional bytes encounterId = 1;
    optional string age = 2;
    optional bool stability = 3;
    optional string injury = 4;
}


message VitalsMessage{
    optional bytes id = 1;
    optional int32 vitalStatus = 2;
    optional Vital vital = 3;
    optional bytes owner = 4;
}

message LocalEncountersIdListMessage {
    repeated bytes id = 3;
    optional bytes owner = 4;
    repeated EncounterliteMessage encounterLite = 5;
}

message EncounterliteMessage {
    optional bytes id = 1;
    optional string name = 3;
    optional Location location = 5;
    repeated string tag = 7;
}

message EncounterImageMessage {
    bytes encounterId = 1;
    bytes imageBytes = 2;
    optional string name = 3;
    optional bytes owner = 4;
}

message TransferOwnershipMessage {
    optional bytes encounterId = 1;
}

message RequestOwnershipMessage {
    bytes encounterId = 1;
    optional bytes proposedNewOwner = 2;
    optional bool onlyRequest = 3;
}

message OwnershipChangeResponse {
    optional bool acceptChange = 1;
    optional bytes encounterId = 2;
    optional bytes newOwner = 3;
}

message OwnershipTransferredMessage {
    optional bytes encounterId = 1;
    optional bytes newOwner = 2;
}

message AcknowledgeOwnershipChangeRequestMessage {
    optional bytes encounterId = 1;
    optional bytes proposedNewOwner = 2;
}

message SensorTransferMessage {
    bytes encounterId = 1;
    repeated SingleSensorInfo sensorInfo  = 2;
}

message RequestEncounterHashesMessage {}

message RequestEncounterMessage{
    bytes encounterId = 1;
}

message ChangeModeCommand {
    optional bytes encounterId = 1;
    optional CompatibleEnum mode = 2;
}

message SingleSensorInfo {
    optional string abbreviation = 1;
    optional string address = 2;
    optional string name = 3;
}

message ExportStatusCommand{
    optional bytes encounterId = 1;
    optional string appname = 2;
    optional int32 statusCode = 3;
}

message ChangeGeneratedNameCommand {
    bytes encounterId = 1;
    string name = 2;
}
