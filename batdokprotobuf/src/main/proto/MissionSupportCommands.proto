syntax = "proto3";

option java_package = "com.batman.batdok.infrastructure.network.commands";

import "document/SharedProtobufObjects.proto";
import "mace/MaceCommands.proto";
import "Roster.proto";
import "google/protobuf/wrappers.proto";

message MissionSupportMessage {
    reserved 2;
    optional RosterMessage rosterMessage = 1;
    optional SensorAliasMessage sensorAliasMessage = 3;
    optional MaceMessage maceMessage = 4;
}

message RosterMessage{
    repeated Roster roster = 1;
}

message SensorAliasMessage {
    repeated SensorAliasData data = 1;
}

message SensorAliasData {
    string address = 1;
    string alias = 2;
    optional int32 color = 3 [deprecated = true];
    google.protobuf.StringValue colorString = 4;
}

message MedBagMessage {
    repeated MedNameOrNumber fluid = 1;
    repeated MedNameOrNumber bloodProduct = 2;
    repeated MedNameOrNumber analgesic = 3;
    repeated MedNameOrNumber antibiotic = 4;
    repeated MedNameOrNumber other = 5;
}

message FastMedMessage {
    repeated FastMed fastMed = 1;
}

message FastMed {
    optional bytes id = 1;
    optional string medName = 2;
    optional string route = 3;
    optional string dose = 4;
    optional string unit = 5;
    optional string type = 6;
    optional string description = 7;
}