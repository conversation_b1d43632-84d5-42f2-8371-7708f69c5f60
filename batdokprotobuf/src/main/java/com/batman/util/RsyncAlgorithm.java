package com.batman.util;

import com.batman.util.rsync.commands.RsyncCommands;
import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.kotlin.ByteStringsKt;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Based on python package pyrsync2
 * https://rsync.samba.org
 * https://pypi.org/project/pyrsync2/
 * https://code.activestate.com/recipes/577518-rsync-algorithm/
 */
public class RsyncAlgorithm {

    public static class Delta {
        public Integer index;
        public byte[] data;

        public Delta(Integer index, byte[] data) {
            this.index = index;
            this.data = data;
        }

        public static byte[] toByteArray(List<Delta> deltas, int blocksize) {
            return toProtobuf(deltas,blocksize).toByteArray();
        }

        public static RsyncCommands.Deltas toProtobuf(List<Delta> deltas, int blocksize) {
            RsyncCommands.Deltas.Builder pdeltas = RsyncCommands.Deltas.newBuilder();
            pdeltas.setBlocksize(blocksize);
            for (int i = 0; i < deltas.size(); i++) {
                if(deltas.get(i).index==null && deltas.get(i).data==null){
                    throw new IllegalArgumentException("index and data cannot both be null");
                }

                RsyncCommands.Delta.Builder dbuilder = RsyncCommands.Delta.newBuilder();
                if (deltas.get(i).index != null) {
                    dbuilder.setIndex(deltas.get(i).index);
                }
                if (deltas.get(i).data != null) {
                    dbuilder.setData(ByteString.copyFrom(deltas.get(i).data));
                }
                pdeltas.addDeltas(dbuilder);
            }
            return pdeltas.build();
        }

        public static List<Delta> from(byte[] deltasbytes) {
            try {
                return from(RsyncCommands.Deltas.parseFrom(deltasbytes));
            } catch (InvalidProtocolBufferException e) {
                e.printStackTrace();
                return new ArrayList<>();
            }
        }

        public static List<Delta> from(RsyncCommands.Deltas deltascommand) {
            if(!deltascommand.hasBlocksize()){
                throw new IllegalArgumentException("missing blocksize");
            }
            List<Delta> deltas = new ArrayList<>();
            for (RsyncCommands.Delta d : deltascommand.getDeltasList()) {
                if(!d.hasIndex() && !d.hasData() ){
                    throw new IllegalArgumentException("index and data cannot both be null");
                }
                Integer index = d.hasIndex() ? d.getIndex() : null;
                byte[] data = d.hasData() ? d.getData().toByteArray() : null;
                deltas.add(new Delta(index, data));
            }

            return deltas;
        }
    }

    public static class Checksum {
        public Integer weak;
        public byte[] strong;

        public Checksum(Integer weak, byte[] strong) {
            this.weak = weak;
            this.strong = strong;
        }

        public static byte[] toByteArray(List<Checksum> checksums,int blocksize) {
            return toProtobuf(checksums, blocksize).toByteArray();
        }

        public static RsyncCommands.BlockChecksums toProtobuf(List<Checksum> checksums, int blocksize) {
            RsyncCommands.BlockChecksums.Builder pchecksums = RsyncCommands.BlockChecksums.newBuilder();
            pchecksums.setBlocksize(blocksize);
            for (int i = 0; i < checksums.size(); i++) {
                pchecksums.addBlockchecksum(RsyncCommands.BlockChecksum.newBuilder()
                        .setIndex(i)
                        .setWeak(checksums.get(i).weak)
                        .setStrong(ByteStringsKt.toByteString(checksums.get(i).strong)));
            }
            return pchecksums.build();
        }

        public static List<Checksum> from(byte[] checksumsbytes) {
            try {
                return from(RsyncCommands.BlockChecksums.parseFrom(checksumsbytes));
            } catch (InvalidProtocolBufferException e) {
                e.printStackTrace();
                return new ArrayList<>();
            }
        }

        public static List<Checksum> from(RsyncCommands.BlockChecksums checksumsCommand) {
            if(!checksumsCommand.hasBlocksize()){
                throw new IllegalArgumentException("missing blocksize");
            }
            ArrayList<Checksum> checksums = new ArrayList<>();
            for (RsyncCommands.BlockChecksum c : checksumsCommand.getBlockchecksumList()) {
                Integer weak = c.hasWeak() ? c.getWeak() : null;
                byte[] strong = c.hasStrong() ? c.getStrong().toByteArray() : null;
                if (weak != null && strong != null) {
                    checksums.add(new Checksum(weak, strong));
                }
            }
            return checksums;
        }
    }

    private static class BlockData {
        public Integer index;
        public Checksum checksum;

        public BlockData(Integer index, Checksum checksum) {
            this.index = index;
            this.checksum = checksum;
        }
    }


    protected static RsyncCommands.Deltas rsyncdelta(byte[] data,
                                                  RsyncCommands.BlockChecksums remotesignatures) throws IOException, NoSuchAlgorithmException {
        return Delta.toProtobuf(rsyncdelta(
                new ByteArrayInputStream(data),
                Checksum.from(remotesignatures),
                remotesignatures.getBlocksize()),remotesignatures.getBlocksize()
        );
    }

    public static byte[] rsyncdelta(byte[] data,
                                    byte[] remoteChecksums) throws IOException, NoSuchAlgorithmException {
        RsyncCommands.BlockChecksums blockChecksums=RsyncCommands.BlockChecksums.parseFrom(remoteChecksums);
        return Delta.toByteArray(
                rsyncdelta(
                        new ByteArrayInputStream(data),
                        Checksum.from(blockChecksums),
                        blockChecksums.getBlocksize()),blockChecksums.getBlocksize()
        );
    }

    /**
     * Generates a binary patch when supplied with the index and strong
     * hashes from an unpatched target and a readable stream for the
     * up-to-date data. The blocksize must be the same as the value
     * used to generate remotesignatures.
     */
    protected static List<Delta> rsyncdelta(ByteArrayInputStream datastream,
                                         List<Checksum> remotesignatures,
                                         int blocksize) throws IOException, NoSuchAlgorithmException {
        List<Delta> delta = new ArrayList<>();
        int max_buffer = 4096;

        Map<Integer, BlockData> signatures = new HashMap<>();

        for (int i = 0; i < remotesignatures.size(); i++) {
            Checksum x = remotesignatures.get(i);
            signatures.put(x.weak, new BlockData(i, new Checksum(x.weak, x.strong)));
        }

        boolean match = true;
        int matchblock = -1;
        ByteArrayOutputStream current_block = new ByteArrayOutputStream();

        ByteArrayOutputStream window = new ByteArrayOutputStream();
        int checksum = 0;
        int a = 0;
        int b = 0;
        int window_offset = 0;
        int tailsize = 0;
        int oldbyte = 0;
        int newbyte = 0;
        while (true) {
            if (match && datastream != null) {
                // Whenever there is a match or the loop is running for the first
                // time, populate the window using weakchecksum instead of rolling
                // through every single byte which takes at least twice as long.
                window.reset();
                byte[] block = new byte[blocksize];
                int read = datastream.read(block);
                window.write(block, 0, read);
                window_offset = 0;
                int[] x = weakchecksum(window.toByteArray());
                checksum = x[0];
                a = x[1];
                b = x[2];
            }

            byte[] strong = MessageDigest.getInstance("MD5") // mobsf-ignore weak_hash
                    .digest(Arrays.copyOfRange(window.toByteArray(), window_offset, window.size()));

            if (signatures.containsKey(checksum) && Arrays.equals(Objects.requireNonNull(signatures.get(checksum)).checksum.strong, strong)) {

                matchblock = Objects.requireNonNull(signatures.get(checksum)).index;

                match = true;

                if (current_block.size() > 0) {
                    delta.add(new Delta(null, current_block.toByteArray()));
                }

                delta.add(new Delta(matchblock, null));

                current_block.reset();

                if (datastream == null || datastream.available() <= 0) {
                    break;
                }
                continue;
            } else {
                // The weakchecksum (or the strong one) did not match
                match = false;
                if (datastream != null) {
                    if (datastream.available() > 0) {
                        // Get the next byte and affix to the window
                        newbyte = datastream.read();
                        window.write(newbyte);
                    } else {
                        // No more data from the file; the window will slowly shrink.
                        // newbyte needs to be zero from here on to keep the checksum
                        // correct.
                        newbyte = 0;
                        datastream.reset();
                        long len = datastream.skip(Long.MAX_VALUE);
                        tailsize = (int) (len % blocksize);
                        datastream = null;
                    }
                }

                if (datastream == null && window.size() - window_offset <= tailsize) {
                    // The likelihood that any blocks will match after this is
                    // nearly nil so call it quits.

                    // Flush the current block
                    if ((current_block.size()) > 0) {
                        delta.add(new Delta(null, current_block.toByteArray()));
                    }

                    current_block.reset();
                    current_block.write(window.toByteArray(), window_offset, window.size() - window_offset);

                    break;
                }

                // Yank off the extra byte and calculate the new window checksum
                oldbyte = window.toByteArray()[window_offset] & 0xFF;
                window_offset += 1;
                int[] x = rollingchecksum(oldbyte, newbyte, a, b, blocksize);
                checksum = x[0];
                a = x[1];
                b = x[2];

                if (current_block.size() >= max_buffer) {
                    delta.add(new Delta(null, current_block.toByteArray()));
                    current_block.reset();
                }

                // Add the old byte the file delta. This is data that was not found
                // inside of a matching block so it needs to be sent to the target.
                current_block.write(oldbyte);
            }
        }

        if (current_block.size() > 0) {
            delta.add(new Delta(null, current_block.toByteArray()));
        }
        return delta;
    }

    public static byte[] blockchecksums(byte[] data, int blocksize) {
        return Checksum.toByteArray(
                Objects.requireNonNull(
                        blockchecksums(new ByteArrayInputStream(data),blocksize)), blocksize
        );
    }

    /**
     * Generator of (weak hash (int), strong hash(bytes)) tuples
     * for each block of the defined size for the given data stream.
     */
    protected static List<Checksum> blockchecksums(ByteArrayInputStream instream, int blocksize) {
        List<Checksum> data = new ArrayList<>();
        while (instream.available() > 0) {
            try {
                byte[] nextBlock = new byte[blocksize];
                int read = instream.read(nextBlock);
                nextBlock = Arrays.copyOf(nextBlock,read);
                int weak = weakchecksum(nextBlock)[0];
                byte[] strong = MessageDigest.getInstance("MD5").digest(nextBlock); // mobsf-ignore weak_hash
                data.add(new Checksum(weak, strong));
            } catch (IOException | NoSuchAlgorithmException e) {
                return null;
            }
        }
        return data;
    }

    public static byte[] patch(byte[] data, byte[] delta) throws IOException {
        return patch(data, RsyncCommands.Deltas.parseFrom(delta));
    }
    protected static byte[] patch(byte[] data, RsyncCommands.Deltas deltas) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        patchstream(new ByteArrayInputStream(data), byteArrayOutputStream, Delta.from(deltas),deltas.getBlocksize());
        return byteArrayOutputStream.toByteArray();
    }

    /**
     * Patches instream using the supplied delta and write the resultantant
     * data to outstream.
     *
     * @param instream
     * @param outstream
     * @param delta
     * @param blocksize
     */

    protected static void patchstream(ByteArrayInputStream instream, ByteArrayOutputStream outstream,
                                   List<Delta> delta,
                                   int blocksize) throws IOException {
        for (int i = 0; i < delta.size(); i++) {
            patchstream_block(instream, outstream, delta.get(i), blocksize);
        }
    }

    protected static void patchstream_block(ByteArrayInputStream instream, ByteArrayOutputStream outstream,
                                          Delta delta_block,
                                          int blocksize) throws IOException {
        if (delta_block.index != null && blocksize > 0) {
            instream.reset();
            long skip = (long) delta_block.index * blocksize;
            long skipped = instream.skip(skip);
            if( skip!=skipped){
                throw new IOException("failed to skip requested number of bytes");
            }
            byte[] block = new byte[blocksize];
            int read = instream.read(block);
            if(read == -1){
                throw new IOException("failed to read bytes");
            }
            outstream.write(block, 0, read);
        } else if(delta_block.data !=null) {
            outstream.write(delta_block.data);
        }
        else{
            throw new IllegalArgumentException("index and data cannot both be null");
        }
    }

    /**
     * Generates a new weak checksum when supplied with the internal state
     * of the checksum calculation for the previous window, the removed
     * byte, and the added byte.
     *
     * @param removed
     * @param added
     * @param a
     * @param b
     * @param blocksize
     * @return
     */
    private static int[] rollingchecksum(int removed, int added, int a, int b, int blocksize) {
        removed &= 0xFF;
        added &= 0xFF;
        a -= removed - added;
        b -= removed * blocksize - a;
        return new int[]{(b << 16) | a, a, b};
    }

    /**
     * Generates a weak checksum from an iterable set of bytes.
     *
     * @param data
     * @return array of checksum and internal state of the sum a,b
     */
    public static int[] weakchecksum(byte[] data) {
        int a = 0, b = 0, l = data.length;
        for (int i = 0; i < l; i++) {
            int d = data[i] & 0xFF;
            a += d;
            b += (l - i) * d;
        }
        return new int[]{(b << 16) | a, a, b};
    }

    private static final char[] HEX_ARRAY = "0123456789ABCDEF".toCharArray();

    public static String bytesToHex(byte[] bytes) {
        char[] hexChars = new char[bytes.length * 2];
        for (int j = 0; j < bytes.length; j++) {
            int v = bytes[j] & 0xFF;
            hexChars[j * 2] = HEX_ARRAY[v >>> 4];
            hexChars[j * 2 + 1] = HEX_ARRAY[v & 0x0F];
        }
        return new String(hexChars);
    }
}