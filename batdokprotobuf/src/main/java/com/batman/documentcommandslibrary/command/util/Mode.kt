package com.batman.documentcommandslibrary.command.util

import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

enum class Mode(override val protoIndex: Int, override val dataString: String): ProtoEnum {
    @Deprecated("This mode was merged into Trauma") TACTICAL_CASUALTY_CARE_CARD(1, "Tactical Casualty Care Card"),
    @Deprecated("This mode is no longer available") MONITORING(2 , "Monitoring"),
    STRATEVAC(3 , "STRATEVAC CCATT"),
    @Deprecated("This mode is no longer available") AMT(4 , "AMT"),
    DNBI(5 , "DNBI"),
    EMS(6 , "EMS"),
    @Deprecated("This mode was merged into Trauma") TACEVAC(7, "TACEVAC"),
    WORKING_DOG(8 , "TCCC K9"),
    @Deprecated("This mode was merged into Trauma") TCCC_MARCH(9 , "TCCC MARCH"),
    STRATEVAC_AE(10, "STRATEVAC AE"),
    STRATEVAC_ACC(11, "Advanced Critical Care"),
    @Deprecated("This mode was merged into Trauma") PCC_MARCH(12, "Prolonged Casualty Care"),
    AERO_EVAC(13, "Aeromedical Evacuation"),
    TRAUMA(14, "Trauma"),
    ;

    companion object{
        fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)
    }
}