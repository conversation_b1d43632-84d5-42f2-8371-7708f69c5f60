package com.batman.documentcommandslibrary.command.util

import com.batman.batdok.infrastructure.contactlesstransfer.proto.ContactlessTransferCommands.ContactlessTransferMessage
import com.batman.batdok.infrastructure.network.commands.EncounterNetworkCommands.EncounterMessage
import com.batman.batdok.infrastructure.network.commands.PlatformCommands.PlatformMessage
import com.google.protobuf.DescriptorProtos
import com.google.protobuf.GeneratedMessage
import com.google.protobuf.GeneratedMessage.GeneratedExtension
import com.google.protobuf.GeneratedMessageV3
import com.google.protobuf.ProtocolMessageEnum
import gov.afrl.batdok.commands.proto.DocumentUpdateCommands.DocumentUpdateMessage
import gov.afrl.batdok.commands.proto.FieldOptions
import gov.afrl.batdok.commands.proto.SharedProtobufObjects

object ProtobufUtils {

    val CURRENT_VERSION = 1;

    fun SharedProtobufObjects.CompatibilityRange.isCompatible(version: Int = CURRENT_VERSION): Boolean{
        val start = inclusiveStart
        val end = exclusiveEnd.takeUnless { it == 0 } ?: Int.MAX_VALUE
        return version in (start until end)
    }
    fun EncounterMessage.isCompatible(version: Int = CURRENT_VERSION): Boolean{
        return !hasCompatibleRange() || compatibleRange.isCompatible(version)
    }
    fun DocumentUpdateMessage.isCompatible(version: Int = CURRENT_VERSION): Boolean{
        return !hasCompatibleRange() || compatibleRange.isCompatible(version)
    }
    fun PlatformMessage.isCompatible(version: Int = CURRENT_VERSION): Boolean{
        return !hasCompatibleRange() || compatibleRange.isCompatible(version)
    }
    fun ContactlessTransferMessage.isCompatible(version: Int = CURRENT_VERSION): Boolean{
        return !hasCompatibleRange() || compatibleRange.isCompatible(version)
    }
//    fun ToBatdokCommand.isCompatible(version: Int = CURRENT_VERSION): Boolean{
//        return !hasCompatibleRange() || compatibleRange.isCompatible(version)
//    }
//    fun FromBatdokCommand.isCompatible(version: Int = CURRENT_VERSION): Boolean{
//        return !hasCompatibleRange() || compatibleRange.isCompatible(version)
//    }

    fun GeneratedExtension<*, *>.introduced() = getExtensionOrNull(FieldOptions.introduced)
    fun GeneratedExtension<*, *>.removed() = getExtensionOrNull(FieldOptions.removed)

    fun <T> GeneratedExtension<*, *>.getExtensionOrNull(extension: GeneratedExtension<DescriptorProtos.FieldOptions, T>): T? {
        return if (descriptor.options.hasExtension(extension)) {
            descriptor.options.getExtension(extension)
        } else {
            null
        }
    }

    fun GeneratedMessageV3.ExtendableMessage<*>.getFirstExtension(): GeneratedMessageV3? {
        allFields.forEach { (descriptor, field) ->
            if(descriptor.isExtension && field is GeneratedMessageV3){
                return field
            }
        }
        return null
    }

    fun GeneratedMessageV3.ExtendableMessage<*>.onEachExtension(block: Any?.() -> Unit) = allFields
        .filter { (descriptor, _) -> descriptor.isExtension }
        .forEach { (descriptor, field) ->
            if(descriptor.isRepeated){
                (field as List<*>).forEach(block)
            }else{
                field.block()
            }
        }

    /**
     * Run the given block on the first extension found in the command.
     *
     * Returns the value returned by the block.
     */
    inline fun <T> GeneratedMessageV3.ExtendableMessage<*>.onFirstExtension(block: GeneratedMessageV3.() -> T): T? {
        return getFirstExtension()?.block()
    }

    fun <T: Any> ProtocolMessageEnum.getExtensionOrNull(extension: GeneratedMessage.GeneratedExtension<DescriptorProtos.EnumValueOptions, T>): T?{
        return if(valueDescriptor.options.hasExtension(extension)){
            valueDescriptor.options.getExtension(extension)
        }else{
            null
        }
    }
}