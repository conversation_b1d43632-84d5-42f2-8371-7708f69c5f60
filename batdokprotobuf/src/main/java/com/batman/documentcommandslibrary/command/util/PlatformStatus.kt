package com.batman.documentcommandslibrary.command.util

import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

enum class PlatformStatus(override val protoIndex: Int, override val dataString: String): ProtoEnum {
    ETA(1, "ETA"),
    LOADING(2, "Loading"),
    EVACED(3, "Evac'ed"),
    UNKNOWN(4, "Unknown"),
    ;

    companion object{
        fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)
    }
}