package com.batman.documentcommandslibrary.command.util

import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

enum class A2APatientType(override val protoIndex: Int, override val dataString: String): ProtoEnum {
    LOCAL(1, "Local"),
    NETWORKED(2, "Networked"),
    ;

    companion object{
        fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)
    }
}