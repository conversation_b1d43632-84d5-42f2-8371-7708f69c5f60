package com.batman.batdok.infrastructure.network.parsers

import com.batman.batdok.infrastructure.network.NetworkAddress
import com.batman.batdok.infrastructure.network.commands.NetworkCommands.NetworkMessage
import com.google.protobuf.Message

data class NetworkResponse(val message: NetworkMessage, val recipient: NetworkAddress? = null)

abstract class MessageParser<T: Message> {
    abstract suspend fun parse(message: T, sender: NetworkAddress? = null): Collection<NetworkResponse>
}