package com.batman.batdok.infrastructure.network.parsers

import com.batman.batdok.infrastructure.network.NetworkAddress
import com.batman.batdok.infrastructure.network.commands.BatdokChatCommands
import com.batman.batdok.infrastructure.network.commands.BatdokTrainerCommands
import com.batman.batdok.infrastructure.network.commands.MissionSupportCommands
import com.batman.batdok.infrastructure.network.commands.NetworkCommands.MiscMessage
import com.batman.batdok.infrastructure.network.commands.mace.MaceCommands

abstract class MiscMessageParser: MessageParser<MiscMessage>() {
    override suspend fun parse(message: MiscMessage, sender: NetworkAddress?): Collection<NetworkResponse> {
        val replies = mutableListOf<NetworkResponse?>()
        with(message){
            when(messageCase){
                MiscMessage.MessageCase.ROSTERMESSAGE -> parse(rosterMessage,sender)
                MiscMessage.MessageCase.MACEMESSAGE -> parse(maceMessage,sender)
                MiscMessage.MessageCase.SENSORALIASMESSAGE -> parse(sensor<PERSON>liasMessage,sender)
                MiscMessage.MessageCase.BATDOKTRAINERVITALMESSAGE -> parse(batdokTrainerVitalMessage,sender)
                MiscMessage.MessageCase.BATDOKCHATMESSAGE -> parse(batdokChatMessage,sender)
                else -> null
            }?.let { replies.addAll(it)}
        }
        return replies.filterNotNull()
    }

    protected abstract suspend fun parse(message: MissionSupportCommands.RosterMessage, sender: NetworkAddress?): Collection<NetworkResponse>
    protected abstract suspend fun parse(message: MaceCommands.MaceMessage, sender: NetworkAddress?): Collection<NetworkResponse>
    protected abstract suspend fun parse(message: MissionSupportCommands.SensorAliasMessage, sender: NetworkAddress?): Collection<NetworkResponse>
    protected abstract suspend fun parse(message: BatdokTrainerCommands.BatdokTrainerVitalMessage, sender: NetworkAddress?): Collection<NetworkResponse>
    protected abstract suspend fun parse(message: BatdokChatCommands.BatdokChatMessage, sender: NetworkAddress?): Collection<NetworkResponse>
}