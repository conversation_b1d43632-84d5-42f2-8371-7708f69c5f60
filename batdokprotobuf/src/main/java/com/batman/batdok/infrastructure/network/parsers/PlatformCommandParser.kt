package com.batman.batdok.infrastructure.network.parsers

import com.batman.batdok.infrastructure.network.NetworkAddress
import com.batman.batdok.infrastructure.network.commands.PlatformCommands.*
import com.batman.documentcommandslibrary.command.util.ProtobufUtils.isCompatible
import com.google.protobuf.Any
import com.google.protobuf.Message
import com.google.protobuf.kotlin.isA
import com.google.protobuf.kotlin.unpack

abstract class PlatformCommandParser: MessageParser<PlatformMessage>(){

    override suspend fun parse(message: PlatformMessage, sender: NetworkAddress?): Collection<NetworkResponse> {
        //If the command isn't compatible, don't do it
        if(!message.isCompatible()) return listOf()

        val replies = mutableListOf<NetworkResponse?>()

        with(message){
            tryParse<AddPatientsToPlatformMessage>(data){ parse(it,sender) }
            tryParse<ChangePlatformNameMessage>(data){ parse(it,sender) }
            tryParse<CreatePlatformMessage>(data){ parse(it,sender) }
            tryParse<DeletePlatformsMessage>(data){ parse(it,sender) }
            tryParse<RemovePatientsFromPlatformsMessage>(data){ parse(it,sender) }
            tryParse<UpdateRankMessage>(data){ parse(it,sender) }
            tryParse<ChangeOutTimeMessage>(data){ parse(it,sender) }
            tryParse<ChangePlatformStatusMessage>(data){ parse(it,sender) }
            tryParse<ChangeDestinationMessage>(data){ parse(it,sender) }
        }?.let { replies.addAll(it) }
        return replies.filterNotNull()
    }

    private inline fun <reified T: Message> tryParse(any: Any, action: (T) -> Collection<NetworkResponse>): Collection<NetworkResponse>{
        return if(any.isA<T>()){
            action(any.unpack())
        }else{
            listOf()
        }
    }

    abstract suspend fun PlatformMessage.parse(message: AddPatientsToPlatformMessage, sender: NetworkAddress?): Collection<NetworkResponse>
    abstract suspend fun PlatformMessage.parse(message: ChangePlatformNameMessage, sender: NetworkAddress?): Collection<NetworkResponse>
    abstract suspend fun PlatformMessage.parse(message: CreatePlatformMessage, sender: NetworkAddress?): Collection<NetworkResponse>
    abstract suspend fun PlatformMessage.parse(message: DeletePlatformsMessage, sender: NetworkAddress?): Collection<NetworkResponse>
    abstract suspend fun PlatformMessage.parse(message: RemovePatientsFromPlatformsMessage, sender: NetworkAddress?): Collection<NetworkResponse>
    abstract suspend fun PlatformMessage.parse(message: UpdateRankMessage, sender: NetworkAddress?): Collection<NetworkResponse>
    abstract suspend fun PlatformMessage.parse(message: ChangeOutTimeMessage, sender: NetworkAddress?): Collection<NetworkResponse>
    abstract suspend fun PlatformMessage.parse(message: ChangePlatformStatusMessage, sender: NetworkAddress?): Collection<NetworkResponse>
    abstract suspend fun PlatformMessage.parse(message: ChangeDestinationMessage, sender: NetworkAddress?): Collection<NetworkResponse>
}