package com.batman.batdok.infrastructure.network.parsers

import com.batman.batdok.infrastructure.network.NetworkAddress
import com.batman.batdok.infrastructure.network.commands.EncounterNetworkCommands.*
import com.batman.batdok.infrastructure.network.commands.EncounterNetworkCommands.EncounterMessage.MessageCase
import com.batman.documentcommandslibrary.command.util.ProtobufUtils.isCompatible

abstract class EncounterMessageParser: MessageParser<EncounterMessage>() {
    override suspend fun parse(message: EncounterMessage, sender: NetworkAddress?): Collection<NetworkResponse> {
        //If the command isn't compatible, don't do it
        if(!message.isCompatible()) return listOf()

        val replies = mutableListOf<NetworkResponse?>()
        with(message){
            when(messageCase){
                MessageCase.CREATEMESSAGE -> parse(createMessage,sender)
                MessageCase.VITALSMESSAGE -> parse(vitalsMessage,sender)
                MessageCase.LOCALENCOUNTERSIDLISTMESSAGE -> parse(localEncountersIdListMessage,sender)
                MessageCase.ENCOUNTERIMAGEMESSAGE -> parse(encounterImageMessage,sender)
                MessageCase.REQUESTOWNERSHIPMESSAGE -> parse(requestOwnershipMessage, sender)
                MessageCase.SENSORTRANSFERMESSAGE -> parse(sensorTransferMessage,sender)
                MessageCase.REQUESTENCOUNTERHASHESMESSAGE -> parse(requestEncounterHashesMessage,sender)
                MessageCase.REQUESTENCOUNTERMESSAGE -> parse(requestEncounterMessage,sender)
                MessageCase.GEOTAGENCOUNTERMESSAGE -> parse(geotagEncounterMessage,sender)
                MessageCase.HANDOFFDATAMESSAGE -> parse(handoffDataMessage,sender)
                MessageCase.TRANSFEROWNERSHIPMESSAGE -> parse(transferOwnershipMessage, sender)
                MessageCase.OWNERSHIPCHANGERESPONSE -> parse(ownershipChangeResponse,sender)
                MessageCase.OWNERSHIPTRANSFERREDMESSAGE -> parse(ownershipTransferredMessage,sender)
                MessageCase.ACKNOWLEDGEOWNERSHIPCHANGEMESSAGE -> parse(acknowledgeOwnershipChangeMessage,sender)
                MessageCase.REQUESTENCOUNTERLOCATION -> parse(requestEncounterLocation,sender)
                MessageCase.CHANGEMODECOMMAND -> parse(changeModeCommand,sender)
                MessageCase.EXPORTSTATUSCOMMAND -> parse(exportStatusCommand,sender)
                MessageCase.CHANGEGENERATEDNAMECOMMAND -> parse(changeGeneratedNameCommand,sender)
                else -> null
            }?.let { replies.addAll(it)}
        }
        return replies.filterNotNull()
    }

    protected abstract suspend fun parse(message: CreateEncounterMessage, sender: NetworkAddress?): Collection<NetworkResponse>
    protected abstract suspend fun parse(message: VitalsMessage, sender: NetworkAddress?): Collection<NetworkResponse>
    protected abstract suspend fun parse(message: LocalEncountersIdListMessage, sender: NetworkAddress?): Collection<NetworkResponse>
    protected abstract suspend fun parse(message: EncounterImageMessage, sender: NetworkAddress?): Collection<NetworkResponse>
    protected abstract suspend fun parse(message: RequestOwnershipMessage, sender: NetworkAddress?): Collection<NetworkResponse>
    protected abstract suspend fun parse(message: SensorTransferMessage, sender: NetworkAddress?): Collection<NetworkResponse>
    protected abstract suspend fun parse(message: RequestEncounterHashesMessage, sender: NetworkAddress?): Collection<NetworkResponse>
    protected abstract suspend fun parse(message: RequestEncounterMessage, sender: NetworkAddress?): Collection<NetworkResponse>
    protected abstract suspend fun parse(message: GeotagEncounterMessage, sender: NetworkAddress?): Collection<NetworkResponse>
    protected abstract suspend fun parse(message: HandoffDataMessage, sender: NetworkAddress?): Collection<NetworkResponse>
    protected abstract suspend fun parse(message: TransferOwnershipMessage, sender: NetworkAddress?): Collection<NetworkResponse>
    protected abstract suspend fun parse(message: OwnershipChangeResponse, sender: NetworkAddress?): Collection<NetworkResponse>
    protected abstract suspend fun parse(message: OwnershipTransferredMessage, sender: NetworkAddress?): Collection<NetworkResponse>
    protected abstract suspend fun parse(message: AcknowledgeOwnershipChangeRequestMessage, sender: NetworkAddress?): Collection<NetworkResponse>
    protected abstract suspend fun parse(message: RequestEncounterLocation, sender: NetworkAddress?): Collection<NetworkResponse>
    protected abstract suspend fun parse(message: ChangeModeCommand, sender: NetworkAddress?): Collection<NetworkResponse>
    protected abstract suspend fun parse(message: ExportStatusCommand, sender: NetworkAddress?): Collection<NetworkResponse>
    protected abstract suspend fun parse(message: ChangeGeneratedNameCommand, sender: NetworkAddress?): Collection<NetworkResponse>
}