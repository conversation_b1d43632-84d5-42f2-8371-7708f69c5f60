package com.batman.batdok.infrastructure.network.parsers

import com.batman.batdok.infrastructure.network.NetworkAddress
import gov.afrl.batdok.commands.proto.UserDataOuterClass.UserData
import gov.afrl.batdok.encounter.ids.toDomainId
import mil.af.afrl.batman.batdokid.DomainId

abstract class UserDataParser: MessageParser<UserData>() {
    override suspend fun parse(message: UserData, sender: NetworkAddress?): Collection<NetworkResponse> {
        handleData(
            message.id.toDomainId(),
            message.name,
            message.dodId,
            message.callsign
        )
        return listOf()
    }

    abstract suspend fun handleData(id: DomainId, name: String, dodId: String, callsign: String)
}