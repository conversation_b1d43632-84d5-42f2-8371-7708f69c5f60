package com.batman.batdok.infrastructure.network.parsers

import com.batman.batdok.infrastructure.contactlesstransfer.proto.ContactlessTransferCommands.*
import com.batman.batdok.infrastructure.network.commands.EncounterNetworkCommands.CreateEncounterMessage
import com.batman.batdok.infrastructure.network.commands.MissionSupportCommands
import com.batman.batdok.infrastructure.network.commands.RosterOuterClass
import com.batman.batdok.infrastructure.network.commands.mace.MaceCommands
import com.batman.documentcommandslibrary.command.util.ProtobufUtils.isCompatible

abstract class ContactlessTransferParser {

    suspend fun parse(message: ContactlessTransferMessage) {
        //Get Metadata
        val index = message.messageIndex
        val count = message.messageCount
        val senderCallsign = message.senderInfo.callsign
        //If the command isn't compatible, don't do it
        if(!message.isCompatible()) return

        //Parse Data
        if(message.hasEncounterCommand()) parseEncounters(listOf(message.encounterCommand))
        if(message.bulkEncounterMessageCount > 0) parseEncounters(message.bulkEncounterMessageList)
        if(message.hasSensorMessage()) parse(message.sensorMessage)
        if(message.hasMedMessage()) parse(message.medMessage)
        if(message.newEncounterMessage) parse(message.newEncounterMessage)
        if(message.hasTreatmentMessage()) parse(message.treatmentMessage)
        if(message.hasRosterMessage()) parseRosters(listOf(message.rosterMessage))
        if(message.multiRosterMessageCount > 0) parseRosters(message.multiRosterMessageList)
        if(message.hasMedBagMessage()) parse(message.medBagMessage)
        if(message.hasFastMedMessage()) parse(message.fastMedMessage)
        if(message.hasMaceMessage()) parse(message.maceMessage)
        if(message.hasNetworkKeyMessage()) parse(message.networkKeyMessage)
        if(message.hasCompressedMultipartPacket()) parse(message.compressedMultipartPacket)
    }

    //Thee Patients and rosters need different names because they're both lists
    protected abstract suspend fun parseEncounters(message: List<CreateEncounterMessage>)
    protected abstract suspend fun parse(sensorMessage: ContactlessTransferSensorMessage)
    protected abstract suspend fun parse(medMessage: ContactlessTransferMedMessage)
    protected abstract suspend fun parse(newEncounterMessage: Boolean)
    protected abstract suspend fun parse(treatmentMessage: ContactlessTransferTreatmentMessage)
    protected abstract suspend fun parseRosters(rosterMessage: List<RosterOuterClass.Roster>)
    protected abstract suspend fun parse(medBagMessage: MissionSupportCommands.MedBagMessage)
    protected abstract suspend fun parse(fastMedMessage: MissionSupportCommands.FastMedMessage)
    protected abstract suspend fun parse(maceMessage: MaceCommands.MaceMessage)
    protected abstract suspend fun parse(networkKeyMessage: NetworkKeyMessage)
    protected abstract suspend fun parse(compressedMultipartPacket: CompressedMultipartPacket)
}

/**
 * Inherit from this clas if you only want to handle a small subset of functions.
 *
 * All others will throw an IncorrectMessageTypeException
 */
abstract class EmptyContactlessTransferParser: ContactlessTransferParser(){

    private fun error(){
        throw IncorrectMessageTypeException()
    }
    override suspend fun parseEncounters(message: List<CreateEncounterMessage>) = error()
    override suspend fun parse(sensorMessage: ContactlessTransferSensorMessage) = error()
    override suspend fun parse(medMessage: ContactlessTransferMedMessage) = error()
    override suspend fun parse(newPatientMessage: Boolean) = error()
    override suspend fun parse(treatmentMessage: ContactlessTransferTreatmentMessage) = error()
    override suspend fun parseRosters(rosterMessage: List<RosterOuterClass.Roster>) = error()
    override suspend fun parse(medBagMessage: MissionSupportCommands.MedBagMessage) = error()
    override suspend fun parse(fastMedMessage: MissionSupportCommands.FastMedMessage) = error()
    override suspend fun parse(maceMessage: MaceCommands.MaceMessage) = error()
    override suspend fun parse(networkKeyMessage: NetworkKeyMessage) = error()
    override suspend fun parse(compressedMultipartPacket: CompressedMultipartPacket) = error()
}

class IncorrectMessageTypeException: Exception()