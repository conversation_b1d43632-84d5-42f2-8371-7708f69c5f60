package com.batman.batdok.infrastructure.network.parsers

import com.batman.batdok.infrastructure.network.commands.NetworkCommands.NetworkMessage
import com.batman.batdok.infrastructure.network.NetworkAddress

class NetworkParser(
    private val patientMessageParser: EncounterMessageParser,
    private val documentCommandParser: DocumentUpdateCommandParser,
    private val platformFullUpdateCommandParser: PlatformFullUpdateCommandParser,
    private val miscMessageParser: MiscMessageParser,
    private val userDataParser: UserDataParser
): MessageParser<NetworkMessage>() {

    override suspend fun parse(message: NetworkMessage, sender: NetworkAddress?): Collection<NetworkResponse> {
        val replies = mutableListOf<NetworkResponse?>()

        message.encounterMessageList.flatMap { patientMessageParser.parse(it, sender) }.let { replies.addAll(it) }
        message.documentUpdateMessageList.flatMap { documentCommandParser.parse(it, sender)}.let { replies.addAll(it) }
        if(message.hasPlatformFullUpdateMessage()) platformFullUpdateCommandParser.parse(message.platformFullUpdateMessage, sender).let { replies.addAll(it) }
        message.miscMessageList.flatMap { miscMessageParser.parse(it, sender) }.let { replies.addAll(it) }
        message.userDataList.flatMap { userDataParser.parse(it, sender) }.let { replies.addAll(it) }

        return replies.filterNotNull()
    }
}