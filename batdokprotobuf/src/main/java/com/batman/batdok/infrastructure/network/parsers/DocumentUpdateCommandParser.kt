package com.batman.batdok.infrastructure.network.parsers

import com.batman.batdok.infrastructure.network.NetworkAddress
import com.batman.documentcommandslibrary.command.util.ProtobufUtils.isCompatible
import gov.afrl.batdok.commands.proto.DocumentCommands.DocumentCommand
import gov.afrl.batdok.commands.proto.DocumentUpdateCommands.*

abstract class DocumentUpdateCommandParser: MessageParser<DocumentUpdateMessage>(){

    override suspend fun parse(message: DocumentUpdateMessage, sender: NetworkAddress?): Collection<NetworkResponse> {
        //If the command isn't compatible, don't do it
        if(!message.isCompatible()) return listOf()

        val replies = mutableListOf<NetworkResponse?>()
        with (message) {
            when(messageCase){
                DocumentUpdateMessage.MessageCase.DOCUMENTCOMMAND -> parseNormalUpdate(documentCommand, sender)
                DocumentUpdateMessage.MessageCase.FULLUPDATECOMMAND -> parseFullUpdate(fullUpdateCommand, sender)
                DocumentUpdateMessage.MessageCase.FULLUPDATEREQUEST -> parse(fullUpdateRequest, sender)
                DocumentUpdateMessage.MessageCase.POSTFULLUPDATESTATUS -> parse(postFullUpdateStatus,sender)
                DocumentUpdateMessage.MessageCase.DOCUMENTDELTAMESSAGE -> parse(documentDeltaMessage,sender)
                else -> null
            }
        }?.let {replies.addAll(it) }
        return replies.filterNotNull()
    }

    protected abstract suspend fun parseNormalUpdate(message: DocumentCommand, sender: NetworkAddress?): Collection<NetworkResponse>
    protected abstract suspend fun parseFullUpdate(message: DocumentCommand, sender: NetworkAddress?): Collection<NetworkResponse>
    protected abstract suspend fun parse(message: FullUpdateRequest, sender: NetworkAddress?): Collection<NetworkResponse>
    protected abstract suspend fun parse(message: PostFullUpdateStatus, sender: NetworkAddress?): Collection<NetworkResponse>
    protected abstract suspend fun parse(message: DocumentDeltaMessage, sender: NetworkAddress?): Collection<NetworkResponse>
}