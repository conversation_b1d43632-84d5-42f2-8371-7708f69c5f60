package com.batman.batdok.infrastructure.network.parsers

import com.batman.batdok.infrastructure.network.NetworkAddress
import com.batman.batdok.infrastructure.network.commands.PlatformCommands.PlatformFullUpdateMessage

abstract class PlatformFullUpdateCommandParser(private val platformCommandParser: PlatformCommandParser): MessageParser<PlatformFullUpdateMessage>(){

    override suspend fun parse(message: PlatformFullUpdateMessage, sender: NetworkAddress?): Collection<NetworkResponse> {
        preFullUpdate()
        message.messageList.forEach { platformCommandParser.parse(it) }
        return listOf()
    }

    abstract suspend fun preFullUpdate()
}