package com.batman.batdok.infrastructure.network.util

import com.batman.batdok.infrastructure.network.commands.*
import com.batman.documentcommandslibrary.command.util.PlatformStatus
import com.google.protobuf.Any
import com.google.protobuf.Message
import gov.afrl.batdok.encounter.ids.toByteString
import mil.af.afrl.batman.batdokid.DomainId
import java.util.Date

fun buildPlatformMessage(subcommand: Message, date: Date = Date(), callsign: String = "") = platformMessage{
    this.data = Any.pack(subcommand, "")
    this.callsign = callsign
    this.date = date.time
}

fun buildAddPatientsToPlatformCommand(platformId: DomainId, encounterIds: List<DomainId>) = addPatientsToPlatformMessage {
    encounterId.addAll(encounterIds.map { it.toByteString() })
    this.platformId = platformId.toByteString()
}

fun buildChangePlatformDestinationCommand(id: DomainId, destination: String) = changeDestinationMessage {
    this.platformId = id.toByteString()
    this.destination = destination
}

fun buildChangePlatformNameCommand(id: DomainId, newName: String) = changePlatformNameMessage {
    this.platformId = id.toByteString()
    this.name = newName
}

fun buildChangePlatformOutTimeCommand(id: DomainId, time: Long) = changeOutTimeMessage {
    this.platformId = id.toByteString()
    this.time = time
}

fun buildChangePlatformStatusCommand(id: DomainId, status: String?) = changePlatformStatusMessage {
    this.platformId = id.toByteString()
    this.status = PlatformStatus.fromString(status)
}

fun buildCreatePlatformCommand(id: DomainId, pName: String, isTutorialPlatform: Boolean = false) = createPlatformMessage {
    this.platformId = id.toByteString()
    this.name = pName
    this.isTutorialPlatform = isTutorialPlatform
}

fun buildCreatePlatformCommand(isTutorialPlatform: Boolean = false) = createPlatformMessage {
    this.isTutorialPlatform = isTutorialPlatform
}

fun buildDeletePlatformsCommand(platforms: List<DomainId>) = deletePlatformsMessage {
    platformId.addAll(platforms.map { it.toByteString() })
}

fun buildRemovePatientsFromPlatformsCommand(encounterIds: List<DomainId>) = removePatientsFromPlatformsMessage {
    encounterId.addAll(encounterIds.map { it.toByteString() })
}

fun buildUpdatePatientPlatformRankCommand(platformId: DomainId, encounterId: DomainId, oldRank: Int, newRank: Int) =
    updateRankMessage {
        this.platformId = platformId.toByteString()
        this.encounterId = encounterId.toByteString()
        this.newRank = newRank
        this.oldRank = oldRank
    }