package com.batman.batdok.infrastructure.network.util

import com.batman.batdok.infrastructure.network.commands.EncounterNetworkCommands
import com.batman.batdok.infrastructure.network.commands.changeModeCommand
import com.batman.batdok.infrastructure.network.commands.encounterMessage
import com.batman.documentcommandslibrary.command.util.Mode
import com.google.protobuf.ByteString
import java.time.Instant

fun buildHandoffDataUpdatedMessage(encounterId: ByteString, age: String?, stability: Boolean?, injury: String?): EncounterNetworkCommands.EncounterMessage{
    val builder = EncounterNetworkCommands.HandoffDataMessage.newBuilder()
            .setEncounterId(encounterId)

    age?.let{builder.setAge(it)}
    stability?.let{ builder.setStability(it) }
    injury?.let{ builder.setInjury(it) }

    return encounterMessage {
        handoffDataMessage = builder.build()
    }
}

fun buildEncounterGeoTagMessage(encounterId: ByteString, lat: Double, long:Double, altitude: Double?, markTime: Instant): EncounterNetworkCommands.EncounterMessage{
    val locationBuilder = EncounterNetworkCommands.Location.newBuilder()
    if(lat != 0.0 || long !=0.0) {
        locationBuilder.lat = lat
        locationBuilder.long = long
        if (altitude != null) locationBuilder.altitude = altitude
    }

    val message = EncounterNetworkCommands.GeotagEncounterMessage.newBuilder()
            .setEncounterId(encounterId)
            .setLocation(locationBuilder.build())
            .setMarkTime(markTime.epochSecond)
            .build()

    return encounterMessage { geotagEncounterMessage = message }
}

fun buildRequestEncounterLocationMessage(encounterId: ByteString) : EncounterNetworkCommands.EncounterMessage {
    val message =  EncounterNetworkCommands.RequestEncounterLocation.newBuilder()
            .setEncounterId(encounterId)
            .build()
    return encounterMessage { requestEncounterLocation = message }
}

fun buildChangeModeCommand(encounterId: ByteString, newMode: String) = encounterMessage {
    changeModeCommand = changeModeCommand {
        mode = Mode.fromString(newMode)
        this.encounterId = encounterId
    }
}


