package com.batman.batdok.infrastructure.contactlesstransfer.util

import com.batman.batdok.infrastructure.contactlesstransfer.proto.ContactlessTransferCommands.ContactlessTransferMessage
import com.batman.batdok.infrastructure.contactlesstransfer.proto.ContactlessTransferMessageKt
import com.batman.batdok.infrastructure.contactlesstransfer.proto.contactlessTransferMessage
import com.batman.batdok.infrastructure.contactlesstransfer.proto.contactlessTransferSenderInfo

object ContactlessTransferProtobufUtils {
    @JvmStatic
    @JvmOverloads
    fun buildContactlessTransferMessage(messageIndex: Int = 0,
                                        messageCount: Int = 1,
                                        senderCallsign: String? = null): ContactlessTransferMessage.Builder {
        val builder = ContactlessTransferMessage.newBuilder()
            .setMessageIndex(messageIndex)
            .setMessageCount(messageCount)

        if (senderCallsign != null) {
            builder.senderInfo = contactlessTransferSenderInfo {
                callsign = senderCallsign
            }
        }

        return builder
    }

    fun buildContactlessTransferMessage(
        messageIndex: Int = 0,
        messageCount: Int = 1,
        senderCallsign: String? = null,
        block: ContactlessTransferMessageKt.Dsl.() -> Unit
    ) = contactlessTransferMessage {
        this.messageIndex = messageIndex
        this.messageCount = messageCount

        if (senderCallsign != null) {
            senderInfo = contactlessTransferSenderInfo {
                callsign = senderCallsign
            }
        }

        block()
    }
}