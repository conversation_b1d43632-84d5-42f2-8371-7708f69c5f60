variables:
  GRADLE_OPTS: '-Dgradle.user.home=$CI_PROJECT_DIR/.gradle -Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository -Dorg.gradle.console=plain -Dorg.gradle.warning.mode=none -Dorg.gradle.logging.level=quiet'
  GRADLE_CMD_OPTS: '--no-daemon --no-configure-on-demand --console=plain --parallel'

stages:
  - test
  - publishRemote

# Make this only happen on Merge Request, Commit onto master, and adding a Tag
workflow:
  rules:
    - if: $CI_COMMIT_TAG
      variables:
        ORG_GRADLE_PROJECT_buildRelease: "true"

    - if: $CI_COMMIT_TAG
    - if: $CI_COMMIT_REF_PROTECTED == 'true'
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"

default:
  image: $CI_REGISTRY/softwarecell/gitlab-components/android:main #this will always use the latest version published from the main branch
  retry: 1
  before_script:
    - chmod +x ./gradlew
    - export
    - free -h
    - df -h
    - find /cache -maxdepth 4
    - find $CI_PROJECT_DIR -maxdepth 3

test:
  stage: test
  script:
    - python3 /opt/android-project-validator.py
    - ./gradlew $GRADLE_CMD_OPTS testDebugUnitTest :batdokdocument:test :batdokprotobuf:test
  artifacts:
    paths:
      - $CI_PROJECT_DIR/**/build/reports/
    reports:
      junit: $CI_PROJECT_DIR/**/build/test-results/**/TEST-*.xml
    expire_in: 1 day
    when: always

publishRemote:
  stage: publishRemote
  dependencies: []
  parallel:
    matrix:
      - LIBRARY:
          - batdokdata
          - batdokdocument
          - batdokprotobuf
          - batdokstorageroom
          - batdokstoragetest
          - exportlibrary
          - pdfrobots
          - hl7library
  script:
    - ./gradlew --stop
    - export URL="$CI_API_V4_URL/projects/$CI_PROJECT_ID/packages/maven/$(./gradlew :$LIBRARY:MavenPublishingInfo | grep "mil/af/afrl")"
    # Try to access the library in the maven repository (wget...$URL). If it does not exist, publish the library
    - wget -q --spider --user "$CI_REGISTRY_USER" --password "$CI_JOB_TOKEN" $URL || ./gradlew :$LIBRARY:publish

publishBOM:
  stage: publishRemote
  dependencies: []
  rules:
    - if: $CI_COMMIT_TAG
  script:
    - ./gradlew --stop
    - ./gradlew $GRADLE_CMD_OPTS :internal:publish