package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.commands.buildAddCustomActionCommand
import gov.afrl.batdok.encounter.commands.buildRemoveCustomActionCommand
import gov.afrl.batdok.encounter.commands.buildUpdateCustomActionCommand
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.encounter.ids.CustomActionId
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.util.buildCommandData
import gov.afrl.batdok.util.buildDocumentCommand
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class CustomActionsStateTest {

    val documentId = DomainId.create<DocumentId>()
    val customActions = CustomActions()
    val callsign = "CS"

    //region Helper Functions
    private fun handle(
        subCommand: Message,
        callsign: String = this.callsign,
        timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS),
        id: CommandId = DomainId.create()
    ) {
        customActions.handlers.handle(documentId, buildCommandData(subCommand, callsign, timestamp, id))
    }

    @Test
    fun testLogCustomAction(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val customActionId = DomainId.create<CustomActionId>()
        val description = "description test"
        val message = "testing message"
        val customActionToAdd = CustomAction(customActionId, timestamp, description, message, callsign)

        handle(buildAddCustomActionCommand(customActionToAdd))

        val addedCustomAction = customActions.actions[0]
        Assert.assertEquals(customActionId, addedCustomAction.id)
        Assert.assertEquals(message, addedCustomAction.message)
        Assert.assertEquals(description, addedCustomAction.description)
        Assert.assertEquals(timestamp, addedCustomAction.timestamp)
        Assert.assertEquals(callsign, addedCustomAction.callSign)
    }

    @Test
    fun testUpdateCustomAction(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val customActionId = DomainId.create<CustomActionId>()
        val description = "description test"
        val message = "testing message"
        val customActionToAdd = CustomAction(customActionId, timestamp, description, message, callsign)
        customActions += customActionToAdd

        val preUpdateCustomAction = customActions.actions[0]
        Assert.assertEquals(customActionId, preUpdateCustomAction.id)
        Assert.assertEquals(message, preUpdateCustomAction.message)
        Assert.assertEquals(description, preUpdateCustomAction.description)

        val updatedMessage = "Updated Message Test"
        val updatedDescrition = "Updated Description Test"
        val newCustomAction = customActionToAdd.copy(message = updatedMessage, timestamp = Instant.now().truncatedTo(
            ChronoUnit.SECONDS), description = updatedDescrition)

        handle(buildUpdateCustomActionCommand(newCustomAction), callsign = "NoteGuy")

        Assert.assertEquals(1, customActions.actions.size)

        val postUpdateCustomAction = customActions.actions[0]
        Assert.assertEquals(customActionId, postUpdateCustomAction.id)
        //Check new updates
        Assert.assertEquals(newCustomAction.message, postUpdateCustomAction.message)
        Assert.assertEquals(newCustomAction.description, postUpdateCustomAction.description)
        Assert.assertEquals(newCustomAction.timestamp, postUpdateCustomAction.timestamp)
        Assert.assertEquals("NoteGuy", postUpdateCustomAction.callSign)
    }

    @Test
    fun testRemoveCustomAction(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val customActionId = DomainId.create<CustomActionId>()
        val description = "description test"
        val message = "testing message"
        val customActionToAdd = CustomAction(customActionId, timestamp, description, message, callsign)
        customActions += customActionToAdd

        val preRemoveNote = customActions.actions[0]
        Assert.assertEquals(customActionId, preRemoveNote.id)
        Assert.assertEquals(message, preRemoveNote.message)
        Assert.assertEquals(description, preRemoveNote.description)
        Assert.assertEquals(timestamp, preRemoveNote.timestamp)
        Assert.assertEquals(callsign, preRemoveNote.callSign)

        handle(buildRemoveCustomActionCommand(customActionId))

        Assert.assertEquals(0, customActions.actions.size)
    }

    @Test
    fun testAddIfDoesntExistOnUpdate(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val customActionId = DomainId.create<CustomActionId>()
        val description = "description test"
        val message = "testing message"
        val customActionToUpdate = CustomAction(customActionId, timestamp, description, message, callsign)

        handle(buildUpdateCustomActionCommand(customActionToUpdate))

        Assert.assertEquals(1, customActions.actions.size)
    }

    @Test
    fun testAddWithId(){
        val id = DomainId.create<DomainId>()
        val doc = Document()

        doc.handle(
            listOf(
                buildDocumentCommand(
                    DomainId.create(),
                    buildCommandData(buildAddCustomActionCommand(CustomAction(id.copy(), message = "message", callSign = callsign)))
                ),
            )
        )
        Assert.assertEquals(id, doc.customActions.actions.first().id)
    }
}