package gov.afrl.batdok.encounter.treatments

import gov.afrl.batdok.encounter.treatment.DressingData
import org.junit.Assert
import org.junit.Test

class DressingDataTest {

    @Test
    fun testDressingTypePairs(){
        fun expectValidPair(type: DressingData.Type, subType: DressingData.SubType){
            try{
                DressingData(type.dataString, null, subType.dataString)
            }catch (ex: IllegalArgumentException){
                Assert.fail("${type.dataString}/${subType.dataString} should be a valid pair")
            }
        }
        fun expectInvalidPair(type: DressingData.Type, subType: DressingData.SubType){
            try{
                DressingData(type.dataString, null, subType.dataString)
                Assert.fail("${type.dataString}/${subType.dataString} should not be a valid pair")
            }catch (ex: IllegalArgumentException){
                // It should throw here
            }
        }

        expectValidPair(DressingData.Type.HEMOSTATIC, DressingData.SubType.XSTAT)
        expectInvalidPair(DressingData.Type.HEMOSTATIC, DressingData.SubType.COMMERCIAL)
        expectInvalidPair(DressingData.Type.HEMOSTATIC, DressingData.SubType.ELASTIC)

        expectInvalidPair(DressingData.Type.PRESSURE, DressingData.SubType.XSTAT)
        expectValidPair(DressingData.Type.PRESSURE, DressingData.SubType.COMMERCIAL)
        expectValidPair(DressingData.Type.PRESSURE, DressingData.SubType.ELASTIC)

        expectInvalidPair(DressingData.Type.KERLIX, DressingData.SubType.XSTAT)
        expectInvalidPair(DressingData.Type.KERLIX, DressingData.SubType.COMMERCIAL)
        expectInvalidPair(DressingData.Type.KERLIX, DressingData.SubType.ELASTIC)

        expectInvalidPair(DressingData.Type.ABDOMINAL, DressingData.SubType.XSTAT)
        expectInvalidPair(DressingData.Type.ABDOMINAL, DressingData.SubType.COMMERCIAL)
        expectInvalidPair(DressingData.Type.ABDOMINAL, DressingData.SubType.ELASTIC)
    }
}