package gov.afrl.batdok.encounter.orders

import junit.framework.TestCase.assertEquals
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Test

class MedicineOrderLineTest {

    var orderedMedicine =  OrderedMedicine("Med", "ndc", "rxcui", DomainId.create(), "IV", 0F, "mL", null, null, null, "Med");

    @Test
    fun verifyTitle1(){
        orderedMedicine = OrderedMedicine("Med", "ndc", "rxcui", DomainId.create(), "IV", 147f, "mL", null, null, null, "Med");
        assertEquals("Med, IV, 147 mL", orderedMedicine.title)
    }

    @Test
    fun verifyTitle2(){
        orderedMedicine = OrderedMedicine("Med", "ndc", "rxcui", DomainId.create(), "IV", 147.1f, "mL", null, null, null, "Med");
        assertEquals("Med, IV, 147.1 mL", orderedMedicine.title)
    }

    @Test
    fun verifyTitle3(){
        orderedMedicine = OrderedMedicine("Med", "ndc", "rxcui", DomainId.create(), "IV", 147.10f, "mL", null, null, null, "Med");
        assertEquals("Med, IV, 147.1 mL", orderedMedicine.title)
    }

    @Test
    fun verifyTitle4(){
        orderedMedicine = OrderedMedicine("Med", "ndc", "rxcui", DomainId.create(), "IV", 1.01f, "mL", null, null, null, "Med");
        assertEquals("Med, IV, 1.01 mL", orderedMedicine.title)
    }

}