package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.Vitals.Companion.includeVitalEvents
import gov.afrl.batdok.encounter.commands.addVital
import gov.afrl.batdok.encounter.commands.buildLogVitalCommand
import gov.afrl.batdok.encounter.commands.buildRemoveVitalCommand
import gov.afrl.batdok.encounter.commands.buildUpdateVitalCommand
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.encounter.ids.EncounterVitalId
import gov.afrl.batdok.encounter.vitals.BloodPressure
import gov.afrl.batdok.encounter.vitals.HR
import gov.afrl.batdok.encounter.vitals.Resp
import gov.afrl.batdok.encounter.vitals.SpO2
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class VitalEventTest {

    val vitals = Vitals()
    val events = Events()

    private fun handleIndividualEvents(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS), commandId: CommandId = DomainId.create()) {
        EventCommandHandler(events) {
            includeVitalEvents(vitals)
        }.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp, commandId))
    }

    private fun handleCombinedEvents(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS), commandId: CommandId = DomainId.create()) {
        EventCommandHandler(events) {
            includeVitalEvents(vitals)
        }.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp, commandId))
    }

    @Test
    fun testLogVitalCommand_IndividualEvent(){
        val vitalRowId = DomainId.create<EncounterVitalId>()
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        Assert.assertEquals(0, events.list.size)
        handleIndividualEvents(buildLogVitalCommand(timestamp){
            addVital(HR(1))
            addVital(SpO2(2))
        }, commandId = vitalRowId.copy(), timestamp = timestamp)
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals(vitalRowId, events.list[0].eventId.copy())
        Assert.assertEquals("Logged Vital: HR: 1; SpO2: 2", events.list[0].event)
        Assert.assertEquals(timestamp, events.list[0].timestamp)
        Assert.assertEquals(KnownEventTypes.VITALS.dataString, events.list[0].eventType)
    }

    @Test
    fun testLogVitalCommand_CombinedEvent(){
        val commandId = DomainId.create<EncounterVitalId>()
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)

        //Put a Vital into the vital list so the event includes the extra fields
        vitals += EncounterVital(
            commandId,
            Instant.now().truncatedTo(ChronoUnit.SECONDS)
        ) + Resp(3)
        Assert.assertEquals(0, events.list.size)
        handleCombinedEvents(buildLogVitalCommand(timestamp){
            addVital(HR(1))
            addVital(SpO2(2))
        }, commandId = commandId.copy())
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals(commandId, events.list[0].eventId.copy())
        Assert.assertEquals("Logged Vital: HR: 1; SpO2: 2", events.list[0].event)
        Assert.assertEquals(timestamp, events.list[0].timestamp)
    }

    @Test
    fun testUpdateVitalCommand_IndividualEvent(){
        val vitalRowId = DomainId.create<EncounterVitalId>()
        val commandId = DomainId.create<CommandId>()
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val commandTime = Instant.now().truncatedTo(ChronoUnit.SECONDS).plusSeconds(10)
        Assert.assertEquals(0, events.list.size)
        handleIndividualEvents(buildUpdateVitalCommand(vitalRowId, timestamp){
            addVital(HR(1))
            addVital(SpO2(2))
        }, commandId = commandId, timestamp = commandTime)
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals(commandId, events.list[0].eventId.copy())
        Assert.assertEquals("Updated Vital: HR: 1; SpO2: 2", events.list[0].event)
        Assert.assertEquals(timestamp, events.list[0].timestamp)
        Assert.assertEquals(KnownEventTypes.VITALS.dataString, events.list[0].eventType)
    }

    @Test
    fun testUpdateVitalCommand_CombinedEvent(){
        val vitalId = DomainId.create<EncounterVitalId>()
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val commandTime = Instant.now().truncatedTo(ChronoUnit.SECONDS).plusSeconds(10)

        //Put a Vital into the vital list so the event includes the extra fields
        vitals += EncounterVital(
            vitalId,
            Instant.now().truncatedTo(ChronoUnit.SECONDS)
        ) + Resp(3) + BloodPressure(4, 5)

        //Put the Event in to make sure it removes it
        events += Event("Event", vitalId.copy(), timestamp.minusSeconds(1000))

        Assert.assertEquals(1, events.list.size)
        handleCombinedEvents(buildUpdateVitalCommand(vitalId, timestamp){
            addVital(HR(1))
            addVital(SpO2(2))
            addVital(Resp(10))
        }, timestamp = commandTime)
        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals("Updated Vital: BP: 4/5; HR: 1; Resp: 10; SpO2: 2", events.list[1].event)
        Assert.assertEquals(timestamp, events.list[1].timestamp)
        Assert.assertEquals(vitalId, events.list[1].referencedItem)
    }

    @Test
    fun testRemoveVitalCommand(){
        val vitalId = DomainId.create<EncounterVitalId>()
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val commandTime = Instant.now().truncatedTo(ChronoUnit.SECONDS).plusSeconds(10)

        //Put a Vital into the vital list so the event includes the extra fields
        vitals += EncounterVital(
            vitalId,
            Instant.now().truncatedTo(ChronoUnit.SECONDS)
        ) + Resp(3) + BloodPressure(4, 5)

        //Put the Event in to make sure it removes it
        events += Event("Event", vitalId.copy(), timestamp)

        Assert.assertEquals(1, events.list.size)
        handleCombinedEvents(buildRemoveVitalCommand(vitalId), timestamp = commandTime)
        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals("Removed Vital: BP: 4/5; Resp: 3", events.list[1].event)
        Assert.assertEquals(commandTime, events.list[1].timestamp)
        Assert.assertEquals(vitalId, events.list[1].referencedItem)
    }
}