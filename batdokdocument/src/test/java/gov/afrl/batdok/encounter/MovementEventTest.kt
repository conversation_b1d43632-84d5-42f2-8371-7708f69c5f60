package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.commands.proto.changeNinelineTime
import gov.afrl.batdok.commands.proto.changeReadyDate
import gov.afrl.batdok.encounter.commands.*
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.encounter.movement.*
import gov.afrl.batdok.encounter.movement.Movement.Companion.includeMovementEvents
import gov.afrl.batdok.util.buildCommandData
import gov.afrl.batdok.util.int64Value
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Clock
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneOffset
import java.time.temporal.ChronoUnit

class MovementEventTest {
    private val events = Events()

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(
        ChronoUnit.SECONDS), commandId: CommandId = DomainId.create()) {
        EventCommandHandler(events) {
            includeMovementEvents()
        }.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp, commandId))
    }

    @Test
    fun testChangeMedMissionNumber() {
        val newMedMissionNumber = MedMissionNumber(otherText = "Med123")
        Assert.assertTrue(events.list.isEmpty())
        handle(buildMedMissionNumberCommand(newMedMissionNumber))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Set Med Mission Number to Med123", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.EVAC.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeMedMissionNumber_paren() {
        val newMedMissionNumber = MedMissionNumber("pt","Med123")
        Assert.assertTrue(events.list.isEmpty())
        handle(buildMedMissionNumberCommand(newMedMissionNumber))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Set Med Mission Number to (pt) Med123", events.list[0].event)
    }

    @Test
    fun testChangeMedMissionNumber_Clear() {
        Assert.assertTrue(events.list.isEmpty())
        handle(buildMedMissionNumberCommand(MedMissionNumber()))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Cleared Med Mission Number", events.list[0].event)
    }

    @Test
    fun testChangeTailToTail() {
        Assert.assertTrue(events.list.isEmpty())
        handle(buildChangeTailToTailCommand(true))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Patient evac was tail to tail", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.EVAC.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeTailToTail_Not() {
        Assert.assertTrue(events.list.isEmpty())
        handle(buildChangeTailToTailCommand(false))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Patient evac was not tail to tail", events.list[0].event)
    }

    @Test
    fun testChangeLegNumber() {
        val newLegNumber = 2
        val newLegTotal = 2
        Assert.assertTrue(events.list.isEmpty())
        handle(buildChangeLegNumberCommand(newLegNumber, newLegTotal))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Set leg # to 2 of 2", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.EVAC.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeLegNumber_Clear() {
        Assert.assertTrue(events.list.isEmpty())
        handle(buildChangeLegNumberCommand(null, null))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Cleared leg #", events.list[0].event)
    }

    @Test
    fun testChangeNinelineTime() {
        val newNinelineTime = Instant.now(Clock.fixed(Instant.ofEpochSecond(**********), ZoneOffset.UTC))
        Assert.assertTrue(events.list.isEmpty())
        handle(changeNinelineTime { time = int64Value(newNinelineTime.epochSecond) })
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Set 9-Line time to Jan 26, 13:20", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.EVAC.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeNinelineTime_Cleared() {
        Assert.assertTrue(events.list.isEmpty())
        handle(buildChangeNinelineTimeCommand(null))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Cleared 9-Line time", events.list[0].event)
    }

    @Test
    fun testChangeDispatchEvac() {
        val newEvacCategory = EvacStatus.CONVENIENCE
        Assert.assertTrue(events.list.isEmpty())
        handle(buildUpdateDispatchedEvacCommand(newEvacCategory))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Set Dispatched Evac to Convenience", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.EVAC.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeDispatchEvac_Clear() {
        Assert.assertTrue(events.list.isEmpty())
        handle(buildUpdateDispatchedEvacCommand(null))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Cleared Dispatched Evac", events.list[0].event)
    }

    @Test
    fun testChangeAssessedEvac(){
        val newEvacCategory = EvacStatus.ROUTINE
        Assert.assertTrue(events.list.isEmpty())
        handle(buildUpdateAssessedEvacCommand(newEvacCategory))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Set Assessed Evac to Routine", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.EVAC.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeAssessedEvac_Clear(){
        Assert.assertTrue(events.list.isEmpty())
        handle(buildUpdateAssessedEvacCommand(null))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Cleared Assessed Evac", events.list[0].event)
    }

    @Test
    fun testChangeNinelinePlatform() {
        val newNinelinePlatform = "PlatformA"
        Assert.assertTrue(events.list.isEmpty())
        handle(buildNinelinePlatformCommand(newNinelinePlatform))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Set Nineline Platform to PlatformA", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.EVAC.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeNinelinePlatform_Clear() {
        Assert.assertTrue(events.list.isEmpty())
        handle(buildNinelinePlatformCommand(null))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Cleared Nineline Platform", events.list[0].event)
    }

    @Test
    fun testChangeCapability() {
        val addedCapability = listOf("CapabilityA")
        val removedCapabilities = listOf("CapabilityB", "CapabilityC")
        Assert.assertTrue(events.list.isEmpty())
        handle(buildCapabilityCommand(addedCapability, removedCapabilities))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals(
            "Added Evac Capabilities: CapabilityA\n" +
                    "Removed Evac Capabilities: CapabilityB, CapabilityC",
            events.list[0].event
        )
        Assert.assertEquals(KnownEventTypes.EVAC.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeCapability_JustAdd() {
        val addedCapability = listOf("CapabilityA")
        Assert.assertTrue(events.list.isEmpty())
        handle(buildCapabilityCommand(addedCapability, listOf()))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals(
            "Added Evac Capabilities: CapabilityA",
            events.list[0].event
        )
    }

    @Test
    fun testChangeCapability_JustRemove() {
        val removedCapabilities = listOf("CapabilityB", "CapabilityC")
        Assert.assertTrue(events.list.isEmpty())
        handle(buildCapabilityCommand(listOf(), removedCapabilities))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals(
            "Removed Evac Capabilities: CapabilityB, CapabilityC",
            events.list[0].event
        )
    }

    @Test
    fun testChangeNinelineLocation_Pickup() {
        val isPickup = true
        val newNinelineLocation = NinelineLocation(
            time = Instant.ofEpochSecond(**********),
            location = "LocationA",
            region = "RegionA",
            role = "RoleA"
        )
        Assert.assertTrue(events.list.isEmpty())
        handle(buildNinelineLocationCommand(isPickup, newNinelineLocation))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Changed pickup location: Time: Jan 26, 18:20Z, Role: RoleA, Region: RegionA, Location: LocationA", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.EVAC.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeNinelineLocation_Dropoff() {
        val isPickup = false
        val newNinelineLocation = NinelineLocation(
            time = Instant.ofEpochSecond(**********),
            location = "LocationA",
            region = "RegionA",
            role = "RoleA"
        )
        Assert.assertTrue(events.list.isEmpty())
        handle(buildNinelineLocationCommand(isPickup, newNinelineLocation))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Changed dropoff location: Time: Jan 26, 18:20Z, Role: RoleA, Region: RegionA, Location: LocationA", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.EVAC.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeOriginatingMtf() {
        val newOriginatingMtf = "OriginatingA"
        Assert.assertTrue(events.list.isEmpty())
        handle(buildOriginatingMtfCommand(newOriginatingMtf))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Set Originating MTF to OriginatingA", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.EVAC.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeOriginatingMtf_Clear() {
        Assert.assertTrue(events.list.isEmpty())
        handle(buildOriginatingMtfCommand(null))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Cleared Originating MTF", events.list[0].event)
    }

    @Test
    fun testChangeDestinationMtf() {
        val newDestinationMtf = "DestinationA"
        Assert.assertTrue(events.list.isEmpty())
        handle(buildDestinationMtfCommand(newDestinationMtf))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Set Destination MTF to DestinationA", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.EVAC.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeDestinationMtf_Clear() {
        Assert.assertTrue(events.list.isEmpty())
        handle(buildDestinationMtfCommand(null))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Cleared Destination MTF", events.list[0].event)
    }

    @Test
    fun testChangeReasonRegulated() {
        val newReasonRegulated = ReasonRegulated.AF.dataString
        Assert.assertTrue(events.list.isEmpty())
        handle(buildReasonRegulatedCommand(newReasonRegulated))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Set Reason Regulated to $newReasonRegulated", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.EVAC.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeReasonRegulated_Clear() {
        Assert.assertTrue(events.list.isEmpty())
        handle(buildReasonRegulatedCommand(null))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Cleared Reason Regulated", events.list[0].event)
    }

    @Test
    fun testChangeMaxStops() {
        val newMaxStops = 2
        Assert.assertTrue(events.list.isEmpty())
        handle(buildMaxStopsCommand(newMaxStops))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Set Max Stops to 2", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.EVAC.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeMaxStops_Clear() {
        Assert.assertTrue(events.list.isEmpty())
        handle(buildMaxStopsCommand(null))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Cleared Max Stops", events.list[0].event)
    }

    @Deprecated("maxRons is deprecated, use maxNumOfRons.")
    @Test
    fun testChangeMaxNumOfStops() {
        Assert.assertTrue(events.list.isEmpty())
        (1..8).forEach{
            val newMaxStops = it.toString()
            handle(buildMaxNumberOfStopsCommand(newMaxStops))
        }
        var newMaxStops = "Unrestricted"
        handle(buildMaxNumberOfStopsCommand(newMaxStops))
        newMaxStops = "SomeMadeUpValue"
        handle(buildMaxNumberOfStopsCommand(newMaxStops))

        Assert.assertEquals(10, events.list.size)
        Assert.assertEquals("Set Max # of Stops to 1", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[0].eventType)
        Assert.assertEquals("Set Max # of Stops to 2", events.list[1].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[1].eventType)
        Assert.assertEquals("Set Max # of Stops to 3", events.list[2].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[2].eventType)
        Assert.assertEquals("Set Max # of Stops to 4", events.list[3].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[3].eventType)
        Assert.assertEquals("Set Max # of Stops to 5", events.list[4].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[4].eventType)
        Assert.assertEquals("Set Max # of Stops to 6", events.list[5].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[5].eventType)
        Assert.assertEquals("Set Max # of Stops to 7", events.list[6].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[6].eventType)
        Assert.assertEquals("Set Max # of Stops to 8", events.list[7].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[7].eventType)
        Assert.assertEquals("Set Max # of Stops to Unrestricted", events.list[8].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[8].eventType)
        Assert.assertEquals("Set Max # of Stops to SomeMadeUpValue", events.list[9].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[9].eventType)
    }
    @Test
    fun testChangeMaxNumOfStopsUnrestricted() {

        Assert.assertTrue(events.list.isEmpty())
        MaxNumOfStops.values().forEach {
            val newMaxStops = it
            handle(buildMaxNumberOfStopsCommand(newMaxStops))
        }
        Assert.assertEquals(9, events.list.size)
        Assert.assertEquals("Set Max # of Stops to 1", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[0].eventType)
        Assert.assertEquals("Set Max # of Stops to 2", events.list[1].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[1].eventType)
        Assert.assertEquals("Set Max # of Stops to 3", events.list[2].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[2].eventType)
        Assert.assertEquals("Set Max # of Stops to 4", events.list[3].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[3].eventType)
        Assert.assertEquals("Set Max # of Stops to 5", events.list[4].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[4].eventType)
        Assert.assertEquals("Set Max # of Stops to 6", events.list[5].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[5].eventType)
        Assert.assertEquals("Set Max # of Stops to 7", events.list[6].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[6].eventType)
        Assert.assertEquals("Set Max # of Stops to 8", events.list[7].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[7].eventType)
        Assert.assertEquals("Set Max # of Stops to Unrestricted", events.list[8].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[8].eventType)
    }

    @Test
    fun testChangeMaxNumOfStops_Clear() {
        Assert.assertTrue(events.list.isEmpty())
        handle(buildMaxNumberOfStopsCommand(null))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Cleared Max # of Stops", events.list[0].event)
    }
    @Test
    fun testChangeMaxRons() {
        val newMaxRons = 2
        Assert.assertTrue(events.list.isEmpty())
        handle(buildMaxRonsCommand(newMaxRons))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Set Max RONs to 2", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.EVAC.dataString, events.list[0].eventType)
    }

    @Deprecated("maxRons is deprecated, use maxNumOfRons.")
    @Test
    fun testChangeMaxRons_Clear() {
        Assert.assertTrue(events.list.isEmpty())
        handle(buildMaxRonsCommand(null))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Cleared Max RONs", events.list[0].event)
    }

    @Test
    fun testChangeMaxNumOfRons_String() {
        Assert.assertTrue(events.list.isEmpty())
        (0..8).forEach{
            val newMaxRons = it.toString()
            handle(buildMaxNumberOfRonsCommand(newMaxRons))
        }
        var newMaxStops = "Unrestricted"
        handle(buildMaxNumberOfRonsCommand(newMaxStops))
        newMaxStops = "SomeMadeUpValue"
        handle(buildMaxNumberOfRonsCommand(newMaxStops))

        Assert.assertEquals(11, events.list.size)
        Assert.assertEquals("Set Max RONS to 0", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[0].eventType)
        Assert.assertEquals("Set Max RONS to 1", events.list[1].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[1].eventType)
        Assert.assertEquals("Set Max RONS to 2", events.list[2].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[2].eventType)
        Assert.assertEquals("Set Max RONS to 3", events.list[3].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[3].eventType)
        Assert.assertEquals("Set Max RONS to 4", events.list[4].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[4].eventType)
        Assert.assertEquals("Set Max RONS to 5", events.list[5].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[5].eventType)
        Assert.assertEquals("Set Max RONS to 6", events.list[6].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[6].eventType)
        Assert.assertEquals("Set Max RONS to 7", events.list[7].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[7].eventType)
        Assert.assertEquals("Set Max RONS to 8", events.list[8].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[8].eventType)
        Assert.assertEquals("Set Max RONS to Unrestricted", events.list[9].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[9].eventType)
        Assert.assertEquals("Set Max RONS to SomeMadeUpValue", events.list[10].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[10].eventType)
    }
    @Test
    fun testChangeMaxNumOfRons_Enum() {
        Assert.assertTrue(events.list.isEmpty())
        MaxNumOfRons.values().forEach {
            val newMaxRons = it
            handle(buildMaxNumberOfRonsCommand(newMaxRons))
        }
        Assert.assertEquals(10, events.list.size)
        Assert.assertEquals("Set Max RONS to 0", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[0].eventType)
        Assert.assertEquals("Set Max RONS to 1", events.list[1].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[1].eventType)
        Assert.assertEquals("Set Max RONS to 2", events.list[2].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[2].eventType)
        Assert.assertEquals("Set Max RONS to 3", events.list[3].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[3].eventType)
        Assert.assertEquals("Set Max RONS to 4", events.list[4].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[4].eventType)
        Assert.assertEquals("Set Max RONS to 5", events.list[5].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[5].eventType)
        Assert.assertEquals("Set Max RONS to 6", events.list[6].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[6].eventType)
        Assert.assertEquals("Set Max RONS to 7", events.list[7].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[7].eventType)
        Assert.assertEquals("Set Max RONS to 8", events.list[8].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[8].eventType)
        Assert.assertEquals("Set Max RONS to Unrestricted", events.list[9].event)
        Assert.assertEquals(KnownEventTypes.MOVEMENT.dataString, events.list[9].eventType)
    }

    @Test
    fun testChangeMaxNumOfRons_Clear() {
        Assert.assertTrue(events.list.isEmpty())
        handle(buildMaxNumberOfRonsCommand(null))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Cleared Max RONS", events.list[0].event)
    }

    @Test
    fun testChangeAltitudeRestrictions() {
        val newAltitudeRestrictions = "AltitudeA"
        Assert.assertTrue(events.list.isEmpty())
        handle(buildAltitudeRestrictionsCommand(newAltitudeRestrictions))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Set Altitude Restrictions to AltitudeA", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.EVAC.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeAltitudeRestrictions_Clear() {
        Assert.assertTrue(events.list.isEmpty())
        handle(buildAltitudeRestrictionsCommand(null))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Cleared Altitude Restrictions", events.list[0].event)
    }

    @Test
    fun testChangeFlightLevel() {
        val newFlightLevel = "LevelA"
        Assert.assertTrue(events.list.isEmpty())
        handle(buildFlightLevelCommand(newFlightLevel))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Set Flight Level to LevelA", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.EVAC.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeFlightLevel_Clear() {
        Assert.assertTrue(events.list.isEmpty())
        handle(buildFlightLevelCommand(null))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Cleared Flight Level", events.list[0].event)
    }

    @Test
    fun testChangeReadyDate() {
        val newReadyDate = LocalDate.of(2024, 1, 26)
        Assert.assertTrue(events.list.isEmpty())
        handle(changeReadyDate { readyDate = int64Value(newReadyDate.toEpochDay()) })
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Set Ready Date to 01-26-2024", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.EVAC.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeReadyDate_Clear(){
        Assert.assertTrue(events.list.isEmpty())
        handle(buildUpdateReadyDateCommand(null))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Cleared Ready Date", events.list[0].event)
    }

    @Test
    fun testChangeMedicalAttendantsNeeded() {
        val newMedicalAttendantsNeeded = 3
        Assert.assertTrue(events.list.isEmpty())
        handle(buildChangeMedicalAttendantsNeededCommand(newMedicalAttendantsNeeded))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Set Medical Attendants Needed to $newMedicalAttendantsNeeded", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.EVAC.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeMedicalAttendantsNeeded_Clear() {
        Assert.assertTrue(events.list.isEmpty())
        handle(buildChangeMedicalAttendantsNeededCommand(null))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Cleared Medical Attendants Needed", events.list[0].event)
    }

    @Test
    fun testChangeNonMedicalAttendantsNeeded() {
        val newNonMedicalAttendantsNeeded = 3
        Assert.assertTrue(events.list.isEmpty())
        handle(buildChangeNonMedicalAttendantsNeededCommand(newNonMedicalAttendantsNeeded))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Set Non-Medical Attendants Needed to $newNonMedicalAttendantsNeeded", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.EVAC.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeNonMedicalAttendantsNeeded_Clear() {
        Assert.assertTrue(events.list.isEmpty())
        handle(buildChangeNonMedicalAttendantsNeededCommand(null))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Cleared Non-Medical Attendants Needed", events.list[0].event)
    }

    @Test
    fun testChangeAttendants_JustAdd() {
        val addedAttendants = listOf(
            Attendant(Name("John Middle Doe"),
                "Male",
                175.2f,
                "7th",
                true)
        )
        Assert.assertTrue(events.list.isEmpty())
        handle(buildAttendantCommand(addedAttendants, listOf()))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals(
            "Added Evac Attendants: John Middle Doe",
            events.list[0].event
        )
    }

    @Test
    fun testChangeAttendants_JustRemove() {
        val removedAttendants = listOf(
            Attendant(Name("John Middle Doe"),
                "Male",
                175.2f,
                "7th",
                true),
            Attendant(Name("Jane Middle Doe"),
                "Female",
                155.2f,
                "6th",
                false)
        )
        Assert.assertTrue(events.list.isEmpty())
        handle(buildAttendantCommand(listOf(), removedAttendants))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals(
            "Removed Evac Attendants: John Middle Doe, Jane Middle Doe",
            events.list[0].event
        )
    }

    @Test
    fun testChangeClassification_String() {
        val newClassification = "ClassA"
        Assert.assertTrue(events.list.isEmpty())
        handle(buildClassificationCommand(newClassification))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Set Classification to ClassA", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.EVAC.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeClassification_Enum() {
        val newClassification = Classification.FIVE_C
        Assert.assertTrue(events.list.isEmpty())
        handle(buildClassificationCommand(newClassification))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Set Classification to 5C - Psychiatric outpatient going for treatment or evaluation.", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.EVAC.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeClassification_Clear_String() {
        Assert.assertTrue(events.list.isEmpty())
        handle(buildClassificationCommand(null as String?))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Cleared Classification", events.list[0].event)
    }

    @Test
    fun testChangeClassification_Clear_Enum() {
        Assert.assertTrue(events.list.isEmpty())
        handle(buildClassificationCommand(null))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Cleared Classification", events.list[0].event)
    }

    @Test
    fun testChangePrecedence() {
        val newPrecedence = "PrecedenceA"
        Assert.assertTrue(events.list.isEmpty())
        handle(buildPrecedenceCommand(newPrecedence))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Set Precedence to PrecedenceA", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.EVAC.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangePrecedence_Clear() {
        Assert.assertTrue(events.list.isEmpty())
        handle(buildPrecedenceCommand(null))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Cleared Precedence", events.list[0].event)
    }

    @Test
    fun testChangeCriticalCare() {
        Assert.assertTrue(events.list.isEmpty())
        handle(buildCriticalCareCommand(true))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Patient is Critical Care", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.EVAC.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeCriticalCare_Not() {
        Assert.assertTrue(events.list.isEmpty())
        handle(buildCriticalCareCommand(false))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Patient is not Critical Care", events.list[0].event)
    }

    @Test
    fun testChangeCriticalCare_Null(){
        Assert.assertTrue(events.list.isEmpty())
        handle(buildCriticalCareCommand(null))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Cleared Critical Care", events.list[0].event)
    }

    @Test
    fun testChangeAcceptingPhysicianCommand_SetAllFields() {
        val physician = Physician(
            "John Doe",
            "1-************",
            "j.doe@unknown"
        )
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeAcceptingPhysicianCommand(physician))
        Assert.assertEquals("Set Accepting Physician to Name: ${physician.name} " +
                "Phone: ${physician.phone} " +
                "Email: ${physician.email}",
            events.list[0].event)
    }

    @Test
    fun testChangeAcceptingPhysicianCommand_SetSomeFieldsEmptyOrBlank() {
        val physician = Physician(
            "     ",
            "",
            "j.doe@unknown"
        )
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeAcceptingPhysicianCommand(physician))
        Assert.assertEquals("Set Accepting Physician to " +
                "Email: ${physician.email}",
            events.list[0].event)
    }
    @Test
    fun testChangeAcceptingPhysicianCommand_Clear() {
        val physician = Physician()
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeAcceptingPhysicianCommand(physician))
        Assert.assertEquals("Cleared Accepting Physician", events.list[0].event)
    }

    @Test
    fun testChangeOriginPhoneCommand_notBlank() {
        val expected = "**************"
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeOriginPhoneCommand(expected))
        Assert.assertEquals("Set Origin Phone to $expected", events.list[0].event)
    }

    @Test
    fun testChangeOriginPhoneCommand_isBlank() {
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeOriginPhoneCommand("     "))
        Assert.assertEquals("Cleared Origin Phone", events.list[0].event)
    }

    @Test
    fun testChangeOriginPhoneCommand_isNull() {
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeOriginPhoneCommand(null))
        Assert.assertEquals("Cleared Origin Phone", events.list[0].event)
    }

    @Test
    fun testChangeOriginPhoneNumbersCommand_DSNAndComm() {
        val expectedComm = "**************"
        val expectedDSN = "123-4567"
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeOriginPhoneNumbersCommand(PhoneNumbers(dsn = expectedDSN, comm = expectedComm)))
        Assert.assertEquals("Set Origin Phone to DSN: $expectedDSN, Comm: $expectedComm", events.list[0].event)

    }

    @Test
    fun testChangeOriginPhoneNumbersCommand_DSNOnly() {
        val expectedDSN = "123-4567"
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeOriginPhoneNumbersCommand(PhoneNumbers(dsn = expectedDSN)))
        Assert.assertEquals("Set Origin Phone to DSN: $expectedDSN", events.list[0].event)
    }

    @Test
    fun testChangeOriginPhoneNumbersCommand_CommOnly() {
        val expectedComm = "**************"
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeOriginPhoneNumbersCommand(PhoneNumbers(comm = expectedComm)))
        Assert.assertEquals("Set Origin Phone to Comm: $expectedComm", events.list[0].event)
    }

    @Test
    fun testChangeOriginPhoneNumbersCommand_FieldsAreBlank() {
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeOriginPhoneNumbersCommand(PhoneNumbers(dsn = "     ", comm = "")))
        Assert.assertEquals("Cleared Origin Phone", events.list[0].event)
    }

    @Test
    fun testChangeOriginPhoneNumbersCommand_FieldsAreNull() {
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeOriginPhoneNumbersCommand(PhoneNumbers()))
        Assert.assertEquals("Cleared Origin Phone", events.list[0].event)
    }

    @Test
    fun testChangeDestinationPhoneCommand_notBlank() {
        val expected = "**************"
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeDestinationPhoneCommand(expected))
        Assert.assertEquals("Set Destination Phone to $expected", events.list[0].event)
    }

    @Test
    fun testChangeDestinationPhoneCommand_isBlank() {
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeDestinationPhoneCommand("     "))
        Assert.assertEquals("Cleared Destination Phone", events.list[0].event)
    }

    @Test
    fun testChangeDestinationPhoneCommand_isNull() {
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeDestinationPhoneCommand(null))
        Assert.assertEquals("Cleared Destination Phone", events.list[0].event)
    }

    @Test
    fun testChangeDestinationPhoneNumbersCommand_DSNAndComm() {
        val expectedComm = "**************"
        val expectedDSN = "123-4567"
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeDestinationPhoneNumbersCommand(PhoneNumbers(dsn = expectedDSN, comm = expectedComm)))
        Assert.assertEquals("Set Destination Phone to DSN: $expectedDSN, Comm: $expectedComm", events.list[0].event)

    }

    @Test
    fun testChangeDestinationPhoneNumbersCommand_DSNOnly() {
        val expectedDSN = "123-4567"
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeDestinationPhoneNumbersCommand(PhoneNumbers(dsn = expectedDSN)))
        Assert.assertEquals("Set Destination Phone to DSN: $expectedDSN", events.list[0].event)
    }

    @Test
    fun testChangeDestinationPhoneNumbersCommand_CommOnly() {
        val expectedComm = "**************"
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeDestinationPhoneNumbersCommand(PhoneNumbers(comm = expectedComm)))
        Assert.assertEquals("Set Destination Phone to Comm: $expectedComm", events.list[0].event)
    }

    @Test
    fun testChangeDestinationPhoneNumbersCommand_FieldsAreBlank() {
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeDestinationPhoneNumbersCommand(PhoneNumbers(dsn = "     ", comm = "")))
        Assert.assertEquals("Cleared Destination Phone", events.list[0].event)
    }

    @Test
    fun testChangeDestinationPhoneNumbersCommand_FieldsAreNull() {
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeDestinationPhoneNumbersCommand(PhoneNumbers()))
        Assert.assertEquals("Cleared Destination Phone", events.list[0].event)
    }

    @Test
    fun testAddWaivers() {
        val waivers = listOf("Waiver1", "Waiver2", "Waiver3")
        Assert.assertEquals(0, events.list.size)
        handle(buildAddRemoveWaiversCommand(waivers, listOf()))
        Assert.assertEquals("Added Waivers: ${waivers.joinToString(", ")}", events.list[0].event)
    }

    @Test
    fun testRemoveWaivers() {
        val waivers = listOf("Waiver1", "Waiver2", "Waiver3")
        Assert.assertEquals(0, events.list.size)
        handle(buildAddRemoveWaiversCommand(listOf(), waivers))
        Assert.assertEquals("Removed Waivers: ${waivers.joinToString(", ")}", events.list[0].event)
    }

    @Test
    fun testAddAndRemoveWaivers() {
        val waivers = listOf("Waiver1", "Waiver2", "Waiver3")
        val waiversToRemove = listOf("Waiver4")
        Assert.assertEquals(0, events.list.size)
        handle(buildAddRemoveWaiversCommand(waivers, waiversToRemove))
        Assert.assertEquals("Added Waivers: ${waivers.joinToString(", ")}\n" +
                "Removed Waivers: ${waiversToRemove.joinToString(", ")}",
            events.list[0].event)
    }

    @Test
    fun testAddAndRemoveWaivers_emptyLists() {
        val waivers = listOf<String>()
        val waiversToRemove = listOf<String>()
        Assert.assertEquals(0, events.list.size)
        handle(buildAddRemoveWaiversCommand(waivers, waiversToRemove))
        Assert.assertEquals(0, events.list.size)
    }

    @Test
    fun testChangeWaiversCommand_NotBlank() {
        val expected = "some waivers"
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeWaiversCommand(expected))
        Assert.assertEquals("Set Waivers to $expected", events.list[0].event)
    }

    @Test
    fun testChangeWaiversCommand_IsBlank() {
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeWaiversCommand("     "))
        Assert.assertEquals("Cleared Waivers", events.list[0].event)
    }

    @Test
    fun testChangeWaiversCommand_IsNull() {
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeWaiversCommand(null))
        Assert.assertEquals("Cleared Waivers", events.list[0].event)
    }

    @Test
    fun testAddAmbulatoryOrLitter(){
        Assert.assertEquals(0, events.list.size)
        handle(buildAmbulatoryOrLitterCommand("Litter"))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Set Ambulatory or Litter to Litter", events.list[0].event)
    }

    @Test
    fun testAddAmbulatoryOrLitter_toNull(){
        Assert.assertEquals(0, events.list.size)
        handle(buildAmbulatoryOrLitterCommand(null))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Cleared Ambulatory or Litter", events.list[0].event)
    }

    @Test
    fun testClearPickupLocationCommand() {
        Assert.assertEquals(0, events.list.size)
        handle(buildClearPickupLocationCommand())
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Cleared Pickup Location", events.list[0].event)
    }

    @Test
    fun testClearDropoffLocationCommand() {
        Assert.assertEquals(0, events.list.size)
        handle(buildClearDropoffLocationCommand())
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Cleared Drop-off Location", events.list[0].event)
    }

}