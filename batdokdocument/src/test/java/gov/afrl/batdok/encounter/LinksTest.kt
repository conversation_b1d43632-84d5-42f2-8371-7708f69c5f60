package gov.afrl.batdok.encounter

import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test

class LinksTest {

    lateinit var classUnderTest: Links

    @Before
    fun setUp() {
        classUnderTest = Links()
    }

    @Test
    fun getLinks_NoValuesForId() {
        //ARRANGE
        val id: DomainId = DomainId.create()
        val otherId1: DomainId = DomainId.create()
        val otherId2: DomainId = DomainId.create()
        val otherId3: DomainId = DomainId.create()
        val link1 = Link(listOf(otherId1, otherId2, otherId3))
        val link2 = Link(listOf(otherId1, otherId3))
        val link3 = Link(listOf(otherId1, otherId2))
        classUnderTest += listOf(link1, link2, link3)

        //ACT
        val result = classUnderTest.getLinks(id)

        //ASSERT
        assertTrue(result.isEmpty())
    }

    @Test
    fun getLinks_ValueForId() {
        //ARRANGE
        val id: DomainId = DomainId.create()
        val otherId1: DomainId = DomainId.create()
        val otherId2: DomainId = DomainId.create()
        val otherId3: DomainId = DomainId.create()
        val link1 = Link(listOf(otherId1, otherId2, otherId3, id))
        val link2 = Link(listOf(otherId1, otherId3))
        val link3 = Link(listOf(otherId1, otherId2, id))
        classUnderTest += listOf(link1, link2, link3)

        //ACT
        val result = classUnderTest.getLinks(id)

        //ASSERT
        assertEquals(2, result.size)
        assertTrue(result.contains(link1))
        assertTrue(result.contains(link3))
    }

    @Test
    fun getLinks_containingId() {
        //ARRANGE
        val id: DomainId = DomainId.create()
        val otherId1: DomainId = DomainId.create()
        val otherId2: DomainId = DomainId.create()
        val otherId3: DomainId = DomainId.create()
        val link1 = Link(listOf(otherId1, otherId2, otherId3, id))
        val link2 = Link(listOf(otherId1, otherId3))
        val link3 = Link(listOf(otherId1, otherId2))
        classUnderTest += listOf(link1, link2, link3)

        //ACT
        val result = classUnderTest.getLinks(link1.id)

        //ASSERT
        assertEquals(1, result.size)
        assertEquals(link1, result[0])
    }

    @Test
    fun testRemoveLinkedItem_LargeLink(){
        val id: DomainId = DomainId.create()
        val otherId1: DomainId = DomainId.create()
        val otherId2: DomainId = DomainId.create()
        val otherId3: DomainId = DomainId.create()
        val link = Link(listOf(otherId1, otherId2, otherId3, id))
        classUnderTest += listOf(link)

        classUnderTest.removeLinkedItem(otherId2)

        Assert.assertEquals(1, classUnderTest.list.size)

        val updatedLink = classUnderTest[link.id]
        Assert.assertEquals(3, updatedLink?.ids?.size)
        Assert.assertFalse(otherId2 in updatedLink!!.ids)
    }

    @Test
    fun testRemoveLinkedItem_SmallLink(){
        val otherId1: DomainId = DomainId.create()
        val otherId2: DomainId = DomainId.create()
        val link = Link(listOf(otherId1, otherId2))

        classUnderTest += listOf(link)

        classUnderTest.removeLinkedItem(otherId2)

        //Link gets removed because there's only one other item
        Assert.assertEquals(0, classUnderTest.list.size)
        Assert.assertFalse(link.id in classUnderTest)
    }
}