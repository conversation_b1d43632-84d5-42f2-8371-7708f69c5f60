package gov.afrl.batdok.encounter.treatments

import com.google.protobuf.Message
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.Events
import gov.afrl.batdok.encounter.KnownEventTypes
import gov.afrl.batdok.encounter.commands.addTreatmentEventCommandHandler
import gov.afrl.batdok.encounter.commands.buildAddTreatmentCommand
import gov.afrl.batdok.encounter.treatment.*
import gov.afrl.batdok.encounter.treatment.TubeData.BreathingConfirmation
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class AddTreatmentEventTest {

    val events = Events()

    //region Helper functions
    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS)) {
        EventCommandHandler(events) {
            +addTreatmentEventCommandHandler()
        }.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp))
    }

    private fun testTreatment(name: String, eventString: String, data: TreatmentData? = null){
        Assert.assertEquals(0, events.list.size)
        handle(buildAddTreatmentCommand(name, data))
        Assert.assertEquals(eventString, events.list[0].event)
        Assert.assertEquals(KnownEventTypes.TREAT.dataString, events.list[0].eventType)
    }

    private fun testTreatment(treatment: CommonTreatments, data: TreatmentData, eventString: String) =
        testTreatment(treatment.dataString, eventString, data)

    private fun testTreatment(treatment: CommonTreatments, eventString: String) =
        testTreatment(treatment.dataString, eventString)
    //endregion

    @Test
    fun testAddTreatmentCommand_TQWithExtras() = testTreatment(
        CommonTreatments.TQ,
        TqData(
            TqData.Location.EXTREMITY.dataString,
            tqType = "CAT5",
            subLocation = "LUE",
            reapplicationTime = Instant.ofEpochSecond(1706274658),
            conversionTime = Instant.ofEpochSecond(1706278658),
            removalTime = Instant.ofEpochSecond(1706282658),
            reassessTime = Instant.ofEpochSecond(1706286658),
        ),
        "Added Treatment TQ: Type: CAT5, Location: Extremity, Sub-Location: LUE, Reapplication: 13:10Z, Conversion: 14:17Z, Removal: 15:24Z, Reassess: 16:30Z"
    )

    @Test
    fun testAddTreatmentCommand_TQWithoutExtras() = testTreatment(
        CommonTreatments.TQ,
        "Added Treatment TQ"
    )

    @Test
    fun testAddTreatmentCommand_DressingWithExtras() = testTreatment(
        CommonTreatments.DRESSING,
        DressingData(DressingData.Type.ABDOMINAL.dataString, null, null),
        "Added Treatment Dressing: Type: Abdominal"
    )

    @Test
    fun testAddTreatmentCommand_DressingWithoutExtras() = testTreatment(
        CommonTreatments.DRESSING,
        "Added Treatment Dressing"
    )

    @Test
    fun testAddTreatmentCommand_ETTubeWithExtras() = testTreatment(
        CommonTreatments.ET_TUBE,
        TubeData("Confirm", 10f, "G", null,  20, "mm", "Location"),
        "Added Treatment ET-Tube: Location: Location, Confirmation: Confirm, Depth: 20 mm, Size: 10 G"
    )

    @Test
    fun testAddTreatmentCommand_ETTubeWithoutExtras() = testTreatment(
        CommonTreatments.ET_TUBE,
        "Added Treatment ET-Tube"
    )

    @Test
    fun testAddTreatmentCommand_NeedleDWithoutExtras() = testTreatment(
        CommonTreatments.NEEDLE_D,
        "Added Treatment Needle-D"
    )

    @Test
    fun testAddTreatmentCommand_NeedleDWithExtras() = testTreatment(
        CommonTreatments.NEEDLE_D,
        NeedleDData(NeedleDData.Location.L_MID_CLAV.dataString),
        "Added Treatment Needle-D: L Mid-Clav"
    )
    @Test
    fun testAddTreatmentCommand_FingerThorWithoutExtras() = testTreatment(
        CommonTreatments.FINGER_THOR,
        "Added Treatment Finger Thor"
    )

    @Test
    fun testAddTreatmentCommand_FingerThorWithExtras() = testTreatment(
        CommonTreatments.FINGER_THOR,
        FingerThorData(FingerThorData.Location.LEFT.dataString),
        "Added Treatment Finger Thor: Left side"
    )

    @Test
    fun testAddTreatmentCommand_ChestTubeWithoutExtras() = testTreatment(
        CommonTreatments.CHEST_TUBE,
        "Added Treatment Chest Tube"
    )
    @Test
    fun testAddTreatmentCommand_ChestTubeWithExtras() = testTreatment(
        CommonTreatments.CHEST_TUBE,
        ChestTubeData(
            location = ChestTubeData.Location.LEFT.dataString,
            suction = true,
            suctionAmount = 1.4f,
            airLeak = true
        ),
        "Added Treatment Chest Tube: Location: Left side, Suction: Yes, Amount: 1.4, Air Leak: Yes"
    )
    @Test
    fun testAddTreatmentCommand_ChestTubeWithExtras_falseValues() = testTreatment(
        CommonTreatments.CHEST_TUBE,
        ChestTubeData(
            location = ChestTubeData.Location.LEFT.dataString,
            suction = false,
            suctionAmount = 1.4f,
            airLeak = false
        ),
        "Added Treatment Chest Tube: Location: Left side, Suction: No, Amount: 1.4, Air Leak: No"
    )

    @Test
    fun testAddTreatmentCommand_ChestSealWithoutExtras() = testTreatment(
        CommonTreatments.CHEST_SEAL,
        "Added Treatment Chest Seal"
    )

    @Test
    fun testAddTreatmentCommand_ChestSealWithExtras() = testTreatment(
        CommonTreatments.CHEST_SEAL,
        ChestSealData(ChestSealData.Location.LEFT_FRONT.dataString),
        "Added Treatment Chest Seal: Left Front"
    )
    @Test
    fun testAddTreatmentCommand_EyeShieldWithoutExtras() = testTreatment(
        CommonTreatments.EYE_SHIELD,
        "Added Treatment Eye Shield"
    )

    @Test
    fun testAddTreatmentCommand_EyeShieldWithExtras_NeitherEye() = testTreatment(
        CommonTreatments.EYE_SHIELD,
        EyeShieldData(false, false),
        "Added Treatment Eye Shield"
    )

    @Test
    fun testAddTreatmentCommand_EyeShieldWithExtras_LeftEye() = testTreatment(
        CommonTreatments.EYE_SHIELD,
        EyeShieldData(true, false),
        "Added Treatment Eye Shield: Left eye"
    )

    @Test
    fun testAddTreatmentCommand_EyeShieldWithExtras_RightEye() = testTreatment(
        CommonTreatments.EYE_SHIELD,
        EyeShieldData(false, true),
        "Added Treatment Eye Shield: Right eye"
    )

    @Test
    fun testAddTreatmentCommand_EyeShieldWithExtras_BothEyes() = testTreatment(
        CommonTreatments.EYE_SHIELD,
        EyeShieldData(true, true),
        "Added Treatment Eye Shield: Both eyes"
    )
    @Test
    fun testAddTreatmentCommand_SplintWithoutExtras() = testTreatment(
        CommonTreatments.SPLINT,
        "Added Treatment Splint"
    )

    @Test
    fun testAddTreatmentCommand_SplintWithExtras_Present() = testTreatment(
        CommonTreatments.SPLINT,
        SplintData(true),
        "Added Treatment Splint: Pulse present"
    )

    @Test
    fun testAddTreatmentCommand_SplintWithExtras_NotPresent() = testTreatment(
        CommonTreatments.SPLINT,
        SplintData(false),
        "Added Treatment Splint: Pulse not present"
    )
    @Test
    fun testAddTreatmentCommand_SplintWithExtras_Type() = testTreatment(
        CommonTreatments.SPLINT,
        SplintData(null, "Type"),
        "Added Treatment Splint: Type"
    )
    @Test
    fun testAddTreatmentCommand_SplintWithExtras_TypeAndPresent() = testTreatment(
        CommonTreatments.SPLINT,
        SplintData(true, "Type"),
        "Added Treatment Splint: Type, Pulse present"
    )
    @Test
    fun testAddTreatmentCommand_O2WithoutExtras() = testTreatment(
        CommonTreatments.O2,
        "Added Treatment O2"
    )

    @Test
    @Deprecated("Remove when O2Data gets rid of deprecated fields")
    fun testAddTreatmentCommand_O2WithExtras_Old() = testTreatment(
        CommonTreatments.O2,
        O2Data(10f, O2Data.DeliveryMethod.NRB.dataString),
        "Added Treatment O2: Source: NRB, LPM: 10.0"
    )

    @Test
    fun testAddTreatmentCommand_O2WithExtras() = testTreatment(
        CommonTreatments.O2,
        O2Data(lpm = 10f, targetSpO2 = 98, fiO2 = 13f, route = O2Data.O2Route.VENTURI_MASK.dataString),
        "Added Treatment O2: LPM: 10.0, Target SPO2: 98, FIO2: 13.0, Route: Venturi Mask"
    )
    @Test
    fun testAddTreatmentCommand_CricWithoutExtras() = testTreatment(
        CommonTreatments.CRIC,
        "Added Treatment CRIC"
    )

    @Test
    fun testAddTreatmentCommand_CricWithExtras() = testTreatment(
        CommonTreatments.CRIC,
        TubeData(size = 10f, breathingConfirmation = BreathingConfirmation.BREATH_SOUNDS.dataString, sizeUnit = "mm"),
        "Added Treatment CRIC: Confirmation: Breath Sounds, Size: 10 mm"
    )
    @Test
    fun testAddTreatmentCommand_EscharatomyWithoutExtras() = testTreatment(
        CommonTreatments.ESCHARATOMY,
        "Added Treatment Escharatomy"
    )

    @Test
    fun testAddTreatmentCommand_EscharatomyWithExtras() = testTreatment(
        CommonTreatments.ESCHARATOMY,
        EscharatomyData("Free text location"),
        "Added Treatment Escharatomy: Free text location"
    )
    @Test
    fun testAddTreatmentCommand_DebridementWithoutExtras() = testTreatment(
        CommonTreatments.DEBRIDEMENT,
        "Added Treatment Debridement"
    )

    @Test
    fun testAddTreatmentCommand_DebridementWithExtras() = testTreatment(
        CommonTreatments.DEBRIDEMENT,
        DebridementData("Free text location"),
        "Added Treatment Debridement: Free text location"
    )
    @Test
    fun testAddTreatmentCommand_GastricTubeWithoutExtras() = testTreatment(
        CommonTreatments.GASTRIC_TUBE,
        "Added Treatment Gastric Tube"
    )

    @Test
    fun testAddTreatmentCommand_GastricTubeWithExtras() = testTreatment(
        CommonTreatments.GASTRIC_TUBE,
        GastricTubeData(
            GastricTubeData.Type.NASAL.dataString,
            GastricTubeData.Side.LEFT.dataString,
            GastricTubeData.SuctionType.SUCTION.dataString,
            GastricTubeData.Interval.CONTINUOUS.dataString
        ),
        "Added Treatment Gastric Tube: Type: Nasal, Side: Left side, Suction Type: Suction, Interval: Continuous"
    )
    @Test
    fun testAddTreatmentCommand_LateralCanthotomyWithoutExtras() = testTreatment(
        CommonTreatments.LATERAL_CANTHOTOMY,
        "Added Treatment Lateral Canthotomy"
    )

    @Test
    fun testAddTreatmentCommand_LateralCanthotomyWithExtras() = testTreatment(
        CommonTreatments.LATERAL_CANTHOTOMY,
        LateralCanthotomyData(LateralCanthotomyData.Location.LEFT.dataString),
        "Added Treatment Lateral Canthotomy: Left side"
    )
    @Test
    fun testAddTreatmentCommand_FasciotomyWithoutExtras() = testTreatment(
        CommonTreatments.FASCIOTOMY,
        "Added Treatment Fasciotomy"
    )

    @Test
    fun testAddTreatmentCommand_FasciotomyWithExtras() = testTreatment(
        CommonTreatments.FASCIOTOMY,
        FasciotomyData("Free text Location"),
        "Added Treatment Fasciotomy: Free text Location"
    )
    @Test
    fun testAddTreatmentCommand_FoleyCatheterWithoutExtras() = testTreatment(
        CommonTreatments.FOLEY_CATHETER,
        "Added Treatment Foley Catheter"
    )

    @Test
    fun testAddTreatmentCommand_FoleyCatheterWithExtras() = testTreatment(
        CommonTreatments.FOLEY_CATHETER,
        FoleyCatheterData(
            10f,
            "G",
            FoleyCatheterData.Color.BLUE.dataString,
            "Character",
            "Assess"
        ),
        "Added Treatment Foley Catheter: Size: 10 G, Type: Blue, Character: Character, Assess: Assess"
    )
    @Test
    fun testAddTreatmentCommand_DirectPressureWithoutExtras() = testTreatment(
        CommonTreatments.DIRECT_PRESSURE,
        "Added Treatment Direct Pressure"
    )

    @Test
    fun testAddTreatmentCommand_DirectPressureWithExtras() = testTreatment(
        CommonTreatments.DIRECT_PRESSURE,
        DirectPressureData("Free text Location"),
        "Added Treatment Direct Pressure: Free text Location"
    )
    @Test
    fun testAddTreatmentCommand_ForeignBodyRemovalWithoutExtras() = testTreatment(
        CommonTreatments.FOREIGN_BODY_REMOVAL,
        "Added Treatment Foreign Body Removal"
    )

    @Test
    fun testAddTreatmentCommand_ForeignBodyRemovalWithExtras() = testTreatment(
        CommonTreatments.FOREIGN_BODY_REMOVAL,
        ForeignBodyRemovalData("Free text Location"),
        "Added Treatment Foreign Body Removal: Free text Location"
    )
    @Test
    fun testAddTreatmentCommand_NPAWithoutExtras() = testTreatment(
        CommonTreatments.NPA,
        "Added Treatment NPA"
    )

    @Test
    fun testAddTreatmentCommand_NPAWithExtras() = testTreatment(
        CommonTreatments.NPA,
        TubeData(BreathingConfirmation.BREATH_SOUNDS.dataString, 10f, "cm"),
        "Added Treatment NPA: Confirmation: Breath Sounds, Size: 10 cm"
    )
    @Test
    fun testAddTreatmentCommand_OPAWithoutExtras() = testTreatment(
        CommonTreatments.OPA,
        "Added Treatment OPA"
    )

    @Test
    fun testAddTreatmentCommand_OPAWithExtras() = testTreatment(
        CommonTreatments.OPA,
        TubeData(BreathingConfirmation.BREATH_SOUNDS.dataString, 10f, "mm"),
        "Added Treatment OPA: Confirmation: Breath Sounds, Size: 10 mm"
    )
    @Test
    fun testAddTreatmentCommand_SGAWithoutExtras() = testTreatment(
        CommonTreatments.SGA,
        "Added Treatment SGA"
    )

    @Test
    fun testAddTreatmentCommand_SGAWithExtras() = testTreatment(
        CommonTreatments.SGA,
        TubeData(BreathingConfirmation.BREATH_SOUNDS.dataString, 10f, "G", "SGA Type"),
        "Added Treatment SGA: Type: SGA Type, Confirmation: Breath Sounds, Size: 10 G"
    )
    @Test
    fun testAddTreatmentCommand_TrachWithoutExtras() = testTreatment(
        CommonTreatments.TRACH,
        "Added Treatment Trach"
    )

    @Test
    fun testAddTreatmentCommand_TrachWithExtras() = testTreatment(
        CommonTreatments.TRACH,
        TubeData(BreathingConfirmation.BREATH_SOUNDS.dataString, 10f, "mm"),
        "Added Treatment Trach: Confirmation: Breath Sounds, Size: 10 mm"
    )
    @Test
    fun testAddTreatmentCommand_HypothermiaPreventionWithoutExtras() = testTreatment(
        CommonTreatments.HYPOTHERMIA_PREVENTION,
        "Added Treatment Hypothermia Prevention"
    )

    @Test
    fun testAddTreatmentCommand_HypothermiaPreventionWithExtras() = testTreatment(
        CommonTreatments.HYPOTHERMIA_PREVENTION,
        HypothermiaPreventionData(HypothermiaPreventionData.Type.BLANKET.dataString),
        "Added Treatment Hypothermia Prevention: Blanket"
    )
    @Test
    fun testAddTreatmentCommand_ImmobilizationWithoutExtras() = testTreatment(
        CommonTreatments.IMMOBILIZATION,
        "Added Treatment Immobilization"
    )

    @Test
    fun testAddTreatmentCommand_ImmobilizationWithExtras() = testTreatment(
        CommonTreatments.IMMOBILIZATION,
        ImmobilizationData(ImmobilizationData.Type.C_COLLAR.dataString),
        "Added Treatment Immobilization: C-Collar"
    )

    @Test
    fun testAddTreatmentCommand_ImmobilizationOther() = testTreatment(
        CommonTreatments.IMMOBILIZATION,
        ImmobilizationData(
            type = ImmobilizationData.Type.SWATH.dataString,
            location = ImmobilizationData.Location.RIGHT_UPPER_ARM.dataString,
            neurovascularBefore = NeurovascularStatus(
                pulse = ImmobilizationData.TernaryStatus.YES,
                motor = ImmobilizationData.TernaryStatus.NO,
                sensory = ImmobilizationData.TernaryStatus.UNKNOWN
            ),
            neurovascularAfter = NeurovascularStatus(
                pulse = ImmobilizationData.TernaryStatus.YES,
                motor = ImmobilizationData.TernaryStatus.YES,
                sensory = ImmobilizationData.TernaryStatus.YES
            )
        ),
        "Added Treatment Immobilization: Swath, Right Upper Arm, Neuro Before - Pulse: Yes, Motor: No, Sensory: Unknown, Neuro After - Pulse: Yes, Motor: Yes, Sensory: Yes"
    )
    
    @Test
    fun testAddTreatmentCommand_SuctionWithoutExtras() = testTreatment(
        CommonTreatments.SUCTION,
        "Added Treatment Suction"
    )

    @Test
    fun testAddTreatmentCommand_SuctionWithExtras() = testTreatment(
        CommonTreatments.SUCTION,
        SuctionData(
            SuctionData.Tool.YANKAUER.dataString,
            "Free Text Location"
        ),
        "Added Treatment Suction: Yankauer"
    )
    @Test
    fun testAddTreatmentCommand_IntubatedByWithoutExtras() = testTreatment(
        CommonTreatments.INTUBATED_BY,
        "Added Treatment Intubated By"
    )

    @Test
    fun testAddTreatmentCommand_IntubatedByWithExtras() = testTreatment(
        CommonTreatments.INTUBATED_BY,
        IntubatedByData("Intubater"),
        "Added Treatment Intubated By: Intubater"
    )

    @Test
    fun testPericardiocentesisTreatmentWithoutExtras() = testTreatment(
        CommonTreatments.PERICARDIOCENTESIS,
        "Added Treatment Pericardiocentesis"
    )

    @Test
    fun testPericardiocentesisTreatmentWithExtras() = testTreatment(
        CommonTreatments.PERICARDIOCENTESIS,
        PericardiocentesisData(200f, "ml"),
        "Added Treatment Pericardiocentesis: Volume: 200 ml"
    )

    @Test
    fun testAddTreatmentCommand_PelvicBinder() = testTreatment(
        CommonTreatments.PELVIC_BINDER,
        PelvicBinderData("Type"),
        "Added Treatment Pelvic Binder: Type"
    )
    @Test
    fun testAddTreatmentCommand_PelvicBinderNoType() = testTreatment(
        CommonTreatments.PELVIC_BINDER,
        PelvicBinderData(),
        "Added Treatment Pelvic Binder"
    )

    @Test
    fun testAddTreatmentCommand_WoundPackingWithoutExtras() = testTreatment(
        CommonTreatments.WOUND_PACKING,
        "Added Treatment Wound Packing"
    )

    @Test
    fun testAddTreatmentCommand_WoundPackingWithExtras() = testTreatment(
        CommonTreatments.WOUND_PACKING,
        WoundPackingData("Location", "Type"),
        "Added Treatment Wound Packing: Type: Type, Location: Location"
    )
    @Test
    fun testAddTreatmentCommand_WoundPackingWithExtras_NoType() = testTreatment(
        CommonTreatments.WOUND_PACKING,
        WoundPackingData("Location", null),
        "Added Treatment Wound Packing: Location: Location"
    )
}