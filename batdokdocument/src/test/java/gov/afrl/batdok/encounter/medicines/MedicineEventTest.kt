package gov.afrl.batdok.encounter.medicines

import com.google.protobuf.Message
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.Event
import gov.afrl.batdok.encounter.Events
import gov.afrl.batdok.encounter.KnownEventTypes
import gov.afrl.batdok.encounter.commands.buildLogMedWithIdCommand
import gov.afrl.batdok.encounter.commands.buildLogMedicineCommand
import gov.afrl.batdok.encounter.commands.buildRemoveMedTreatmentCommand
import gov.afrl.batdok.encounter.commands.buildUpdateMedicineCommand
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.encounter.ids.EventId
import gov.afrl.batdok.encounter.ids.MedicineId
import gov.afrl.batdok.encounter.medicine.Medicine
import gov.afrl.batdok.encounter.medicine.Medicines
import gov.afrl.batdok.encounter.medicine.Medicines.Companion.includeMedicineEvents
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class MedicineEventTest {

    val events = Events()
    val medicines = Medicines()

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now(), commandId: CommandId = DomainId.create()) {
        EventCommandHandler(events) {
            includeMedicineEvents(medicines)
        }.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp, commandId))
    }

    @Test
    fun testLogMedicineCommmand(){
        //create new medicine to log
        val id = DomainId.create<MedicineId>()
        val medicine = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            Instant.ofEpochSecond(12331),
            id,
            "testroute",
            1.2f,
            "cm",
            "45646",
            "11111",
            "tsttype"
        )
        //handle log medicine command
        handle(buildLogMedicineCommand(medicine), timestamp = medicine.administrationTime!!, commandId = id.copy())
        //ensure event is created
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals(id.copy(), events.list[0].eventId)
        //Assert.assertEquals("Medicine added tsttype: newmed testroute 1.2 cm (45646, 11111)", events.list[0].event)
        Assert.assertEquals("Logged Medicine tsttype: newmed testroute 1.2 cm (45646, 11111)", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.MEDS.dataString, events.list[0].eventType)

        //test medicine with missing fields
        val id2 = DomainId.create<MedicineId>()
        val medicine2 = Medicine(
            name = "newmed2",
            ndc = "testNdc",
            administrationTime = Instant.ofEpochSecond(12332),
            medId = id2,
            route = "testroute",
            volume = 1.2f,
            unit = "cm",
            serialNumber = "45646",
        )
        handle(buildLogMedicineCommand(medicine2), timestamp = medicine2.administrationTime!!, commandId = id2.copy())
        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals(id2.copy(), events.list[1].eventId)
        Assert.assertEquals("Logged Medicine: newmed2 testroute 1.2 cm (45646)", events.list[1].event)
        Assert.assertEquals(KnownEventTypes.MEDS.dataString, events.list[1].eventType)
    }

    @Test
    fun testLogMedWithIdCommmand(){
        //create new medicine to log
        val id = DomainId.create<MedicineId>()
        val medicine = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            Instant.ofEpochSecond(12331),
            id,
            "testroute",
            1.2f,
            "cm",
            "45646",
            "11111",
            "tsttype"
        )
        //handle log medicine command
        handle(buildLogMedWithIdCommand(medicine))
        //ensure event is created
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals(id.copy(), events.list[0].referencedItem)
        Assert.assertEquals("Logged Medicine tsttype: newmed testroute 1.2 cm (45646, 11111)", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.MEDS.dataString, events.list[0].eventType)

        //test medicine with missing fields
        val id2 = DomainId.create<MedicineId>()
        val medicine2 = Medicine(
            name = "newmed2",
            ndc = "testNdc",
            administrationTime = Instant.ofEpochSecond(12332),
            medId = id2,
            route = "testroute",
            volume = 1.2f,
            unit = "cm",
            serialNumber = "45646",
        )
        handle(buildLogMedWithIdCommand(medicine2))
        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals(id2.copy(), events.list[1].referencedItem)
        Assert.assertEquals("Logged Medicine: newmed2 testroute 1.2 cm (45646)", events.list[1].event)
        Assert.assertEquals(KnownEventTypes.MEDS.dataString, events.list[1].eventType)
    }

    @Test
    fun testUpdateMedicineCommand(){
        //create medicine
        val timestamp = Instant.ofEpochSecond(12331)
        val id = DomainId.create<MedicineId>()
        val commandTime = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val medicine = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            timestamp,
            id,
            "testroute",
            1.2f,
            "cm",
            "45646",
            "11111",
            "tsttype"
        )
        //make event
        events += Event(eventId = id.copy(), event = "Old Event", timestamp = timestamp)
        //update medicine
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals(id.copy(), events.list[0].eventId)
        Assert.assertEquals("Old Event", events.list[0].event)
        handle(buildUpdateMedicineCommand(medicine), timestamp = commandTime)
        //ensure event is updated
        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals("Updated Medicine tsttype: newmed testroute 1.2 cm (45646, 11111)", events.list[1].event)
        Assert.assertEquals(commandTime, events.list[1].timestamp)
        Assert.assertEquals(KnownEventTypes.MEDS.dataString, events.list[1].eventType)
        Assert.assertEquals(medicine.id, events.list[1].referencedItem)
    }

    @Test
    fun testUpdateMedicineCommandFromOldMed(){
        //create medicine
        val timestamp = Instant.ofEpochSecond(12331)
        val id = DomainId.create<MedicineId>()
        val commandTime = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val medicine = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            timestamp,
            id,
            "testroute",
            1.2f,
            "cm",
            "45646",
            "11111",
            "tsttype"
        )

        val oldMedicine = Medicine(
            "oldmed",
            "testNdc",
            "testRxcui",
            timestamp,
            id,
            "testroute",
            1.2f,
            "mm",
            "45646",
            "22222",
            "tsttype"
        )

        medicines+=oldMedicine
        //make event
        events += Event(eventId = id.copy(), event = "Old Event", timestamp = timestamp)
        //update medicine
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals(id.copy(), events.list[0].eventId)
        Assert.assertEquals("Old Event", events.list[0].event)

        handle(buildUpdateMedicineCommand(medicine, oldMedicine), timestamp = commandTime)

        //ensure event is updated
        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals("Updated Medicine tsttype: newmed testroute 1.2 cm (45646, 11111)", events.list[1].event)
        Assert.assertEquals(commandTime, events.list[1].timestamp)
        Assert.assertEquals(KnownEventTypes.MEDS.dataString, events.list[1].eventType)
        Assert.assertEquals(medicine.id, events.list[1].referencedItem)
    }

    @Test
    fun testRemoveMedicineCommand(){
        //create and add medicine
        val eventId = DomainId.create<EventId>()
        val id = DomainId.create<MedicineId>()
        val medicine = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            Instant.ofEpochSecond(12331),
            id,
            "testroute",
            1.2f,
            "cm",
            "45646",
            "11111",
            "tsttype"
        )
        medicines += medicine
        //remove medicine
        handle(buildRemoveMedTreatmentCommand(id, false), commandId = eventId.copy())
        //ensure event is added
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals(eventId, events.list[0].eventId)
        Assert.assertEquals("Removed Medicine tsttype: newmed testroute 1.2 cm (45646, 11111)", events.list[0].event)
        Assert.assertEquals(medicine.id, events.list[0].referencedItem)
    }

    @Test
    fun testRemoveMedicineCommand_Error(){
        //create and add medicine
        val eventId = DomainId.create<EventId>()
        val id = DomainId.create<MedicineId>()
        val medicine = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            Instant.ofEpochSecond(12331),
            id,
            "testroute",
            1.2f,
            "cm",
            "45646",
            "11111",
            "tsttype"
        )
        medicines += medicine
        //remove medicine
        handle(buildRemoveMedTreatmentCommand(id, true), commandId = eventId.copy())
        //ensure event is added
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals(eventId, events.list[0].eventId)
        Assert.assertEquals("Removed Medicine tsttype: newmed testroute 1.2 cm (45646, 11111) due to documentation error", events.list[0].event)
        Assert.assertEquals(medicine.id, events.list[0].referencedItem)
    }

    @Test
    fun testEventString(){
        //test medicine with missing fields
        val id = DomainId.create<MedicineId>()
        val medicine = Medicine(
            name = "newmed",
            "testNdc",
            "testRxcui",
            Instant.ofEpochSecond(12331),
            medId = id,
            volume = 1.2f,
            unit = "cm",
        )
        events += Event(eventId = id.copy(), event = "Event2", timestamp = Instant.now())
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals(id.copy(), events.list[0].eventId)
        Assert.assertEquals("Event2", events.list[0].event)
        handle(buildUpdateMedicineCommand(medicine))
        Assert.assertEquals("Updated Medicine: newmed 1.2 cm", events.list[0].event)
    }

    @Test
    fun testEmptyEventString(){
        //test medicine with only required fields
        val id = DomainId.create<MedicineId>()
        val medicine = Medicine(
            name = "newmed",
            ndc = "testNdc",
            administrationTime = Instant.ofEpochSecond(123),
            medId = id
        )
        events += Event(eventId = id.copy(), event = "Event2", timestamp = Instant.now())
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals(id.copy(), events.list[0].eventId)
        Assert.assertEquals("Event2", events.list[0].event)
        handle(buildUpdateMedicineCommand(medicine))
        Assert.assertEquals("Updated Medicine: newmed", events.list[0].event)
    }
}