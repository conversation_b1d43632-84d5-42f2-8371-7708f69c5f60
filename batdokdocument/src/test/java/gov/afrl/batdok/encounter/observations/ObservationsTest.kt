package gov.afrl.batdok.encounter.observations

import gov.afrl.batdok.encounter.Observations
import gov.afrl.batdok.encounter.observation.CommonObservations
import gov.afrl.batdok.encounter.observation.Observation
import org.junit.Assert
import org.junit.Test

class ObservationsTest {
    @Test
    fun testGetAllExcept_String(){
        val observations = Observations()
        val ob1 = Observation(CommonObservations.PUPIL_DILATION.dataString)
        val ob2 = Observation(CommonObservations.FEELING.dataString)
        observations += listOf(
            ob1,
            ob2,
        )

        val result = observations.getAllExcept(CommonObservations.PUPIL_DILATION)

        Assert.assertEquals(1, result.size)
        Assert.assertEquals(ob2, result.first())
    }
}