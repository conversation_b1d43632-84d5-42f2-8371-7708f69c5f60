package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.commands.buildAddEquipmentCommand
import gov.afrl.batdok.encounter.commands.buildAddRemoveEquipmentCommands
import gov.afrl.batdok.encounter.commands.buildAddRemoveEquipmentFailureCommands
import gov.afrl.batdok.encounter.commands.buildAddRemovePowerLossCommands
import gov.afrl.batdok.encounter.commands.buildAddRemoveUserErrorCommands
import gov.afrl.batdok.encounter.commands.buildRemoveEquipmentCommand
import gov.afrl.batdok.encounter.commands.buildUpdateEquipmentCommand
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.encounter.ids.EquipmentId
import gov.afrl.batdok.encounter.metadata.*
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class EquipmentStateTest {

    private val metadata = Metadata()
    private var currentEquipment: List<Equipment>
        get() = metadata.equipment
        set(it) { metadata.equipment = it }
    private val equipmentLog = metadata.equipmentLog

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS), commandId: CommandId = DomainId.create()) {
        metadata.handlers.handle(
            DomainId.nil(),
            buildCommandData(subCommand, callsign, timestamp, commandId)
        )
    }

    @Test
    fun testAddEquipmentCommand() {
        Assert.assertEquals(0, metadata.equipment.size)
        handle(buildAddEquipmentCommand(equipment = Equipment(name ="Test",data = OrthopedicData("Everything", "All at once", "Everywhere"))))
        Assert.assertEquals(1, metadata.equipment.size)
        Assert.assertTrue(currentEquipment[0].data is OrthopedicData)
        val data = currentEquipment[0].data as OrthopedicData
        Assert.assertEquals("Everything", data.type)
        Assert.assertEquals("All at once", data.subtype)
        Assert.assertEquals("Everywhere", data.location)
    }

    @Test
    fun testUpdateEquipmentCommand() {
        var id = DomainId.create<EquipmentId>()
        val equipment = listOf(Equipment(id = id, name = "Test", data = OrthopedicData("Everything", "All at once", "Everywhere")))
        metadata.equipment += equipment
        handle(buildUpdateEquipmentCommand(Equipment(id = id, name = "Test2", data = OrthopedicData("Everything2", "All at once2", "Everywhere2"))))
        Assert.assertEquals(1, metadata.equipment.size)
        Assert.assertEquals("Test2", metadata.equipment[0].name)
        Assert.assertTrue(currentEquipment[0].data is OrthopedicData)
        val data = currentEquipment[0].data as OrthopedicData
        Assert.assertEquals("Everything2", data.type)
        Assert.assertEquals("All at once2", data.subtype)
        Assert.assertEquals("Everywhere2", data.location)
    }

    @Test
    fun testRemoveEquipmentCommand() {
        val id = DomainId.create<EquipmentId>()
        val equipment = listOf(Equipment(id = id, name = "Test", data = OrthopedicData("Everything", "All at once", "Everywhere")))
        metadata.equipment += equipment
        Assert.assertEquals(1, metadata.equipment.size)
        handle(buildRemoveEquipmentCommand(equipment = Equipment(id = id, name = "Test", data = OrthopedicData("Everything", "All at once", "Everywhere")), false))
        Assert.assertEquals(0, metadata.equipment.size)
    }

    // region tests for equipment log which is deprecated
    @Test
    fun testAddRemoveEquipmentCommand_EquipmentLog_Add(){
        val equipment = listOf(Equipment("Test", LitterData(true, true, true)))
        Assert.assertEquals(0, equipmentLog.used.size)
        handle(buildAddRemoveEquipmentCommands(equipment, listOf()))
        Assert.assertEquals(1, equipmentLog.used.size)
        Assert.assertEquals("Test", equipmentLog.used[0].name)
        Assert.assertTrue(equipmentLog.used[0].data is LitterData)
        val data = equipmentLog.used[0].data as LitterData
        Assert.assertTrue(data.isFront?: false)
        Assert.assertTrue(data.isHeadFirst?: false)
        Assert.assertTrue(data.isRight?: false)
    }
    
    @Test
    fun testAddRemoveEquipmentCommand_EquipmentLog_Remove(){
        val equipment = listOf(Equipment("Test", LitterData(true, true, true)))
        equipmentLog.used += equipment
        Assert.assertEquals(1, equipmentLog.used.size)
        handle(buildAddRemoveEquipmentCommands(listOf(), equipment))
        Assert.assertEquals(0, equipmentLog.used.size)
    }

    @Test
    fun testAddRemovePowerLossCommand_EquipmentLog_Add(){
        val equipment = listOf(Equipment("Test", LitterData(true, true, true)))
        Assert.assertEquals(0, equipmentLog.powerLoss.size)
        handle(buildAddRemovePowerLossCommands(equipment, listOf()))
        Assert.assertEquals(1, equipmentLog.powerLoss.size)
        Assert.assertEquals("Test", equipmentLog.powerLoss[0].name)
        Assert.assertTrue(equipmentLog.powerLoss[0].data is LitterData)
        val data = equipmentLog.powerLoss[0].data as LitterData
        Assert.assertTrue(data.isFront?: false)
        Assert.assertTrue(data.isHeadFirst?: false)
        Assert.assertTrue(data.isRight?: false)
    }

    @Test
    fun testAddRemovePowerLossCommand_EquipmentLog_Remove(){
        val equipment = listOf(Equipment("Test", LitterData(true, true, true)))
        equipmentLog.powerLoss += equipment
        Assert.assertEquals(1, equipmentLog.powerLoss.size)
        handle(buildAddRemovePowerLossCommands(listOf(), equipment))
        Assert.assertEquals(0, equipmentLog.powerLoss.size)
    }


    @Test
    fun testAddRemoveUserErrorCommand_EquipmentLog_Add(){
        val equipment = listOf(Equipment("Test", LitterData(true, true, true)))
        Assert.assertEquals(0, equipmentLog.userError.size)
        handle(buildAddRemoveUserErrorCommands(equipment, listOf()))
        Assert.assertEquals(1, equipmentLog.userError.size)
        Assert.assertEquals("Test", equipmentLog.userError[0].name)
        Assert.assertTrue(equipmentLog.userError[0].data is LitterData)
        val data = equipmentLog.userError[0].data as LitterData
        Assert.assertTrue(data.isFront?: false)
        Assert.assertTrue(data.isHeadFirst?: false)
        Assert.assertTrue(data.isRight?: false)
    }

    @Test
    fun testAddRemoveUserErrorCommand_EquipmentLog_Remove(){
        val equipment = listOf(Equipment("Test", LitterData(true, true, true)))
        equipmentLog.userError += equipment
        Assert.assertEquals(1, equipmentLog.userError.size)
        handle(buildAddRemoveUserErrorCommands(listOf(), equipment))
        Assert.assertEquals(0, equipmentLog.userError.size)
    }


    @Test
    fun testAddRemoveEquipmentFailureCommand_EquipmentLog_Add(){
        val equipment = listOf(Equipment("Test", LitterData(true, true, true)))
        Assert.assertEquals(0, equipmentLog.equipmentFailure.size)
        handle(buildAddRemoveEquipmentFailureCommands(equipment, listOf()))
        Assert.assertEquals(1, equipmentLog.equipmentFailure.size)
        Assert.assertEquals("Test", equipmentLog.equipmentFailure[0].name)
        Assert.assertTrue(equipmentLog.equipmentFailure[0].data is LitterData)
        val data = equipmentLog.equipmentFailure[0].data as LitterData
        Assert.assertTrue(data.isFront?: false)
        Assert.assertTrue(data.isHeadFirst?: false)
        Assert.assertTrue(data.isRight?: false)
    }

    @Test
    fun testAddRemoveEquipmentFailureCommand_EquipmentLog_Remove(){
        val equipment = listOf(Equipment("Test", LitterData(true, true, true)))
        equipmentLog.equipmentFailure += equipment
        Assert.assertEquals(1, equipmentLog.equipmentFailure.size)
        handle(buildAddRemoveEquipmentFailureCommands(listOf(), equipment))
        Assert.assertEquals(0, equipmentLog.equipmentFailure.size)
    }
    // endregion
}