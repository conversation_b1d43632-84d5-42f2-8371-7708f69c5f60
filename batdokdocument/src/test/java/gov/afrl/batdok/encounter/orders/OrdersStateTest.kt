package gov.afrl.batdok.encounter.orders

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.Contact
import gov.afrl.batdok.encounter.Interval
import gov.afrl.batdok.encounter.commands.orders.buildAddOrderLineCommand
import gov.afrl.batdok.encounter.commands.orders.buildUpdateOrderLineCommand
import gov.afrl.batdok.encounter.commands.orders.buildUpdateOrderLineStatusCommand
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.encounter.ids.OrderLineId
import gov.afrl.batdok.encounter.metadata.Signature
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class OrdersStateTest {

    val documentId = DomainId.create<DocumentId>()
    val orders = Orders()
    val callsign = "CS"

    private fun handle(
        subCommand: Message,
        callsign: String = this.callsign,
        timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS),
        id: CommandId = DomainId.create()
    ) {
        orders.handlers.handle(documentId, buildCommandData(subCommand, callsign, timestamp, id))
    }

    private fun createOrderLine() = CustomActionOrderLine(
        id = DomainId.create(),
        timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS),
        title = "Test order line",
        instructions = "Take your meds",
        orderType = OrderType.MEDICATION.dataString,
        orderStatus = OrderStatus.IN_PROGRESS.dataString,
        frequency = Interval(1, 30),
        lastOccurrence = Instant.now().minusSeconds(3600).truncatedTo(ChronoUnit.SECONDS),
        provider = Contact("Dr Phil", "**********", "<EMAIL>"),
        signature = Signature("Dr Phil", Instant.now().truncatedTo(ChronoUnit.SECONDS), ByteArray(100))
    )

    @Test
    fun testAddOrderLine(){
        val orderLineToAdd = createOrderLine()

        handle(buildAddOrderLineCommand(orderLineToAdd))

        Assert.assertEquals(1, orders.list.size)
        val addedOrderLine = orders.list[0]
        Assert.assertEquals(orderLineToAdd.id, addedOrderLine.id)
        Assert.assertEquals(orderLineToAdd.timestamp, addedOrderLine.timestamp)
        Assert.assertEquals(orderLineToAdd.title, addedOrderLine.title)
        Assert.assertEquals(orderLineToAdd.instructions, addedOrderLine.instructions)
        Assert.assertEquals(orderLineToAdd.orderType, addedOrderLine.orderType)
        Assert.assertEquals(orderLineToAdd.orderStatus, addedOrderLine.orderStatus)
        Assert.assertEquals(orderLineToAdd.frequency, addedOrderLine.frequency)
        Assert.assertEquals(orderLineToAdd.lastOccurrence, addedOrderLine.lastOccurrence)
        Assert.assertEquals(orderLineToAdd.provider?.name, addedOrderLine.provider?.name)
        Assert.assertEquals(orderLineToAdd.provider?.email, addedOrderLine.provider?.email)
        Assert.assertEquals(orderLineToAdd.provider?.phone, addedOrderLine.provider?.phone)
        Assert.assertEquals(orderLineToAdd.signature?.name, addedOrderLine.signature?.name)
        Assert.assertEquals(orderLineToAdd.signature?.timeStamp, addedOrderLine.signature?.timeStamp)
        Assert.assertArrayEquals(orderLineToAdd.signature?.signature, addedOrderLine.signature?.signature)
    }

    @Test
    fun testUpdateOrderLine(){
        val orderLineToUpdate = createOrderLine()
        orders += orderLineToUpdate

        val newTimestamp = Instant.now().plusSeconds(3600).truncatedTo(ChronoUnit.SECONDS)
        val newTitle = "A different order line"
        val newInstructions = "Takest thine potions"
        val newFrequency = Interval(3, 0)
        val newLastOccurrence = Instant.now().plusSeconds(1800).truncatedTo(ChronoUnit.SECONDS)
        val newProvider = Contact("Dr Laura", "**********", "<EMAIL>")
        val newSignature = Signature("Dr Laura", Instant.now().plusSeconds(3600).truncatedTo(ChronoUnit.SECONDS), ByteArray(128))

        Assert.assertEquals(1, orders.list.size)

        handle(
            buildUpdateOrderLineCommand(
            id = orderLineToUpdate.id,
            timestamp = newTimestamp,
            title = newTitle,
            instructions = newInstructions,
            frequency = newFrequency,
            lastOccurrence = newLastOccurrence,
            provider = newProvider,
            signature = newSignature
        ), callsign = "NewWorldOrder")

        Assert.assertEquals(1, orders.list.size)
        val updatedOrderLine = orders.list[0]
        Assert.assertEquals(orderLineToUpdate.id, updatedOrderLine.id)
        Assert.assertEquals(newTimestamp, updatedOrderLine.timestamp)
        Assert.assertEquals(newTitle, updatedOrderLine.title)
        Assert.assertEquals(newInstructions, updatedOrderLine.instructions)
        Assert.assertEquals(orderLineToUpdate.orderType, updatedOrderLine.orderType)
        Assert.assertEquals(orderLineToUpdate.orderStatus, updatedOrderLine.orderStatus)
        Assert.assertEquals(newFrequency, updatedOrderLine.frequency)
        Assert.assertEquals(newLastOccurrence, updatedOrderLine.lastOccurrence)
        Assert.assertEquals(newProvider.name, updatedOrderLine.provider?.name)
        Assert.assertEquals(newProvider.email, updatedOrderLine.provider?.email)
        Assert.assertEquals(newProvider.phone, updatedOrderLine.provider?.phone)
        Assert.assertEquals(newSignature.name, updatedOrderLine.signature?.name)
        Assert.assertEquals(newSignature.timeStamp, updatedOrderLine.signature?.timeStamp)
        Assert.assertArrayEquals(newSignature.signature, updatedOrderLine.signature?.signature)
    }

    @Test
    fun testUpdateOrderLineStatus(){
        val orderLineToUpdate = createOrderLine()
        orders += orderLineToUpdate

        val newStatus = OrderStatus.COMPLETE

        Assert.assertEquals(1, orders.list.size)

        handle(
            buildUpdateOrderLineStatusCommand(
            id = orderLineToUpdate.id,
            orderStatus = newStatus
        ), callsign = "NewWorldOrder")

        Assert.assertEquals(1, orders.list.size)
        val updatedOrderLine = orders.list[0]
        Assert.assertEquals(orderLineToUpdate.id, updatedOrderLine.id)
        Assert.assertEquals(orderLineToUpdate.timestamp, updatedOrderLine.timestamp)
        Assert.assertEquals(orderLineToUpdate.title, updatedOrderLine.title)
        Assert.assertEquals(orderLineToUpdate.instructions, updatedOrderLine.instructions)
        Assert.assertEquals(orderLineToUpdate.orderType, updatedOrderLine.orderType)
        Assert.assertEquals(newStatus.dataString, updatedOrderLine.orderStatus)
        Assert.assertEquals(orderLineToUpdate.frequency, updatedOrderLine.frequency)
        Assert.assertEquals(orderLineToUpdate.lastOccurrence, updatedOrderLine.lastOccurrence)
        Assert.assertEquals(orderLineToUpdate.provider, updatedOrderLine.provider)
        Assert.assertEquals(orderLineToUpdate.signature?.name, updatedOrderLine.signature?.name)
        Assert.assertEquals(orderLineToUpdate.signature?.timeStamp, updatedOrderLine.signature?.timeStamp)
        Assert.assertArrayEquals(orderLineToUpdate.signature?.signature, updatedOrderLine.signature?.signature)
    }

    @Test
    fun testNothingHappensIfDoesntExistOnUpdate(){
        val orderLineToNotUpdate = createOrderLine()
        orders += orderLineToNotUpdate

        val newTimestamp = Instant.now().plusSeconds(3600)
        val newTitle = "A different order line"
        val newInstructions = "Takest thine potions"
        val newFrequency = Interval(3, 0)
        val newLastOccurrence = Instant.now().plusSeconds(1800)
        val newProvider = Contact("Dr Laura", "**********", "<EMAIL>")

        Assert.assertEquals(1, orders.list.size)

        val nonexistentId = DomainId.create<OrderLineId>()

        handle(
            buildUpdateOrderLineCommand(
            id = nonexistentId,
            timestamp = newTimestamp,
            title = newTitle,
            instructions = newInstructions,
            frequency = newFrequency,
            lastOccurrence = newLastOccurrence,
            provider = newProvider
        ), callsign = "NewWorldOrder")

        Assert.assertEquals(1, orders.list.size)
        val updatedOrderLine = orders.list[0]
        Assert.assertEquals(orderLineToNotUpdate.id, updatedOrderLine.id)
        Assert.assertEquals(orderLineToNotUpdate.timestamp, updatedOrderLine.timestamp)
        Assert.assertEquals(orderLineToNotUpdate.title, updatedOrderLine.title)
        Assert.assertEquals(orderLineToNotUpdate.instructions, updatedOrderLine.instructions)
        Assert.assertEquals(orderLineToNotUpdate.orderType, updatedOrderLine.orderType)
        Assert.assertEquals(orderLineToNotUpdate.orderStatus, updatedOrderLine.orderStatus)
        Assert.assertEquals(orderLineToNotUpdate.frequency, updatedOrderLine.frequency)
        Assert.assertEquals(orderLineToNotUpdate.lastOccurrence, updatedOrderLine.lastOccurrence)
        Assert.assertEquals(orderLineToNotUpdate.provider, updatedOrderLine.provider)
        Assert.assertEquals(orderLineToNotUpdate.signature?.name, updatedOrderLine.signature?.name)
        Assert.assertEquals(orderLineToNotUpdate.signature?.timeStamp, updatedOrderLine.signature?.timeStamp)
        Assert.assertArrayEquals(orderLineToNotUpdate.signature?.signature, updatedOrderLine.signature?.signature)
    }

    @Test
    fun testNothingHappensIfDoesntExistOnUpdateStatus(){
        val orderLineToNotUpdate = createOrderLine()
        orders += orderLineToNotUpdate

        val newStatus = OrderStatus.COMPLETE

        Assert.assertEquals(1, orders.list.size)

        val nonexistentId = DomainId.create<OrderLineId>()

        handle(
            buildUpdateOrderLineStatusCommand(
            id = nonexistentId,
            orderStatus = newStatus
        ), callsign = "NewWorldOrder")

        Assert.assertEquals(1, orders.list.size)
        val updatedOrderLine = orders.list[0]
        Assert.assertEquals(orderLineToNotUpdate.id, updatedOrderLine.id)
        Assert.assertEquals(orderLineToNotUpdate.timestamp, updatedOrderLine.timestamp)
        Assert.assertEquals(orderLineToNotUpdate.title, updatedOrderLine.title)
        Assert.assertEquals(orderLineToNotUpdate.instructions, updatedOrderLine.instructions)
        Assert.assertEquals(orderLineToNotUpdate.orderType, updatedOrderLine.orderType)
        Assert.assertEquals(orderLineToNotUpdate.orderStatus, updatedOrderLine.orderStatus)
        Assert.assertEquals(orderLineToNotUpdate.frequency, updatedOrderLine.frequency)
        Assert.assertEquals(orderLineToNotUpdate.lastOccurrence, updatedOrderLine.lastOccurrence)
        Assert.assertEquals(orderLineToNotUpdate.provider, updatedOrderLine.provider)
        Assert.assertEquals(orderLineToNotUpdate.signature?.name, updatedOrderLine.signature?.name)
        Assert.assertEquals(orderLineToNotUpdate.signature?.timeStamp, updatedOrderLine.signature?.timeStamp)
        Assert.assertArrayEquals(orderLineToNotUpdate.signature?.signature, updatedOrderLine.signature?.signature)
    }
}