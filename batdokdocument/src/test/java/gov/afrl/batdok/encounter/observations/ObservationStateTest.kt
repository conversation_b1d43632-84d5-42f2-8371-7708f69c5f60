package gov.afrl.batdok.encounter.observations

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.Document
import gov.afrl.batdok.encounter.Links
import gov.afrl.batdok.encounter.Observations
import gov.afrl.batdok.encounter.commands.buildLogObservationCommand
import gov.afrl.batdok.encounter.commands.buildRemoveObservationCommand
import gov.afrl.batdok.encounter.commands.buildUpdateObservationCommand
import gov.afrl.batdok.encounter.commands.buildUpdatePhysicalExamFindingCommand
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.encounter.ids.ObservationId
import gov.afrl.batdok.encounter.observation.*
import gov.afrl.batdok.util.buildCommandData
import gov.afrl.batdok.util.buildDocumentCommand
import io.mockk.mockk
import io.mockk.verify
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class ObservationStateTest {

    val observations = Observations()
    val docId = DomainId.create<DocumentId>()
    val linksMockk = mockk<Links>(relaxed = true)

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS)) {
        observations.handlers.handle(docId, buildCommandData(subCommand, callsign, timestamp))
        observations.linkHandlers(linksMockk).handle(docId, buildCommandData(subCommand, callsign, timestamp))
    }

    @Test
    fun testUpdatePhysicalExamFinding(){
        Assert.assertTrue(observations.basicPhysicalExam.isEmpty())
        handle(buildUpdatePhysicalExamFindingCommand("Head", "Headache"))
        Assert.assertTrue(observations.basicPhysicalExam.isNotEmpty())
        Assert.assertEquals("Headache", observations.basicPhysicalExam["Head"])
    }

    @Test
    fun testLogObservationCommand(){
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.PUPIL_REACTION.dataString, PupilReactionData(true, true)))
        Assert.assertTrue(observations.list.isNotEmpty())
        Assert.assertEquals("Pupil Reaction", observations.list[0].name)
        with(observations.list[0].observationData){
            Assert.assertNotNull(this)
            Assert.assertTrue(this is PupilReactionData)
            this as PupilReactionData
            Assert.assertTrue(leftSide!!)
            Assert.assertTrue(reaction!!)
        }
        Assert.assertEquals(docId, observations.list[0].documentId)
    }

    @Test
    fun testLogObservation_Wheezing(){
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.WHEEZING.dataString, RespSideData(RespSideData.Type.LEFT.dataString)))
        Assert.assertTrue(observations.list.isNotEmpty())
        Assert.assertEquals("Wheezing", observations.list[0].name)
        Assert.assertEquals("Left", (observations.list[0].observationData as RespSideData).type)
        Assert.assertEquals(docId, observations.list[0].documentId)
    }

    @Test
    fun testLogObservation_Rhonchi(){
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.RHONCHI.dataString, RespSideData(RespSideData.Type.RIGHT.dataString)))
        Assert.assertTrue(observations.list.isNotEmpty())
        Assert.assertEquals("Rhonchi", observations.list[0].name)
        Assert.assertEquals("Right", (observations.list[0].observationData as RespSideData).type)
        Assert.assertEquals(docId, observations.list[0].documentId)
    }

    @Test
    fun testLogObservation_Rales(){
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.RALES.dataString, RespSideData(RespSideData.Type.BOTH.dataString)))
        Assert.assertTrue(observations.list.isNotEmpty())
        Assert.assertEquals("Rales", observations.list[0].name)
        Assert.assertEquals("Both", (observations.list[0].observationData as RespSideData).type)
        Assert.assertEquals(docId, observations.list[0].documentId)

        handle(buildLogObservationCommand(CommonObservations.RALES.dataString, RespSideData(null)))
        Assert.assertEquals(2, observations.list.size)
        Assert.assertEquals("Rales", observations.list[1].name)
        Assert.assertEquals(null, (observations.list[1].observationData as RespSideData).type)
        Assert.assertEquals(docId, observations.list[1].documentId)
    }

    @Test
    fun testLogObservation_HeadInjury(){
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.HEAD_INJURY.dataString))
        Assert.assertTrue(observations.list.isNotEmpty())
        Assert.assertEquals("Head Injury", observations.list[0].name)
        Assert.assertEquals(docId, observations.list[0].documentId)
    }

    @Test
    fun testUpdateObservationCommand(){
        val oldObservation = Observation(
            CommonObservations.PUPIL_REACTION.dataString,
            PupilReactionData(false, false),
            DomainId.create(),
            Instant.now().truncatedTo(ChronoUnit.SECONDS)
        )
        observations += oldObservation
        Assert.assertTrue(observations.list.isNotEmpty())
        handle(
            buildUpdateObservationCommand(
                Observation(
                    name = CommonObservations.PUPIL_REACTION.dataString,
                    observationData = PupilReactionData(true, true),
                    id = oldObservation.id
                )
            )
        )
        Assert.assertTrue(observations.list.isNotEmpty())
        Assert.assertEquals("Pupil Reaction", observations.list[0].name)
        with(observations.list[0].observationData){
            Assert.assertNotNull(this)
            Assert.assertTrue(this is PupilReactionData)
            this as PupilReactionData
            Assert.assertTrue(leftSide!!)
            Assert.assertTrue(reaction!!)
        }
        Assert.assertEquals(docId, observations.list[0].documentId)
    }

    @Test
    fun testUpdateObservationCommand_OnlySomeFields(){
        val oldObservation = Observation(
            CommonObservations.PUPIL_REACTION.dataString,
            PupilReactionData(false, false),
            DomainId.create(),
            Instant.now().truncatedTo(ChronoUnit.SECONDS)
        )
        observations += oldObservation
        Assert.assertTrue(observations.list.isNotEmpty())
        handle(
            buildUpdateObservationCommand(
                Observation(
                    name = CommonObservations.PUPIL_REACTION.dataString,
                    observationData = PupilReactionData(false, true),
                    id = oldObservation.id
                )
            )
        )
        Assert.assertTrue(observations.list.isNotEmpty())
        Assert.assertEquals("Pupil Reaction", observations.list[0].name)
        with(observations.list[0].observationData){
            Assert.assertNotNull(this)
            Assert.assertTrue(this is PupilReactionData)
            this as PupilReactionData
            Assert.assertFalse(leftSide!!)
            Assert.assertTrue(reaction!!)
        }
        Assert.assertEquals(docId, observations.list[0].documentId)
    }

    @Test
    fun testRemoveObservationCommand_Error(){
        val id = DomainId.create<ObservationId>()
        observations += Observation("Name", id = id)
        Assert.assertTrue(observations.list.isNotEmpty())
        handle(buildRemoveObservationCommand(id, true))
        Assert.assertTrue(observations.list.isEmpty())
        Assert.assertEquals(1, observations.onErrorRemovedItems.size)
    }

    @Test
    fun testRemoveObservationCommand_Proper(){
        val id = DomainId.create<ObservationId>()
        observations += Observation("Name", id = id)
        Assert.assertTrue(observations.list.isNotEmpty())
        handle(buildRemoveObservationCommand(id, false))
        Assert.assertTrue(observations.list.isEmpty())
        Assert.assertEquals(1, observations.onProperRemovedItems.size)
    }

    @Test
    fun testLogObservation_PulseType(){
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.PULSE_TYPES.dataString, PulseTypeData(PulseTypeData.Quality.NORMAL.dataString, PulseTypeData.Location.CAROTID_LEFT.dataString)))
        Assert.assertTrue(observations.list.isNotEmpty())
        Assert.assertEquals("Pulse Types", observations.list[0].name)
        Assert.assertEquals("Normal", (observations.list[0].observationData as PulseTypeData).quality)
        Assert.assertEquals("Carotid Left", (observations.list[0].getData<PulseTypeData>())?.location)
    }

    @Test
    fun testLogObservation_PulseValues(){
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.PULSE_VALUES.dataString, PulseValuesData(PulseValuesData.Quality.ONE.dataString, femoral = PulseValuesData.Quality.THREE.dataString)))
        Assert.assertTrue(observations.list.isNotEmpty())
        Assert.assertEquals("Pulse Values", observations.list[0].name)
        observations.list[0].getData<PulseValuesData>()!!.let {
            Assert.assertEquals("+1", it.brachial)
            Assert.assertEquals("+3", it.femoral)
        }
    }

    @Test
    fun testLogObservation_RespEffortData(){
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.RESP_EFFORT.dataString, RespEffortData(RespEffortData.Effort.AGONAL.dataString)))
        Assert.assertTrue(observations.list.isNotEmpty())
        Assert.assertEquals("Respiratory Effort", observations.list[0].name)
        Assert.assertEquals("Agonal", observations.list[0].getData<RespEffortData>()?.effort)
        Assert.assertEquals(docId, observations.list[0].documentId)
    }

    @Test
    fun testLogObservation_ChestRiseFallData(){
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.CHEST_EQUAL_RISE_FALL.dataString, ChestRiseFallData(ChestRiseFallData.Type.SYMMETRICAL.dataString)))
        Assert.assertTrue(observations.list.isNotEmpty())
        Assert.assertEquals("Chest Equal Rise & Fall", observations.list[0].name)
        Assert.assertEquals("Symmetrical", observations.list[0].getData<ChestRiseFallData>()?.riseOrFall)

    }

    @Test
    fun testLogObservation_Feeling(){
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.FEELING.dataString, FeelingData(FeelingData.Type.WEAKNESS.dataString)))
        Assert.assertTrue(observations.list.isNotEmpty())
        Assert.assertEquals("Feeling", observations.list[0].name)
        Assert.assertEquals("Weakness", (observations.list[0].observationData as FeelingData).type)
        Assert.assertEquals(docId, observations.list[0].documentId)
    }

    @Test
    fun testLogObservation_UseDataHelperFunction(){
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.FEELING.dataString, FeelingData(FeelingData.Type.WEAKNESS.dataString)))
        Assert.assertTrue(observations.list.isNotEmpty())
        Assert.assertEquals("Feeling", observations.list[0].name)
        Assert.assertEquals("Weakness", (observations.list[0].getData<FeelingData>())?.type)
        Assert.assertEquals(docId, observations.list[0].documentId)
    }

    @Test
    fun testLogObservation_Rhythm(){
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.RHYTHM.dataString, RhythmData(RhythmData.Type.SVT.dataString)))
        Assert.assertTrue(observations.list.isNotEmpty())
        Assert.assertEquals("Rhythm", observations.list[0].name)
        Assert.assertEquals("SVT", (observations.list[0].observationData as RhythmData).type)
        Assert.assertEquals(docId, observations.list[0].documentId)
    }

    @Test
    fun testLogObservation_CapRefill(){
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.CAP_REFILL.dataString, CapillaryRefillData(3)))
        Assert.assertTrue(observations.list.isNotEmpty())
        Assert.assertEquals("Capillary Refill", observations.list[0].name)
        Assert.assertEquals(3, (observations.list[0].observationData as CapillaryRefillData).data)
        Assert.assertEquals(docId, observations.list[0].documentId)
    }

    @Test
    fun testLogObservationCommand_Gastro(){
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.GASTRO.dataString, GastroData("Open Abdomen")))
        Assert.assertTrue(observations.list.isNotEmpty())
        Assert.assertEquals("Gastro", observations.list[0].name)
        Assert.assertEquals("Open Abdomen", (observations.list[0].observationData as GastroData).type)
        Assert.assertEquals(docId, observations.list[0].documentId)
    }

    @Test
    fun testLogObservationCommand_Integ(){
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.INTEG.dataString, IntegData("Warm")))
        Assert.assertTrue(observations.list.isNotEmpty())
        Assert.assertEquals("Integ", observations.list[0].name)
        Assert.assertEquals("Warm", (observations.list[0].observationData as IntegData).type)
        Assert.assertEquals(docId, observations.list[0].documentId)
    }

    @Test
    fun testLogObservationCommand_TransfusionIndication(){
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.TRANSFUSION_INDICATION.dataString, TransfusionIndicationData(true, true, true)))
        Assert.assertTrue(observations.list.isNotEmpty())
        Assert.assertEquals(CommonObservations.TRANSFUSION_INDICATION.dataString, observations.list[0].name)
        Assert.assertTrue(observations.list[0].getData<TransfusionIndicationData>()?.amputation!!)
        Assert.assertTrue(observations.list[0].getData<TransfusionIndicationData>()?.hrOver120!!)
        Assert.assertTrue(observations.list[0].getData<TransfusionIndicationData>()?.sbpUnder90!!)
        Assert.assertEquals(docId, observations.list[0].documentId)
    }

    @Test
    fun testUpdateObservationCommand_TransfusionIndication(){
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.TRANSFUSION_INDICATION.dataString, TransfusionIndicationData(true, true, true)))
        val o = observations.list[0]
        handle(buildUpdateObservationCommand(Observation(
            o.name,
            TransfusionIndicationData(false, true, true),
            o.id,
            o.timestamp,
            o.documentId
        ), observations.list[0]))
        Assert.assertTrue(observations.list.isNotEmpty())
        Assert.assertEquals(CommonObservations.TRANSFUSION_INDICATION.dataString, observations.list[0].name)
        Assert.assertFalse(observations.list[0].getData<TransfusionIndicationData>()?.amputation!!)
        Assert.assertTrue(observations.list[0].getData<TransfusionIndicationData>()?.hrOver120!!)
        Assert.assertTrue(observations.list[0].getData<TransfusionIndicationData>()?.sbpUnder90!!)
        Assert.assertEquals(docId, observations.list[0].documentId)
    }

    @Test
    fun testRemoveObservationCommand_TransfusionIndication(){
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.TRANSFUSION_INDICATION.dataString, TransfusionIndicationData()))
        val o = observations.list[0]
        handle(buildRemoveObservationCommand(o.id, true))
        Assert.assertTrue(observations.list.isEmpty())
        Assert.assertEquals(1, observations.onErrorRemovedItems.size)
    }

    @Test
    fun testLogObservationCommand_BloodSugar(){
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.BLOOD_SUGAR.dataString, BloodSugarData(4.5, "mg/dL")))
        Assert.assertTrue(observations.list.isNotEmpty())
        Assert.assertEquals(CommonObservations.BLOOD_SUGAR.dataString, observations.list[0].name)
        Assert.assertEquals(4.5, observations.list[0].getData<BloodSugarData>()?.value!!, 0.1)
        Assert.assertEquals("mg/dL", observations.list[0].getData<BloodSugarData>()?.unit!!)
        Assert.assertEquals(docId, observations.list[0].documentId)
    }

    @Test
    fun testLogObservationCommand_Ultrasound(){
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.ULTRASOUND.dataString, UltrasoundData("TEST")))
        Assert.assertTrue(observations.list.isNotEmpty())
        Assert.assertEquals(CommonObservations.ULTRASOUND.dataString, observations.list[0].name)
        Assert.assertEquals("TEST", observations.list[0].getData<UltrasoundData>()?.description!!)
        Assert.assertEquals(docId, observations.list[0].documentId)
    }

    @Test
    fun testUpdateObservationCommand_Ultrasound(){
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.ULTRASOUND.dataString, UltrasoundData("Test")))
        val o = observations.list[0]
        handle(buildUpdateObservationCommand(Observation(
            o.name,
            UltrasoundData(),
            o.id,
            o.timestamp,
            o.documentId
        ), observations.list[0]))
        Assert.assertTrue(observations.list.isNotEmpty())
        Assert.assertEquals(CommonObservations.ULTRASOUND.dataString, observations.list[0].name)
        Assert.assertNull(observations.list[0].getData<UltrasoundData>()?.description)
        Assert.assertEquals(docId, observations.list[0].documentId)
    }

    @Test
    fun testRemoveObservationCommand_Ultrasound(){
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.ULTRASOUND.dataString, UltrasoundData()))
        val o = observations.list[0]
        handle(buildRemoveObservationCommand(o.id, true))
        Assert.assertTrue(observations.list.isEmpty())
    }

    @Test
    fun testLogObservationCommand_PhysicalExam(){
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.PHYSICAL_EXAM.dataString, PhysicalExamData("TEST", "Right Arm")))
        Assert.assertTrue(observations.list.isNotEmpty())
        Assert.assertEquals(CommonObservations.PHYSICAL_EXAM.dataString, observations.list[0].name)
        Assert.assertEquals("TEST", observations.list[0].getData<PhysicalExamData>()?.finding!!)
        Assert.assertEquals("Right Arm", observations.list[0].getData<PhysicalExamData>()?.location)
        Assert.assertEquals(docId, observations.list[0].documentId)
    }

    @Test
    fun testUpdateObservationCommand_PhysicalExam(){
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.PHYSICAL_EXAM.dataString, PhysicalExamData("Test")))
        val o = observations.list[0]
        handle(buildUpdateObservationCommand(Observation(
            o.name,
            PhysicalExamData(""),
            o.id,
            o.timestamp,
            o.documentId
        ), observations.list[0]))
        Assert.assertTrue(observations.list.isNotEmpty())
        Assert.assertEquals(CommonObservations.PHYSICAL_EXAM.dataString, observations.list[0].name)
        Assert.assertEquals("", observations.list[0].getData<PhysicalExamData>()?.finding)
        Assert.assertNull(observations.list[0].getData<PhysicalExamData>()?.location)
        Assert.assertEquals(docId, observations.list[0].documentId)
    }

    @Test
    fun testRemoveObservationCommand_PhysicalExam(){
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.PHYSICAL_EXAM.dataString))
        val o = observations.list[0]
        handle(buildRemoveObservationCommand(o.id, true))
        Assert.assertTrue(observations.list.isEmpty())
    }
    @Test
    fun testLogObservationCommand_CasualtyPPE(){
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.CASUALTY_PPE.dataString, CasualtyPPEData(listOf(CasualtyPPEData.Type.BLAST_SENSOR_OTHER.dataString))))
        Assert.assertTrue(observations.list.isNotEmpty())
        Assert.assertEquals(CommonObservations.CASUALTY_PPE.dataString, observations.list[0].name)
        Assert.assertEquals(listOf(CasualtyPPEData.Type.BLAST_SENSOR_OTHER.dataString), observations.list[0].getData<CasualtyPPEData>()?.ppeList!!)
        Assert.assertEquals(docId, observations.list[0].documentId)
    }

    @Test
    fun testUpdateObservationCommand_CasualtyPPE(){
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.CASUALTY_PPE.dataString,CasualtyPPEData(listOf(CasualtyPPEData.Type.BLAST_SENSOR_OTHER.dataString))))
        val o = observations.list[0]
        handle(buildUpdateObservationCommand(Observation(
            o.name,
            CasualtyPPEData(),
            o.id,
            o.timestamp,
            o.documentId
        ), observations.list[0]))
        Assert.assertTrue(observations.list.isNotEmpty())
        Assert.assertEquals(CommonObservations.CASUALTY_PPE.dataString, observations.list[0].name)
        Assert.assertEquals(listOf<String>(), observations.list[0].getData<CasualtyPPEData>()?.ppeList!!)
        Assert.assertEquals(docId, observations.list[0].documentId)
    }

    @Test
    fun testRemoveObservationCommand_CasualtyPPE(){
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.CASUALTY_PPE.dataString, CasualtyPPEData()))
        val o = observations.list[0]
        handle(buildRemoveObservationCommand(o.id, true))
        Assert.assertTrue(observations.list.isEmpty())
    }

    @Test
    fun testLogObservationCommand_BooleanObservationData_true_Diabetes(){
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.DIABETES.dataString, BooleanObservation(true)))
        Assert.assertTrue(observations.list.isNotEmpty())
        Assert.assertEquals(CommonObservations.DIABETES.dataString, observations.list[0].name)
        Assert.assertEquals(true, observations.list[0].getData<BooleanObservation>()?.data)
    }

    @Test
    fun testLogObservationCommand_BooleanObservationData_false_Diabetes(){
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.DIABETES.dataString, BooleanObservation(false)))
        Assert.assertTrue(observations.list.isNotEmpty())
        Assert.assertEquals(CommonObservations.DIABETES.dataString, observations.list[0].name)
        Assert.assertEquals(false, observations.list[0].getData<BooleanObservation>()?.data)
    }

    @Test
    fun testLogObservationCommand_BooleanObservationData_null_Diabetes(){
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.DIABETES.dataString, BooleanObservation()))
        Assert.assertTrue(observations.list.isNotEmpty())
        Assert.assertEquals(CommonObservations.DIABETES.dataString, observations.list[0].name)
        Assert.assertNull(observations.list[0].getData<BooleanObservation>()?.data)
        Assert.assertEquals(docId, observations.list[0].documentId)
    }

    @Test
    fun testLogObservationCommand_BreathSounds() {
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.BREATH_SOUNDS.dataString, BreathSoundsData(listOf(BreathSoundsData.Type.LEFT_ABSENT.dataString))))
        Assert.assertTrue(observations.list.isNotEmpty())
        Assert.assertEquals(CommonObservations.BREATH_SOUNDS.dataString, observations.list[0].name)
        Assert.assertEquals(listOf(BreathSoundsData.Type.LEFT_ABSENT.dataString), observations.list[0].getData<BreathSoundsData>()?.typeList!!)
        Assert.assertEquals(docId, observations.list[0].documentId)
    }
    
    @Test
    fun testUpdateObservationCommand_BreathSounds() {
        val expectedUpdatedList = listOf(BreathSoundsData.Type.RIGHT_ABSENT.dataString, BreathSoundsData.Type.RIGHT_NORMAL.dataString)
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.BREATH_SOUNDS.dataString,BreathSoundsData(listOf(BreathSoundsData.Type.LEFT_ABSENT.dataString))))
        val o = observations.list[0]
        handle(buildUpdateObservationCommand(Observation(
            o.name,
            BreathSoundsData(expectedUpdatedList),
            o.id,
            o.timestamp,
            o.documentId
        ), observations.list[0]))
        Assert.assertTrue(observations.list.isNotEmpty())
        Assert.assertEquals(CommonObservations.BREATH_SOUNDS.dataString, observations.list[0].name)
        Assert.assertEquals(expectedUpdatedList, observations.list[0].getData<BreathSoundsData>()?.typeList!!)
        Assert.assertEquals(docId, observations.list[0].documentId)
    }

    @Test
    fun testRemoveObservationCommand_BreathSounds() {
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.BREATH_SOUNDS.dataString,BreathSoundsData(listOf(BreathSoundsData.Type.LEFT_ABSENT.dataString))))
        val o = observations.list[0]
        handle(buildRemoveObservationCommand(o.id, true))
        Assert.assertTrue(observations.list.isEmpty())
        verify { linksMockk.removeLinkedItem(any(), any()) }
    }

    @Test
    fun testLogObservation_EFastExamData() {
        Assert.assertTrue(observations.list.isEmpty())
        val examData = EFastExamData(
            leftLungSliding = EFastExamData.Type.POSITIVE.dataString,
            rightLungSliding = EFastExamData.Type.NEGATIVE.dataString,
            pericardialFluid = EFastExamData.Type.EQUIVOCAL.dataString,
            rightUpperQuadrant = EFastExamData.Type.POSITIVE.dataString,
            leftUpperQuadrant = EFastExamData.Type.NEGATIVE.dataString,
            suprapubicFluid = EFastExamData.Type.EQUIVOCAL.dataString,
            interpretation = EFastExamData.Type.POSITIVE.dataString
        )
        handle(buildLogObservationCommand(CommonObservations.EFAST_EXAM.dataString, examData))
        Assert.assertTrue(observations.list.isNotEmpty())
        Assert.assertEquals("EFAST Exam", observations.list[0].name)
        val loggedData = observations.list[0].getData<EFastExamData>()
        Assert.assertNotNull(loggedData)
        Assert.assertEquals(EFastExamData.Type.POSITIVE.dataString, loggedData!!.leftLungSliding)
        Assert.assertEquals(EFastExamData.Type.NEGATIVE.dataString, loggedData.rightLungSliding)
        Assert.assertEquals(EFastExamData.Type.EQUIVOCAL.dataString, loggedData.pericardialFluid)
        Assert.assertEquals(EFastExamData.Type.POSITIVE.dataString, loggedData.rightUpperQuadrant)
        Assert.assertEquals(EFastExamData.Type.NEGATIVE.dataString, loggedData.leftUpperQuadrant)
        Assert.assertEquals(EFastExamData.Type.EQUIVOCAL.dataString, loggedData.suprapubicFluid)
        Assert.assertEquals(EFastExamData.Type.POSITIVE.dataString, loggedData.interpretation)
    }

    @Test
    fun testUpdateObservation_EFastExamData() {
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.EFAST_EXAM.dataString, EFastExamData()))
        val o = observations.list[0]
        handle(buildUpdateObservationCommand(Observation(
            o.name,
            EFastExamData(pericardialFluid = EFastExamData.Type.POSITIVE.dataString),
            o.id,
            o.timestamp,
            o.documentId
        ), observations.list[0]))
        Assert.assertTrue(observations.list.isNotEmpty())
        Assert.assertEquals(CommonObservations.EFAST_EXAM.dataString, observations.list[0].name)
        Assert.assertEquals(EFastExamData.Type.POSITIVE.dataString, observations.list[0].getData<EFastExamData>()?.pericardialFluid!!)
        Assert.assertEquals(docId, observations.list[0].documentId)
    }

    @Test
    fun testRemoveObservationCommand_EFastExamData() {
        Assert.assertTrue(observations.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.EFAST_EXAM.dataString, EFastExamData()))
        val o = observations.list[0]
        handle(buildRemoveObservationCommand(o.id, true))
        Assert.assertTrue(observations.list.isEmpty())
        verify { linksMockk.removeLinkedItem(any(), any()) }
    }

    @Test
    fun testAddWithId(){
        val id = DomainId.create<DomainId>()
        val doc = Document()

        doc.handle(
            listOf(
                buildDocumentCommand(
                    DomainId.create(),
                    buildCommandData(buildLogObservationCommand("Ob", id = id.copy()))
                ),
            )
        )
        Assert.assertEquals(id, doc.observations.list.first().id)
    }
}