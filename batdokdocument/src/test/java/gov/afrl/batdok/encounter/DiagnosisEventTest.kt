package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.Diagnosis.Companion.includeDiagnosisCommands
import gov.afrl.batdok.encounter.commands.buildChangeDiagnosisCommand
import gov.afrl.batdok.encounter.commands.buildSelectProtocolCommand
import gov.afrl.batdok.encounter.commands.buildUpdateDifferentialsCommand
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class DiagnosisEventTest {

    val events = Events()

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS)) {
        EventCommandHandler(events) {
            includeDiagnosisCommands()
        }.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp))
    }

    @Test
    fun testChangeDiagnosisCommand_enum(){
        val newDiagnosis = DiagnosisCategory.BATTLE_INJURY
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeDiagnosisCommand(newDiagnosis))
        Assert.assertEquals("Set diagnosis to ${newDiagnosis.dataString}", events.list[0].event)
    }
    @Test
    fun testChangeDiagnosisCommand_string(){
        val newDiagnosis = "Diagnosis"
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeDiagnosisCommand(newDiagnosis))
        Assert.assertEquals("Set diagnosis to $newDiagnosis", events.list[0].event)
    }
    @Test
    fun testChangeDiagnosisCommand_clear(){
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeDiagnosisCommand(""))
        Assert.assertEquals("Cleared diagnosis", events.list[0].event)
    }

    @Test
    fun testUpdateDifferentials_Add(){
        val differential = "Test"
        Assert.assertEquals(0, events.list.size)
        handle(buildUpdateDifferentialsCommand(differential, true))
        Assert.assertEquals("Added Test as a differential diagnosis", events.list[0].event)
    }

    @Test
    fun testUpdateDifferentials_Remove(){
        val differential = "Test"
        Assert.assertEquals(0, events.list.size)
        handle(buildUpdateDifferentialsCommand(differential, false))
        Assert.assertEquals("Removed Test as a differential diagnosis", events.list[0].event)
    }

    @Test
    fun testSelectedProtocol_Value(){
        val newProtocol = "Protocol"
        Assert.assertEquals(0, events.list.size)
        handle(buildSelectProtocolCommand(newProtocol))
        Assert.assertEquals("Set selected protocol to $newProtocol", events.list[0].event)
    }
    @Test
    fun testSelectedProtocol_Clear(){
        Assert.assertEquals(0, events.list.size)
        handle(buildSelectProtocolCommand(""))
        Assert.assertEquals("Cleared selected protocol", events.list[0].event)
    }
}