package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.commands.proto.*
import gov.afrl.batdok.encounter.Info.Companion.includeInfoEvents
import gov.afrl.batdok.encounter.commands.*
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.encounter.ids.ProblemId
import gov.afrl.batdok.encounter.ids.toByteString
import gov.afrl.batdok.encounter.movement.EvacCategory
import gov.afrl.batdok.encounter.movement.EvacType
import gov.afrl.batdok.encounter.pampi.Immunization
import gov.afrl.batdok.encounter.pampi.Problem
import gov.afrl.batdok.encounter.pampi.ProblemStatus
import gov.afrl.batdok.encounter.pampi.Procedure
import gov.afrl.batdok.util.*
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit

class InfoEventTest {

    val events = Events()
    val info = Info()

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS)) {
        EventCommandHandler(events) {
            includeInfoEvents(info)
        }.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp))
    }

    //region Filled Info Event
    @Test
    fun testChangeName() {
        val newName = "Test"
        Assert.assertEquals(0, events.list.size)
        handle(changeNameCommand { last = newName })
        Assert.assertEquals("Set name to $newName", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testChangeName_ThreeNames() {
        val newName = "Test"
        Assert.assertEquals(0, events.list.size)
        handle(changeNameCommand {
            first = newName
            last = newName
            middle = newName
        })
        Assert.assertEquals("Set name to $newName $newName $newName", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeSSN() {
        val newSSN = "1234"
        Assert.assertEquals(0, events.list.size)
        handle(changeSSNCommand { ssn = newSSN })
        Assert.assertEquals("Set ssn to $newSSN", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeCiteNumber() {
        val newCiteNumber = "1234"
        Assert.assertEquals(0, events.list.size)
        handle(changeCiteNumberCommand { citeNumber = newCiteNumber })
        Assert.assertEquals("Set cite number to $newCiteNumber", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeBattleRosterNumber() {
        val newBRN = "Test1234"
        Assert.assertEquals(0, events.list.size)
        handle(changeBattleRosterNumberCommand { battleRosterNumber = newBRN })
        Assert.assertEquals("Set battle roster number to $newBRN", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeAlias(){
        val newAlias = "Bed3"
        Assert.assertEquals(0, events.list.size)
        handle(changeAliasCommand { alias = newAlias })
        Assert.assertEquals("Set alias to $newAlias", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeUnitCommand(){
        val newUnit = "Test"
        Assert.assertEquals(0, events.list.size)
        handle(changeUnitCommand{ unit = newUnit })
        Assert.assertEquals("Set unit to $newUnit", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testChangeUnitPhoneNumberCommand(){
        val newUnitPhoneNumber = "Test"
        Assert.assertEquals(0, events.list.size)
        handle(changeUnitPhoneNumberCommand{ unitPhoneNumber = newUnitPhoneNumber })
        Assert.assertEquals("Set unit phone number to $newUnitPhoneNumber", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testChangeRankCommand(){
        val newRank = "Test"
        Assert.assertEquals(0, events.list.size)
        handle(changeRankCommand{ rank = newRank })
        Assert.assertEquals("Set rank to $newRank", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testChangeGradeCommand_enum() {
        val newGrade = Grade.E09
        Assert.assertEquals(0, events.list.size)
        handle(changeGradeCommand { grade = newGrade.toProto() })
        Assert.assertEquals("Set grade to ${newGrade.dataString}", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testChangeGradeCommand_string() {
        val newGrade = "Test"
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeGradeCommand(newGrade))
        Assert.assertEquals("Set grade to $newGrade", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testChangeNationalityCommand() {
        val newNationality = CountryData.AFGHANISTAN.dataString
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeNationalityCommand(newNationality))
        Assert.assertEquals("Set nationality to $newNationality", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testChangeServiceCommand_enum(){
        val newService = Service.AIR_FORCE
        Assert.assertEquals(0, events.list.size)
        handle(changeServiceCommand{ service = newService.toProto() })
        Assert.assertEquals("Set service to ${newService.dataString}", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testChangeServiceCommand_string(){
        val newService = "TEST"
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeServiceCommand(newService))
        Assert.assertEquals("Set service to $newService", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testChangePatcatCommand_enum_valid(){
        val newService = PatcatService.ARMY
        val newStatus = PatcatStatus.CADET
        val patcat = Patcat(newService, newStatus)
        Assert.assertEquals(0, events.list.size)
        handle(buildChangePatcatCommand(patcat))
        Assert.assertEquals("Set patcat to A14 - USA CADET", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testChangePatcatCommand_enum_invalid(){
        val newService = PatcatService.ARMY // this is a military service
        val newStatus = PatcatStatus.CIVILIAN_HUMANITARIAN_OR_REFUGEE // this is a civilian status, so it's invalid here
        val patcat = Patcat(newService, newStatus)
        Assert.assertEquals(0, events.list.size)
        handle(buildChangePatcatCommand(patcat))
        // TODO: do we want to indicate that the patcat is invalid in the event text?
        Assert.assertEquals("Set patcat to A91 - USA CIVILIAN HUMANITARIAN", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testChangePatcatCommand_string_valid(){
        val newServiceCode = "A"
        val newStatusCode = "14"
        val patcat = Patcat(newServiceCode, newStatusCode)
        Assert.assertEquals(0, events.list.size)
        handle(buildChangePatcatCommand(patcat))
        Assert.assertEquals("Set patcat to A14 - USA CADET", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testChangePatcatCommand_string_invalid(){
        val newServiceCode = "ZZZ" // these codes don't exist
        val newStatusCode = "9000"
        val patcat = Patcat(newServiceCode, newStatusCode)
        Assert.assertEquals(0, events.list.size)
        handle(buildChangePatcatCommand(patcat))
        // TODO: do we want to indicate that the patcat is invalid in the event text?
        Assert.assertEquals("Set patcat to ZZZ9000 - ZZZ 9000", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangePatcatCommmand_enum_null_clearEvent() {
        val patcat = Patcat(service = null, status = null)
        Assert.assertEquals(0, events.list.size)
        handle(buildChangePatcatCommand(patcat))
        Assert.assertEquals("Cleared patcat", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangePatcatCommmand_string_null_clearEvent() {
        val patcat = Patcat(serviceCode = null, statusCode = null)
        Assert.assertEquals(0, events.list.size)
        handle(buildChangePatcatCommand(patcat))
        Assert.assertEquals("Cleared patcat", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testChangeTattooCommand(){
        val newTattoo = "Test"
        Assert.assertEquals(0, events.list.size)
        handle(changeTattooCommand{ tattoo = newTattoo })
        Assert.assertEquals("Set tattoo to $newTattoo", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testChangeEvacCommand_enum(){
        val newEvac = EvacCategory.EXPECTANT
        Assert.assertEquals(0, events.list.size)
        handle(changeEvacCommand{ evac = newEvac.toProto() })
        Assert.assertEquals("Set triage status to ${newEvac.dataString}", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testChangeEvacCommand_string(){
        val newEvac = "Test"
        Assert.assertEquals(0, events.list.size)
        handle(changeEvacCommand{ evac = EvacCategory.fromString(newEvac) })
        Assert.assertEquals("Set triage status to $newEvac", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testChangeEvacTypeCommand_enum(){
        val newEvacType = EvacType.CASEVAC
        Assert.assertEquals(0, events.list.size)
        handle(changeEvacTypeCommand{ evac = newEvacType.toProto() })
        Assert.assertEquals("Set evac type to $newEvacType", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testChangeEvacTypeCommand_string(){
        val newEvacType = "Test"
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeEvacTypeCommand(newEvacType))
        Assert.assertEquals("Set evac type to $newEvacType", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testChangeBloodTypeCommand_enum(){
        val newBloodType = BloodType.AB_NEGATIVE.dataString
        Assert.assertEquals(0, events.list.size)
        handle(changeBloodTypeCommand{ bloodType = BloodType.fromString(newBloodType) })
        Assert.assertEquals("Set blood type to $newBloodType", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testChangeBloodTypeCommand_string(){
        val newBloodType = "Test"
        Assert.assertEquals(0, events.list.size)
        handle(changeBloodTypeCommand{ bloodType = BloodType.fromString(newBloodType) })
        Assert.assertEquals("Set blood type to $newBloodType", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }

    /**
     * WHEN we generate a blood type command that includes a positive low titer
     * THEN the event message contains the positive low titer state
     */
    @Test
    fun testChangeBloodTypeCommand_PositiveLowTiter(){
        val newBloodType = "O+"
        Assert.assertEquals(0, events.list.size)
        handle(changeBloodTypeCommand{
            bloodType = BloodType.fromString(newBloodType)
            lowTiter = nullableBoolValue(true)
        })
        Assert.assertEquals("Set blood type to O+ (Low Titer)", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }

    /**
     * WHEN we generate a blood type command that includes a negative low titer
     * THEN the event message will not contain the low titer state
     */
    @Test
    fun testChangeBloodTypeCommand_NegativeLowTiter(){
        val newBloodType = "O+"
        Assert.assertEquals(0, events.list.size)
        handle(changeBloodTypeCommand{
            bloodType = BloodType.fromString(newBloodType)
            lowTiter = nullableBoolValue(false)
        })
        Assert.assertEquals("Set blood type to O+", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }

    /**
     * WHEN we generate a blood type command that does not include a low titer state
     * THEN the event message does not contain the low titer state
     */
    @Test
    fun testChangeBloodTypeCommand_NoLowTiter(){
        val newBloodType = "O+"
        Assert.assertEquals(0, events.list.size)
        handle(changeBloodTypeCommand{
            bloodType = BloodType.fromString(newBloodType)
        })
        Assert.assertEquals("Set blood type to O+", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeGenderCommand_enum(){
        val newGender = Gender.MALE
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeGenderCommand(newGender))
        Assert.assertEquals("Set sex to ${newGender.dataString}", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testChangeGenderCommand_string(){
        val newGender = "Test"
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeGenderCommand(newGender))
        Assert.assertEquals("Set sex to $newGender", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testChangeInjuryTimeCommand(){
        val newInjuryTime = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val dateFormat = newInjuryTime.format(Patterns.mdyhm_24_dash_comma_colon)
        Assert.assertEquals(0, events.list.size)
        handle(changeInjuryTimeCommand{ datetime = int64Value(newInjuryTime.epochSecond) })
        Assert.assertEquals("Set injury time to $dateFormat", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testChangeDobCommand(){
        val newDob = LocalDate.now()
        val dateFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd").format(newDob)
        Assert.assertEquals(0, events.list.size)
        handle(changeDobCommand{ dob = int64Value(newDob.toEpochDay()) })
        Assert.assertEquals("Set DOB to $dateFormat", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeDobCommand_Estimate(){
        val newDob = LocalDate.now()
        val dateFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd").format(newDob)
        Assert.assertEquals(0, events.list.size)
        handle(changeDobCommand{
            dob = int64Value(newDob.toEpochDay())
            estimate = true
        })
        Assert.assertEquals("Set DOB to $dateFormat (Estimated)", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeDodIdCommand(){
        val newDodId = "Test"
        Assert.assertEquals(0, events.list.size)
        handle(changeDodIdCommand{ dodId = newDodId })
        Assert.assertEquals("Set DoD ID to $newDodId", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testChangeWeightCommand(){
        val newWeight = 123.4f
        Assert.assertEquals(0, events.list.size)
        handle(changeWeightCommand{ weight = floatValue(newWeight) })
        Assert.assertEquals("Set weight to 272.05 lbs (123.40 kg)", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testChangeHeightCommand(){
        val newHeight = 123.4f
        Assert.assertEquals(0, events.list.size)
        handle(changeHeightCommand{ height = floatValue(newHeight) })
        Assert.assertEquals("Set height to 4' 0.58\" (123.40 cm)", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testChangeDispositionCommand(){
        val newDisposition = Disposition.DECEASED.dataString
        Assert.assertEquals(0, events.list.size)
        handle(changeDisposition{ disposition = Disposition.fromString(newDisposition) })
        Assert.assertEquals("Set disposition to Deceased", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    //endregion

    //region Cleared Info Events
    @Test
    fun testClearName() {
        Assert.assertEquals(0, events.list.size)
        handle(changeNameCommand { last = "" })
        Assert.assertEquals("Cleared name", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }

    @Test
    fun testClearSSN() {
        Assert.assertEquals(0, events.list.size)
        handle(changeSSNCommand { ssn = "" })
        Assert.assertEquals("Cleared ssn", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }

    @Test
    fun testClearCiteNumber() {
        Assert.assertEquals(0, events.list.size)
        handle(changeCiteNumberCommand { citeNumber = "" })
        Assert.assertEquals("Cleared cite number", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }

    @Test
    fun testClearBattleRosterNumber() {
        Assert.assertEquals(0, events.list.size)
        handle(changeBattleRosterNumberCommand { battleRosterNumber = "" })
        Assert.assertEquals("Cleared battle roster number", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }

    @Test
    fun testClearUnitCommand(){
        Assert.assertEquals(0, events.list.size)
        handle(changeUnitCommand{ unit = "" })
        Assert.assertEquals("Cleared unit", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testClearRankCommand(){
        Assert.assertEquals(0, events.list.size)
        handle(changeRankCommand{ rank = "" })
        Assert.assertEquals("Cleared rank", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testClearGradeCommand() {
        Assert.assertEquals(0, events.list.size)
        handle(changeGradeCommand{ grade = compatibleEnum {} })
        Assert.assertEquals("Cleared grade", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testClearServiceCommand(){
        Assert.assertEquals(0, events.list.size)
        handle(changeServiceCommand{ service = compatibleEnum {} })
        Assert.assertEquals("Cleared service", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testClearTattooCommand(){
        Assert.assertEquals(0, events.list.size)
        handle(changeTattooCommand{ tattoo = "" })
        Assert.assertEquals("Cleared tattoo", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testClearEvacCommand(){
        Assert.assertEquals(0, events.list.size)
        handle(changeEvacCommand{ evac = compatibleEnum {} })
        Assert.assertEquals("Cleared triage status", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testClearEvacTypeCommand(){
        Assert.assertEquals(0, events.list.size)
        handle(changeEvacTypeCommand{ evac = compatibleEnum{} })
        Assert.assertEquals("Cleared evac type", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testClearBloodTypeCommand(){
        Assert.assertEquals(0, events.list.size)
        handle(changeBloodTypeCommand{ bloodType = compatibleEnum {  } })
        Assert.assertEquals("Cleared blood type", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testClearGenderCommand(){
        Assert.assertEquals(0, events.list.size)
        handle(changeGenderCommand{ gender = compatibleEnum {  } })
        Assert.assertEquals("Cleared sex", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testClearInjuryTimeCommand(){
        Assert.assertEquals(0, events.list.size)
        handle(changeInjuryTimeCommand{ })
        Assert.assertEquals("Cleared injury time", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testClearDobCommand(){
        Assert.assertEquals(0, events.list.size)
        handle(changeDobCommand{ })
        Assert.assertEquals("Cleared DOB", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testClearDodIdCommand(){
        Assert.assertEquals(0, events.list.size)
        handle(changeDodIdCommand{ dodId = "" })
        Assert.assertEquals("Cleared DoD ID", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testClearWeightCommand(){
        Assert.assertEquals(0, events.list.size)
        handle(changeWeightCommand{ })
        Assert.assertEquals("Cleared weight", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testClearHeightCommand(){
        Assert.assertEquals(0, events.list.size)
        handle(changeHeightCommand{ })
        Assert.assertEquals("Cleared height", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testClearDispositionCommand_NoContent(){
        Assert.assertEquals(0, events.list.size)
        handle(changeDisposition{ })
        Assert.assertEquals("Cleared disposition", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    @Test
    fun testClearDispositionCommand_EmptyCompatibleEnum(){
        Assert.assertEquals(0, events.list.size)
        handle(changeDisposition{ disposition = compatibleEnum {  }})
        Assert.assertEquals("Cleared disposition", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    //endregion

    //region Allergy Tests
    @Test
    fun testAddRemoveAllergiesCommandEvent_AddAndRemove(){
        val allergiesToAdd = listOf("Opiates", "A1", "A2")
        val allergiesToRemove = listOf("Penicillin")
        Assert.assertEquals(0, events.list.size)
        handle(buildAddRemoveAllergyListCommand(allergiesToAdd, allergiesToRemove))
        Assert.assertEquals(
            "Added allergies: Opiates, A1, A2\nRemoved allergies: Penicillin",
            events.list[0].event
        )
    }
    @Test
    fun testAddRemoveAllergiesCommandEvent_AddOnly(){
        val allergiesToAdd = listOf("Opiates", "A1", "A2")
        Assert.assertEquals(0, events.list.size)
        handle(buildAddRemoveAllergyListCommand(allergiesToAdd, listOf()))
        Assert.assertEquals(
            "Added allergies: Opiates, A1, A2",
            events.list[0].event
        )
    }

    @Test
    fun testAddRemoveAllergiesCommandEvent_AddDuplicateAllergies(){
        val allergiesToAdd = listOf("A2", "A2", "A2", "A2", "A2")
        Assert.assertEquals(0, events.list.size)

        handle(buildAddRemoveAllergyListCommand(allergiesToAdd, listOf()))
        Assert.assertEquals(1, events.list.size)

        Assert.assertEquals(
            "Added allergies: A2",
            events.list[0].event
        )
    }

    @Test
    fun testAddRemoveAllergiesCommandEvent_RemoveDuplicateAllergies(){
        val allergiesToRemove = listOf("A2", "A2", "A2", "A2", "A2")
        Assert.assertEquals(0, events.list.size)

        handle(buildAddRemoveAllergyListCommand(listOf(), allergiesToRemove))
        Assert.assertEquals(1, events.list.size)

        Assert.assertEquals(
            "Removed allergies: A2",
            events.list[0].event
        )
    }

    @Test
    fun testAddRemoveAllergiesCommandEvent_NKA(){
        val allergiesToAdd = listOf(CommonAllergies.NKA.dataString)
        Assert.assertEquals(0, events.list.size)
        handle(buildAddRemoveAllergyListCommand(allergiesToAdd, listOf()))
        Assert.assertEquals(
            "Patient has no known allergies",
            events.list[0].event
        )
    }
    @Test
    fun testAddRemoveAllergiesCommandEvent_RemoveOnly(){
        val allergiesToRemove = listOf("Penicillin")
        Assert.assertEquals(0, events.list.size)
        handle(buildAddRemoveAllergyListCommand(listOf(), allergiesToRemove))
        Assert.assertEquals(
            "Removed allergies: Penicillin",
            events.list[0].event
        )
    }
    //endregion

    @Test
    fun testChangeHandoffCommand(){
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeHandoffCommand("Callsign"))
        Assert.assertEquals(
            "Received handoff from Callsign",
            events.list[0].event
        )
    }

    @Test
    fun testOpenEncounterCommand(){
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeOpenCloseEncounterCommand(true))
        Assert.assertEquals("Patient encounter opened", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }

    @Test
    fun testCloseEncounterCommand(){
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeOpenCloseEncounterCommand(false))
        Assert.assertEquals("Patient encounter closed", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeMaskingCommand_Add(){
        val maskReason = "Reason"
        Assert.assertEquals(0, events.list.size)
        handle(buildPatientMaskingCommand(true, maskReason))
        Assert.assertEquals("Masking enabled. Justification: $maskReason", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeMaskingCommand_Remove(){
        val maskReason = "Reason"
        Assert.assertEquals(0, events.list.size)
        handle(buildPatientMaskingCommand(false, maskReason))
        Assert.assertEquals("Masking disabled. Justification: $maskReason", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }

    //region Medication Tests
    @Test
    fun testAddRemoveMedicationsCommandEvent_AddAndRemove(){
        val medicationsToAdd = listOf("Opiates", "A1", "A2")
        val medicationsToRemove = listOf("Penicillin")
        Assert.assertEquals(0, events.list.size)
        handle(buildAddRemoveMedicationCommand(medicationsToAdd, medicationsToRemove))
        Assert.assertEquals(
            "Added Medications: Opiates, A1, A2\nRemoved Medications: Penicillin",
            events.list[0].event
        )
    }
    @Test
    fun testAddRemoveMedicationsCommandEvent_AddOnly(){
        val medicationsToAdd = listOf("Opiates", "A1", "A2")
        Assert.assertEquals(0, events.list.size)
        handle(buildAddRemoveMedicationCommand(medicationsToAdd, listOf()))
        Assert.assertEquals(
            "Added Medications: Opiates, A1, A2",
            events.list[0].event
        )
    }
    @Test
    fun testAddRemoveMedicationsCommandEvent_RemoveOnly(){
        val medicationsToRemove = listOf("Penicillin")
        Assert.assertEquals(0, events.list.size)
        handle(buildAddRemoveMedicationCommand(listOf(), medicationsToRemove))
        Assert.assertEquals(
            "Removed Medications: Penicillin",
            events.list[0].event
        )
    }
    //endregion

    @Test
    fun testChangePatientId() {
        Assert.assertEquals(0, events.list.size)
        handle(changePatientIdCommand { patientId = DomainId.create<DocumentId>().toByteString() })
        //No Event is created
        Assert.assertEquals(0, events.list.size)
    }

    @Test
    fun testAddImmunizations(){
        Assert.assertEquals(0, events.list.size)
        handle(buildUpdateImmunizationCommand(listOf(Immunization("Flu Vaccine", "ml", 2f, Instant.now())), listOf()))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Added immunizations: Flu Vaccine 2.0ml", events.list[0].event)
    }

    @Test
    fun removedImmunizations(){
        Assert.assertEquals(0, events.list.size)
        handle(buildUpdateImmunizationCommand(listOf(Immunization("Flu Vaccine", "ml", 2f, Instant.now())), listOf()))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Added immunizations: Flu Vaccine 2.0ml", events.list[0].event)
        handle(buildUpdateImmunizationCommand(listOf(), listOf(Immunization("Flu Vaccine", "ml", 2f, Instant.now()))))
        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals("Added immunizations: Flu Vaccine 2.0ml", events.list[0].event)
        Assert.assertEquals("Removed immunizations: Flu Vaccine 2.0ml", events.list[1].event)
    }

    @Test
    fun testAddProcedures(){
        val procedure = "Applied tourniquet due to gunshot wound"
        Assert.assertEquals(0, events.list.size)
        handle(buildUpdateProceduresCommand(listOf(Procedure(procedure, Instant.now())), listOf()))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Added procedures: $procedure", events.list[0].event)
    }

    @Test
    fun testRemoveProcedures(){
        val procedure = "Applied tourniquet due to gunshot wound"
        Assert.assertEquals(0, events.list.size)
        handle(buildUpdateProceduresCommand(listOf(Procedure(procedure, Instant.now())), listOf()))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Added procedures: $procedure", events.list[0].event)
        handle(buildUpdateProceduresCommand(listOf(), listOf(Procedure(procedure, Instant.now()))))
        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals("Added procedures: $procedure", events.list[0].event)
        Assert.assertEquals("Removed procedures: $procedure", events.list[1].event)
    }

    @Test
    fun testBuildUpdateProblemsCommandEvent() {
        Assert.assertEquals(0, events.list.size)
        handle(buildUpdateProblemsCommand(null, null))
        Assert.assertEquals(0, events.list.size)

        val problem1Id = DomainId.create<ProblemId>()
        val problem2Id = DomainId.create<ProblemId>()
        val problemListAdd = listOf(
            Problem("Problem 1", "ACTIVE", null,problem1Id),
            Problem("Problem 2", "PROPOSED", null,problem2Id)
        )
        handle(buildUpdateProblemsCommand(problemListAdd, null))
        Assert.assertEquals("Added Problems: Problem 1, Problem 2", events.list[0].event)

        info.problems = problemListAdd
        handle(buildUpdateProblemsCommand(null, listOf(problem1Id, problem2Id)))
        Assert.assertEquals("Removed Problems: Problem 1, Problem 2", events.list[1].event)
    }

    @Test
    fun testBuildUpdateProblemsCommandEvents_AddRemove(){
        Assert.assertEquals(0, events.list.size)
        val problem1Id = DomainId.create<ProblemId>()
        val problem2Id = DomainId.create<ProblemId>()
        val problemListAdd = listOf(
            Problem("Problem 2", ProblemStatus.PROPOSED.dataString, null, problem2Id)
        )
        info.problems += Problem("Problem 1", ProblemStatus.ACTIVE.dataString, null, problem1Id)
        handle(buildUpdateProblemsCommand(problemListAdd, listOf(problem1Id)))
        Assert.assertEquals("Added Problem: Problem 2\nRemoved Problem: Problem 1", events.list[0].event)
    }
}