package gov.afrl.batdok.encounter.treatments

import com.google.protobuf.Message
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.Event
import gov.afrl.batdok.encounter.Events
import gov.afrl.batdok.encounter.Treatments
import gov.afrl.batdok.encounter.commands.buildRemoveTreatmentCommand
import gov.afrl.batdok.encounter.commands.removeTreatmentEventCommandHandler
import gov.afrl.batdok.encounter.ids.TreatmentId
import gov.afrl.batdok.encounter.treatment.O2Data
import gov.afrl.batdok.encounter.treatment.Treatment
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class RemoveTreatmentEventTest {

    val treatments = Treatments()
    val events = Events()

    //region Helper functions
    private fun handle(
        subCommand: Message,
        callsign: String = "CS",
        timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS)
    ) {
        EventCommandHandler(events) {
            +removeTreatmentEventCommandHandler(treatments)
        }.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp))
    }
    //endregion

    @Test
    @Deprecated("Remove when O2Data gets rid of deprecated fields")
    fun testRemoveTreatmentCommand_Error_Old() {
        //Setup variables
        val id = DomainId.create<TreatmentId>()
        //Add Previous Event
        events += Event("Existing Event", id.copy(), Instant.now().truncatedTo(ChronoUnit.SECONDS))
        val treatment = Treatment(
            name = "O2 Data",
            treatmentData = O2Data(1f, O2Data.DeliveryMethod.NC.dataString),
            id = id
        )
        //Add Previous items
        treatments += treatment
        //Make sure the previous event exists
        Assert.assertEquals(1, events.list.size)
        //Handle Command
        handle(buildRemoveTreatmentCommand(id, true))
        //Make sure that still only 1 event exists
        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals("Removed Treatment O2 Data: Source: NC, LPM: 1.0 due to documentation error", events.list[1].event)
        Assert.assertEquals(id, events.list[1].referencedItem)
    }

    @Test
    fun testRemoveTreatmentCommand_Error() {
        //Setup variables
        val id = DomainId.create<TreatmentId>()
        //Add Previous Event
        events += Event("Existing Event", id.copy(), Instant.now().truncatedTo(ChronoUnit.SECONDS))
        val treatment = Treatment(
            name = "O2 Data",
            treatmentData = O2Data(lpm = 10f, targetSpO2 = 98, fiO2 = 13f, route = O2Data.O2Route.VENTURI_MASK.dataString),
            id = id
        )
        //Add Previous items
        treatments += treatment
        //Make sure the previous event exists
        Assert.assertEquals(1, events.list.size)
        //Handle Command
        handle(buildRemoveTreatmentCommand(id, true))
        //Make sure that still only 1 event exists
        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals("Removed Treatment O2 Data: LPM: 10.0, Target SPO2: 98, FIO2: 13.0, Route: Venturi Mask due to documentation error", events.list[1].event)
        Assert.assertEquals(id, events.list[1].referencedItem)
    }

    @Test
    @Deprecated("Remove when O2Data gets rid of deprecated fields")
    fun testRemoveTreatmentCommand_Proper_Old() {
        //Setup variables
        val id = DomainId.create<TreatmentId>()
        //Add Previous Event
        events += Event("Existing Event", id.copy(), Instant.now().truncatedTo(ChronoUnit.SECONDS))
        val treatment = Treatment(
            name = "O2 Data",
            treatmentData = O2Data(1f, O2Data.DeliveryMethod.NC.dataString),
            id = id
        )
        //Add Previous items
        treatments += treatment
        //Make sure the previous event exists
        Assert.assertEquals(1, events.list.size)
        //Handle Command
        handle(buildRemoveTreatmentCommand(id, false))
        //Make sure that still only 1 event exists
        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals("Removed Treatment O2 Data: Source: NC, LPM: 1.0", events.list[1].event)
        Assert.assertEquals(id, events.list[1].referencedItem)
    }

    @Test
    fun testRemoveTreatmentCommand_Proper() {
        //Setup variables
        val id = DomainId.create<TreatmentId>()
        //Add Previous Event
        events += Event("Existing Event", id.copy(), Instant.now().truncatedTo(ChronoUnit.SECONDS))
        val treatment = Treatment(
            name = "O2 Data",
            treatmentData = O2Data(lpm = 10f, targetSpO2 = 98, fiO2 = 13f, route = O2Data.O2Route.VENTURI_MASK.dataString),
            id = id
        )
        //Add Previous items
        treatments += treatment
        //Make sure the previous event exists
        Assert.assertEquals(1, events.list.size)
        //Handle Command
        handle(buildRemoveTreatmentCommand(id, false))
        //Make sure that still only 1 event exists
        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals("Removed Treatment O2 Data: LPM: 10.0, Target SPO2: 98, FIO2: 13.0, Route: Venturi Mask", events.list[1].event)
        Assert.assertEquals(id, events.list[1].referencedItem)
    }
}