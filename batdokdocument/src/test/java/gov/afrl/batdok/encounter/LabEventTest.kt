package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.Labs.Companion.includeLabHandlers
import gov.afrl.batdok.encounter.commands.buildLogLabCommand
import gov.afrl.batdok.encounter.commands.buildRemoveLabCommand
import gov.afrl.batdok.encounter.commands.buildUpdateLabCommand
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.encounter.ids.LabId
import gov.afrl.batdok.encounter.panel.KnownLabs
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class LabEventTest {

    private val labs = Labs()
    private val events = Events()

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS), commandId: CommandId = DomainId.create()) {
        EventCommandHandler(events) {
            includeLabHandlers(labs)
        }.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp, commandId))
    }

    @Test
    fun testLogLab(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val labList = listOf(
            IndividualLab(KnownLabs.K, "1.2"),
            IndividualLab("Test", "3.4")
        )
        handle(buildLogLabCommand(labList), timestamp = timestamp)

        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Logged Lab: K: 1.2, Test: 3.4", events.list[0].event)
        Assert.assertEquals(timestamp, events.list[0].timestamp)
        Assert.assertEquals(KnownEventTypes.LABS.dataString, events.list[0].eventType)
    }

    @Test
    fun testUpdateLab(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val labId = DomainId.create<LabId>()
        val labList = listOf(
            IndividualLab(KnownLabs.K, "1.2"),
            IndividualLab("Test", "3.4")
        )
        labs += labList
        events += Event("Test Event", labId.copy(), timestamp = timestamp.minusSeconds(1000))

        handle(buildUpdateLabCommand(labList[0].copy(value = "5.6")))

        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals("Updated Lab: K: 5.6", events.list[1].event)
        Assert.assertEquals(timestamp, events.list[1].timestamp)
        Assert.assertEquals(KnownEventTypes.LABS.dataString, events.list[1].eventType)
    }

    @Test
    fun testUpdateLab_ClearValues(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val labId = DomainId.create<LabId>()
        val labList = listOf(
            IndividualLab(KnownLabs.K, "1.2"),
            IndividualLab("Test", "3.4")
        )
        labs += labList
        events += Event("Test Event", labId.copy(), timestamp = timestamp.minusSeconds(100))

        val newLabs = listOf(
            labList[1].copy(value = null)
        )
        handle(buildUpdateLabCommand(newLabs[0]))

        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals("Removed Lab: Test: 3.4", events.list[1].event)
        Assert.assertEquals(timestamp, events.list[1].timestamp)
    }

    @Test
    fun testRemoveLabs(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val labList = listOf(
            IndividualLab(KnownLabs.K, "1.2"),
            IndividualLab("Test", "3.4")
        )

        labs += labList
        events += Event("Test Event", labList[1].id.copy(), timestamp = timestamp)

        handle(buildRemoveLabCommand(labList[1].id))

        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals("Removed Lab: Test: 3.4", events.list[1].event)
    }
}