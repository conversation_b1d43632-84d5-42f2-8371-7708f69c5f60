package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.commands.buildChangeDiagnosisCommand
import gov.afrl.batdok.encounter.commands.buildSelectProtocolCommand
import gov.afrl.batdok.encounter.commands.buildUpdateDifferentialsCommand
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class DiagnosisStateTest {
    private val diagnosis = Diagnosis()

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(
        ChronoUnit.SECONDS)) {
        diagnosis.handlers.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp))
    }

    @Test
    fun testChangeDiagnosisCommand_enum(){
        val newDiagnosis = DiagnosisCategory.BATTLE_INJURY
        Assert.assertNotEquals(newDiagnosis.dataString, diagnosis.category)
        handle(buildChangeDiagnosisCommand(newDiagnosis))
        Assert.assertEquals(newDiagnosis.dataString, diagnosis.category)
    }
    @Test
    fun testChangeDiagnosisCommand_string(){
        val newDiagnosis = "Test"
        Assert.assertNotEquals(newDiagnosis, diagnosis.category)
        handle(buildChangeDiagnosisCommand(newDiagnosis))
        Assert.assertEquals(newDiagnosis, diagnosis.category)
    }

    @Test
    fun testUpdateDifferentials_Add(){
        val differential = "Test"
        Assert.assertEquals(0, diagnosis.differentials.size)
        handle(buildUpdateDifferentialsCommand(differential, true))
        Assert.assertEquals("Test", diagnosis.differentials[0])
    }

    @Test
    fun testUpdateDifferentials_Remove(){
        val differential = "Test"
        diagnosis.differentials += differential
        diagnosis.selectedProtocol = differential
        Assert.assertEquals("Test", diagnosis.differentials[0])
        handle(buildUpdateDifferentialsCommand(differential, false))
        Assert.assertEquals(0, diagnosis.differentials.size)
        Assert.assertNull(diagnosis.selectedProtocol)
    }

    @Test
    fun testSelectedProtocol_Value(){
        val newProtocol = "Protocol"
        Assert.assertNull(diagnosis.selectedProtocol)
        handle(buildSelectProtocolCommand(newProtocol))
        Assert.assertEquals(newProtocol, diagnosis.selectedProtocol)
    }
    @Test
    fun testSelectedProtocol_Clear(){
        val newProtocol = "Protocol"
        diagnosis.selectedProtocol = newProtocol
        Assert.assertEquals(newProtocol, diagnosis.selectedProtocol)
        handle(buildSelectProtocolCommand(null))
        Assert.assertNull(diagnosis.selectedProtocol)
    }
}