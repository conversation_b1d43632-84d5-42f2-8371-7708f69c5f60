package gov.afrl.batdok.encounter.treatments

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.DrawingPoint
import gov.afrl.batdok.encounter.Treatments
import gov.afrl.batdok.encounter.commands.buildUpdateTreatmentCommand
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.encounter.ids.TreatmentId
import gov.afrl.batdok.encounter.treatment.*
import gov.afrl.batdok.encounter.treatment.TubeData.BreathingConfirmation
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class UpdateTreatmentStateTest {

    private val documentId = DomainId.create<DocumentId>()
    private val treatments = Treatments()

    //region Helper Functions
    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS)) {
        treatments.handlers.handle(documentId, buildCommandData(subCommand, callsign, timestamp))
    }

    private fun <T: TreatmentData> testTreatment(
        name: String,
        data: T? = null,
        timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS),
        previousData: TreatmentData? = data,
        assertData: (result: T?) -> Unit = { Assert.assertNull(it) }
    ){
        val id = DomainId.create<TreatmentId>()
        val oldTimestamp = Instant.ofEpochMilli(123456789L)
        val treatment = Treatment(
            name = name,
            treatmentData = data,
            id = id,
            timestamp = timestamp,
            documentId = documentId
        )

        treatments += Treatment(
            name = name,
            treatmentData = previousData,
            id = id,
            timestamp = oldTimestamp,
            documentId = documentId
        )
        Assert.assertEquals(1, treatments.list.size)
        handle(buildUpdateTreatmentCommand(treatment, treatments[id]), timestamp = timestamp)
        Assert.assertEquals(1, treatments.list.size)

        val newTreatment = treatments.list[0]
        Assert.assertEquals(name, newTreatment.name)
        Assert.assertEquals(timestamp, newTreatment.timestamp)
        Assert.assertEquals(id, newTreatment.id)

        @Suppress("UNCHECKED_CAST") //We don't need the warning. If the cast fails, the test will fail.
        assertData(newTreatment.treatmentData as T?)
    }

    private fun <T: TreatmentData> testTreatment(
        name: CommonTreatments,
        data: T,
        timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS),
        previousData: TreatmentData? = null,
        assertData: (result: T) -> Unit
    ) = testTreatment(name.dataString, data, timestamp, previousData){
        Assert.assertNotNull(it)
        assertData(it!!)
    }

    private fun testTreatment(name: CommonTreatments, previousData: TreatmentData? = null) = testTreatment(name.dataString, null, previousData = previousData)
    //endregion

    //region TQ

    val oldTimestamp = Instant.ofEpochSecond(10000000)
    val newTimestamp = Instant.ofEpochSecond(20000000)

    val oldDrawingPoint = DrawingPoint("TQ1", .5f, .5f)
    val newDrawingPoint = DrawingPoint("TQ2", .5f, .75f)
    @Test
    fun testUpdateTreatmentCommand_TQWithExtras() = testTreatment(
        CommonTreatments.TQ,
        TqData(
            TqData.Location.EXTREMITY.dataString,
            tqType = "CAT5",
            subLocation = "LUE",
            reapplicationTime = newTimestamp,
            drawingPoint = newDrawingPoint
        )
    ){ data ->
        Assert.assertEquals("CAT5", data.tqType)
        Assert.assertEquals("Extremity", data.tqLocation)
        Assert.assertEquals("LUE", data.subLocation)
        Assert.assertEquals(newTimestamp, data.reapplicationTime)
        Assert.assertEquals(newDrawingPoint, data.drawingPoint)
    }

    @Test
    fun testUpdateTreatmentCommand_TQWithExtras_WithPreviousData() = testTreatment(
        CommonTreatments.TQ,
        TqData(
            TqData.Location.EXTREMITY.dataString,
            subLocation = "LUE",
            reapplicationTime = newTimestamp,
            conversionTime = oldTimestamp,
            drawingPoint = newDrawingPoint
        ),
        previousData = TqData(
            TqData.Location.EXTREMITY.dataString,
            tqType = "CAT4",
            subLocation = "RLE",
            reapplicationTime = oldTimestamp,
            conversionTime = oldTimestamp,
            drawingPoint = oldDrawingPoint
        )
    ){ data ->
        Assert.assertNull(data.tqType)
        Assert.assertEquals("Extremity", data.tqLocation)
        Assert.assertEquals("LUE", data.subLocation)
        Assert.assertEquals(newDrawingPoint, data.drawingPoint)
    }

    @Test
    fun testUpdateTreatmentCommand_TQWithoutExtras() = testTreatment(CommonTreatments.TQ)

    @Test
    fun testUpdateTreatmentCommand_TQWithoutExtras_ClearAllData() = testTreatment(
        CommonTreatments.TQ,
        previousData = TqData(
            TqData.Location.EXTREMITY.dataString,
            tqType = "CAT4",
            subLocation = "RLE",
            drawingPoint = oldDrawingPoint
        )
    )

    @Test
    fun testUpdateTreatmentCommand_TQWithoutExtras_ClearSomeData() = testTreatment(
        CommonTreatments.TQ,
        TqData(TqData.Location.EXTREMITY.dataString),
        previousData = TqData(TqData.Location.EXTREMITY.dataString,
            tqType = "CAT4",
            subLocation = "RLE",
            reapplicationTime = oldTimestamp)
    ){
        Assert.assertNull(it.tqType)
        Assert.assertNull(it.subLocation)
        Assert.assertNull(it.reapplicationTime)
    }

    //endregion

    //region Dressing

    @Test
    fun testUpdateTreatmentCommand_DressingWithExtras() = testTreatment(
        CommonTreatments.DRESSING,
        DressingData(DressingData.Type.ABDOMINAL.dataString, null, null)
    ){
        Assert.assertEquals("Abdominal", it.type)
    }

    @Test
    fun testUpdateTreatmentCommand_DressingWithExtras_WithPrevious() = testTreatment(
        CommonTreatments.DRESSING,
        DressingData(DressingData.Type.ABDOMINAL.dataString, null, null),
        previousData = DressingData(DressingData.Type.HEMOSTATIC.dataString, "Arm", DressingData.SubType.XSTAT.dataString)
    ){
        Assert.assertEquals("Abdominal", it.type)
    }

    @Test
    fun testUpdateTreatmentCommand_DressingWithoutExtras() = testTreatment(CommonTreatments.DRESSING)

    @Test
    fun testUpdateTreatmentCommand_DressingWithoutExtras_ClearData() = testTreatment(
        CommonTreatments.DRESSING,
        previousData = DressingData(DressingData.Type.HEMOSTATIC.dataString, "Arm", DressingData.SubType.XSTAT.dataString)
    )

    //endregion

    //region ET Tube

    @Test
    fun testUpdateTreatmentCommand_ETTubeWithExtras() = testTreatment(
        CommonTreatments.ET_TUBE,
        TubeData("Confirm", 10f, "cm", null, 20, "mm", "Location")
    ){
        Assert.assertEquals(10f, it.size)
        Assert.assertEquals("cm", it.sizeUnit)
        Assert.assertEquals(20, it.depth)
        Assert.assertEquals("mm", it.depthUnit)
        Assert.assertEquals("Location", it.location)
        Assert.assertEquals("Confirm", it.breathingConfirmation)
    }

    @Test
    fun testUpdateTreatmentCommand_ETTubeWithExtras_WithPrevious() = testTreatment(
        CommonTreatments.ET_TUBE,
        TubeData(BreathingConfirmation.ETCO2.dataString, 10f, null, null, 30, null, "Location"),
        previousData = TubeData("Confirm", 10f, null, null, 20, null, "Location"),
    ){
        Assert.assertEquals(10f, it.size)
        Assert.assertEquals(30, it.depth)
        Assert.assertEquals("Location", it.location)
        Assert.assertEquals("EtCO2", it.breathingConfirmation)
    }

    @Test
    fun testUpdateTreatmentCommand_ETTubeWithoutExtras() = testTreatment(CommonTreatments.ET_TUBE)

    @Test
    fun testUpdateTreatmentCommand_ETTubeWithoutExtras_ClearData() = testTreatment(
        CommonTreatments.ET_TUBE,
        previousData = TubeData("Confirm", 10f, null, null, 30, "Location")
    )

    //endregion

    //region Needle D

    @Test
    fun testUpdateTreatmentCommand_NeedleDWithoutExtras() = testTreatment(CommonTreatments.NEEDLE_D)

    @Test
    fun testUpdateTreatmentCommand_NeedleDWithoutExtras_ClearData() = testTreatment(
        CommonTreatments.NEEDLE_D,
        previousData = NeedleDData(NeedleDData.Location.L_MID_CLAV.dataString)
    )

    @Test
    fun testUpdateTreatmentCommand_NeedleDWithExtras() = testTreatment(
        CommonTreatments.NEEDLE_D,
        NeedleDData(NeedleDData.Location.L_MID_CLAV.dataString)
    ){
        Assert.assertEquals(NeedleDData.Location.L_MID_CLAV.dataString, it.location)
    }

    @Test
    fun testUpdateTreatmentCommand_NeedleDWithExtras_WithPrevious() = testTreatment(
        CommonTreatments.NEEDLE_D,
        NeedleDData(NeedleDData.Location.R_ANT_AX.dataString),
        previousData = NeedleDData(NeedleDData.Location.L_MID_CLAV.dataString)
    ){
        Assert.assertEquals(NeedleDData.Location.R_ANT_AX.dataString, it.location)
    }

    //endregion

    //region Finger Thor

    @Test
    fun testUpdateTreatmentCommand_FingerThorWithoutExtras() = testTreatment(CommonTreatments.FINGER_THOR)
    @Test
    fun testUpdateTreatmentCommand_FingerThorWithoutExtras_ClearData() = testTreatment(
        CommonTreatments.FINGER_THOR,
        previousData = FingerThorData(FingerThorData.Location.LEFT.dataString)
    )

    @Test
    fun testUpdateTreatmentCommand_FingerThorWithExtras() = testTreatment(
        CommonTreatments.FINGER_THOR,
        FingerThorData(FingerThorData.Location.LEFT.dataString)
    ){
        Assert.assertEquals(FingerThorData.Location.LEFT.dataString, it.location)
    }
    @Test
    fun testUpdateTreatmentCommand_FingerThorWithExtras_WithPrevious() = testTreatment(
        CommonTreatments.FINGER_THOR,
        FingerThorData(FingerThorData.Location.RIGHT.dataString),
        previousData = FingerThorData(FingerThorData.Location.LEFT.dataString)
    ){
        Assert.assertEquals(FingerThorData.Location.RIGHT.dataString, it.location)
    }

    //endregion

    //region Chest Tube

    @Test
    fun testUpdateTreatmentCommand_ChestTubeWithoutExtras() = testTreatment(CommonTreatments.CHEST_TUBE)
    @Test
    fun testUpdateTreatmentCommand_ChestTubeWithoutExtras_clearData() = testTreatment(
        CommonTreatments.CHEST_TUBE,
        previousData = ChestTubeData(ChestTubeData.Location.LEFT.dataString)
    )

    @Test
    fun testUpdateTreatmentCommand_ChestTubeWithExtras() = testTreatment(
        CommonTreatments.CHEST_TUBE,
        ChestTubeData(
            location = ChestTubeData.Location.LEFT.dataString,
            suction = true,
            suctionAmount = 1.4f,
            airLeak = true
        ),
    ){
        Assert.assertEquals(ChestTubeData.Location.LEFT.dataString, it.location)
        Assert.assertTrue(it.suction!!)
        Assert.assertEquals(1.4f, it.suctionAmount!!, 0.1f)
        Assert.assertTrue(it.airLeak!!)
    }

    @Test
    fun testUpdateTreatmentCommand_ChestTubeWithExtras_WithPrevious() = testTreatment(
        CommonTreatments.CHEST_TUBE,
        ChestTubeData(
            location = ChestTubeData.Location.LEFT.dataString,
            suction = true,
            suctionAmount = 1.4f,
            airLeak = true
        ),
        previousData = ChestTubeData(
            location = ChestTubeData.Location.RIGHT.dataString,
            suction = false,
            suctionAmount = 2.8f,
            airLeak = false
        ),
    ){
        Assert.assertEquals(ChestTubeData.Location.LEFT.dataString, it.location)
        Assert.assertTrue(it.suction!!)
        Assert.assertEquals(1.4f, it.suctionAmount!!, 0.1f)
        Assert.assertTrue(it.airLeak!!)
    }

    //endregion

    //region Chest Seal

    @Test
    fun testUpdateTreatmentCommand_ChestSealWithoutExtras() = testTreatment(CommonTreatments.CHEST_SEAL)

    @Test
    fun testUpdateTreatmentCommand_ChestSealWithoutExtras_clearData() = testTreatment(
        CommonTreatments.CHEST_SEAL,
        previousData = ChestSealData(ChestSealData.Location.LEFT_FRONT.dataString)
    )

    @Test
    fun testUpdateTreatmentCommand_ChestSealWithExtras() = testTreatment(
        CommonTreatments.CHEST_SEAL,
        ChestSealData(ChestSealData.Location.LEFT_FRONT.dataString)
    ){
        Assert.assertEquals(ChestSealData.Location.LEFT_FRONT.dataString, it.location)
    }

    @Test
    fun testUpdateTreatmentCommand_ChestSealWithExtras_WithPrevious() = testTreatment(
        CommonTreatments.CHEST_SEAL,
        ChestSealData(ChestSealData.Location.LEFT_FRONT.dataString),
        previousData = ChestSealData(ChestSealData.Location.RIGHT_BACK.dataString)
    ){
        Assert.assertEquals(ChestSealData.Location.LEFT_FRONT.dataString, it.location)
    }
    //endregion

    //region Eye Shield

    @Test
    fun testUpdateTreatmentCommand_EyeShieldWithoutExtras() = testTreatment(CommonTreatments.EYE_SHIELD)

    @Test
    fun testUpdateTreatmentCommand_EyeShieldWithoutExtras_clearData() = testTreatment(
        CommonTreatments.EYE_SHIELD,
        previousData = EyeShieldData(left = true, right = false)
    )

    @Test
    fun testUpdateTreatmentCommand_EyeShieldWithExtras() = testTreatment(
        CommonTreatments.EYE_SHIELD,
        EyeShieldData(left = true, right = false)
    ){
        Assert.assertTrue(it.left)
        Assert.assertFalse(it.right)
    }

    @Test
    fun testUpdateTreatmentCommand_EyeShieldWithExtras_WithPrevious() = testTreatment(
        CommonTreatments.EYE_SHIELD,
        EyeShieldData(left = true, right = true),
        previousData = EyeShieldData(left = true, right = false)
    ){
        Assert.assertTrue(it.left)
        Assert.assertTrue(it.right)
    }

    //endregion

    //region Splint

    @Test
    fun testUpdateTreatmentCommand_SplintWithoutExtras() = testTreatment(CommonTreatments.SPLINT)

    @Test
    fun testUpdateTreatmentCommand_SplintWithoutExtras_clearData() = testTreatment(
        CommonTreatments.SPLINT,
        previousData = SplintData(true, "Type")
    )

    @Test
    fun testUpdateTreatmentCommand_SplintWithExtras() = testTreatment(
        CommonTreatments.SPLINT,
        SplintData(true, "Type")
    ){
        Assert.assertTrue(it.pulsePresent?:false)
        Assert.assertEquals("Type", it.type)
    }

    @Test
    fun testUpdateTreatmentCommand_SplintWithExtras_WithPrevious() = testTreatment(
        CommonTreatments.SPLINT,
        SplintData(false, "Type"),
        previousData = SplintData(true, "test")
    ){
        Assert.assertFalse(it.pulsePresent?:false)
        Assert.assertEquals("Type", it.type)
    }

    //endregion

    //region O2

    @Test
    fun testUpdateTreatmentCommand_O2WithoutExtras() = testTreatment(CommonTreatments.O2)

    @Test
    fun testUpdateTreatmentCommand_O2WithoutExtras_clearData_Old() = testTreatment(
        CommonTreatments.O2,
        O2Data(10f, O2Data.DeliveryMethod.NRB.dataString)
    )

    @Test
    fun testUpdateTreatmentCommand_O2WithoutExtras_clearData() = testTreatment(
        CommonTreatments.O2,
        O2Data(lpm = 10f, targetSpO2 = 98, fiO2 = 13f, route = O2Data.O2Route.VENTURI_MASK.dataString)
    )

    @Test
    fun testUpdateTreatmentCommand_O2WithExtras_Old() = testTreatment(
        CommonTreatments.O2,
        O2Data(10f, O2Data.DeliveryMethod.NRB.dataString)
    ){
        Assert.assertEquals(10f, it.volume)
        Assert.assertEquals("NRB", it.deliveryMethod)
    }

    @Test
    fun testUpdateTreatmentCommand_O2WithExtras() = testTreatment(
        CommonTreatments.O2,
        O2Data(lpm = 10f, targetSpO2 = 98, fiO2 = 13f, route = O2Data.O2Route.VENTURI_MASK.dataString)
    ){
        Assert.assertEquals(10f, it.lpm)
        Assert.assertEquals(98, it.targetSpO2)
        Assert.assertEquals(13f, it.fiO2)
        Assert.assertEquals(O2Data.O2Route.VENTURI_MASK.dataString, it.route)
    }

    @Test
    fun testUpdateTreatmentCommand_O2WithExtras_WithPrevious_Old() = testTreatment(
        CommonTreatments.O2,
        O2Data(10f, O2Data.DeliveryMethod.NRB.dataString),
        previousData = O2Data(20f, O2Data.DeliveryMethod.NRB.dataString)
    ){
        Assert.assertEquals(10f, it.volume)
        Assert.assertEquals("NRB", it.deliveryMethod)
    }

    @Test
    fun testUpdateTreatmentCommand_O2WithExtras_WithPrevious() = testTreatment(
        CommonTreatments.O2,
        O2Data(lpm = 10f, targetSpO2 = 98, fiO2 = 13f, route = O2Data.O2Route.VENTURI_MASK.dataString),
        previousData = O2Data(lpm = 20f, targetSpO2 = 20, fiO2 = 20f, route = O2Data.O2Route.NRB.dataString)
    ){
        Assert.assertEquals(10f, it.lpm)
        Assert.assertEquals(98, it.targetSpO2)
        Assert.assertEquals(13f, it.fiO2)
        Assert.assertEquals(O2Data.O2Route.VENTURI_MASK.dataString, it.route)
    }

    //endregion

    //region CRIC

    @Test
    fun testUpdateTreatmentCommand_CricWithoutExtras() = testTreatment(CommonTreatments.CRIC)

    @Test
    fun testUpdateTreatmentCommand_CricWithoutExtras_ClearData() = testTreatment(
        CommonTreatments.CRIC,
        previousData = TubeData(breathingConfirmation = BreathingConfirmation.ETCO2.dataString, size = 10f)
    )

    @Test
    fun testUpdateTreatmentCommand_CricWithExtras() = testTreatment(
        CommonTreatments.CRIC,
        TubeData(breathingConfirmation = BreathingConfirmation.BREATH_SOUNDS.dataString, size = 10f)
    ){
        Assert.assertEquals(BreathingConfirmation.BREATH_SOUNDS.dataString, it.breathingConfirmation)
        Assert.assertEquals(10f, it.size)
    }

    @Test
    fun testUpdateTreatmentCommand_CricWithExtras_WithExtras() = testTreatment(
        CommonTreatments.CRIC,
        TubeData(breathingConfirmation = BreathingConfirmation.BREATH_SOUNDS.dataString, size = 10f),
        previousData = TubeData(breathingConfirmation = BreathingConfirmation.ETCO2.dataString, size = 10f)
    ){
        Assert.assertEquals(BreathingConfirmation.BREATH_SOUNDS.dataString, it.breathingConfirmation)
        Assert.assertEquals(10f, it.size)
    }

    //endregion

    //region Escharatomy

    @Test
    fun testUpdateTreatmentCommand_EscharatomyWithoutExtras() = testTreatment(CommonTreatments.ESCHARATOMY)

    @Test
    fun testUpdateTreatmentCommand_EscharatomyWithoutExtras_ClearData() = testTreatment(
        CommonTreatments.ESCHARATOMY,
        previousData = EscharatomyData("Previous Location")
    )

    @Test
    fun testUpdateTreatmentCommand_EscharatomyWithExtras() = testTreatment(
        CommonTreatments.ESCHARATOMY,
        EscharatomyData("Free text location")
    ){
        Assert.assertEquals("Free text location", it.location)
    }

    @Test
    fun testUpdateTreatmentCommand_EscharatomyWithExtras_withPrevious() = testTreatment(
        CommonTreatments.ESCHARATOMY,
        EscharatomyData("Free text location"),
        previousData = EscharatomyData("Previous Location")
    ){
        Assert.assertEquals("Free text location", it.location)
    }

    //endregion

    //region Debridement

    @Test
    fun testUpdateTreatmentCommand_DebridementWithoutExtras() = testTreatment(CommonTreatments.DEBRIDEMENT)

    @Test
    fun testUpdateTreatmentCommand_DebridementWithoutExtras_ClearData() = testTreatment(
        CommonTreatments.DEBRIDEMENT,
        previousData = DebridementData("Previous Location")
    )

    @Test
    fun testUpdateTreatmentCommand_DebridementWithExtras() = testTreatment(
        CommonTreatments.DEBRIDEMENT,
        DebridementData("Free text location")
    ){
        Assert.assertEquals("Free text location", it.location)
    }

    @Test
    fun testUpdateTreatmentCommand_DebridementWithExtras_WithPrevious() = testTreatment(
        CommonTreatments.DEBRIDEMENT,
        DebridementData("Free text location"),
        previousData = DebridementData("Previous Location")
    ){
        Assert.assertEquals("Free text location", it.location)
    }

    //endregion

    //region GastricTube

    @Test
    fun testUpdateTreatmentCommand_GastricTubeWithoutExtras() = testTreatment(CommonTreatments.GASTRIC_TUBE)

    @Test
    fun testUpdateTreatmentCommand_GastricTubeWithoutExtras_ClearData() = testTreatment(
        CommonTreatments.GASTRIC_TUBE,
        previousData = GastricTubeData(
            GastricTubeData.Type.ORAL.dataString,
            GastricTubeData.Side.RIGHT.dataString,
            GastricTubeData.SuctionType.GRAVITY.dataString,
            GastricTubeData.Interval.INTERMITTENT.dataString
        )
    )

    @Test
    fun testUpdateTreatmentCommand_GastricTubeWithExtras() = testTreatment(
        CommonTreatments.GASTRIC_TUBE,
        GastricTubeData(
            GastricTubeData.Type.NASAL.dataString,
            GastricTubeData.Side.LEFT.dataString,
            GastricTubeData.SuctionType.SUCTION.dataString,
            GastricTubeData.Interval.CONTINUOUS.dataString
        )
    ){
        Assert.assertEquals(GastricTubeData.Type.NASAL.dataString, it.type)
        Assert.assertEquals(GastricTubeData.Side.LEFT.dataString, it.side)
        Assert.assertEquals(GastricTubeData.SuctionType.SUCTION.dataString, it.suctionType)
        Assert.assertEquals(GastricTubeData.Interval.CONTINUOUS.dataString, it.interval)
    }

    @Test
    fun testUpdateTreatmentCommand_GastricTubeWithExtras_WithPrevious() = testTreatment(
        CommonTreatments.GASTRIC_TUBE,
        GastricTubeData(
            GastricTubeData.Type.NASAL.dataString,
            GastricTubeData.Side.LEFT.dataString,
            GastricTubeData.SuctionType.SUCTION.dataString,
            GastricTubeData.Interval.CONTINUOUS.dataString
        ),
        previousData = GastricTubeData(
            GastricTubeData.Type.ORAL.dataString,
            GastricTubeData.Side.RIGHT.dataString,
            GastricTubeData.SuctionType.GRAVITY.dataString,
            GastricTubeData.Interval.INTERMITTENT.dataString
        )
    ){
        Assert.assertEquals(GastricTubeData.Type.NASAL.dataString, it.type)
        Assert.assertEquals(GastricTubeData.Side.LEFT.dataString, it.side)
        Assert.assertEquals(GastricTubeData.SuctionType.SUCTION.dataString, it.suctionType)
        Assert.assertEquals(GastricTubeData.Interval.CONTINUOUS.dataString, it.interval)
    }

    //endregion

    //region Lateral Canthotomy

    @Test
    fun testUpdateTreatmentCommand_LateralCanthotomyWithoutExtras() = testTreatment(CommonTreatments.LATERAL_CANTHOTOMY)

    @Test
    fun testUpdateTreatmentCommand_LateralCanthotomyWithoutExtras_ClearData() = testTreatment(
        CommonTreatments.LATERAL_CANTHOTOMY,
        previousData = LateralCanthotomyData(LateralCanthotomyData.Location.RIGHT.dataString)
    )

    @Test
    fun testUpdateTreatmentCommand_LateralCanthotomyWithExtras() = testTreatment(
        CommonTreatments.LATERAL_CANTHOTOMY,
        LateralCanthotomyData(LateralCanthotomyData.Location.LEFT.dataString)
    ){
        Assert.assertEquals(LateralCanthotomyData.Location.LEFT.dataString, it.location)
    }

    @Test
    fun testUpdateTreatmentCommand_LateralCanthotomyWithExtras_WithPrevious() = testTreatment(
        CommonTreatments.LATERAL_CANTHOTOMY,
        LateralCanthotomyData(LateralCanthotomyData.Location.LEFT.dataString),
        previousData = LateralCanthotomyData(LateralCanthotomyData.Location.RIGHT.dataString)
    ){
        Assert.assertEquals(LateralCanthotomyData.Location.LEFT.dataString, it.location)
    }

    //endregion

    //region Fasciotomy

    @Test
    fun testUpdateTreatmentCommand_FasciotomyWithoutExtras() = testTreatment(CommonTreatments.FASCIOTOMY)

    @Test
    fun testUpdateTreatmentCommand_FasciotomyWithoutExtras_ClearData() = testTreatment(
        CommonTreatments.FASCIOTOMY,
        previousData = FasciotomyData("Previous Location")
    )

    @Test
    fun testUpdateTreatmentCommand_FasciotomyWithExtras() = testTreatment(
        CommonTreatments.FASCIOTOMY,
        FasciotomyData("Free text Location")
    ){
        Assert.assertEquals("Free text Location", it.location)
    }

    @Test
    fun testUpdateTreatmentCommand_FasciotomyWithExtras_WithPrevious() = testTreatment(
        CommonTreatments.FASCIOTOMY,
        FasciotomyData("Free text Location"),
        previousData = FasciotomyData("Previous Location")
    ){
        Assert.assertEquals("Free text Location", it.location)
    }

    //endregion

    //region Foley Catheter

    @Test
    fun testUpdateTreatmentCommand_FoleyCatheterWithoutExtras() = testTreatment(CommonTreatments.FOLEY_CATHETER)

    @Test
    fun testUpdateTreatmentCommand_FoleyCatheterWithoutExtras_ClearData() = testTreatment(
        CommonTreatments.FOLEY_CATHETER,
        previousData = FoleyCatheterData(
            20f,
            FoleyCatheterData.Color.RED.dataString,
            "PrevChar",
            "PrevAssess"
        )
    )

    @Test
    fun testUpdateTreatmentCommand_FoleyCatheterWithExtras() = testTreatment(
        CommonTreatments.FOLEY_CATHETER,
        FoleyCatheterData(
            10f,
            "cm",
            FoleyCatheterData.Color.BLUE.dataString,
            "Character",
            "Assess"
        )
    ){
        Assert.assertEquals(10f, it.size)
        Assert.assertEquals("cm", it.sizeUnit)
        Assert.assertEquals("Blue", it.color)
        Assert.assertEquals("Character", it.character)
        Assert.assertEquals("Assess", it.assess)
    }

    @Test
    fun testUpdateTreatmentCommand_FoleyCatheterWithExtras_WithPrevious() = testTreatment(
        CommonTreatments.FOLEY_CATHETER,
        FoleyCatheterData(
            10f,
            "G",
            FoleyCatheterData.Color.BLUE.dataString,
            "Character",
            "Assess"
        ),
        previousData = FoleyCatheterData(
            20f,
            "mm",
            FoleyCatheterData.Color.BLUE.dataString,
            "Character",
            "PrevAssess"
        )
    ){
        Assert.assertEquals(10f, it.size)
        Assert.assertEquals("G", it.sizeUnit)
        Assert.assertEquals("Blue", it.color)
        Assert.assertEquals("Character", it.character)
        Assert.assertEquals("Assess", it.assess)
    }

    //endregion

    //region Direct Pressure

    @Test
    fun testUpdateTreatmentCommand_DirectPressureWithoutExtras() = testTreatment(CommonTreatments.DIRECT_PRESSURE)
    @Test
    fun testUpdateTreatmentCommand_DirectPressureWithoutExtras_ClearData() = testTreatment(
        CommonTreatments.DIRECT_PRESSURE,
        previousData = DirectPressureData("Previous Location")
    )

    @Test
    fun testUpdateTreatmentCommand_DirectPressureWithExtras() = testTreatment(
        CommonTreatments.DIRECT_PRESSURE,
        DirectPressureData("Free text Location")
    ){
        Assert.assertEquals("Free text Location", it.location)
    }

    @Test
    fun testUpdateTreatmentCommand_DirectPressureWithExtras_WithPrevious() = testTreatment(
        CommonTreatments.DIRECT_PRESSURE,
        DirectPressureData("Free text Location"),
        previousData = DirectPressureData("Previous Location")
    ){
        Assert.assertEquals("Free text Location", it.location)
    }

    //endregion

    //region Foreign Body Removal

    @Test
    fun testUpdateTreatmentCommand_ForeignBodyRemovalWithoutExtras() = testTreatment(CommonTreatments.FOREIGN_BODY_REMOVAL)

    @Test
    fun testUpdateTreatmentCommand_ForeignBodyRemovalWithoutExtras_ClearData() = testTreatment(
        CommonTreatments.FOREIGN_BODY_REMOVAL,
        previousData = ForeignBodyRemovalData("Previous Location")
    )

    @Test
    fun testUpdateTreatmentCommand_ForeignBodyRemovalWithExtras() = testTreatment(
        CommonTreatments.FOREIGN_BODY_REMOVAL,
        ForeignBodyRemovalData("Free text Location")
    ){
        Assert.assertEquals("Free text Location", it.location)
    }

    @Test
    fun testUpdateTreatmentCommand_ForeignBodyRemovalWithExtras_WithPrevious() = testTreatment(
        CommonTreatments.FOREIGN_BODY_REMOVAL,
        ForeignBodyRemovalData("Free text Location"),
        previousData = ForeignBodyRemovalData("Previous Location")
    ){
        Assert.assertEquals("Free text Location", it.location)
    }

    //endregion

    //region NPA

    @Test
    fun testUpdateTreatmentCommand_NPAWithoutExtras() = testTreatment(CommonTreatments.NPA)

    @Test
    fun testUpdateTreatmentCommand_NPAWithoutExtras_ClearData() = testTreatment(
        CommonTreatments.NPA,
        previousData = TubeData(BreathingConfirmation.BREATH_SOUNDS.dataString, 10f)
    )

    @Test
    fun testUpdateTreatmentCommand_NPAWithExtras() = testTreatment(
        CommonTreatments.NPA,
        TubeData(BreathingConfirmation.BREATH_SOUNDS.dataString, 10f)
    ){
        Assert.assertEquals(BreathingConfirmation.BREATH_SOUNDS.dataString, it.breathingConfirmation)
        Assert.assertEquals(10f, it.size)
    }

    @Test
    fun testUpdateTreatmentCommand_NPAWithExtras_WithPrevious() = testTreatment(
        CommonTreatments.NPA,
        TubeData(BreathingConfirmation.BREATH_SOUNDS.dataString, 10f),
        previousData = TubeData(BreathingConfirmation.BREATH_SOUNDS.dataString, 20f)
    ){
        Assert.assertEquals(BreathingConfirmation.BREATH_SOUNDS.dataString, it.breathingConfirmation)
        Assert.assertEquals(10f, it.size)
    }

    //endregion

    //region OPA

    @Test
    fun testUpdateTreatmentCommand_OPAWithoutExtras() = testTreatment(CommonTreatments.OPA)

    @Test
    fun testUpdateTreatmentCommand_OPAWithoutExtras_ClearData() = testTreatment(
        CommonTreatments.OPA,
        previousData = TubeData(BreathingConfirmation.BREATH_SOUNDS.dataString, 10f)
    )

    @Test
    fun testUpdateTreatmentCommand_OPAWithExtras() = testTreatment(
        CommonTreatments.OPA,
        TubeData(BreathingConfirmation.BREATH_SOUNDS.dataString, 10f)
    ){
        Assert.assertEquals(BreathingConfirmation.BREATH_SOUNDS.dataString, it.breathingConfirmation)
        Assert.assertEquals(10f, it.size)
    }

    @Test
    fun testUpdateTreatmentCommand_OPAWithExtras_WithPrevious() = testTreatment(
        CommonTreatments.OPA,
        TubeData(BreathingConfirmation.BREATH_SOUNDS.dataString, 10f),
        previousData = TubeData(BreathingConfirmation.ETCO2.dataString, 10f)
    ){
        Assert.assertEquals(BreathingConfirmation.BREATH_SOUNDS.dataString, it.breathingConfirmation)
        Assert.assertEquals(10f, it.size)
    }

    //endregion

    //region SGA

    @Test
    fun testUpdateTreatmentCommand_SGAWithoutExtras() = testTreatment(CommonTreatments.SGA)

    @Test
    fun testUpdateTreatmentCommand_SGAWithoutExtras_ClearData() = testTreatment(
        CommonTreatments.SGA,
        previousData = TubeData(BreathingConfirmation.BREATH_SOUNDS.dataString, 10f, "PrevType")
    )

    @Test
    fun testUpdateTreatmentCommand_SGAWithExtras() = testTreatment(
        CommonTreatments.SGA,
        TubeData(BreathingConfirmation.BREATH_SOUNDS.dataString, 10f, "mm", "SGA Type")
    ){
        Assert.assertEquals(BreathingConfirmation.BREATH_SOUNDS.dataString, it.breathingConfirmation)
        Assert.assertEquals(10f, it.size)
        Assert.assertEquals("mm", it.sizeUnit)
        Assert.assertEquals("SGA Type", it.type)
    }

    @Test
    fun testUpdateTreatmentCommand_SGAWithExtras_WithPrevious() = testTreatment(
        CommonTreatments.SGA,
        TubeData(BreathingConfirmation.BREATH_SOUNDS.dataString, 10f, "mm", "SGA Type"),
        previousData = TubeData(BreathingConfirmation.BREATH_SOUNDS.dataString, 20f, "cm", "PrevType")
    ){
        Assert.assertEquals(BreathingConfirmation.BREATH_SOUNDS.dataString, it.breathingConfirmation)
        Assert.assertEquals(10f, it.size)
        Assert.assertEquals("mm", it.sizeUnit)
        Assert.assertEquals("SGA Type", it.type)
    }

    //endregion

    //region Trach

    @Test
    fun testUpdateTreatmentCommand_TrachWithoutExtras() = testTreatment(CommonTreatments.TRACH)

    @Test
    fun testUpdateTreatmentCommand_TrachWithoutExtras_ClearData() = testTreatment(
        CommonTreatments.TRACH,
        previousData = TubeData(BreathingConfirmation.BREATH_SOUNDS.dataString, 10f)
    )

    @Test
    fun testUpdateTreatmentCommand_TrachWithExtras() = testTreatment(
        CommonTreatments.TRACH,
        TubeData(BreathingConfirmation.BREATH_SOUNDS.dataString, 10f)
    ){
        Assert.assertEquals(BreathingConfirmation.BREATH_SOUNDS.dataString, it.breathingConfirmation)
        Assert.assertEquals(10f, it.size)
    }

    @Test
    fun testUpdateTreatmentCommand_TrachWithExtras_WithPrevious() = testTreatment(
        CommonTreatments.TRACH,
        TubeData(BreathingConfirmation.BREATH_SOUNDS.dataString, 10f),
        previousData = TubeData(BreathingConfirmation.ETCO2.dataString, 10f)
    ){
        Assert.assertEquals(BreathingConfirmation.BREATH_SOUNDS.dataString, it.breathingConfirmation)
        Assert.assertEquals(10f, it.size)
    }

    //endregion

    //region Hypothermia Prevention

    @Test
    fun testUpdateTreatmentCommand_HypothermiaPreventionWithoutExtras() = testTreatment(CommonTreatments.HYPOTHERMIA_PREVENTION)

    @Test
    fun testUpdateTreatmentCommand_HypothermiaPreventionWithoutExtras_ClearData() = testTreatment(
        CommonTreatments.HYPOTHERMIA_PREVENTION,
        previousData = HypothermiaPreventionData(HypothermiaPreventionData.Type.BLIZZARD_BLANKET.dataString)
    )

    @Test
    fun testUpdateTreatmentCommand_HypothermiaPreventionWithExtras() = testTreatment(
        CommonTreatments.HYPOTHERMIA_PREVENTION,
        HypothermiaPreventionData(HypothermiaPreventionData.Type.BLANKET.dataString)
    ){
        Assert.assertEquals(HypothermiaPreventionData.Type.BLANKET.dataString, it.type)
    }

    @Test
    fun testUpdateTreatmentCommand_HypothermiaPreventionWithExtras_WithPrevious() = testTreatment(
        CommonTreatments.HYPOTHERMIA_PREVENTION,
        HypothermiaPreventionData(HypothermiaPreventionData.Type.BLANKET.dataString),
        previousData = HypothermiaPreventionData(HypothermiaPreventionData.Type.BLIZZARD_BLANKET.dataString)
    ){
        Assert.assertEquals(HypothermiaPreventionData.Type.BLANKET.dataString, it.type)
    }

    //endregion

    //region Immobilization

    @Test
    fun testUpdateTreatmentCommand_ImmobilizationWithoutExtras() = testTreatment(CommonTreatments.IMMOBILIZATION)

    @Test
    fun testUpdateTreatmentCommand_ImmobilizationWithoutExtras_ClearData() = testTreatment(
        CommonTreatments.IMMOBILIZATION,
        previousData = ImmobilizationData(ImmobilizationData.Type.SPINE_BOARD.dataString, "Subtype")
    )

    @Test
    fun testUpdateTreatmentCommand_ImmobilizationWithExtras() = testTreatment(
        CommonTreatments.IMMOBILIZATION,
        ImmobilizationData(ImmobilizationData.Type.C_COLLAR.dataString, "Subtype")
    ){
        Assert.assertEquals(ImmobilizationData.Type.C_COLLAR.dataString, it.type)
        Assert.assertEquals(null, it.subtype)
    }

    @Test
    fun testUpdateTreatmentCommand_ImmobilizationWithExtras_WithPrevious() = testTreatment(
        CommonTreatments.IMMOBILIZATION,
        ImmobilizationData(ImmobilizationData.Type.C_COLLAR.dataString, "Subtype"),
        previousData = ImmobilizationData(ImmobilizationData.Type.SPINE_BOARD.dataString, "old")
    ){
        Assert.assertEquals(ImmobilizationData.Type.C_COLLAR.dataString, it.type)
        Assert.assertEquals(null, it.subtype)
    }

    //endregion

    //region Suction

    @Test
    fun testUpdateTreatmentCommand_SuctionWithoutExtras() = testTreatment(CommonTreatments.SUCTION)

    @Test
    fun testUpdateTreatmentCommand_SuctionWithoutExtras_ClearData() = testTreatment(
        CommonTreatments.SUCTION,
        previousData = SuctionData(
            SuctionData.Tool.CATHETER.dataString,
            "Free Text Location"
        )
    )

    @Test
    fun testUpdateTreatmentCommand_SuctionWithExtras() = testTreatment(
        CommonTreatments.SUCTION,
        SuctionData(
            SuctionData.Tool.YANKAUER.dataString,
            "Free Text Location"
        )
    ){
        Assert.assertEquals(SuctionData.Tool.YANKAUER.dataString, it.tool)
        Assert.assertEquals("Free Text Location", it.location)
    }

    @Test
    fun testUpdateTreatmentCommand_SuctionWithExtras_WithPrevious() = testTreatment(
        CommonTreatments.SUCTION,
        SuctionData(
            SuctionData.Tool.YANKAUER.dataString,
            "Free Text Location"
        ),
        previousData = SuctionData(
            SuctionData.Tool.CATHETER.dataString,
            "Free Text Location"
        )
    ){
        Assert.assertEquals(SuctionData.Tool.YANKAUER.dataString, it.tool)
        Assert.assertEquals("Free Text Location", it.location)
    }

    //endregion

    //region Intubated By

    @Test
    fun testUpdateTreatmentCommand_IntubatedByWithoutExtras() = testTreatment(CommonTreatments.INTUBATED_BY)

    @Test
    fun testUpdateTreatmentCommand_IntubatedByWithoutExtras_ClearData() = testTreatment(
        CommonTreatments.INTUBATED_BY,
        previousData = IntubatedByData("PrevIntubater")
    )

    @Test
    fun testUpdateTreatmentCommand_IntubatedByWithExtras() = testTreatment(
        CommonTreatments.INTUBATED_BY,
        IntubatedByData("Intubater")
    ){
        Assert.assertEquals("Intubater", it.intubater)
    }

    @Test
    fun testUpdateTreatmentCommand_IntubatedByWithExtras_WithPrevious() = testTreatment(
        CommonTreatments.INTUBATED_BY,
        IntubatedByData("Intubater"),
        previousData = IntubatedByData("PrevIntubater")
    ){
        Assert.assertEquals("Intubater", it.intubater)
    }

    //endregion
    // region Pelvic Binder
    @Test
    fun testUpdateTreatmentCommand_PelvicBinder() = testTreatment(CommonTreatments.PELVIC_BINDER)

    @Test
    fun testUpdateTreatmentCommand_PelvicBinder_ClearData() = testTreatment(
        CommonTreatments.PELVIC_BINDER,
        previousData = PelvicBinderData("PrevType")
    )
    @Test
    fun testUpdateTreatmentCommand_PelvicBinder_WithData() = testTreatment(
        CommonTreatments.PELVIC_BINDER,
        PelvicBinderData("Type")
    ){
        Assert.assertEquals("Type", it.type)
    }

    @Test
    fun testUpdateTreatmentCommand_PelvicBinder_ClearData_WithData() = testTreatment(
        CommonTreatments.PELVIC_BINDER,
        PelvicBinderData("Type"),
        previousData = PelvicBinderData("PrevType")
    ){
        Assert.assertEquals("Type", it.type)
    }
    //endregion

    //region Wound Packing

    @Test
    fun testUpdateTreatmentCommand_WoundPackingWithoutExtras() = testTreatment(CommonTreatments.WOUND_PACKING)

    @Test
    fun testUpdateTreatmentCommand_WoundPackingWithoutExtras_ClearData() = testTreatment(
        CommonTreatments.WOUND_PACKING,
        previousData = WoundPackingData("location")
    )

    @Test
    fun testUpdateTreatmentCommand_WoundPackingWithExtras() = testTreatment(
        CommonTreatments.WOUND_PACKING,
        WoundPackingData("location", "type")
    ){
        Assert.assertEquals("location", it.location)
        Assert.assertEquals("type", it.type)
    }

    @Test
    fun testUpdateTreatmentCommand_WoundPackingWithExtras_WithPrevious() = testTreatment(
        CommonTreatments.WOUND_PACKING,
        WoundPackingData("location", "type"),
        previousData = WoundPackingData("prevLocation", "prevType")
    ){
        Assert.assertEquals("location", it.location)
        Assert.assertEquals("type", it.type)
    }

    @Test
    fun testUpdateTreatmentCommand_WoundPackingWithExtras_WithPrevious_Clear() = testTreatment(
        CommonTreatments.WOUND_PACKING,
        WoundPackingData(null, null),
        previousData = WoundPackingData("prevLocation", "prevType")
    ){
        Assert.assertEquals(null, it.location)
        Assert.assertEquals(null, it.type)
    }

    //endregion
}