package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.updateIOItem
import gov.afrl.batdok.encounter.commands.buildAddIntakeOutputItemCommand
import gov.afrl.batdok.encounter.commands.buildRemoveIOItem
import gov.afrl.batdok.encounter.commands.buildUpdateIOItemCommand
import gov.afrl.batdok.encounter.commands.buildUpdateIntakeOutputItemCommand
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.encounter.ids.IntakeOutputId
import gov.afrl.batdok.encounter.ids.toByteString
import gov.afrl.batdok.util.buildCommandData
import gov.afrl.batdok.util.buildDocumentCommand
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class IntakeOutputStateTest {

    val docId = DomainId.create<DocumentId>()
    val intakeOutputs = IntakeOutputs()

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS), commandId: CommandId = DomainId.create()) {
        intakeOutputs.handlers.handle(docId, buildCommandData(subCommand, callsign, timestamp, commandId = commandId))
    }

    @Test
    fun testAddIOItem_input(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val itemId = DomainId.create<IntakeOutputId>()
        val item = IntakeOutput(
            itemId,
            timestamp,
            IntakeOutputType.ORAL.dataString,
            true,
            "label",
            1.0,
            IntakeOutputUnit.CC.dataString
        )
        Assert.assertEquals(0, intakeOutputs.inputs.size)
        Assert.assertEquals(0, intakeOutputs.outputs.size)
        handle(buildAddIntakeOutputItemCommand(item))
        Assert.assertEquals(1, intakeOutputs.inputs.size)
        Assert.assertEquals(0, intakeOutputs.outputs.size)
        Assert.assertEquals(itemId, intakeOutputs.inputs[0].id)
        val intakeOutput = intakeOutputs[itemId]
        Assert.assertNotNull(intakeOutput)
        Assert.assertEquals(timestamp, intakeOutput?.timestamp)
        if (intakeOutput != null) {
            Assert.assertEquals(1.0, intakeOutput.volume, 1.0)
        }
        Assert.assertEquals("cc", intakeOutput?.unit)
        Assert.assertEquals(docId, intakeOutputs.inputs[0].documentId)
    }

    @Test
    fun testAddIOItem_output(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val itemId = DomainId.create<IntakeOutputId>()
        val item = IntakeOutput(
            itemId,
            timestamp,
            IntakeOutputType.URINE.dataString,
            false,
            "label",
            1.0,
            IntakeOutputUnit.CC.dataString
        )
        Assert.assertEquals(0, intakeOutputs.inputs.size)
        Assert.assertEquals(0, intakeOutputs.outputs.size)
        handle(buildAddIntakeOutputItemCommand(item))
        Assert.assertEquals(0, intakeOutputs.inputs.size)
        Assert.assertEquals(1, intakeOutputs.outputs.size)
        Assert.assertEquals(itemId, intakeOutputs.outputs[0].id)
        val intakeOutput = intakeOutputs[itemId]
        Assert.assertNotNull(intakeOutput)
        Assert.assertEquals(timestamp, intakeOutput?.timestamp)
        if (intakeOutput != null) {
            Assert.assertEquals(1.0, intakeOutput.volume, 1.0)
        }
        Assert.assertEquals("cc", intakeOutput?.unit)
        Assert.assertEquals(docId, intakeOutputs.outputs[0].documentId)
    }

    @Test
    fun testUpdateIOItem_Output(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val itemId = DomainId.create<IntakeOutputId>()
        val item = IntakeOutput(
            itemId,
            timestamp,
            IntakeOutputType.URINE.dataString,
            false,
            "label",
            1.0,
            IntakeOutputUnit.CC.dataString
        )
        intakeOutputs += IntakeOutput(
            itemId,
            timestamp,
            IntakeOutputType.URINE.dataString,
            false,
            "label",
            2.0,
            IntakeOutputUnit.CC.dataString
        )
        Assert.assertEquals(0, intakeOutputs.inputs.size)
        Assert.assertEquals(1, intakeOutputs.outputs.size)
        handle(buildUpdateIntakeOutputItemCommand(item, false))
        Assert.assertEquals(0, intakeOutputs.inputs.size)
        Assert.assertEquals(1, intakeOutputs.outputs.size)
        Assert.assertEquals(itemId, intakeOutputs.outputs[0].id)
        val intakeOutput = intakeOutputs[itemId]
        Assert.assertNotNull(intakeOutput)
        Assert.assertEquals(timestamp, intakeOutput?.timestamp)
        if (intakeOutput != null) {
            Assert.assertEquals(1.0, intakeOutput.volume, 0.001)
        }
        Assert.assertEquals("cc", intakeOutput?.unit)
        Assert.assertEquals(docId, intakeOutputs.outputs[0].documentId)
    }

    @Test
    fun testUpdateIOItem_Intake(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val itemId = DomainId.create<IntakeOutputId>()
        val item = IntakeOutput(
            itemId,
            timestamp,
            IntakeOutputType.ORAL.dataString,
            true,
            "label",
            1.0,
            IntakeOutputUnit.CC.dataString
        )
        intakeOutputs += IntakeOutput(
            itemId,
            timestamp,
            IntakeOutputType.ORAL.dataString,
            true,
            "label",
            2.0,
            IntakeOutputUnit.CC.dataString
        )
        Assert.assertEquals(1, intakeOutputs.inputs.size)
        Assert.assertEquals(0, intakeOutputs.outputs.size)
        handle(buildUpdateIntakeOutputItemCommand(item, true))
        Assert.assertEquals(1, intakeOutputs.inputs.size)
        Assert.assertEquals(0, intakeOutputs.outputs.size)
        Assert.assertEquals(itemId, intakeOutputs.inputs[0].id)
        val intakeOutput = intakeOutputs[itemId]
        Assert.assertNotNull(intakeOutput)
        Assert.assertEquals(timestamp, intakeOutput?.timestamp)
        if (intakeOutput != null) {
            Assert.assertEquals(1.0, intakeOutput.volume, 0.001)
        }
        Assert.assertEquals("cc", intakeOutput?.unit)
        Assert.assertEquals(docId, intakeOutputs.inputs[0].documentId)
    }

    @Test
    fun testUpdateIOItem_output_someNewFields(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val itemId = DomainId.create<IntakeOutputId>()
        val item = IntakeOutput(
            itemId,
            timestamp,
            IntakeOutputType.URINE.dataString,
            false,
            "label",
            1.0,
            IntakeOutputUnit.CC.dataString
        )
        val oldItem = IntakeOutput(
            itemId,
            timestamp,
            IntakeOutputType.EMESIS.dataString,
            false,
            "label",
            1.0,
            IntakeOutputUnit.CC.dataString
        )
        intakeOutputs += oldItem
        Assert.assertEquals(0, intakeOutputs.inputs.size)
        Assert.assertEquals(1, intakeOutputs.outputs.size)
        handle(buildUpdateIntakeOutputItemCommand(item, false, oldItem))
        Assert.assertEquals(0, intakeOutputs.inputs.size)
        Assert.assertEquals(1, intakeOutputs.outputs.size)
        Assert.assertEquals(itemId, intakeOutputs.outputs[0].id)
        val intakeOutput = intakeOutputs[itemId]
        Assert.assertNotNull(intakeOutput)
        Assert.assertEquals(timestamp, intakeOutput?.timestamp)
        if (intakeOutput != null) {
            Assert.assertEquals(1.0, intakeOutput.volume, 0.001)
        }
        Assert.assertEquals("cc", intakeOutput?.unit)
        Assert.assertEquals(docId, intakeOutputs.outputs[0].documentId)
    }


    @Test
    fun testRemoveIOItem_input(){
        val itemId = DomainId.create<IntakeOutputId>()
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        intakeOutputs += IntakeOutput(
            itemId,
            timestamp,
            IntakeOutputType.ORAL.dataString,
            true,
            "label",
            1.0,
            IntakeOutputUnit.CC.dataString
        )
        Assert.assertEquals(1, intakeOutputs.inputs.size)
        Assert.assertEquals(0, intakeOutputs.outputs.size)
        handle(buildRemoveIOItem(itemId))
        Assert.assertEquals(1, intakeOutputs.inputs.size)
        Assert.assertEquals(0, intakeOutputs.outputs.size)
    }

    @Test
    fun testRemoveIOItem_output(){
        val itemId = DomainId.create<IntakeOutputId>()
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        intakeOutputs += IntakeOutput(
            itemId,
            timestamp,
            IntakeOutputType.URINE.dataString,
            false,
            "label",
            1.0,
            IntakeOutputUnit.CC.dataString
        )
        Assert.assertEquals(0, intakeOutputs.inputs.size)
        Assert.assertEquals(1, intakeOutputs.outputs.size)
        handle(buildRemoveIOItem(itemId))
        Assert.assertEquals(0, intakeOutputs.inputs.size)
        Assert.assertEquals(1, intakeOutputs.outputs.size)
    }

    @Test
    fun testAddWithId(){
        val itemId = DomainId.create<IntakeOutputId>()
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val doc = Document()

        val item = IntakeOutput(
            itemId,
            timestamp,
            IntakeOutputType.ORAL.dataString,
            true,
            "label",
            1.0,
            IntakeOutputUnit.CC.dataString
        )

        doc.handle(
            listOf(
                buildDocumentCommand(
                    DomainId.create(),
                    buildCommandData(buildAddIntakeOutputItemCommand(item))
                ),
            )
        )
        Assert.assertEquals(itemId, doc.intakeOutputs.inputs.first().id)
    }
}