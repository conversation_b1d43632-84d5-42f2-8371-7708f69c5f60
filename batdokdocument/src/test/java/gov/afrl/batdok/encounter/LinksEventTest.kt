package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.Links.Companion.includeLinkEvents
import gov.afrl.batdok.encounter.commands.buildAddRemoveFromLinkCommand
import gov.afrl.batdok.encounter.commands.buildCreateLinkCommand
import gov.afrl.batdok.encounter.commands.buildDeleteLinkCommand
import gov.afrl.batdok.encounter.commands.buildUpdateLinkCommentCommand
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class LinksEventTest {

    private val document = Document()
    private val events = document.events

    private fun handle(
        subCommand: Message,
        callsign: String = "CS",
        timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS),
        commandId: CommandId = DomainId.create()
    ) {
        EventCommandHandler(events) {
            includeLinkEvents(document)
        }.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp, commandId))
    }

    /**
     * No Event is created for a link
     */
    @Test
    fun testCreateLinkCommand(){
        Assert.assertEquals(0, events.list.size)
        handle(buildCreateLinkCommand(Link(listOf(DomainId.create()), "Comment")))
        Assert.assertEquals(0, events.list.size)
    }

    /**
     * No Event is created for a link
     */
    @Test
    fun testAddRemoveFromLinkCommand(){
        Assert.assertEquals(0, events.list.size)
        handle(buildAddRemoveFromLinkCommand(DomainId.create(), listOf(DomainId.create()), listOf(DomainId.create())))
        Assert.assertEquals(0, events.list.size)
    }

    /**
     * No Event is created for a link
     */
    @Test
    fun testUpdateLinkCommentCommand(){
        Assert.assertEquals(0, events.list.size)
        handle(buildUpdateLinkCommentCommand(DomainId.create(), "New Comment"))
        Assert.assertEquals(0, events.list.size)
    }

    /**
     * No Event is created for a link
     */
    @Test
    fun testDeleteLinkCommand(){
        Assert.assertEquals(0, events.list.size)
        handle(buildDeleteLinkCommand(DomainId.create()))
        Assert.assertEquals(0, events.list.size)
    }
}