package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.commands.buildAddComplaintCommand
import gov.afrl.batdok.encounter.commands.buildAddEventCommand
import gov.afrl.batdok.encounter.commands.buildLogObservationCommand
import gov.afrl.batdok.encounter.commands.buildUpdateComplaintCommand
import gov.afrl.batdok.encounter.ids.EventId
import gov.afrl.batdok.encounter.ids.ObservationId
import gov.afrl.batdok.encounter.observation.CommonObservations
import gov.afrl.batdok.encounter.observation.ExamData
import gov.afrl.batdok.encounter.observation.Observation
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant

class ComplaintTest {

    val document = Document()

    fun handle(vararg message: Message, timestamp: Instant = Instant.now()){
        document.handle(message.map { buildCommandData(it, timestamp = timestamp) })
    }

    @Test
    fun testBuildAssociationCommand_DefaultParam(){
        val complaint = Complaint("Complaint")
        val observation = Observation("Observation")

        handle(complaint.buildAssociationCommand(observation))

        Assert.assertEquals(1, document.links.size)
        val link = document.links.list[0]

        Assert.assertEquals(CommonLinkRelationships.ASSOC_WITH_COMPLAINT.dataString, link.relationship)
        Assert.assertTrue(complaint.id in link.ids)
        Assert.assertTrue(observation.id in link.ids)
    }

    @Test
    fun testBuildAssociationCommand_CustomParam(){
        val complaint = Complaint("Complaint")
        val observation = Observation("Observation")

        handle(complaint.buildAssociationCommand(observation, CommonLinkRelationships.SYMPTOMS))

        Assert.assertEquals(1, document.links.size)
        val link = document.links.list[0]

        Assert.assertEquals(CommonLinkRelationships.SYMPTOMS.dataString, link.relationship)
        Assert.assertTrue(complaint.id in link.ids)
        Assert.assertTrue(observation.id in link.ids)
    }

    @Test
    fun testGetSymptoms(){
        val complaint = Complaint("Complaint")
        val observation = Observation("Observation")

        document.observations += observation
        handle(complaint.buildAssociationCommand(observation, CommonLinkRelationships.SYMPTOMS))

        Assert.assertEquals(1, document.links.size)

        val symptoms = complaint.getSymptoms(document)
        Assert.assertEquals(1, symptoms.size)
        Assert.assertEquals(observation, symptoms[0])
    }

    @Test
    fun testGetRedFlags(){
        val complaint = Complaint("Complaint")
        val observation = Observation("Red Flag")

        document.observations += observation
        handle(complaint.buildAssociationCommand(observation, CommonLinkRelationships.RED_FLAGS))

        Assert.assertEquals(1, document.links.size)

        val redFlags = complaint.getRedFlags(document)
        Assert.assertEquals(1, redFlags.size)
        Assert.assertEquals(observation, redFlags[0])
    }

    @Test
    fun testGetExams(){
        val complaint = Complaint("Complaint")
        val observation = Observation(CommonObservations.EXAM.dataString, ExamData("Exam", "Data"))

        document.observations += observation
        handle(complaint.buildAssociationCommand(observation))

        Assert.assertEquals(1, document.links.size)

        val exams = complaint.getExams(document)
        Assert.assertEquals(1, exams.size)
        Assert.assertEquals(observation.getData<ExamData>(), exams[0])
    }

    @Test
    fun testBuildLinkCommand_DocumentItems() {
        val complaint = Complaint("Complaint")
        val id = DomainId.create<ObservationId>()
        handle(buildLogObservationCommand("Observation", id = id))

        val observation = document.observations.list.first()
        handle(complaint.buildLinkCommand(observation, document = document))

        val item = complaint.getAssociatedItems(document).map { it }.filterIsInstance<Observation>().firstOrNull()
        Assert.assertEquals("Observation", item?.name?:"")
    }

    @Test
    fun testBuildLinkCommand_Ids_NewLink() {
        val complaint = Complaint("Complaint")
        val id = DomainId.create<ObservationId>()

        handle(
            buildLogObservationCommand("Observation", id = id),
            complaint.buildLinkCommand(id, document = document)
        )

        val item = complaint.getAssociatedItems(document).map { it }.filterIsInstance<Observation>().firstOrNull()
        Assert.assertEquals("Observation", item?.name?:"")
    }

    @Test
    fun testBuildLinkCommand_Ids_ExistingLink() {
        val complaint = Complaint("Complaint")
        val observationId = DomainId.create<ObservationId>()
        val eventId = DomainId.create<EventId>()

        handle(
            buildAddEventCommand(Instant.now(), "Event", true, "", true, eventId),
            complaint.buildLinkCommand(eventId, document = document),
        )

        handle(
            buildLogObservationCommand("Observation 2", id = observationId),
            complaint.buildLinkCommand(observationId, document = document)
        )

        val observation = complaint.getAssociatedItems(document).map { it }.filterIsInstance<Observation>().firstOrNull()
        val event = complaint.getAssociatedItems(document).map { it }.filterIsInstance<Event>().firstOrNull()

        Assert.assertEquals("Observation 2", observation?.name?:"")
        Assert.assertEquals("Event", event?.event?:"")
    }

    @Test
    fun testBuildUnLinkCommand_DocumentItems() {
        val complaint = Complaint("Complaint")
        val id = DomainId.create<ObservationId>()
        handle(buildLogObservationCommand("Observation", id = id))

        val observation = document.observations.list.first()
        handle(complaint.buildLinkCommand(observation, document = document))

        val item = complaint.getAssociatedItems(document).map { it }.filterIsInstance<Observation>().firstOrNull()
        Assert.assertEquals("Observation", item?.name?:"")

        complaint.buildUnLinkCommand(observation, document = document)?.let { handle(it) }

        val removed = complaint.getAssociatedItems(document).map { it }.filterIsInstance<Observation>().firstOrNull()
        Assert.assertNull(removed)
    }

    @Test
    fun testBuildUnLinkCommand_Ids() {
        val complaint = Complaint("Complaint")
        val id = DomainId.create<ObservationId>()

        handle(
            buildLogObservationCommand("Observation", id = id),
            complaint.buildLinkCommand(id, document = document)
        )

        val item = complaint.getAssociatedItems(document).map { it }.filterIsInstance<Observation>().firstOrNull()
        Assert.assertEquals("Observation", item?.name?:"")

        complaint.buildUnLinkCommand(id, document = document)?.let { handle(it) }

        val removed = complaint.getAssociatedItems(document).map { it }.filterIsInstance<Observation>().firstOrNull()
        Assert.assertNull(removed)
    }

    @Test
    fun testAddDispositionToComplaint() {
        handle(
            buildAddComplaintCommand("Complaint", disposition = Disposition.MINOR_CARE_PROTOCOL.dataString)
        )
        val item = document.subjective.complaints.list.first().disposition
        Assert.assertEquals(Disposition.MINOR_CARE_PROTOCOL.dataString, item)
    }

    @Test
    fun testUpdateDisposition() {
        handle(
            buildAddComplaintCommand("Complaint", disposition = Disposition.MINOR_CARE_PROTOCOL.dataString)
        )
        val complaint = document.subjective.complaints.list.first()
        handle(
            complaint.buildSetDispositionCommand(Disposition.AEM_NOW.dataString)
        )
        val item = document.subjective.complaints.list.first().disposition
        Assert.assertEquals(Disposition.AEM_NOW.dataString, item)
    }

    @Test
    fun testRemoveDisposition() {
        handle(
            buildAddComplaintCommand("Complaint", disposition = Disposition.MINOR_CARE_PROTOCOL.dataString)
        )
        val complaint = document.subjective.complaints.list.first()
        handle(
            complaint.buildSetDispositionCommand(null)
        )
        val item = document.subjective.complaints.list.first().disposition
        Assert.assertEquals(null, item)
    }

    @Test
    fun testUpdateDoesNotClearDisposition() {
        handle(
            buildAddComplaintCommand("Complaint", disposition = Disposition.MINOR_CARE_PROTOCOL.dataString)
        )
        val complaint = document.subjective.complaints.list.first()
        handle(
            buildUpdateComplaintCommand(complaint.id, "New Complaint")
        )
        val item = document.subjective.complaints.list.first().disposition
        Assert.assertEquals(Disposition.MINOR_CARE_PROTOCOL.dataString, item)
    }

}