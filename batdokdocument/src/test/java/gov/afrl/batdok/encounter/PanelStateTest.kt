package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.commands.buildLogPanelCommand
import gov.afrl.batdok.encounter.commands.buildRemovePanelCommand
import gov.afrl.batdok.encounter.commands.buildUpdatePanelCommand
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.encounter.ids.PanelId
import gov.afrl.batdok.encounter.panel.KnownLabs
import gov.afrl.batdok.encounter.panel.LabEntry
import gov.afrl.batdok.encounter.panel.Panel
import gov.afrl.batdok.util.buildCommandData
import gov.afrl.batdok.util.buildDocumentCommand
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class PanelStateTest {

    val docId = DomainId.create<DocumentId>()
    private val panels = Panels()

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS), commandId: CommandId = DomainId.create()) {
        panels.handlers.handle(docId, buildCommandData(subCommand, callsign, timestamp, commandId = commandId))
    }

    @Test
    fun testLogPanel(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val labId = DomainId.create<PanelId>()
        val labList = listOf(
            LabEntry(KnownLabs.K, "1.2", ""),
            LabEntry("Test", "3.4", "Unit")
        )
        val panel = Panel(labId, timestamp, labList, docId)

        handle(buildLogPanelCommand(panel), commandId = labId.copy())

        Assert.assertEquals(labId, panels.panels[0].id)
        val parsedLab = panels.panels[0]
        Assert.assertEquals(2, parsedLab.labs.size)
        Assert.assertTrue(KnownLabs.K in parsedLab)
        Assert.assertEquals("1.2", parsedLab[KnownLabs.K]?.value?: "")
        Assert.assertEquals(null, parsedLab[KnownLabs.K]?.unit)
        Assert.assertTrue("Test" in parsedLab)
        Assert.assertEquals("3.4", parsedLab["Test"]?.value?: "")
        Assert.assertEquals("Unit", parsedLab["Test"]?.unit)
        Assert.assertEquals(docId, parsedLab.documentId)
    }

    @Test
    fun testUpdatePanel(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val labId = DomainId.create<PanelId>()
        val labList = listOf(
            LabEntry(KnownLabs.K, "1.2", "Unit"),
            LabEntry("Test", "3.4", "Unit")
        )
        val lab = Panel(labId, timestamp, labList, docId)
        panels += lab

        val preLab = panels.panels[0]
        Assert.assertEquals(2, preLab.labs.size)
        Assert.assertTrue(KnownLabs.K in preLab)
        Assert.assertEquals("1.2", preLab[KnownLabs.K]?.value?: "")
        Assert.assertEquals("Unit", preLab[KnownLabs.K]?.unit?: "")
        Assert.assertTrue("Test" in preLab)
        Assert.assertEquals("3.4", preLab["Test"]?.value?: "")
        Assert.assertEquals("Unit", preLab["Test"]?.unit?: "")
        Assert.assertEquals(docId, preLab.documentId)

        val newLabs = listOf(
            LabEntry(KnownLabs.K, "5.6", "New Unit"),
            LabEntry("Test", "3.4"),
            LabEntry("New Item", "555.0", "New Unit")
        )
        handle(buildUpdatePanelCommand(Panel(labId, timestamp, newLabs, docId), lab))
        Assert.assertEquals(1, panels.panels.size)

        val postLab = panels.panels[0]
        Assert.assertEquals(3, postLab.labs.size)
        //Check new updates
        Assert.assertTrue(KnownLabs.K in postLab)
        Assert.assertEquals("5.6", postLab[KnownLabs.K]?.value?: "")
        Assert.assertEquals("New Unit", postLab[KnownLabs.K]?.unit?: "")
        Assert.assertTrue("New Item" in postLab)
        Assert.assertEquals("555.0", postLab["New Item"]?.value?: "")
        Assert.assertEquals("New Unit", postLab["New Item"]?.unit?: "")

        //Check old values
        Assert.assertTrue("Test" in postLab)
        Assert.assertEquals("3.4", postLab["Test"]?.value?: "")
        Assert.assertEquals(null, postLab["Test"]?.unit)
        Assert.assertEquals(docId, postLab.documentId)
    }

    @Test
    fun testUpdateLab_ClearValues(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val labId = DomainId.create<PanelId>()
        val labList = listOf(
            LabEntry(KnownLabs.K, "1.2"),
            LabEntry("Test", "3.4")
        )
        val lab = Panel(labId, timestamp, labList, docId)
        panels += lab

        val preLab = panels.panels[0]
        Assert.assertEquals(2, preLab.labs.size)
        Assert.assertTrue(KnownLabs.K in preLab)
        Assert.assertEquals("1.2", preLab[KnownLabs.K]?.value?: "")
        Assert.assertTrue("Test" in preLab)
        Assert.assertEquals("3.4", preLab["Test"]?.value?: "")
        Assert.assertEquals(docId, preLab.documentId)

        val newLabs = listOf(
            LabEntry("Test", null)
        )
        handle(buildUpdatePanelCommand(Panel(labId, timestamp, newLabs, docId)))

        val postLab = panels.panels[0]
        Assert.assertEquals(1, postLab.labs.size)

        //Check new updates
        Assert.assertFalse("Test" in postLab)

        //Check old values
        Assert.assertTrue(KnownLabs.K in postLab)
        Assert.assertEquals("1.2", postLab[KnownLabs.K]?.value?: "")
        Assert.assertEquals(docId, postLab.documentId)
    }

    @Test
    fun testRemoveLabs(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val labId = DomainId.create<PanelId>()
        val labList = listOf(
            LabEntry(KnownLabs.K, "1.2"),
            LabEntry("Test", "3.4")
        )

        panels += Panel(labId, timestamp, labList, docId)

        val preLab = panels[labId]!!
        Assert.assertEquals(2, preLab.labs.size)
        Assert.assertTrue(KnownLabs.K in preLab)
        Assert.assertEquals("1.2", preLab[KnownLabs.K]?.value?: "")
        Assert.assertTrue("Test" in preLab)
        Assert.assertEquals("3.4", preLab["Test"]?.value?: "")

        handle(buildRemovePanelCommand(labId))

        Assert.assertEquals(0, panels.panels.size)
    }

    @Test
    fun testDoNotSendPreviousData(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val labId = DomainId.create<PanelId>()
        val labList = listOf(
            LabEntry(KnownLabs.K, "1.2."),
            LabEntry("Test", "3.4")
        )

        val lab = Panel(labId, timestamp, labList, docId)
        val command = buildUpdatePanelCommand(lab, lab)

        Assert.assertEquals(0, command.labEntriesCount)
    }

    @Test
    fun testAddWithId(){
        val id = DomainId.create<DomainId>()
        val doc = Document()

        doc.handle(
            listOf(
                buildDocumentCommand(
                    DomainId.create(),
                    buildCommandData(buildLogPanelCommand(Panel(id.copy(), Instant.now(), listOf())))
                ),
            )
        )
        Assert.assertEquals(id, doc.panels.panels.first().id)
    }
}