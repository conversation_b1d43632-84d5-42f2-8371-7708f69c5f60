package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.Panels.Companion.includePanelHandlers
import gov.afrl.batdok.encounter.commands.buildLogPanelCommand
import gov.afrl.batdok.encounter.commands.buildRemovePanelCommand
import gov.afrl.batdok.encounter.commands.buildUpdatePanelCommand
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.encounter.ids.PanelId
import gov.afrl.batdok.encounter.panel.KnownLabs
import gov.afrl.batdok.encounter.panel.LabEntry
import gov.afrl.batdok.encounter.panel.Panel
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class PanelEventTest {

    private val panels = Panels()
    private val events = Events()

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS), commandId: CommandId = DomainId.create()) {
        EventCommandHandler(events) {
            includePanelHandlers(panels)
        }.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp, commandId))
    }

    @Test
    fun testLogLab(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val panelId = DomainId.create<PanelId>()
        val labList = listOf(
            LabEntry(KnownLabs.K, "1.2"),
            LabEntry("Test", "3.4", "Unit")
        )
        val panel = Panel(panelId, timestamp, labList)

        handle(buildLogPanelCommand(panel), commandId = panelId.copy())

        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Logged Panel: K: 1.2, Test: 3.4 Unit", events.list[0].event)
        Assert.assertEquals(timestamp, events.list[0].timestamp)
        Assert.assertEquals(KnownEventTypes.LABS.dataString, events.list[0].eventType)
    }

    @Test
    fun testUpdateLab(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val panelId = DomainId.create<PanelId>()
        val labList = listOf(
            LabEntry(KnownLabs.K, "1.2"),
            LabEntry("Test", "3.4", "Unit")
        )
        val panel = Panel(panelId, timestamp, labList)
        panels += panel
        events += Event("Test Event", panelId.copy(), timestamp = timestamp.minusSeconds(1000))

        val newLabs = listOf(
            LabEntry(KnownLabs.K, "5.6"),
            LabEntry("Test", "3.4"),
            LabEntry("New Item", "555.0", "New Unit")
        )
        handle(buildUpdatePanelCommand(Panel(panelId, timestamp, newLabs)))

        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals("Updated Panel: K: 5.6, Test: 3.4, New Item: 555.0 New Unit", events.list[1].event)
        Assert.assertEquals(timestamp, events.list[1].timestamp)
        Assert.assertEquals(KnownEventTypes.LABS.dataString, events.list[1].eventType)
    }

    @Test
    fun testUpdateLab_ClearValues(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val panelId = DomainId.create<PanelId>()
        val labList = listOf(
            LabEntry(KnownLabs.K, "1.2"),
            LabEntry("Test", "3.4", "Unit")
        )
        val panel = Panel(panelId, timestamp, labList)
        panels += panel
        events += Event("Test Event", panelId.copy(), timestamp = timestamp.minusSeconds(100))

        val newLabs = listOf(
            LabEntry("Test", null)
        )
        handle(buildUpdatePanelCommand(Panel(panelId, timestamp, newLabs)))

        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals("Updated Panel: K: 1.2", events.list[1].event)
        Assert.assertEquals(timestamp, events.list[1].timestamp)
    }

    @Test
    fun testRemoveLabs(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val panelId = DomainId.create<PanelId>()
        val labList = listOf(
            LabEntry(KnownLabs.K, "1.2"),
            LabEntry("Test", "3.4", "Unit")
        )

        panels += Panel(panelId, timestamp, labList)
        events += Event("Test Event", panelId.copy(), timestamp = timestamp)

        handle(buildRemovePanelCommand(panelId))

        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals("Removed Panel: K: 1.2, Test: 3.4 Unit", events.list[1].event)
    }
}