package gov.afrl.batdok.encounter.treatments

import gov.afrl.batdok.encounter.treatment.LineData
import org.junit.Assert
import org.junit.Test

class LineDataTest {
    @Test
    fun testTqLocationPairs(){
        fun expectValidPair(location: String, type: String){
            try{
                LineData(location = location, type = type)
            }catch (ex: IllegalArgumentException){
                Assert.fail("${location}/${type} should be a valid pair")
            }
        }
        expectValidPair("Custom", LineData.Type.IO.dataString)
        expectValidPair(LineData.Location.STERNUM.dataString, "Custom")
        expectValidPair("Custom", "Custom")
    }
}