package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.commands.*
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class SubjectiveStateTest {

    val subjective = Subjective()

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS)) {
        subjective.handlers.handle(
            DomainId.nil(),
            buildCommandData(subCommand, callsign, timestamp)
        )
    }

    @Test
    fun testAddChiefComplaint(){
        //ARRANGE
        val complaint = "Head > Headache > Left Side"

        //ACT
        handle(buildUpdateChiefComplaintCommand(complaint, true))

        //ASSERT
        Assert.assertEquals(1, subjective.chiefComplaints.size)
        Assert.assertEquals(complaint, subjective.chiefComplaints[0])
    }

    @Test
    fun testRemoveChiefComplaint(){
        //ARRANGE
        val complaint = "Head > Headache > Left Side"
        subjective.chiefComplaints += complaint

        Assert.assertEquals(1, subjective.chiefComplaints.size)

        //ACT
        handle(buildUpdateChiefComplaintCommand(complaint, false))

        //ASSERT
        Assert.assertEquals(0, subjective.chiefComplaints.size)
    }

    @Test
    fun testAddComplaint(){
        //ARRANGE
        val complaint = "Head > Headache > Left Side"

        //ACT
        handle(buildAddComplaintCommand(complaint))

        //ASSERT
        Assert.assertEquals(1, subjective.complaints.size)
        Assert.assertEquals(complaint, subjective.complaints.list[0].complaint)
    }

    @Test
    fun testAddComplaint_WithValues(){
        //ARRANGE
        val complaint = "Head > Headache > Left Side"
        val history = "It started last night and hasn't gone away"
        val reviewOfSystem = "+Headache, -Rash"

        //ACT
        handle(buildAddComplaintCommand(complaint, history, reviewOfSystem))

        //ASSERT
        Assert.assertEquals(1, subjective.complaints.size)
        Assert.assertEquals(complaint, subjective.complaints.list[0].complaint)
        Assert.assertEquals(history, subjective.complaints.list[0].history)
        Assert.assertEquals(reviewOfSystem, subjective.complaints.list[0].reviewOfSystems)
    }

    @Test
    fun testUpdateComplaint(){
        //ARRANGE
        val oldComplaint = Complaint("Complaint", "History")
        val newComplaint = Complaint("Complaint2", "History2", id = oldComplaint.id)
        subjective.complaints += oldComplaint

        //ACT
        handle(buildUpdateComplaintCommand(oldComplaint.id, newComplaint.complaint, newComplaint.history))

        //ASSERT
        Assert.assertEquals(1, subjective.complaints.size)
        Assert.assertEquals(newComplaint.complaint, subjective.complaints.list[0].complaint)
        Assert.assertEquals(newComplaint.history, subjective.complaints.list[0].history)
        Assert.assertEquals("CS", subjective.complaints.list[0].careProvider)
    }

    @Test
    fun testUpdateComplaint_DontUpdate(){
        //ARRANGE
        val oldComplaint = Complaint("Complaint", "History")
        subjective.complaints += oldComplaint

        //ACT
        handle(buildUpdateComplaintCommand(oldComplaint.id, null, null))

        //ASSERT
        Assert.assertEquals(1, subjective.complaints.size)
        Assert.assertEquals(oldComplaint.complaint, subjective.complaints.list[0].complaint)
        Assert.assertEquals(oldComplaint.history, subjective.complaints.list[0].history)
    }

    @Test
    fun testUpdateComplaint_UpdateValues(){
        //ARRANGE
        val oldComplaint = Complaint("Complaint", "History", "Old ROS")
        subjective.complaints += oldComplaint

        //ACT
        handle(buildUpdateComplaintCommand(oldComplaint.id, null, "History2", "ROS2"))

        //ASSERT
        Assert.assertEquals(1, subjective.complaints.size)
        Assert.assertEquals(oldComplaint.complaint, subjective.complaints.list[0].complaint)
        Assert.assertEquals("History2", subjective.complaints.list[0].history)
        Assert.assertEquals("ROS2", subjective.complaints.list[0].reviewOfSystems)
    }

    @Test
    fun testRemoveComplaint(){
        //ARRANGE
        val complaint = Complaint("Head > Headache > Left Side")
        subjective.complaints += complaint

        Assert.assertEquals(1, subjective.complaints.size)

        //ACT
        handle(buildRemoveComplaintCommand(complaint.id))

        //ASSERT
        Assert.assertEquals(0, subjective.complaints.size)
    }

    @Test
    fun testAddPositiveReviewOfSystems(){
        //ARRANGE
        val complaint = "Head > Headache > Left Side"

        //ACT
        handle(buildUpdateReviewOfSystemsCommand(complaint, true))

        //ASSERT
        Assert.assertEquals(1, subjective.reviewOfSystems.size)
        Assert.assertTrue(subjective.reviewOfSystems[0].second)
        Assert.assertEquals(complaint, subjective.reviewOfSystems[0].first)
    }

    @Test
    fun testAddNegativeReviewOfSystems(){
        //ARRANGE
        val complaint = "Head > Headache > Left Side"

        //ACT
        handle(buildUpdateReviewOfSystemsCommand(complaint, false))

        //ASSERT
        Assert.assertEquals(1, subjective.reviewOfSystems.size)
        Assert.assertFalse(subjective.reviewOfSystems[0].second)
        Assert.assertEquals(complaint, subjective.reviewOfSystems[0].first)
    }

    @Test
    fun testUpdateExistingNegativeReviewOfSystems(){
        //ARRANGE
        val complaint = "Head > Headache > Left Side"
        subjective += complaint to true
        Assert.assertEquals(1, subjective.reviewOfSystems.size)
        Assert.assertTrue(subjective.reviewOfSystems[0].second)
        Assert.assertEquals(complaint, subjective.reviewOfSystems[0].first)

        //ACT
        handle(buildUpdateReviewOfSystemsCommand(complaint, false))

        //ASSERT
        Assert.assertEquals(1, subjective.reviewOfSystems.size)
        Assert.assertFalse(subjective.reviewOfSystems[0].second)
        Assert.assertEquals(complaint, subjective.reviewOfSystems[0].first)
    }

    @Test
    fun testRemoveReviewOfSystems(){
        //ARRANGE
        val complaint = "Head > Headache > Left Side"
        subjective.reviewOfSystems += complaint to false

        Assert.assertEquals(1, subjective.reviewOfSystems.size)

        //ACT
        handle(buildUpdateReviewOfSystemsCommand(complaint, null))

        //ASSERT
        Assert.assertEquals(0, subjective.reviewOfSystems.size)
    }

    @Test
    fun testUpdateOpqrstlCommand_InitialChange(){
        //ARRANGE
        val opqrstl = Opqrstl(
            Onset.GRADUAL.dataString,
            "Pressure",
            "Touch",
            listOf(
                Quality.BETTER_NOW.dataString,
                "Test"
            ),
            true,
            3,
            300L,
            200L
        )

        //ACT
        handle(buildUpdateOpqrstlCommand(opqrstl))

        //ASSERT
        Assert.assertEquals(opqrstl.onset, subjective.opqrstl?.onset)
        Assert.assertEquals(opqrstl.provocationBetterWhen, subjective.opqrstl?.provocationBetterWhen)
        Assert.assertEquals(opqrstl.provocationWorseWhen, subjective.opqrstl?.provocationWorseWhen)
        Assert.assertEquals(opqrstl.qualities, subjective.opqrstl?.qualities)
        Assert.assertEquals(opqrstl.radiates, subjective.opqrstl?.radiates)
        Assert.assertEquals(opqrstl.severity, subjective.opqrstl?.severity)
        Assert.assertEquals(opqrstl.time, subjective.opqrstl?.time)
        Assert.assertEquals(opqrstl.lastOralIntake, subjective.opqrstl?.lastOralIntake)
    }

    @Test
    fun testUpdateOpqrstlCommand_UpdateAllFields(){
        //ARRANGE
        val oldOpqrstl = Opqrstl(
            Onset.GRADUAL.dataString,
            "Pressure",
            "Touch",
            listOf(
                Quality.BETTER_NOW.dataString,
                "Test"
            ),
            true,
            3,
            300L,
            200L
        )
        val opqrstl = Opqrstl(
            Onset.SUDDEN.dataString,
            "Not Pressure",
            "Not Touch",
            listOf(
                Quality.WORSE_NOW.dataString,
                "Not Test"
            ),
            false,
            2,
            500L,
            700L
        )
        subjective.opqrstl = oldOpqrstl

        //ACT
        handle(buildUpdateOpqrstlCommand(opqrstl, oldOpqrstl))

        //ASSERT
        Assert.assertEquals(opqrstl.onset, subjective.opqrstl?.onset)
        Assert.assertEquals(opqrstl.provocationBetterWhen, subjective.opqrstl?.provocationBetterWhen)
        Assert.assertEquals(opqrstl.provocationWorseWhen, subjective.opqrstl?.provocationWorseWhen)
        Assert.assertEquals(opqrstl.qualities, subjective.opqrstl?.qualities)
        Assert.assertEquals(opqrstl.radiates, subjective.opqrstl?.radiates)
        Assert.assertEquals(opqrstl.severity, subjective.opqrstl?.severity)
        Assert.assertEquals(opqrstl.time, subjective.opqrstl?.time)
        Assert.assertEquals(opqrstl.lastOralIntake, subjective.opqrstl?.lastOralIntake)
    }

    @Test
    fun testUpdateOpqrstlCommand_UpdateNoFields(){
        //ARRANGE
        val opqrstl = Opqrstl(
            Onset.GRADUAL.dataString,
            "Pressure",
            "Touch",
            listOf(
                Quality.BETTER_NOW.dataString,
                "Test"
            ),
            true,
            3,
            300L,
            200L
        )
        subjective.opqrstl = opqrstl

        //ACT
        val command = buildUpdateOpqrstlCommand(opqrstl, opqrstl)
        handle(command)

        //ASSERT

        // If a field doesn't change, it will be removed from the command.
        // Since none are changing, all are removed
        Assert.assertEquals(0, command.serializedSize)

        Assert.assertEquals(opqrstl.onset, subjective.opqrstl?.onset)
        Assert.assertEquals(opqrstl.provocationBetterWhen, subjective.opqrstl?.provocationBetterWhen)
        Assert.assertEquals(opqrstl.provocationWorseWhen, subjective.opqrstl?.provocationWorseWhen)
        Assert.assertEquals(opqrstl.qualities, subjective.opqrstl?.qualities)
        Assert.assertEquals(opqrstl.radiates, subjective.opqrstl?.radiates)
        Assert.assertEquals(opqrstl.severity, subjective.opqrstl?.severity)
        Assert.assertEquals(opqrstl.time, subjective.opqrstl?.time)
        Assert.assertEquals(opqrstl.lastOralIntake, subjective.opqrstl?.lastOralIntake)
    }

    @Test
    fun testUpdateOpqrstlCommand_Clear(){
        //ARRANGE
        val oldOpqrstl = Opqrstl(
            Onset.GRADUAL.dataString,
            "Pressure",
            "Touch",
            listOf(
                Quality.BETTER_NOW.dataString,
                "Test"
            ),
            true,
            3,
            300L,
            200L
        )
        val opqrstl = Opqrstl(
            null,
            "",
            "",
            listOf(),
            null,
            null,
            null,
            null
        )
        subjective.opqrstl = oldOpqrstl

        //ACT
        handle(buildUpdateOpqrstlCommand(opqrstl, oldOpqrstl))

        //ASSERT
        Assert.assertEquals(opqrstl.onset, subjective.opqrstl?.onset)
        Assert.assertEquals(opqrstl.provocationBetterWhen, subjective.opqrstl?.provocationBetterWhen)
        Assert.assertEquals(opqrstl.provocationWorseWhen, subjective.opqrstl?.provocationWorseWhen)
        Assert.assertEquals(opqrstl.qualities, subjective.opqrstl?.qualities)
        Assert.assertEquals(opqrstl.radiates, subjective.opqrstl?.radiates)
        Assert.assertEquals(opqrstl.severity, subjective.opqrstl?.severity)
        Assert.assertEquals(opqrstl.time, subjective.opqrstl?.time)
        Assert.assertEquals(opqrstl.lastOralIntake, subjective.opqrstl?.lastOralIntake)
    }
}