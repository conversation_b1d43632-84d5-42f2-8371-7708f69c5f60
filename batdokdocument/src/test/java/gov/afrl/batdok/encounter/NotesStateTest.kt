package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.commands.buildAddNoteCommand
import gov.afrl.batdok.encounter.commands.buildRemoveNoteCommand
import gov.afrl.batdok.encounter.commands.buildUpdateNoteCommand
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.encounter.ids.NoteId
import gov.afrl.batdok.util.buildCommandData
import gov.afrl.batdok.util.buildDocumentCommand
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class NotesStateTest {

    val documentId = DomainId.create<DocumentId>()
    val notes = Notes()
    val callsign = "CS"

    //region Helper Functions
    private fun handle(
        subCommand: Message,
        callsign: String = this.callsign,
        timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS),
        id: CommandId = DomainId.create()
    ) {
        notes.handlers.handle(documentId, buildCommandData(subCommand, callsign, timestamp, id))
    }

    @Test
    fun testLogNote(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val message = "Testing note"
        val noteId = DomainId.create<NoteId>()
        val noteToAdd = Note(noteId, message, timestamp, callsign)

        handle(buildAddNoteCommand(noteToAdd))

        val addedNote = notes.list[0]
        Assert.assertEquals(noteId, addedNote.id)
        Assert.assertEquals(message, addedNote.message)
        Assert.assertEquals(timestamp, addedNote.timeStamp)
        Assert.assertEquals(callsign, addedNote.callsign)
    }

    @Test
    fun testUpdateNote(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val message = "Testing note"
        val noteId = DomainId.create<NoteId>()
        val noteToAdd = Note(noteId, message, timestamp, callsign)
        notes += noteToAdd

        val preUpdateNote = notes.list[0]
        Assert.assertEquals(noteId, preUpdateNote.id)
        Assert.assertEquals(message, preUpdateNote.message)

        val updatedMessage = "Updated Note Test"
        val newNote = noteToAdd.copy(message = updatedMessage, timeStamp = Instant.now().truncatedTo(ChronoUnit.SECONDS))

        handle(buildUpdateNoteCommand(newNote), callsign = "NoteGuy")

        Assert.assertEquals(1, notes.list.size)

        val postUpdateNote = notes.list[0]
        Assert.assertEquals(noteId, postUpdateNote.id)
        //Check new updates
        Assert.assertEquals(updatedMessage, postUpdateNote.message)
        Assert.assertEquals(newNote.timeStamp, postUpdateNote.timeStamp)
        Assert.assertEquals("NoteGuy", postUpdateNote.callsign)
    }

    @Test
    fun testRemoveNote(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val message = "Testing note"
        val noteId = DomainId.create<NoteId>()
        val noteToAdd = Note(noteId, message, timestamp, callsign)
        notes += noteToAdd
        
        val preRemoveNote = notes.list[0]
        Assert.assertEquals(noteId, preRemoveNote.id)
        Assert.assertEquals(message, preRemoveNote.message)
        Assert.assertEquals(timestamp, preRemoveNote.timeStamp)
        Assert.assertEquals(callsign, preRemoveNote.callsign)

        handle(buildRemoveNoteCommand(noteId))

        Assert.assertEquals(0, notes.list.size)
    }

    @Test
    fun testAddIfDoesntExistOnUpdate(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val message = "Testing note"
        val noteId = DomainId.create<NoteId>()
        val noteToUpdate = Note(noteId, message, timestamp, callsign)

        handle(buildUpdateNoteCommand(noteToUpdate))

        Assert.assertEquals(1, notes.list.size)
    }

    @Test
    fun testAddWithId(){
        val id = DomainId.create<DomainId>()
        val doc = Document()

        doc.handle(
            listOf(
                buildDocumentCommand(
                    DomainId.create(),
                    buildCommandData(buildAddNoteCommand(Note(id.copy(), message = "message", callsign = callsign)))
                ),
            )
        )
        Assert.assertEquals(id, doc.notes.list.first().id)
    }
}