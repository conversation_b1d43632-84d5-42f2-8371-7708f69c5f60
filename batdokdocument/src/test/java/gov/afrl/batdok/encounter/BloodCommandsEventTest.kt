package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.commands.buildLogBloodCommand
import gov.afrl.batdok.encounter.commands.buildRemoveBloodCommand
import gov.afrl.batdok.encounter.commands.buildUpdateBloodCommand
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.encounter.ids.TreatmentId
import gov.afrl.batdok.encounter.observation.Blood
import gov.afrl.batdok.encounter.observation.BloodList
import gov.afrl.batdok.encounter.observation.BloodList.Companion.includeBloodEvents
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant

class BloodCommandsEventTest {
    val events = Events()
    val bloodList = BloodList()
    val commandTime = Instant.ofEpochSecond(1L)
    val bloodStartedTime = Instant.ofEpochSecond(100L)
    val newBloodStartedTime = Instant.ofEpochSecond(200L)
    private fun handle(subCommand: Message, callsign: String = "CS",
                       timestamp: Instant = commandTime,
                       commandId: CommandId = DomainId.create()) {
            EventCommandHandler(events) {
                includeBloodEvents(bloodList)
            }.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp, commandId))
    }

    @Test
    fun testBloodEventCommandWithoutAge(){
        //Age Unit shouldn't display if you don't set an age value
        val blood = Blood(bloodAgeUnit = "Month")
        Assert.assertEquals(": ", blood.toEventString())
    }

    @Test
    fun testLogBloodCommand(){
        val id = DomainId.create<TreatmentId>()
        val blood = Blood(
            bloodId = id,
            administrationTime = bloodStartedTime,
            bloodProduct = "newblood",
            bloodType = "tsttype",
            donationIdNumber = "12345",
            expirationDate = "1111",
            bloodAge = 1,
            bloodAgeUnit = "day",
            volume = 1,
            unit = "mL",
            route = "IV"
        )
        handle(buildLogBloodCommand(blood))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Logged Blood Item newblood: Type: tsttype, 12345, 1 mL, IV, (1 day, 1111)", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.MEDS.dataString, events.list[0].eventType)
    }

    @Test
    fun testUpdateBloodCommand(){
        val id = DomainId.create<TreatmentId>()
        val blood = Blood(
            bloodId = id,
            administrationTime = newBloodStartedTime,
            bloodProduct = "newblood",
            bloodType = "tsttype",
            donationIdNumber = "12345",
            expirationDate = "1111",
            bloodAge = 1,
            bloodAgeUnit = "month"
        )
        bloodList += Blood(
            bloodId = id,
            administrationTime = bloodStartedTime,
            bloodProduct = "oldblood",
            bloodType = "oldtype",
            donationIdNumber = "12",
            expirationDate = "0000",
            bloodAge = 0
        )
        handle(buildUpdateBloodCommand(blood))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Updated Blood Item newblood: Type: tsttype, 12345, (1 month, 1111)", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.MEDS.dataString, events.list[0].eventType)
    }

    @Test
    fun testRemoveBloodCommand(){
        val id = DomainId.create<TreatmentId>()
        val blood = Blood(
            bloodId = id,
            administrationTime = bloodStartedTime,
            bloodProduct = "newblood",
            bloodType = "tsttype",
            donationIdNumber = "12345",
            expirationDate = "1111",
            bloodAge = 1
        )
        bloodList += blood
        handle(buildRemoveBloodCommand(id, false))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Removed Blood Item newblood: Type: tsttype, 12345, (1, 1111)", events.list[0].event)
        Assert.assertEquals(id, events.list[0].referencedItem)
    }

    @Test
    fun testRemoveBloodCommand_Error(){
        val id = DomainId.create<TreatmentId>()
        val blood = Blood(
            bloodId = id,
            administrationTime = bloodStartedTime,
            bloodProduct = "newblood",
            bloodType = "tsttype",
            donationIdNumber = "12345",
            expirationDate = "1111",
            bloodAge = 1
        )
        bloodList += blood
        handle(buildRemoveBloodCommand(id, true))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Removed Blood Item newblood: Type: tsttype, 12345, (1, 1111) due to documentation error", events.list[0].event)
        Assert.assertEquals(id, events.list[0].referencedItem)
    }
}