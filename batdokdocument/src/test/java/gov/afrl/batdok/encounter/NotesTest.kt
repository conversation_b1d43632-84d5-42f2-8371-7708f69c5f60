package gov.afrl.batdok.encounter

import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import java.time.Instant

class NotesTest {
    //region Class Setups

    val timestamp: Instant = Instant.now()

    private val notes1 =  Note(
        message = "hello world",
        id = DomainId.create("00000010"),
        timeStamp = timestamp.minusSeconds(1000),
        callsign = "person1"
    )

    private val note2a =  Note(
        message = "hello my honey hello my baby hello my ragtime gal",
        id = DomainId.create("0000002a"),
        timeStamp = timestamp.minusSeconds(2000),
        callsign = "person2"
    )

    private val note2b =  Note(
        message = "it's cold outside there's no kind of atmosphere",
        id = DomainId.create("0000002b"),
        timeStamp = timestamp.minusSeconds(3000),
        callsign = "person2"
    )

    private val note3 =  Note(
        message = "linus and lucy",
        id = DomainId.create("00000030"),
        timeStamp = timestamp.minusSeconds(4000),
        callsign = "person3"
    )

    private lateinit var testNotes: Notes

    //endregion

    @Before
    fun setup() {
        testNotes = Notes()
    }

    @Test
    fun testGet() {
        //ARRANGE
        listOf(notes1, note2a, note2b, note3).forEach {
            testNotes += it
        }

        //ACT
        val result = testNotes[note2a.id]

        //ASSERT
        Assert.assertEquals(note2a, result)
    }

    @Test
    fun testPlusAssign() {
        //ACT
        testNotes += notes1

        //ASSERT
        Assert.assertEquals(1, testNotes.list.size)
    }

    @Test
    fun testPlusAssign_ListIsSortedAscending() {
        //ACT
        val messagesToAdd = listOf(note2b, notes1, note2a, note3)
        messagesToAdd.forEach {
            testNotes += it
        }

        Assert.assertEquals(messagesToAdd.sortedBy { it.timeStamp }, testNotes.list)
    }

    @Test
    fun testPlusAssignReplace() {
        //ARRANGE
        val note1Id = notes1.id
        listOf(notes1, note2a, note2b, note3).forEach {
            testNotes += it
        }

        //ACT
        val message1Edit = notes1.copy(message = "Edited")
        testNotes += message1Edit

        //ASSERT
        Assert.assertEquals(message1Edit, testNotes[note1Id])
    }


    @Test
    fun testMinusAssign() {
        //ARRANGE
        listOf(notes1, note2a, note2b, note3).forEach {
            testNotes += it
        }

        //ACT
        testNotes.removeItem(notes1.id, Instant.now())

        //ASSERT
        Assert.assertEquals(3, testNotes.list.size)

    }
}