package gov.afrl.batdok.encounter

import org.junit.Assert
import org.junit.Test

class HIstoryTest {

    val classUnderTest = History()

    @Test
    fun testGetHistoryByString(){
        //ASSIGN
        classUnderTest.histories = mapOf(
            "History" to "Test1",
            HistoryType.MEDICAL.dataString to "Test2"
        )

        //ACT
        val history = classUnderTest["History"]

        //ASSERT
        Assert.assertEquals("Test1", history)
    }

    @Test
    fun testGetHistoryByEnum(){
        //ASSIGN
        classUnderTest.histories = mapOf(
            "History" to "Test1",
            HistoryType.MEDICAL.dataString to "Test2"
        )

        //ACT
        val history = classUnderTest[HistoryType.MEDICAL]

        //ASSERT
        Assert.assertEquals("Test2", history)
    }
}