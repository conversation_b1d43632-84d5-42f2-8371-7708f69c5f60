package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.commands.buildChangeGenderCommand
import gov.afrl.batdok.encounter.commands.buildChangeGradeCommand
import gov.afrl.batdok.encounter.commands.buildChangeNameCommand
import gov.afrl.batdok.encounter.commands.buildChangeWeightCommand
import gov.afrl.batdok.encounter.commands.buildIsMedicalCommand
import gov.afrl.batdok.encounter.movement.Attendant
import gov.afrl.batdok.util.NameFormatter
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class AttendantStateTest {
    val attendant = Attendant()

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(
        ChronoUnit.SECONDS)) {
        attendant.handlers.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp))
    }

    @Test
    fun testChangeName_SingleString_JustLast(){
        val newName = "Test"
        Assert.assertNotEquals(newName, attendant.name?.last)
        handle(buildChangeNameCommand(newName))
        Assert.assertEquals(newName, attendant.name?.last)
    }

    @Test
    fun testChangeName_SingleName_FullName(){
        val newName = "Bruce Batman Wayne"
        Assert.assertNotEquals(newName, attendant.name?.toString(NameFormatter.Format.FIRST_MIDDLE_LAST))
        handle(buildChangeNameCommand(newName))
        Assert.assertEquals(newName, attendant.name?.toString(NameFormatter.Format.FIRST_MIDDLE_LAST))
    }

    @Test
    fun testChangeName_ThreeNames(){
        val newName = "Bruce Batman Wayne"
        Assert.assertNotEquals(newName, attendant.name?.toString(NameFormatter.Format.FIRST_MIDDLE_LAST))
        handle(buildChangeNameCommand("Bruce", "Batman", "Wayne"))
        Assert.assertEquals(newName, attendant.name?.toString(NameFormatter.Format.FIRST_MIDDLE_LAST))
    }

    @Test
    fun testChangeName_ThreeNames_JustMiddle(){
        val newName = "Batman"
        Assert.assertNotEquals(newName, attendant.name?.toString(NameFormatter.Format.FIRST_MIDDLE_LAST))
        handle(buildChangeNameCommand(null, "Batman", null))
        Assert.assertEquals(newName, attendant.name?.middle)
        Assert.assertEquals(newName, attendant.name?.toString(NameFormatter.Format.FIRST_MIDDLE_LAST))
    }
    
    @Test
    fun testChangeGradeCommand_enum() {
        val newGrade = Grade.E09
        Assert.assertNotEquals(newGrade.dataString, attendant.grade)
        handle(buildChangeGradeCommand(newGrade))
        Assert.assertEquals(newGrade.dataString, attendant.grade)
    }
    
    @Test
    fun testChangeGradeCommand_string() {
        val newGrade = "Test"
        Assert.assertNotEquals(newGrade, attendant.grade)
        handle(buildChangeGradeCommand(newGrade))
        Assert.assertEquals(newGrade, attendant.grade)
    }
    
    @Test
    fun testChangeGradeCommand_null() {
        val newGrade: String? = null
        handle(buildChangeGradeCommand(Grade.W01))
        Assert.assertNotEquals(newGrade, attendant.grade)
        handle(buildChangeGradeCommand(newGrade))
        Assert.assertEquals(newGrade, attendant.grade)
    }

    @Test
    fun testChangeGenderCommand_enum(){
        val newGender = Gender.MALE
        Assert.assertNotEquals(newGender.dataString, attendant.gender)
        handle(buildChangeGenderCommand(newGender))
        Assert.assertEquals(newGender.dataString, attendant.gender)
    }
    
    @Test
    fun testChangeGenderCommand_string(){
        val newGender = "Test"
        Assert.assertNotEquals(newGender, attendant.gender)
        handle(buildChangeGenderCommand(newGender))
        Assert.assertEquals(newGender, attendant.gender)
    }

    @Test
    fun testChangeWeightCommand(){
        val newWeight = 123.4f
        Assert.assertNotEquals(newWeight, attendant.weight)
        handle(buildChangeWeightCommand(newWeight))
        Assert.assertEquals(newWeight, attendant.weight)
    }

    @Test
    fun testChangeIsMedical() {
        val newIsMedical = true
        Assert.assertNotEquals(newIsMedical, attendant.isMedical)
        handle(buildIsMedicalCommand(newIsMedical))
        Assert.assertEquals(newIsMedical, attendant.isMedical)
    }
}