package gov.afrl.batdok.encounter.metadata

import org.junit.Assert
import org.junit.Test

class ArrhythmiaDataTest {

    @Test
    fun toEventString() {
        Assert.assertEquals(
            "Tachycardia, Narrow",
            ArrhythmiaData(true, true).toEventString()
        )
        Assert.assertEquals(
            "Bradycardia, Wide",
            ArrhythmiaData(false, false).toEventString()
        )
        Assert.assertNull(ArrhythmiaData().toEventString())
        Assert.assertEquals(
            "Tachycardia", ArrhythmiaData(true).toEventString()
        )
        Assert.assertEquals(
            "Narrow", ArrhythmiaData(null, true).toEventString()
        )
    }
}