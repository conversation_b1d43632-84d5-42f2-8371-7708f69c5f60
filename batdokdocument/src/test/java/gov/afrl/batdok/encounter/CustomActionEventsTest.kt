package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.CustomActions.Companion.includeCustomActionHandlers
import gov.afrl.batdok.encounter.commands.buildAddCustomActionCommand
import gov.afrl.batdok.encounter.commands.buildRemoveCustomActionCommand
import gov.afrl.batdok.encounter.commands.buildUpdateCustomActionCommand
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class CustomActionEventsTest {
    private val events = Events()
    private val customActions = CustomActions()

    private fun handle(subCommand: Message, callSign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(
        ChronoUnit.SECONDS), commandId: CommandId = DomainId.create()) {
        EventCommandHandler(events) {
            includeCustomActionHandlers(customActions)
        }.handle(DomainId.nil(), buildCommandData(subCommand, callSign, timestamp, commandId))
    }

    @Test
    fun testAddCustomAction() {
        val newAction = CustomAction(
            message = "New instructions for patient",
            callSign = "Fred",
            timestamp = Instant.ofEpochSecond(0)
        )
        Assert.assertTrue(events.list.isEmpty())

        handle(buildAddCustomActionCommand(newAction))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals(newAction.id, events.list.first().referencedItem)
        Assert.assertEquals(newAction.timestamp, events.list.first().timestamp)
        Assert.assertEquals("Added Action:\n${newAction.message}", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.ACTION.dataString, events.list[0].eventType)
    }

    @Test
    fun testUpdateCustomAction() {
        val oldCustomAction = CustomAction(
            message = "New instructions for patient",
            callSign = "Fred"
        )
        val newAction = oldCustomAction.copy(message = "Amended instructions for patient")
        customActions += oldCustomAction
        Assert.assertTrue(events.list.isEmpty())

        handle(buildUpdateCustomActionCommand(newAction))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Updated Action from:\n\"${oldCustomAction.message}\"\nto:\n\"${newAction.message}\"", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.OTHER.dataString, events.list[0].eventType)
    }

    @Test
    fun testUpdateNote_NoPreExisting() {
        val oldCustomAction = CustomAction(
            message = "New instructions for patient",
            callSign = "Fred"
        )
        val newAction = oldCustomAction.copy(message = "Amended instructions for patient")
        Assert.assertTrue(events.list.isEmpty())

        handle(buildUpdateCustomActionCommand(newAction))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Added Action:\n${newAction.message}", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.OTHER.dataString, events.list[0].eventType)
    }

    @Test
    fun testRemoveCustomAction() {
        val oldCustomAction = CustomAction(
            message = "New instructions for patient",
            callSign = "Fred"
        )
        customActions += oldCustomAction
        Assert.assertTrue(events.list.isEmpty())

        handle(buildRemoveCustomActionCommand(oldCustomAction.id))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Removed Action:\n\"${oldCustomAction.message}\"", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.OTHER.dataString, events.list[0].eventType)
    }

    @Test
    fun testRemoveNote_NoPreExisting() {
        val oldCustomAction = CustomAction(
            message = "New instructions for patient",
            callSign = "Fred"
        )
        Assert.assertTrue(events.list.isEmpty())

        handle(buildRemoveCustomActionCommand(oldCustomAction.id))
        Assert.assertEquals(0, events.list.size)
    }
}