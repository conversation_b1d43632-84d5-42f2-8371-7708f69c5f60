package gov.afrl.batdok.encounter

import org.junit.Assert
import org.junit.Test

class InjuryTest {

    @Test
    fun testAddMoi(){
        val injuries = Injuries()
        injuries.addMoi("Test", "Head")

        Assert.assertTrue(injuries.mechanismsOfInjury.contains<PERSON><PERSON>("Head"))
        Assert.assertTrue(injuries.mechanismsOfInjury["Head"]!!.contains("Test"))
    }

    @Test
    fun testAddMoi_NullLocation(){
        val injuries = Injuries()
        injuries.addMoi("Test")

        Assert.assertTrue(injuries.mechanismsOfInjury.containsKey(null))
        Assert.assertTrue(injuries.mechanismsOfInjury[null]!!.contains("Test"))
    }

    @Test
    fun testRemoveMoi(){
        val injuries = Injuries()
        injuries.mechanismsOfInjury = mapOf(
            "Head" to listOf("Test"),
            "Body" to listOf("Test")
        )

        injuries.removeMoi("Test", "Head")

        Assert.assertFalse(injuries.mechanismsOfInjury.contains<PERSON><PERSON>("Head"))
        Assert.assertTrue(injuries.mechanismsOfInjury.containsKey("Body"))
        Assert.assertTrue(injuries.mechanismsOfInjury["Body"]!!.contains("Test"))
    }

    @Test
    fun testRemoveMoi_NullLocation(){
        val injuries = Injuries()
        injuries.mechanismsOfInjury = mapOf(
            null to listOf("Test"),
            "Body" to listOf("Test")
        )

        injuries.removeMoi("Test")

        Assert.assertFalse(injuries.mechanismsOfInjury.containsKey(null))
        Assert.assertTrue(injuries.mechanismsOfInjury.containsKey("Body"))
        Assert.assertTrue(injuries.mechanismsOfInjury["Body"]!!.contains("Test"))
    }

    @Test
    fun testAddInjury(){
        val injuries = Injuries()
        val injury = Injury("Test", null)
        injuries.addInjury(Injury("Test", null), "Head")

        Assert.assertTrue(injuries.injuries.containsKey("Head"))
        Assert.assertTrue(injuries.injuries["Head"]!!.contains(injury))
    }

    @Test
    fun testAddInjury_NullLocation(){
        val injuries = Injuries()
        val injury = Injury("Test", null)
        injuries.addInjury(Injury("Test", null))
        Assert.assertTrue(injuries.injuries.containsKey(null))
        Assert.assertTrue(injuries.injuries[null]!!.contains(injury))
    }

    @Test
    fun testRemoveInjury(){
        val injuries = Injuries()
        val injury = Injury("Test", null)
        injuries.injuries = mapOf(
            "Head" to listOf(injury),
            "Body" to listOf(injury)
        )

        injuries.removeInjury(injury, "Head")

        Assert.assertFalse(injuries.injuries.containsKey("Head"))
        Assert.assertTrue(injuries.injuries.containsKey("Body"))
        Assert.assertTrue(injuries.injuries["Body"]!!.contains(injury))
    }

    @Test
    fun testRemoveInjury_NullLocation(){
        val injuries = Injuries()
        val injury = Injury("Test", null)
        injuries.injuries = mapOf(
            null to listOf(injury),
            "Body" to listOf(injury)
        )

        injuries.removeInjury(injury)

        Assert.assertFalse(injuries.injuries.containsKey(null))
        Assert.assertTrue(injuries.injuries.containsKey("Body"))
        Assert.assertTrue(injuries.injuries["Body"]!!.contains(injury))
    }

    @Test
    fun testHasMoi(){
        val injuries = Injuries()
        injuries.mechanismsOfInjury = mapOf(
            null to listOf("Test"),
            "Head" to listOf(Moi.ARTY.dataString)
        )

        Assert.assertTrue(injuries.hasMoi("Test"))
        Assert.assertTrue(injuries.hasMoi(Moi.ARTY))
    }

    @Test
    fun testHasInjury(){
        val injuries = Injuries()
        injuries.injuries = mapOf(
            null to listOf(Injury("Test", null)),
            "Head" to listOf(Injury(Injury.Type.AMPUTATION.dataString, Injury.Type.AMPUTATION.abbrev))
        )

        Assert.assertTrue(injuries.hasInjury("Test"))
        Assert.assertTrue(injuries.hasInjury(Injury.Type.AMPUTATION))
    }

    @Test
    fun testAllMoiInjuryString_Empty(){
        Assert.assertEquals("Mois: None; Injuries: None", Injuries().allMoiInjuryString())
    }

    @Test
    fun testAllMoiInjuryString_Full(){
        val injuries = Injuries()
        injuries.injuries = mapOf(
            null to listOf(Injury("TestInjury", null)),
            "Head" to listOf(Injury(Injury.Type.AMPUTATION.dataString, Injury.Type.AMPUTATION.abbrev))
        )
        injuries.mechanismsOfInjury = mapOf(
            null to listOf("TestMoi"),
            "Head" to listOf(Moi.ARTY.dataString)
        )
        Assert.assertEquals("Mois: TestMoi, Artillery; Injuries: TestInjury, Amputation", injuries.allMoiInjuryString())
    }

    @Test
    fun testGetAllInjuriesExcept(){
        val injuries = Injuries()
        injuries.injuries = mapOf(
            null to listOf(Injury("TestInjury", null), Injury("Test2", null)),
            "Head" to listOf(Injury(Injury.Type.AMPUTATION.dataString, Injury.Type.AMPUTATION.abbrev))
        )

        val result = injuries.getAllInuriesExcept(Injury.Type.AMPUTATION)

        Assert.assertEquals(injuries.injuries[null], result)
    }

    @Test
    fun testGetAllInjuriesExcept_String(){
        val injuries = Injuries()
        injuries.injuries = mapOf(
            null to listOf(Injury("TestInjury", null), Injury("Test2", null)),
            "Head" to listOf(Injury(Injury.Type.AMPUTATION.dataString, Injury.Type.AMPUTATION.abbrev))
        )

        val result = injuries.getAllInuriesExcept("Test2")

        Assert.assertEquals(listOf(Injury("TestInjury", null), Injury(Injury.Type.AMPUTATION.dataString, Injury.Type.AMPUTATION.abbrev)), result)
    }

    @Test
    fun testGetAllMoisExcept(){
        val injuries = Injuries()
        injuries.mechanismsOfInjury = mapOf(
            null to listOf("Test Moi", "Test Moi 2"),
            "Head" to listOf(Moi.ARTY.dataString)
        )

        val result = injuries.getAllMoisExcept(Moi.ARTY)

        Assert.assertEquals(injuries.mechanismsOfInjury[null], result)
    }

    @Test
    fun testGetAllMoisExcept_String(){
        val injuries = Injuries()
        injuries.mechanismsOfInjury = mapOf(
            null to listOf("Test Moi", "Test Moi 2"),
            "Head" to listOf(Moi.ARTY.dataString)
        )

        val result = injuries.getAllMoisExcept("Test Moi")

        Assert.assertEquals(injuries.mechanismsOfInjury.flatMap { it.value } - "Test Moi", result)
    }
}