package gov.afrl.batdok.encounter.treatments

import gov.afrl.batdok.encounter.Treatments
import gov.afrl.batdok.encounter.treatment.CommonTreatments
import gov.afrl.batdok.encounter.treatment.DressingData
import gov.afrl.batdok.encounter.treatment.TqData
import gov.afrl.batdok.encounter.treatment.Treatment
import org.junit.Assert
import org.junit.Test

class TreatmentsTest {

    @Test
    fun testTreatmentContains(){
        val treats = Treatments()
        treats += Treatment(CommonTreatments.NEEDLE_D.dataString)
        treats += Treatment("Test")

        Assert.assertTrue(treats.list.any { it.name == CommonTreatments.NEEDLE_D.dataString })
        Assert.assertTrue(CommonTreatments.NEEDLE_D in treats)

        Assert.assertTrue(treats.list.any { it.name == "Test" })
        Assert.assertTrue("Test" in treats)

        Assert.assertFalse(CommonTreatments.CHEST_SEAL in treats)
        Assert.assertFalse("NOT" in treats)
    }

    @Test
    fun testTreatmentCount(){
        val treats = Treatments()
        Assert.assertEquals(0, treats.count(CommonTreatments.NEEDLE_D))

        treats += Treatment(CommonTreatments.NEEDLE_D.dataString)
        Assert.assertEquals(1, treats.count(CommonTreatments.NEEDLE_D))

        treats += Treatment(CommonTreatments.NEEDLE_D.dataString)
        Assert.assertEquals(2, treats.count(CommonTreatments.NEEDLE_D))
    }

    @Test
    fun testGetByName(){
        val treats = Treatments()
        treats += Treatment(CommonTreatments.NEEDLE_D.dataString)
        treats += Treatment(CommonTreatments.NEEDLE_D.dataString)
        treats += Treatment("Test")

        Assert.assertEquals(2, treats[CommonTreatments.NEEDLE_D].size)
        Assert.assertEquals(1, treats["Test"].size)
    }

    @Test
    fun testAllTreatmentsString_Full(){
        val treats = Treatments()
        treats += listOf(
            Treatment(
                CommonTreatments.TQ.dataString,
                TqData(TqData.Location.EXTREMITY.dataString, "Subtype")
            ),
            Treatment(
                CommonTreatments.TQ.dataString,
                TqData(TqData.Location.EXTREMITY.dataString, "Subtype2")
            ),
            Treatment(
                CommonTreatments.TQ.dataString,
                TqData(TqData.Location.TRUNCAL.dataString, "Subtype")
            ),
            Treatment(
                CommonTreatments.DRESSING.dataString,
                DressingData(DressingData.Type.ABDOMINAL.dataString, null, null)
            ),
            Treatment(
                CommonTreatments.DRESSING.dataString,
                DressingData(DressingData.Type.ABDOMINAL.dataString, null, null)
            ),
            Treatment(
                CommonTreatments.DRESSING.dataString,
                DressingData(DressingData.Type.KERLIX.dataString, null, null)
            ),
            Treatment("Test"),
            Treatment("Test"),
            Treatment("Test2")
        )

        Assert.assertEquals("TQ-Extremity x2, TQ-Truncal x1, Abdominal Dressing x2, Kerlix Dressing x1, Test x2, Test2 x1", treats.getTreatmentString(false))
    }

    @Test
    fun testAllTreatmentsString_Limit_LessThanLimit(){
        val treats = Treatments()
        treats += listOf(
            Treatment(
                CommonTreatments.TQ.dataString,
                TqData(TqData.Location.EXTREMITY.dataString, "Subtype")
            ),
            Treatment(
                CommonTreatments.TQ.dataString,
                TqData(TqData.Location.EXTREMITY.dataString, "Subtype2")
            ),
            Treatment(
                CommonTreatments.TQ.dataString,
                TqData(TqData.Location.TRUNCAL.dataString, "Subtype")
            ),
            Treatment(
                CommonTreatments.DRESSING.dataString,
                DressingData(DressingData.Type.ABDOMINAL.dataString, null, null)
            ),
            Treatment(
                CommonTreatments.DRESSING.dataString,
                DressingData(DressingData.Type.ABDOMINAL.dataString, null, null)
            ),
            Treatment(
                CommonTreatments.DRESSING.dataString,
                DressingData(DressingData.Type.KERLIX.dataString, null, null)
            ),
            Treatment("Test"),
            Treatment("Test"),
            Treatment("Test2")
        )
        Assert.assertEquals("TQ-Extremity x2\n" +
                "TQ-Truncal x1\n" +
                "Abdominal Dressing x2\n" +
                "Kerlix Dressing x1\n" +
                "Test x2\n" +
                "Test2 x1", treats.getTreatmentString(true, 10, "\n"))
    }

    @Test
    fun testAllTreatmentsString_Limit_TooMany(){
        val treats = Treatments()
        treats += listOf(
            Treatment(
                CommonTreatments.TQ.dataString,
                TqData(TqData.Location.EXTREMITY.dataString, "Subtype")
            ),
            Treatment(
                CommonTreatments.TQ.dataString,
                TqData(TqData.Location.EXTREMITY.dataString, "Subtype2")
            ),
            Treatment(
                CommonTreatments.TQ.dataString,
                TqData(TqData.Location.TRUNCAL.dataString, "Subtype")
            ),
            Treatment(
                CommonTreatments.DRESSING.dataString,
                DressingData(DressingData.Type.ABDOMINAL.dataString, null, null)
            ),
            Treatment(
                CommonTreatments.DRESSING.dataString,
                DressingData(DressingData.Type.ABDOMINAL.dataString, null, null)
            ),
            Treatment(
                CommonTreatments.DRESSING.dataString,
                DressingData(DressingData.Type.KERLIX.dataString, null, null)
            ),
            Treatment("Test"),
            Treatment("Test"),
            Treatment("Test2")
        )

        Assert.assertEquals(
            "TQ-Extremity x2\n" +
                    "TQ-Truncal x1\n" +
                    "Abdominal Dressing x2\n" +
                    "Kerlix Dressing x1\n" +
                    "(2 more treatments can be viewed in BATDOK)",
            treats.getTreatmentString(true, 4, "\n")
        )
    }

    @Test
    fun testAllTreatmentsString_Empty(){
        val treats = Treatments()

        Assert.assertEquals("", treats.getTreatmentString(true, 4, "\n"))
    }

    @Test
    fun testGetAllExcept_String(){
        val treats = Treatments()
        treats += listOf(
            Treatment(
                CommonTreatments.TQ.dataString,
                TqData(TqData.Location.EXTREMITY.dataString, "Subtype")
            ),
            Treatment(
                CommonTreatments.TQ.dataString,
                TqData(TqData.Location.EXTREMITY.dataString, "Subtype2")
            ),
            Treatment(
                CommonTreatments.TQ.dataString,
                TqData(TqData.Location.TRUNCAL.dataString, "Subtype")
            ),
            Treatment(
                CommonTreatments.DRESSING.dataString,
                DressingData(DressingData.Type.ABDOMINAL.dataString, null, null)
            ),
            Treatment(
                CommonTreatments.DRESSING.dataString,
                DressingData(DressingData.Type.ABDOMINAL.dataString, null, null)
            ),
            Treatment(
                CommonTreatments.DRESSING.dataString,
                DressingData(DressingData.Type.KERLIX.dataString, null, null)
            ),
            Treatment("Test"),
            Treatment("Test"),
            Treatment("Test2")
        )

        val result = treats.getAllExcept(CommonTreatments.TQ.dataString, CommonTreatments.DRESSING.dataString, "Test")

        Assert.assertEquals(1, result.size)
        Assert.assertEquals(treats["Test2"], result)
    }

    @Test
    fun testGetAllExcept_Enum(){
        val treats = Treatments()
        treats += listOf(
            Treatment(
                CommonTreatments.TQ.dataString,
                TqData(TqData.Location.EXTREMITY.dataString, "Subtype")
            ),
            Treatment(
                CommonTreatments.TQ.dataString,
                TqData(TqData.Location.EXTREMITY.dataString, "Subtype2")
            ),
            Treatment(
                CommonTreatments.TQ.dataString,
                TqData(TqData.Location.TRUNCAL.dataString, "Subtype")
            ),
            Treatment(
                CommonTreatments.DRESSING.dataString,
                DressingData(DressingData.Type.ABDOMINAL.dataString, null, null)
            ),
            Treatment(
                CommonTreatments.DRESSING.dataString,
                DressingData(DressingData.Type.ABDOMINAL.dataString, null, null)
            ),
            Treatment(
                CommonTreatments.DRESSING.dataString,
                DressingData(DressingData.Type.KERLIX.dataString, null, null)
            ),
            Treatment("Test"),
            Treatment("Test"),
            Treatment("Test2")
        )

        val result = treats.getAllExcept(CommonTreatments.TQ, CommonTreatments.DRESSING)

        Assert.assertEquals(3, result.size)
        Assert.assertEquals(treats["Test"] + treats["Test2"], result)
    }
}