package gov.afrl.batdok.encounter

import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import java.time.Instant

class CustomActionsTest {

    //region Class Setups

    val timestamp: Instant = Instant.now()

    private val customAction1 =  CustomAction(
        message = "hello world",
        id = DomainId.create("00000010"),
        description = "custom action one",
        timestamp = timestamp.minusSeconds(1000),
        callSign = "person1"
    )

    private val customAction2a =  CustomAction(
        message = "hello my honey hello my baby hello my ragtime gal",
        id = DomainId.create("0000002a"),
        description = "custom action two",
        timestamp = timestamp.minusSeconds(2000),
        callSign = "person2"
    )

    private val customAction2b =  CustomAction(
        message = "it's cold outside there's no kind of atmosphere",
        id = DomainId.create("0000002b"),
        description = "custom action three",
        timestamp = timestamp.minusSeconds(3000),
        callSign = "person2"
    )

    private val customAction3 =  CustomAction(
        message = "linus and lucy",
        id = DomainId.create("00000030"),
        description = "custom action four",
        timestamp = timestamp.minusSeconds(4000),
        callSign = "person3"
    )

    private lateinit var testCustomActions: CustomActions

    //endregion

    @Before
    fun setup() {
        testCustomActions = CustomActions()
    }

    @Test
    fun testGet() {
        //ARRANGE
        listOf(customAction1, customAction2a, customAction2b, customAction3).forEach {
            testCustomActions += it
        }

        //ACT
        val result = testCustomActions[customAction2a.id]

        //ASSERT
        Assert.assertEquals(customAction2a, result)
    }

    @Test
    fun testPlusAssign() {
        //ACT
        testCustomActions += customAction1

        //ASSERT
        Assert.assertEquals(1, testCustomActions.actions.size)
    }

    @Test
    fun testPlusAssign_ListIsSortedAscending() {
        //ACT
        val messagesToAdd = listOf(customAction2b, customAction1, customAction2a, customAction3)
        messagesToAdd.forEach {
            testCustomActions += it
        }

        Assert.assertEquals(messagesToAdd.sortedBy { it.timestamp }, testCustomActions.actions)
    }

    @Test
    fun testPlusAssignReplace() {
        //ARRANGE
        val customAction1Id = customAction1.id
        listOf(customAction1, customAction2a, customAction2b, customAction3).forEach {
            testCustomActions += it
        }

        //ACT
        val message1Edit = customAction1.copy(message = "Edited", description = "description Edited")
        testCustomActions += message1Edit

        //ASSERT
        Assert.assertEquals(message1Edit, testCustomActions[customAction1Id])
    }


    @Test
    fun testMinusAssign() {
        //ARRANGE
        listOf(customAction1, customAction2a, customAction2b, customAction3).forEach {
            testCustomActions += it
        }

        //ACT
        testCustomActions.removeItem(customAction1.id, Instant.now())

        //ASSERT
        Assert.assertEquals(3, testCustomActions.actions.size)

    }
}