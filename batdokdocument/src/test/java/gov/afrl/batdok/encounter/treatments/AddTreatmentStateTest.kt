package gov.afrl.batdok.encounter.treatments

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.Document
import gov.afrl.batdok.encounter.Treatments
import gov.afrl.batdok.encounter.commands.buildAddTreatmentCommand
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.encounter.ids.TreatmentId
import gov.afrl.batdok.encounter.treatment.*
import gov.afrl.batdok.encounter.treatment.TubeData.BreathingConfirmation
import gov.afrl.batdok.util.buildCommandData
import gov.afrl.batdok.util.buildDocumentCommand
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class AddTreatmentStateTest {

    val documentId = DomainId.create<DocumentId>()
    val treatments = Treatments()

    //region Helper Functions
    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS), id: CommandId = DomainId.create()) {
        treatments.handlers.handle(documentId, buildCommandData(subCommand, callsign, timestamp, id))
    }

    private fun <T: TreatmentData> testTreatment(
        name: String,
        data: T? = null,
        assertData: (result: T?) -> Unit = { Assert.assertNull(it) }
    ){
        val id = DomainId.create<TreatmentId>()
        val timestamp = Instant.ofEpochMilli(123456789).truncatedTo(ChronoUnit.SECONDS)
        val treatment = Treatment(
            name = name,
            treatmentData = data,
            id = id,
            timestamp = timestamp,
            documentId = documentId
        )
        Assert.assertEquals(0, treatments.list.size)
        handle(buildAddTreatmentCommand(treatment), timestamp = timestamp, id = id.copy())

        val newTreatment = treatments.list[0]
        Assert.assertEquals(name, newTreatment.name)
        Assert.assertEquals(timestamp, newTreatment.timestamp)
        Assert.assertEquals(id, newTreatment.id)
        Assert.assertEquals(documentId, newTreatment.documentId)

        @Suppress("UNCHECKED_CAST") //We don't need the warning. If the cast fails, the test will fail.
        assertData(newTreatment.treatmentData as T?)
    }

    private fun <T: TreatmentData> testTreatment(name: CommonTreatments, data: T, assertData: (result: T) -> Unit) =
        testTreatment(name.dataString, data){
            Assert.assertNotNull(it)
            assertData(it!!)
        }

    private fun testTreatment(name: CommonTreatments) = testTreatment(name.dataString, null)
    //endregion

    @Test
    fun testAddTreatmentCommand_TQWithExtras() = testTreatment(
        CommonTreatments.TQ,
        TqData(
            TqData.Location.EXTREMITY.dataString,
            tqType = "CAT5",
            subLocation = "LUE",
            label = "TQ1"
        )
    ){data ->
        Assert.assertEquals("CAT5", data.tqType)
        Assert.assertEquals("Extremity", data.tqLocation)
        Assert.assertEquals("LUE", data.subLocation)
        Assert.assertEquals("TQ1", data.label)
    }

    @Test
    fun testAddTreatmentCommand_TQWithoutExtras() = testTreatment(CommonTreatments.TQ)

    @Test
    fun testAddTreatmentCommand_DressingWithExtras() = testTreatment(
        CommonTreatments.DRESSING,
        DressingData(DressingData.Type.ABDOMINAL.dataString, null, null)
    ){
        Assert.assertEquals("Abdominal", it.type)
    }

    @Test
    fun testAddTreatmentCommand_DressingWithoutExtras() = testTreatment(CommonTreatments.DRESSING)

    @Test
    fun testAddTreatmentCommand_ETTubeWithExtras() = testTreatment(
        CommonTreatments.ET_TUBE,
        TubeData(size = 10f, depth = 20, location = "Location", breathingConfirmation = "Confirm", sizeUnit = "mm", depthUnit = "cm")
    ){
        Assert.assertEquals(10f, it.size)
        Assert.assertEquals(20, it.depth)
        Assert.assertEquals("mm", it.sizeUnit)
        Assert.assertEquals("cm", it.depthUnit)
        Assert.assertEquals("Location", it.location)
        Assert.assertEquals("Confirm", it.breathingConfirmation)
    }

    @Test
    fun testAddTreatmentCommand_ETTubeWithoutExtras() = testTreatment(CommonTreatments.ET_TUBE)
    
    @Test
    fun testAddTreatmentCommand_NeedleDWithoutExtras() = testTreatment(CommonTreatments.NEEDLE_D)

    @Test
    fun testAddTreatmentCommand_NeedleDWithExtras() = testTreatment(
        CommonTreatments.NEEDLE_D,
        NeedleDData(NeedleDData.Location.L_MID_CLAV.dataString)
    ){
        Assert.assertEquals(NeedleDData.Location.L_MID_CLAV.dataString, it.location)
    }
    @Test
    fun testAddTreatmentCommand_FingerThorWithoutExtras() = testTreatment(CommonTreatments.FINGER_THOR)

    @Test
    fun testAddTreatmentCommand_FingerThorWithExtras() = testTreatment(
        CommonTreatments.FINGER_THOR,
        FingerThorData(FingerThorData.Location.LEFT.dataString)
    ){
        Assert.assertEquals(FingerThorData.Location.LEFT.dataString, it.location)
    }
    @Test
    fun testAddTreatmentCommand_ChestTubeWithoutExtras() = testTreatment(CommonTreatments.CHEST_TUBE)

    @Test
    fun testAddTreatmentCommand_ChestTubeWithExtras() = testTreatment(
        CommonTreatments.CHEST_TUBE,
        ChestTubeData(
            location = ChestTubeData.Location.LEFT.dataString,
            suction = true,
            suctionAmount = 1.4f,
            airLeak = true
        )
    ){
        Assert.assertEquals(ChestTubeData.Location.LEFT.dataString, it.location)
        Assert.assertTrue(it.suction!!)
        Assert.assertEquals(1.4f, it.suctionAmount!!, 0.1f)
        Assert.assertTrue(it.airLeak!!)
    }
    @Test
    fun testAddTreatmentCommand_ChestSealWithoutExtras() = testTreatment(CommonTreatments.CHEST_SEAL)

    @Test
    fun testAddTreatmentCommand_ChestSealWithExtras() = testTreatment(
        CommonTreatments.CHEST_SEAL,
        ChestSealData(ChestSealData.Location.LEFT_FRONT.dataString)
    ){
        Assert.assertEquals(ChestSealData.Location.LEFT_FRONT.dataString, it.location)
    }
    @Test
    fun testAddTreatmentCommand_EyeShieldWithoutExtras() = testTreatment(CommonTreatments.EYE_SHIELD)

    @Test
    fun testAddTreatmentCommand_EyeShieldWithExtras() = testTreatment(
        CommonTreatments.EYE_SHIELD,
        EyeShieldData(true, false)
    ){
        Assert.assertTrue(it.left)
        Assert.assertFalse(it.right)
    }
    @Test
    fun testAddTreatmentCommand_SplintWithoutExtras() = testTreatment(CommonTreatments.SPLINT)

    @Test
    fun testAddTreatmentCommand_SplintWithExtras() = testTreatment(
        CommonTreatments.SPLINT,
        SplintData(true, "Type")
    ){
        Assert.assertTrue(it.pulsePresent?:false)
        Assert.assertEquals("Type", it.type)
    }
    @Test
    fun testAddTreatmentCommand_O2WithoutExtras() = testTreatment(CommonTreatments.O2)

    @Test
    @Deprecated("Remove when O2Data gets rid of deprecated fields")
    fun testAddTreatmentCommand_O2WithExtras_Old() = testTreatment(
        CommonTreatments.O2,
        O2Data(10f, O2Data.DeliveryMethod.NRB.dataString)
    ){
        Assert.assertEquals(10f, it.volume)
        Assert.assertEquals("NRB", it.deliveryMethod)
    }

    @Test
    fun testAddTreatmentCommand_O2WithExtras() = testTreatment(
        CommonTreatments.O2,
        O2Data(lpm = 10f, targetSpO2 = 98, fiO2 = 13f, route = O2Data.O2Route.VENTURI_MASK.dataString),
    ){
        Assert.assertEquals(10f, it.lpm)
        Assert.assertEquals(98, it.targetSpO2)
        Assert.assertEquals(13f, it.fiO2)
        Assert.assertEquals(O2Data.O2Route.VENTURI_MASK.dataString, it.route)
    }
    @Test
    fun testAddTreatmentCommand_CricWithoutExtras() = testTreatment(CommonTreatments.CRIC)

    @Test
    fun testAddTreatmentCommand_CricWithExtras() = testTreatment(
        CommonTreatments.CRIC,
        TubeData(breathingConfirmation = BreathingConfirmation.BREATH_SOUNDS.dataString, size = 10f, sizeUnit = "Unit")
    ){
        Assert.assertEquals(BreathingConfirmation.BREATH_SOUNDS.dataString, it.breathingConfirmation)
        Assert.assertEquals(10f, it.size)
        Assert.assertEquals("Unit", it.sizeUnit)
    }
    @Test
    fun testAddTreatmentCommand_EscharatomyWithoutExtras() = testTreatment(CommonTreatments.ESCHARATOMY)

    @Test
    fun testAddTreatmentCommand_EscharatomyWithExtras() = testTreatment(
        CommonTreatments.ESCHARATOMY,
        EscharatomyData("Free text location")
    ){
        Assert.assertEquals("Free text location", it.location)
    }
    @Test
    fun testAddTreatmentCommand_DebridementWithoutExtras() = testTreatment(CommonTreatments.DEBRIDEMENT)

    @Test
    fun testAddTreatmentCommand_DebridementWithExtras() = testTreatment(
        CommonTreatments.DEBRIDEMENT,
        DebridementData("Free text location")
    ){
        Assert.assertEquals("Free text location", it.location)
    }
    @Test
    fun testAddTreatmentCommand_GastricTubeWithoutExtras() = testTreatment(CommonTreatments.GASTRIC_TUBE)

    @Test
    fun testAddTreatmentCommand_GastricTubeWithExtras() = testTreatment(
        CommonTreatments.GASTRIC_TUBE,
        GastricTubeData(
            GastricTubeData.Type.NASAL.dataString,
            GastricTubeData.Side.LEFT.dataString,
            GastricTubeData.SuctionType.SUCTION.dataString,
            GastricTubeData.Interval.CONTINUOUS.dataString
            )
    ){
        Assert.assertEquals(GastricTubeData.Type.NASAL.dataString, it.type)
        Assert.assertEquals(GastricTubeData.Side.LEFT.dataString, it.side)
        Assert.assertEquals(GastricTubeData.SuctionType.SUCTION.dataString, it.suctionType)
        Assert.assertEquals(GastricTubeData.Interval.CONTINUOUS.dataString, it.interval)
    }
    @Test
    fun testAddTreatmentCommand_LateralCanthotomyWithoutExtras() = testTreatment(CommonTreatments.LATERAL_CANTHOTOMY)

    @Test
    fun testAddTreatmentCommand_LateralCanthotomyWithExtras() = testTreatment(
        CommonTreatments.LATERAL_CANTHOTOMY,
        LateralCanthotomyData(LateralCanthotomyData.Location.LEFT.dataString)
    ){
        Assert.assertEquals(LateralCanthotomyData.Location.LEFT.dataString, it.location)
    }
    @Test
    fun testAddTreatmentCommand_FasciotomyWithoutExtras() = testTreatment(CommonTreatments.FASCIOTOMY)

    @Test
    fun testAddTreatmentCommand_FasciotomyWithExtras() = testTreatment(
        CommonTreatments.FASCIOTOMY,
        FasciotomyData("Free text Location")
    ){
        Assert.assertEquals("Free text Location", it.location)
    }
    @Test
    fun testAddTreatmentCommand_FoleyCatheterWithoutExtras() = testTreatment(CommonTreatments.FOLEY_CATHETER)

    @Test
    fun testAddTreatmentCommand_FoleyCatheterWithExtras() = testTreatment(
        CommonTreatments.FOLEY_CATHETER,
        FoleyCatheterData(
            10f,
            "G",
            FoleyCatheterData.Color.BLUE.dataString,
            "Character",
            "Assess"
        )
    ){
        Assert.assertEquals(10f, it.size)
        Assert.assertEquals("G", it.sizeUnit)
        Assert.assertEquals("Blue", it.color)
        Assert.assertEquals("Character", it.character)
        Assert.assertEquals("Assess", it.assess)
    }
    @Test
    fun testAddTreatmentCommand_DirectPressureWithoutExtras() = testTreatment(CommonTreatments.DIRECT_PRESSURE)

    @Test
    fun testAddTreatmentCommand_DirectPressureWithExtras() = testTreatment(
        CommonTreatments.DIRECT_PRESSURE,
        DirectPressureData("Free text Location")
    ){
        Assert.assertEquals("Free text Location", it.location)
    }
    @Test
    fun testAddTreatmentCommand_ForeignBodyRemovalWithoutExtras() = testTreatment(CommonTreatments.FOREIGN_BODY_REMOVAL)

    @Test
    fun testAddTreatmentCommand_ForeignBodyRemovalWithExtras() = testTreatment(
        CommonTreatments.FOREIGN_BODY_REMOVAL,
        ForeignBodyRemovalData("Free text Location")
    ){
        Assert.assertEquals("Free text Location", it.location)
    }
    @Test
    fun testAddTreatmentCommand_NPAWithoutExtras() = testTreatment(CommonTreatments.NPA)

    @Test
    fun testAddTreatmentCommand_NPAWithExtras() = testTreatment(
        CommonTreatments.NPA,
        TubeData(BreathingConfirmation.BREATH_SOUNDS.dataString, 10f, sizeUnit = "Unit")
    ){
        Assert.assertEquals(BreathingConfirmation.BREATH_SOUNDS.dataString, it.breathingConfirmation)
        Assert.assertEquals(10f, it.size)
        Assert.assertEquals("Unit", it.sizeUnit)
    }
    @Test
    fun testAddTreatmentCommand_OPAWithoutExtras() = testTreatment(CommonTreatments.OPA)

    @Test
    fun testAddTreatmentCommand_OPAWithExtras() = testTreatment(
        CommonTreatments.OPA,
        TubeData(BreathingConfirmation.BREATH_SOUNDS.dataString, 10f, sizeUnit = "Unit")
    ){
        Assert.assertEquals(BreathingConfirmation.BREATH_SOUNDS.dataString, it.breathingConfirmation)
        Assert.assertEquals(10f, it.size)
        Assert.assertEquals("Unit", it.sizeUnit)
    }
    @Test
    fun testAddTreatmentCommand_SGAWithoutExtras() = testTreatment(CommonTreatments.SGA)

    @Test
    fun testAddTreatmentCommand_SGAWithExtras() = testTreatment(
        CommonTreatments.SGA,
        TubeData(BreathingConfirmation.BREATH_SOUNDS.dataString, 10f, "Unit", "SGA Type")
    ){
        Assert.assertEquals(BreathingConfirmation.BREATH_SOUNDS.dataString, it.breathingConfirmation)
        Assert.assertEquals(10f, it.size)
        Assert.assertEquals("Unit", it.sizeUnit)
        Assert.assertEquals("SGA Type", it.type)
    }
    @Test
    fun testAddTreatmentCommand_TrachWithoutExtras() = testTreatment(CommonTreatments.TRACH)

    @Test
    fun testAddTreatmentCommand_TrachWithExtras() = testTreatment(
        CommonTreatments.TRACH,
        TubeData(BreathingConfirmation.BREATH_SOUNDS.dataString, 10f, sizeUnit = "Unit")
    ){
        Assert.assertEquals(BreathingConfirmation.BREATH_SOUNDS.dataString, it.breathingConfirmation)
        Assert.assertEquals(10f, it.size)
        Assert.assertEquals("Unit", it.sizeUnit)
    }
    @Test
    fun testAddTreatmentCommand_HypothermiaPreventionWithoutExtras() = testTreatment(CommonTreatments.HYPOTHERMIA_PREVENTION)

    @Test
    fun testAddTreatmentCommand_HypothermiaPreventionWithExtras() = testTreatment(
        CommonTreatments.HYPOTHERMIA_PREVENTION,
        HypothermiaPreventionData(HypothermiaPreventionData.Type.BLANKET.dataString)
    ){
        Assert.assertEquals(HypothermiaPreventionData.Type.BLANKET.dataString, it.type)
    }
    @Test
    fun testAddTreatmentCommand_ImmobilizationWithoutExtras() = testTreatment(CommonTreatments.IMMOBILIZATION)

    @Test
    fun testAddTreatmentCommand_ImmobilizationWithExtras() = testTreatment(
        CommonTreatments.IMMOBILIZATION,
        ImmobilizationData(ImmobilizationData.Type.C_COLLAR.dataString)
    ){
        Assert.assertEquals(ImmobilizationData.Type.C_COLLAR.dataString, it.type)
    }
    @Test
    fun testAddTreatmentCommand_SuctionWithoutExtras() = testTreatment(CommonTreatments.SUCTION)

    @Test
    fun testAddTreatmentCommand_SuctionWithExtras() = testTreatment(
        CommonTreatments.SUCTION,
        SuctionData(
            SuctionData.Tool.YANKAUER.dataString,
            "Free Text Location"
        )
    ){
        Assert.assertEquals(SuctionData.Tool.YANKAUER.dataString, it.tool)
        Assert.assertEquals("Free Text Location", it.location)
    }
    @Test
    fun testAddTreatmentCommand_IntubatedByWithoutExtras() = testTreatment(CommonTreatments.INTUBATED_BY)

    @Test
    fun testAddTreatmentCommand_IntubatedByWithExtras() = testTreatment(
        CommonTreatments.INTUBATED_BY,
        IntubatedByData("Intubater")
    ){
        Assert.assertEquals("Intubater", it.intubater)
    }

    @Test
    fun testAddTreatmentCommand_PelvicBinderWithoutExtras() = testTreatment(CommonTreatments.PELVIC_BINDER)

    @Test
    fun testAddTreatmentCommand_PelvicBinderWithExtras() = testTreatment(
        CommonTreatments.PELVIC_BINDER,
        PelvicBinderData("Type")
    ){
        Assert.assertEquals("Type", it.type)
    }

    @Test
    fun testAddWithId(){
        val treatmentid = DomainId.create<TreatmentId>()
        val doc = Document()

        doc.handle(
            listOf(
                buildDocumentCommand(
                    DomainId.create(),
                    buildCommandData(buildAddTreatmentCommand("Treat", id = treatmentid))
                ),
                buildDocumentCommand(
                    DomainId.create(),
                    buildCommandData(buildAddTreatmentCommand(Treatment("Treat2", id = treatmentid)))
                ),
            )
        )

        doc.treatments.list.forEach {
            Assert.assertEquals(treatmentid, it.id)
        }
    }

    @Test
    fun testAddWithId_Nil(){
        val treatmentid = DomainId.nil<TreatmentId>()
        val doc = Document()

        doc.handle(
            listOf(
                buildDocumentCommand(
                    DomainId.create(),
                    buildCommandData(buildAddTreatmentCommand("Treat", id = treatmentid))
                ),
                buildDocumentCommand(
                    DomainId.create(),
                    buildCommandData(buildAddTreatmentCommand(Treatment("Treat2", id = treatmentid)))
                ),
            )
        )

        doc.treatments.list.forEach {
            Assert.assertNotEquals(treatmentid, it.id)
        }
    }

    @Test
    fun testAddTreatmentCommand_WoundPackingWithoutExtras() = testTreatment(CommonTreatments.WOUND_PACKING)

    @Test
    fun testAddTreatmentCommand_WoundPackingWithExtras() = testTreatment(
        CommonTreatments.WOUND_PACKING,
        WoundPackingData(WoundPackingData.Location.LLE.dataString, WoundPackingData.Type.HEMOSTATIC.dataString)
    ){
        Assert.assertEquals(WoundPackingData.Location.LLE.dataString, it.location)
        Assert.assertEquals(WoundPackingData.Type.HEMOSTATIC.dataString, it.type)
    }
}