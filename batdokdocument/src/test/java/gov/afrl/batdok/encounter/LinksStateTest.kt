package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.commands.buildAddRemoveFromLinkCommand
import gov.afrl.batdok.encounter.commands.buildCreateLinkCommand
import gov.afrl.batdok.encounter.commands.buildDeleteLinkCommand
import gov.afrl.batdok.encounter.commands.buildUpdateLinkCommentCommand
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.encounter.ids.EncounterVitalId
import gov.afrl.batdok.encounter.ids.TreatmentId
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class LinksStateTest {

    private val links = Links()

    private fun handle(
        subCommand: Message,
        callsign: String = "CS",
        timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS),
        commandId: CommandId = DomainId.create()
    ) {
        links.handlers.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp, commandId))
    }

    @Test
    fun testCreateLinkCommand(){
        val link = Link(listOf(DomainId.create<TreatmentId>(), DomainId.create<EncounterVitalId>()), "Treatment and Resultant vital")
        Assert.assertEquals(0, links.list.size)
        handle(buildCreateLinkCommand(link))
        Assert.assertEquals(1, links.list.size)
        Assert.assertEquals(link.relationship, links.list[0].relationship)
        Assert.assertEquals(link.ids[0], links.list[0].ids[0])
        Assert.assertEquals(link.ids[1], links.list[0].ids[1])
    }

    @Test
    fun testAddRemoveFromLinkCommand(){
        val link = Link(listOf(DomainId.create<TreatmentId>(), DomainId.create<EncounterVitalId>()), "Treatment and Resultant vital")
        links += link
        Assert.assertEquals(1, links.list.size)
        val idsToAdd = listOf(DomainId.create<EncounterVitalId>())
        val idsToRemove = listOf(link.ids[1])
        handle(buildAddRemoveFromLinkCommand(link.id, idsToAdd, idsToRemove))
        Assert.assertEquals(1, links.list.size)
        Assert.assertEquals(link.relationship, links.list[0].relationship)
        Assert.assertEquals(link.ids[0], links.list[0].ids[0])
        Assert.assertNotEquals(link.ids[1], links.list[0].ids[1])
        Assert.assertEquals(idsToAdd[0], links.list[0].ids[1])
    }

    @Test
    fun testUpdateLinkCommentCommand(){
        val link = Link(listOf(DomainId.create<TreatmentId>(), DomainId.create<EncounterVitalId>()), "Treatment and Resultant vital")
        links += link
        Assert.assertEquals(1, links.list.size)
        val newComment = "Vital that made me want to apply the treatment"
        handle(buildUpdateLinkCommentCommand(link.id, newComment))
        Assert.assertEquals(1, links.list.size)
        Assert.assertEquals(newComment, links.list[0].relationship)
        Assert.assertEquals(link.ids[0], links.list[0].ids[0])
        Assert.assertEquals(link.ids[1], links.list[0].ids[1])
    }

    @Test
    fun testDeleteLinkCommand(){
        val link = Link(listOf(DomainId.create<TreatmentId>(), DomainId.create<EncounterVitalId>()), "Treatment and Resultant vital")
        links += link
        Assert.assertEquals(1, links.list.size)
        handle(buildDeleteLinkCommand(link.id))
        Assert.assertEquals(0, links.list.size)
    }
}