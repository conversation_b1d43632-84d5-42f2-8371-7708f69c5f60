package gov.afrl.batdok.encounter.observations

import com.google.protobuf.Message
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.Event
import gov.afrl.batdok.encounter.Events
import gov.afrl.batdok.encounter.KnownEventTypes
import gov.afrl.batdok.encounter.Observations
import gov.afrl.batdok.encounter.Observations.Companion.includeObservationEvents
import gov.afrl.batdok.encounter.commands.buildLogObservationCommand
import gov.afrl.batdok.encounter.commands.buildRemoveObservationCommand
import gov.afrl.batdok.encounter.commands.buildUpdateObservationCommand
import gov.afrl.batdok.encounter.commands.buildUpdatePhysicalExamFindingCommand
import gov.afrl.batdok.encounter.observation.*
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class ObservationEventTest {

    val observations = Observations()
    val events = Events()

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS)) {
        EventCommandHandler(events) {
            includeObservationEvents(observations)
        }.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp))
    }

    @Test
    fun testUpdatePhysicalExamFindingCommand(){
        Assert.assertTrue(events.list.isEmpty())
        handle(buildUpdatePhysicalExamFindingCommand("Head", "Headache"))
        Assert.assertTrue(events.list.isNotEmpty())
        Assert.assertEquals("Head Physical Exam: Headache", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.OBJECTIVE.dataString, events.list[0].eventType)
    }

    @Test
    fun testLogObservationCommand(){
        Assert.assertTrue(events.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.PUPIL_REACTION.dataString, PupilReactionData(true, true)))
        Assert.assertTrue(events.list.isNotEmpty())
        Assert.assertEquals("Added Observation: Pupil Reaction: Left pupil is reactive", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.OBSERVATIONS.dataString, events.list[0].eventType)
    }

    @Test
    fun testLogObservationCommand_Wheezing(){
        Assert.assertTrue(events.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.WHEEZING.dataString, RespSideData(RespSideData.Type.LEFT.dataString)))
        Assert.assertTrue(events.list.isNotEmpty())
        Assert.assertEquals("Added Observation: Wheezing: on Left side", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.OBSERVATIONS.dataString, events.list[0].eventType)
    }

    @Test
    fun testLogObservationCommand_Rhonchi(){
        Assert.assertTrue(events.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.RHONCHI.dataString, RespSideData(RespSideData.Type.RIGHT.dataString)))
        Assert.assertTrue(events.list.isNotEmpty())
        Assert.assertEquals("Added Observation: Rhonchi: on Right side", events.list[0].event)
    }

    @Test
    fun testLogObservationCommand_Rales(){
        Assert.assertTrue(events.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.RALES.dataString, RespSideData(RespSideData.Type.BOTH.dataString)))
        Assert.assertTrue(events.list.isNotEmpty())
        Assert.assertEquals("Added Observation: Rales: on Both sides", events.list[0].event)

        handle(buildLogObservationCommand(CommonObservations.RALES.dataString, RespSideData(null)))
        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals("Added Observation: Rales: on neither side", events.list[1].event)
    }

    @Test
    fun testLogObservationCommand_Rhythm(){
        Assert.assertTrue(events.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.RHYTHM.dataString, RhythmData(RhythmData.Type.SVT.dataString)))
        Assert.assertTrue(events.list.isNotEmpty())
        Assert.assertEquals("Added Observation: Rhythm: SVT", events.list[0].event)
    }

    @Test
    fun testLogObservationCommand_CapRefill(){
        Assert.assertTrue(events.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.CAP_REFILL.dataString, CapillaryRefillData(3)))
        Assert.assertTrue(events.list.isNotEmpty())
        Assert.assertEquals("Added Observation: Capillary Refill: 3 seconds", events.list[0].event)
    }

    @Test
    fun testLogObservationCommand_Gastro(){
        Assert.assertTrue(events.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.GASTRO.dataString, GastroData("Open Abdomen")))
        Assert.assertTrue(events.list.isNotEmpty())
        Assert.assertEquals("Added Observation: Gastro: Open Abdomen", events.list[0].event)
    }

    @Test
    fun testLogObservationCommand_Integ(){
        Assert.assertTrue(events.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.INTEG.dataString, IntegData("Warm")))
        Assert.assertTrue(events.list.isNotEmpty())
        Assert.assertEquals("Added Observation: Integ: Warm", events.list[0].event)
    }

    @Test
    fun testLogObservation_PulseValues(){
        Assert.assertTrue(events.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.PULSE_VALUES.dataString, PulseValuesData(PulseValuesData.Quality.ONE.dataString, femoral = PulseValuesData.Quality.THREE.dataString)))
        Assert.assertTrue(events.list.isNotEmpty())
        Assert.assertEquals("Added Observation: Pulse Values: Brachial: +1, Femoral: +3", events.list[0].event)
    }

    @Test
    fun testLogObservation_RespEffortData(){
        Assert.assertTrue(events.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.RESP_EFFORT.dataString, RespEffortData(RespEffortData.Effort.AGONAL.dataString)))
        Assert.assertTrue(events.list.isNotEmpty())
        Assert.assertEquals("Added Observation: Respiratory Effort: Agonal", events.list[0].event)
    }

    @Test
    fun testLogObservation_ChestRiseFallData(){
        Assert.assertTrue(events.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.CHEST_EQUAL_RISE_FALL.dataString, ChestRiseFallData(ChestRiseFallData.Type.SYMMETRICAL.dataString)))
        Assert.assertTrue(events.list.isNotEmpty())
        Assert.assertEquals("Added Observation: Chest Equal Rise & Fall: Symmetrical", events.list[0].event)
    }

    @Test
    fun testLogObservation_Ultrasound(){
        Assert.assertTrue(events.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.ULTRASOUND.dataString, UltrasoundData("Test")))
        Assert.assertTrue(events.list.isNotEmpty())
        Assert.assertEquals("Added Observation: Ultrasound: Test", events.list[0].event)
    }

    @Test
    fun testLogObservation_PhysicalExam(){
        Assert.assertTrue(events.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.PHYSICAL_EXAM.dataString, PhysicalExamData("Test")))
        Assert.assertTrue(events.list.isNotEmpty())
        Assert.assertEquals("Added Observation: Physical Exam: Test", events.list[0].event)
    }

    @Test
    fun testLogObservation_HeadInjury(){
        Assert.assertTrue(events.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.HEAD_INJURY.dataString))
        Assert.assertTrue(events.list.isNotEmpty())
        Assert.assertEquals("Added Observation: Head Injury", events.list[0].event)
    }

    @Test
    fun testLogObservation_BloodSugar(){
        Assert.assertTrue(events.list.isEmpty())

        // blood sugar is displayed to one decimal place
        handle(buildLogObservationCommand(CommonObservations.BLOOD_SUGAR.dataString, BloodSugarData(123.0, "mmol/L")))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Added Observation: Blood Sugar: 123.0 mmol/L", events.list.last().event)

        // no rounding
        handle(buildLogObservationCommand(CommonObservations.BLOOD_SUGAR.dataString, BloodSugarData(4.5, "mmol/L")))
        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals("Added Observation: Blood Sugar: 4.5 mmol/L", events.list.last().event)

        // round up
        handle(buildLogObservationCommand(CommonObservations.BLOOD_SUGAR.dataString, BloodSugarData(4.55, "mmol/L")))
        Assert.assertEquals(3, events.list.size)
        Assert.assertEquals("Added Observation: Blood Sugar: 4.6 mmol/L", events.list.last().event)

        // round up
        handle(buildLogObservationCommand(CommonObservations.BLOOD_SUGAR.dataString, BloodSugarData(67.89, "mmol/L")))
        Assert.assertEquals(4, events.list.size)
        Assert.assertEquals("Added Observation: Blood Sugar: 67.9 mmol/L", events.list.last().event)

        // round down
        handle(buildLogObservationCommand(CommonObservations.BLOOD_SUGAR.dataString, BloodSugarData(1.234, "mmol/L")))
        Assert.assertEquals(5, events.list.size)
        Assert.assertEquals("Added Observation: Blood Sugar: 1.2 mmol/L", events.list.last().event)
    }

    @Test
    fun testLogObservation_OtherDiagnostics(){
        Assert.assertTrue(events.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.OTHER_DIAGNOSTICS.dataString, OtherDiagnostics("Diagnostic")))
        Assert.assertTrue(events.list.isNotEmpty())
        Assert.assertEquals("Added Observation: Other Diagnostics: Diagnostic", events.list[0].event)
    }

    @Test
    fun testLogObservationCommand_BreathSounds(){
        Assert.assertTrue(events.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.BREATH_SOUNDS.dataString, BreathSoundsData(listOf(BreathSoundsData.Type.LEFT_ABSENT.dataString))))
        Assert.assertTrue(events.list.isNotEmpty())
        Assert.assertEquals("Added Observation: ${CommonObservations.BREATH_SOUNDS.dataString}: ${BreathSoundsData.Type.LEFT_ABSENT.dataString}", events.list[0].event)
    }

    @Test
    fun testUpdateObservationCommand(){
        val oldObservation = Observation(
            CommonObservations.PUPIL_REACTION.dataString,
            PupilReactionData(false, true),
            DomainId.create(),
            Instant.now().truncatedTo(ChronoUnit.SECONDS)
        )
        observations += oldObservation
        Assert.assertTrue(events.list.isEmpty())
        handle(buildUpdateObservationCommand(
            Observation(
                name = CommonObservations.PUPIL_REACTION.dataString,
                observationData = PupilReactionData(false, false),
                id = oldObservation.id
            )
        ))
        Assert.assertTrue(events.list.isNotEmpty())
        Assert.assertEquals("Updated Observation: Pupil Reaction: Right pupil is not reactive", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.OBSERVATIONS.dataString, events.list[0].eventType)
        Assert.assertEquals(oldObservation.id, events.list[0].referencedItem)
    }

    @Test
    fun testUpdateObservationCommand_OnlySomeFields(){
        val commandTime = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val oldObservation = Observation(
            CommonObservations.PUPIL_REACTION.dataString,
            PupilReactionData(false, true),
            DomainId.create(),
            Instant.now().truncatedTo(ChronoUnit.SECONDS)
        )
        observations += oldObservation
        Assert.assertTrue(events.list.isEmpty())
        handle(buildUpdateObservationCommand(
            Observation(
                name = CommonObservations.PUPIL_REACTION.dataString,
                observationData = PupilReactionData(false, false),
                id = oldObservation.id
            )
        ), timestamp = commandTime)
        Assert.assertTrue(events.list.isNotEmpty())
        Assert.assertEquals("Updated Observation: Pupil Reaction: Right pupil is not reactive", events.list[0].event)
        Assert.assertEquals(commandTime, events.list[0].timestamp)
        Assert.assertEquals(oldObservation.id, events.list[0].referencedItem)
    }

    @Test
    fun testRemoveObservationCommand_Proper(){
        val oldObservation = Observation(
            CommonObservations.PUPIL_REACTION.dataString,
            PupilReactionData(false, true),
            DomainId.create(),
            Instant.now().truncatedTo(ChronoUnit.SECONDS)
        )
        observations += oldObservation
        events += Event("Test Event", oldObservation.id.copy(), Instant.now().truncatedTo(ChronoUnit.SECONDS))
        Assert.assertTrue(events.list.isNotEmpty())
        handle(buildRemoveObservationCommand(oldObservation.id, false))
        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals("Removed Observation: Pupil Reaction: Right pupil is reactive", events.list[1].event)
        Assert.assertEquals(oldObservation.id, events.list[1].referencedItem)
    }

    @Test
    fun testRemoveObservationCommand_Error(){
        val oldObservation = Observation(
            CommonObservations.PUPIL_REACTION.dataString,
            PupilReactionData(false, true),
            DomainId.create(),
            Instant.now().truncatedTo(ChronoUnit.SECONDS)
        )
        observations += oldObservation
        events += Event("Test Event", oldObservation.id.copy(), Instant.now().truncatedTo(ChronoUnit.SECONDS))
        Assert.assertTrue(events.list.isNotEmpty())
        handle(buildRemoveObservationCommand(oldObservation.id, true))
        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals("Removed Observation: Pupil Reaction: Right pupil is reactive due to documentation error", events.list[1].event)
        Assert.assertEquals(oldObservation.id, events.list[1].referencedItem)
    }

    //region Observation Events

    @Test
    fun testBooleanEvent(){
        Assert.assertEquals("Yes", BooleanObservation(true).toEventText())
        Assert.assertEquals("No", BooleanObservation(false).toEventText())
    }

    @Test
    fun testPupilDilationEvent(){
        Assert.assertEquals("Left pupil dilated 1mm", PupilDilationData(1, null).toEventText())
        Assert.assertEquals("Right pupil dilated 10mm", PupilDilationData(null, 10).toEventText())
        Assert.assertEquals("Left pupil dilated 1mm, Right pupil dilated 10mm", PupilDilationData(1, 10).toEventText())
    }

    @Test
    fun testPupilReactiveEvent(){
        Assert.assertEquals("Left pupil is reactive", PupilReactionData(true, true).toEventText())
        Assert.assertEquals("Right pupil is not reactive", PupilReactionData(false, false).toEventText())
    }

    @Test
    fun testLogObservationCommand_Pulse(){
        Assert.assertTrue(events.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.PULSE_TYPES.dataString, PulseTypeData(PulseTypeData.Quality.NORMAL.dataString, PulseTypeData.Location.CAROTID_LEFT.dataString)))
        Assert.assertTrue(events.list.isNotEmpty())
        Assert.assertEquals("Added Observation: Pulse Types: Quality: Normal, Location: Carotid Left", events.list[0].event)
    }

    @Test
    fun testPulseTypeDataEvent(){
        Assert.assertEquals("Quality: N/A, Location: N/A", PulseTypeData().toEventText())
        Assert.assertEquals("Quality: Normal, Location: N/A", PulseTypeData(PulseTypeData.Quality.NORMAL.dataString).toEventText())
        Assert.assertEquals("Quality: N/A, Location: Carotid Left", PulseTypeData(location = PulseTypeData.Location.CAROTID_LEFT.dataString).toEventText())
        Assert.assertEquals("Quality: Normal, Location: Carotid Left", PulseTypeData(PulseTypeData.Quality.NORMAL.dataString, PulseTypeData.Location.CAROTID_LEFT.dataString).toEventText())
    }

    @Test
    fun testLogObservationCommand_Feeling(){
        Assert.assertTrue(events.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.FEELING.dataString, FeelingData(FeelingData.Type.NORMAL_SENSATION.dataString)))
        Assert.assertTrue(events.list.isNotEmpty())
        Assert.assertEquals("Added Observation: Feeling: Normal Sensation", events.list[0].event)
    }

    @Test
    fun testRhythmEvent(){
        Assert.assertEquals("SVT", RhythmData(RhythmData.Type.SVT.dataString).toEventText())
    }

    @Test
    fun testGastroDataEvent(){
        Assert.assertEquals("Open Abdomen", GastroData(GastroData.Type.OPENABDOMEN.dataString).toEventText())
    }

    @Test
    fun testIntegDataEvent(){
        Assert.assertEquals("Warm", IntegData(IntegData.Type.WARM.dataString).toEventText())
    }

    @Test
    fun testRespSideDataEvent(){
        Assert.assertEquals("on Left side", RespSideData(RespSideData.Type.LEFT.dataString).toEventText())
        Assert.assertEquals("on Right side", RespSideData(RespSideData.Type.RIGHT.dataString).toEventText())
        Assert.assertEquals("on Both sides", RespSideData(RespSideData.Type.BOTH.dataString).toEventText())
        Assert.assertEquals("on neither side", RespSideData(null).toEventText())
    }

    @Test
    fun testPulseValuesDataEvent(){
        Assert.assertEquals(
            "Brachial: A, Carotid: A, Femoral: A, Pedal: A, Radial: A, Temperature: A",
            PulseValuesData(
                PulseValuesData.Quality.A.dataString,
                PulseValuesData.Quality.A.dataString,
                PulseValuesData.Quality.A.dataString,
                PulseValuesData.Quality.A.dataString,
                PulseValuesData.Quality.A.dataString,
                PulseValuesData.Quality.A.dataString,
            ).toEventText()
        )
        Assert.assertEquals(
            "Brachial: +1, Femoral: +2, Radial: +3",
            PulseValuesData(
                PulseValuesData.Quality.ONE.dataString,
                null,
                PulseValuesData.Quality.TWO.dataString,
                null,
                PulseValuesData.Quality.THREE.dataString,
                null,
            ).toEventText()
        )
        Assert.assertEquals("", PulseValuesData().toEventText())
    }

    @Test
    fun testBreathSoundsEvent(){
        Assert.assertEquals(BreathSoundsData.Type.LEFT_ABSENT.dataString, BreathSoundsData(listOf(BreathSoundsData.Type.LEFT_ABSENT.dataString)).toEventText())
        Assert.assertEquals(BreathSoundsData.Type.LEFT_DIMINISHED.dataString, BreathSoundsData(listOf(BreathSoundsData.Type.LEFT_DIMINISHED.dataString)).toEventText())
        Assert.assertEquals(BreathSoundsData.Type.LEFT_NORMAL.dataString, BreathSoundsData(listOf(BreathSoundsData.Type.LEFT_NORMAL.dataString)).toEventText())
        Assert.assertEquals(BreathSoundsData.Type.RIGHT_ABSENT.dataString, BreathSoundsData(listOf(BreathSoundsData.Type.RIGHT_ABSENT.dataString)).toEventText())
        Assert.assertEquals(BreathSoundsData.Type.RIGHT_DIMINISHED.dataString, BreathSoundsData(listOf(BreathSoundsData.Type.RIGHT_DIMINISHED.dataString)).toEventText())
        Assert.assertEquals(BreathSoundsData.Type.RIGHT_NORMAL.dataString, BreathSoundsData(listOf(BreathSoundsData.Type.RIGHT_NORMAL.dataString)).toEventText())
        Assert.assertEquals(
            "${BreathSoundsData.Type.LEFT_ABSENT.dataString}, ${BreathSoundsData.Type.RIGHT_ABSENT.dataString}",
            BreathSoundsData(listOf(BreathSoundsData.Type.LEFT_ABSENT.dataString, BreathSoundsData.Type.RIGHT_ABSENT.dataString)).toEventText()
        )
    }

    @Test
    fun testLogObservationCommand_BooleanObservationData_true_Diabetes(){
        Assert.assertTrue(events.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.DIABETES.dataString, BooleanObservation(true)))
        Assert.assertTrue(events.list.isNotEmpty())
        Assert.assertEquals("Added Observation: ${CommonObservations.DIABETES.dataString}: Yes", events.list[0].event)
    }

    @Test
    fun testLogObservationCommand_BooleanObservationData_false_Diabetes(){
        Assert.assertTrue(events.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.DIABETES.dataString, BooleanObservation(false)))
        Assert.assertTrue(events.list.isNotEmpty())
        Assert.assertEquals("Added Observation: ${CommonObservations.DIABETES.dataString}: No", events.list[0].event)
    }

    @Test
    fun testLogObservationCommand_BooleanObservationData_null_Diabetes(){
        Assert.assertTrue(events.list.isEmpty())
        handle(buildLogObservationCommand(CommonObservations.DIABETES.dataString, BooleanObservation()))
        Assert.assertTrue(events.list.isNotEmpty())
        Assert.assertEquals("Added Observation: ${CommonObservations.DIABETES.dataString}", events.list[0].event)
    }

    @Test
    fun testLogObservationCommand_EFastExam() {
        Assert.assertTrue(events.list.isEmpty())
        val examData = EFastExamData(
            leftLungSliding = EFastExamData.Type.POSITIVE.dataString,
            rightLungSliding = EFastExamData.Type.NEGATIVE.dataString,
            pericardialFluid = EFastExamData.Type.EQUIVOCAL.dataString,
            rightUpperQuadrant = EFastExamData.Type.POSITIVE.dataString,
            leftUpperQuadrant = EFastExamData.Type.NEGATIVE.dataString,
            suprapubicFluid = EFastExamData.Type.EQUIVOCAL.dataString,
            interpretation = EFastExamData.Type.POSITIVE.dataString
        )
        handle(buildLogObservationCommand(CommonObservations.EFAST_EXAM.dataString, examData))
        Assert.assertTrue(events.list.isNotEmpty())
        Assert.assertEquals("Added Observation: EFAST Exam: Left Lung Sliding - Positive, " +
                "Right Lung Sliding - Negative, Pericardial Fluid - Equivocal, Right Upper Quadrant - Positive, " +
                "Left Upper Quadrant - Negative, Suprapubic Fluid - Equivocal, Interpretation - Positive", events.list[0].event)
    }
    //endregion
}