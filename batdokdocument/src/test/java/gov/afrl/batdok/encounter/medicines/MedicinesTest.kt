package gov.afrl.batdok.encounter.medicines

import gov.afrl.batdok.encounter.ids.MedicineId
import gov.afrl.batdok.encounter.medicine.KnownMedTypes
import gov.afrl.batdok.encounter.medicine.Medicine
import gov.afrl.batdok.encounter.medicine.Medicines
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant

class MedicinesTest {
    val medicines = Medicines()
    val id1 = DomainId.create<MedicineId>()
    val id2 = DomainId.create<MedicineId>()

    @Test
    fun testGetMedsByType_String() {
        val medicine1 = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            Instant.ofEpochSecond(12331),
            id1,
            "testroute",
            1.2.toFloat(),
            "cm",
            "45646",
            "11111",
            "tsttype1"
        )
        val medicine2 = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            Instant.ofEpochSecond(12331),
            id2,
            "testroute",
            1.2.toFloat(),
            "cm",
            "45646",
            "11111",
            "tsttype2"
        )
        medicines += medicine1
        medicines += medicine2
        Assert.assertEquals(listOf(medicine1), medicines.getAll("tsttype1"))
        Assert.assertEquals(listOf(medicine2), medicines.getAll("tsttype2"))
        Assert.assertEquals(listOf<Medicine>(), medicines.getAll("tsttype3"))
    }
    @Test
    fun testGetMedsByType_Enum() {
        val medicine1 = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            Instant.ofEpochSecond(12331),
            id1,
            "testroute",
            1.2.toFloat(),
            "cm",
            "45646",
            "11111",
            KnownMedTypes.ANTIBIOTIC.dataString
        )
        val medicine2 = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            Instant.ofEpochSecond(12331),
            id2,
            "testroute",
            1.2.toFloat(),
            "cm",
            "45646",
            "11111",
            KnownMedTypes.ANALGESIC.dataString
        )
        medicines += medicine1
        medicines += medicine2
        Assert.assertEquals(listOf(medicine1), medicines.getAll(KnownMedTypes.ANTIBIOTIC))
        Assert.assertEquals(listOf(medicine2), medicines.getAll(KnownMedTypes.ANALGESIC))
        Assert.assertEquals(listOf<Medicine>(), medicines.getAll(KnownMedTypes.FLUID))
    }
    @Test
    fun testMinus() {
        val medicine1 = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            Instant.ofEpochSecond(12331),
            id1,
            "testroute",
            1.2.toFloat(),
            "cm",
            "45646",
            "11111",
            "tsttype1"
        )
        val medicine2 = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            Instant.ofEpochSecond(12331),
            id2,
            "testroute",
            1.2.toFloat(),
            "cm",
            "45646",
            "11111",
            "tsttype2"
        )
        medicines += listOf(medicine1, medicine2)
        val result = medicines - medicine1.id
        Assert.assertEquals(listOf(medicine2), result)
    }

    @Test
    fun testGetAllExcept_String() {
        val medicine1 = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            Instant.ofEpochSecond(12331),
            id1,
            "testroute",
            1.2.toFloat(),
            "cm",
            "45646",
            "11111",
            KnownMedTypes.ANTIBIOTIC.dataString
        )
        val medicine2 = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            Instant.ofEpochSecond(12331),
            id2,
            "testroute",
            1.2.toFloat(),
            "cm",
            "45646",
            "11111",
            KnownMedTypes.ANALGESIC.dataString
        )
        medicines += listOf(medicine1, medicine2)

        val result = medicines.getAllExcept(KnownMedTypes.ANTIBIOTIC.dataString)

        Assert.assertEquals(listOf(medicine2), result)
    }

    @Test
    fun testGetAllExcept_Enum() {
        val medicine1 = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            Instant.ofEpochSecond(12331),
            id1,
            "testroute",
            1.2.toFloat(),
            "cm",
            "45646",
            "11111",
            KnownMedTypes.ANTIBIOTIC.dataString
        )
        val medicine2 = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            Instant.ofEpochSecond(12331),
            id2,
            "testroute",
            1.2.toFloat(),
            "cm",
            "45646",
            "11111",
            KnownMedTypes.FLUID.dataString
        )
        medicines += listOf(medicine1, medicine2)

        val result = medicines.getAllExcept(KnownMedTypes.ANALGESIC)

        Assert.assertEquals(listOf(medicine1, medicine2), result)
    }

    @Test
    fun testGetVolumeString(){
        val med = Medicine(
            name = "",
            ndc = "",
            administrationTime = Instant.now(),
            volume = 12.345f,
            docId = DomainId.create()
        )
        Assert.assertEquals("12.345", med.getVolumeString())
        val medWithTrailingZero = Medicine(
            name = "",
            ndc = "",
            administrationTime = Instant.now(),
            volume = 12f,
            docId = DomainId.create()
        )
        Assert.assertEquals("12", medWithTrailingZero.getVolumeString())
    }
    @Test
    fun testGetVolumeString_nullVolume(){
        val med = Medicine(
            name = "",
            volume = null,
            unit = "unit"
        )
        Assert.assertEquals("unit", med.getVolumeString())
    }
    @Test
    fun testGetVolumeString_nullUnit(){
        val med = Medicine(
            name = "",
            volume = 1f,
            unit = null
        )
        Assert.assertEquals("1", med.getVolumeString())
    }
    @Test
    fun testGetVolumeString_nullVolumeUnit(){
        val med = Medicine(
            name = "",
            volume = null,
            unit = null
        )
        Assert.assertEquals("", med.getVolumeString())
    }

    @Test
    fun testSorting(){
        val medicine1 = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            Instant.ofEpochSecond(2),
            id1,
            "testroute",
            1.2.toFloat(),
            "cm",
            "45646",
            "11111",
            KnownMedTypes.ANTIBIOTIC.dataString
        )
        val medicine2 = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            Instant.ofEpochSecond(1),
            id2,
            "testroute",
            1.2.toFloat(),
            "cm",
            "45646",
            "11111",
            KnownMedTypes.ANALGESIC.dataString
        )
        medicines += listOf(medicine1, medicine2)

        Assert.assertEquals(listOf(medicine2, medicine1), medicines.list.sorted())
    }
}