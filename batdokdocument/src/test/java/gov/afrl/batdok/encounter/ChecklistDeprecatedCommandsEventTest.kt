package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.Checklist.Companion.includeChecklistEvents
import gov.afrl.batdok.encounter.commands.buildChecklistCommand
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

@Deprecated(
    message = "Can delete this once Checklist, ChecklistCommands deprecations are removed.  ChecklistnewCommandsEventTest uses the nondeprecated commands"
)
class ChecklistDeprecatedCommandsEventTest {

    val events = Events()

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS)) {
        EventCommandHandler(events) {
            includeChecklistEvents()
        }.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp))
    }

    @Test
    fun testChecklistItem_known_checked(){
        Assert.assertEquals(0, events.list.size)
        handle(buildChecklistCommand(KnownChecklistItems.CHECK_DRESSINGS, true, KnownChecklistItems.CHECK_DRESSINGS.defaultInterval))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("PCC Checklist: ${KnownChecklistItems.CHECK_DRESSINGS.dataString} Selected", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.PCC_CHECKLIST.dataString, events.list[0].eventType)
    }

    @Test
    fun testChecklistItem_known_uncheck(){
        Assert.assertEquals(0, events.list.size)
        handle(buildChecklistCommand(KnownChecklistItems.CHECK_DRESSINGS, false, KnownChecklistItems.CHECK_DRESSINGS.defaultInterval))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("PCC Checklist: ${KnownChecklistItems.CHECK_DRESSINGS.dataString} Unselected", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.PCC_CHECKLIST.dataString, events.list[0].eventType)
    }

    @Test
    fun testChecklistItem_known_newInterval(){
        Assert.assertEquals(0, events.list.size)
        handle(buildChecklistCommand(KnownChecklistItems.CHECK_DRESSINGS, false, interval = "Custom"))

        //Event will still be created if check is passed
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("PCC Checklist: ${KnownChecklistItems.CHECK_DRESSINGS.dataString} Unselected", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.PCC_CHECKLIST.dataString, events.list[0].eventType)
    }

    @Test
    fun testChecklistItem_known_newInterval_existing(){
        Assert.assertEquals(0, events.list.size)
        handle(buildChecklistCommand(KnownChecklistItems.CHECK_DRESSINGS, null, "Custom"))

        //Event isn't created if checked is null
        Assert.assertEquals(0, events.list.size)
    }


    @Test
    fun testChecklistItem_custom_checked(){
        Assert.assertEquals(0, events.list.size)
        handle(buildChecklistCommand("Custom Item", true, "Once"))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("PCC Checklist: Custom Item Selected", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.PCC_CHECKLIST.dataString, events.list[0].eventType)
    }

    @Test
    fun testChecklistItem_custom_uncheck(){
        Assert.assertEquals(0, events.list.size)
        handle(buildChecklistCommand("Custom Item", false, "Once"))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("PCC Checklist: Custom Item Unselected", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.PCC_CHECKLIST.dataString, events.list[0].eventType)
    }

    @Test
    fun testChecklistItem_custom_newInterval(){
        Assert.assertEquals(0, events.list.size)
        handle(buildChecklistCommand("Custom Item", false, interval = "Custom"))

        //Event will still be created if check is passed
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("PCC Checklist: Custom Item Unselected", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.PCC_CHECKLIST.dataString, events.list[0].eventType)
    }

    @Test
    fun testChecklistItem_custom_newInterval_existing(){
        Assert.assertEquals(0, events.list.size)
        handle(buildChecklistCommand("Custom Item", null, "Custom"))

        //Event isn't created if checked is null
        Assert.assertEquals(0, events.list.size)
    }
}