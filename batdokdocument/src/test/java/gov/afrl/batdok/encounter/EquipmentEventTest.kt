package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.commands.proto.EquipmentCommands.AddEquipmentCommand
import gov.afrl.batdok.encounter.commands.buildAddEquipmentCommand
import gov.afrl.batdok.encounter.commands.buildAddRemoveEquipmentCommands
import gov.afrl.batdok.encounter.commands.buildAddRemoveEquipmentFailureCommands
import gov.afrl.batdok.encounter.commands.buildAddRemovePowerLossCommands
import gov.afrl.batdok.encounter.commands.buildAddRemoveUserErrorCommands
import gov.afrl.batdok.encounter.commands.buildRemoveEquipmentCommand
import gov.afrl.batdok.encounter.commands.buildUpdateEquipmentCommand
import gov.afrl.batdok.encounter.commands.modifyEquipimentEventHandler
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.encounter.metadata.*
import gov.afrl.batdok.encounter.metadata.EquipmentLog.Companion.includeEquipmentEvents
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class EquipmentEventTest {

    private val events = Events()

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS), commandId: CommandId = DomainId.create()) {
        EventCommandHandler(events) {
            includeEquipmentEvents()
        }.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp, commandId))
    }


// Orthopedic Data

    @Test
    fun testAddEquipmentCommandOrthopedicData_Event(){
        val equipment = Equipment("Orthopedic", OrthopedicData("Cast", "Plaster", "RUE"))
        Assert.assertEquals(0, events.list.size)
        handle(buildAddEquipmentCommand(equipment))
        Assert.assertEquals("Added equipment: Orthopedic (Cast: Plaster, Location: RUE)", events.list[0].event)
    }


    @Test
    fun testRemoveEquipmentCommandOrthopedicData_Event(){
        val equipment = Equipment("Orthopedic", OrthopedicData("Cast", "Plaster", "RUE"))
        Assert.assertEquals(0, events.list.size)
        handle(buildRemoveEquipmentCommand(equipment, false))
        Assert.assertEquals("Removed equipment: Orthopedic (Cast: Plaster, Location: RUE)", events.list[0].event)
    }


    @Test
    fun testUpdateEquipmentCommandOrthopedicData_Event(){
        val equipment = Equipment("Orthopedic", OrthopedicData("Cast", "Plaster", "RUE"))
        Assert.assertEquals(0, events.list.size)
        handle(buildUpdateEquipmentCommand(equipment))
        Assert.assertEquals("Modified equipment: Orthopedic (Cast: Plaster, Location: RUE)", events.list[0].event)
    }


    // Boolean Data

    @Test
    fun testAddBooleanEquipmentCommand_Event(){
        val equipment =  Equipment("NG Tube")
        Assert.assertEquals(0, events.list.size)
        handle(buildAddEquipmentCommand(equipment))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Added equipment: NG Tube", events.list[0].event)
    }

    @Test
    fun testRemoveBooleanEquipmentCommand_Event(){
        val equipment =  Equipment("NG Tube")
        Assert.assertEquals(0, events.list.size)
        handle(buildRemoveEquipmentCommand(equipment, false))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Removed equipment: NG Tube", events.list[0].event)
    }


    @Test
    fun testUpdateBooleanEquipmentCommand_Event(){
        val equipment =  Equipment("NG Tube")
        Assert.assertEquals(0, events.list.size)
        handle(buildUpdateEquipmentCommand(equipment))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Modified equipment: NG Tube", events.list[0].event)
    }

    // Integer Data

    @Test
    fun testAddIntegerEquipmentCommand_Event(){
        val equipment = Equipment("IV Pumps", IntegerEquipmentData(1))
        Assert.assertEquals(0, events.list.size)
        handle(buildAddEquipmentCommand(equipment))
        Assert.assertEquals("Added equipment: IV Pumps (1)", events.list[0].event)
    }

    @Test
    fun testRemoveIntegerEquipmentCommand_Remove(){
        val equipment = Equipment("IV Pumps", IntegerEquipmentData(1))
        Assert.assertEquals(0, events.list.size)
        handle(buildRemoveEquipmentCommand( equipment, false))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Removed equipment: IV Pumps (1)", events.list[0].event)
    }

    @Test
    fun testRemoveIntegerEquipmentCommand_RemoveDocumentationError(){
        val equipment = Equipment("IV Pumps", IntegerEquipmentData(1))
        Assert.assertEquals(0, events.list.size)
        handle(buildRemoveEquipmentCommand( equipment, true))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Removed equipment: IV Pumps (1) due to documentation error", events.list[0].event)
    }

    @Test
    fun testUpdateIntegerEquipmentCommand_Remove(){
        val equipment = Equipment("IV Pumps", IntegerEquipmentData(2))
        Assert.assertEquals(0, events.list.size)
        handle(buildUpdateEquipmentCommand( equipment))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Modified equipment: IV Pumps (2)", events.list[0].event)
    }

    // String Data

    @Test
    fun testAddStringEquipmentCommand_Event(){
        val equipment = Equipment("Other", StringEquipmentData("OtherString"))
        Assert.assertEquals(0, events.list.size)
        handle(buildAddEquipmentCommand(equipment))
        Assert.assertEquals("Added equipment: Other (OtherString)", events.list[0].event)
    }

    @Test
    fun testRemoveStringEquipmentCommand_Event(){
        val equipment = Equipment("Other", StringEquipmentData("OtherString"))
        Assert.assertEquals(0, events.list.size)
        handle(buildRemoveEquipmentCommand(equipment, false))
        Assert.assertEquals("Removed equipment: Other (OtherString)", events.list[0].event)
    }

    @Test
    fun testUpdateStringEquipmentCommand_Event(){
        val equipment = Equipment("Other", StringEquipmentData("OtherString"))
        Assert.assertEquals(0, events.list.size)
        handle(buildUpdateEquipmentCommand(equipment))
        Assert.assertEquals("Modified equipment: Other (OtherString)", events.list[0].event)
    }


    @Test
    fun testAddRemoveEquipmentCommand_Add(){
        val equipment = listOf(Equipment("Test", LitterData(true, true, true)))
        Assert.assertEquals(0, events.list.size)
        handle(buildAddRemoveEquipmentCommands(equipment, listOf()))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Added equipment: Test (facing front, head first, right side)", events.list[0].event)
    }
    
    @Test
    fun testAddRemoveEquipmentCommand_Remove(){
        val equipment = listOf(Equipment("Test", LitterData(true, true, true)))
        Assert.assertEquals(0, events.list.size)
        handle(buildAddRemoveEquipmentCommands(listOf(), equipment))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Removed equipment: Test (facing front, head first, right side)", events.list[0].event)
    }

    @Test
    fun testAddRemoveEquipmentCommand_AddAndRemove(){
        val equipmentToAdd = listOf(Equipment("Test", LitterData(true, true, true)))
        val equipmentToRemove = listOf(Equipment("Test2"))
        Assert.assertEquals(0, events.list.size)
        handle(buildAddRemoveEquipmentCommands(equipmentToAdd, equipmentToRemove))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Added equipment: Test (facing front, head first, right side)\nRemoved equipment: Test2", events.list[0].event)
    }



    @Test
    fun testAddRemovePowerLossCommand_Add(){
        val equipment = listOf(Equipment("Test", LitterData(true, true, true)))
        Assert.assertEquals(0, events.list.size)
        handle(buildAddRemovePowerLossCommands(equipment, listOf()))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Added equipment power loss: Test (facing front, head first, right side)", events.list[0].event)
    }

    @Test
    fun testAddRemovePowerLossCommand_Remove(){
        val equipment = listOf(Equipment("Test", LitterData(true, true, true)))
        Assert.assertEquals(0, events.list.size)
        handle(buildAddRemovePowerLossCommands(listOf(), equipment))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Removed equipment power loss: Test (facing front, head first, right side)", events.list[0].event)
    }

    @Test
    fun testAddRemovePowerLossCommand_AddAndRemove(){
        val equipmentToAdd = listOf(Equipment("Test", LitterData(true, true, true)))
        val equipmentToRemove = listOf(Equipment("Test2"))
        Assert.assertEquals(0, events.list.size)
        handle(buildAddRemovePowerLossCommands(equipmentToAdd, equipmentToRemove))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Added equipment power loss: Test (facing front, head first, right side)\nRemoved equipment power loss: Test2", events.list[0].event)
    }

    @Test
    fun testAddRemoveUserErrorCommand_Add(){
        val equipment = listOf(Equipment("Test", LitterData(true, true, true)))
        Assert.assertEquals(0, events.list.size)
        handle(buildAddRemoveUserErrorCommands(equipment, listOf()))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Added equipment user error: Test (facing front, head first, right side)", events.list[0].event)
    }

    @Test
    fun testAddRemoveUserErrorCommand_Remove(){
        val equipment = listOf(Equipment("Test", LitterData(true, true, true)))
        Assert.assertEquals(0, events.list.size)
        handle(buildAddRemoveUserErrorCommands(listOf(), equipment))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Removed equipment user error: Test (facing front, head first, right side)", events.list[0].event)
    }

    @Test
    fun testAddRemoveUserErrorCommand_AddAndRemove(){
        val equipmentToAdd = listOf(Equipment("Test", LitterData(true, true, true)))
        val equipmentToRemove = listOf(Equipment("Test2"))
        Assert.assertEquals(0, events.list.size)
        handle(buildAddRemoveUserErrorCommands(equipmentToAdd, equipmentToRemove))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Added equipment user error: Test (facing front, head first, right side)\nRemoved equipment user error: Test2", events.list[0].event)
    }


    @Test
    fun testAddRemoveEquipmentFailureCommand_Add(){
        val equipment = listOf(Equipment("Test", LitterData(true, true, true)))
        Assert.assertEquals(0, events.list.size)
        handle(buildAddRemoveEquipmentFailureCommands(equipment, listOf()))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Added equipment failure: Test (facing front, head first, right side)", events.list[0].event)
    }

    @Test
    fun testAddRemoveEquipmentFailureCommand_Remove(){
        val equipment = listOf(Equipment("Test", LitterData(true, true, true)))
        Assert.assertEquals(0, events.list.size)
        handle(buildAddRemoveEquipmentFailureCommands(listOf(), equipment))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Removed equipment failure: Test (facing front, head first, right side)", events.list[0].event)
    }

    @Test
    fun testAddRemoveEquipmentFailureCommand_AddAndRemove(){
        val equipmentToAdd = listOf(Equipment("Test", LitterData(true, true, true)))
        val equipmentToRemove = listOf(Equipment("Test2"))
        Assert.assertEquals(0, events.list.size)
        handle(buildAddRemoveEquipmentFailureCommands(equipmentToAdd, equipmentToRemove))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Added equipment failure: Test (facing front, head first, right side)\nRemoved equipment failure: Test2", events.list[0].event)
    }

}