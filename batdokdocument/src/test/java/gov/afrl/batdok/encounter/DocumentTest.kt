package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.Observations.RemoveObservation
import gov.afrl.batdok.commands.proto.changeNameCommand
import gov.afrl.batdok.encounter.commands.*
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.encounter.ids.InputOutputId
import gov.afrl.batdok.encounter.ids.ObservationId
import gov.afrl.batdok.encounter.ids.TreatmentId
import gov.afrl.batdok.encounter.observation.Observation
import gov.afrl.batdok.encounter.orders.OrderLine
import gov.afrl.batdok.encounter.orders.OrderStatus
import gov.afrl.batdok.encounter.orders.OrderType
import gov.afrl.batdok.encounter.treatment.CommonTreatments
import gov.afrl.batdok.encounter.treatment.LineData
import gov.afrl.batdok.encounter.treatment.Treatment
import gov.afrl.batdok.util.CURRENT_ENCOUNTER_VERSION
import gov.afrl.batdok.util.buildCommandData
import gov.afrl.batdok.util.buildDocumentCommand
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class DocumentTest {

    val document = Document()

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS), compatibleRange: Pair<Int?, Int?>? = null) {
        document.handle(
            listOf(buildCommandData(subCommand, callsign, timestamp, compatibilityRange = compatibleRange))
        )
    }

    @Test
    fun testHandle_SingleEncounter(){
        val newName = "Name"
        Assert.assertNotEquals(newName, document.info.name.toString())
        Assert.assertEquals(0, document.events.list.size)
        handle(changeNameCommand { last = newName })
        Assert.assertEquals(newName, document.info.name?.last)
        Assert.assertEquals(1, document.events.list.size)
        Assert.assertEquals("Set name to $newName", document.events.list[0].event)
    }

    @Test
    fun testHandle_multiEncounter(){
        val otherDocumentId = DomainId.create<DocumentId>()
        Assert.assertEquals(0, document.treatments.list.size)
        Assert.assertEquals(0, document.events.list.size)
        fun buildTreatmentCommand(documentId: DocumentId, name: String) = buildDocumentCommand(
            documentId,
            DomainId.create(),
            commandData = listOf(
                buildCommandData(
                    buildAddTreatmentCommand(Treatment(name)),
                    timestamp = Instant.ofEpochSecond(1)
                )
            )
        )
        val command1 = buildTreatmentCommand(document.id, "Treatment1")
        val command2 = buildTreatmentCommand(otherDocumentId, "Treatment2")

        document.handle(listOf(command1, command2))

        Assert.assertEquals(2, document.treatments.list.size)
        Assert.assertEquals(document.id, document.treatments.list[0].documentId)
        Assert.assertEquals(otherDocumentId, document.treatments.list[1].documentId)
        Assert.assertEquals(2, document.events.list.size)
        Assert.assertEquals(document.id, document.events.list[0].documentId)
        Assert.assertEquals(otherDocumentId, document.events.list[1].documentId)
    }

    @Test
    fun testCompatibilityHandling(){
        //A command data without the range is always compatible
        handle(changeNameCommand { last = "Name" })
        Assert.assertEquals(1, document.events.list.size)

        //An empty range is always compatible
        handle(changeNameCommand { last = "Name" }, compatibleRange = null to null)
        Assert.assertEquals(2, document.events.list.size)

        //The current version is 1, so it is not in [0,1)
        handle(changeNameCommand { last = "Name" }, compatibleRange = null to CURRENT_ENCOUNTER_VERSION)
        Assert.assertEquals(2, document.events.list.size)

        //It is in the range [0,2)
        handle(changeNameCommand { last = "Name" }, compatibleRange = null to CURRENT_ENCOUNTER_VERSION+1)
        Assert.assertEquals(3, document.events.list.size)

        //It isn't in the range [3,MAX)
        handle(changeNameCommand { last = "Name" }, compatibleRange = (CURRENT_ENCOUNTER_VERSION +1) to null)
        Assert.assertEquals(3, document.events.list.size)
    }

    @Test
    fun testLinkingObjects() {
        val linkId: LinkId = DomainId.create()
        val treatId: TreatmentId = DomainId.create()
        val inputOutputId: InputOutputId = DomainId.create()
        val observationId: ObservationId = DomainId.create()

        val idsToLink = listOf(treatId, observationId, inputOutputId)
        val link = Link(idsToLink, "Test Comment", id = linkId, documentId = document.id)

        val treat = Treatment("Test Treatment", id = treatId)
        val observation = Observation("Test Observation", id = observationId)
        val ioItem = InputOutput(inputOutputId, Instant.now(), "Test IO", "Route", 1.0, "unit", documentId = document.id)

        handle(buildAddTreatmentCommand(treat))
        handle(buildLogObservationCommand(observation.name!!, id = observation.id))
        handle(buildAddIOItemCommand(ioItem, true))
        handle(buildCreateLinkCommand(link))

        val linkList = document.getLinkedObjects(linkId)

        Assert.assertEquals(1, document.links.list.size)
        Assert.assertEquals("Test Comment", document.links.list[0].relationship)
        Assert.assertEquals(3, linkList.size)
        Assert.assertTrue(linkList[0].first is Treatment)
        Assert.assertTrue(linkList[1].first is Observation)
        Assert.assertTrue(linkList[2].first is InputOutput)
    }

    @Test
    fun testAddAndRemoveLink() {
        val linkId: LinkId = DomainId.create()
        val treatId: TreatmentId = DomainId.create()
        val inputOutputId: InputOutputId = DomainId.create()
        val observationId: ObservationId = DomainId.create()

        val idsToLink = listOf(treatId, observationId)
        val link = Link(idsToLink, "Test Comment", id = linkId, documentId = document.id)

        val treat = Treatment("Test Treatment", id = treatId)
        val observation = Observation("Test Observation", id = observationId)
        val ioItem = InputOutput(inputOutputId, Instant.now(), "Test IO", "Route", 1.0, "unit", documentId = document.id)

        handle(buildAddTreatmentCommand(treat))
        handle(buildLogObservationCommand(observation.name!!, id = observation.id))
        handle(buildAddIOItemCommand(ioItem, true))
        handle(buildCreateLinkCommand(link))

        val linkList = document.getLinkedObjects(linkId)

        Assert.assertEquals(1, document.links.list.size)
        Assert.assertEquals("Test Comment", document.links.list[0].relationship)
        Assert.assertEquals(2, linkList.size)
        Assert.assertTrue(linkList[0].first is Treatment)
        Assert.assertTrue(linkList[1].first is Observation)

        handle(buildAddRemoveFromLinkCommand(linkId, listOf(inputOutputId), listOf(observationId)))

        val editedLinkList = document.getLinkedObjects(linkId)

        Assert.assertEquals(1, document.links.list.size)
        Assert.assertEquals("Test Comment", document.links.list[0].relationship)
        Assert.assertEquals(2, editedLinkList.size)
        Assert.assertTrue(editedLinkList[0].first is Treatment)
        Assert.assertTrue(editedLinkList[1].first is InputOutput)
    }

    @Test
    fun testUpdateLinkComment() {
        val linkId: LinkId = DomainId.create()
        val treatId: TreatmentId = DomainId.create()
        val inputOutputId: InputOutputId = DomainId.create()

        val idsToLink = listOf(treatId,  inputOutputId)
        val link = Link(idsToLink, "Test Comment", id = linkId, documentId = document.id)

        val treat = Treatment("Test Treatment", id = treatId)
        val ioItem = InputOutput(inputOutputId, Instant.now(), "Test IO", "Route", 1.0, "unit", documentId = document.id)

        handle(buildAddTreatmentCommand(treat))
        handle(buildAddIOItemCommand(ioItem, true))
        handle(buildCreateLinkCommand(link))

        handle(buildUpdateLinkCommentCommand(linkId, "New Comment"))

        val linkList = document.getLinkedObjects(linkId)

        Assert.assertEquals(1, document.links.list.size)
        Assert.assertEquals("New Comment", document.links.list[0].relationship)
        Assert.assertEquals(2, linkList.size)
        Assert.assertTrue(linkList[0].first is Treatment)
        Assert.assertTrue(linkList[1].first is InputOutput)
    }

    @Test
    fun testRemoveLink() {
        val linkId: LinkId = DomainId.create()
        val treatId: TreatmentId = DomainId.create()
        val inputOutputId: InputOutputId = DomainId.create()

        val idsToLink = listOf(treatId,  inputOutputId)
        val link = Link(idsToLink, "Test Comment", id = linkId, documentId = document.id)

        val treat = Treatment("Test Treatment", id = treatId)
        val ioItem = InputOutput(inputOutputId, Instant.now(), "Test IO", "Route", 1.0, "unit", documentId = document.id)

        handle(buildAddTreatmentCommand(treat))
        handle(buildAddIOItemCommand(ioItem, true))
        handle(buildCreateLinkCommand(link))

        handle(buildDeleteLinkCommand(linkId))

        val linkList = document.getLinkedObjects(linkId)

        Assert.assertEquals(0, document.links.list.size)
        Assert.assertEquals(0, linkList.size)
    }

    @Test
    fun testGetLinkedObjects() {
        val linkId: LinkId = DomainId.create()
        val treatId: TreatmentId = DomainId.create()
        val inputOutputId: InputOutputId = DomainId.create()

        val idsToLink = listOf(treatId,  inputOutputId)
        val link = Link(idsToLink, "Test Comment", id = linkId, documentId = document.id)

        val treat = Treatment("Test Treatment", id = treatId)
        val ioItem = InputOutput(inputOutputId, Instant.now(), "Test IO", "Route",
            1.0, "unit", documentId = document.id)

        handle(buildAddTreatmentCommand(treat))
        handle(buildAddIOItemCommand(ioItem, true))
        handle(buildCreateLinkCommand(link))

        val linkList = document.getLinkedObjects(treatId)
        Assert.assertEquals(2, linkList.size)
        Assert.assertTrue(linkList.any { it.first is Treatment })
        Assert.assertTrue(linkList.any { it.first is InputOutput })
        Assert.assertEquals(document.treatments[treatId], linkList.first { it.first is Treatment }.first)
        Assert.assertEquals(document.inputOutputs[inputOutputId], linkList.first { it.first is InputOutput }.first)
    }

    @Test
    fun testGetLinkableObjects_oneLinkObjectExists() {
        val orderLine1 = OrderLine(DomainId.create(), Instant.now(), "title",
            "instructions", OrderType.CUSTOM.dataString, OrderStatus.ORDERED.dataString, Interval(1,1))
        val orderLine2 = OrderLine(DomainId.create(), Instant.now(), "title2",
            "instructions2", OrderType.CUSTOM.dataString, OrderStatus.ORDERED.dataString, Interval(1,1))
        val orderLine3 = OrderLine(DomainId.create(), Instant.now(), "title",
            "instructions", OrderType.CUSTOM.dataString, OrderStatus.ORDERED.dataString, Interval(1,1))
        val orderLine4 = OrderLine(DomainId.create(), Instant.now(), "title",
            "instructions", OrderType.CUSTOM.dataString, OrderStatus.ORDERED.dataString, Interval(1,1))

        document.orders += orderLine1
        document.orders += orderLine2
        document.orders += orderLine3
        document.orders += orderLine4
        val link1 = Link(listOf(orderLine1.id, orderLine2.id, orderLine4.id), "Test Comment",
            id = DomainId.create(), documentId = document.id)
        val link2 = Link(listOf(orderLine1.id, orderLine2.id, orderLine3.id), "Test Comment",
            id = DomainId.create(), documentId = document.id)

        document.links += link1
        document.links += link2

        val linkList = document.getLinkableObjects(orderLine3.id)
        Assert.assertEquals(3, linkList.size)
        Assert.assertTrue(linkList.contains(orderLine1))
        Assert.assertTrue(linkList.contains(orderLine2))
        Assert.assertTrue(linkList.contains(orderLine3))
    }
    @Test
    fun testGetLinkableObjects_multipleLinkObjectExists() {
        //arrange
        val orderLine1 = OrderLine(DomainId.create(), Instant.now(), "title",
            "instructions", OrderType.CUSTOM.dataString, OrderStatus.ORDERED.dataString, Interval(1,1))
        val orderLine2 = OrderLine(DomainId.create(), Instant.now(), "title2",
            "instructions2", OrderType.CUSTOM.dataString, OrderStatus.ORDERED.dataString, Interval(1,1))
        val orderLine3 = OrderLine(DomainId.create(), Instant.now(), "title",
            "instructions", OrderType.CUSTOM.dataString, OrderStatus.ORDERED.dataString, Interval(1,1))

        document.orders += orderLine1
        document.orders += orderLine2
        document.orders += orderLine3
        val link1 = Link(listOf(orderLine1.id, orderLine2.id), "Test Comment",
            id = DomainId.create(), documentId = document.id)
        val link2 = Link(listOf(orderLine1.id, orderLine2.id, orderLine3.id), "Test Comment",
            id = DomainId.create(), documentId = document.id)
        val link3 = Link(listOf(orderLine1.id, orderLine3.id), "Test Comment",
            id = DomainId.create(), documentId = document.id)
        document.links += link1
        document.links += link2
        document.links += link3
        //act
        val linkList = document.getLinkableObjects(orderLine1.id)
        //assert
        Assert.assertEquals(3, linkList.size)
        Assert.assertTrue(linkList.contains(orderLine1))
        Assert.assertTrue(linkList.contains(orderLine2))
        Assert.assertTrue(linkList.contains(orderLine3))
    }

    @Test
    fun testGetLinkableObjects_NoLinksForObject() {
        //arrange
        val orderLine1 = OrderLine(DomainId.create(), Instant.now(), "title",
            "instructions", OrderType.CUSTOM.dataString, OrderStatus.ORDERED.dataString, Interval(1,1))
        val orderLine2 = OrderLine(DomainId.create(), Instant.now(), "title2",
            "instructions2", OrderType.CUSTOM.dataString, OrderStatus.ORDERED.dataString, Interval(1,1))
        val orderLine3 = OrderLine(DomainId.create(), Instant.now(), "title",
            "instructions", OrderType.CUSTOM.dataString, OrderStatus.ORDERED.dataString, Interval(1,1))
        val orderLine4 = OrderLine(DomainId.create(), Instant.now(), "title",
            "instructions", OrderType.CUSTOM.dataString, OrderStatus.ORDERED.dataString, Interval(1,1))

        document.orders += orderLine1
        document.orders += orderLine2
        document.orders += orderLine3
        document.orders += orderLine4
        val link1 = Link(listOf(orderLine1.id, orderLine2.id), "Test Comment",
            id = DomainId.create(), documentId = document.id)
        val link2 = Link(listOf(orderLine1.id, orderLine2.id, orderLine3.id), "Test Comment",
            id = DomainId.create(), documentId = document.id)
        val link3 = Link(listOf(orderLine1.id, orderLine3.id), "Test Comment",
            id = DomainId.create(), documentId = document.id)
        document.links += link1
        document.links += link2
        document.links += link3
        //act
        val linkList = document.getLinkableObjects(orderLine4.id)
        //assert
        Assert.assertEquals(0, linkList.size)
    }


    @Test
    fun testGetLinkableObjects_RequestObjectNotLinkable() {
        //arrange
        val orderLine1 = OrderLine(DomainId.create(), Instant.now(), "title",
            "instructions", OrderType.CUSTOM.dataString, OrderStatus.ORDERED.dataString, Interval(1,1))
        val orderLine2 = OrderLine(DomainId.create(), Instant.now(), "title2",
            "instructions2", OrderType.CUSTOM.dataString, OrderStatus.ORDERED.dataString, Interval(1,1))
        val orderLine3 = OrderLine(DomainId.create(), Instant.now(), "title",
            "instructions", OrderType.CUSTOM.dataString, OrderStatus.ORDERED.dataString, Interval(1,1))

        //note: at the time of writing this test, [Event] was not [Linkable], with no plans to make it so, therefore we
        //hope this test remains valid unless that changes.
        val event = Event("event", DomainId.create(), Instant.now())
        val event2 = Event("event", DomainId.create(), Instant.now())
        document.orders += listOf(orderLine1, orderLine2, orderLine3)
        document.events += listOf(event, event2)

        val link1 = Link(listOf(orderLine1.id, orderLine2.id),
            "Test Comment", id = DomainId.create(), documentId = document.id)
        val link2 = Link(listOf(orderLine1.id, orderLine2.id, orderLine3.id),
            "Test Comment", id = DomainId.create(), documentId = document.id)
        val link3 = Link(listOf(orderLine1.id, orderLine3.id), "Test Comment",
            id = DomainId.create(), documentId = document.id)
        val link4 = Link(listOf(orderLine1.id, event.id))
        val link5 = Link(listOf(event2.id, event.id))
        document.links += listOf(link1, link2, link3, link4, link5)

        //act
        val linkList = document.getLinkableObjects(event.id)
        //assert
        Assert.assertEquals(3, linkList.size)
        Assert.assertTrue(linkList.contains(orderLine1))
    }

    @Test
    fun testGetLinkableObjects_NoLinksExist() {
        //arrange
        val orderLine4 = OrderLine(DomainId.create(), Instant.now(), "title",
            "instructions", OrderType.CUSTOM.dataString, OrderStatus.ORDERED.dataString, Interval(1,1))
        document.orders += orderLine4

        //act
        val linkList = document.getLinkableObjects(orderLine4.id)
        //assert
        Assert.assertEquals(0, linkList.size)
    }

    @Test
    fun testHasHandlers(){
        Assert.assertTrue(document.handlers.hasHandlerFor<RemoveObservation>())
    }

    @Test
    fun testTreatmentsCanHaveAssociatedNotes(){
        // Create the treatment with a note
        val id = DomainId.create<TreatmentId>()
        val treatment = Treatment(CommonTreatments.LINE.dataString, LineData(), id)
        val note = Note(message = "Test Message", callsign = "")
        val lineCommand = buildAddTreatmentCommand(treatment)
        val noteCommand = buildAddNoteCommand(note)
        val linkCommand = buildLinkFromLinkables(CommonLinkRelationships.NOTE, treatment, note)
        handle(linkCommand)
        handle(lineCommand)
        handle(noteCommand)

        // Read the notes
        val readNote = document.treatments[CommonTreatments.LINE].first().getAssociatedNotes(document).first()
        Assert.assertEquals("Test Message", readNote.message)

        // Create the treatment with a note
        handle(buildUpdateTreatmentCommand(treatment.copy(treatmentData = LineData(type = "Type"))))
        handle(buildUpdateNoteCommand(note.copy(message = "New Message")))

        // Read the notes
        val readTreatment = document.treatments[CommonTreatments.LINE].first()
        val newNote = readTreatment.getAssociatedNotes(document).first()
        Assert.assertEquals("New Message", newNote.message)
        Assert.assertEquals("Type", readTreatment.getData<LineData>()?.type)
    }
}