package gov.afrl.batdok.encounter.medicines

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.commands.buildLogMedWithIdCommand
import gov.afrl.batdok.encounter.commands.buildLogMedicineCommand
import gov.afrl.batdok.encounter.commands.buildRemoveMedTreatmentCommand
import gov.afrl.batdok.encounter.commands.buildUpdateMedicineCommand
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.encounter.ids.MedicineId
import gov.afrl.batdok.encounter.medicine.Medicine
import gov.afrl.batdok.encounter.medicine.Medicines
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant

class MedicineStateTest {
    val medicines = Medicines()
    val docId = DomainId.create<DocumentId>()
    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now(), commandId : CommandId = DomainId.create()) {
        medicines.handlers.handle(docId, buildCommandData(subCommand,callsign,timestamp, commandId))
    }
    @Test
    fun testLogMedicineCommand(){
        val id = DomainId.create<MedicineId>()

        val medicine = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            Instant.ofEpochSecond(12331),
            id,
            "testroute",
            1.2f,
            "cm",
            "45646",
            "11111",
            "tsttype",
            docId
        )
        Assert.assertEquals(0, medicines.list.size)
        handle(buildLogMedicineCommand(medicine), timestamp = medicine.administrationTime!!, commandId = id.copy())

        Assert.assertEquals(1, medicines.list.size)
        Assert.assertEquals(id, medicines.list[0].id)
        Assert.assertEquals(medicine.name, medicines.list[0].name)
        Assert.assertEquals(medicine.ndc, medicines.list[0].ndc)
        Assert.assertEquals(medicine.rxcui, medicines.list[0].rxcui)
        Assert.assertEquals(medicine.administrationTime, medicines.list[0].administrationTime)
        Assert.assertEquals(medicine.route, medicines.list[0].route)
        Assert.assertEquals(medicine.volume, medicines.list[0].volume)
        Assert.assertEquals(medicine.unit, medicines.list[0].unit)
        Assert.assertEquals(medicine.serialNumber, medicines.list[0].serialNumber)
        Assert.assertEquals(medicine.expirationDate, medicines.list[0].expirationDate)
        Assert.assertEquals(medicine.category, medicines.list[0].category)
        Assert.assertEquals(medicine.documentId, medicines.list[0].documentId)
    }

    @Test
    fun testLogMedWithIdCommand(){
        val id = DomainId.create<MedicineId>()

        val medicine = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            Instant.ofEpochSecond(12331),
            id,
            "testroute",
            1.2f,
            "cm",
            "45646",
            "11111",
            "tsttype",
            docId
        )
        Assert.assertEquals(0, medicines.list.size)
        handle(buildLogMedWithIdCommand(medicine))

        Assert.assertEquals(1, medicines.list.size)
        Assert.assertEquals(id, medicines.list[0].id)
        Assert.assertEquals(medicine.name, medicines.list[0].name)
        Assert.assertEquals(medicine.ndc, medicines.list[0].ndc)
        Assert.assertEquals(medicine.rxcui, medicines.list[0].rxcui)
        Assert.assertEquals(medicine.administrationTime, medicines.list[0].administrationTime)
        Assert.assertEquals(medicine.route, medicines.list[0].route)
        Assert.assertEquals(medicine.volume, medicines.list[0].volume)
        Assert.assertEquals(medicine.unit, medicines.list[0].unit)
        Assert.assertEquals(medicine.serialNumber, medicines.list[0].serialNumber)
        Assert.assertEquals(medicine.expirationDate, medicines.list[0].expirationDate)
        Assert.assertEquals(medicine.category, medicines.list[0].category)
        Assert.assertEquals(medicine.documentId, medicines.list[0].documentId)
    }

    @Test
    fun testUpdateMedicineCommand(){
        val id = DomainId.create<MedicineId>()

        Assert.assertEquals(0, medicines.list.size)
        val medicine = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            Instant.ofEpochSecond(12331),
            id,
            "testroute",
            1.2.toFloat(),
            "cm",
            "45646",
            "11111",
            "tsttype",
            docId
        )
        medicines += Medicine(
            "oldMed",
            "oldNdc",
            "oldRxcui",
            Instant.ofEpochSecond(12222),
            id,
            "oldroute",
            1.1f,
            "oldUnit",
            "oldSerial",
            "oldExpiration",
            "oldtype",
            docId
        )

        Assert.assertEquals(1, medicines.list.size)
        handle(buildUpdateMedicineCommand(medicine))
        Assert.assertEquals(1, medicines.list.size)
        Assert.assertEquals(id, medicines.list[0].id)
        Assert.assertEquals(medicine.name, medicines.list[0].name)
        Assert.assertEquals(medicine.ndc, medicines.list[0].ndc)
        Assert.assertEquals(medicine.rxcui, medicines.list[0].rxcui)
        Assert.assertEquals(medicine.administrationTime, medicines.list[0].administrationTime)
        Assert.assertEquals(medicine.route, medicines.list[0].route)
        Assert.assertEquals(medicine.volume, medicines.list[0].volume)
        Assert.assertEquals(medicine.unit, medicines.list[0].unit)
        Assert.assertEquals(medicine.serialNumber, medicines.list[0].serialNumber)
        Assert.assertEquals(medicine.expirationDate, medicines.list[0].expirationDate)
        Assert.assertEquals(medicine.category, medicines.list[0].category)
        Assert.assertEquals(medicine.documentId, medicines.list[0].documentId)
    }

    @Test
    fun testRemoveMedicineCommand(){
        val id = DomainId.create<MedicineId>()
        Assert.assertEquals(0, medicines.list.size)
        val medicine = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            Instant.ofEpochSecond(12331),
            id,
            "testroute",
            1.2f,
            "cm",
            "45646",
            "11111",
            "tsttype"
        )
        medicines += medicine
        Assert.assertEquals(1, medicines.list.size)
        handle(buildRemoveMedTreatmentCommand(id, false))
        Assert.assertEquals(0, medicines.list.size)
        Assert.assertEquals(1, medicines.onProperRemovedItems.size)
    }
}