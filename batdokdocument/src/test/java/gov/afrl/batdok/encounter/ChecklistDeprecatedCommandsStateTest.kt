package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.commands.buildChecklistCommand
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

@Deprecated(
    message = "Can delete this once Checklist, ChecklistCommands deprecations are removed.  ChecklistnewCommandsStateTest uses the nondeprecated commands"
)
class ChecklistDeprecatedCommandsStateTest {

    val checklistItems = Checklist()

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS)) {
        checklistItems.handlers.handle(
            DomainId.nil(),
            buildCommandData(subCommand, callsign, timestamp)
        )
    }

    @Test
    fun testChecklistItem_known_checked(){
        Assert.assertEquals(0, checklistItems.size)
        handle(buildChecklistCommand(KnownChecklistItems.CHECK_DRESSINGS, true, KnownChecklistItems.CHECK_DRESSINGS.defaultInterval))
        Assert.assertEquals(1, checklistItems.size)
        with(checklistItems[0]){
            Assert.assertEquals(KnownChecklistItems.CHECK_DRESSINGS.dataString, message)
            Assert.assertEquals(KnownChecklistItems.CHECK_DRESSINGS.defaultInterval, interval)
            Assert.assertTrue(checked)
        }
    }

    @Test
    fun testChecklistItem_known_uncheck(){
        Assert.assertEquals(0, checklistItems.size)
        handle(buildChecklistCommand(KnownChecklistItems.CHECK_DRESSINGS, false, KnownChecklistItems.CHECK_DRESSINGS.defaultInterval))
        Assert.assertEquals(1, checklistItems.size)
        with(checklistItems[0]){
            Assert.assertEquals(KnownChecklistItems.CHECK_DRESSINGS.dataString, message)
            Assert.assertEquals(KnownChecklistItems.CHECK_DRESSINGS.defaultInterval, interval)
            Assert.assertFalse(checked)
        }
    }

    @Test
    fun testChecklistItem_known_uncheck_existing(){
        checklistItems += ChecklistItem(KnownChecklistItems.CHECK_DRESSINGS)
        Assert.assertEquals(1, checklistItems.size)
        handle(buildChecklistCommand(KnownChecklistItems.CHECK_DRESSINGS, false, KnownChecklistItems.CHECK_DRESSINGS.defaultInterval))
        Assert.assertEquals(1, checklistItems.size)
        with(checklistItems[0]){
            Assert.assertEquals(KnownChecklistItems.CHECK_DRESSINGS.dataString, message)
            Assert.assertEquals(KnownChecklistItems.CHECK_DRESSINGS.defaultInterval, interval)
            Assert.assertFalse(checked)
        }
    }

    @Test
    fun testChecklistItem_known_newInterval(){
        Assert.assertEquals(0, checklistItems.size)
        handle(buildChecklistCommand(KnownChecklistItems.CHECK_DRESSINGS, false, interval = "Custom"))
        Assert.assertEquals(1, checklistItems.size)
        with(checklistItems[0]){
            Assert.assertEquals(KnownChecklistItems.CHECK_DRESSINGS.dataString, message)
            Assert.assertEquals("Custom", interval)
            Assert.assertFalse(checked)
        }
    }

    @Test
    fun testChecklistItem_known_newInterval_existing(){
        checklistItems += ChecklistItem(KnownChecklistItems.CHECK_DRESSINGS)
        Assert.assertEquals(1, checklistItems.size)
        handle(buildChecklistCommand(KnownChecklistItems.CHECK_DRESSINGS, null, "Custom"))
        Assert.assertEquals(1, checklistItems.size)
        with(checklistItems[0]){
            Assert.assertEquals(KnownChecklistItems.CHECK_DRESSINGS.dataString, message)
            Assert.assertEquals("Custom", interval)
            Assert.assertTrue(checked)
        }
    }


    @Test
    fun testChecklistItem_custom_checked(){
        Assert.assertEquals(0, checklistItems.size)
        handle(buildChecklistCommand("Custom Item", true, "Once"))
        Assert.assertEquals(1, checklistItems.size)
        with(checklistItems[0]){
            Assert.assertEquals("Custom Item", message)
            Assert.assertEquals("Once", interval)
            Assert.assertTrue(checked)
        }
    }

    @Test
    fun testChecklistItem_custom_uncheck(){
        Assert.assertEquals(0, checklistItems.size)
        handle(buildChecklistCommand("Custom Item", false, "Once"))
        Assert.assertEquals(1, checklistItems.size)
        with(checklistItems[0]){
            Assert.assertEquals("Custom Item", message)
            Assert.assertEquals("Once", interval)
            Assert.assertFalse(checked)
        }
    }

    @Test
    fun testChecklistItem_custom_uncheck_existing(){
        checklistItems += ChecklistItem("Custom Item", "Once")
        Assert.assertEquals(1, checklistItems.size)
        handle(buildChecklistCommand("Custom Item", false, "Once"))
        Assert.assertEquals(1, checklistItems.size)
        with(checklistItems[0]){
            Assert.assertEquals("Custom Item", message)
            Assert.assertEquals("Once", interval)
            Assert.assertFalse(checked)
        }
    }

    @Test
    fun testChecklistItem_custom_newInterval(){
        Assert.assertEquals(0, checklistItems.size)
        handle(buildChecklistCommand("Custom Item", null, interval = "Custom"))
        Assert.assertEquals(1, checklistItems.size)
        with(checklistItems[0]){
            Assert.assertEquals("Custom Item", message)
            Assert.assertEquals("Custom", interval)
            Assert.assertFalse(checked)
        }
    }

    @Test
    fun testChecklistItem_custom_newInterval_existing(){
        checklistItems += ChecklistItem("Custom Item", "Once")
        Assert.assertEquals(1, checklistItems.size)
        handle(buildChecklistCommand("Custom Item", null, "Custom"))
        Assert.assertEquals(1, checklistItems.size)
        with(checklistItems[0]){
            Assert.assertEquals("Custom Item", message)
            Assert.assertEquals("Custom", interval)
            Assert.assertTrue(checked)
        }
    }
}