package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.commands.buildLogBloodCommand
import gov.afrl.batdok.encounter.commands.buildRemoveBloodCommand
import gov.afrl.batdok.encounter.commands.buildUpdateBloodCommand
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.encounter.ids.TreatmentId
import gov.afrl.batdok.encounter.observation.Blood
import gov.afrl.batdok.encounter.observation.BloodList
import gov.afrl.batdok.util.buildCommandData
import gov.afrl.batdok.util.buildDocumentCommand
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant

class BloodCommandsStateTest {
    val docId = DomainId.create<DocumentId>()
    val bloodList = BloodList()
    val commandTime = Instant.ofEpochSecond(1L)
    val bloodStartedTime = Instant.ofEpochSecond(100L)
    val newBloodStartedTime = Instant.ofEpochSecond(200L)

    private fun handle(subCommand: Message, callsign: String = "CS",
                       timestamp: Instant = commandTime,
                       commandId: CommandId = DomainId.create()) {
        bloodList.handlers.handle(docId, buildCommandData(subCommand,callsign,timestamp, commandId))
    }

    @Test
    fun testLogBloodCommand(){
        val id = DomainId.create<TreatmentId>()
        val blood = Blood(
            bloodId = id,
            administrationTime = bloodStartedTime, // This time is ignored when logging
            bloodProduct = "newblood",
            bloodType = "tsttype",
            donationIdNumber = "12345",
            expirationDate = "1111",
            bloodAge = 1,
            volume = 22L,
            unit = "mL",
            route = "IV",
            docId = docId
        )
        Assert.assertEquals(0, bloodList.list.size)
        handle(buildLogBloodCommand(blood), commandId = id.copy())
        Assert.assertEquals(1, bloodList.list.size)
        Assert.assertEquals(id, bloodList.list[0].id)
        Assert.assertEquals(docId, bloodList.list[0].docId)
        Assert.assertEquals(commandTime, bloodList.list[0].administrationTime)
        Assert.assertEquals("newblood", bloodList.list[0].bloodProduct)
        Assert.assertEquals("tsttype", bloodList.list[0].bloodType)
        Assert.assertEquals("12345", bloodList.list[0].donationIdNumber)
        Assert.assertEquals("1111", bloodList.list[0].expirationDate)
        Assert.assertEquals(1, bloodList.list[0].bloodAge)
        Assert.assertEquals(22L, bloodList.list[0].volume)
        Assert.assertEquals("mL", bloodList.list[0].unit)
        Assert.assertEquals("IV", bloodList.list[0].route)
    }

    @Test
    fun testUpdateBloodCommand(){
        val id = DomainId.create<TreatmentId>()
        val blood = Blood(
            bloodId = id,
            administrationTime = newBloodStartedTime,
            bloodProduct = Blood.BloodProduct.FRESH_WHOLE_BLOOD.dataString,
            bloodType = "tsttype",
            donationIdNumber = "12345",
            expirationDate = "1111",
            bloodAge = 1,
            volume = 22L,
            unit = "mL",
            route = null,
            docId = docId
        )
        bloodList += Blood(
            bloodId = id,
            administrationTime = bloodStartedTime,
            bloodProduct = "oldblood",
            bloodType = "oldtype",
            donationIdNumber = "12",
            expirationDate = "0000",
            bloodAge = 0,
            volume = 0,
            route = "IV",
            unit = "L"
        )
        Assert.assertEquals(1, bloodList.list.size)
        handle(buildUpdateBloodCommand(blood))
        Assert.assertEquals(1, bloodList.list.size)
        Assert.assertEquals(id, bloodList.list[0].id)
        Assert.assertEquals(docId, bloodList.list[0].docId)
        Assert.assertEquals(newBloodStartedTime, bloodList.list[0].administrationTime)
        Assert.assertEquals(Blood.BloodProduct.FRESH_WHOLE_BLOOD.dataString, bloodList.list[0].bloodProduct)
        Assert.assertEquals("tsttype", bloodList.list[0].bloodType)
        Assert.assertEquals("12345", bloodList.list[0].donationIdNumber)
        Assert.assertEquals("1111", bloodList.list[0].expirationDate)
        Assert.assertEquals(1, bloodList.list[0].bloodAge)
        Assert.assertEquals(22L, bloodList.list[0].volume)
        Assert.assertEquals("mL", bloodList.list[0].unit)
        Assert.assertNull(bloodList.list[0].route)
    }

    @Test
    fun testRemoveBloodCommand(){
        val id = DomainId.create<TreatmentId>()
        val blood = Blood(
            bloodId = id,
            bloodProduct = "newblood",
            bloodType = "tsttype",
            donationIdNumber = "12345",
            expirationDate = "1111",
            bloodAge = 1,
            docId = docId
        )
        bloodList += blood
        Assert.assertEquals(1, bloodList.list.size)
        handle(buildRemoveBloodCommand(id, false))
        Assert.assertEquals(0, bloodList.list.size)
        Assert.assertEquals(1, bloodList.onProperRemovedItems.size)
    }

    @Test
    fun testAddWithId(){
        val id = DomainId.create<DomainId>()
        val doc = Document()

        doc.handle(
            listOf(
                buildDocumentCommand(
                    DomainId.create(),
                    buildCommandData(buildLogBloodCommand(Blood(bloodId = id)))
                ),
            )
        )
        Assert.assertEquals(id, doc.bloodList.list.first().bloodId)
    }
}