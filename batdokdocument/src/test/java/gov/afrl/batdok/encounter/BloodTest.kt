package gov.afrl.batdok.encounter

import gov.afrl.batdok.encounter.observation.Blood
import org.junit.Assert
import org.junit.Test

class BloodTest {
    @Test
    fun testGetVolumeString_Both() {
        val blood = Blood(volume = 1, unit = "unit")

        val result = blood.getVolumeString()

        Assert.assertEquals("1 unit", result)
    }
    @Test
    fun testGetVolumeString_JustVolume() {
        val blood = Blood(volume = 1)

        val result = blood.getVolumeString()

        Assert.assertEquals("1", result)
    }
    @Test
    fun testGetVolumeString_JustUnit() {
        val blood = Blood(unit = "unit")

        val result = blood.getVolumeString()

        Assert.assertEquals("unit", result)
    }
}