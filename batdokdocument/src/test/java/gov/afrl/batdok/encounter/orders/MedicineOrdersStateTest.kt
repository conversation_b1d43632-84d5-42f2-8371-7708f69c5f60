package gov.afrl.batdok.encounter.orders

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.Contact
import gov.afrl.batdok.encounter.Interval
import gov.afrl.batdok.encounter.commands.orders.*
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.encounter.ids.OrderLineId
import gov.afrl.batdok.encounter.metadata.Signature
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class MedicineOrdersStateTest {

    val documentId = DomainId.create<DocumentId>()
    val orders = Orders()
    val callsign = "CS"

    private fun handle(
        subCommand: Message,
        callsign: String = this.callsign,
        timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS),
        id: CommandId = DomainId.create()
    ) {
        orders.handlers.handle(documentId, buildCommandData(subCommand, callsign, timestamp, id))
    }

    private fun createMedicineOrderLine() = MedicineOrderLine(
        id = DomainId.create(),
        timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS),
        instructions = "Take your meds",
        orderType = OrderType.MEDICATION.dataString,
        orderStatus = OrderStatus.IN_PROGRESS.dataString,
        frequency = Interval(1, 30),
        lastOccurrence = Instant.now().minusSeconds(3600).truncatedTo(ChronoUnit.SECONDS),
        provider = Contact("Dr Phil", "**********", "<EMAIL>"),
        signature = Signature("Dr Phil", Instant.now().truncatedTo(ChronoUnit.SECONDS), ByteArray(100)),
        orderedMedicine = OrderedMedicine(
            name = "Ibuprofen",
            ndc = "NDC",
            rxcui = "RXCUI",
            medId = DomainId.create("11111111"),
            route = "IO",
            volume = 200f,
            unit = "mg",
            serialNumber = "frootloops",
            expirationDate = "someday",
            type = "medicine",
            exportName = "Ibuprofen"
        )
    )

    private fun createUpdatedMedicine() = OrderedMedicine(
        name = "Acetaminophen",
        ndc = "NDC2",
        rxcui = "RXCUI2",
        medId = DomainId.create("12345678"),
        route = "IV",
        volume = 50f,
        unit = "cc",
        serialNumber = "frostedflakes",
        expirationDate = "whenever",
        type = "meds",
        exportName = "Acetaminophen"
    )

    @Test
    fun testAddMedicineOrderLine(){
        val orderLineToAdd = createMedicineOrderLine()

        handle(buildAddMedicineOrderLineCommand(orderLineToAdd))

        Assert.assertEquals(1, orders.list.size)
        val addedOrderLine = orders.list[0] as MedicineOrderLine
        Assert.assertEquals(orderLineToAdd.id, addedOrderLine.id)
        Assert.assertEquals(orderLineToAdd.timestamp, addedOrderLine.timestamp)
        Assert.assertEquals(orderLineToAdd.title, addedOrderLine.title)
        Assert.assertEquals(orderLineToAdd.instructions, addedOrderLine.instructions)
        Assert.assertEquals(orderLineToAdd.orderType, addedOrderLine.orderType)
        Assert.assertEquals(orderLineToAdd.orderStatus, addedOrderLine.orderStatus)
        Assert.assertEquals(orderLineToAdd.frequency, addedOrderLine.frequency)
        Assert.assertEquals(orderLineToAdd.lastOccurrence, addedOrderLine.lastOccurrence)
        Assert.assertEquals(orderLineToAdd.provider?.name, addedOrderLine.provider?.name)
        Assert.assertEquals(orderLineToAdd.provider?.email, addedOrderLine.provider?.email)
        Assert.assertEquals(orderLineToAdd.provider?.phone, addedOrderLine.provider?.phone)
        Assert.assertEquals(orderLineToAdd.signature?.name, addedOrderLine.signature?.name)
        Assert.assertEquals(orderLineToAdd.signature?.timeStamp, addedOrderLine.signature?.timeStamp)
        Assert.assertArrayEquals(orderLineToAdd.signature?.signature, addedOrderLine.signature?.signature)
        Assert.assertEquals(orderLineToAdd.orderedMedicine.name, addedOrderLine.orderedMedicine.name)
        Assert.assertEquals(orderLineToAdd.orderedMedicine.ndc, addedOrderLine.orderedMedicine.ndc)
        Assert.assertEquals(orderLineToAdd.orderedMedicine.medId, addedOrderLine.orderedMedicine.medId)
        Assert.assertEquals(orderLineToAdd.orderedMedicine.route, addedOrderLine.orderedMedicine.route)
        Assert.assertEquals(orderLineToAdd.orderedMedicine.volume, addedOrderLine.orderedMedicine.volume)
        Assert.assertEquals(orderLineToAdd.orderedMedicine.unit, addedOrderLine.orderedMedicine.unit)
        Assert.assertEquals(orderLineToAdd.orderedMedicine.serialNumber, addedOrderLine.orderedMedicine.serialNumber)
        Assert.assertEquals(orderLineToAdd.orderedMedicine.expirationDate, addedOrderLine.orderedMedicine.expirationDate)
        Assert.assertEquals(orderLineToAdd.orderedMedicine.expirationDate, addedOrderLine.orderedMedicine.expirationDate)
        Assert.assertEquals(orderLineToAdd.orderedMedicine.exportName, addedOrderLine.orderedMedicine.exportName)
    }

    @Test
    fun testUpdateMedicineOrderLine(){
        val orderLineToUpdate = createMedicineOrderLine()
        orders += orderLineToUpdate

        val newTimestamp = Instant.now().plusSeconds(3600).truncatedTo(ChronoUnit.SECONDS)
        val newInstructions = "Takest thine potions"
        val newFrequency = Interval(3, 0)
        val newLastOccurrence = Instant.now().plusSeconds(1800).truncatedTo(ChronoUnit.SECONDS)
        val newProvider = Contact("Dr Laura", "**********", "<EMAIL>")
        val newSignature = Signature("Dr Laura", Instant.now().plusSeconds(3600).truncatedTo(ChronoUnit.SECONDS), ByteArray(128))
        val newMedicine = createUpdatedMedicine()

        Assert.assertEquals(1, orders.list.size)

        handle(
            buildUpdateMedicineOrderLineCommand(
            id = orderLineToUpdate.id,
            timestamp = newTimestamp,
            instructions = newInstructions,
            frequency = newFrequency,
            lastOccurrence = newLastOccurrence,
            provider = newProvider,
            signature = newSignature,
            medicine = newMedicine
        ), callsign = "NewWorldOrder")

        Assert.assertEquals(1, orders.list.size)
        val updatedOrderLine = orders.list[0] as MedicineOrderLine
        Assert.assertEquals(orderLineToUpdate.id, updatedOrderLine.id)
        Assert.assertEquals(newTimestamp, updatedOrderLine.timestamp)
        // title will change on its own according to the medicine, there should be a separate test for that
        Assert.assertEquals(newInstructions, updatedOrderLine.instructions)
        Assert.assertEquals(orderLineToUpdate.orderType, updatedOrderLine.orderType)
        Assert.assertEquals(orderLineToUpdate.orderStatus, updatedOrderLine.orderStatus)
        Assert.assertEquals(newFrequency, updatedOrderLine.frequency)
        Assert.assertEquals(newLastOccurrence, updatedOrderLine.lastOccurrence)
        Assert.assertEquals(newProvider.name, updatedOrderLine.provider?.name)
        Assert.assertEquals(newProvider.email, updatedOrderLine.provider?.email)
        Assert.assertEquals(newProvider.phone, updatedOrderLine.provider?.phone)
        Assert.assertEquals(newSignature.name, updatedOrderLine.signature?.name)
        Assert.assertEquals(newSignature.timeStamp, updatedOrderLine.signature?.timeStamp)
        Assert.assertArrayEquals(newSignature.signature, updatedOrderLine.signature?.signature)
        Assert.assertEquals(newMedicine.name, updatedOrderLine.orderedMedicine.name)
        Assert.assertEquals(newMedicine.ndc, updatedOrderLine.orderedMedicine.ndc)
        Assert.assertEquals(newMedicine.medId, updatedOrderLine.orderedMedicine.medId)
        Assert.assertEquals(newMedicine.route, updatedOrderLine.orderedMedicine.route)
        Assert.assertEquals(newMedicine.volume, updatedOrderLine.orderedMedicine.volume)
        Assert.assertEquals(newMedicine.unit, updatedOrderLine.orderedMedicine.unit)
        Assert.assertEquals(newMedicine.serialNumber, updatedOrderLine.orderedMedicine.serialNumber)
        Assert.assertEquals(newMedicine.expirationDate, updatedOrderLine.orderedMedicine.expirationDate)
        Assert.assertEquals(newMedicine.expirationDate, updatedOrderLine.orderedMedicine.expirationDate)
        Assert.assertEquals(newMedicine.exportName, updatedOrderLine.orderedMedicine.exportName)
    }

    @Test
    fun testUpdateMedicineOrderLineStatus(){
        val orderLineToUpdate = createMedicineOrderLine()
        orders += orderLineToUpdate

        val newStatus = OrderStatus.COMPLETE

        Assert.assertEquals(1, orders.list.size)

        handle(
            buildUpdateMedicineOrderLineStatusCommand(
            id = orderLineToUpdate.id,
            orderStatus = newStatus
        ), callsign = "NewWorldOrder")

        Assert.assertEquals(1, orders.list.size)
        val updatedOrderLine = orders.list[0] as MedicineOrderLine
        Assert.assertEquals(orderLineToUpdate.id, updatedOrderLine.id)
        Assert.assertEquals(orderLineToUpdate.timestamp, updatedOrderLine.timestamp)
        Assert.assertEquals(orderLineToUpdate.title, updatedOrderLine.title)
        Assert.assertEquals(orderLineToUpdate.instructions, updatedOrderLine.instructions)
        Assert.assertEquals(orderLineToUpdate.orderType, updatedOrderLine.orderType)
        Assert.assertEquals(newStatus.dataString, updatedOrderLine.orderStatus)
        Assert.assertEquals(orderLineToUpdate.frequency, updatedOrderLine.frequency)
        Assert.assertEquals(orderLineToUpdate.lastOccurrence, updatedOrderLine.lastOccurrence)
        Assert.assertEquals(orderLineToUpdate.provider, updatedOrderLine.provider)
        Assert.assertEquals(orderLineToUpdate.signature?.name, updatedOrderLine.signature?.name)
        Assert.assertEquals(orderLineToUpdate.signature?.timeStamp, updatedOrderLine.signature?.timeStamp)
        Assert.assertArrayEquals(orderLineToUpdate.signature?.signature, updatedOrderLine.signature?.signature)
        Assert.assertEquals(orderLineToUpdate.orderedMedicine.name, updatedOrderLine.orderedMedicine.name)
        Assert.assertEquals(orderLineToUpdate.orderedMedicine.ndc, updatedOrderLine.orderedMedicine.ndc)
        Assert.assertEquals(orderLineToUpdate.orderedMedicine.medId, updatedOrderLine.orderedMedicine.medId)
        Assert.assertEquals(orderLineToUpdate.orderedMedicine.route, updatedOrderLine.orderedMedicine.route)
        Assert.assertEquals(orderLineToUpdate.orderedMedicine.volume, updatedOrderLine.orderedMedicine.volume)
        Assert.assertEquals(orderLineToUpdate.orderedMedicine.unit, updatedOrderLine.orderedMedicine.unit)
        Assert.assertEquals(orderLineToUpdate.orderedMedicine.serialNumber, updatedOrderLine.orderedMedicine.serialNumber)
        Assert.assertEquals(orderLineToUpdate.orderedMedicine.expirationDate, updatedOrderLine.orderedMedicine.expirationDate)
        Assert.assertEquals(orderLineToUpdate.orderedMedicine.expirationDate, updatedOrderLine.orderedMedicine.expirationDate)
        Assert.assertEquals(orderLineToUpdate.orderedMedicine.exportName, updatedOrderLine.orderedMedicine.exportName)
    }

    @Test
    fun testNothingHappensIfDoesntExistOnUpdate(){
        val orderLineToNotUpdate = createMedicineOrderLine()
        orders += orderLineToNotUpdate

        val newTimestamp = Instant.now().plusSeconds(3600)
        val newTitle = "A different order line"
        val newInstructions = "Takest thine potions"
        val newFrequency = Interval(3, 0)
        val newLastOccurrence = Instant.now().plusSeconds(1800)
        val newProvider = Contact("Dr Laura", "**********", "<EMAIL>")

        Assert.assertEquals(1, orders.list.size)

        val nonexistentId = DomainId.create<OrderLineId>()

        handle(
            buildUpdateMedicineOrderLineCommand(
                id = nonexistentId,
                timestamp = newTimestamp,
                instructions = newInstructions,
                frequency = newFrequency,
                lastOccurrence = newLastOccurrence,
                provider = newProvider,
                medicine = createUpdatedMedicine()
        ), callsign = "NewWorldOrder")

        Assert.assertEquals(1, orders.list.size)
        val updatedOrderLine = orders.list[0] as MedicineOrderLine
        Assert.assertEquals(orderLineToNotUpdate.id, updatedOrderLine.id)
        Assert.assertEquals(orderLineToNotUpdate.timestamp, updatedOrderLine.timestamp)
        Assert.assertEquals(orderLineToNotUpdate.title, updatedOrderLine.title)
        Assert.assertEquals(orderLineToNotUpdate.instructions, updatedOrderLine.instructions)
        Assert.assertEquals(orderLineToNotUpdate.orderType, updatedOrderLine.orderType)
        Assert.assertEquals(orderLineToNotUpdate.orderStatus, updatedOrderLine.orderStatus)
        Assert.assertEquals(orderLineToNotUpdate.frequency, updatedOrderLine.frequency)
        Assert.assertEquals(orderLineToNotUpdate.lastOccurrence, updatedOrderLine.lastOccurrence)
        Assert.assertEquals(orderLineToNotUpdate.provider, updatedOrderLine.provider)
        Assert.assertEquals(orderLineToNotUpdate.signature?.name, updatedOrderLine.signature?.name)
        Assert.assertEquals(orderLineToNotUpdate.signature?.timeStamp, updatedOrderLine.signature?.timeStamp)
        Assert.assertArrayEquals(orderLineToNotUpdate.signature?.signature, updatedOrderLine.signature?.signature)
        Assert.assertEquals(orderLineToNotUpdate.orderedMedicine.name, updatedOrderLine.orderedMedicine.name)
        Assert.assertEquals(orderLineToNotUpdate.orderedMedicine.ndc, updatedOrderLine.orderedMedicine.ndc)
        Assert.assertEquals(orderLineToNotUpdate.orderedMedicine.medId, updatedOrderLine.orderedMedicine.medId)
        Assert.assertEquals(orderLineToNotUpdate.orderedMedicine.route, updatedOrderLine.orderedMedicine.route)
        Assert.assertEquals(orderLineToNotUpdate.orderedMedicine.volume, updatedOrderLine.orderedMedicine.volume)
        Assert.assertEquals(orderLineToNotUpdate.orderedMedicine.unit, updatedOrderLine.orderedMedicine.unit)
        Assert.assertEquals(orderLineToNotUpdate.orderedMedicine.serialNumber, updatedOrderLine.orderedMedicine.serialNumber)
        Assert.assertEquals(orderLineToNotUpdate.orderedMedicine.expirationDate, updatedOrderLine.orderedMedicine.expirationDate)
        Assert.assertEquals(orderLineToNotUpdate.orderedMedicine.expirationDate, updatedOrderLine.orderedMedicine.expirationDate)
        Assert.assertEquals(orderLineToNotUpdate.orderedMedicine.exportName, updatedOrderLine.orderedMedicine.exportName)
    }

    @Test
    fun testNothingHappensIfDoesntExistOnUpdateStatus(){
        val orderLineToNotUpdate = createMedicineOrderLine()
        orders += orderLineToNotUpdate

        val newStatus = OrderStatus.COMPLETE

        Assert.assertEquals(1, orders.list.size)

        val nonexistentId = DomainId.create<OrderLineId>()

        handle(
            buildUpdateMedicineOrderLineStatusCommand(
            id = nonexistentId,
            orderStatus = newStatus
        ), callsign = "NewWorldOrder")

        Assert.assertEquals(1, orders.list.size)
        val updatedOrderLine = orders.list[0] as MedicineOrderLine
        Assert.assertEquals(orderLineToNotUpdate.id, updatedOrderLine.id)
        Assert.assertEquals(orderLineToNotUpdate.timestamp, updatedOrderLine.timestamp)
        Assert.assertEquals(orderLineToNotUpdate.title, updatedOrderLine.title)
        Assert.assertEquals(orderLineToNotUpdate.instructions, updatedOrderLine.instructions)
        Assert.assertEquals(orderLineToNotUpdate.orderType, updatedOrderLine.orderType)
        Assert.assertEquals(orderLineToNotUpdate.orderStatus, updatedOrderLine.orderStatus)
        Assert.assertEquals(orderLineToNotUpdate.frequency, updatedOrderLine.frequency)
        Assert.assertEquals(orderLineToNotUpdate.lastOccurrence, updatedOrderLine.lastOccurrence)
        Assert.assertEquals(orderLineToNotUpdate.provider, updatedOrderLine.provider)
        Assert.assertEquals(orderLineToNotUpdate.signature?.name, updatedOrderLine.signature?.name)
        Assert.assertEquals(orderLineToNotUpdate.signature?.timeStamp, updatedOrderLine.signature?.timeStamp)
        Assert.assertArrayEquals(orderLineToNotUpdate.signature?.signature, updatedOrderLine.signature?.signature)
        Assert.assertEquals(orderLineToNotUpdate.orderedMedicine.name, updatedOrderLine.orderedMedicine.name)
        Assert.assertEquals(orderLineToNotUpdate.orderedMedicine.ndc, updatedOrderLine.orderedMedicine.ndc)
        Assert.assertEquals(orderLineToNotUpdate.orderedMedicine.medId, updatedOrderLine.orderedMedicine.medId)
        Assert.assertEquals(orderLineToNotUpdate.orderedMedicine.route, updatedOrderLine.orderedMedicine.route)
        Assert.assertEquals(orderLineToNotUpdate.orderedMedicine.volume, updatedOrderLine.orderedMedicine.volume)
        Assert.assertEquals(orderLineToNotUpdate.orderedMedicine.unit, updatedOrderLine.orderedMedicine.unit)
        Assert.assertEquals(orderLineToNotUpdate.orderedMedicine.serialNumber, updatedOrderLine.orderedMedicine.serialNumber)
        Assert.assertEquals(orderLineToNotUpdate.orderedMedicine.expirationDate, updatedOrderLine.orderedMedicine.expirationDate)
        Assert.assertEquals(orderLineToNotUpdate.orderedMedicine.expirationDate, updatedOrderLine.orderedMedicine.expirationDate)
        Assert.assertEquals(orderLineToNotUpdate.orderedMedicine.exportName, updatedOrderLine.orderedMedicine.exportName)
    }
}