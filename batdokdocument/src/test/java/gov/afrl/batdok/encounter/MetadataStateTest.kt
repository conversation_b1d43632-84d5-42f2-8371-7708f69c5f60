package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.commands.*
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.encounter.metadata.*
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.LocalDate
import java.time.temporal.ChronoUnit

class MetadataStateTest {

    private val metadata = Metadata()

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS), commandId: CommandId = DomainId.create()) {
        metadata.handlers.handle(
            DomainId.nil(),
            buildCommandData(subCommand, callsign, timestamp, commandId)
        )
    }
    
    @Test
    fun testDecisionCommand_Add(){
        val decision = Decision.CARDIO.dataString
        Assert.assertEquals(0, metadata.decisionsMade.size)
        handle(buildChangeDecisionCommand(decision, true))
        Assert.assertEquals(1, metadata.decisionsMade.size)
        Assert.assertEquals("Cardiovascular", metadata.decisionsMade[0])
    }
    
    @Test
    fun testDecisionCommand_Remove(){
        val decision = Decision.CARDIO.dataString
        metadata.decisionsMade += decision
        Assert.assertEquals(1, metadata.decisionsMade.size)
        Assert.assertEquals("Cardiovascular", metadata.decisionsMade[0])
        handle(buildChangeDecisionCommand(decision, false))
        Assert.assertEquals(0, metadata.decisionsMade.size)
    }


    @Test
    fun testCareCommand_Add(){
        val care = CareType.VAC_CHANGE.dataString
        Assert.assertEquals(0, metadata.careProvided.size)
        handle(buildChangeCareCommand(care, true))
        Assert.assertEquals(1, metadata.careProvided.size)
        Assert.assertEquals("VAC Δ", metadata.careProvided[0])
    }


    @Test
    fun testCareCommand_Remove(){
        val care = CareType.VAC_CHANGE.dataString
        metadata.careProvided += care
        Assert.assertEquals(1, metadata.careProvided.size)
        Assert.assertEquals("VAC Δ", metadata.careProvided[0])
        handle(buildChangeCareCommand(care, false))
        Assert.assertEquals(0, metadata.careProvided.size)
    }
    @Test
    fun testChangeInfectionControlPrecautionCommand_Add(){
        val precaution = InfectionControl.AB.dataString
        Assert.assertEquals(0, metadata.infectionControlPrecautions.size)
        handle(buildChangeInfectionControlPrecautionCommand(precaution, true))
        Assert.assertEquals(1, metadata.infectionControlPrecautions.size)
        Assert.assertEquals(precaution, metadata.infectionControlPrecautions[0])
    }


    @Test
    fun testChangeInfectionControlPrecautionCommand_Remove(){
        val precaution = InfectionControl.AB.dataString
        metadata.infectionControlPrecautions += precaution
        Assert.assertEquals(1, metadata.infectionControlPrecautions.size)
        Assert.assertEquals(precaution, metadata.infectionControlPrecautions[0])
        handle(buildChangeInfectionControlPrecautionCommand(precaution, false))
        Assert.assertEquals(0, metadata.infectionControlPrecautions.size)
    }
    @Test
    fun testProcedureCommand_Add_NoExtras(){
        val procedure = KnownProcedures.CHEST_TUBE.dataString
        Assert.assertEquals(0, metadata.proceduresDone.size)
        handle(buildChangeProcedureCommand(Procedure(procedure)))
        Assert.assertEquals(1, metadata.proceduresDone.size)
        Assert.assertEquals("Chest Tube", metadata.proceduresDone[0].name)
    }
    @Test
    fun testProcedureCommand_Add_WithLineExtras(){
        val procedure = KnownProcedures.LINE.dataString
        Assert.assertEquals(0, metadata.proceduresDone.size)
        handle(buildChangeProcedureCommand(Procedure(procedure, LineData(true, true))))
        Assert.assertEquals(1, metadata.proceduresDone.size)
        Assert.assertEquals("Line", metadata.proceduresDone[0].name)
        val data = metadata.proceduresDone[0].data
        Assert.assertTrue(data is LineData)
        data as LineData
        Assert.assertTrue(data.arterialLine)
        Assert.assertTrue(data.centralLine)
    }
    @Test
    fun testProcedureCommand_Remove(){
        val procedure = KnownProcedures.LINE.dataString
        metadata.proceduresDone += Procedure(procedure, LineData(true, true))
        Assert.assertEquals(1, metadata.proceduresDone.size)
        Assert.assertEquals("Line", metadata.proceduresDone[0].name)
        handle(buildChangeProcedureCommand(Procedure(procedure, LineData(true, true)), false))
        Assert.assertEquals(0, metadata.proceduresDone.size)
    }


    @Test
    fun testAddRemoveMajorEventCommands_AddOnly(){
        val majorEvents = listOf(
            MajorEvent("Event", FreeTextData("Free Text")),
            MajorEvent(MajorEvents.BLEEDING.dataString, BleedingData("Arm", "100mL"))
        )
        val location = "Location"

        Assert.assertEquals(0, metadata.majorEvents[location].size)
        handle(buildAddRemoveMajorEventCommand(location, majorEvents, listOf()))
        Assert.assertEquals(2, metadata.majorEvents[location].size)

        val event1 = metadata.majorEvents[location][0]
        Assert.assertEquals("Event", event1.name)
        Assert.assertEquals("Free Text", (event1.data as FreeTextData).string)

        val event2 = metadata.majorEvents[location][1]
        Assert.assertEquals("Bleeding", event2.name)
        val data = event2.data as BleedingData
        Assert.assertEquals("Arm", data.site)
        Assert.assertEquals("100mL", data.estVol)
    }


    @Test
    fun testAddRemoveMajorEventCommands_RemoveOnly(){
        val majorEvents = listOf(
            MajorEvent("Event", FreeTextData("Free Text")),
            MajorEvent(MajorEvents.BLEEDING.dataString, BleedingData("Arm", "100mL"))
        )
        val location = "Location"
        metadata.majorEvents[location] = majorEvents

        Assert.assertEquals(2, metadata.majorEvents[location].size)
        handle(buildAddRemoveMajorEventCommand(location, listOf(), majorEvents))
        Assert.assertEquals(0, metadata.majorEvents[location].size)
    }

    @Test
    fun testAddRemoveMajorEventCommands_AddAndRemove(){
        val majorEvents = listOf(
            MajorEvent("Event", FreeTextData("Free Text")),
            MajorEvent(MajorEvents.BLEEDING.dataString, BleedingData("Arm", "100mL"))
        )
        val removeEvents = listOf(
            MajorEvent("Remove"),
            MajorEvent(MajorEvents.ARRHYTHMIA.dataString, ArrhythmiaData(true, false))
        )

        val location = "Location"
        metadata.majorEvents[location] = removeEvents

        Assert.assertEquals(2, metadata.majorEvents[location].size)
        handle(buildAddRemoveMajorEventCommand(location, majorEvents, removeEvents))
        Assert.assertEquals(2, metadata.majorEvents[location].size)

        val event1 = metadata.majorEvents[location][0]
        Assert.assertEquals("Event", event1.name)
        Assert.assertEquals("Free Text", (event1.data as FreeTextData).string)

        val event2 = metadata.majorEvents[location][1]
        Assert.assertEquals("Bleeding", event2.name)
        val data = event2.data as BleedingData
        Assert.assertEquals("Arm", data.site)
        Assert.assertEquals("100mL", data.estVol)
    }

    @Test
    fun testChangeFlightInfoCommand_Set(){
        val flightInfo = FlightInfo(
            DomainId.create(),
            "Tail",
            "Origin",
            "Dest",
            Instant.ofEpochSecond(10000000),
            Instant.ofEpochSecond(20000000),
            5000,
            "Unit"
        )
        Assert.assertTrue(metadata.flightInfo.id.isNil())
        handle(buildChangeFlightInfoCommand(flightInfo))
        Assert.assertEquals(flightInfo.id, metadata.flightInfo.id)
        Assert.assertEquals(flightInfo.tail, metadata.flightInfo.tail)
        Assert.assertEquals(flightInfo.origin, metadata.flightInfo.origin)
        Assert.assertEquals(flightInfo.destination, metadata.flightInfo.destination)
        Assert.assertEquals(flightInfo.takeoff, metadata.flightInfo.takeoff)
        Assert.assertEquals(flightInfo.landing, metadata.flightInfo.landing)
        Assert.assertEquals(flightInfo.maxAlt, metadata.flightInfo.maxAlt)
        Assert.assertEquals(flightInfo.unit, metadata.flightInfo.unit)
    }

    @Test
    fun testChangeFlightInfoCommand_Clear(){
        val flightInfo = FlightInfo()
        metadata.flightInfo = FlightInfo(
            DomainId.create(),
            "Tail",
            "Origin",
            "Dest",
            Instant.ofEpochSecond(10000000),
            Instant.ofEpochSecond(20000000),
            5000,
            "Unit"
        )
        Assert.assertFalse(metadata.flightInfo.id.isNil())
        handle(buildChangeFlightInfoCommand(flightInfo))
        Assert.assertEquals(flightInfo.id, metadata.flightInfo.id)
        Assert.assertEquals(flightInfo.tail, metadata.flightInfo.tail)
        Assert.assertEquals(flightInfo.origin, metadata.flightInfo.origin)
        Assert.assertEquals(flightInfo.destination, metadata.flightInfo.destination)
        Assert.assertEquals(flightInfo.takeoff, metadata.flightInfo.takeoff)
        Assert.assertEquals(flightInfo.landing, metadata.flightInfo.landing)
        Assert.assertEquals(flightInfo.maxAlt, metadata.flightInfo.maxAlt)
        Assert.assertEquals(flightInfo.unit, metadata.flightInfo.unit)
    }

    @Test
    fun testAddPatientMediaCommand(){
        val filename = "Image.jpg"
        Assert.assertTrue(metadata.medialist.isEmpty())
        handle(buildAddMediaFileCommand(filename))
        Assert.assertEquals(filename, metadata.medialist[0])
    }

    @Test
    fun testChangeInsuranceCommand() {
        val insurance = Insurance(
            "Company",
            "123 Example St. Dayton, OH 12345",
            "**************",
            "ABCDEF123456",
            "self"
        )
        Assert.assertNull(metadata.insurance.companyName)
        Assert.assertNull(metadata.insurance.companyAddress)
        Assert.assertNull(metadata.insurance.companyPhone)
        Assert.assertNull(metadata.insurance.policyNumber)
        Assert.assertNull(metadata.insurance.relationToPolicyHolder)
        handle(buildChangeInsuranceCommand(insurance))
        Assert.assertEquals(insurance.companyName, metadata.insurance.companyName)
        Assert.assertEquals(insurance.companyAddress, metadata.insurance.companyAddress)
        Assert.assertEquals(insurance.companyPhone, metadata.insurance.companyPhone)
        Assert.assertEquals(insurance.policyNumber, metadata.insurance.policyNumber)
        Assert.assertEquals(insurance.relationToPolicyHolder, metadata.insurance.relationToPolicyHolder)
    }

    @Test
    fun testChangeInsuranceCommand_NullsAndBlanks() {
        val insurance = Insurance(
            companyName = "Company",
            companyAddress = "     ",
            policyNumber = "ABCDEF123456",
            relationToPolicyHolder = "self"
        )
        Assert.assertNull(metadata.insurance.companyName)
        Assert.assertNull(metadata.insurance.companyAddress)
        Assert.assertNull(metadata.insurance.companyPhone)
        Assert.assertNull(metadata.insurance.policyNumber)
        Assert.assertNull(metadata.insurance.relationToPolicyHolder)
        handle(buildChangeInsuranceCommand(insurance))
        Assert.assertEquals(insurance.companyName, metadata.insurance.companyName)
        Assert.assertNull(metadata.insurance.companyAddress)
        Assert.assertNull(metadata.insurance.companyPhone)
        Assert.assertEquals(insurance.policyNumber, metadata.insurance.policyNumber)
        Assert.assertEquals(insurance.relationToPolicyHolder, metadata.insurance.relationToPolicyHolder)
    }

    @Test
    fun testChangeAttendingPhysicianCommand() {
        val physician = Physician(
            "John Doe",
            "**************",
            "j.doe@unknown"
        )
        Assert.assertNull(metadata.attendingPhysician.name)
        Assert.assertNull(metadata.attendingPhysician.phone)
        Assert.assertNull(metadata.attendingPhysician.email)
        handle(buildChangeAttendingPhysicianCommand(physician))
        Assert.assertEquals(physician.name, metadata.attendingPhysician.name)
        Assert.assertEquals(physician.phone, metadata.attendingPhysician.phone)
        Assert.assertEquals(physician.email, metadata.attendingPhysician.email)
    }

    @Test
    fun testChangeAttendingPhysicianCommand_NullAndBlank() {
        val physician = Physician(
            "John Doe",
            null,
            "     "
        )
        Assert.assertNull(metadata.attendingPhysician.name)
        Assert.assertNull(metadata.attendingPhysician.phone)
        Assert.assertNull(metadata.attendingPhysician.email)
        handle(buildChangeAttendingPhysicianCommand(physician))
        Assert.assertEquals(physician.name, metadata.attendingPhysician.name)
        Assert.assertNull(metadata.attendingPhysician.phone)
        Assert.assertNull(metadata.attendingPhysician.email)
    }

    @Test
    fun testSetSignaturePMRPhysicianCommandHandler() {
        val signature = Signature(
            name = "Dr. Fred",
            timeStamp = Instant.now().truncatedTo(ChronoUnit.SECONDS),
            signature = "Test".toByteArray()
        )
        Assert.assertNull(metadata.signatures.pmrPhysicianSignature)
        handle(buildSetSignaturePMRPhysicianCommand(signature))
        Assert.assertEquals(signature, metadata.signatures.pmrPhysicianSignature)
    }

    @Test
    fun testSetSignaturePMRFlightSurgeonCommandHandler() {
        val signature = Signature(
            name = "Dr. Fred",
            timeStamp = Instant.now().truncatedTo(ChronoUnit.SECONDS),
            signature = "Test".toByteArray()
        )
        Assert.assertNull(metadata.signatures.pmrFlightSurgeonSignature)
        handle(buildSetSignaturePMRFlightSurgeonCommand(signature))
        Assert.assertEquals(signature, metadata.signatures.pmrFlightSurgeonSignature)
    }

    @Test
    fun testRemoveSignaturePMRPhysicianCommandHandler() {
        metadata.signatures.pmrPhysicianSignature = Signature()
        Assert.assertNotNull(metadata.signatures.pmrPhysicianSignature)
        handle(buildRemoveSignaturePMRPhysicianCommand())
        Assert.assertNull(metadata.signatures.pmrPhysicianSignature)
    }

    @Test
    fun testRemoveSignaturePMRFlightSurgeonCommandHandler() {
        metadata.signatures.pmrFlightSurgeonSignature = Signature()
        Assert.assertNotNull(metadata.signatures.pmrFlightSurgeonSignature)
        handle(buildRemoveSignaturePMRFlightSurgeonCommand())
        Assert.assertNull(metadata.signatures.pmrFlightSurgeonSignature)
    }

    @Test
    fun testChangeDeathDeclarationCommand() {
        Assert.assertNull(metadata.deathDeclaration)
        val date = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        handle(buildChangeDeathDeclarationCommand(DeathDeclaration(date, "Declarer", "Certification", "Verification")))
        Assert.assertNotNull(metadata.deathDeclaration)
        Assert.assertEquals(date, metadata.deathDeclaration?.timeOfDeath)
        Assert.assertEquals("Declarer", metadata.deathDeclaration?.declarer)
        Assert.assertEquals("Certification", metadata.deathDeclaration?.certification)
        Assert.assertEquals("Verification", metadata.deathDeclaration?.verificationMethod)
    }

    @Test
    fun testChangeDeathDeclarationCommand_ChangeExisting() {
        val date = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        metadata.deathDeclaration = DeathDeclaration(date, "Declarer", "Certification", "Verification")
        Assert.assertNotNull(metadata.deathDeclaration)

        val command = buildChangeDeathDeclarationCommand(DeathDeclaration(date, "New Declarer", "Certification2", "Verification"), metadata.deathDeclaration)

        Assert.assertFalse(command.hasVerificationMethod())
        Assert.assertFalse(command.hasTimeOfDeath())

        handle(command)
        Assert.assertEquals(date, metadata.deathDeclaration?.timeOfDeath)
        Assert.assertEquals("New Declarer", metadata.deathDeclaration?.declarer)
        Assert.assertEquals("Certification2", metadata.deathDeclaration?.certification)
        Assert.assertEquals("Verification", metadata.deathDeclaration?.verificationMethod)
    }

    @Test
    fun testRemoveDeathDeclarationCommand() {
        val date = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        metadata.deathDeclaration = DeathDeclaration(date, "Declarer", "Certification", "Verification")
        Assert.assertNotNull(metadata.deathDeclaration)
        handle(buildRemoveDeathDeclarationCommand("Error"))
        Assert.assertNull(metadata.deathDeclaration)
    }

    @Test
    fun testChangeAppointmentDateCommand() {
        Assert.assertNull(metadata.appointmentDate)
        val date = LocalDate.now()
        handle(buildChangeAppointmentDateCommand(date))
        Assert.assertEquals(date, metadata.appointmentDate)
    }

    @Test
    fun testChangeAppointmentDateCommand_ChangeExisting() {
        val date = LocalDate.of(2009, 1, 20)
        val date2 = LocalDate.of(2016, 11, 30)
        metadata.appointmentDate = date
        Assert.assertNotNull(metadata.appointmentDate)
        val command = buildChangeAppointmentDateCommand(date2)
        handle(command)
        Assert.assertEquals(date2, metadata.appointmentDate)
    }

    @Test
    fun testRemoveAppointmentDateCommand() {
        val date = LocalDate.of(2020, 6, 15)
        metadata.appointmentDate = date
        Assert.assertNotNull(metadata.appointmentDate)
        handle(buildRemoveAppointmentDateCommand())
        Assert.assertNull(metadata.appointmentDate)
    }

    @Test
    fun testUpdateThumbnailCommand_set(){
        val filename = "testfile.jpg"
        handle(buildUpdateThumbnailCommand(filename))
        Assert.assertEquals(filename, metadata.thumbnailFileName)
    }

    @Test
    fun testUpdateThumbnailCommand_update(){
        val filename = "testfile.jpg"
        metadata.thumbnailFileName = "oldname"
        handle(buildUpdateThumbnailCommand(filename))
        Assert.assertEquals(filename, metadata.thumbnailFileName)
    }
    @Test
    fun testUpdateThumbnailCommand_clear(){
        metadata.thumbnailFileName = "oldname"
        handle(buildUpdateThumbnailCommand(null))
        Assert.assertNull(metadata.thumbnailFileName)
    }

    @Test
    fun testUpdateCreationNameCommand(){
        metadata.creationName = null
        val first = "First"
        val middle = "Middle"
        val last = "Last"
        handle(buildUpdateCreationNameCommand(first,middle, last))
        Assert.assertEquals(first, metadata.creationName!!.first)
        Assert.assertEquals(middle, metadata.creationName!!.middle)
        Assert.assertEquals(last, metadata.creationName!!.last)
    }

    @Test
    fun testUpdateCreationAliasCommand(){
        metadata.creationAlias = null
        val creationAlias = "ABC_1"
        handle(buildUpdateCreationAliasCommand(creationAlias))
        Assert.assertEquals(creationAlias, metadata.creationAlias)
    }
}