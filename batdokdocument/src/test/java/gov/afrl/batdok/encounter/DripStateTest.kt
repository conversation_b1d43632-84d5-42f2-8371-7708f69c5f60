package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.commands.*
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.encounter.ids.MedicineId
import gov.afrl.batdok.encounter.medicine.Drip
import gov.afrl.batdok.encounter.medicine.Drips
import gov.afrl.batdok.encounter.medicine.Medicine
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant

class DripStateTest {
    val docId = DomainId.create<DocumentId>()
    val drips = Drips()
    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now()) {
        drips.handlers.handle(docId, buildCommandData(subCommand,callsign, timestamp))
    }

    @Test
    fun testCreateDrip(){
        val id = DomainId.create<MedicineId>()
        val medicine = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            Instant.ofEpochSecond(12331),
            id,
            "testroute",
            1.2f,
            "cm",
            "45646",
            "11111",
            "tsttype",
            docId
        )
        Assert.assertEquals(0, drips.drips.size)
        handle(buildCreateDrip(medicine),  timestamp = medicine.administrationTime!!)

        Assert.assertEquals(1, drips.drips.size)
        Assert.assertEquals(id, drips.drips[0].dripId)
        Assert.assertEquals(medicine, drips.drips[0].base)
        Assert.assertNull(drips.drips[0].startTime)
        Assert.assertNull(drips.drips[0].endTime)
        Assert.assertEquals(false, drips.drips[0].isStarted)
        Assert.assertEquals(false, drips.drips[0].isStopped)
        Assert.assertEquals(docId, drips.drips[0].documentId)
    }
    @Test
    fun testRemoveDrip() {
        val id = DomainId.create<MedicineId>()
        val medicine = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            Instant.ofEpochSecond(12331),
            id,
            "testroute",
            1.2f,
            "cm",
            "45646",
            "11111",
            "tsttype",
            docId
        )
        Assert.assertEquals(0, drips.drips.size)
        val testDrip = Drip(
            medicine,
            medicine.administrationTime,
            MutableList(1){medicine},
            documentId = docId
        )
        drips += testDrip
        Assert.assertEquals(1, drips.drips.size)

        handle(buildRemoveDrip(id), timestamp = medicine.administrationTime!!)
        Assert.assertEquals(0, drips.drips.size)
    }
    @Test
    fun testAddToDrip(){
        val id = DomainId.create<MedicineId>()
        val medicine = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            Instant.ofEpochSecond(12331),
            id,
            "testroute",
            1.2f,
            "cm",
            "45646",
            "11111",
            "tsttype",
            docId
        )
        Assert.assertEquals(0, drips.drips.size)
        val testDrip = Drip(
            medicine,
            medicine.administrationTime,
            MutableList(1){medicine},
            documentId = docId
        )
        drips += testDrip
        Assert.assertEquals(1, drips.drips.size)

        val id2 = DomainId.create<MedicineId>()
        val medicine2 = Medicine(
            "newmed2",
            "testNdc2",
            "testRxcui",
            Instant.ofEpochSecond(100),
            id2,
            "testroute2",
            1.1f,
            "cm",
            "4444",
            "0000",
            "tsttype2",
            docId
        )
        Assert.assertEquals(1, drips.drips[0].components.size)

        handle(buildAddToDrip(testDrip, medicine2), timestamp = medicine2.administrationTime!!)
        Assert.assertEquals(1, drips.drips.size)
        Assert.assertEquals(2, drips.drips[0].components.size)
        Assert.assertEquals(medicine, drips.drips[0].components[0])
        Assert.assertEquals(medicine2, drips.drips[0].components[1])
        Assert.assertNull(drips.drips[0].startTime)
        Assert.assertNull(drips.drips[0].endTime)
        Assert.assertEquals(false, drips.drips[0].isStarted)
        Assert.assertEquals(false, drips.drips[0].isStopped)
        Assert.assertEquals(docId, drips.drips[0].documentId)
    }
    @Test
    fun testRemoveFromDrip(){
        val id = DomainId.create<MedicineId>()
        val medicine = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            Instant.ofEpochSecond(12331),
            id,
            "testroute",
            1.2f,
            "cm",
            "45646",
            "11111",
            "tsttype",
            docId
        )
        Assert.assertEquals(0, drips.drips.size)
        val testDrip = Drip(
            medicine,
            medicine.administrationTime,
            MutableList(1){medicine},
            documentId = docId
        )
        drips += testDrip
        Assert.assertEquals(1, drips.drips.size)

        val id2 = DomainId.create<MedicineId>()
        val medicine2 = Medicine(
            "newmed2",
            "testNdc2",
            "testRxcui",
            Instant.ofEpochSecond(100),
            id2,
            "testroute2",
            1.1f,
            "cm",
            "4444",
            "0000",
            "tsttype2",
            docId
        )
        Assert.assertEquals(1, drips.drips[0].components.size)
        drips.drips[0].components.add(medicine2)
        Assert.assertEquals(2, drips.drips[0].components.size)

        handle(buildRemoveFromDrip(testDrip, medicine2), timestamp = medicine2.administrationTime!!)
        Assert.assertEquals(1, drips.drips[0].components.size)
        Assert.assertEquals(medicine, drips.drips[0].components[0])
        Assert.assertNull(drips.drips[0].startTime)
        Assert.assertNull(drips.drips[0].endTime)
        Assert.assertEquals(false, drips.drips[0].isStarted)
        Assert.assertEquals(false, drips.drips[0].isStopped)
        Assert.assertEquals(docId, drips.drips[0].documentId)
    }
    @Test
    fun testStartDrip(){
        val id = DomainId.create<MedicineId>()
        val medicine = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            Instant.ofEpochSecond(12331),
            id,
            "testroute",
            1.2f,
            "cm",
            "45646",
            "11111",
            "tsttype",
            docId
        )
        Assert.assertEquals(0, drips.drips.size)
        val testDrip = Drip(
            medicine,
            medicine.administrationTime,
            MutableList(1){medicine},
            documentId = docId
        )
        drips += testDrip
        Assert.assertEquals(1, drips.drips.size)
        Assert.assertEquals(false, drips.drips[0].isStarted)

        //start drip
        handle(buildStartDrip(testDrip, Instant.ofEpochSecond(1111)), timestamp = medicine.administrationTime!!)
        Assert.assertEquals(true, drips.drips[0].isStarted)
        Assert.assertEquals(Instant.ofEpochSecond(1111), drips.drips[0].startTime)
        //start drip when drip has not stopped
        handle(buildStartDrip(testDrip, Instant.ofEpochSecond(2222)), timestamp = medicine.administrationTime)
        Assert.assertEquals(Instant.ofEpochSecond(1111), drips.drips[0].startTime)

        //stop drip and restart drip
        testDrip.endTime = Instant.ofEpochSecond(9999)
        handle(buildStartDrip(testDrip, Instant.ofEpochSecond(2222)), timestamp = medicine.administrationTime)
        Assert.assertEquals(Instant.ofEpochSecond(2222), drips.drips[0].startTime)
        Assert.assertEquals(true, drips.drips[0].isStarted)
        Assert.assertEquals(false, drips.drips[0].isStopped)
        Assert.assertEquals(docId, drips.drips[0].documentId)
    }
    @Test
    fun testStopDrip(){
        val id = DomainId.create<MedicineId>()
        val medicine = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            Instant.ofEpochSecond(12331),
            id,
            "testroute",
            1.2f,
            "cm",
            "45646",
            "11111",
            "tsttype",
            docId
        )
        Assert.assertEquals(0, drips.drips.size)
        val testDrip = Drip(
            medicine,
            medicine.administrationTime,
            MutableList(1){medicine},
            documentId = docId
        )
        drips += testDrip
        Assert.assertEquals(1, drips.drips.size)
        Assert.assertEquals(false, drips.drips[0].isStopped)

        //drip has not started yet
        handle(buildStopDrip(testDrip, Instant.ofEpochSecond(1234)))
        Assert.assertEquals(false, drips.drips[0].isStarted)
        Assert.assertEquals(false, drips.drips[0].isStopped)

        //drip has started
        testDrip.startTime = Instant.ofEpochSecond(1234)
        Assert.assertEquals(true, drips.drips[0].isStarted)
        Assert.assertEquals(false, drips.drips[0].isStopped)

        handle(buildStopDrip(testDrip, Instant.ofEpochSecond(2222)), timestamp = medicine.administrationTime!!)
        Assert.assertEquals(true, drips.drips[0].isStopped)
        Assert.assertEquals(Instant.ofEpochSecond(2222), drips.drips[0].endTime)
        Assert.assertEquals(docId, drips.drips[0].documentId)
    }
}