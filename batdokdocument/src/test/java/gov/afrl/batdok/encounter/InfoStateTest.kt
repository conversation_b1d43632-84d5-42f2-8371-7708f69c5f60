package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.commands.*
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.encounter.ids.ProblemId
import gov.afrl.batdok.encounter.movement.EvacType
import gov.afrl.batdok.encounter.movement.TriageCategory
import gov.afrl.batdok.encounter.pampi.Immunization
import gov.afrl.batdok.encounter.pampi.Problem
import gov.afrl.batdok.encounter.pampi.ProblemStatus
import gov.afrl.batdok.encounter.pampi.Procedure
import gov.afrl.batdok.util.NameFormatter
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.LocalDate
import java.time.temporal.ChronoUnit

class InfoStateTest {

    val info = Info()

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS)) {
        info.handlers.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp))
    }

    @Test
    fun testChangeName_SingleString_JustLast(){
        val newName = "Test"
        Assert.assertNotEquals(newName, info.name?.last)
        handle(buildChangeNameCommand(newName))
        Assert.assertEquals(newName, info.name?.last)
    }

    @Test
    fun testChangeName_SingleName_FullName(){
        val newName = "Bruce Batman Wayne"
        Assert.assertNotEquals(newName, info.name?.toString(NameFormatter.Format.FIRST_MIDDLE_LAST))
        handle(buildChangeNameCommand(newName))
        Assert.assertEquals(newName, info.name?.toString(NameFormatter.Format.FIRST_MIDDLE_LAST))
    }

    @Test
    fun testChangeName_ThreeNames(){
        val newName = "Bruce Batman Wayne"
        Assert.assertNotEquals(newName, info.name?.toString(NameFormatter.Format.FIRST_MIDDLE_LAST))
        handle(buildChangeNameCommand("Bruce", "Batman", "Wayne"))
        Assert.assertEquals(newName, info.name?.toString(NameFormatter.Format.FIRST_MIDDLE_LAST))
    }

    @Test
    fun testChangeName_ThreeNames_JustMiddle(){
        val newName = "Batman"
        Assert.assertNotEquals(newName, info.name?.toString(NameFormatter.Format.FIRST_MIDDLE_LAST))
        handle(buildChangeNameCommand(null, "Batman", null))
        Assert.assertEquals(newName, info.name?.middle)
        Assert.assertEquals(newName, info.name?.toString(NameFormatter.Format.FIRST_MIDDLE_LAST))
    }

    @Test
    fun testChangeSSN(){
        val newSSN = "1234"
        Assert.assertNotEquals(newSSN, info.ssn)
        handle(buildChangeSsnCommand(newSSN))
        Assert.assertEquals(newSSN, info.ssn)
    }

    @Test
    fun testChangeCiteNumber(){
        val newCiteNumber = "1234"
        Assert.assertNotEquals(newCiteNumber, info.citeNumber)
        handle(buildChangeCiteNumberCommand(newCiteNumber))
        Assert.assertEquals(newCiteNumber, info.citeNumber)
    }

    @Test
    fun testChangeBattleRosterNumber(){
        val newBRN = "Test1234"
        Assert.assertNotEquals(newBRN, info.battleRosterNumber)
        handle(buildChangeBattleRosterNumberCommand(newBRN))
        Assert.assertEquals(newBRN, info.battleRosterNumber)
    }

    @Test
    fun testChangeAlias(){
        val newAlias = "Bed 3"
        Assert.assertNotEquals(newAlias, info.alias)
        handle(buildChangeAliasCommand(newAlias))
        Assert.assertEquals(newAlias, info.alias)
    }

    @Test
    fun testChangeUnitCommand(){
        val newUnit = "Test"
        Assert.assertNotEquals(newUnit, info.unit)
        handle(buildChangeUnitCommand(newUnit))
        Assert.assertEquals(newUnit, info.unit)
    }
    @Test
    fun testChangeUnitPhoneNumberCommand(){
        val newUnitPhoneNumber = "Test"
        Assert.assertNotEquals(newUnitPhoneNumber, info.unitPhoneNumber)
        handle(buildChangeUnitPhoneNumberCommand(newUnitPhoneNumber))
        Assert.assertEquals(newUnitPhoneNumber, info.unitPhoneNumber)
    }
    @Test
    fun testChangeRankCommand(){
        val newRank = "Test"
        Assert.assertNotEquals(newRank, info.rank)
        handle(buildChangeRankCommand(newRank))
        Assert.assertEquals(newRank, info.rank)
    }
    @Test
    fun testChangeGradeCommand_enum() {
        val newGrade = Grade.E09
        Assert.assertNotEquals(newGrade.dataString, info.grade)
        handle(buildChangeGradeCommand(newGrade))
        Assert.assertEquals(newGrade.dataString, info.grade)
    }
    @Test
    fun testChangeGradeCommand_string() {
        val newGrade = "Test"
        Assert.assertNotEquals(newGrade, info.grade)
        handle(buildChangeGradeCommand(newGrade))
        Assert.assertEquals(newGrade, info.grade)
    }

    @Test
    fun testChangeNationalityCommand() {
        val newNationality = CountryData.AFGHANISTAN.dataString
        Assert.assertNotEquals(newNationality, info.nationality?.dataString)
        handle(buildChangeNationalityCommand(newNationality))
        Assert.assertEquals(newNationality, info.nationality?.dataString)
    }
    @Test
    fun testChangeGradeCommand_null() {
        val newGrade: String? = null
        handle(buildChangeGradeCommand(Grade.W01))
        Assert.assertNotEquals(newGrade, info.grade)
        handle(buildChangeGradeCommand(newGrade))
        Assert.assertEquals(newGrade, info.grade)
    }
    @Test
    fun testChangeServiceCommand_enum(){
        val newService = Service.AIR_FORCE
        Assert.assertNotEquals(newService.dataString, info.service)
        handle(buildChangeServiceCommand(newService))
        Assert.assertEquals(newService.dataString, info.service)
    }
    @Test
    fun testChangeServiceCommand_string(){
        val newService = "Test"
        Assert.assertNotEquals(newService, info.service)
        handle(buildChangeServiceCommand(newService))
        Assert.assertEquals(newService, info.service)
    }
    @Test
    fun testChangePatcatCommand_enum_valid(){
        val newService = PatcatService.ARMY
        val newStatus = PatcatStatus.CADET
        Assert.assertNotEquals(newService, info.patcat?.service)
        Assert.assertNotEquals(newStatus, info.patcat?.status)
        handle(buildChangePatcatCommand(Patcat(newService, newStatus)))
        Assert.assertNotNull(info.patcat)
        val patcat = info.patcat!!
        Assert.assertEquals(newService, patcat.service)
        Assert.assertEquals(newStatus, patcat.status)
        Assert.assertEquals("A14", patcat.getCode())
        Assert.assertEquals("USA CADET", patcat.getShortDescription())
        Assert.assertEquals("USA CADET", patcat.getFullDescription())
        Assert.assertTrue(patcat.isValid())
    }
    @Test
    fun testChangePatcatCommand_enum_invalid(){
        val newService = PatcatService.ARMY // this is a military service
        val newStatus = PatcatStatus.CIVILIAN_HUMANITARIAN_OR_REFUGEE // this is a civilian status, so it's invalid here
        Assert.assertNotEquals(newService, info.patcat?.service)
        Assert.assertNotEquals(newStatus, info.patcat?.status)
        handle(buildChangePatcatCommand(Patcat(newService, newStatus)))
        Assert.assertNotNull(info.patcat)
        val patcat = info.patcat!!
        Assert.assertEquals(newService, patcat.service)
        Assert.assertEquals(newStatus, patcat.status)
        Assert.assertEquals("A91", patcat.getCode())
        Assert.assertEquals("USA CIV HUM", patcat.getShortDescription())
        Assert.assertEquals("USA CIVILIAN HUMANITARIAN", patcat.getFullDescription())
        Assert.assertFalse(patcat.isValid())
    }
    @Test
    fun testChangePatcatCommand_string_valid(){
        val newService = PatcatService.ARMY
        val newStatus = PatcatStatus.CADET
        Assert.assertNotEquals(newService, info.patcat?.service)
        Assert.assertNotEquals(newStatus, info.patcat?.status)
        handle(buildChangePatcatCommand(Patcat(newService.dataString, newStatus.dataString)))
        Assert.assertNotNull(info.patcat)
        val patcat = info.patcat!!
        Assert.assertEquals(newService, patcat.service)
        Assert.assertEquals(newStatus, patcat.status)
        Assert.assertEquals("A14", patcat.getCode())
        Assert.assertEquals("USA CADET", patcat.getShortDescription())
        Assert.assertEquals("USA CADET", patcat.getFullDescription())
        Assert.assertTrue(patcat.isValid())
    }
    @Test
    fun testChangePatcatCommand_string_invalid(){
        val newServiceCode = "ZZZ" // these codes don't exist
        val newStatusCode = "9000"
        Assert.assertNotEquals(newServiceCode, info.patcat?.serviceCode)
        Assert.assertNotEquals(newStatusCode, info.patcat?.statusCode)
        handle(buildChangePatcatCommand(Patcat(newServiceCode, newStatusCode)))
        Assert.assertNotNull(info.patcat)
        val patcat = info.patcat!!
        Assert.assertEquals(newServiceCode, patcat.serviceCode)
        Assert.assertEquals(newStatusCode, patcat.statusCode)
        Assert.assertEquals("ZZZ9000", patcat.getCode())
        Assert.assertEquals("ZZZ 9000", patcat.getShortDescription())
        Assert.assertEquals("ZZZ 9000", patcat.getFullDescription())
        Assert.assertFalse(patcat.isValid())
    }

    @Test
    fun testNullPatcatNotSaved_string(){
        val newServiceCode: String? = null
        val newStatusCode = null
        handle(buildChangePatcatCommand(Patcat(newServiceCode, newStatusCode)))
        Assert.assertNull(info.patcat)
    }

    @Test
    fun testGetCodeWithOneNull(){
        val serviceCode = "ABC"
        handle(buildChangePatcatCommand(Patcat(serviceCode, null)))
        Assert.assertEquals(serviceCode, info.patcat?.getCode())

        val statusCode = "123"
        handle(buildChangePatcatCommand(Patcat(null, statusCode)))
        Assert.assertEquals(statusCode, info.patcat?.getCode())
    }
    @Test
    fun testNullClearsPatcat(){
        handle(buildChangePatcatCommand(Patcat("ABC", "123")))
        handle(buildChangePatcatCommand(null))
        Assert.assertNull(info.patcat)
    }
    @Test
    fun testChangeTattooCommand(){
        val newTattoo = "Test"
        Assert.assertNotEquals(newTattoo, info.tattoo)
        handle(buildChangeTattooCommand(newTattoo))
        Assert.assertEquals(newTattoo, info.tattoo)
    }
    @Test
    fun testChangeTriageCommand_enum(){
        val newTriage = TriageCategory.EXPECTANT
        Assert.assertNotEquals(newTriage.dataString, info.triage)
        handle(buildChangeTriageCommand(newTriage))
        Assert.assertEquals(newTriage.dataString, info.triage)
    }
    @Test
    fun testChangeTriageCommand_string(){
        val newTriage = "Test"
        Assert.assertNotEquals(newTriage, info.triage)
        handle(buildChangeTriageCommand(newTriage))
        Assert.assertEquals(newTriage, info.triage)
    }
    @Test
    fun testChangeEvacTypeCommand_enum(){
        val newEvacType = EvacType.CASEVAC
        Assert.assertNotEquals(newEvacType.dataString, info.evacType)
        handle(buildChangeEvacTypeCommand(newEvacType))
        Assert.assertEquals(newEvacType.dataString, info.evacType)
    }
    @Test
    fun testChangeEvacTypeCommand_string(){
        val newEvacType = "Test"
        Assert.assertNotEquals(newEvacType, info.evacType)
        handle(buildChangeEvacTypeCommand(newEvacType))
        Assert.assertEquals(newEvacType, info.evacType)
    }
    @Test
    fun testChangeBloodTypeCommand_PositiveLowTiter_enum(){
        val newBloodType = BloodType.AB_NEGATIVE
        val lowTiter = true
        Assert.assertNotEquals(newBloodType.dataString, info.bloodType)
        Assert.assertNotEquals(lowTiter, info.lowTiter)
        handle(buildChangeBloodTypeCommand(newBloodType, lowTiter))
        Assert.assertEquals(newBloodType.dataString, info.bloodType)
        Assert.assertEquals(lowTiter, info.lowTiter)
    }
    @Test
    fun testChangeBloodTypeCommand_NegativeLowTiter_string(){
        val newBloodType = "Test"
        val lowTiter = false
        Assert.assertNotEquals(newBloodType, info.bloodType)
        Assert.assertNotEquals(lowTiter, info.lowTiter)
        handle(buildChangeBloodTypeCommand(newBloodType, lowTiter))
        Assert.assertEquals(newBloodType, info.bloodType)
        Assert.assertEquals(lowTiter, info.lowTiter)
    }
    @Test
    fun testChangeBloodTypeCommand_NoLowTiter_string(){
        val newBloodType = "Test"
        val lowTiter = null
        info.lowTiter = true
        Assert.assertNotEquals(newBloodType, info.bloodType)
        Assert.assertNotEquals(lowTiter, info.lowTiter)
        handle(buildChangeBloodTypeCommand(newBloodType, lowTiter))
        Assert.assertEquals(newBloodType, info.bloodType)
        Assert.assertEquals(lowTiter, info.lowTiter)
    }
    @Test
    fun testChangeGenderCommand_enum(){
        val newGender = Gender.MALE
        Assert.assertNotEquals(newGender.dataString, info.gender)
        handle(buildChangeGenderCommand(newGender))
        Assert.assertEquals(newGender.dataString, info.gender)
    }
    @Test
    fun testChangeGenderCommand_string(){
        val newGender = "Test"
        Assert.assertNotEquals(newGender, info.gender)
        handle(buildChangeGenderCommand(newGender))
        Assert.assertEquals(newGender, info.gender)
    }
    @Test
    fun testChangeInjuryTimeCommand(){
        val newInjuryTime = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        Assert.assertNotEquals(newInjuryTime, info.timeInfo)
        handle(buildChangeInjuryTimeCommand(newInjuryTime))
        Assert.assertEquals(newInjuryTime, info.timeInfo)
    }
    @Test
    fun testChangeDobCommand(){
        val newDob = LocalDate.now()
        val estimate = true
        Assert.assertNotEquals(newDob, info.dob)
        Assert.assertNotEquals(newDob, info.dateOfBirth.dob)
        Assert.assertNotEquals(estimate, info.dateOfBirth.estimate)
        handle(buildChangeDOBCommand(newDob, estimate))
        Assert.assertEquals(newDob, info.dob)
        Assert.assertEquals(newDob, info.dateOfBirth.dob)
        Assert.assertEquals(estimate, info.dateOfBirth.estimate)
    }
    @Test
    fun testChangeDodIdCommand(){
        val newDodId = "Test"
        Assert.assertNotEquals(newDodId, info.dodId)
        handle(buildChangeDodIdCommand(newDodId))
        Assert.assertEquals(newDodId, info.dodId)
    }
    @Test
    fun testChangeWeightCommand(){
        val newWeight = 123.4f
        Assert.assertNotEquals(newWeight, info.weight)
        handle(buildChangeWeightCommand(newWeight))
        Assert.assertEquals(newWeight, info.weight)
    }
    @Test
    fun testChangeHeightCommand(){
        val newHeight = 123.4f
        Assert.assertNotEquals(newHeight, info.height)
        handle(buildChangeHeightCommand(newHeight))
        Assert.assertEquals(newHeight, info.height)
    }
    @Test
    fun testChangeDispositionCommand(){
        val newDisposition = Disposition.DECEASED.dataString
        Assert.assertNotEquals(newDisposition, info.disposition)
        handle(buildChangeDispositionCommand(newDisposition))
        Assert.assertEquals(newDisposition, info.disposition)
    }

    //region Allergy Tests
    @Test
    fun testAddAllergies(){
        val allergies = listOf("Opiates", "Penicillin", "A3")
        Assert.assertNotEquals(allergies.size, info.allergies.size)
        handle(buildAddRemoveAllergyListCommand(allergies, listOf()))
        Assert.assertEquals(allergies.size, info.allergies.size)
    }

    @Test
    fun testAddAllergies_duplicates(){
        val allergies = listOf("A2", "A2", "A2", "A2", "A2")
        Assert.assertNotEquals(allergies.size, info.allergies.size)

        handle(buildAddRemoveAllergyListCommand(allergies, listOf()))
        Assert.assertEquals(1, info.allergies.size)

        handle(buildAddRemoveAllergyListCommand(allergies, listOf()))
        Assert.assertEquals(1, info.allergies.size)
    }

    @Test
    fun testRemoveAllergies_duplicates(){
        val allergies = listOf("A2", "A2", "A2", "A2", "A2")
        Assert.assertNotEquals(allergies.size, info.allergies.size)

        info.allergies = allergies.distinct()

        handle(buildAddRemoveAllergyListCommand(listOf(), allergies))
        Assert.assertEquals(0, info.allergies.size)
    }

    @Test
    fun testRemoveAllergies(){
        val allergies = listOf("Opiates", "Penicillin", "A3")
        info.allergies = allergies
        Assert.assertEquals(allergies.size, info.allergies.size)
        handle(buildAddRemoveAllergyListCommand(listOf(), allergies))
        Assert.assertEquals(0, info.allergies.size)
    }

    @Test
    fun testAddAndRemoveAllergies(){
        val allergies = listOf("Opiates", "A2", "A3")
        val allergiesToRemove = listOf("Penicillin")
        info.allergies = allergiesToRemove
        Assert.assertEquals(allergiesToRemove.size, info.allergies.size)
        handle(buildAddRemoveAllergyListCommand(allergies, allergiesToRemove))
        Assert.assertEquals(allergies.size, info.allergies.size)
    }

    @Test
    fun testAddAndRemoveAllergies_NKA(){
        val allergies = listOf(CommonAllergies.NKA.dataString)
        info.allergies = listOf("Penicillin", "Other Med")
        Assert.assertEquals(2, info.allergies.size)
        handle(buildAddRemoveAllergyListCommand(allergies, listOf()))
        Assert.assertEquals(1, info.allergies.size)
        Assert.assertEquals(CommonAllergies.NKA.dataString, info.allergies[0])
    }
    //endregion

    @Test
    fun testChangeMaskingCommand_Add(){
        val maskReason = "Reason"
        Assert.assertNull(info.maskingJustification)
        handle(buildPatientMaskingCommand(true, maskReason))
        Assert.assertEquals(maskReason, info.maskingJustification)
    }

    @Test
    fun testChangeMaskingCommand_Remove(){
        val maskReason = "Reason"
        info.maskingJustification = maskReason
        Assert.assertNotNull(info.maskingJustification)
        handle(buildPatientMaskingCommand(false, "Removal Reason"))
        Assert.assertNull(info.maskingJustification)
    }

    //region Medication Tests
    @Test
    fun testAddMedications(){
        val medications = listOf("Opiates", "Penicillin", "A3")
        Assert.assertNotEquals(medications.size, info.medications.size)
        handle(buildAddRemoveMedicationCommand(medications, listOf()))
        Assert.assertEquals(medications.size, info.medications.size)
    }

    @Test
    fun testRemoveMedications(){
        val medications = listOf("Opiates", "Penicillin", "A3")
        info.medications = medications
        Assert.assertEquals(medications.size, info.medications.size)
        handle(buildAddRemoveMedicationCommand(listOf(), medications))
        Assert.assertEquals(0, info.medications.size)
    }

    @Test
    fun testAddAndRemoveMedications(){
        val medications = listOf("Opiates", "A2", "A3")
        val medicationsToRemove = listOf("Penicillin")
        info.medications = medicationsToRemove
        Assert.assertEquals(medicationsToRemove.size, info.medications.size)
        handle(buildAddRemoveMedicationCommand(medications, medicationsToRemove))
        Assert.assertEquals(medications.size, info.medications.size)
    }
    //endregion

    @Test
    fun testChangePatientId(){
        val newPatientId = DomainId.create<DocumentId>() //It would be a PatientId, but that doesn't exist here
        Assert.assertNotEquals(newPatientId, info.patientId)
        handle(buildChangePatientIdCommand(newPatientId))
        Assert.assertEquals(newPatientId, info.patientId)
    }

    @Test
    fun testAddImmunizations(){
        val immunization = "Flu Vaccine 2ml"
        Assert.assertNotEquals(immunization, info.immunizations)
        Assert.assertEquals(0, info.immunizations.size)
        handle(buildUpdateImmunizationCommand(listOf(Immunization("Flu Vaccine", "ml", 2f, Instant.now().truncatedTo(ChronoUnit.SECONDS))), listOf()), )
        Assert.assertEquals(1, info.immunizations.size)
        Assert.assertEquals("Flu Vaccine", info.immunizations[0].name)
        Assert.assertEquals("ml", info.immunizations[0].unit)
        Assert.assertEquals(2.0f, info.immunizations[0].volume)
        Assert.assertEquals(Instant.now().truncatedTo(ChronoUnit.SECONDS), info.immunizations[0].date?.truncatedTo(ChronoUnit.SECONDS))
    }

    @Test
    fun testRemoveImmunizations(){
        val immunization = "Flu Vaccine 2ml"
        Assert.assertNotEquals(immunization, info.immunizations)
        Assert.assertEquals(0, info.immunizations.size)
        handle(buildUpdateImmunizationCommand(listOf(Immunization("Flu Vaccine", "ml", 2f, Instant.now().truncatedTo(ChronoUnit.SECONDS))), listOf()), )
        Assert.assertEquals(1, info.immunizations.size)
        Assert.assertEquals("Flu Vaccine", info.immunizations[0].name)
        Assert.assertEquals("ml", info.immunizations[0].unit)
        Assert.assertEquals(2.0f, info.immunizations[0].volume)
        Assert.assertEquals(Instant.now().truncatedTo(ChronoUnit.SECONDS), info.immunizations[0].date?.truncatedTo(ChronoUnit.SECONDS))
        handle(buildUpdateImmunizationCommand(listOf(), listOf(Immunization("Flu Vaccine", "ml", 2f, Instant.now().truncatedTo(ChronoUnit.SECONDS)))))
        Assert.assertEquals(0, info.immunizations.size)
    }

    @Test
    fun testAddProcedures(){
        val procedure = "Applied tourniquet due to gunshot wound"
        Assert.assertNotEquals(procedure, info.procedures)
        Assert.assertEquals(0, info.procedures.size)
        handle(buildUpdateProceduresCommand(listOf(Procedure(procedure, Instant.now().truncatedTo(ChronoUnit.SECONDS))), listOf()))
        Assert.assertEquals(1, info.procedures.size)
        Assert.assertEquals(procedure, info.procedures[0].name)
        Assert.assertEquals(Instant.now().truncatedTo(ChronoUnit.SECONDS), info.procedures[0].date?.truncatedTo(ChronoUnit.SECONDS))
    }

    @Test
    fun testRemovedProcedures(){
        val procedure = "Applied tourniquet due to gunshot wound"
        Assert.assertNotEquals(procedure, info.procedures)
        Assert.assertEquals(0, info.procedures.size)
        handle(buildUpdateProceduresCommand(listOf(Procedure(procedure, Instant.now().truncatedTo(ChronoUnit.SECONDS))), listOf()))
        Assert.assertEquals(1, info.procedures.size)
        Assert.assertEquals(procedure, info.procedures[0].name)
        Assert.assertEquals(Instant.now().truncatedTo(ChronoUnit.SECONDS), info.procedures[0].date?.truncatedTo(ChronoUnit.SECONDS))
        handle(buildUpdateProceduresCommand(listOf(), listOf(Procedure(procedure, Instant.now().truncatedTo(ChronoUnit.SECONDS)))))
        Assert.assertEquals(0, info.procedures.size)
    }

    @Test
    fun testProblems(){
        val problem1Id = DomainId.create<ProblemId>()
        val problem2Id = DomainId.create<ProblemId>()
        val problemInstant1 = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val problemListAdd = listOf(
            Problem("Problem 1", ProblemStatus.ACTIVE.dataString, problemInstant1, problem1Id),
            Problem("Problem 2", "PROPOSED", Instant.now(), problem2Id)
        )

        Assert.assertTrue(info.problems.isEmpty())
        handle(buildUpdateProblemsCommand(problemListAdd, null))
        Assert.assertEquals(2, info.problems.size)

        handle(buildUpdateProblemsCommand(null, listOf(problem2Id)))
        Assert.assertEquals(1, info.problems.size)
        Assert.assertEquals(problem1Id, info.problems.first().id)
        Assert.assertEquals(problemInstant1, info.problems.first().date)
        Assert.assertEquals(ProblemStatus.ACTIVE.dataString,  info.problems.first().status)
    }
}