package gov.afrl.batdok.encounter

import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test

class LinkableTest {
    lateinit var classUnderTest: ActualLinkableForTesting
    @Before
    fun setup(){
        classUnderTest = ActualLinkableForTesting(DomainId.create())
    }

    @Test
    fun getLinks_returnsDistinctListOfIDSFromLinks(){
        //ARRANGE
        val otherId1: DomainId = DomainId.create()
        val otherId2: DomainId = DomainId.create()
        val otherId3: DomainId = DomainId.create()
        val link1 = Link(listOf(otherId1, otherId2, otherId3, classUnderTest.id))
        val link2 = Link(listOf(otherId1, otherId3, classUnderTest.id))
        val link3 = Link(listOf(otherId1, otherId2, classUnderTest.id))
        val links = Links()
        links += link1
        links += link2
        links += link3
        //ACT
        val result = classUnderTest.getLinks(links)
        //ASSERT
        assertEquals(4, result.size)
        assertTrue(result.contains(otherId1))
        assertTrue(result.contains(otherId2))
        assertTrue(result.contains(otherId3))
        assertTrue(result.contains(classUnderTest.id))
    }
}

class ActualLinkableForTesting(override val id: DomainId) : Linkable