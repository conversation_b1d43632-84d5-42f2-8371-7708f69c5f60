package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.InputOutputs.Companion.includeInputOutputEvents
import gov.afrl.batdok.encounter.IntakeOutputs.Companion.includeIntakeOutputEvents
import gov.afrl.batdok.encounter.commands.buildAddIOItemCommand
import gov.afrl.batdok.encounter.commands.buildAddIntakeOutputItemCommand
import gov.afrl.batdok.encounter.commands.buildRemoveIOItem
import gov.afrl.batdok.encounter.commands.buildRemoveIntakeOutputItem
import gov.afrl.batdok.encounter.commands.buildUpdateIOItemCommand
import gov.afrl.batdok.encounter.commands.buildUpdateIntakeOutputItemCommand
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.encounter.ids.InputOutputId
import gov.afrl.batdok.encounter.ids.IntakeOutputId
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class IntakeOutputEventTest {

    val events = Events()
    val intakeOutputs = IntakeOutputs()

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS), commandId: CommandId = DomainId.create()) {
        EventCommandHandler(events) {
            includeIntakeOutputEvents(intakeOutputs)
        }.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp, commandId))
    }

    @Test
    fun testAddIOItem_input(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val intakeId = DomainId.create<IntakeOutputId>()
        val item = IntakeOutput(intakeId, timestamp, IntakeOutputType.ORAL.dataString, true, "Route", 1.0, "Unit")
        Assert.assertEquals(0, events.list.size)
        handle(buildAddIntakeOutputItemCommand(item))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Logged Intake:\nOral Fluid 1.0 Unit Route", events.list[0].event)
        Assert.assertEquals(timestamp, events.list[0].timestamp)
    }

    @Test
    fun testAddIOItem_output(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val itemId = DomainId.create<IntakeOutputId>()
        val item = IntakeOutput(
            itemId,
            timestamp,
            IntakeOutputType.URINE.dataString,
            false,
            "label",
            1.0,
            IntakeOutputUnit.CC.dataString
        )
        Assert.assertEquals(0, events.list.size)
        handle(buildAddIntakeOutputItemCommand(item))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Logged Output:\nUrine 1.0 cc label", events.list[0].event)
        Assert.assertEquals(timestamp, events.list[0].timestamp)
    }

    @Test
    fun testUpdateIOItem_input(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val itemId = DomainId.create<IntakeOutputId>()
        val item = IntakeOutput(
            itemId,
            timestamp,
            IntakeOutputType.ORAL.dataString,
            true,
            "label",
            1.0,
            IntakeOutputUnit.CC.dataString
        )
        intakeOutputs += IntakeOutput(
            itemId,
            timestamp,
            IntakeOutputType.ORAL.dataString,
            true,
            "label",
            2.0,
            IntakeOutputUnit.CC.dataString
        )
        events += Event("Old Event", itemId.copy(), timestamp.minusSeconds(1000))
        Assert.assertEquals(1, events.list.size)
        handle(buildUpdateIntakeOutputItemCommand(item, true))
        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals("Updated Intake:\nOral Fluid 1.0 cc label", events.list[1].event)
        Assert.assertEquals(timestamp, events.list[1].timestamp)
    }

    @Test
    fun testUpdateIOItem_output(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val itemId = DomainId.create<IntakeOutputId>()
        val item = IntakeOutput(
            itemId,
            timestamp,
            IntakeOutputType.URINE.dataString,
            false,
            "label",
            1.0,
            IntakeOutputUnit.CC.dataString
        )
        IntakeOutput(
            itemId,
            timestamp,
            IntakeOutputType.URINE.dataString,
            false,
            "label",
            2.0,
            IntakeOutputUnit.CC.dataString
        )
        events += Event("Old Event", itemId.copy(), timestamp.minusSeconds(1000))
        Assert.assertEquals(1, events.list.size)
        handle(buildUpdateIntakeOutputItemCommand(item, false))
        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals("Updated Output:\nUrine 1.0 cc label", events.list[1].event)
        Assert.assertEquals(timestamp, events.list[1].timestamp)
        Assert.assertEquals(itemId, events.list[1].referencedItem)
    }

    @Test
    fun testRemoveIOItem_input(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)

        val itemId = DomainId.create<IntakeOutputId>()
        intakeOutputs += IntakeOutput(
            itemId,
            timestamp,
            IntakeOutputType.ORAL.dataString,
            true,
            "label",
            2.0,
            IntakeOutputUnit.CC.dataString
        )
        events += Event("Old Event", itemId.copy(), Instant.now().truncatedTo(ChronoUnit.SECONDS))
        Assert.assertEquals(1, events.list.size)
        handle(buildRemoveIntakeOutputItem(itemId))
        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals("Removed Intake:\nOral Fluid 2.0 cc label", events.list[1].event)
        Assert.assertEquals(itemId, events.list[1].referencedItem)
    }

    @Test
    fun testRemoveIOItem_output(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)

        val itemId = DomainId.create<IntakeOutputId>()
        intakeOutputs += IntakeOutput(
            itemId,
            timestamp,
            IntakeOutputType.URINE.dataString,
            false,
            "label",
            2.0,
            IntakeOutputUnit.CC.dataString
        )
        events += Event("Old Event", itemId.copy(), Instant.now().truncatedTo(ChronoUnit.SECONDS))
        Assert.assertEquals(1, events.list.size)
        handle(buildRemoveIntakeOutputItem(itemId))
        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals("Removed Output:\nUrine 2.0 cc label", events.list[1].event)
        Assert.assertEquals(itemId, events.list[1].referencedItem)
    }
}