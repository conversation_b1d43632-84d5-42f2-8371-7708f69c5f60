package gov.afrl.batdok.encounter.treatments

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.Treatments
import gov.afrl.batdok.encounter.commands.buildRemoveTreatmentCommand
import gov.afrl.batdok.encounter.ids.TreatmentId
import gov.afrl.batdok.encounter.treatment.CommonTreatments
import gov.afrl.batdok.encounter.treatment.Treatment
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class RemoveTreatmentStateTest {

    val treatments = Treatments()

    //region Helper Functions
    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS)) {
        treatments.handlers.handle(
            DomainId.nil(),
            buildCommandData(subCommand, callsign, timestamp)
        )
    }
    //endregion

    @Test
    fun testRemoveTreatmentCommand_errorRemoval() {
        //Setup variables
        val id = DomainId.create<TreatmentId>()
        //Add Previous Event
        treatments += Treatment(
            CommonTreatments.SPLINT.dataString,
            id = id,
        )

        //Make sure the previous event exists
        Assert.assertEquals(1, treatments.list.size)
        //Handle Command
        handle(buildRemoveTreatmentCommand(id, true))
        //Make sure that still only 1 event exists
        Assert.assertEquals(0, treatments.list.size)
        Assert.assertEquals(1, treatments.onErrorRemovedItems.size)
        Assert.assertEquals(1, treatments.onErrorRemovedItems.size)
        Assert.assertEquals(0, treatments.onProperRemovedItems.size)
    }

    @Test
    fun testRemoveTreatmentCommand_properRemoval() {
        //Setup variables
        val id = DomainId.create<TreatmentId>()
        //Add Previous Event
        treatments += Treatment(CommonTreatments.SPLINT.dataString, id = id)

        //Make sure the previous event exists
        Assert.assertEquals(1, treatments.list.size)
        //Handle Command
        handle(buildRemoveTreatmentCommand(id, false))
        //Make sure that still only 1 event exists
        Assert.assertEquals(0, treatments.list.size)
        Assert.assertEquals(1, treatments.onProperRemovedItems.size)
        Assert.assertEquals(0, treatments.onErrorRemovedItems.size)
        Assert.assertEquals(1, treatments.onProperRemovedItems.size)
    }
}