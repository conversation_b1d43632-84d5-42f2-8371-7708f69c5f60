package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.commands.*
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.encounter.movement.*
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.LocalDate
import java.time.temporal.ChronoUnit

class MovementStateTest {

    private val movement = Movement()

    private fun handle(
        subCommand: Message,
        callsign: String = "CS",
        timestamp: Instant = Instant.now().truncatedTo(
            ChronoUnit.SECONDS
        ),
        commandId: CommandId = DomainId.create()
    ) {
        movement.handlers.handle(
            DomainId.nil(),
            buildCommandData(subCommand, callsign, timestamp, commandId)
        )
    }

    @Test
    fun testChangeMedMissionNumber() {
        val newMedMissionNumber = MedMissionNumber(otherText = "Med123")
        Assert.assertNotEquals(newMedMissionNumber, movement.medMissionNumber)
        handle(buildMedMissionNumberCommand(newMedMissionNumber))
        Assert.assertEquals(newMedMissionNumber.toString(), movement.medMissionNumber?.toString())
    }

    @Test
    fun testChangeMedMissionNumber_paren() {
        val newMedMissionNumber = MedMissionNumber("test", "Med123")
        Assert.assertNotEquals(newMedMissionNumber, movement.medMissionNumber)
        handle(buildMedMissionNumberCommand(newMedMissionNumber))
        Assert.assertEquals(newMedMissionNumber.toString(), movement.medMissionNumber?.toString())
    }

    @Test
    fun testChangeTailToTail() {
        val newTailToTail = true
        Assert.assertNotEquals(newTailToTail, movement.tailToTail)
        handle(buildChangeTailToTailCommand(newTailToTail))
        Assert.assertEquals(newTailToTail, movement.tailToTail)
    }

    @Test
    fun testChangeLegNumber() {
        val newLegNumber = 2
        val newTotalLeg = 2
        Assert.assertNotEquals(newLegNumber, movement.legNumber)
        Assert.assertNotEquals(newTotalLeg, movement.totalLegs)
        handle(buildChangeLegNumberCommand(newLegNumber, newTotalLeg))
        Assert.assertEquals(newLegNumber, movement.legNumber)
        Assert.assertEquals(newLegNumber, movement.totalLegs)
    }

    @Test
    fun testChangeNinelineTime() {
        val newNinelineTime = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        Assert.assertNotEquals(newNinelineTime, movement.ninelineTime)
        handle(buildChangeNinelineTimeCommand(newNinelineTime))
        Assert.assertEquals(newNinelineTime, movement.ninelineTime)
    }

    @Test
    fun testChangeDispatchedEvac_Enum(){
        val newEvacCategory = EvacStatus.URGENTSURGICAL
        Assert.assertNotEquals(newEvacCategory.dataString, movement.dispatchEvac)
        handle(buildUpdateDispatchedEvacCommand(newEvacCategory))
        Assert.assertEquals(newEvacCategory.dataString, movement.dispatchEvac)
    }

    @Test
    fun testChangeDispatchEvac_String() {
        val newEvacCategory = "CategoryA"
        Assert.assertNotEquals(newEvacCategory, movement.dispatchEvac)
        handle(buildUpdateDispatchedEvacCommand(newEvacCategory))
        Assert.assertEquals(newEvacCategory, movement.dispatchEvac)
    }

    @Test
    fun testUpdateAssessedEvac_Enum(){
        val newEvacCategory = EvacStatus.URGENT
        Assert.assertNotEquals(newEvacCategory.dataString, movement.assessedEvac)
        handle(buildUpdateAssessedEvacCommand(newEvacCategory))
        Assert.assertEquals(newEvacCategory.dataString, movement.assessedEvac)
    }

    @Test
    fun testChangeAssessedEvac_String(){
        val newEvacCategory = "CategoryA"
        Assert.assertNotEquals(newEvacCategory, movement.assessedEvac)
        handle(buildUpdateAssessedEvacCommand(newEvacCategory))
        Assert.assertEquals(newEvacCategory, movement.assessedEvac)
    }

    @Test
    fun testChangeNinelinePlatform() {
        val newNinelinePlatform = "PlatformA"
        Assert.assertNotEquals(newNinelinePlatform, movement.ninelinePlatform)
        handle(buildNinelinePlatformCommand(newNinelinePlatform))
        Assert.assertEquals(newNinelinePlatform, movement.ninelinePlatform)
    }

    @Test
    fun testChangeCapability() {
        val addedCapability = listOf("CapabilityA")
        val removedCapabilities = listOf("CapabilityB", "CapabilityC")
        Assert.assertNotEquals(addedCapability, movement.capability)
        Assert.assertNotEquals(removedCapabilities, movement.capability)
        handle(buildCapabilityCommand(addedCapability, removedCapabilities))
        Assert.assertTrue(movement.capability.contains(addedCapability[0]))
        Assert.assertFalse(movement.capability.contains("CapabilityB"))
        Assert.assertFalse(movement.capability.contains("CapabilityC"))
    }

    @Test
    fun testChangeNinelineLocation_Pickup() {
        val isPickup = true
        val newNinelineLocation = NinelineLocation(
            time = Instant.now().truncatedTo(ChronoUnit.SECONDS),
            location = "LocationA",
            region = "RegionA",
            role = "RoleA"
        )
        Assert.assertNotEquals(newNinelineLocation, movement.pickupLocation)
        handle(buildNinelineLocationCommand(isPickup, newNinelineLocation))
        Assert.assertEquals(newNinelineLocation, movement.pickupLocation)
    }

    @Test
    fun testChangeNinelineLocation_Dropoff() {
        val isPickup = false
        val newNinelineLocation = NinelineLocation(
            time = Instant.now().truncatedTo(ChronoUnit.SECONDS),
            location = "LocationA",
            region = "RegionA",
            role = "RoleA"
        )
        Assert.assertNotEquals(newNinelineLocation, movement.pickupLocation)
        handle(buildNinelineLocationCommand(isPickup, newNinelineLocation))
        Assert.assertEquals(newNinelineLocation, movement.dropoffLocation)
    }

    @Test
    fun testChangeOriginatingMtf() {
        val newOriginatingMtf = "OriginatingA"
        Assert.assertNotEquals(newOriginatingMtf, movement.originatingMtf)
        handle(buildOriginatingMtfCommand(newOriginatingMtf))
        Assert.assertEquals(newOriginatingMtf, movement.originatingMtf)
    }

    @Test
    fun testChangeDestinationMtf() {
        val newDestinationMtf = "DestinationA"
        Assert.assertNotEquals(newDestinationMtf, movement.destinationMtf)
        handle(buildDestinationMtfCommand(newDestinationMtf))
        Assert.assertEquals(newDestinationMtf, movement.destinationMtf)
    }

    @Test
    fun testChangeReasonRegulated() {
        val newReasonRegulated = ReasonRegulated.BA.dataString
        Assert.assertNotEquals(newReasonRegulated, movement.reasonRegulated)
        handle(buildReasonRegulatedCommand(newReasonRegulated))
        Assert.assertEquals(newReasonRegulated, movement.reasonRegulated)
    }

    @Test
    fun testChangeMaxStops() {
        val newMaxStops = 7
        Assert.assertNotEquals(newMaxStops, movement.maxStops)
        handle(buildMaxStopsCommand(newMaxStops))
        Assert.assertEquals(newMaxStops, movement.maxStops)
    }

    @Deprecated("maxRons is deprecated, use maxNumOfRons.")
    @Test
    fun testChangeMaxRons() {
        val newMaxRons = 5
        Assert.assertNotEquals(newMaxRons, movement.maxRons)
        handle(buildMaxRonsCommand(newMaxRons))
        Assert.assertEquals(newMaxRons, movement.maxRons)
    }

    @Test
    fun testChangeMaxNumOfRons() {
        val newMaxNumOfRons = MaxNumOfRons.FIVE
        Assert.assertNotEquals(newMaxNumOfRons.dataString, movement.maxNumOfRons)
        handle(buildMaxNumberOfRonsCommand(newMaxNumOfRons))
        Assert.assertEquals(newMaxNumOfRons.dataString, movement.maxNumOfRons)
    }

    @Test
    fun testChangeMaxNumOfRons_Clear() {
        movement.maxNumOfRons = MaxNumOfRons.FIVE.dataString
        Assert.assertNotNull(movement.maxNumOfRons)
        handle(buildMaxNumberOfRonsCommand(null))
        Assert.assertNull(movement.maxNumOfRons)
    }

    @Test
    fun testChangeAltitudeRestrictions() {
        val newAltitudeRestrictions = "AltitudeRestrictionsA"
        Assert.assertNotEquals(newAltitudeRestrictions, movement.altitudeRestrictions)
        handle(buildAltitudeRestrictionsCommand(newAltitudeRestrictions))
        Assert.assertEquals(newAltitudeRestrictions, movement.altitudeRestrictions)
    }

    @Test
    fun testChangeFlightLevel() {
        val newFlightLevel = "FlightLevelA"
        Assert.assertNotEquals(newFlightLevel, movement.flightLevel)
        handle(buildFlightLevelCommand(newFlightLevel))
        Assert.assertEquals(newFlightLevel, movement.flightLevel)
    }

    @Test
    fun testChangeReadyDate() {
        val newReadyDate = Instant.now().plusSeconds(4)
        Assert.assertNotEquals(newReadyDate, movement.readyDate)
        handle(buildChangeReadyDateCommand(newReadyDate))
        Assert.assertEquals(newReadyDate.epochSecond, movement.readyDate?.epochSecond)
    }

    @Test
    fun testChangeLocalReadyDate() {
        Assert.assertNull(movement.localReadyDate)
        val date = LocalDate.now()
        Assert.assertNotEquals(date, movement.localReadyDate)
        handle(buildUpdateReadyDateCommand(date))
        Assert.assertEquals(date, movement.localReadyDate)
    }

    @Test
    fun testChangeMedicalAttendantsNeeded() {
        val newMedicalAttendantsNeeded = 3
        Assert.assertNotEquals(newMedicalAttendantsNeeded, movement.medicalAttendantsNeeded)
        handle(buildChangeMedicalAttendantsNeededCommand(newMedicalAttendantsNeeded))
        Assert.assertEquals(newMedicalAttendantsNeeded, movement.medicalAttendantsNeeded)
    }

    @Test
    fun testChangeMedicalAttendantsNeeded_Clear() {
        movement.medicalAttendantsNeeded = 3
        Assert.assertNotNull(movement.medicalAttendantsNeeded)
        handle(buildChangeMedicalAttendantsNeededCommand(null))
        Assert.assertNull(movement.medicalAttendantsNeeded)
    }

    @Test
    fun testChangeNonMedicalAttendantsNeeded() {
        val newNonMedicalAttendantsNeeded = 3
        Assert.assertNotEquals(newNonMedicalAttendantsNeeded, movement.nonMedicalAttendantsNeeded)
        handle(buildChangeNonMedicalAttendantsNeededCommand(newNonMedicalAttendantsNeeded))
        Assert.assertEquals(newNonMedicalAttendantsNeeded, movement.nonMedicalAttendantsNeeded)
    }

    @Test
    fun testChangeNonMedicalAttendantsNeeded_Clear() {
        movement.nonMedicalAttendantsNeeded = 3
        Assert.assertNotNull(movement.nonMedicalAttendantsNeeded)
        handle(buildChangeNonMedicalAttendantsNeededCommand(null))
        Assert.assertNull(movement.nonMedicalAttendantsNeeded)
    }

    @Test
    fun testChangeAttendants() {
        val newAttendants = listOf(
            Attendant(
                Name("John Middle Doe"),
                "Male",
                175.2f,
                "7th",
                true
            )
        )
        Assert.assertNotEquals(newAttendants.count(), movement.attendants.count())
        handle(buildAttendantCommand(newAttendants, listOf()))
        Assert.assertEquals(newAttendants[0].name?.first, movement.attendants[0].name?.first)
        Assert.assertEquals(newAttendants[0].name?.middle, movement.attendants[0].name?.middle)
        Assert.assertEquals(newAttendants[0].name?.last, movement.attendants[0].name?.last)
        Assert.assertEquals(newAttendants[0].gender, movement.attendants[0].gender)
        Assert.assertEquals(newAttendants[0].weight, movement.attendants[0].weight)
        Assert.assertEquals(newAttendants[0].grade, movement.attendants[0].grade)
        Assert.assertEquals(newAttendants[0].isMedical, movement.attendants[0].isMedical)

        Assert.assertEquals(1, movement.getMedicalAttendants())
        Assert.assertEquals(0, movement.getNonMedicalAttendants())
    }

    @Test
    fun testChangeClassification_String() {
        val newClassification = "ClassificationA"
        Assert.assertNotEquals(newClassification, movement.classification)
        handle(buildClassificationCommand(newClassification))
        Assert.assertEquals(newClassification, movement.classification)
    }

    @Test
    fun testChangeClassification_Enum() {
        val newClassification = Classification.FIVE_C
        Assert.assertNotEquals(newClassification.dataString, movement.classification)
        handle(buildClassificationCommand(newClassification))
        Assert.assertEquals(newClassification.dataString, movement.classification)
    }

    @Test
    fun testChangePrecedence() {
        val newPrecedence = "PrecendenceA"
        Assert.assertNotEquals(newPrecedence, movement.precedence)
        handle(buildPrecedenceCommand(newPrecedence))
        Assert.assertEquals(newPrecedence, movement.precedence)
    }

    @Test
    fun testChangeCriticalCare() {
        val newCriticalCare = true
        val removeCriticalCare = false
        val nullCriticalCare = null
        Assert.assertNotEquals(newCriticalCare, movement.criticalCare)
        handle(buildCriticalCareCommand(newCriticalCare))
        Assert.assertEquals(newCriticalCare, movement.criticalCare)
        handle(buildCriticalCareCommand(removeCriticalCare))
        Assert.assertNotEquals(newCriticalCare, movement.criticalCare)
        Assert.assertEquals(removeCriticalCare, movement.criticalCare)
        handle(buildCriticalCareCommand(nullCriticalCare))
        Assert.assertNotEquals(newCriticalCare, movement.criticalCare)
        Assert.assertNotEquals(removeCriticalCare, movement.criticalCare)
        Assert.assertEquals(nullCriticalCare, movement.criticalCare)
    }

    @Test
    fun testChangeAcceptingPhysicianCommand() {
        val physician = Physician(
            "John Doe",
            "**************",
            "j.doe@unknown"
        )
        Assert.assertNull(movement.acceptingPhysician.name)
        Assert.assertNull(movement.acceptingPhysician.phone)
        Assert.assertNull(movement.acceptingPhysician.email)
        handle(buildChangeAcceptingPhysicianCommand(physician))
        Assert.assertEquals(physician.name, movement.acceptingPhysician.name)
        Assert.assertEquals(physician.phone, movement.acceptingPhysician.phone)
        Assert.assertEquals(physician.email, movement.acceptingPhysician.email)
    }

    @Test
    fun testChangeAcceptingPhysicianCommand_NullAndBlank() {
        val physician = Physician(
            "John Doe",
            null,
            "     "
        )
        Assert.assertNull(movement.acceptingPhysician.name)
        Assert.assertNull(movement.acceptingPhysician.phone)
        Assert.assertNull(movement.acceptingPhysician.email)
        handle(buildChangeAcceptingPhysicianCommand(physician))
        Assert.assertEquals(physician.name, movement.acceptingPhysician.name)
        Assert.assertNull(movement.acceptingPhysician.phone)
        Assert.assertNull(movement.acceptingPhysician.email)
    }

    @Test
    fun testChangeOriginPhoneCommand_notBlank() {
        val expected = "**************"
        Assert.assertNull(movement.originPhone)
        handle(buildChangeOriginPhoneCommand(expected))
        Assert.assertEquals(expected, movement.originPhone)
    }

    @Test
    fun testChangeOriginPhoneCommand_isBlank() {
        movement.originPhone = "123-4567"
        val expected = "     "
        Assert.assertNotNull(movement.originPhone)
        handle(buildChangeOriginPhoneCommand(expected))
        Assert.assertNull(movement.originPhone)
    }

    @Test
    fun testChangeOriginPhoneCommand_isNull() {
        movement.originPhone = "123-4567"
        Assert.assertNotNull(movement.originPhone)
        handle(buildChangeOriginPhoneCommand(null))
        Assert.assertNull(movement.originPhone)
    }

    @Test
    fun testChangeOriginPhoneNumbersCommand_DSNAndComm() {
        val expectedComm = "**************"
        val expectedDSN = "123-4567"
        Assert.assertNull(movement.originPhoneNumbers.dsn)
        Assert.assertNull(movement.originPhoneNumbers.comm)
        handle(
            buildChangeOriginPhoneNumbersCommand(
                PhoneNumbers(
                    dsn = expectedDSN,
                    comm = expectedComm
                )
            )
        )
        Assert.assertEquals(expectedComm, movement.originPhoneNumbers.comm)
        Assert.assertEquals(expectedDSN, movement.originPhoneNumbers.dsn)
    }

    @Test
    fun testChangeOriginPhoneNumbersCommand_DSNOnly() {
        val expectedDSN = "123-4567"
        Assert.assertNull(movement.originPhoneNumbers.dsn)
        handle(buildChangeOriginPhoneNumbersCommand(PhoneNumbers(dsn = expectedDSN)))
        Assert.assertNull(movement.originPhoneNumbers.comm)
        Assert.assertEquals(expectedDSN, movement.originPhoneNumbers.dsn)
    }

    @Test
    fun testChangeOriginPhoneNumbersCommand_CommOnly() {
        val expectedComm = "**************"
        Assert.assertNull(movement.originPhoneNumbers.dsn)
        Assert.assertNull(movement.originPhoneNumbers.comm)
        handle(buildChangeOriginPhoneNumbersCommand(PhoneNumbers(comm = expectedComm)))
        Assert.assertEquals(expectedComm, movement.originPhoneNumbers.comm)
        Assert.assertNull(movement.originPhoneNumbers.dsn)
    }

    @Test
    fun testChangeOriginPhoneNumbersCommand_FieldsAreBlank() {
        val expectedComm = ""
        val expectedDSN = ""
        Assert.assertNull(movement.originPhoneNumbers.dsn)
        Assert.assertNull(movement.originPhoneNumbers.comm)
        handle(
            buildChangeOriginPhoneNumbersCommand(
                PhoneNumbers(
                    dsn = expectedDSN,
                    comm = expectedComm
                )
            )
        )
        Assert.assertEquals(expectedComm, movement.originPhoneNumbers.comm)
        Assert.assertEquals(expectedDSN, movement.originPhoneNumbers.dsn)
    }

    @Test
    fun testChangeOriginPhoneNumbersCommand_FieldsAreNull() {
        Assert.assertNull(movement.originPhoneNumbers.dsn)
        Assert.assertNull(movement.originPhoneNumbers.comm)
        handle(buildChangeOriginPhoneNumbersCommand(PhoneNumbers(dsn = null, comm = null)))
        Assert.assertNull(movement.originPhoneNumbers.comm)
        Assert.assertNull(movement.originPhoneNumbers.dsn)
    }

    @Test
    fun testChangeDestinationPhoneCommand_notBlank() {
        val expected = "**************"
        Assert.assertNull(movement.destinationPhone)
        handle(buildChangeDestinationPhoneCommand(expected))
        Assert.assertEquals(expected, movement.destinationPhone)
    }

    @Test
    fun testChangeDestinationPhoneCommand_isBlank() {
        movement.destinationPhone = "123-4567"
        val expected = "     "
        Assert.assertNotNull(movement.destinationPhone)
        handle(buildChangeDestinationPhoneCommand(expected))
        Assert.assertNull(movement.destinationPhone)
    }

    @Test
    fun testChangeDestinationPhoneCommand_isNull() {
        movement.destinationPhone = "123-4567"
        Assert.assertNotNull(movement.destinationPhone)
        handle(buildChangeDestinationPhoneCommand(null))
        Assert.assertNull(movement.destinationPhone)
    }

    @Test
    fun testChangeDestinationPhoneNumbersCommand_DSNAndComm() {
        val expectedComm = "**************"
        val expectedDSN = "123-4567"
        Assert.assertNull(movement.destinationPhoneNumbers.dsn)
        Assert.assertNull(movement.destinationPhoneNumbers.comm)
        handle(
            buildChangeDestinationPhoneNumbersCommand(
                PhoneNumbers(
                    dsn = expectedDSN,
                    comm = expectedComm
                )
            )
        )
        Assert.assertEquals(expectedComm, movement.destinationPhoneNumbers.comm)
        Assert.assertEquals(expectedDSN, movement.destinationPhoneNumbers.dsn)
    }

    @Test
    fun testChangeDestinationPhoneNumbersCommand_DSNOnly() {
        val expectedDSN = "123-4567"
        Assert.assertNull(movement.destinationPhoneNumbers.dsn)
        handle(buildChangeDestinationPhoneNumbersCommand(PhoneNumbers(dsn = expectedDSN)))
        Assert.assertNull(movement.destinationPhoneNumbers.comm)
        Assert.assertEquals(expectedDSN, movement.destinationPhoneNumbers.dsn)
    }

    @Test
    fun testChangeDestinationPhoneNumbersCommand_CommOnly() {
        val expectedComm = "**************"
        Assert.assertNull(movement.destinationPhoneNumbers.dsn)
        Assert.assertNull(movement.destinationPhoneNumbers.comm)
        handle(buildChangeDestinationPhoneNumbersCommand(PhoneNumbers(comm = expectedComm)))
        Assert.assertEquals(expectedComm, movement.destinationPhoneNumbers.comm)
        Assert.assertNull(movement.destinationPhoneNumbers.dsn)
    }

    @Test
    fun testChangeDestinationPhoneNumbersCommand_FieldsAreBlank() {
        val expectedComm = ""
        val expectedDSN = ""
        Assert.assertNull(movement.destinationPhoneNumbers.dsn)
        Assert.assertNull(movement.destinationPhoneNumbers.comm)
        handle(
            buildChangeDestinationPhoneNumbersCommand(
                PhoneNumbers(
                    dsn = expectedDSN,
                    comm = expectedComm
                )
            )
        )
        Assert.assertEquals(expectedComm, movement.destinationPhoneNumbers.comm)
        Assert.assertEquals(expectedDSN, movement.destinationPhoneNumbers.dsn)
    }

    @Test
    fun testChangeDestinationPhoneNumbersCommand_FieldsAreNull() {
        Assert.assertNull(movement.destinationPhoneNumbers.dsn)
        Assert.assertNull(movement.destinationPhoneNumbers.comm)
        handle(buildChangeDestinationPhoneNumbersCommand(PhoneNumbers(dsn = null, comm = null)))
        Assert.assertNull(movement.destinationPhoneNumbers.comm)
        Assert.assertNull(movement.destinationPhoneNumbers.dsn)
    }

    @Test
    fun testAddWaivers() {
        val waivers = listOf("Waiver1", "Waiver2", "Waiver3")
        Assert.assertNotEquals(waivers.size, movement.waivers.size)
        handle(buildAddRemoveWaiversCommand(waivers, listOf()))
        Assert.assertEquals(waivers.size, movement.waivers.size)
        waivers.map {
            Assert.assertTrue(movement.waivers.contains(it))
        }
    }

    @Test
    fun testRemoveWaivers() {
        val waivers = listOf("Waiver1", "Waiver2", "Waiver3")
        movement.waivers = waivers
        Assert.assertEquals(waivers.size, movement.waivers.size)
        handle(buildAddRemoveWaiversCommand(listOf(), waivers))
        Assert.assertEquals(0, movement.waivers.size)
    }

    @Test
    fun testAddAndRemoveWaivers() {
        val waivers = listOf("Waiver1", "Waiver2", "Waiver3")
        val waiversToRemove = listOf("Waiver4")
        movement.waivers = waiversToRemove
        Assert.assertEquals(waiversToRemove.size, movement.waivers.size)
        handle(buildAddRemoveWaiversCommand(waivers, waiversToRemove))
        Assert.assertEquals(waivers.size, movement.waivers.size)
    }

    @Test
    fun testChangeWaiversCommand_NotBlank() {
        val expected = "some waiver"
        Assert.assertNull(movement.waiversText)
        handle(buildChangeWaiversCommand(expected))
        Assert.assertEquals(expected, movement.waiversText)
    }

    @Test
    fun testChangeWaiversCommand_IsBlank() {
        Assert.assertNull(movement.waiversText)
        handle(buildChangeWaiversCommand("     "))
        Assert.assertNull(movement.waiversText)
    }

    @Test
    fun testChangeWaiversCommand_IsNull() {
        Assert.assertNull(movement.waiversText)
        handle(buildChangeWaiversCommand(null))
        Assert.assertNull(movement.waiversText)
    }

    @Test
    fun testAddAmbulatoryOrLitter(){
        Assert.assertNull(movement.ambulatoryOrLitter)
        handle(buildAmbulatoryOrLitterCommand("Not Set"))
        Assert.assertEquals("Not Set",movement.ambulatoryOrLitter)
    }

    @Test
    fun testRemoveAmbulatoryOrLitter() {
        movement.ambulatoryOrLitter = "Litter"
        Assert.assertNotNull(movement.ambulatoryOrLitter)
        handle(buildAmbulatoryOrLitterCommand(null))
        Assert.assertNull(movement.ambulatoryOrLitter)
    }

    @Test
    fun testRemoveAmbulatoryOrLitter_null() {
        Assert.assertNull(movement.ambulatoryOrLitter)
        handle(buildAmbulatoryOrLitterCommand(null))
        Assert.assertNull(movement.ambulatoryOrLitter)
    }

    @Test
    fun testClearPickupLocation() {
        movement.pickupLocation = NinelineLocation()
        Assert.assertNotNull(movement.pickupLocation)
        handle(buildClearPickupLocationCommand())
        Assert.assertNull(movement.pickupLocation)
    }

    @Test
    fun testClearPickupLocation_null() {
        Assert.assertNull(movement.pickupLocation)
        handle(buildClearPickupLocationCommand())
        Assert.assertNull(movement.pickupLocation)
    }

    @Test
    fun testClearDropoffLocation() {
        movement.dropoffLocation = NinelineLocation()
        Assert.assertNotNull(movement.dropoffLocation)
        handle(buildClearDropoffLocationCommand())
        Assert.assertNull(movement.dropoffLocation)
    }

    @Test
    fun testClearDropoffLocation_null() {
        Assert.assertNull(movement.dropoffLocation)
        handle(buildClearDropoffLocationCommand())
        Assert.assertNull(movement.dropoffLocation)
    }

}