package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.History.Companion.includeHistoryEvents
import gov.afrl.batdok.encounter.commands.buildHistoryOfPresentIllnessCommand
import gov.afrl.batdok.encounter.commands.buildLastEventTimeCommand
import gov.afrl.batdok.encounter.commands.buildPastHistoryCommand
import gov.afrl.batdok.util.Patterns
import gov.afrl.batdok.util.buildCommandData
import gov.afrl.batdok.util.format
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class HistoryEventTest {
    val events = Events()

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS)) {
        EventCommandHandler(events).apply {
            includeHistoryEvents()
        }.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp))
    }

    @Test
    fun testUpdateHpiCommand(){
        //ASSIGN
        val history = "History"

        //ACT
        handle(buildHistoryOfPresentIllnessCommand(history))

        //ASSERT
        Assert.assertEquals("Set history of present illness to $history", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.SUBJECTIVE.dataString, events.list[0].eventType)
    }

    @Test
    fun testUpdateHpiCommand_Clear_null(){
        //ASSIGN
        val history = null

        //ACT
        handle(buildHistoryOfPresentIllnessCommand(history))

        //ASSERT
        Assert.assertEquals("Cleared history of present illness", events.list[0].event)
    }

    @Test
    fun testUpdateHpiCommand_Clear_empty(){
        //ASSIGN
        val history = ""

        //ACT
        handle(buildHistoryOfPresentIllnessCommand(history))

        //ASSERT
        Assert.assertEquals("Cleared history of present illness", events.list[0].event)
    }

    @Test
    fun testUpdatePastHistory(){
        //ASSIGN
        val type = HistoryType.FAMILY.dataString
        val history = "TEST"

        //ACT
        handle(buildPastHistoryCommand(type, history))

        //ASSERT
        Assert.assertEquals("Set past family history to $history", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.SUBJECTIVE.dataString, events.list[0].eventType)
    }

    @Test
    fun testUpdatePastHistory_Clear_null(){
        //ASSIGN
        val type = HistoryType.FAMILY.dataString
        val history = null

        //ACT
        handle(buildPastHistoryCommand(type, history))

        //ASSERT
        Assert.assertEquals("Cleared past family history", events.list[0].event)
    }

    @Test
    fun testUpdatePastHistory_Clear_emptyString(){
        //ASSIGN
        val type = HistoryType.FAMILY.dataString
        val history = ""

        //ACT
        handle(buildPastHistoryCommand(type, history))

        //ASSERT
        Assert.assertEquals("Cleared past family history", events.list[0].event)
    }

    @Test
    fun testUpdateLastEvent(){
        //ASSIGN
        val type = HistoryLastEventType.BOWEL.dataString
        val date = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val formattedDate = date.format(Patterns.mdy_dash)

        //ACT
        handle(buildLastEventTimeCommand(type, date))

        //ASSERT
        Assert.assertEquals("Set Last $type to $formattedDate", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.SUBJECTIVE.dataString, events.list[0].eventType)
    }

    @Test
    fun testUpdateLastEvent_WithBloodDonationExtra(){
        //ASSIGN
        val type = HistoryLastEventType.BLOOD_DONATION.dataString
        val date = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val volume = 4
        val formattedDate = date.format(Patterns.mdy_dash)

        //ACT
        handle(buildLastEventTimeCommand(type, date, BloodDonationExtras(volume)))

        //ASSERT
        Assert.assertEquals("Set Last Blood Donation ($volume Units) to $formattedDate", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.SUBJECTIVE.dataString, events.list[0].eventType)
    }

    @Test
    fun testUpdateLastEvent_Clear_null(){
        //ASSIGN
        val type = HistoryLastEventType.BOWEL.dataString
        val date: Instant? = null

        //ACT
        handle(buildLastEventTimeCommand(type, date))

        //ASSERT
        Assert.assertEquals("Cleared Last $type", events.list[0].event)
    }
}