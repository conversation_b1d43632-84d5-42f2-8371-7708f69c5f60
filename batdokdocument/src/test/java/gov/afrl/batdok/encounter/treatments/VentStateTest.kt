package gov.afrl.batdok.encounter.treatments

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.Document
import gov.afrl.batdok.encounter.EncounterVital
import gov.afrl.batdok.encounter.VentSettings
import gov.afrl.batdok.encounter.VentValues
import gov.afrl.batdok.encounter.commands.buildLogVentCommand
import gov.afrl.batdok.encounter.commands.buildRemoveVentCommand
import gov.afrl.batdok.encounter.commands.buildUpdateVentCommand
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.encounter.vitals.EtCO2
import gov.afrl.batdok.encounter.vitals.PIP
import gov.afrl.batdok.util.buildCommandData
import gov.afrl.batdok.util.buildDocumentCommand
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class VentStateTest {

    val ventValues = VentValues()

    val testVital = EncounterVital(DomainId.create(), Instant.now(), documentId = DomainId.create()) + PIP(12) + EtCO2(13)

    //region Helper functions
    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant? = Instant.now().truncatedTo(ChronoUnit.SECONDS), commandId: CommandId = DomainId.create()) {
        ventValues.handlers.handle(
            DomainId.nil(),
            buildCommandData(subCommand, callsign, timestamp, commandId)
        )
    }

    fun VentSettings.assertEquals(otherVent: VentSettings){
        Assert.assertEquals(otherVent.time, time)
        Assert.assertEquals(otherVent.mode, mode)
        Assert.assertEquals(otherVent.fio2, fio2)
        Assert.assertEquals(otherVent.rate, rate)
        Assert.assertEquals(otherVent.tv, tv)
        Assert.assertEquals(otherVent.peep, peep)
        Assert.assertEquals(otherVent.ventilator, ventilator)
    }

    @Test
    fun testAddVent() {
        //ASSIGN
        val vent = VentSettings(
            DomainId.create(),
            Instant.now().truncatedTo(ChronoUnit.SECONDS),
            mode = "MODE",
            fio2 = 1.3,
            rate = 3,
            tv = 100,
            peep = 4.5,
            ventilator = "TEST",
            associatedVital = testVital
        )

        //ACT
        Assert.assertEquals(0, ventValues.ventSettingsList.size)
        handle(buildLogVentCommand(vent), timestamp = vent.time, commandId = vent.id.copy())

        //ASSERT
        Assert.assertEquals(1, ventValues.ventSettingsList.size)
        ventValues.ventSettingsList[0].assertEquals(vent)
    }

    @Test
    fun testAddVent_Some_Missing_Fields() {
        //ASSIGN
        val vent = VentSettings(
            DomainId.create(),
            Instant.now().truncatedTo(ChronoUnit.SECONDS),
            mode = "MODE"
        )

        //ACT
        Assert.assertEquals(0, ventValues.ventSettingsList.size)
        handle(buildLogVentCommand(vent), timestamp = vent.time, commandId = vent.id.copy())

        //ASSERT
        Assert.assertEquals(1, ventValues.ventSettingsList.size)
        ventValues.ventSettingsList[0].assertEquals(vent)
    }

    @Test
    fun testAddVent_All_Missing_Fields() {
        //ASSIGN
        val vent = VentSettings(
            DomainId.create(),
            Instant.now().truncatedTo(ChronoUnit.SECONDS)
        )

        //ACT
        Assert.assertEquals(0, ventValues.ventSettingsList.size)
        handle(buildLogVentCommand(vent), timestamp = vent.time, commandId = vent.id.copy())

        //ASSERT
        Assert.assertEquals(1, ventValues.ventSettingsList.size)
        ventValues.ventSettingsList[0].assertEquals(vent)
    }

    @Test
    fun testUpdateVent(){
        //ASSIGN
        val oldVent = VentSettings(
            DomainId.create(),
            Instant.now().truncatedTo(ChronoUnit.SECONDS).minusMillis(100_000),
            mode = "MODE2",
            fio2 = 2.6,
            rate = 9,
            tv = 394,
            peep = 6.2,
            ventilator = "OldVent",
            associatedVital = testVital
        )
        val vent = VentSettings(
            oldVent.id,
            Instant.now().truncatedTo(ChronoUnit.SECONDS),
            mode = "MODE",
            fio2 = 1.3,
            rate = 3,
            tv = 100,
            peep = 4.5,
            ventilator = "TEST",
            associatedVital = null
        )
        ventValues.ventSettingsList += oldVent

        //ACT
        Assert.assertEquals(1, ventValues.ventSettingsList.size)
        ventValues.ventSettingsList[0].assertEquals(oldVent)
        handle(buildUpdateVentCommand(vent, oldVent))

        //ASSERT
        Assert.assertEquals(1, ventValues.ventSettingsList.size)
        ventValues.ventSettingsList[0].assertEquals(vent)
    }

    @Test
    fun testUpdateVent_SameFields(){
        //ASSIGN
        val oldVent = VentSettings(
            DomainId.create(),
            Instant.now().truncatedTo(ChronoUnit.SECONDS).minusMillis(100_000),
            mode = "MODE2",
            fio2 = 2.6,
            rate = 9,
            tv = 394,
            peep = 6.2,
            ventilator = "OldVent",
            associatedVital = testVital
        )
        ventValues.ventSettingsList += oldVent

        //ACT
        Assert.assertEquals(1, ventValues.ventSettingsList.size)
        ventValues.ventSettingsList[0].assertEquals(oldVent)
        handle(buildUpdateVentCommand(oldVent, oldVent))

        //ASSERT
        Assert.assertEquals(1, ventValues.ventSettingsList.size)
        ventValues.ventSettingsList[0].assertEquals(oldVent)
    }

    @Test
    fun testUpdateVent_SomeMissingFields(){
        //ASSIGN
        val oldVent = VentSettings(
            DomainId.create(),
            Instant.now().truncatedTo(ChronoUnit.SECONDS).minusMillis(100_000),
            mode = "MODE2",
            fio2 = 2.6,
            rate = 9,
            tv = 394,
            peep = 6.2,
            ventilator = "OldVent",
            associatedVital = testVital
        )
        val vent = VentSettings(
            oldVent.id,
            Instant.now().truncatedTo(ChronoUnit.SECONDS),
            mode = "MODE"
        )
        ventValues.ventSettingsList += oldVent

        //ACT
        Assert.assertEquals(1, ventValues.ventSettingsList.size)
        ventValues.ventSettingsList[0].assertEquals(oldVent)
        handle(buildUpdateVentCommand(vent, oldVent))

        //ASSERT
        Assert.assertEquals(1, ventValues.ventSettingsList.size)
        ventValues.ventSettingsList[0].assertEquals(vent)
    }

    @Test
    fun testUpdateVent_All_Missing_Fields() {
        //ASSIGN
        val oldVent = VentSettings(
            DomainId.create(),
            Instant.now().truncatedTo(ChronoUnit.SECONDS).minusMillis(100_000),
            mode = "MODE2",
            fio2 = 2.6,
            rate = 9,
            tv = 394,
            peep = 6.2,
            ventilator = "OldVent",
            associatedVital = testVital
        )
        val vent = VentSettings(
            oldVent.id,
            Instant.now().truncatedTo(ChronoUnit.SECONDS)
        )
        ventValues.ventSettingsList += oldVent

        //ACT
        Assert.assertEquals(1, ventValues.ventSettingsList.size)
        ventValues.ventSettingsList[0].assertEquals(oldVent)
        handle(buildUpdateVentCommand(vent, oldVent))

        //ASSERT
        Assert.assertEquals(1, ventValues.ventSettingsList.size)
        ventValues.ventSettingsList[0].assertEquals(vent)
    }

    @Test
    fun testRemoveVent() {
        //ASSIGN
        val vent = VentSettings(
            DomainId.create(),
            Instant.now().truncatedTo(ChronoUnit.SECONDS)
        )
        ventValues.ventSettingsList += vent

        //ACT
        Assert.assertEquals(1, ventValues.ventSettingsList.size)
        handle(buildRemoveVentCommand(vent.id))

        //ASSERT
        Assert.assertEquals(0, ventValues.ventSettingsList.size)
    }

    @Test
    fun testVentCommandSize(){
        //ASSIGN
        val oldVent = VentSettings(
            DomainId.create(),
            Instant.now().truncatedTo(ChronoUnit.SECONDS).minusMillis(100_000),
            mode = "MODE2",
            fio2 = 2.6,
            rate = 9,
            tv = 394,
            peep = 6.2,
            ventilator = "OldVent",
            associatedVital = testVital
        )

        //ACT
        val command = buildUpdateVentCommand(oldVent)
        val minimizedCommand = buildUpdateVentCommand(oldVent, oldVent)

        //ASSERT
        //Make sure the size is actually smaller when removing the unchanged data
        println("Full Data: " + command.serializedSize)
        println("Minimized Data: " + minimizedCommand.serializedSize)
        Assert.assertNotEquals(command.serializedSize, minimizedCommand.serializedSize)
    }

    @Test
    fun testAddWithId(){
        val id = DomainId.create<DomainId>()
        val doc = Document()

        doc.handle(
            listOf(
                buildDocumentCommand(
                    DomainId.create(),
                    buildCommandData(buildLogVentCommand(VentSettings(id.copy())))
                ),
            )
        )
        Assert.assertEquals(id, doc.ventValues.ventSettingsList.first().id)
    }
}