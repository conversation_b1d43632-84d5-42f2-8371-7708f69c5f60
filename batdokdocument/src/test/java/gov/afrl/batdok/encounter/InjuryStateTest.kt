package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.commands.*
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class InjuryStateTest {

    val injuries = Injuries()

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS)) {
        injuries.handlers.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp))
    }


    @Test
    fun testChangeInjuryCommand_checked_notInShared(){
        Assert.assertEquals(0, injuries.mechanismsOfInjury.size)
        Assert.assertEquals(0, injuries.injuries.size)
        handle(buildChangeInjuryCommand(Injury.Type.AMPUTATION.dataString, true, Injury.Type.AMPUTATION.abbrev, null))

        Assert.assertEquals(0, injuries.mechanismsOfInjury.size)
        Assert.assertEquals(1, injuries.injuries.size)
        Assert.assertTrue(null in injuries.injuries)
        Assert.assertEquals(Injury.Type.AMPUTATION.dataString, injuries.injuries[null]?.get(0)?.injury)
    }

    /**
     * GIVEN an injury is in the injury list
     * WHEN we try to add the same injury to the injury list
     * THEN it should not be added to the injury list
     */
    @Test
    fun testChangeInjuryCommand_checked_alreadyExists(){
        //GIVEN
        handle(buildChangeInjuryCommand(Injury.Type.AMPUTATION.dataString, true, Injury.Type.AMPUTATION.abbrev, null))
        Assert.assertEquals(1, injuries.injuries.size)

        //WHEN
        handle(buildChangeInjuryCommand(Injury.Type.AMPUTATION.dataString, true, Injury.Type.AMPUTATION.abbrev, null))

        //THEN
        Assert.assertEquals(1, injuries.injuries.size)
    }

    @Test
    fun verifyRemovingGSWFromMoiDoesNotRemoveFromInjury(){
        //GIVEN GSW is added to both injury and moi
        injuries.mechanismsOfInjury += (null to listOf(Injury.Type.GSW.dataString))
        injuries.injuries += (null to listOf(Injury(Injury.Type.GSW.dataString, Injury.Type.GSW.abbrev), Injury(Injury.Type.GSW.dataString, Injury.Type.GSW.abbrev)))

        Assert.assertEquals(1, injuries.mechanismsOfInjury.size)
        Assert.assertEquals(1, injuries.injuries.size)

        //WHEN we remove from MOI
        handle(buildChangeMoiCommand(Injury.Type.GSW.dataString, false, null))

        //THEN it is only removed from MOI and not MOI and Injury
        Assert.assertEquals(0, injuries.mechanismsOfInjury.size)
        Assert.assertEquals(1, injuries.injuries.size)
    }

    @Test
    fun verifyRemovingGSWFromInjuryDoesNotRemoveFromMoi(){
        //GIVEN GSW is added to both injury and moi
        injuries.mechanismsOfInjury += (null to listOf(Injury.Type.GSW.dataString))
        injuries.injuries += (null to listOf(Injury(Injury.Type.GSW.dataString, Injury.Type.GSW.abbrev), Injury(Injury.Type.GSW.dataString, Injury.Type.GSW.abbrev)))

        Assert.assertEquals(1, injuries.mechanismsOfInjury.size)
        Assert.assertEquals(1, injuries.injuries.size)

        //WHEN we remove from Injury
        handle(buildChangeInjuryCommand(Injury.Type.GSW.dataString, false, "", null))

        //THEN it is only removed from MOI and not MOI and Injury
        Assert.assertEquals(1, injuries.mechanismsOfInjury.size)
        Assert.assertEquals(0, injuries.injuries.size)
    }

    @Test
    fun verifyRemovingBurnFromMoiDoesNotRemoveFromInjury(){
        //GIVEN Burn is added to both injury and moi
        injuries.mechanismsOfInjury += (null to listOf(Injury.Type.BURN.dataString))
        injuries.injuries += (null to listOf(Injury(Injury.Type.BURN.dataString, Injury.Type.BURN.abbrev), Injury(Injury.Type.BURN.dataString, Injury.Type.BURN.abbrev)))

        Assert.assertEquals(1, injuries.mechanismsOfInjury.size)
        Assert.assertEquals(1, injuries.injuries.size)

        //WHEN we remove from MOI
        handle(buildChangeMoiCommand(Injury.Type.BURN.dataString, false, null))

        //THEN it is only removed from MOI and not MOI and Injury
        Assert.assertEquals(0, injuries.mechanismsOfInjury.size)
        Assert.assertEquals(1, injuries.injuries.size)
    }

    @Test
    fun verifyRemovingBurnFromInjuryDoesNotRemoveFromMoi(){
        //GIVEN Burn is added to both injury and moi
        injuries.mechanismsOfInjury += (null to listOf(Injury.Type.BURN.dataString))
        injuries.injuries += (null to listOf(Injury(Injury.Type.BURN.dataString, Injury.Type.BURN.abbrev), Injury(Injury.Type.BURN.dataString, Injury.Type.BURN.abbrev)))

        Assert.assertEquals(1, injuries.mechanismsOfInjury.size)
        Assert.assertEquals(1, injuries.injuries.size)

        //WHEN we remove from MOI
        handle(buildChangeInjuryCommand(Injury.Type.BURN.dataString, false, "", null))

        //THEN it is only removed from MOI and not MOI and Injury
        Assert.assertEquals(1, injuries.mechanismsOfInjury.size)
        Assert.assertEquals(0, injuries.injuries.size)
    }

    @Test
    fun testChangeInjuryCommand_removeInjury_fromMultipleLocations_Null(){
        //GIVEN injury's in multiple locations with null location
        injuries.injuries += (InjuryLocation.HEAD.dataString to listOf(Injury(Injury.Type.AMPUTATION.dataString, Injury.Type.AMPUTATION.abbrev)))
        injuries.injuries += (null to listOf(Injury(Injury.Type.AMPUTATION.dataString, Injury.Type.AMPUTATION.abbrev), Injury(Injury.Type.AMPUTATION.dataString, Injury.Type.AMPUTATION.abbrev)))
        injuries.drawingPoints += DrawingPoint(Injury.Type.AMPUTATION.abbrev, 10, 10, 100, 100)

        Assert.assertEquals(2, injuries.injuries.size)
        //WHEN we remove injury and don't specify the location
        handle(buildChangeInjuryCommand(Injury.Type.AMPUTATION.dataString, false, Injury.Type.AMPUTATION.abbrev, null))
        //THEN remove from all locations
        Assert.assertEquals(0, injuries.injuries.size)
    }

    @Test
    fun testChangeInjuryCommand_removeInjury_fromMultipleLocations_NotNull(){
        //GIVEN injury's in multiple locations with null location
        injuries.mechanismsOfInjury += (InjuryLocation.HEAD.dataString to listOf(Injury.Type.GSW.dataString))
        injuries.mechanismsOfInjury += (null to listOf(Injury.Type.GSW.dataString))
        injuries.injuries += (InjuryLocation.HEAD.dataString to listOf(Injury(Injury.Type.AMPUTATION.dataString, Injury.Type.AMPUTATION.abbrev)))
        injuries.injuries += (null to listOf(Injury(Injury.Type.AMPUTATION.dataString, Injury.Type.AMPUTATION.abbrev), Injury(Injury.Type.AMPUTATION.dataString, Injury.Type.AMPUTATION.abbrev)))
        injuries.drawingPoints += DrawingPoint(Injury.Type.AMPUTATION.abbrev, 10, 10, 100, 100)

        Assert.assertEquals(2, injuries.mechanismsOfInjury.size)
        Assert.assertEquals(2, injuries.injuries.size)
        //WHEN we remove injury and don't specify the location
        handle(buildChangeInjuryCommand(Injury.Type.AMPUTATION.dataString, false, Injury.Type.AMPUTATION.abbrev,InjuryLocation.HEAD.dataString ))
        handle(buildChangeMoiCommand(Injury.Type.GSW.dataString, false, InjuryLocation.HEAD.dataString))
        //THEN remove from all locations
        Assert.assertEquals(1, injuries.mechanismsOfInjury.size)
        Assert.assertEquals(1, injuries.injuries.size)
    }

    @Test
    fun testChangeInjuryCommand_notChecked_notInShared() {
        Assert.assertEquals(0, injuries.mechanismsOfInjury.size)
        Assert.assertEquals(0, injuries.injuries.size)
        Assert.assertEquals(0, injuries.injuries.size)
        handle(buildChangeInjuryCommand(Injury.Type.AMPUTATION.dataString, false, Injury.Type.AMPUTATION.abbrev, null))
        Assert.assertEquals(0, injuries.mechanismsOfInjury.size)
        Assert.assertEquals(0, injuries.injuries.size)
        Assert.assertEquals(0, injuries.drawingPoints.size)
    }

    @Test
    fun testChangeMoiCommand_checked_notInShared(){
        // checked and is in shared list
        Assert.assertEquals(0, injuries.mechanismsOfInjury.size)
        Assert.assertEquals(0, injuries.injuries.size)
        handle(buildChangeMoiCommand(Moi.ARTY.dataString, true, null))

        Assert.assertEquals(1, injuries.mechanismsOfInjury.size)
        Assert.assertTrue(null in injuries.mechanismsOfInjury)
        Assert.assertEquals(Moi.ARTY.dataString, injuries.mechanismsOfInjury[null]?.get(0))

        Assert.assertEquals(0, injuries.injuries.size)
    }

    @Test
    fun testChangeMoiCommand_notCheck_notInShared(){
        Assert.assertEquals(0, injuries.mechanismsOfInjury.size)
        Assert.assertEquals(0, injuries.injuries.size)
        Assert.assertEquals(0, injuries.injuries.size)

        handle(buildChangeMoiCommand(Moi.AIRCRAFT_CRASH.dataString, false, null))
        Assert.assertEquals(0, injuries.mechanismsOfInjury.size)
        Assert.assertEquals(0, injuries.injuries.size)
        Assert.assertEquals(0, injuries.drawingPoints.size)
    }

    /**
     * GIVEN an moi is in the moi list
     * WHEN we try to add the same moi to the moi list
     * THEN it should not be added to the moi list
     */
    @Test
    fun testChangeMoiCommand_checked_alreadyExists(){
        // GIVEN
        handle(buildChangeMoiCommand(Moi.ARTY.dataString, true, null))
        Assert.assertEquals(1, injuries.mechanismsOfInjury[null]?.size)

        // WHEN
        handle(buildChangeMoiCommand(Moi.ARTY.dataString, true, null))

        // THEN
        Assert.assertEquals(1, injuries.mechanismsOfInjury[null]?.size)
    }

    @Test
    fun testAddPointWithLabelCommand(){
        val point = DrawingPoint("ABC", 15, 19, 100, 100)
        Assert.assertTrue(injuries.drawingPoints.isEmpty())
        handle(buildAddPointWithLabelCommand(point))
        Assert.assertTrue(injuries.drawingPoints.isNotEmpty())
        Assert.assertEquals(point.label, injuries.drawingPoints[0].label)
        Assert.assertEquals(point.x, injuries.drawingPoints[0].x)
        Assert.assertEquals(point.y, injuries.drawingPoints[0].y)
    }

    @Test
    fun testChangeTBSACommand(){
        Assert.assertNull(injuries.tbsa)
        handle(buildChangeTBSACommand(15.0))
        Assert.assertEquals(15.0, injuries.tbsa)
    }

    @Test
    fun testChangeTBSACommand_Null(){
        Assert.assertNull(injuries.tbsa)
        handle(buildChangeTBSACommand(null))
        Assert.assertEquals(null, injuries.tbsa)
    }

    @Test
    fun testChangeTBSACommand_Zero(){
        Assert.assertNull(injuries.tbsa)
        handle(buildChangeTBSACommand(0.0))
        Assert.assertEquals(0.0, injuries.tbsa)
    }

    @Test
    fun testRemoveDrawViewCommand_pointExists(){
        // checks that a DrawingPoint is removed when found in the list
        val point1 = DrawingPoint("ABC", 10, 10, 100, 100)
        injuries.drawingPoints += point1
        Assert.assertTrue(injuries.drawingPoints.isNotEmpty())
        handle(buildRemoveDrawPointCommand(point1))
        Assert.assertTrue(injuries.drawingPoints.isEmpty())
    }

    @Test
    fun testRemoveDrawViewCommand_pointDoesNotExist(){
        // checks that a DrawingPoint is not removed when not found in list
        val point1 = DrawingPoint("ABC", 10, 10, 100, 100)
        val point2 = DrawingPoint("ABC", 20, 20, 100, 100)
        injuries.drawingPoints += point1
        Assert.assertTrue(injuries.drawingPoints.isNotEmpty())
        handle(buildRemoveDrawPointCommand(point2))
        Assert.assertTrue(injuries.drawingPoints.isNotEmpty())
    }

    @Test
    fun testUndoDrawViewCommand(){
        // tests that the most recently added DrawingPoint is removed
        val point1 = DrawingPoint("ABC", 10, 10, 100, 100)
        val point2 = DrawingPoint("ABC", 20, 20, 100, 100)
        injuries.drawingPoints += point1
        injuries.drawingPoints += point2
        Assert.assertEquals(2, injuries.drawingPoints.size)
        handle(buildUndoDrawViewCommand())
        Assert.assertEquals(injuries.drawingPoints.size,1)
        Assert.assertEquals(point1.x, injuries.drawingPoints[0].x)
        Assert.assertEquals(point1.y, injuries.drawingPoints[0].y)
    }

    @Test
    fun testClearDrawViewCommand_tq(){
        // tests that DrawingPoints with "TQ" group are removed
        val tqPoint = DrawingPoint("TQ", 10, 10, 100, 100)
        val injuryPoint = DrawingPoint("INJURY", 20, 20, 100, 100)
        injuries.drawingPoints += tqPoint
        injuries.drawingPoints += injuryPoint
        Assert.assertEquals(2, injuries.drawingPoints.size)
        handle(buildClearDrawViewCommand("TQ"))
        Assert.assertEquals(injuries.drawingPoints.size,1)
        Assert.assertEquals(injuryPoint.x, injuries.drawingPoints[0].x)
        Assert.assertEquals(injuryPoint.y, injuries.drawingPoints[0].y)

        // tests that no DrawingPoints are removed when they don't have the "TQ" group
        val injuryPoint2 = DrawingPoint("INJURY", 30, 30, 100, 100)
        handle(buildAddPointWithLabelCommand(injuryPoint2))
        Assert.assertEquals(2, injuries.drawingPoints.size)
        handle(buildClearDrawViewCommand("TQ"))
        Assert.assertEquals(injuries.drawingPoints.size,2)
        Assert.assertEquals(injuryPoint.x, injuries.drawingPoints[0].x)
        Assert.assertEquals(injuryPoint.y, injuries.drawingPoints[0].y)
        Assert.assertEquals(injuryPoint2.x, injuries.drawingPoints[1].x)
        Assert.assertEquals(injuryPoint2.y, injuries.drawingPoints[1].y)

        // tests that DrawingPoints are cleared if there is no label
        handle(buildClearDrawViewCommand(null))
        Assert.assertEquals(0, injuries.drawingPoints.size)
    }

    @Test
    fun testClearDrawViewCommand_injury(){
        // tests that DrawingPoints with "TQ" group are removed
        val tqPoint = DrawingPoint("TQ", 10, 10, 100, 100)
        val injuryPoint = DrawingPoint("INJURY", 20, 20, 100, 100)
        injuries.drawingPoints += tqPoint
        injuries.drawingPoints += injuryPoint
        Assert.assertEquals(2, injuries.drawingPoints.size)
        handle(buildClearDrawViewCommand("INJURY"))
        Assert.assertEquals(injuries.drawingPoints.size,1)
        Assert.assertEquals(tqPoint.x, injuries.drawingPoints[0].x)
        Assert.assertEquals(tqPoint.y, injuries.drawingPoints[0].y)

        // tests that no DrawingPoints are removed when they don't have the "TQ" group
        val tqPoint2 = DrawingPoint("TQ", 30, 30, 100, 100)
        injuries.drawingPoints += tqPoint2
        Assert.assertEquals(2, injuries.drawingPoints.size)
        handle(buildClearDrawViewCommand("INJURY"))
        Assert.assertEquals(injuries.drawingPoints.size,2)
        Assert.assertEquals(tqPoint.x, injuries.drawingPoints[0].x)
        Assert.assertEquals(tqPoint.y, injuries.drawingPoints[0].y)
        Assert.assertEquals(tqPoint2.x, injuries.drawingPoints[1].x)
        Assert.assertEquals(tqPoint2.y, injuries.drawingPoints[1].y)

        // tests that DrawingPoints are cleared if there is no label
        handle(buildClearDrawViewCommand(null))
        Assert.assertEquals(0, injuries.drawingPoints.size)
    }

    @Test
    fun testClearInjuryDrawingsCommand_tq(){
        // tests that Drawing Points with "TQ" label are removed
        val tqPoint = DrawingPoint("TQ", 10, 10, 100, 100)
        val injuryPoint = DrawingPoint("INJURY", 20, 20, 100, 100)
        injuries.drawingPoints += tqPoint
        injuries.drawingPoints += injuryPoint
        Assert.assertEquals(2, injuries.drawingPoints.size)
        handle(buildClearInjuryDrawingsCommand("TQ"))
        Assert.assertEquals(injuries.drawingPoints.size,1)
        Assert.assertEquals(injuryPoint.x, injuries.drawingPoints[0].x)
        Assert.assertEquals(injuryPoint.y, injuries.drawingPoints[0].y)

        // tests that no DrawingPoints are removed when they don't have the "TQ" label
        val injuryPoint2 = DrawingPoint("INJURY", 30, 30, 100, 100)
        injuries.drawingPoints += injuryPoint2
        Assert.assertEquals(2, injuries.drawingPoints.size)
        handle(buildClearInjuryDrawingsCommand("TQ"))
        Assert.assertEquals(injuries.drawingPoints.size,2)
        Assert.assertEquals(injuryPoint.x, injuries.drawingPoints[0].x)
        Assert.assertEquals(injuryPoint.y, injuries.drawingPoints[0].y)
        Assert.assertEquals(injuryPoint2.x, injuries.drawingPoints[1].x)
        Assert.assertEquals(injuryPoint2.y, injuries.drawingPoints[1].y)

        // tests that DrawingPoints are cleared if there is no label
        handle(buildClearInjuryDrawingsCommand(null))
        Assert.assertEquals(0, injuries.drawingPoints.size)
    }

    @Test
    fun testClearInjuryDrawingsCommand_injury(){
        // tests that Drawing Points with "TQ" label are removed
        val tqPoint = DrawingPoint("TQ", 10, 10, 100, 100)
        val injuryPoint = DrawingPoint("INJURY", 20, 20, 100, 100)
        injuries.drawingPoints += tqPoint
        injuries.drawingPoints += injuryPoint
        Assert.assertEquals(2, injuries.drawingPoints.size)
        handle(buildClearInjuryDrawingsCommand("INJURY"))
        Assert.assertEquals(injuries.drawingPoints.size,1)
        Assert.assertEquals(tqPoint.x, injuries.drawingPoints[0].x)
        Assert.assertEquals(tqPoint.y, injuries.drawingPoints[0].y)

        // tests that no DrawingPoints are removed when they don't have the "TQ" label
        val tqPoint2 = DrawingPoint("TQ", 30, 30, 100, 100)
        injuries.drawingPoints += tqPoint2
        Assert.assertEquals(2, injuries.drawingPoints.size)
        handle(buildClearInjuryDrawingsCommand("INJURY"))
        Assert.assertEquals(injuries.drawingPoints.size,2)
        Assert.assertEquals(tqPoint.x, injuries.drawingPoints[0].x)
        Assert.assertEquals(tqPoint.y, injuries.drawingPoints[0].y)
        Assert.assertEquals(tqPoint2.x, injuries.drawingPoints[1].x)
        Assert.assertEquals(tqPoint2.y, injuries.drawingPoints[1].y)

        // tests that DrawingPoints are cleared if there is no type
        handle(buildClearInjuryDrawingsCommand(null))
        Assert.assertEquals(0, injuries.drawingPoints.size)
    }
}