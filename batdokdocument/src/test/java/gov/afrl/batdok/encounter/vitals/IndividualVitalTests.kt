package gov.afrl.batdok.encounter.vitals

import gov.afrl.batdok.commands.proto.*
import gov.afrl.batdok.util.floatValue
import gov.afrl.batdok.util.int32Value
import gov.afrl.batdok.util.stringValue
import org.junit.Assert
import org.junit.Test

class IndividualVitalTests {

    @Test
    fun testAvpu(){
        val avpu = Avpu(Avpu.Level.ALERT.dataString)
        val protobuf = avpu.toProtobuf()
        Assert.assertEquals(avpu.avpu, Avpu.Level.fromProto(protobuf.value))
        Assert.assertEquals("Alert", avpu.eventMessage)
        Assert.assertFalse(avpu.isEmpty)
        Assert.assertEquals("AVPU: Alert", avpu.toEventItem())
    }

    @Test
    fun testAvpu_Empty(){
        val avpu = Avpu(aVPU { }).produceEmptyVital()
        val protobuf = avpu.toProtobuf()
        Assert.assertEquals(null, Avpu.Level.fromProto(protobuf.value))
        Assert.assertTrue(avpu.isEmpty)
        Assert.assertNull(avpu.toEventItem())
        Assert.assertEquals("Cleared AVPU", avpu.toEventItem(true))
    }

    @Test
    fun testAvpu_ProtoConstructor(){
        val avpu = Avpu(aVPU { value = Avpu.Level.fromString("Verbal") })
        Assert.assertEquals(Avpu.Level.VERBAL.dataString, avpu.avpu)
    }

    @Test
    fun testBP(){
        val bp = BloodPressure(1, 2, "Test")
        val protobuf = bp.toProtobuf()
        Assert.assertEquals(bp.systolic, protobuf.bps.value)
        Assert.assertEquals(bp.diastolic, protobuf.bpd.value)
        Assert.assertEquals(bp.location, protobuf.location.value)
        Assert.assertEquals("1/2 Test", bp.eventMessage)
        Assert.assertFalse(bp.isEmpty)
        Assert.assertEquals("BP: 1/2 Test", bp.toEventItem())
    }

    @Test
    fun testBP_Empty(){
        val bp = BloodPressure(null, null).produceEmptyVital()
        val protobuf = bp.toProtobuf()
        Assert.assertFalse(protobuf.hasBps())
        Assert.assertFalse(protobuf.hasBpd())
        Assert.assertFalse(protobuf.hasLocation())
        Assert.assertTrue(bp.isEmpty)
        Assert.assertNull(bp.toEventItem())
        Assert.assertEquals("Cleared BP", bp.toEventItem(true))
    }

    @Test
    fun testBP_ProtoConstructor(){
        val bp = BloodPressure(bP { 
            bps = int32Value(12)
            bpd = int32Value(34)
            location = stringValue("Heart")
        })
        Assert.assertEquals(12, bp.systolic)
        Assert.assertEquals(34, bp.diastolic)
        Assert.assertEquals("Heart", bp.location)
    }
    
    @Test
    fun testCapRefill(){
        val capRefill = CapRefill(1.234f)
        val protobuf = capRefill.toProtobuf()
        Assert.assertEquals(capRefill.capRefill, protobuf.capRefill.value)
        Assert.assertEquals("1.2", capRefill.eventMessage)
        Assert.assertFalse(capRefill.isEmpty)
        Assert.assertEquals("Capillary Refill: 1.2", capRefill.toEventItem())
    }

    @Test
    fun testCapRefill_Empty(){
        val capRefill = CapRefill(null).produceEmptyVital()
        val protobuf = capRefill.toProtobuf()
        Assert.assertFalse(protobuf.hasCapRefill())
        Assert.assertTrue(capRefill.isEmpty)
        Assert.assertNull(capRefill.toEventItem())
        Assert.assertEquals("Cleared Capillary Refill", capRefill.toEventItem(true))
    }

    @Test
    fun testCapRefill_ProtoConstructor(){
        val capRefill = CapRefill(capillaryRefill {
            capRefill = floatValue(1.234f)
        })
        Assert.assertEquals(1.234f, capRefill.capRefill)
    }
    
    @Test
    fun testEtCO2(){
        val etCO2 = EtCO2(1)
        val protobuf = etCO2.toProtobuf()
        Assert.assertEquals(etCO2.etco2, protobuf.etco2.value)
        Assert.assertEquals("1", etCO2.eventMessage)
        Assert.assertFalse(etCO2.isEmpty)
        Assert.assertEquals("EtCO2: 1", etCO2.toEventItem())
    }

    @Test
    fun testEtCO2_Empty(){
        val etCO2 = EtCO2(null).produceEmptyVital()
        val protobuf = etCO2.toProtobuf()
        Assert.assertFalse(protobuf.hasEtco2())
        Assert.assertTrue(etCO2.isEmpty)
        Assert.assertNull(etCO2.toEventItem())
        Assert.assertEquals("Cleared EtCO2", etCO2.toEventItem(true))
    }

    @Test
    fun testEtco2_ProtoConstructor(){
        val etco2 = EtCO2(etCO2 {
            etco2 = int32Value(1)
        })
        Assert.assertEquals(1, etco2.etco2)
    }

    @Test
    fun testGCS(){
        val capRefill = GCS(2, 2, 2, 1)
        val protobuf = capRefill.toProtobuf()
        Assert.assertEquals(capRefill.eye, protobuf.eye)
        Assert.assertEquals(capRefill.verbal, protobuf.verbal)
        Assert.assertEquals(capRefill.motor, protobuf.motor)
        Assert.assertEquals(capRefill.intubated, protobuf.intubated)
        Assert.assertEquals("6 (E2 V2 M2)T", capRefill.eventMessage)
        Assert.assertFalse(capRefill.isEmpty)
        Assert.assertEquals("GCS: 6 (E2 V2 M2)T", capRefill.toEventItem())
        Assert.assertEquals(6, capRefill.total)
        Assert.assertEquals(false, capRefill.isEmpty)
    }

    @Test
    fun testGCS_Empty(){
        val capRefill = GCS(-1, -1, -1, -1).produceEmptyVital()
        val protobuf = capRefill.toProtobuf()
        Assert.assertEquals(capRefill.eye, protobuf.eye)
        Assert.assertEquals(capRefill.verbal, protobuf.verbal)
        Assert.assertEquals(capRefill.motor, protobuf.motor)
        Assert.assertEquals(capRefill.intubated, protobuf.intubated)
        Assert.assertTrue(capRefill.isEmpty)
        Assert.assertNull(capRefill.toEventItem())
        Assert.assertEquals("Cleared GCS", capRefill.toEventItem(true))
        Assert.assertEquals(-3, capRefill.total)
        Assert.assertEquals(true, capRefill.isEmpty)
    }

    @Test
    fun testGcs_ProtoConstructor(){
        val gcs = GCS(gCS {
            eye = 1
            verbal = 2
            motor = 3
            intubated = 0
        })
        Assert.assertEquals(1, gcs.eye)
        Assert.assertEquals(2, gcs.verbal)
        Assert.assertEquals(3, gcs.motor)
        Assert.assertEquals(0, gcs.intubated)
    }

    @Test
    fun testIBP(){
        val ibp = InvasiveBloodPressure(1, 2)
        val protobuf = ibp.toProtobuf()
        Assert.assertEquals(ibp.systolic, protobuf.bps.value)
        Assert.assertEquals(ibp.diastolic, protobuf.bpd.value)
        Assert.assertEquals("1/2", ibp.eventMessage)
        Assert.assertFalse(ibp.isEmpty)
        Assert.assertEquals("iBP: 1/2", ibp.toEventItem())
    }

    @Test
    fun testIBP_Empty(){
        val ibp = InvasiveBloodPressure(null, null).produceEmptyVital()
        val protobuf = ibp.toProtobuf()
        Assert.assertFalse(protobuf.hasBps())
        Assert.assertFalse(protobuf.hasBpd())
        Assert.assertTrue(ibp.isEmpty)
        Assert.assertNull(ibp.toEventItem())
        Assert.assertEquals("Cleared iBP", ibp.toEventItem(true))
    }

    @Test
    fun testIBP_ProtoConstructor(){
        val ibp = InvasiveBloodPressure(iBP {
            bps = int32Value(12)
            bpd = int32Value(34)
        })
        Assert.assertEquals(12, ibp.systolic)
        Assert.assertEquals(34, ibp.diastolic)
    }

    @Test
    fun testOutput(){
        val output = Output(1.234f)
        val protobuf = output.toProtobuf()
        Assert.assertEquals(output.output, protobuf.output.value)
        Assert.assertEquals("1.2", output.eventMessage)
        Assert.assertFalse(output.isEmpty)
        Assert.assertEquals("Output: 1.2", output.toEventItem())
    }

    @Test
    fun testOutput_Empty(){
        val output = Output(null).produceEmptyVital()
        val protobuf = output.toProtobuf()
        Assert.assertFalse(protobuf.hasOutput())
        Assert.assertTrue(output.isEmpty)
        Assert.assertNull(output.toEventItem())
        Assert.assertEquals("Cleared Output", output.toEventItem(true))
    }

    @Test
    fun testOutput_ProtoConstructor(){
        val output = Output(output {
            output = floatValue(1.234f)
        })
        Assert.assertEquals(1.234f, output.output)
    }

    @Test
    fun testPain(){
        val pain = Pain(1)
        val protobuf = pain.toProtobuf()
        Assert.assertEquals(pain.pain, protobuf.pain.value)
        Assert.assertEquals("1", pain.eventMessage)
        Assert.assertFalse(pain.isEmpty)
        Assert.assertEquals("Pain Scale: 1", pain.toEventItem())
    }

    @Test
    fun testPain_Empty(){
        val pain = Pain(null).produceEmptyVital()
        val protobuf = pain.toProtobuf()
        Assert.assertFalse(protobuf.hasPain())
        Assert.assertTrue(pain.isEmpty)
        Assert.assertNull(pain.toEventItem())
        Assert.assertEquals("Cleared Pain Scale", pain.toEventItem(true))
    }

    @Test
    fun testPain_ProtoConstructor(){
        val pain = Pain(pain {
            pain = int32Value(1)
        })
        Assert.assertEquals(1, pain.pain)
    }

    @Test
    fun testTemp() {
        val temp = Temp(1.234f)
        val protobuf = temp.toProtobuf()

        Assert.assertEquals(1.234f, protobuf.temp.value)
        Assert.assertEquals("1.2 (F) -17.1 (C)", temp.eventMessage)
        Assert.assertFalse(temp.isEmpty)
        Assert.assertEquals("Temp: 1.2 (F) -17.1 (C)", temp.toEventItem())
    }

    @Test
    fun testTempWithMethod() {
        val method = "Method"
        val temp = Temp(1.234f, method)
        val protobuf = temp.toProtobuf()

        Assert.assertEquals(1.234f, protobuf.temp.value)
        Assert.assertEquals(method, protobuf.measurementMethod.value)
        Assert.assertEquals("1.2 (F) -17.1 (C) $method", temp.eventMessage)
        Assert.assertFalse(temp.isEmpty)
        Assert.assertEquals("Temp: 1.2 (F) -17.1 (C) $method", temp.toEventItem())
    }

    @Test
    fun testTemp_Empty(){
        val temp = Temp(null).produceEmptyVital()
        val protobuf = temp.toProtobuf()
        Assert.assertFalse(protobuf.hasTemp())
        Assert.assertTrue(temp.isEmpty)
        Assert.assertNull(temp.toEventItem())
        Assert.assertEquals("Cleared Temp", temp.toEventItem(true))
    }

    @Test
    fun testTemp_ProtoConstructor(){
        val temp = Temp(temp {
            temp = floatValue(1.234f)
        })
        Assert.assertEquals(1.234f, temp.temp)
    }

    @Test
    fun testTemp_ProtoConstructorWithMethod(){
        val method = "Method"
        val temp = Temp(temp {
            temp = floatValue(1.234f)
            measurementMethod = stringValue(method)
        })
        Assert.assertEquals(1.234f, temp.temp)
        Assert.assertEquals(method, temp.measurementMethod)
    }

    @Test
    fun testPip(){
        val pip = PIP(1)
        val protobuf = pip.toProtobuf()
        Assert.assertEquals(pip.pip, protobuf.pip.value)
        Assert.assertEquals("1", pip.eventMessage)
        Assert.assertFalse(pip.isEmpty)
        Assert.assertEquals("PIP: 1", pip.toEventItem())
    }

    @Test
    fun testPip_Empty(){
        val pip = PIP(null).produceEmptyVital()
        val protobuf = pip.toProtobuf()
        Assert.assertFalse(protobuf.hasPip())
        Assert.assertTrue(pip.isEmpty)
        Assert.assertNull(pip.toEventItem())
        Assert.assertEquals("Cleared PIP", pip.toEventItem(true))
    }

    @Test
    fun testPip_ProtoConstructor(){
        val pip = PIP(pIP {
            pip = int32Value(1)
        })
        Assert.assertEquals(1, pip.pip)
    }

    @Test
    fun testResp(){
        val resp = Resp(1)
        val protobuf = resp.toProtobuf()
        Assert.assertEquals(resp.resp, protobuf.resp.value)
        Assert.assertEquals("1", resp.eventMessage)
        Assert.assertFalse(resp.isEmpty)
        Assert.assertEquals("Resp: 1", resp.toEventItem())
    }

    @Test
    fun testResp_Empty(){
        val resp = Resp(null).produceEmptyVital()
        val protobuf = resp.toProtobuf()
        Assert.assertFalse(protobuf.hasResp())
        Assert.assertTrue(resp.isEmpty)
        Assert.assertNull(resp.toEventItem())
        Assert.assertEquals("Cleared Resp", resp.toEventItem(true))
    }

    @Test
    fun testResp_ProtoConstructor(){
        val resp = Resp(resp {
            resp = int32Value(1)
        })
        Assert.assertEquals(1, resp.resp)
    }

    @Test
    fun testSpo2(){
        val spo2 = SpO2(1)
        val protobuf = spo2.toProtobuf()
        Assert.assertEquals(spo2.spo2, protobuf.spo2.value)
        Assert.assertEquals("1", spo2.eventMessage)
        Assert.assertFalse(spo2.isEmpty)
        Assert.assertEquals("SpO2: 1", spo2.toEventItem())
    }

    @Test
    fun testSpo2_Empty(){
        val spo2 = SpO2(null).produceEmptyVital()
        val protobuf = spo2.toProtobuf()
        Assert.assertFalse(protobuf.hasSpo2())
        Assert.assertTrue(spo2.isEmpty)
        Assert.assertNull(spo2.toEventItem())
        Assert.assertEquals("Cleared SpO2", spo2.toEventItem(true))
    }

    @Test
    fun testSpo2_ProtoConstructor(){
        val spo2 = SpO2(spO2 {
            spo2 = int32Value(1)
        })
        Assert.assertEquals(1, spo2.spo2)
    }

    @Test
    fun testEmptyVital(){
        val hr = HR(10)
        Assert.assertFalse(hr.isEmpty)
        Assert.assertTrue(hr.produceEmptyVital().isEmpty)
    }
}