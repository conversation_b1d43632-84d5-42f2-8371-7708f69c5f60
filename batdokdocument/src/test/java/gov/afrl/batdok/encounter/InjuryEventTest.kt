package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.Injuries.Companion.includeInjuryEvents
import gov.afrl.batdok.encounter.commands.buildAddPointWithLabelCommand
import gov.afrl.batdok.encounter.commands.buildChangeInjuryCommand
import gov.afrl.batdok.encounter.commands.buildChangeMoiCommand
import gov.afrl.batdok.encounter.commands.buildChangeTBSACommand
import gov.afrl.batdok.encounter.commands.buildClearDrawViewCommand
import gov.afrl.batdok.encounter.commands.buildClearInjuryDrawingsCommand
import gov.afrl.batdok.encounter.commands.buildRemoveDrawPointCommand
import gov.afrl.batdok.encounter.commands.buildUndoDrawViewCommand
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class InjuryEventTest {

    val events = Events()

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS)) {
        EventCommandHandler(events) {
            includeInjuryEvents()
        }.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp))
    }

    @Test
    fun testChangeInjuryCommand(){
        // checked
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeInjuryCommand(Injury.Type.GSW.dataString, true, Injury.Type.GSW.abbrev, InjuryLocation.HEAD.dataString))
        Assert.assertEquals("Set " + Injury.Type.GSW.dataString + " as Injury", events.list[0].event)
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals(KnownEventTypes.MOI.dataString, events.list[0].eventType)

        // unchecked
        Assert.assertEquals(1, events.list.size)
        handle(buildChangeInjuryCommand(Injury.Type.GSW.dataString, false, Injury.Type.GSW.abbrev, InjuryLocation.HEAD.dataString))
        Assert.assertEquals("Unset " + Moi.GSW.dataString + " as Injury", events.list[1].event)
        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals(KnownEventTypes.MOI.dataString, events.list[1].eventType)
    }

    @Test
    fun testChangeMoiCommand(){
        // checked
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeMoiCommand(Moi.GSW.dataString, true, InjuryLocation.HEAD.dataString))
        Assert.assertEquals("Set " + Moi.GSW.dataString + " as MoI", events.list[0].event)
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals(KnownEventTypes.MOI.dataString, events.list[0].eventType)

        // unchecked
        Assert.assertEquals(1, events.list.size)
        handle(buildChangeMoiCommand(Moi.GSW.dataString, false, InjuryLocation.HEAD.dataString))
        Assert.assertEquals("Unset " + Moi.GSW.dataString + " as MoI", events.list[1].event)
        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals(KnownEventTypes.MOI.dataString, events.list[1].eventType)
    }

    @Test
    fun testChangeTBSACommand(){
        val tbsa = 15.0
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeTBSACommand(tbsa))
        Assert.assertEquals("Set Burn TBSA to $tbsa%", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.MOI.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeTBSACommand_Zero(){
        val tbsa = 0.0
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeTBSACommand(tbsa))
        Assert.assertEquals("Set Burn TBSA to $tbsa%", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.MOI.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeTBSACommand_Null(){
        val tbsa = null
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeTBSACommand(tbsa))
        Assert.assertEquals("Cleared Burn TBSA", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.MOI.dataString, events.list[0].eventType)
    }

    @Test
    fun testAddDrawPointEventHandler() {
        Assert.assertEquals(0, events.list.size)
        // Injury from BATDOK
        handle(buildAddPointWithLabelCommand(DrawingPoint("AMP", .25f, .2f)))
        // Injury from CDP
        handle(buildAddPointWithLabelCommand(DrawingPoint("BUB", .15f, .25f)))
        // Ambiguous injury from CDP
        handle(buildAddPointWithLabelCommand(DrawingPoint("INJ", .1f, .4f)))

        Assert.assertEquals(3, events.list.size)
        val eventStrings = events.list.map { it.event }
        Assert.assertEquals("Added Amputation to body diagram on Anterior Neck", eventStrings[0])
        Assert.assertEquals("Added Bubbling to body diagram on Anterior Right Shoulder", eventStrings[1])
        Assert.assertEquals("Added Unknown injury to body diagram on Anterior Right Lower Arm", eventStrings[2])
        events.list.map { Assert.assertEquals(KnownEventTypes.MOI.dataString, it.eventType) }
    }

    @Test
    fun testRemoveDrawPointEventHandler() {
        Assert.assertEquals(0, events.list.size)
        // Injury from BATDOK
        handle(buildRemoveDrawPointCommand(DrawingPoint("AMP", .25f, .2f)))
        // Injury from CDP
        handle(buildRemoveDrawPointCommand(DrawingPoint("BUB", .15f, .25f)))
        // Ambiguous injury from CDP
        handle(buildRemoveDrawPointCommand(DrawingPoint("INJ", .1f, .4f)))

        Assert.assertEquals(3, events.list.size)
        val eventStrings = events.list.map { it.event }
        Assert.assertEquals("Removed injury from body diagram on Anterior Neck", eventStrings[0])
        Assert.assertEquals("Removed injury from body diagram on Anterior Right Shoulder", eventStrings[1])
        Assert.assertEquals("Removed injury from body diagram on Anterior Right Lower Arm", eventStrings[2])
        events.list.map { Assert.assertEquals(KnownEventTypes.MOI.dataString, it.eventType) }
    }

    @Test
    fun testUndoDrawPointEventHandler() {
        Assert.assertEquals(0, events.list.size)
        handle(buildUndoDrawViewCommand())

        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Removed last drawing point from body diagram", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.MOI.dataString, events.list[0].eventType)
    }

    @Test
    fun testClearInjuryDrawingsEventHandler() {
        Assert.assertEquals(0, events.list.size)
        handle(buildClearInjuryDrawingsCommand("AMP"))
        handle(buildClearInjuryDrawingsCommand(null))

        Assert.assertEquals(2, events.list.size)
        val eventStrings = events.list.map { it.event }
        Assert.assertEquals("Removed Amputation drawing points from body diagram", eventStrings[0])
        Assert.assertEquals("Cleared drawing points from body diagram", eventStrings[1])
        events.list.map { Assert.assertEquals(KnownEventTypes.MOI.dataString, it.eventType) }
    }

    @Test
    fun testClearDrawViewEventHandler() {
        Assert.assertEquals(0, events.list.size)
        handle(buildClearDrawViewCommand("type"))
        handle(buildClearDrawViewCommand(null))

        Assert.assertEquals(2, events.list.size)
        val eventStrings = events.list.map { it.event }
        Assert.assertEquals("Cleared type drawing points from body diagram", eventStrings[0])
        Assert.assertEquals("Cleared all drawing points from body diagram", eventStrings[1])
        events.list.map { Assert.assertEquals(KnownEventTypes.MOI.dataString, it.eventType) }
    }
}
