package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.commands.buildHL7DataCommand
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class HL7CommandsStateTest {

    private lateinit var classUnderTest: HistoricalHL7Data

    @Before
    fun setup() {
        classUnderTest = HistoricalHL7Data()
    }

    @Test
    fun testHL7DataCommand() {
        // ARRANGE
        val sampleMessage = "sample message data"

        // ACT
        handle(buildHL7DataCommand(sampleMessage))

        // ASSERT
        Assert.assertEquals(sampleMessage, classUnderTest.hl7Messages)
    }

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS)) {
        classUnderTest.handlers.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp))
    }

}
