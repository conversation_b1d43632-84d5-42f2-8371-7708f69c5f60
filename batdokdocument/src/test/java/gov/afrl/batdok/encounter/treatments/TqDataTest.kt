package gov.afrl.batdok.encounter.treatments

import gov.afrl.batdok.encounter.treatment.TqData
import org.junit.Assert
import org.junit.Test

class TqDataTest {

    @Test
    fun testTqLocationPairs(){
        fun expectValidPair(location: TqData.Location, subLocation: TqData.SubLocation){
            try{
                TqData(location.dataString, subLocation = subLocation.dataString)
            }catch (ex: IllegalArgumentException){
                Assert.fail("${location.dataString}/${subLocation.dataString} should be a valid pair")
            }
        }
        fun expectInvalidPair(location: TqData.Location, subLocation: TqData.SubLocation){
            try{
                TqData(location.dataString, subLocation = subLocation.dataString)
                Assert.fail("${location.dataString}/${subLocation.dataString} should not be a valid pair")
            }catch (ex: IllegalArgumentException){
                // It should throw here
            }
        }

        expectValidPair(TqData.Location.EXTREMITY, TqData.SubLocation.LLE)
        expectValidPair(TqData.Location.EXTREMITY, TqData.SubLocation.LUE)
        expectValidPair(TqData.Location.EXTREMITY, TqData.SubLocation.RLE)
        expectValidPair(TqData.Location.EXTREMITY, TqData.SubLocation.RUE)
        expectInvalidPair(TqData.Location.EXTREMITY, TqData.SubLocation.AXILLA)
        expectInvalidPair(TqData.Location.EXTREMITY, TqData.SubLocation.INGUINAL)

        expectValidPair(TqData.Location.JUNCTIONAL, TqData.SubLocation.AXILLA)
        expectValidPair(TqData.Location.JUNCTIONAL, TqData.SubLocation.INGUINAL)
        expectInvalidPair(TqData.Location.JUNCTIONAL, TqData.SubLocation.LLE)
        expectInvalidPair(TqData.Location.JUNCTIONAL, TqData.SubLocation.LUE)
        expectInvalidPair(TqData.Location.JUNCTIONAL, TqData.SubLocation.RLE)
        expectInvalidPair(TqData.Location.JUNCTIONAL, TqData.SubLocation.RUE)

        expectInvalidPair(TqData.Location.TRUNCAL, TqData.SubLocation.AXILLA)
        expectInvalidPair(TqData.Location.TRUNCAL, TqData.SubLocation.INGUINAL)
        expectInvalidPair(TqData.Location.TRUNCAL, TqData.SubLocation.LLE)
        expectInvalidPair(TqData.Location.TRUNCAL, TqData.SubLocation.LUE)
        expectInvalidPair(TqData.Location.TRUNCAL, TqData.SubLocation.RLE)
        expectInvalidPair(TqData.Location.TRUNCAL, TqData.SubLocation.RUE)
    }
}