package gov.afrl.batdok.encounter

import gov.afrl.batdok.util.Patterns
import gov.afrl.batdok.util.format
import org.junit.Assert.*

import org.junit.Before
import org.junit.Test
import java.time.Instant
import java.time.ZoneId
import java.time.ZonedDateTime

class IntervalTest {
    @Test
    fun createInterval() {
        val interval = Interval("Once")
        assertEquals("Once", interval.toString())
    }

    @Test
    fun createInterval_HourMinuteConstructor() {
        val interval = Interval(hour = 35, minute = 30)
        assertEquals("Q35H30MIN", interval.toString())
    }

    @Test
    fun createInterval_HourMinuteConstructor_NegativeHour() {
        val interval = Interval(hour = -35, minute = 30)
        assertEquals("Q30MIN", interval.toString())
    }

    @Test
    fun createInterval_HourMinuteConstructor_NegativeMinute() {
        val interval = Interval(hour = 35, minute = -30)
        assertEquals("Q35H", interval.toString())
    }

    @Test
    fun createInterval_HourMinuteConstructor_ZeroHourZeroMinute() {
        val interval = Interval(hour = 0, minute = 0)
        assertEquals("", interval.toString())
    }

    @Test
    fun nextDue_lastApplication_resultIsNull() {
        var interval = Interval("Once")
        assertNull(interval.nextDue(Instant.now()))

        interval = Interval("QuincyHowardMinutia")
        assertNull(interval.nextDue(Instant.now()))
    }

    @Test
    fun nextDue_lastApplicationAt12_result1350() {
        var interval = Interval(1,50)
        val lastApplication = ZonedDateTime.of(2024, 5, 21, 12, 0, 0, 0, ZoneId.systemDefault()).toInstant()
        assertEquals("13:50", interval.nextDue(lastApplication)?.format(Patterns.hm_24_colon))

        interval = Interval("Q1H50MIN")
        assertEquals("13:50", interval.nextDue(lastApplication)?.format(Patterns.hm_24_colon))
    }

}