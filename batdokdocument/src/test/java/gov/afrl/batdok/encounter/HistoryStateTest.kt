package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.commands.buildHistoryOfPresentIllnessCommand
import gov.afrl.batdok.encounter.commands.buildLastEventTimeCommand
import gov.afrl.batdok.encounter.commands.buildPastHistoryCommand
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class HistoryStateTest {
    val history = History()

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS)) {
        history.handlers.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp))
    }

    @Test
    fun testUpdateHpiCommand(){
        //ASSIGN
        val history = "History"

        //ACT
        handle(buildHistoryOfPresentIllnessCommand(history))

        //ASSERT
        Assert.assertEquals(history, this.history.historyOfPresentIllness)
    }

    @Test
    fun testUpdateHpiCommand_Clear_null(){
        //ASSIGN
        val history = null
        this.history.historyOfPresentIllness = "History"

        //ACT
        handle(buildHistoryOfPresentIllnessCommand(history))

        //ASSERT
        Assert.assertNull(this.history.historyOfPresentIllness)
    }

    @Test
    fun testUpdateHpiCommand_Clear_empty(){
        //ASSIGN
        val history = ""
        this.history.historyOfPresentIllness = "History"

        //ACT
        handle(buildHistoryOfPresentIllnessCommand(history))

        //ASSERT
        Assert.assertNull(this.history.historyOfPresentIllness)
    }

    @Test
    fun testUpdatePastHistory(){
        //ASSIGN
        val type = HistoryType.FAMILY.dataString
        val history = "TEST"

        //ACT
        handle(buildPastHistoryCommand(type, history))

        //ASSERT
        Assert.assertEquals(history, this.history.histories[type])
    }

    @Test
    fun testUpdatePastHistory_Clear_null(){
        //ASSIGN
        val type = HistoryType.FAMILY.dataString
        val history = null
        this.history.histories += (type to "Test")

        //ACT
        handle(buildPastHistoryCommand(type, history))

        //ASSERT
        Assert.assertNull(this.history.histories[type])
    }

    @Test
    fun testUpdatePastHistory_Clear_emptyString(){
        //ASSIGN
        val type = HistoryType.FAMILY.dataString
        val history = ""
        this.history.histories += (type to "Test")

        //ACT
        handle(buildPastHistoryCommand(type, history))

        //ASSERT
        Assert.assertNull(this.history.histories[type])
    }

    @Test
    fun testUpdateHistoryLastEvent_Add_KnownType() {
        //ASSIGN
        val type = HistoryLastEventType.BOWEL.dataString
        val date = Instant.now().truncatedTo(ChronoUnit.SECONDS)

        //ACT
        handle(buildLastEventTimeCommand(type, date))

        //ASSERT
        Assert.assertEquals(date, this.history.lastEventTimes[type]?.time)
    }

    @Test
    fun testUpdateHistoryLastEvent_Add_CustomType() {
        //ASSIGN
        val type = "Test"
        val date = Instant.now().truncatedTo(ChronoUnit.SECONDS)

        //ACT
        handle(buildLastEventTimeCommand(type, date))

        //ASSERT
        Assert.assertEquals(date, this.history.lastEventTimes[type]?.time)
    }

    @Test
    fun testUpdateHistoryLastEvent_Add_BloodDonationExtras() {
        //ASSIGN
        val type = HistoryLastEventType.BLOOD_DONATION.dataString
        val date = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val volume = 4
        val extras = BloodDonationExtras(volume)

        //ACT
        handle(buildLastEventTimeCommand(type, date, extras))

        //ASSERT
        Assert.assertEquals(date, this.history.lastEventTimes[type]?.time)
        Assert.assertEquals("$volume Units", this.history.lastEventTimes[type]?.extras?.toEventText())
    }

    @Test
    fun testUpdateHistoryLastEvent_Clear_null(){
        //ASSIGN
        val type = HistoryLastEventType.BOWEL.dataString
        val date = null
        this.history.lastEventTimes += (type to LastEventTime(Instant.now()))

        //ACT
        handle(buildLastEventTimeCommand(type, date))

        //ASSERT
        Assert.assertNull(this.history.lastEventTimes[type])
    }
}