package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.InputOutputs.Companion.includeInputOutputEvents
import gov.afrl.batdok.encounter.commands.buildAddIOItemCommand
import gov.afrl.batdok.encounter.commands.buildRemoveIOItem
import gov.afrl.batdok.encounter.commands.buildUpdateIOItemCommand
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.encounter.ids.InputOutputId
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class InputOutputEventTest {

    val events = Events()
    val inputOutputs = InputOutputs()

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS), commandId: CommandId = DomainId.create()) {
        EventCommandHandler(events) {
            includeInputOutputEvents(inputOutputs)
        }.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp, commandId))
    }

    @Test
    fun testAddIOItem_input(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val itemId = DomainId.create<InputOutputId>()
        val item = InputOutput(itemId, timestamp, "Name", "Route", 1.0, "Unit")
        Assert.assertEquals(0, events.list.size)
        handle(buildAddIOItemCommand(item, true), commandId = itemId.copy(), timestamp = timestamp)
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Logged Input: Name 1.0 Unit Route", events.list[0].event)
        Assert.assertEquals(timestamp, events.list[0].timestamp)
    }

    @Test
    fun testAddIOItem_output(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val itemId = DomainId.create<InputOutputId>()
        val item = InputOutput(
            itemId,
            timestamp,
            InputOutputType.URINE.dataString,
            InputOutputRoute.CATHETER.dataString,
            1.0,
            InputOutputUnit.CC.dataString
        )
        Assert.assertEquals(0, events.list.size)
        handle(buildAddIOItemCommand(item, false), commandId = itemId.copy(), timestamp = timestamp)
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Logged Output: Urine 1.0 cc Catheter", events.list[0].event)
        Assert.assertEquals(timestamp, events.list[0].timestamp)
    }

    @Test
    fun testUpdateIOItem_input(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val itemId = DomainId.create<InputOutputId>()
        val item = InputOutput(
            itemId, timestamp, "Name", "Route", 1.0, "Unit", DomainId.nil()
        )
        inputOutputs.inputs += InputOutput(
            itemId,
            timestamp.minusMillis(1000),
            "Old Type",
            "Old Route",
            2.718281828459045,
            "Old Unit"
        )
        events += Event("Old Event", itemId.copy(), timestamp.minusSeconds(1000))
        Assert.assertEquals(1, events.list.size)
        handle(buildUpdateIOItemCommand(item, true))
        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals("Updated Input: Name 1.0 Unit Route", events.list[1].event)
        Assert.assertEquals(timestamp, events.list[1].timestamp)
    }

    @Test
    fun testUpdateIOItem_output(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val itemId = DomainId.create<InputOutputId>()
        val item = InputOutput(
            itemId,
            timestamp,
            InputOutputType.URINE.dataString,
            InputOutputRoute.CATHETER.dataString,
            1.0,
            InputOutputUnit.CC.dataString
        )
        inputOutputs.outputs += InputOutput(
            itemId,
            timestamp.minusMillis(1000),
            "Old Type",
            "Old Route",
            2.718281828459045,
            "Old Unit"
        )
        events += Event("Old Event", itemId.copy(), timestamp.minusSeconds(1000))
        Assert.assertEquals(1, events.list.size)
        handle(buildUpdateIOItemCommand(item, false), timestamp = timestamp)
        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals("Updated Output: Urine 1.0 cc Catheter", events.list[1].event)
        Assert.assertEquals(timestamp, events.list[1].timestamp)
        Assert.assertEquals(itemId, events.list[1].referencedItem)
    }

    @Test
    fun testRemoveIOItem_input(){
        val itemId = DomainId.create<InputOutputId>()
        inputOutputs.inputs += InputOutput(
            itemId,
            Instant.now().truncatedTo(ChronoUnit.SECONDS),
            "Old Type",
            "Old Route",
            2.718281828459045,
            "Old Unit"
        )
        events += Event("Old Event", itemId.copy(), Instant.now().truncatedTo(ChronoUnit.SECONDS))
        Assert.assertEquals(1, events.list.size)
        handle(buildRemoveIOItem(itemId))
        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals("Removed Input: Old Type 2.718281828459045 Old Unit Old Route", events.list[1].event)
        Assert.assertEquals(itemId, events.list[1].referencedItem)
    }

    @Test
    fun testRemoveIOItem_output(){
        val itemId = DomainId.create<InputOutputId>()
        inputOutputs.outputs += InputOutput(
            itemId,
            Instant.now().truncatedTo(ChronoUnit.SECONDS),
            "Old Type",
            "Old Route",
            2.718281828459045,
            "Old Unit"
        )
        events += Event("Old Event", itemId.copy(), Instant.now().truncatedTo(ChronoUnit.SECONDS))
        Assert.assertEquals(1, events.list.size)
        handle(buildRemoveIOItem(itemId))
        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals("Removed Output: Old Type 2.718281828459045 Old Unit Old Route", events.list[1].event)
        Assert.assertEquals(itemId, events.list[1].referencedItem)
    }
}