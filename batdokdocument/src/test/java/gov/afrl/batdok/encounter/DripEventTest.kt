package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.commands.*
import gov.afrl.batdok.encounter.ids.MedicineId
import gov.afrl.batdok.encounter.medicine.Drip
import gov.afrl.batdok.encounter.medicine.Drips
import gov.afrl.batdok.encounter.medicine.Drips.Companion.includeDripEvents
import gov.afrl.batdok.encounter.medicine.Medicine
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant

class DripEventTest {
    val events = Events()
    val drips = Drips()
    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now()) {
        EventCommandHandler(events) {
            includeDripEvents()
        }.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp))
    }

    @Test
    fun testCreateDrip(){
        val id = DomainId.create<MedicineId>()
        val medicine = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            Instant.ofEpochSecond(12331),
            id,
            "testroute",
            1.2f,
            "cm",
            "45646",
            "11111",
            "tsttype"
        )
        Assert.assertEquals(0, events.list.size)

        handle(buildCreateDrip(medicine), timestamp = medicine.administrationTime!!)
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Drip Created with Base tsttype: newmed testroute 1.2 cm (45646, 11111)", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.MEDS.dataString, events.list[0].eventType)
    }
    @Test
    fun testRemoveDrip(){
        val id = DomainId.create<MedicineId>()
        val medicine = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            Instant.ofEpochSecond(12331),
            id,
            "testroute",
            1.2f,
            "cm",
            "45646",
            "11111",
            "tsttype"
        )
        val testDrip = Drip(
            medicine,
            medicine.administrationTime,
            MutableList(1){medicine}
        )
        drips += testDrip

        handle(buildRemoveDrip(id), timestamp = medicine.administrationTime!!)
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Removed Drip with Base Medicine id: ${id.toHexString()}", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.MEDS.dataString, events.list[0].eventType)
    }
    @Test
    fun testAddToDrip(){
        val id = DomainId.create<MedicineId>()
        val medicine = Medicine(
            "newmed2",
            "testNdc2",
            "testRxcui2",
            Instant.ofEpochSecond(100),
            id,
            "testroute2",
            1.1f,
            "cm",
            "4444",
            "0000",
            "tsttype2"
        )

        val testDrip = Drip(
            medicine,
            medicine.administrationTime,
            MutableList(1){medicine}
        )
        drips += testDrip

        val id2 = DomainId.create<MedicineId>()
        val medicine2 = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            Instant.ofEpochSecond(12331),
            id2,
            "testroute",
            1.2f,
            "cm",
            "45646",
            "11111",
            "tsttype"
        )

        handle(buildAddToDrip(testDrip, medicine2), timestamp = medicine2.administrationTime!!)
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Added Medicine tsttype: newmed testroute 1.2 cm (45646, 11111) to Drip: newmed2", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.MEDS.dataString, events.list[0].eventType)
    }
    @Test
    fun testRemoveFromDrip(){
        val id = DomainId.create<MedicineId>()
        val medicine = Medicine(
            "newmed2",
            "testNdc2",
            "testRxcui2",
            Instant.ofEpochSecond(100),
            id,
            "testroute2",
            1.1f,
            "cm",
            "4444",
            "0000",
            "tsttype2"
        )

        val testDrip = Drip(
            medicine,
            medicine.administrationTime,
            MutableList(1){medicine}
        )
        drips += testDrip

        val id2 = DomainId.create<MedicineId>()
        val medicine2 = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            Instant.ofEpochSecond(12331),
            id2,
            "testroute",
            1.2f,
            "cm",
            "45646",
            "11111",
            "tsttype"
        )

        drips.drips[0].components.add(medicine2)
        Assert.assertEquals(0, events.list.size)

        handle(buildRemoveFromDrip(testDrip, medicine2), timestamp = medicine2.administrationTime!!)
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Removed Medicine tsttype: newmed testroute 1.2 cm (45646, 11111) from Drip newmed2", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.MEDS.dataString, events.list[0].eventType)
    }
    @Test
    fun testStartDrip(){
        val id = DomainId.create<MedicineId>()
        val medicine = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            Instant.ofEpochSecond(12331),
            id,
            "testroute",
            1.2f,
            "cm",
            "45646",
            "11111",
            "tsttype"
        )
        val testDrip = Drip(
            medicine,
            medicine.administrationTime,
            MutableList(1){medicine}
        )
        drips += testDrip
        Assert.assertEquals(0, events.list.size)

        handle(buildStartDrip(testDrip, Instant.ofEpochSecond(1111)), timestamp = medicine.administrationTime!!)
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Started Drip tsttype: newmed testroute 1.2 cm (45646, 11111) at 1111", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.MEDS.dataString, events.list[0].eventType)
    }
    @Test
    fun testStopDrip(){
        val id = DomainId.create<MedicineId>()
        val medicine = Medicine(
            "newmed",
            "testNdc",
            "testRxcui",
            Instant.ofEpochSecond(12331),
            id,
            "testroute",
            1.2f,
            "cm",
            "45646",
            "11111",
            "tsttype"
        )
        val testDrip = Drip(
            medicine,
            medicine.administrationTime,
            MutableList(1){medicine}
        )
        drips += testDrip
        Assert.assertEquals(0, events.list.size)
        testDrip.startTime = Instant.ofEpochSecond(1111)

        handle(buildStopDrip(testDrip, Instant.ofEpochSecond(2222)), timestamp = medicine.administrationTime!!)
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Stopped Drip tsttype: newmed testroute 1.2 cm (45646, 11111) at 2222", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.MEDS.dataString, events.list[0].eventType)
    }
}