package gov.afrl.batdok.encounter.orders

import gov.afrl.batdok.encounter.medicine.Medicine
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert.*
import org.junit.Before

import org.junit.Test

class OrderedMedicineTest {

    private lateinit var classUnderTest: OrderedMedicine
    @Before
    fun setup() {
        classUnderTest = OrderedMedicine(
            name = "Acetaminophen",
            ndc = "NDC2",
            rxcui = "RXCUI2",
            medId = DomainId.create("12345678"),
            route = "IV",
            volume = 50f,
            unit = "cc",
            serialNumber = "frostedflakes",
            expirationDate = "whenever",
            type = "meds",
            exportName = "Acetaminophen"
        )
    }

    @Test
    fun toMedicine() {
        val result = classUnderTest.toMedicine()

        assertEquals(classUnderTest.name, result.name)
        assertEquals(classUnderTest.ndc, result.ndc)
        assertEquals(classUnderTest.rxcui, result.rxcui)
        assertNotEquals(classUnderTest.medId, result.medId)
        assertEquals(classUnderTest.route, result.route)
        assertEquals(classUnderTest.volume, result.volume)
        assertEquals(classUnderTest.unit, result.unit)
        assertEquals(classUnderTest.serialNumber, result.serialNumber)
        assertEquals(classUnderTest.expirationDate, result.expirationDate)
        assertEquals(classUnderTest.type, result.type)
        assertEquals(classUnderTest.exportName, result.exportName)
    }
}