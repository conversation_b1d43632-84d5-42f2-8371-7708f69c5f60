package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.commands.addVital
import gov.afrl.batdok.encounter.commands.buildLogVitalCommand
import gov.afrl.batdok.encounter.commands.buildRemoveVitalCommand
import gov.afrl.batdok.encounter.commands.buildUpdateVitalCommand
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.encounter.ids.EncounterVitalId
import gov.afrl.batdok.encounter.vitals.*
import gov.afrl.batdok.util.buildCommandData
import gov.afrl.batdok.util.buildDocumentCommand
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Clock
import java.time.Duration
import java.time.Instant
import java.time.ZoneId
import java.time.temporal.ChronoUnit

class VitalStateTest {

    val docId = DomainId.create<DocumentId>()
    val vitals = Vitals()

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS), commandId: CommandId = DomainId.create()) {
        vitals.handlers.handle(docId, buildCommandData(subCommand, callsign, timestamp, commandId = commandId))
    }

    @Test
    fun testLogVital(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val vitalId = DomainId.create<EncounterVitalId>()
        handle(buildLogVitalCommand(timestamp){
            addVital(HR(1))
            addVital(SpO2(2))
        }, commandId = vitalId.copy())
        Assert.assertEquals(vitalId, vitals.list[0].vitalId)
        val vital = vitals[vitalId]
        Assert.assertNotNull(vital)
        Assert.assertEquals(1, vital!!.get<HR>()?.pulse)
        Assert.assertEquals(2, vital.get<SpO2>()?.spo2)
        Assert.assertNull(vital.get<InvasiveBloodPressure>())
        Assert.assertEquals(docId, vital.documentId)
    }

    @Test
    fun testUpdateVital(){
        val clock = Clock.fixed(Instant.now(), ZoneId.systemDefault())
        val timestamp = Instant.now(clock).truncatedTo(ChronoUnit.SECONDS)
        val vitalId = DomainId.create<EncounterVitalId>()

        vitals += EncounterVital(vitalId, timestamp, documentId = docId) + HR(1) + SpO2(2)

        val preVital = vitals[vitalId]
        Assert.assertEquals(1, preVital!!.get<HR>()?.pulse)
        Assert.assertEquals(2, preVital.get<SpO2>()?.spo2)


        val timestamp2 = Instant.now(Clock.tick(clock, Duration.ofSeconds(10))).truncatedTo(ChronoUnit.SECONDS)

        handle(buildUpdateVitalCommand(vitalId, timestamp2){
            addVital(Resp(3))
            addVital(BloodPressure(4, 5))
        })

        Assert.assertEquals(1, vitals.size)
        Assert.assertEquals(vitalId, vitals.list[0].vitalId)
        val postVital = vitals[vitalId]
        Assert.assertNotNull(postVital)
        //Check new items first
        Assert.assertEquals(3, postVital!!.get<Resp>()?.resp)
        Assert.assertEquals(4, postVital.get<BloodPressure>()?.systolic)
        Assert.assertEquals(5, postVital.get<BloodPressure>()?.diastolic)
        Assert.assertEquals(timestamp2, postVital.timestamp)

        //Then check that old items are still there
        Assert.assertEquals(1, postVital.get<HR>()?.pulse)
        Assert.assertEquals(2, postVital.get<SpO2>()?.spo2)
        Assert.assertNull(postVital.get<InvasiveBloodPressure>())
        Assert.assertEquals(docId, postVital.documentId)
    }

    @Test
    fun testUpdateVital_ClearVitals(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val vitalId = DomainId.create<EncounterVitalId>()

        vitals += EncounterVital(vitalId, timestamp, documentId = docId) + HR(1) + SpO2(2)

        val preVital = vitals[vitalId]
        Assert.assertEquals(1, preVital!!.get<HR>()?.pulse)
        Assert.assertEquals(2, preVital.get<SpO2>()?.spo2)

        handle(buildUpdateVitalCommand(vitalId, timestamp){
            addVital(HR(null))
            addVital(SpO2(null))
            addVital(Resp(3))
            addVital(BloodPressure(4, 5))
        })

        Assert.assertEquals(1, vitals.list.size)
        Assert.assertEquals(vitalId, vitals.list[0].vitalId)
        val postVital = vitals[vitalId]
        Assert.assertNotNull(postVital)
        //Check new items first
        Assert.assertEquals(3, postVital!!.get<Resp>()?.resp)
        Assert.assertEquals(4, postVital.get<BloodPressure>()?.systolic)
        Assert.assertEquals(5, postVital.get<BloodPressure>()?.diastolic)

        //Then check that old items are still there
        Assert.assertNull(postVital.get<HR>())
        Assert.assertNull(postVital.get<SpO2>())
        Assert.assertNull(postVital.get<InvasiveBloodPressure>())
        Assert.assertEquals(docId, postVital.documentId)
    }

    @Test
    fun testRemoveVitals(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val vitalId = DomainId.create<EncounterVitalId>()

        vitals += EncounterVital(vitalId, timestamp, documentId = docId) + HR(1) + SpO2(2)

        val preVital = vitals[vitalId]
        Assert.assertEquals(1, preVital!!.get<HR>()?.pulse)
        Assert.assertEquals(2, preVital.get<SpO2>()?.spo2)

        handle(buildRemoveVitalCommand(vitalId))

        Assert.assertEquals(0, vitals.list.size)
    }

    @Test
    fun testSetId(){
        val vitalId = DomainId.create<EncounterVitalId>()
        val doc = Document()

        doc.handle(
            listOf(
                buildDocumentCommand(
                    DomainId.create(),
                    buildCommandData(buildLogVitalCommand(Instant.now(), vitalId){ addVital(PIP(5)) })
                )
            )
        )

        Assert.assertEquals(vitalId, doc.vitals.list.first().vitalId)
    }
}