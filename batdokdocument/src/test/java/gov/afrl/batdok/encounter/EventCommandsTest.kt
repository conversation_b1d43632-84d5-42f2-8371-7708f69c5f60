package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.commands.buildAddCommentCommand
import gov.afrl.batdok.encounter.commands.buildAddEventCommand
import gov.afrl.batdok.encounter.commands.buildEditEventCommand
import gov.afrl.batdok.encounter.commands.buildRemoveEventCommand
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.encounter.ids.EventId
import gov.afrl.batdok.util.buildCommandData
import gov.afrl.batdok.util.buildDocumentCommand
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class EventCommandsTest {
    val docId = DomainId.create<DocumentId>()
    val events = Events()

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS)) {
        events.handlers.handle(docId, buildCommandData(subCommand,callsign,timestamp))
    }

    @Test
    fun testAddEventCommand(){
        Assert.assertEquals(0, events.list.size)
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)

        val addEvent = buildAddEventCommand(timestamp,"Sample Event", true, "TEST", true)
        handle(addEvent)

        Assert.assertEquals(1,events.list.size)
        Assert.assertEquals(timestamp, events.list[0].timestamp)
        Assert.assertEquals("Sample Event", events.list[0].event)
        Assert.assertEquals(true, events.list[0].showTime)
        Assert.assertEquals("TEST", events.list[0].eventType)
        Assert.assertEquals(true, events.list[0].isCustom)
        Assert.assertEquals(docId, events.list[0].documentId)
    }

    @Test
    fun testAddEventCommand_Enum(){
        Assert.assertEquals(0, events.list.size)
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)

        val addEvent = buildAddEventCommand(timestamp,"Sample Event", true, KnownEventTypes.CASEVAC, true)
        handle(addEvent)

        Assert.assertEquals(1,events.list.size)
        Assert.assertEquals(timestamp, events.list[0].timestamp)
        Assert.assertEquals("Sample Event", events.list[0].event)
        Assert.assertEquals(true, events.list[0].showTime)
        Assert.assertEquals(KnownEventTypes.CASEVAC.dataString, events.list[0].eventType)
        Assert.assertEquals(true, events.list[0].isCustom)
        Assert.assertEquals(docId, events.list[0].documentId)
    }

    @Test
    fun testEditExistingEventCommand(){
        Assert.assertEquals(0, events.list.size)
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)

        val newEvent = Event("Test Event", DomainId.create(), timestamp, docId, "CS", "TEST", true)
        events += newEvent

        Assert.assertEquals(1, events.list.size)

        val newTimestamp = Instant.ofEpochMilli(12000)
        val editEvent = buildEditEventCommand(newEvent.eventId, newTimestamp, "Changed Event", false, "CHANGE", true)
        handle(editEvent)

        Assert.assertEquals(1,events.list.size)
        Assert.assertEquals(newEvent.eventId, events.list[0].eventId)
        Assert.assertEquals(newTimestamp, events.list[0].timestamp)
        Assert.assertEquals("Changed Event", events.list[0].event)
        Assert.assertEquals(false, events.list[0].showTime)
        Assert.assertEquals("CHANGE", events.list[0].eventType)
        Assert.assertEquals(true, events.list[0].isCustom)
        Assert.assertEquals(docId, events.list[0].documentId)
    }

    @Test
    fun testEditExistingEventCommand_DontUpdateFields(){
        Assert.assertEquals(0, events.list.size)
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)

        val newEvent = Event("Test Event", DomainId.create(), timestamp, docId, "CS", "TEST", true)
        events += newEvent

        Assert.assertEquals(1, events.list.size)

        val editEvent = buildEditEventCommand(newEvent.eventId, null, null, null, null, null)
        handle(editEvent)

        Assert.assertEquals(1,events.list.size)
        Assert.assertEquals(newEvent.eventId, events.list[0].eventId)
        Assert.assertEquals(newEvent.timestamp, events.list[0].timestamp)
        Assert.assertEquals(newEvent.event, events.list[0].event)
        Assert.assertEquals(newEvent.showTime, events.list[0].showTime)
        Assert.assertEquals(newEvent.eventType, events.list[0].eventType)
        Assert.assertEquals(newEvent.isCustom, events.list[0].isCustom)
        Assert.assertEquals(docId, events.list[0].documentId)
    }

    @Test
    fun testEditNotExistingCommand(){
        Assert.assertEquals(0, events.list.size)

        val newEvent = Event(
            "Test Event",
            DomainId.create(),
            Instant.now().truncatedTo(ChronoUnit.SECONDS),
            docId,
            "CS",
            "TEST",
            true
        )
        events += newEvent

        val newEventId = newEvent.eventId
        val newEventTimestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val randomId = DomainId.create<EventId>()

        Assert.assertEquals(1, events.list.size)
        Assert.assertNotEquals(newEventId, randomId)

        val editEvent = buildEditEventCommand(randomId, newEventTimestamp, "Changed Event", false, "CHANGE", false)
        handle(editEvent)

        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals(randomId, events.list[1].eventId)
        Assert.assertEquals(newEventTimestamp, events.list[1].timestamp)
        Assert.assertEquals("Changed Event", events.list[1].event)
        Assert.assertEquals(false, events.list[1].showTime)
        Assert.assertEquals("CHANGE", events.list[1].eventType)
        Assert.assertEquals(false, events.list[1].isCustom)
        Assert.assertEquals(docId, events.list[0].documentId)
        Assert.assertEquals(docId, events.list[1].documentId)
    }

    @Test
    fun testRemoveEventCommand(){
        Assert.assertEquals(0, events.list.size)

        val newEvent = Event(
            "Test Event",
            DomainId.create(),
            Instant.now().truncatedTo(ChronoUnit.SECONDS),
            docId,
            "CS",
            "TEST",
            true
        )
        events += newEvent

        Assert.assertEquals(1, events.list.size)

        val removeEvent = buildRemoveEventCommand(newEvent.eventId)
        handle(removeEvent)

        Assert.assertEquals(0,events.list.size)
        Assert.assertEquals(1, events.onProperRemovedItems.size)
    }

    @Test
    fun testAddCommentCommand(){
        Assert.assertEquals(0, events.list.size)
        val time = Instant.now().truncatedTo(ChronoUnit.SECONDS)

        val newEvent = Event(
            "Test Event",
            DomainId.create(),
            time,
            docId,
            "CS",
            "TEST",
            true
        )
        events += newEvent

        Assert.assertEquals(1, events.list.size)

        val commentCommand = buildAddCommentCommand(
            newEvent.eventId,
            "Comment on Event",
            time.plusSeconds(1)
        )
        handle(commentCommand)

        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals(newEvent, events[newEvent.id])

        val comment = events.list[1]
        Assert.assertEquals(newEvent.eventId, comment.referencedItem)
        Assert.assertEquals("Comment on Event", comment.event)
        Assert.assertEquals(time.plusSeconds(1), comment.timestamp)
        Assert.assertEquals(KnownEventTypes.COMMENT.dataString, comment.eventType)
        Assert.assertTrue(comment.isCustom)
        Assert.assertFalse(comment.showTime)
    }

    @Test
    fun testAddWithId(){
        val id = DomainId.create<DomainId>()
        val doc = Document()

        doc.handle(
            listOf(
                buildDocumentCommand(
                    DomainId.create(),
                    buildCommandData(buildAddEventCommand(Instant.now(),"Sample Event", true, KnownEventTypes.CASEVAC, true, id.copy()))
                ),
            )
        )
        Assert.assertEquals(id, doc.events.list.first().id)
    }
}