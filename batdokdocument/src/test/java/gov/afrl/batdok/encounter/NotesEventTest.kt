package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.Notes.Companion.includeNoteEvents
import gov.afrl.batdok.encounter.commands.buildAddNoteCommand
import gov.afrl.batdok.encounter.commands.buildRemoveNoteCommand
import gov.afrl.batdok.encounter.commands.buildUpdateNoteCommand
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.encounter.ids.NoteId
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class NotesEventTest {
    private val events = Events()
    private val notes = Notes()

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(
        ChronoUnit.SECONDS), commandId: CommandId = DomainId.create()) {
        EventCommandHandler(events) {
            includeNoteEvents(notes)
        }.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp, commandId))
    }

    @Test
    fun testAddNote() {
        val newNote = Note(
            message = "New instructions for patient",
            callsign = "Fred"
        )
        Assert.assertTrue(events.list.isEmpty())

        handle(buildAddNoteCommand(newNote))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Added Note:\n${newNote.message}", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.NOTE.dataString, events.list[0].eventType)
    }

    @Test
    fun testUpdateNote() {
        val oldNote = Note(
            message = "New instructions for patient",
            callsign = "Fred"
        )
        val newNote = oldNote.copy(message = "Amended instructions for patient")
        notes += oldNote
        Assert.assertTrue(events.list.isEmpty())

        handle(buildUpdateNoteCommand(newNote))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Updated Note from:\n\"${oldNote.message}\"\nto:\n\"${newNote.message}\"", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.OTHER.dataString, events.list[0].eventType)
    }

    @Test
    fun testUpdateNote_NoPreExisting() {
        val oldNote = Note(
            message = "New instructions for patient",
            callsign = "Fred"
        )
        val newNote = oldNote.copy(message = "Amended instructions for patient")
        Assert.assertTrue(events.list.isEmpty())

        handle(buildUpdateNoteCommand(newNote))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Added Note:\n${newNote.message}", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.OTHER.dataString, events.list[0].eventType)
    }

    @Test
    fun testRemoveNote() {
        val oldNote = Note(
            message = "New instructions for patient",
            callsign = "Fred"
        )
        notes += oldNote
        Assert.assertTrue(events.list.isEmpty())

        handle(buildRemoveNoteCommand(oldNote.id))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Removed Note:\n\"${oldNote.message}\"", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.OTHER.dataString, events.list[0].eventType)
    }

    @Test
    fun testRemoveNote_NoPreExisting() {
        val oldNote = Note(
            message = "New instructions for patient",
            callsign = "Fred"
        )
        Assert.assertTrue(events.list.isEmpty())

        handle(buildRemoveNoteCommand(oldNote.id))
        Assert.assertEquals(0, events.list.size)
    }

    @Test
    fun testReferencedNoteEvent()
    {
        val noteId = DomainId.create<NoteId>();
        val note = Note(
            id = noteId,
            message = "message",
            callsign = "Fred"
        )
        Assert.assertTrue(events.list.isEmpty())

        handle(buildAddNoteCommand(note))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals(noteId,events.list.first().referencedItem)
    }

}