package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.Subjective.Companion.includeSubjectiveEvents
import gov.afrl.batdok.encounter.commands.*
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class SubjectiveEventTest {

    val subjective = Subjective()
    val events = Events()

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS)) {
        EventCommandHandler(events).apply {
            includeSubjectiveEvents(subjective)
        }.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp))
    }

    @Test
    fun testAddChiefComplaint(){
        //ARRANGE
        val complaint = "Head > Headache > Left Side"

        //ACT
        handle(buildUpdateChiefComplaintCommand(complaint, true))

        //ASSERT
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Added Chief Complaint: Head > Headache > Left Side", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.SUBJECTIVE.dataString, events.list[0].eventType)
    }

    @Test
    fun testRemoveChiefComplaint(){
        //ARRANGE
        val complaint = "Head > Headache > 2 days"

        //ACT
        handle(buildUpdateChiefComplaintCommand(complaint, false))

        //ASSERT
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Removed Chief Complaint: Head > Headache > 2 days", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.SUBJECTIVE.dataString, events.list[0].eventType)
    }

    @Test
    fun testAddComplaint_NoHistory(){
        //ARRANGE
        val complaint = "Head > Headache > Left Side"

        //ACT
        handle(buildAddComplaintCommand(complaint))

        //ASSERT
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Added Complaint: Head > Headache > Left Side", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.SUBJECTIVE.dataString, events.list[0].eventType)
    }

    @Test
    fun testAddComplaint_WithHistory(){
        //ARRANGE
        val complaint = "Sore Throat"
        val history = "It started yesterday"

        //ACT
        handle(buildAddComplaintCommand(complaint, history))

        //ASSERT
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Added Complaint: Sore Throat - It started yesterday", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.SUBJECTIVE.dataString, events.list[0].eventType)
    }

    @Test
    fun testAddComplaint_WithROS(){
        //ARRANGE
        val complaint = "Sore Throat"
        val reviewOfSystems = "+Headache, -Rash"

        //ACT
        handle(buildAddComplaintCommand(complaint, reviewOfSystems = reviewOfSystems))

        //ASSERT
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Added Complaint: Sore Throat - +Headache, -Rash", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.SUBJECTIVE.dataString, events.list[0].eventType)
    }

    @Test
    fun testAddComplaint_WithHistoryAndROS(){
        //ARRANGE
        val complaint = "Sore Throat"
        val history = "It started yesterday"
        val reviewOfSystems = "+Headache, -Rash"

        //ACT
        handle(buildAddComplaintCommand(complaint, history, reviewOfSystems))

        //ASSERT
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Added Complaint: Sore Throat - It started yesterday - +Headache, -Rash", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.SUBJECTIVE.dataString, events.list[0].eventType)
    }

    @Test
    fun testUpdateComplaint(){
        //ARRANGE
        val oldComplaint = Complaint("Complaint", "History")
        val newComplaint = Complaint("Complaint2", "History2", id = oldComplaint.id)
        subjective.complaints += oldComplaint

        //ACT
        handle(buildUpdateComplaintCommand(newComplaint.id, newComplaint.complaint, newComplaint.history))

        //ASSERT
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Updated Complaint: Complaint2 - History2", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.SUBJECTIVE.dataString, events.list[0].eventType)
    }

    @Test
    fun testUpdateComplaint_DontUpdate(){
        //ARRANGE
        val oldComplaint = Complaint("Complaint", "History")
        subjective.complaints += oldComplaint

        //ACT
        handle(buildUpdateComplaintCommand(oldComplaint.id, null, null))

        //ASSERT
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Updated Complaint: Complaint - History", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.SUBJECTIVE.dataString, events.list[0].eventType)
    }

    @Test
    fun testUpdateComplaint_JustUpdateHistory(){
        //ARRANGE
        val oldComplaint = Complaint("Complaint", "History")
        subjective.complaints += oldComplaint

        //ACT
        handle(buildUpdateComplaintCommand(oldComplaint.id, null, "History2"))

        //ASSERT
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Updated Complaint: Complaint - History2", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.SUBJECTIVE.dataString, events.list[0].eventType)
    }

    @Test
    fun testUpdateComplaint_JustUpdateROS(){
        //ARRANGE
        val oldComplaint = Complaint("Sore Throat", reviewOfSystems = "Old ROS")
        subjective.complaints += oldComplaint
        val reviewOfSystems = "+Headache, -Rash"

        //ACT
        handle(buildUpdateComplaintCommand(oldComplaint.id, reviewOfSystems = reviewOfSystems))

        //ASSERT
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Updated Complaint: Sore Throat - +Headache, -Rash", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.SUBJECTIVE.dataString, events.list[0].eventType)
    }

    @Test
    fun testUpdateComplaint_UpdateHistoryAndROS(){
        //ARRANGE
        val oldComplaint = Complaint("Sore Throat", "Old History", "Old ROS")
        subjective.complaints += oldComplaint
        val history = "It started yesterday"
        val reviewOfSystems = "+Headache, -Rash"

        //ACT
        handle(buildUpdateComplaintCommand(oldComplaint.id, history = history, reviewOfSystems = reviewOfSystems))

        //ASSERT
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Updated Complaint: Sore Throat - It started yesterday - +Headache, -Rash", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.SUBJECTIVE.dataString, events.list[0].eventType)
    }

    @Test
    fun testRemoveComplaint(){
        //ARRANGE
        val complaint = Complaint("Head > Headache > 2 days")
        subjective += complaint

        //ACT
        handle(buildRemoveComplaintCommand(complaint.id))

        //ASSERT
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Removed Complaint: Head > Headache > 2 days", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.SUBJECTIVE.dataString, events.list[0].eventType)
    }

    @Test
    fun testRemoveChiefComplaint_WithHistory(){
        //ARRANGE
        val complaint = Complaint("Head > Headache > 2 days", "It started yesterday")
        subjective.complaints += complaint

        //ACT
        handle(buildRemoveComplaintCommand(complaint.id))

        //ASSERT
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Removed Complaint: Head > Headache > 2 days - It started yesterday", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.SUBJECTIVE.dataString, events.list[0].eventType)
    }

    @Test
    fun testAddPositiveReviewOfSystems(){
        //ARRANGE
        val complaint = "Head > Headache > Left Side"

        //ACT
        handle(buildUpdateReviewOfSystemsCommand(complaint, true))

        //ASSERT
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Added positive Review of System: Head > Headache > Left Side", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.SUBJECTIVE.dataString, events.list[0].eventType)
    }

    @Test
    fun testAddNegativeReviewOfSystems(){
        //ARRANGE
        val complaint = "Head > Headache > 2 days"

        //ACT
        handle(buildUpdateReviewOfSystemsCommand(complaint, false))

        //ASSERT
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Added negative Review of System: Head > Headache > 2 days", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.SUBJECTIVE.dataString, events.list[0].eventType)
    }

    @Test
    fun testRemoveReviewOfSystems(){
        //ARRANGE
        val complaint = "Head > Headache > Left Side"

        //ACT
        handle(buildUpdateReviewOfSystemsCommand(complaint, null))

        //ASSERT
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Removed Review of System: Head > Headache > Left Side", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.SUBJECTIVE.dataString, events.list[0].eventType)
    }

    @Test
    fun testUpdateOpqrstlCommand(){
        //ARRANGE
        val opqrstl = Opqrstl(
            Onset.GRADUAL.dataString,
            "Pressure",
            "Touch",
            listOf(
                Quality.BETTER_NOW.dataString,
                "Test"
            ),
            true,
            3,
            100_000_000L,
            1698679054953L
        )

        //ACT
        handle(buildUpdateOpqrstlCommand(opqrstl))

        //ASSERT
        Assert.assertEquals(1, events.list.size)
        try{
            Assert.assertEquals(
                "Updated OPQRST-L - Onset: Gradual; Better when: Pressure; Worse when: Touch; " +
                        "Qualities: Better now, Test; Radiates; Severity: 3; Time: 1d 3h 46m; " +
                        "Last Oral Intake: Oct 30, 11:17",
                events.list[0].event
            )
        } catch(e: Exception){
            Assert.assertEquals(
                "Updated OPQRST-L - Onset: Gradual; Better when: Pressure; Worse when: Touch; " +
                        "Qualities: Better now, Test; Radiates; Severity: 3; Time: 1d 3h 46m; " +
                        "Last Oral Intake: Oct 30, 15:17",
                events.list[0].event
            )
        }
        Assert.assertEquals(KnownEventTypes.SUBJECTIVE.dataString, events.list[0].eventType)
    }

    @Test
    fun testUpdateOpqrstlCommand_SomeItems(){
        //ARRANGE
        val opqrstl = Opqrstl(
            null,
            "",
            "Touch",
            listOf(),
            false,
            null,
            null,
            null
        )

        //ACT
        handle(buildUpdateOpqrstlCommand(opqrstl))

        //ASSERT
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Updated OPQRST-L - Worse when: Touch; Does not Radiate", events.list[0].event)
    }

    @Test
    fun testUpdateOpqrstlCommand_WithExisting(){
        //ARRANGE
        val oldOpqrstl = Opqrstl(
            Onset.GRADUAL.dataString,
            "Pressure",
            "Touch",
            listOf(
                Quality.BETTER_NOW.dataString,
                "Test"
            ),
            true,
            3,
            1000000L,
            1698679054953L
        )
        val opqrstl = Opqrstl(
            null,
            "",
            "",
            listOf(),
            null,
            null,
            null,
            null
        )
        subjective.opqrstl = oldOpqrstl

        //ACT
        handle(buildUpdateOpqrstlCommand(opqrstl))

        //ASSERT
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Cleared OPQRST-L", events.list[0].event)
    }
}