package gov.afrl.batdok.encounter

import gov.afrl.batdok.encounter.Patcat.Companion.getMapOfValidPatCats
import org.junit.Assert
import org.junit.Test


class PatcatTest {

    @Test
    fun verifyGetMapOfValidPatCats_OnlyValidPatcats() {
        getMapOfValidPatCats().values.forEach {
            Assert.assertTrue("${it.getEventDescription()} is not valid", it.isValid())
        }
    }

    @Test
    fun verifyUniqueServiceProtoIndices() {
        Assert.assertEquals(PatcatService.entries.size, PatcatService.entries.groupBy { it.protoIndex }.size)
    }

    @Test
    fun verifyUniqueStatusProtoIndices() {
        Assert.assertEquals(PatcatStatus.entries.size, PatcatStatus.entries.groupBy { it.protoIndex }.size)
    }

    @Test
    fun verifyUniqueServiceDataStrings() {
        Assert.assertEquals(PatcatService.entries.size, PatcatService.entries.groupBy { it.dataString }.size)
    }

    @Test
    fun verifyUniqueStatusDataStrings() {
        Assert.assertEquals(PatcatStatus.entries.size, PatcatStatus.entries.groupBy { it.dataString }.size)
    }

    @Test
    fun verifyThatTwoPatCatsContainingSameValuesAreEqual() {
        Assert.assertEquals(Patcat(PatcatService.ARMY, PatcatStatus.ACTIVE_DUTY), Patcat(PatcatService.ARMY, PatcatStatus.ACTIVE_DUTY))
        Assert.assertEquals(Patcat(serviceCode = "1", statusCode = "2"), Patcat(serviceCode = "1", statusCode = "2"))
        //Since you can't construct a PatCat with a mix of service/status/service code/status code.  The two PatCats below are the same.
        Assert.assertEquals(Patcat(service = null, status = null), Patcat(serviceCode = null, statusCode = null))
    }

    @Test
    fun verifyThatTwoPatCatsContainingDifferentValuesAreNotEqual() {
        Assert.assertNotEquals(Patcat(PatcatService.ARMY, PatcatStatus.ACTIVE_DUTY), Patcat(PatcatService.AIR_FORCE, PatcatStatus.ACTIVE_DUTY))
        Assert.assertNotEquals(Patcat(serviceCode = "1", statusCode = "2"), Patcat(serviceCode = "a", statusCode = "b"))
    }
}