package gov.afrl.batdok.encounter.orders

import gov.afrl.batdok.commands.proto.SharedProtobufObjects.CompatibleEnum
import org.junit.Assert
import org.junit.Test

class OrderStatusTest {
    @Test
    fun testDefaultValue() {
        val proto = CompatibleEnum.newBuilder().setEnum(999).setString("").build()
        val str = OrderStatus.fromProto(proto)
        // ORDERED is the default
        Assert.assertEquals(OrderStatus.ORDERED.dataString, str)
    }
}