package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.commands.*
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.encounter.metadata.*
import gov.afrl.batdok.encounter.metadata.Metadata.Companion.includeMetadataEvents
import gov.afrl.batdok.util.Patterns
import gov.afrl.batdok.util.buildCommandData
import gov.afrl.batdok.util.format
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Clock
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZoneOffset
import java.time.temporal.ChronoUnit

class MetadataEventTest {

    private val metadata = Metadata()
    private val events = Events()

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS), commandId: CommandId = DomainId.create()) {
        EventCommandHandler(events) {
            includeMetadataEvents(metadata)
        }.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp, commandId))
    }
    
    @Test
    fun testDecisionCommand_Add(){
        val decision = Decision.CARDIO.dataString
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeDecisionCommand(decision, true))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Added decision: Cardiovascular", events.list[0].event)
    }
    
    @Test
    fun testDecisionCommand_Remove(){
        val decision = Decision.CARDIO.dataString
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeDecisionCommand(decision, false))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Removed decision: Cardiovascular", events.list[0].event)
    }


    @Test
    fun testCareCommand_Add(){
        val care = CareType.VAC_CHANGE.dataString
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeCareCommand(care, true))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Added care provided: VAC Δ", events.list[0].event)
    }


    @Test
    fun testCareCommand_Remove(){
        val care = CareType.VAC_CHANGE.dataString
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeCareCommand(care, false))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Removed care provided: VAC Δ", events.list[0].event)
    }

    @Test
    fun testChangeInfectionControlPrecautionCommand_Add(){
        val precaution = InfectionControl.AB.dataString
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeInfectionControlPrecautionCommand(precaution, true))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Added infection control precaution: $precaution", events.list[0].event)
    }


    @Test
    fun testChangeInfectionControlPrecautionCommand_Remove(){
        val precaution = InfectionControl.AB.dataString
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeInfectionControlPrecautionCommand(precaution, false))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Removed infection control precaution: $precaution", events.list[0].event)
    }


    @Test
    fun testProcedureCommand_Add_NoExtras(){
        val procedure = KnownProcedures.CHEST_TUBE.dataString
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeProcedureCommand(Procedure(procedure)))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Added procedure performed: Chest Tube", events.list[0].event)
    }
    @Test
    fun testProcedureCommand_Add_WithLineExtras(){
        val procedure = KnownProcedures.LINE.dataString
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeProcedureCommand(Procedure(procedure, LineData(true, true))))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Added procedure performed: Line (Arterial Line, Central Line)", events.list[0].event)
    }

    @Test
    fun testProcedureCommand_Remove(){
        val procedure = KnownProcedures.LINE.dataString
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeProcedureCommand(Procedure(procedure, LineData(true, true)), false))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Removed procedure performed: Line", events.list[0].event)
    }

    @Test
    fun testAddRemoveMajorEventCommands_AddOnly(){
        val majorEvents = listOf(
            MajorEvent("Event", FreeTextData("Free Text")),
            MajorEvent(MajorEvents.BLEEDING.dataString, BleedingData("Arm", "100mL"))
        )
        Assert.assertEquals(0, events.list.size)
        handle(buildAddRemoveMajorEventCommand("Location", majorEvents, listOf()))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Updated events at Location\nAdded Event (Free Text), Bleeding (Arm, 100mL)", events.list[0].event)
    }

    @Test
    fun testAddRemoveMajorEventCommands_RemoveOnly(){
        val majorEvents = listOf(
            MajorEvent("Event", FreeTextData("Free Text")),
            MajorEvent(MajorEvents.BLEEDING.dataString, BleedingData("Arm", "100mL"))
        )
        Assert.assertEquals(0, events.list.size)
        handle(buildAddRemoveMajorEventCommand("Location", listOf(), majorEvents))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Updated events at Location\nRemoved Event (Free Text), Bleeding (Arm, 100mL)", events.list[0].event)
    }

    @Test
    fun testAddRemoveMajorEventCommands_AddAndRemove(){
        val majorEvents = listOf(
            MajorEvent("Event", FreeTextData("Free Text")),
            MajorEvent(MajorEvents.BLEEDING.dataString, BleedingData("Arm", "100mL"))
        )
        val removeEvents = listOf(
            MajorEvent("Remove"),
            MajorEvent(MajorEvents.ARRHYTHMIA.dataString, ArrhythmiaData(true, false))
        )
        Assert.assertEquals(0, events.list.size)
        handle(buildAddRemoveMajorEventCommand("Location", majorEvents, removeEvents))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Updated events at Location\nAdded Event (Free Text), Bleeding (Arm, 100mL)\nRemoved Remove, Arrhythmia (Tachycardia, Wide)", events.list[0].event)
    }

    @Test
    fun testChangeFlightInfoCommand_SetAllFields(){
        val flightInfo = FlightInfo(
            DomainId.create(),
            "Tail",
            "Origin",
            "Dest",
            Instant.now(Clock.fixed(Instant.ofEpochSecond(10000000), ZoneOffset.UTC)),
            Instant.now(Clock.fixed(Instant.ofEpochSecond(20000000), ZoneOffset.UTC)),
            5000,
            "Unit"
        )
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeFlightInfoCommand(flightInfo))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Updated Flight Info: Tail: Tail, Origin: Origin, Destination: Dest, Takeoff: Apr 26, 13:46, Landing: Aug 20, 07:33, Max Altitude: 5000, Unit: Unit"
            , events.list[0].event)
    }

    @Test
    fun testChangeFlightInfoCommand_SetSomeFields(){
        val flightInfo = FlightInfo(
            DomainId.create(),
            "",
            "Origin",
            "",
            Instant.now(Clock.fixed(Instant.ofEpochSecond(10000000), ZoneOffset.UTC)),
            null,
            5000,
            ""
        )
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeFlightInfoCommand(flightInfo))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Updated Flight Info: Origin: Origin, Takeoff: Apr 26, 13:46, Max Altitude: 5000", events.list[0].event)
    }

    @Test
    fun testChangeFlightInfoCommand_Clear(){
        val flightInfo = FlightInfo()
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeFlightInfoCommand(flightInfo))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Cleared flight info", events.list[0].event)
    }

    @Test
    fun testAddPatientMediaCommand(){
        val filename = "Image.jpg"
        Assert.assertEquals(0, events.list.size)
        handle(buildAddMediaFileCommand(filename))
        Assert.assertEquals("Added Media File: $filename", events.list[0].event)
    }

    @Test
    fun testChangeInsuranceCommand_SetAllFields() {
        val insurance = Insurance(
            "Company",
            "123 Example St. Dayton, OH 12345",
            "**************",
            "ABCDEF123456",
            "self"
        )
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeInsuranceCommand(insurance))
        Assert.assertEquals("Set Insurance to Company: ${insurance.companyName} " +
                "Address: ${insurance.companyAddress} " +
                "Phone: ${insurance.companyPhone} " +
                "Policy Number: ${insurance.policyNumber} " +
                "Relation to Policy Holder: ${insurance.relationToPolicyHolder}",
            events.list[0].event)
    }

    @Test
    fun testChangeInsuranceCommand_SetSomeFieldsNullOrBlank() {
        val insurance = Insurance(
            "Company",
            "     ",
            null,
            "ABCDEF123456",
            "self"
        )
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeInsuranceCommand(insurance))
        Assert.assertEquals("Set Insurance to Company: ${insurance.companyName} " +
                "Policy Number: ${insurance.policyNumber} " +
                "Relation to Policy Holder: ${insurance.relationToPolicyHolder}",
            events.list[0].event)
    }
    @Test
    fun testChangeInsuranceCommand_Clear() {
        val insurance = Insurance()
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeInsuranceCommand(insurance))
        Assert.assertEquals("Cleared Insurance", events.list[0].event)
    }

    @Test
    fun testChangeAttendingPhysicianCommand_SetAllFields() {
        val physician = Physician(
            "John Doe",
            "**************",
            "j.doe@unknown"
        )
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeAttendingPhysicianCommand(physician))
        Assert.assertEquals("Set Attending Physician to Name: ${physician.name} " +
                "Phone: ${physician.phone} " +
                "Email: ${physician.email}",
            events.list[0].event)
    }

    @Test
    fun testChangeAttendingPhysicianCommand_SetSomeFieldsEmptyOrBlank() {
        val physician = Physician(
            "     ",
            "",
            "j.doe@unknown"
        )
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeAttendingPhysicianCommand(physician))
        Assert.assertEquals("Set Attending Physician to " +
                "Email: ${physician.email}",
            events.list[0].event)
    }
    @Test
    fun testChangeAttendingPhysicianCommand_Clear() {
        val physician = Physician()
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeAttendingPhysicianCommand(physician))
        Assert.assertEquals("Cleared Attending Physician", events.list[0].event)
    }

    @Test
    fun testSetSignaturePMRPhysicianCommand() {
        Assert.assertEquals(0, events.list.size)
        handle(buildSetSignaturePMRPhysicianCommand(Signature()))
        Assert.assertEquals("Updated PMR Physician Signature", events.list[0].event)

    }

    @Test
    fun testSetSignaturePMRFlightSurgeonCommand() {
        Assert.assertEquals(0, events.list.size)
        handle(buildSetSignaturePMRFlightSurgeonCommand(Signature()))
        Assert.assertEquals("Updated PMR Flight Surgeon Signature", events.list[0].event)

    }

    @Test
    fun testRemoveSignaturePMRPhysicianCommand() {
        Assert.assertEquals(0, events.list.size)
        handle(buildRemoveSignaturePMRPhysicianCommand())
        Assert.assertEquals("Removed PMR Physician Signature", events.list[0].event)

    }

    @Test
    fun testRemoveSignaturePMRFlightSurgeonCommand() {
        Assert.assertEquals(0, events.list.size)
        handle(buildRemoveSignaturePMRFlightSurgeonCommand())
        Assert.assertEquals("Removed PMR Flight Surgeon Signature", events.list[0].event)
    }

    @Test
    fun testDeclareTimeOfDeath_NoPrevious(){
        Assert.assertEquals(0, events.list.size)
        val timestamp = Instant.now()
        val timeDisplay = timestamp.format(Patterns.mdyhm_24_space_comma_space_colon)
        handle(buildChangeDeathDeclarationCommand(DeathDeclaration(timestamp, "Declarer", "CertLevel", "Verification")))
        Assert.assertEquals("Declared Time of Death: $timeDisplay Declarer: Declarer, Level of Medicine: CertLevel, Verification Method: Verification", events.list[0].event)
    }

    @Test
    fun testDeclareTimeOfDeath_WithPrevious(){
        val timestamp = Instant.now()
        val timeDisplay = timestamp.format(Patterns.mdyhm_24_space_comma_space_colon)
        metadata.deathDeclaration = DeathDeclaration(timestamp, "Declarer", "CertLevel", "Verification")
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeDeathDeclarationCommand(metadata.deathDeclaration!!.copy(declarer = "Test", certification = "Test2"), metadata.deathDeclaration))
        Assert.assertEquals("Declared Time of Death: $timeDisplay Declarer: Test, Level of Medicine: Test2, Verification Method: Verification", events.list[0].event)
    }

    @Test
    fun testRemoveDeclarationOfDeath(){
        Assert.assertEquals(0, events.list.size)
        handle(buildRemoveDeathDeclarationCommand("It was a mistake"))
        Assert.assertEquals("Removed Declaration of death: It was a mistake", events.list[0].event)
    }

    @Test
    fun testChangeAppointmentDateCommand() {
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeAppointmentDateCommand(LocalDate.of(1976, 7, 4)))
        Assert.assertEquals("Set Appointment Date: 1976-07-04", events.list[0].event)
    }

    @Test
    fun testClearAppointmentDateCommand() {
        Assert.assertEquals(0, events.list.size)
        handle(buildRemoveAppointmentDateCommand())
        Assert.assertEquals("Cleared Appointment Date", events.list[0].event)
    }
}