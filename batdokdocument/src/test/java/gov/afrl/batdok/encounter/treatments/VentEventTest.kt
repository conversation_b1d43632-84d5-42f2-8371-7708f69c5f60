package gov.afrl.batdok.encounter.treatments

import com.google.protobuf.Message
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.*
import gov.afrl.batdok.encounter.VentValues.Companion.includeVentEvents
import gov.afrl.batdok.encounter.commands.buildLogVentCommand
import gov.afrl.batdok.encounter.commands.buildRemoveVentCommand
import gov.afrl.batdok.encounter.commands.buildUpdateVentCommand
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.encounter.vitals.EtCO2
import gov.afrl.batdok.encounter.vitals.PIP
import gov.afrl.batdok.util.Patterns
import gov.afrl.batdok.util.buildCommandData
import gov.afrl.batdok.util.format
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class VentEventTest {

    val ventValues = VentValues()
    val vitals = Vitals()
    val events = Events()
    val testVital = EncounterVital(DomainId.create(), Instant.now(), documentId = DomainId.create()) + PIP(12) + EtCO2(13)
    //region Helper functions
    init {
        vitals += testVital
    }
    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant? = Instant.now().truncatedTo(ChronoUnit.SECONDS), commandId: CommandId = DomainId.create()) {
        EventCommandHandler(events) {
            includeVentEvents(ventValues, vitals)
        }.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp, commandId))
    }

    @Test
    fun testAddVent() {
        //ASSIGN
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val vent = VentSettings(
            DomainId.create(),
            timestamp,
            mode = "MODE",
            fio2 = 1.3,
            rate = 3,
            tv = 100,
            peep = 4.5,
            ventilator = "TEST",
            associatedVital = testVital
        )
        //ACT
        Assert.assertEquals(0, events.list.size)
        handle(buildLogVentCommand(vent), timestamp = vent.time, commandId = vent.id.copy())

        //ASSERT
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Logged Vent Settings - Time: ${timestamp.format(Patterns.hm_24_colon)}, Type: TEST, Mode: MODE, Rate: 3, TV: 100, PEEP: 4.5, FiO2: 1.3, EtCO2: 13, PIP: 12", events.list[0].event)
        Assert.assertEquals(vent.time, events.list[0].timestamp)
        Assert.assertEquals(KnownEventTypes.TREAT.dataString, events.list[0].eventType)
    }

    @Test
    fun testAddVent_Some_Missing_Fields() {
        //ASSIGN
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val vent = VentSettings(
            DomainId.create(),
            timestamp,
            mode = "MODE"
        )

        //ACT
        Assert.assertEquals(0, events.list.size)
        handle(buildLogVentCommand(vent), timestamp = vent.time, commandId = vent.id.copy())

        //ASSERT
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Logged Vent Settings - Time: ${timestamp.format(Patterns.hm_24_colon)}, Mode: MODE", events.list[0].event)
        Assert.assertEquals(vent.time, events.list[0].timestamp)
        Assert.assertEquals(KnownEventTypes.TREAT.dataString, events.list[0].eventType)
    }

    @Test
    fun testAddVent_All_Missing_Fields() {
        //ASSIGN
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val vent = VentSettings(
            DomainId.create(),
            timestamp
        )

        //ACT
        Assert.assertEquals(0, events.list.size)
        handle(buildLogVentCommand(vent), timestamp = vent.time, commandId = vent.id.copy())

        //ASSERT
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Logged Vent Settings - Time: ${timestamp.format(Patterns.hm_24_colon)}", events.list[0].event)
        Assert.assertEquals(vent.time, events.list[0].timestamp)
        Assert.assertEquals(KnownEventTypes.TREAT.dataString, events.list[0].eventType)
    }

    @Test
    fun testUpdateEvent(){
        //ASSIGN
        val oldVent = VentSettings(
            DomainId.create(),
            Instant.now().truncatedTo(ChronoUnit.SECONDS).minusSeconds(100),
            mode = "MODE2",
            fio2 = 2.6,
            rate = 9,
            tv = 394,
            peep = 6.2,
            ventilator = "OldVent",
            associatedVital = testVital
        )
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val vent = VentSettings(
            DomainId.create(),
            timestamp,
            mode = "MODE",
            fio2 = 1.3,
            rate = 3,
            tv = 100,
            peep = 4.5,
            ventilator = "TEST"
        )
        ventValues.ventSettingsList += oldVent
        events += Event("Test", vent.id.copy(), timestamp = oldVent.time)

        //ACT
        Assert.assertEquals(1, events.list.size)
        handle(buildUpdateVentCommand(vent, oldVent))

        //ASSERT
        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals("Updated Vent Settings - Time: ${timestamp.format(Patterns.hm_24_colon)}, Type: TEST, Mode: MODE, Rate: 3, TV: 100, PEEP: 4.5, FiO2: 1.3", events.list[1].event)
        Assert.assertEquals(vent.time, events.list[1].timestamp)
        Assert.assertEquals(KnownEventTypes.TREAT.dataString, events.list[1].eventType)
        Assert.assertEquals(vent.id, events.list[1].referencedItem)
    }

    @Test
    fun testUpdateEvent_SameFields(){
        //ASSIGN
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val vent = VentSettings(
            DomainId.create(),
            timestamp,
            mode = "MODE",
            fio2 = 1.3,
            rate = 3,
            tv = 100,
            peep = 4.5,
            ventilator = "TEST",
            associatedVital = testVital
        )
        ventValues.ventSettingsList += vent
        events += Event("Test", vent.id.copy(), timestamp = vent.time)

        //ACT
        Assert.assertEquals(1, events.list.size)
        handle(buildUpdateVentCommand(vent, vent))

        //ASSERT
        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals("Updated Vent Settings - Time: ${timestamp.format(Patterns.hm_24_colon)}, Type: TEST, Mode: MODE, Rate: 3, TV: 100, PEEP: 4.5, FiO2: 1.3, EtCO2: 13, PIP: 12", events.list[1].event)
        Assert.assertEquals(vent.time, events.list[1].timestamp)
        Assert.assertEquals(KnownEventTypes.TREAT.dataString, events.list[1].eventType)
        Assert.assertEquals(vent.id, events.list[1].referencedItem)
    }

    @Test
    fun testUpdateEvent_SomeMissingFields(){
        //ASSIGN
        val oldVent = VentSettings(
            DomainId.create(),
            Instant.now().truncatedTo(ChronoUnit.SECONDS).minusSeconds(100),
            mode = "MODE2",
            fio2 = 2.6,
            rate = 9,
            tv = 394,
            peep = 6.2,
            ventilator = "OldVent",
            associatedVital = testVital
        )
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val vent = VentSettings(
            DomainId.create(),
            timestamp,
            mode = "MODE"
        )
        ventValues.ventSettingsList += oldVent
        events += Event("Test", vent.id.copy(), timestamp = oldVent.time)

        //ACT
        Assert.assertEquals(1, events.list.size)
        handle(buildUpdateVentCommand(vent, oldVent))

        //ASSERT
        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals("Updated Vent Settings - Time: ${vent.time?.format(Patterns.hm_24_colon)}, Mode: MODE", events.list[1].event)
        Assert.assertEquals(vent.time, events.list[1].timestamp)
        Assert.assertEquals(KnownEventTypes.TREAT.dataString, events.list[1].eventType)
        Assert.assertEquals(vent.id, events.list[1].referencedItem)
    }

    @Test
    fun testUpdateVent_All_Missing_Fields() {
        //ASSIGN
        val oldVent = VentSettings(
            DomainId.create(),
            Instant.now().truncatedTo(ChronoUnit.SECONDS).minusSeconds(100),
            mode = "MODE2",
            fio2 = 2.6,
            rate = 9,
            tv = 394,
            peep = 6.2,
            ventilator = "OldVent",
            associatedVital = testVital
        )
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val vent = VentSettings(
            DomainId.create(),
            timestamp
        )
        ventValues.ventSettingsList += oldVent
        events += Event("Test", vent.id.copy(), timestamp = oldVent.time)

        //ACT
        Assert.assertEquals(1, events.list.size)
        handle(buildUpdateVentCommand(vent, oldVent))

        //ASSERT
        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals("Updated Vent Settings - Time: ${timestamp.format(Patterns.hm_24_colon)}", events.list[1].event)
        Assert.assertEquals(vent.time, events.list[1].timestamp)
        Assert.assertEquals(KnownEventTypes.TREAT.dataString, events.list[1].eventType)
        Assert.assertEquals(vent.id, events.list[1].referencedItem)
    }

    @Test
    fun testRemoveVent() {
        //ASSIGN
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val vent = VentSettings(
            DomainId.create(),
            timestamp
        )
        ventValues += vent
        events += Event("Test", vent.id.copy(), timestamp = vent.time)

        //ACT
        Assert.assertEquals(1, events.list.size)
        handle(buildRemoveVentCommand(vent.id))

        //ASSERT
        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals("Removed Vent Settings - Time: ${timestamp.format(Patterns.hm_24_colon)}", events.list[1].event)
        Assert.assertEquals(vent.time, events.list[1].timestamp)
        Assert.assertEquals(KnownEventTypes.TREAT.dataString, events.list[1].eventType)
        Assert.assertEquals(vent.id, events.list[1].referencedItem)
    }
}