package gov.afrl.batdok.encounter


import com.google.gson.*
import com.google.gson.annotations.SerializedName
import gov.afrl.batdok.encounter.medicine.Drip
import gov.afrl.batdok.encounter.medicine.Drips
import gov.afrl.batdok.encounter.medicine.Medicine
import gov.afrl.batdok.encounter.medicine.Medicines
import gov.afrl.batdok.encounter.metadata.*
import gov.afrl.batdok.encounter.metadata.LineData
import gov.afrl.batdok.encounter.movement.*
import gov.afrl.batdok.encounter.observation.Blood
import gov.afrl.batdok.encounter.observation.BloodList
import gov.afrl.batdok.encounter.observation.Observation
import gov.afrl.batdok.encounter.observation.UltrasoundData
import gov.afrl.batdok.encounter.treatment.*
import gov.afrl.batdok.encounter.vitals.HR
import gov.afrl.batdok.util.*
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Assert.assertEquals
import org.junit.Test
import java.io.*
import java.time.Instant
import java.time.LocalDate
import java.time.temporal.ChronoUnit


class SerializableTest {

    @Suppress("UNCHECKED_CAST")
    private fun <T: Serializable> T.serializeAndBack() = ByteArrayOutputStream().use { baos ->
        ObjectOutputStream(baos).use { oos ->
            oos.writeObject(this)
        }
        println(baos.toByteArray().joinToString("") { "%02x".format(it) })
        println(baos.toString(Charsets.ISO_8859_1))
        ByteArrayInputStream(baos.toByteArray()).use{ bais ->
            ObjectInputStream(bais).use { ois ->
                ois.readObject() as T
            }
        }
    }.serializeAndBackGson()
    private fun <T: Serializable> T.serializeAndBackGson() = EncounterGsonBuilder.build()
        .setPrettyPrinting()
        .create().let { gson ->
            val json = gson.toJson(this)
            println(json)
            gson.fromJson(json, this::class.java)
        }

    @Test fun gson_broke(){
        open class MyClass {
           @SerializedName("field_base")
//            @Transient
            open var field = 22
        }
        open class SubClass : MyClass() {
//            @SerializedName("field")
            override var field = 25
            val b = 12
        }

        val gson = GsonBuilder().setPrettyPrinting().create()

        val orig_base = MyClass()
        orig_base.field = 33
        val json_base = gson.toJson(orig_base)
        println(json_base)
        val other_base = gson.fromJson(json_base,MyClass::class.java)

        assertEquals(orig_base.field, other_base.field)

        val orig = SubClass()
        orig.field = 33
        val json = gson.toJson(orig)
        println(json)
        val other = gson.fromJson(json,SubClass::class.java)

        assertEquals(orig.field, other.field)
        assertEquals(orig.b, other.b)
    }


    @Test
    fun verifySerilizationDoesNotCrash(){
        Checklist().serializeAndBack()
        ChecklistItem("", "").serializeAndBack()
        Diagnosis().serializeAndBack()
        History().serializeAndBack()
        Info().serializeAndBack()
        Name("", "", "").serializeAndBack()
        DrawingPoint("", 0f, 0f).serializeAndBack()
        Injuries().serializeAndBack()
        InputOutput(DomainId.create(), Instant.now(), "", "", 0.0, "").serializeAndBack()
        InputOutputs().serializeAndBack()
        Labs().serializeAndBack()
        Observations().serializeAndBack()
        Opqrstl().serializeAndBack()
        Subjective().serializeAndBack()
        Treatments().serializeAndBack()
        VentValues().serializeAndBack()
        EncounterVital(DomainId.create(), Instant.now()).serializeAndBack()
        HR(1, "").serializeAndBack()
        Vitals().serializeAndBack()
        MedMissionNumber().serializeAndBack()
        NinelineLocation().serializeAndBack()
        Drip(Medicine(""), Instant.now(), mutableListOf()).serializeAndBack()
        Drips().serializeAndBack()
        Medicine("").serializeAndBack()
        Medicines().serializeAndBack()
        Equipment("").serializeAndBack()
        FlightInfo().serializeAndBack()
        EquipmentLog().serializeAndBack()
        MajorEvent("").serializeAndBack()
        MajorEventLog().serializeAndBack()
        Metadata().serializeAndBack()
        Procedure("").serializeAndBack()
        LineData().serializeAndBack()
        BloodList().serializeAndBack()
        Blood().serializeAndBack()
        Observation().serializeAndBack()
        UltrasoundData().serializeAndBack()
        Treatment().serializeAndBack()
        TqData("").serializeAndBack()
        Event("", DomainId.create(), Instant.now()).serializeAndBack()
        Movement().serializeAndBack()
        Panels().serializeAndBack()
        Signatures().serializeAndBack()
        HistoricalHL7Data().serializeAndBack()
    }

    @Test
    fun verifySerilizationDoesNotCrash_Gson(){
        Checklist().serializeAndBackGson()
        ChecklistItem("", "").serializeAndBackGson()
        Diagnosis().serializeAndBackGson()
        History().serializeAndBackGson()
        Info().serializeAndBackGson()
        Name("", "", "").serializeAndBackGson()
        DrawingPoint("", 0f, 0f).serializeAndBackGson()
        Injuries().serializeAndBackGson()
        InputOutput(DomainId.create(), Instant.now(), "", "", 0.0, "").serializeAndBackGson()
        InputOutputs().serializeAndBackGson()
        Labs().serializeAndBackGson()
        Observations().serializeAndBackGson()
        Opqrstl().serializeAndBackGson()
        Subjective().serializeAndBackGson()
        Treatments().serializeAndBackGson()
        VentValues().serializeAndBackGson()
        EncounterVital(DomainId.create(), Instant.now()).serializeAndBackGson()
        HR(1, "").serializeAndBackGson()
        Vitals().serializeAndBackGson()
        MedMissionNumber().serializeAndBackGson()
        NinelineLocation().serializeAndBackGson()
        Drip(Medicine(""), Instant.now(), mutableListOf()).serializeAndBackGson()
        Drips().serializeAndBackGson()
        Medicine("").serializeAndBackGson()
        Medicines().serializeAndBackGson()
        Equipment("").serializeAndBackGson()
        FlightInfo().serializeAndBackGson()
        EquipmentLog().serializeAndBackGson()
        MajorEvent("").serializeAndBackGson()
        MajorEventLog().serializeAndBackGson()
        Metadata().serializeAndBackGson()
        Procedure("").serializeAndBackGson()
        LineData().serializeAndBackGson()
        BloodList().serializeAndBackGson()
        Blood().serializeAndBackGson()
        Observation().serializeAndBackGson()
        UltrasoundData().serializeAndBackGson()
        Treatment().serializeAndBackGson()
        TqData("").serializeAndBackGson()
        Event("", DomainId.create(), Instant.now()).serializeAndBackGson()
        Movement().serializeAndBackGson()
        Panels().serializeAndBackGson()
        Signatures().serializeAndBackGson()
        HistoricalHL7Data().serializeAndBackGson()
    }


    @Test
    fun testSerializableMedicine(){
        val med = Medicine("Name", "NDC", "RxCUI", Instant.now(), DomainId.create(), "Route", 12.34f, "Unit", "SerialNumber", "Expiration", "Type", DomainId.create())
        val newMed = med.serializeAndBack()

        assertEquals(med.name, newMed.name)
        assertEquals(med.ndc, newMed.ndc)
        assertEquals(med.rxcui, newMed.rxcui)
        assertEquals(med.administrationTime, newMed.administrationTime)
        assertEquals(med.medId, newMed.medId)
        assertEquals(med.route, newMed.route)
        assertEquals(med.volume, newMed.volume)
        assertEquals(med.unit, newMed.unit)
        assertEquals(med.serialNumber, newMed.serialNumber)
        assertEquals(med.expirationDate, newMed.expirationDate)
        assertEquals(med.type, newMed.type)
        assertEquals(med.documentId, newMed.documentId)
    }

    @Test
    fun testSerializableTreatment(){
        val treatment = Treatment(
            CommonTreatments.TQ.dataString,
            TqData(
                TqData.Location.TRUNCAL.dataString,
                "Type",
                TqData.SubLocation.PELVIS.dataString,
                Instant.now().plusSeconds(1),
                Instant.now().plusSeconds(2),
                Instant.now().plusSeconds(3),
                Instant.now().plusSeconds(4)
            )
        )
        val newTreatment = treatment.serializeAndBack()

        assertEquals(treatment.name, newTreatment.name)
        assertEquals(treatment.id, newTreatment.id)
        assertEquals(treatment.timestamp, newTreatment.timestamp)
        assertEquals(treatment.documentId, newTreatment.documentId)

        val treatmentData = treatment.getData<TqData>()
        val newTreatmentData = newTreatment.getData<TqData>()

        assertEquals(treatmentData?.tqLocation, newTreatmentData?.tqLocation)
        assertEquals(treatmentData?.tqType, newTreatmentData?.tqType)
        assertEquals(treatmentData?.subLocation, newTreatmentData?.subLocation)
        assertEquals(treatmentData?.reapplicationTime, newTreatmentData?.reapplicationTime)
        assertEquals(treatmentData?.conversionTime, newTreatmentData?.conversionTime)
        assertEquals(treatmentData?.removalTime, newTreatmentData?.removalTime)
        assertEquals(treatmentData?.reassessTime, newTreatmentData?.reassessTime)
        assertEquals(treatmentData?.drawingPoint, newTreatmentData?.drawingPoint)
    }

    @Test
    fun testBlood(){
        val blood = Blood()
        val newBlood = blood.serializeAndBack()

        assertEquals(blood.bloodId, newBlood.bloodId)
        assertEquals(blood.administrationTime, newBlood.administrationTime)
        assertEquals(blood.bloodProduct, newBlood.bloodProduct)
        assertEquals(blood.bloodType, newBlood.bloodType)
        assertEquals(blood.donationIdNumber, newBlood.donationIdNumber)
        assertEquals(blood.expirationDate, newBlood.expirationDate)
        assertEquals(blood.bloodAge, newBlood.bloodAge)
        assertEquals(blood.volume, newBlood.volume)
        assertEquals(blood.unit, newBlood.unit)
        assertEquals(blood.docId, newBlood.docId)
    }
    
    @Test
    fun testEvac(){
        val movementRecord = Movement().apply {
            medMissionNumber = MedMissionNumber("123", "456789")
            tailToTail = true
            legNumber = 1
            totalLegs = 3
            ninelineTime = Instant.now()
            dispatchEvac = "Urgent"
            pickupLocation = NinelineLocation(
                Instant.now().plusSeconds(1),
                "Role1",
                "Region1",
                "Location1"
            )
            dropoffLocation = NinelineLocation(
                Instant.now().plusSeconds(2),
                "Role2",
                "Region2",
                "Location2"
            )
            capability = listOf("Cap")
            ninelinePlatform = "Platform"
            originatingMtf = "Originating"
            destinationMtf = "Destination"
            reasonRegulated = ReasonRegulated.BA.toString()
            maxStops = 5
            maxRons = 7
            maxNumOfRons = MaxNumOfRons.SEVEN.dataString
            altitudeRestrictions = "Restrictions"
            flightLevel = "Level"
            readyDate = Instant.now().plusSeconds(4)
            localReadyDate = LocalDate.now()
            medicalAttendantsNeeded = 1
            nonMedicalAttendantsNeeded = 2
            attendants = listOf(
                Attendant(Name("John Middle Doe"),
                "Male",
                175.2f,
                "7th",
                true)
            )
            classification = "Class"
            precedence = "None"
            criticalCare = false
        }
        val newEvac = movementRecord.serializeAndBack()

        assertEquals(movementRecord.medMissionNumber, newEvac.medMissionNumber)
        assertEquals(movementRecord.tailToTail, newEvac.tailToTail)
        assertEquals(movementRecord.legNumber, newEvac.legNumber)
        assertEquals(movementRecord.totalLegs, newEvac.totalLegs)
        assertEquals(movementRecord.ninelineTime, newEvac.ninelineTime)
        assertEquals(movementRecord.dispatchEvac, newEvac.dispatchEvac)
        assertEquals(movementRecord.pickupLocation, newEvac.pickupLocation)
        assertEquals(movementRecord.dropoffLocation, newEvac.dropoffLocation)
        assertEquals(movementRecord.capability, newEvac.capability)
        assertEquals(movementRecord.ninelinePlatform, newEvac.ninelinePlatform)
        assertEquals(movementRecord.originatingMtf, newEvac.originatingMtf)
        assertEquals(movementRecord.destinationMtf, newEvac.destinationMtf)
        assertEquals(movementRecord.reasonRegulated, newEvac.reasonRegulated)
        assertEquals(movementRecord.maxStops, newEvac.maxStops)
        assertEquals(movementRecord.maxRons, newEvac.maxRons)
        assertEquals(movementRecord.maxNumOfRons, newEvac.maxNumOfRons)
        assertEquals(movementRecord.altitudeRestrictions, newEvac.altitudeRestrictions)
        assertEquals(movementRecord.flightLevel, newEvac.flightLevel)
        assertEquals(movementRecord.readyDate, newEvac.readyDate)
        assertEquals(movementRecord.medicalAttendantsNeeded, newEvac.medicalAttendantsNeeded)
        assertEquals(movementRecord.nonMedicalAttendantsNeeded, newEvac.nonMedicalAttendantsNeeded)
        assertEquals(movementRecord.attendants[0].name, newEvac.attendants[0].name)
        assertEquals(movementRecord.attendants[0].gender, newEvac.attendants[0].gender)
        assertEquals(movementRecord.attendants[0].weight, newEvac.attendants[0].weight)
        assertEquals(movementRecord.attendants[0].grade, newEvac.attendants[0].grade)
        assertEquals(movementRecord.attendants[0].isMedical, newEvac.attendants[0].isMedical)
        assertEquals(movementRecord.getMedicalAttendants(), newEvac.getMedicalAttendants())
        assertEquals(movementRecord.getNonMedicalAttendants(), newEvac.getNonMedicalAttendants())
        assertEquals(movementRecord.classification, newEvac.classification)
        assertEquals(movementRecord.precedence, newEvac.precedence)
        assertEquals(movementRecord.criticalCare, newEvac.criticalCare)
    }

    @Test
    fun testAttendant(){
        val attendant = Attendant().apply {
            name = Name("First Middle Last")
            gender = "Male"
            weight = 175.2f
            grade = "1st Lieutenant"
            isMedical = false
        }
        val newAttendant = attendant.serializeAndBack()

        assertEquals(attendant.name?.first, newAttendant.name?.first)
        assertEquals(attendant.name?.middle, newAttendant.name?.middle)
        assertEquals(attendant.name?.last, newAttendant.name?.last)
        assertEquals(attendant.gender, newAttendant.gender)
        assertEquals(attendant.weight, newAttendant.weight)
        assertEquals(attendant.grade, newAttendant.grade)
        assertEquals(attendant.isMedical, newAttendant.isMedical)
    }

    @Test
    fun testDrips(){
        val drips = Drips()
        drips += Drip(
            Medicine("Base"),
            Instant.now(),
            mutableListOf(Medicine("Med1")),
            Instant.now().plusSeconds(1),
            Instant.now().plusSeconds(2),
            DomainId.create()
        )

        val newDrips = drips.serializeAndBack()
        assertEquals(drips.drips.size, newDrips.drips.size)
        assertEquals(drips.drips[0].base, newDrips.drips[0].base)
        assertEquals(drips.drips[0].baseTimestamp, newDrips.drips[0].baseTimestamp)
        assertEquals(drips.drips[0].startTime, newDrips.drips[0].startTime)
        assertEquals(drips.drips[0].endTime, newDrips.drips[0].endTime)
        assertEquals(drips.drips[0].documentId, newDrips.drips[0].documentId)

        val med = drips.drips[0].components[0]
        val newMed = newDrips.drips[0].components[0]

        assertEquals(med.name, newMed.name)
        assertEquals(med.ndc, newMed.ndc)
        assertEquals(med.administrationTime, newMed.administrationTime)
        assertEquals(med.medId, newMed.medId)
        assertEquals(med.route, newMed.route)
        assertEquals(med.volume, newMed.volume)
        assertEquals(med.unit, newMed.unit)
        assertEquals(med.serialNumber, newMed.serialNumber)
        assertEquals(med.expirationDate, newMed.expirationDate)
        assertEquals(med.type, newMed.type)
        assertEquals(med.documentId, newMed.documentId)
    }

    @Test
    fun testPhysician() {
        val physicianOrig = Physician(
            name = "Test",
            phone = "     ",
            email = null
        )
        val physicianNew = physicianOrig.serializeAndBack()
        assertEquals(physicianOrig.name, physicianNew.name)
        assertEquals(physicianOrig.phone, physicianNew.phone)
        assertEquals(physicianOrig.email, physicianNew.email)
    }

    @Test
    fun testInsurance() {
        val insuranceOrig = Insurance(
            companyName = "Test",
            companyAddress = "TestAddress",
            companyPhone = "TestPhone",
            policyNumber = "     ",
            relationToPolicyHolder = null
        )
        val insuranceNew = insuranceOrig.serializeAndBack()
        assertEquals(insuranceOrig.companyName, insuranceNew.companyName)
        assertEquals(insuranceOrig.companyPhone, insuranceNew.companyPhone)
        assertEquals(insuranceOrig.companyAddress, insuranceNew.companyAddress)
        assertEquals(insuranceOrig.policyNumber, insuranceNew.policyNumber)
        assertEquals(insuranceOrig.relationToPolicyHolder, insuranceNew.relationToPolicyHolder)
    }

    @Test
    fun testSignature() {
        val signature = Signature(
            name = "Test Guy",
            timeStamp = Instant.now().truncatedTo(ChronoUnit.SECONDS),
            signature = "Test".toByteArray()
        )
        val signatureNew = signature.serializeAndBack()
        assertEquals(signature.name, signatureNew.name)
        assertEquals(signature.timeStamp, signatureNew.timeStamp)
        Assert.assertTrue(signature.signature.contentEquals(signatureNew.signature))
    }

    @Test
    fun testNote() {
        val note = Note(
            message = "hello world",
            id = DomainId.create("11111111"),
            timeStamp = Instant.now(),
            callsign = "main"
        )
        val noteNew = note.serializeAndBack()
        assertEquals(note, noteNew)
    }

    @Test
    fun testNotes() {
        val note1 = Note(
            message = "hello world",
            id = DomainId.create("11111111"),
            timeStamp = Instant.now(),
            callsign = "main"
        )
        val note2 = Note(
            message = "void main()",
            id = DomainId.create("12345612"),
            timeStamp = Instant.now().minusSeconds(1000),
            callsign = "main2"
        )
        val notes = Notes()
        notes += note1
        notes += note2
        val notesNew = notes.serializeAndBack()
        assertEquals(notes.list.size, notesNew.list.size)
        assertEquals(notes.list.first(), notesNew.list.first())
        assertEquals(notes.list.last(), notesNew.list.last())
    }

    @Test
    fun testCustomAction() {
        val action = CustomAction(
            id = DomainId.create("12345678"),
            timestamp = Instant.now(),
            description = "This is a custom action.",
            message = "kafrizzle!",
            callSign = "borya"
        )
        val actionNew = action.serializeAndBack()
        assertEquals(action, actionNew)
    }

    @Test
    fun testCustomActions() {
        val action1 = CustomAction(
            id = DomainId.create("12345678"),
            timestamp = Instant.now(),
            description = "This is a custom action.",
            message = "kafrizzle!",
            callSign = "borya"
        )
        val action2 = CustomAction(
            id = DomainId.create("77777777"),
            timestamp = Instant.now().plusSeconds(1000),
            description = "This is a different custom action.",
            message = "multiheal!",
            callSign = "kiryl"
        )
        val actions = CustomActions()
        actions += action1
        actions += action2
        val actionsNew = actions.serializeAndBack()
        assertEquals(actions.actions.size, actionsNew.actions.size)
        assertEquals(actions.actions.first(), actionsNew.actions.first())
        assertEquals(actions.actions.last(), actionsNew.actions.last())
    }
    @Test
    fun `test doc with commands`(){
        val doc = Document()
//        doc.handle(listOf(buildCommandData(buildChangeInjuryTimeCommand(Instant.now()))))
        val egson = EncounterGsonBuilder.build().create()
        val json = egson.toJson(doc, Document::class.java)
        val newDoc = egson.fromJson(json, Document::class.java)

        // Add assertions to check all parameters for equality
        assertEquals(doc.id, newDoc.id)

        assertEquals(doc.diagnosis?.category, newDoc.diagnosis?.category)
        assertEquals(doc.diagnosis?.differentials, newDoc.diagnosis?.differentials)
        assertEquals(doc.diagnosis?.selectedProtocol, newDoc.diagnosis?.selectedProtocol)

        assertEquals(doc.injuries?.mechanismsOfInjury, newDoc.injuries?.mechanismsOfInjury)
        assertEquals(doc.injuries?.drawingPoints, newDoc.injuries?.drawingPoints)
        assertEquals(doc.injuries?.mechanismsOfInjury, newDoc.injuries?.mechanismsOfInjury)
        assertEquals(doc.injuries?.tbsa, newDoc.injuries?.tbsa)

        assertEquals(doc.treatments.list, newDoc.treatments.list)

        assertEquals(doc.checklist.size, newDoc.checklist.size)
        assertEquals(doc.checklist.checklistItems, newDoc.checklist.checklistItems)

        assertEquals(doc.vitals.size, newDoc.vitals.size)
        assertEquals(doc.vitals.list, newDoc.vitals.list)

        assertEquals(doc.ventValues.vitals.size, newDoc.ventValues.vitals.size)
        assertEquals(doc.ventValues.vitals.list, newDoc.ventValues.vitals.list)
        assertEquals(doc.ventValues.ventSettingsList, newDoc.ventValues.ventSettingsList)


        assertEquals(doc.subjective.opqrstl, newDoc.subjective.opqrstl)
        assertEquals(doc.subjective.reviewOfSystems, newDoc.subjective.reviewOfSystems)
        assertEquals(doc.subjective.complaints.size, newDoc.subjective.complaints.size)
        assertEquals(doc.subjective.complaints.list, newDoc.subjective.complaints.list)

        assertEquals(doc.history.histories, newDoc.history.histories)
        assertEquals(doc.history.historyOfPresentIllness, newDoc.history.historyOfPresentIllness)
        assertEquals(doc.history.lastEventTimes, newDoc.history.lastEventTimes)


        assertEquals(doc.inputOutputs.inputs, newDoc.inputOutputs.inputs)
        assertEquals(doc.inputOutputs.outputs, newDoc.inputOutputs.outputs)

        assertEquals(doc.intakeOutputs.outputs, newDoc.intakeOutputs.outputs)
        assertEquals(doc.intakeOutputs.inputs, newDoc.intakeOutputs.inputs)
        assertEquals(doc.intakeOutputs.size, newDoc.intakeOutputs.size)
        assertEquals(doc.intakeOutputs.list, newDoc.intakeOutputs.list)
        assertEquals(doc.intakeOutputs.onErrorRemovedItems, newDoc.intakeOutputs.onErrorRemovedItems)
        assertEquals(doc.intakeOutputs.onProperRemovedItems, newDoc.intakeOutputs.onProperRemovedItems)

        assertEquals(doc.labs.size, newDoc.labs.size)
        assertEquals(doc.labs.list, newDoc.labs.list)

        assertEquals(doc.panels.panels, newDoc.panels.panels)
        assertEquals(doc.panels.size, newDoc.panels.size)
        assertEquals(doc.panels.list, newDoc.panels.list)
        assertEquals(doc.panels.onErrorRemovedItems, newDoc.panels.onErrorRemovedItems)
        assertEquals(doc.panels.onProperRemovedItems, newDoc.panels.onProperRemovedItems)

        assertEquals(doc.metadata.signatures.pmrPhysicianSignature, newDoc.metadata.signatures.pmrPhysicianSignature)
        assertEquals(doc.metadata.signatures.pmrFlightSurgeonSignature, newDoc.metadata.signatures.pmrFlightSurgeonSignature)

        assertEquals(doc.metadata.deathDeclaration, newDoc.metadata.deathDeclaration)
        assertEquals(doc.metadata.appointmentDate, newDoc.metadata.appointmentDate)

        assertEquals(doc.metadata.decisionsMade, newDoc.metadata.decisionsMade)
        assertEquals(doc.metadata.careProvided, newDoc.metadata.careProvided)
        assertEquals(doc.metadata.equipment, newDoc.metadata.equipment)
        assertEquals(doc.metadata.proceduresDone, newDoc.metadata.proceduresDone)
        assertEquals(doc.metadata.majorEvents.events, newDoc.metadata.majorEvents.events)
        assertEquals(doc.metadata.flightInfo, newDoc.metadata.flightInfo)
        assertEquals(doc.metadata.medialist, newDoc.metadata.medialist)
        assertEquals(doc.metadata.infectionControlPrecautions, newDoc.metadata.infectionControlPrecautions)
        assertEquals(doc.metadata.insurance, newDoc.metadata.insurance)
        assertEquals(doc.metadata.attendingPhysician.name, newDoc.metadata.attendingPhysician.name)
        assertEquals(doc.metadata.attendingPhysician.phone, newDoc.metadata.attendingPhysician.phone)
        assertEquals(doc.metadata.attendingPhysician.email, newDoc.metadata.attendingPhysician.email)

        assertEquals(doc.metadata.equipmentLog.used, newDoc.metadata.equipmentLog.used)
        assertEquals(doc.metadata.equipmentLog.powerLoss, newDoc.metadata.equipmentLog.powerLoss)
        assertEquals(doc.metadata.equipmentLog.userError, newDoc.metadata.equipmentLog.userError)
        assertEquals(doc.metadata.equipmentLog.equipmentFailure, newDoc.metadata.equipmentLog.equipmentFailure)

        assertEquals(doc.observations.size, newDoc.observations.size)
        assertEquals(doc.observations.list, newDoc.observations.list)

        assertEquals(doc.bloodList.list, newDoc.bloodList.list)
        assertEquals(doc.bloodList.size, newDoc.bloodList.size)

        assertEquals(doc.medicines.size, newDoc.medicines.size)
        assertEquals(doc.medicines.list, newDoc.medicines.list)

        assertEquals(doc.drips.drips, newDoc.drips.drips)
        assertEquals(doc.events.size, newDoc.events.size)
        assertEquals(doc.events.list, newDoc.events.list)

        assertEquals(doc.movement.medMissionNumber, newDoc.movement.medMissionNumber)
        assertEquals(doc.movement.tailToTail, newDoc.movement.tailToTail)
        assertEquals(doc.movement.legNumber, newDoc.movement.legNumber)
        assertEquals(doc.movement.totalLegs, newDoc.movement.totalLegs)
        assertEquals(doc.movement.ninelineTime, newDoc.movement.ninelineTime)
        assertEquals(doc.movement.dispatchEvac, newDoc.movement.dispatchEvac)
        assertEquals(doc.movement.pickupLocation, newDoc.movement.pickupLocation)
        assertEquals(doc.movement.dropoffLocation, newDoc.movement.dropoffLocation)
        assertEquals(doc.movement.capability, newDoc.movement.capability)
        assertEquals(doc.movement.ninelinePlatform, newDoc.movement.ninelinePlatform)
        assertEquals(doc.movement.originatingMtf, newDoc.movement.originatingMtf)
        assertEquals(doc.movement.destinationMtf, newDoc.movement.destinationMtf)
        assertEquals(doc.movement.reasonRegulated, newDoc.movement.reasonRegulated)
        assertEquals(doc.movement.maxStops, newDoc.movement.maxStops)
        assertEquals(doc.movement.maxRons, newDoc.movement.maxRons)
        assertEquals(doc.movement.maxNumOfRons, newDoc.movement.maxNumOfRons)
        assertEquals(doc.movement.altitudeRestrictions, newDoc.movement.altitudeRestrictions)
        assertEquals(doc.movement.flightLevel, newDoc.movement.flightLevel)
        assertEquals(doc.movement.readyDate, newDoc.movement.readyDate)
        assertEquals(doc.movement.medicalAttendantsNeeded, newDoc.movement.medicalAttendantsNeeded)
        assertEquals(doc.movement.nonMedicalAttendantsNeeded, newDoc.movement.nonMedicalAttendantsNeeded)
        assertEquals(doc.movement.attendants, newDoc.movement.attendants)
        assertEquals(doc.movement.classification, newDoc.movement.classification)
        assertEquals(doc.movement.precedence, newDoc.movement.precedence)
        assertEquals(doc.movement.criticalCare, newDoc.movement.criticalCare)

        assertEquals(doc.links.size, newDoc.links.size)
        assertEquals(doc.links.list, newDoc.links.list)
        assertEquals(doc.links.onErrorRemovedItems, newDoc.links.onErrorRemovedItems)
        assertEquals(doc.links.onProperRemovedItems, newDoc.links.onProperRemovedItems)

        assertEquals(doc.notes.list, newDoc.notes.list)
        assertEquals(doc.notes.size, newDoc.notes.size)
        assertEquals(doc.customActions.size, newDoc.customActions.size)
        assertEquals(doc.customActions.actions, newDoc.customActions.actions)
        assertEquals(doc.customActions.list, newDoc.customActions.list)

        assertEquals(doc.orders.list, newDoc.orders.list)
        assertEquals(doc.orders.size, newDoc.orders.size)
        assertEquals(doc.orders.onErrorRemovedItems, newDoc.orders.onErrorRemovedItems)
        assertEquals(doc.orders.onProperRemovedItems, newDoc.orders.onProperRemovedItems)

        assertEquals(doc.historicalHL7Data.hl7Messages, newDoc.historicalHL7Data.hl7Messages)
    }
}