package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.commands.proto.changeGenderCommand
import gov.afrl.batdok.commands.proto.changeGradeCommand
import gov.afrl.batdok.commands.proto.changeNameCommand
import gov.afrl.batdok.commands.proto.changeWeightCommand
import gov.afrl.batdok.commands.proto.compatibleEnum
import gov.afrl.batdok.encounter.commands.buildChangeGenderCommand
import gov.afrl.batdok.encounter.commands.buildChangeGradeCommand
import gov.afrl.batdok.encounter.commands.buildIsMedicalCommand
import gov.afrl.batdok.encounter.movement.Attendant.Companion.includeAttendantCommands
import gov.afrl.batdok.util.buildCommandData
import gov.afrl.batdok.util.floatValue
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class AttendantEventTest {

    val events = Events()
    
    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(
        ChronoUnit.SECONDS)) {
        EventCommandHandler(events) {
            includeAttendantCommands()
        }.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp))
    }
    
    @Test
    fun testChangeName() {
        val newName = "Test"
        Assert.assertEquals(0, events.list.size)
        handle(changeNameCommand { last = newName })
        Assert.assertEquals("Set name to $newName", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    
    @Test
    fun testChangeName_ThreeNames() {
        val newName = "Test"
        Assert.assertEquals(0, events.list.size)
        handle(changeNameCommand {
            first = newName
            last = newName
            middle = newName
        })
        Assert.assertEquals("Set name to $newName $newName $newName", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    
    @Test
    fun testClearName() {
        Assert.assertEquals(0, events.list.size)
        handle(changeNameCommand { last = "" })
        Assert.assertEquals("Cleared name", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    
    @Test
    fun testChangeGradeCommand_enum() {
        val newGrade = Grade.E09
        Assert.assertEquals(0, events.list.size)
        handle(changeGradeCommand { grade = newGrade.toProto() })
        Assert.assertEquals("Set grade to ${newGrade.dataString}", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    
    @Test
    fun testChangeGradeCommand_string() {
        val newGrade = "Test"
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeGradeCommand(newGrade))
        Assert.assertEquals("Set grade to $newGrade", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }

    @Test
    fun testClearGradeCommand() {
        Assert.assertEquals(0, events.list.size)
        handle(changeGradeCommand{ grade = compatibleEnum {} })
        Assert.assertEquals("Cleared grade", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    
    @Test
    fun testChangeGenderCommand_enum(){
        val newGender = Gender.MALE
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeGenderCommand(newGender))
        Assert.assertEquals("Set sex to ${newGender.dataString}", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }
    
    @Test
    fun testChangeGenderCommand_string(){
        val newGender = "Test"
        Assert.assertEquals(0, events.list.size)
        handle(buildChangeGenderCommand(newGender))
        Assert.assertEquals("Set sex to $newGender", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }

    @Test
    fun testClearGenderCommand(){
        Assert.assertEquals(0, events.list.size)
        handle(changeGenderCommand{ gender = compatibleEnum {  } })
        Assert.assertEquals("Cleared sex", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeWeightCommand(){
        val newWeight = 123.4f
        Assert.assertEquals(0, events.list.size)
        handle(changeWeightCommand{ weight = floatValue(newWeight) })
        Assert.assertEquals("Set weight to 272.05 lbs (123.40 kg)", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }

    @Test
    fun testClearWeightCommand(){
        Assert.assertEquals(0, events.list.size)
        handle(changeWeightCommand{ })
        Assert.assertEquals("Cleared weight", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.INFO.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeIsMedical() {
        Assert.assertTrue(events.list.isEmpty())
        handle(buildIsMedicalCommand(true))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Attendant is medical", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.EVAC.dataString, events.list[0].eventType)
    }

    @Test
    fun testChangeIsMedical_Not() {
        Assert.assertTrue(events.list.isEmpty())
        handle(buildIsMedicalCommand(false))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Attendant is not medical", events.list[0].event)
    }
}