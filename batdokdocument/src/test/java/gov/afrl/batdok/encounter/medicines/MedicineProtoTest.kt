package gov.afrl.batdok.encounter.medicines

import gov.afrl.batdok.commands.proto.valueOrNull
import gov.afrl.batdok.encounter.commands.buildUpdateMedicineCommand
import gov.afrl.batdok.encounter.medicine.Medicine
import gov.afrl.batdok.encounter.medicine.Medicines
import gov.afrl.batdok.util.buildCommandData
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class MedicineProtoTest {

    @Test
    fun testUpdateMedicineRemoveDuplicates_All(){
        val medicine = Medicine(
            "Test",
            "12345",
            "testRxcui",
            Instant.now(),
            route = "Route",
            volume = 12.34f,
            unit = "Unit",
            serialNumber = "123456",
            expirationDate = "1/31",
            type = "Type",
            exportName = "Test"
        )
        val command = buildUpdateMedicineCommand(medicine, medicine)
        Assert.assertFalse(command.medicine.hasName())
        Assert.assertFalse(command.medicine.hasNdc())
        Assert.assertFalse(command.medicine.hasRoute())
        Assert.assertFalse(command.medicine.hasVolume())
        Assert.assertFalse(command.medicine.hasUnit())
        Assert.assertFalse(command.medicine.hasSerialNumber())
        Assert.assertFalse(command.medicine.hasExpirationDate())
        Assert.assertFalse(command.medicine.hasType())
        Assert.assertFalse(command.medicine.hasExportName())
    }

    @Test
    fun testUpdateMedicineRemoveDuplicates_Some(){
        val medicine = Medicine(
            "Test",
            "12345",
            "testRxcui",
            Instant.now(),
            route = "Route",
            volume = 12.34f,
            unit = "Unit",
            serialNumber = "123456",
            expirationDate = "1/31",
            type = "Type",
            exportName = "Test"
        )
        val newMedicine = Medicine(
            "newTest",
            "12345",
            "testRxcui",
            Instant.now(),
            route = "Route2",
            volume = 12.34f,
            unit = "Unit",
            serialNumber = "123456",
            expirationDate = "1/31",
            type = "Type",
            exportName = "newTest"
        )
        val command = buildUpdateMedicineCommand(newMedicine, medicine)
        Assert.assertTrue(command.medicine.hasName())
        Assert.assertEquals("newTest", command.medicine.name.value)
        Assert.assertFalse(command.medicine.hasNdc())
        Assert.assertTrue(command.medicine.hasRoute())
        Assert.assertEquals("Route2", command.medicine.route.string)
        Assert.assertFalse(command.medicine.hasVolume())
        Assert.assertFalse(command.medicine.hasUnit())
        Assert.assertFalse(command.medicine.hasSerialNumber())
        Assert.assertFalse(command.medicine.hasExpirationDate())
        Assert.assertFalse(command.medicine.hasType())
        Assert.assertTrue(command.medicine.hasExportName())
        Assert.assertEquals("newTest", command.medicine.exportName?.valueOrNull?.value)
    }

    @Test
    fun testUpdateMedicineRemoveDuplicates_Clear(){
        val medicine = Medicine(
            "Test",
            "12345",
            "testRxcui",
            Instant.now(),
            route = "Route",
            volume = 12.34f,
            unit = "Unit",
            serialNumber = "123456",
            expirationDate = "1/31",
            type = "Type",
            exportName = "Test"
        )
        val newMedicine = Medicine(
            "",
            null,
            null,
            Instant.now(),
            route = null,
            volume = null,
            unit = null,
            serialNumber = null,
            expirationDate = null,
            type = null,
            exportName = null
        )
        val command = buildUpdateMedicineCommand(newMedicine, medicine)
        Assert.assertTrue(command.medicine.hasName())
        Assert.assertEquals("", command.medicine.name.value)
        Assert.assertTrue(command.medicine.hasNdc())
        Assert.assertFalse(command.medicine.ndc.hasValue())
        Assert.assertTrue(command.medicine.hasRoute())
        Assert.assertEquals("", command.medicine.route.string)
        Assert.assertEquals(0, command.medicine.route.enum)
        Assert.assertTrue(command.medicine.hasVolume())
        Assert.assertFalse(command.medicine.volume.hasValue())
        Assert.assertTrue(command.medicine.hasUnit())
        Assert.assertEquals("", command.medicine.unit.string)
        Assert.assertEquals(0, command.medicine.unit.enum)
        Assert.assertTrue(command.medicine.hasSerialNumber())
        Assert.assertFalse(command.medicine.serialNumber.hasValue())
        Assert.assertTrue(command.medicine.hasExpirationDate())
        Assert.assertFalse(command.medicine.expirationDate.hasValue())
        Assert.assertTrue(command.medicine.hasType())
        Assert.assertEquals("", command.medicine.type.string)
        Assert.assertEquals(0, command.medicine.type.enum)
        Assert.assertTrue(command.medicine.hasExportName())
        Assert.assertFalse(command.medicine.exportName.hasValue())
    }

    @Test
    fun testUpdateMedicineRestoreValues_All(){
        val medicine = Medicine(
            "Test",
            "12345",
            "testRxcui",
            Instant.now().truncatedTo(ChronoUnit.SECONDS),
            route = "Route",
            volume = 12.34f,
            unit = "Unit",
            serialNumber = "123456",
            expirationDate = "1/31",
            type = "Type",
            exportName = "Test"
        )
        val command = buildUpdateMedicineCommand(medicine, medicine)

        val medicines = Medicines()
        medicines += medicine
        medicines.handlers.handle(medicine.documentId, buildCommandData(command))

        Assert.assertEquals(medicine, medicines.list[0])
    }

    @Test
    fun testUpdateMedicineRestoreValues_Some(){
        val medicine = Medicine(
            "Test",
            "12345",
            "testRxcui",
            Instant.now().truncatedTo(ChronoUnit.SECONDS),
            route = "Route",
            volume = 12.34f,
            unit = "Unit",
            serialNumber = "123456",
            expirationDate = "1/31",
            type = "Type",
            exportName = "Test"
        )
        val newMedicine = Medicine(
            "newTest",
            "12345",
            "testRxcui",
            Instant.now().truncatedTo(ChronoUnit.SECONDS),
            medId = medicine.id,
            route = "Route2",
            volume = 12.34f,
            unit = "Unit",
            serialNumber = "123456",
            expirationDate = "1/31",
            type = "Type",
            exportName = "newTest"
        )
        val command = buildUpdateMedicineCommand(newMedicine, medicine)

        val medicines = Medicines()
        medicines += medicine
        medicines.handlers.handle(newMedicine.documentId, buildCommandData(command))

        Assert.assertEquals(newMedicine, medicines.list[0])
    }

    @Test
    fun testUpdateMedicineRestoreValues_Clear(){
        val medicine = Medicine(
            "Test",
            "12345",
            "testRxcui",
            Instant.now().truncatedTo(ChronoUnit.SECONDS),
            route = "Route",
            volume = 12.34f,
            unit = "Unit",
            serialNumber = "123456",
            expirationDate = "1/31",
            type = "Type",
            exportName = "Test"
        )
        val newMedicine = Medicine(
            "",
            null,
            null,
            Instant.now().truncatedTo(ChronoUnit.SECONDS),
            medId = medicine.id,
            route = null,
            volume = null,
            unit = null,
            serialNumber = null,
            expirationDate = null,
            type = null,
            exportName = null
        )
        val command = buildUpdateMedicineCommand(newMedicine, medicine)

        val medicines = Medicines()
        medicines += medicine
        medicines.handlers.handle(newMedicine.documentId, buildCommandData(command))

        Assert.assertEquals(newMedicine, medicines.list[0])
    }
}