package gov.afrl.batdok.encounter.treatments

import com.google.protobuf.Message
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.*
import gov.afrl.batdok.encounter.commands.buildUpdateTreatmentCommand
import gov.afrl.batdok.encounter.commands.updateTreatmentEventCommandHandler
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.encounter.ids.TreatmentId
import gov.afrl.batdok.encounter.treatment.*
import gov.afrl.batdok.encounter.treatment.TubeData.BreathingConfirmation
import gov.afrl.batdok.util.buildCommandData
import gov.afrl.batdok.util.buildDocumentCommand
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class UpdateTreatmentEventTest {

    val events = Events()
    val treatments = Treatments()

    //region Helper functions
    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS), commandId: CommandId = DomainId.create()) {
        EventCommandHandler(events) {
            +updateTreatmentEventCommandHandler(treatments)
        }.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp, commandId))
    }

    private fun testTreatment(name: String, eventString: String, data: TreatmentData? = null){
        //Setup variables
        val id = DomainId.create<TreatmentId>()
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val treatment = Treatment(
            name = name,
            treatmentData = data,
            id = id,
            timestamp = timestamp
        )
        //Add Previous items
        treatments += Treatment(name, id = id)
        events += Event("Existing Event", id.copy(), timestamp.minusSeconds(1000).truncatedTo(ChronoUnit.SECONDS))

        //Make sure the previous event exists
        Assert.assertEquals(1, events.list.size)
        //Handle Command
        handle(buildUpdateTreatmentCommand(treatment))
        //Make sure that still only 1 event exists
        Assert.assertEquals(2, events.list.size)
        Assert.assertEquals(eventString, events.list[1].event)
        Assert.assertEquals(timestamp, events.list[1].timestamp)
        Assert.assertEquals(KnownEventTypes.TREAT.dataString, events.list[1].eventType)
    }

    private fun testTreatment(treatment: CommonTreatments, data: TreatmentData, eventString: String) =
        testTreatment(treatment.dataString, eventString, data)

    private fun testTreatment(treatment: CommonTreatments, eventString: String) =
        testTreatment(treatment.dataString, eventString)
    //endregion

    @Test
    fun testUpdateTreatmentCommand_TQWithExtras() = testTreatment(
        CommonTreatments.TQ,
        TqData(
            TqData.Location.EXTREMITY.dataString,
            tqType = "CAT5",
            subLocation = "LUE"
        ),
        "Updated Treatment TQ: Type: CAT5, Location: Extremity, Sub-Location: LUE"
    )

    @Test
    fun testUpdateTreatmentCommand_TQWithoutExtras() = testTreatment(
        CommonTreatments.TQ,
        "Updated Treatment TQ"
    )

    @Test
    fun testUpdateTreatmentCommand_DressingWithExtras() = testTreatment(
        CommonTreatments.DRESSING,
        DressingData(DressingData.Type.ABDOMINAL.dataString, null, null),
        "Updated Treatment Dressing: Type: Abdominal"
    )

    @Test
    fun testUpdateTreatmentCommand_DressingWithoutExtras() = testTreatment(
        CommonTreatments.DRESSING,
        "Updated Treatment Dressing"
    )

    @Test
    fun testUpdateTreatmentCommand_ETTubeWithExtras() = testTreatment(
        CommonTreatments.ET_TUBE,
        TubeData(size = 10f, depth = 20, location = "Location", breathingConfirmation = "Confirm"),
        "Updated Treatment ET-Tube: Location: Location, Confirmation: Confirm, Depth: 20, Size: 10"
    )

    @Test
    fun testUpdateTreatmentCommand_ETTubeWithoutExtras() = testTreatment(
        CommonTreatments.ET_TUBE,
        "Updated Treatment ET-Tube"
    )

    @Test
    fun testUpdateTreatmentCommand_NeedleDWithoutExtras() = testTreatment(
        CommonTreatments.NEEDLE_D,
        "Updated Treatment Needle-D"
    )

    @Test
    fun testUpdateTreatmentCommand_NeedleDWithExtras() = testTreatment(
        CommonTreatments.NEEDLE_D,
        NeedleDData(NeedleDData.Location.L_MID_CLAV.dataString),
        "Updated Treatment Needle-D: L Mid-Clav"
    )
    @Test
    fun testUpdateTreatmentCommand_FingerThorWithoutExtras() = testTreatment(
        CommonTreatments.FINGER_THOR,
        "Updated Treatment Finger Thor"
    )

    @Test
    fun testUpdateTreatmentCommand_FingerThorWithExtras() = testTreatment(
        CommonTreatments.FINGER_THOR,
        FingerThorData(FingerThorData.Location.LEFT.dataString),
        "Updated Treatment Finger Thor: Left side"
    )
    @Test
    fun testUpdateTreatmentCommand_ChestTubeWithoutExtras() = testTreatment(
        CommonTreatments.CHEST_TUBE,
        "Updated Treatment Chest Tube"
    )
    @Test
    fun testAddTreatmentCommand_ChestTubeWithExtras() = testTreatment(
        CommonTreatments.CHEST_TUBE,
        ChestTubeData(
            location = ChestTubeData.Location.LEFT.dataString,
            suction = true,
            suctionAmount = 1.4f,
            airLeak = true
        ),
        "Updated Treatment Chest Tube: Location: Left side, Suction: Yes, Amount: 1.4, Air Leak: Yes"
    )
    @Test
    fun testAddTreatmentCommand_ChestTubeNullExtras() = testTreatment(
        CommonTreatments.CHEST_TUBE,
        ChestTubeData(),
        "Updated Treatment Chest Tube"
    )
    @Test
    fun testAddTreatmentCommand_ChestTubeWithExtras_falseValues() = testTreatment(
        CommonTreatments.CHEST_TUBE,
        ChestTubeData(
            location = ChestTubeData.Location.LEFT.dataString,
            suction = false,
            suctionAmount = 1.4f,
            airLeak = false
        ),
        "Updated Treatment Chest Tube: Location: Left side, Suction: No, Amount: 1.4, Air Leak: No"
    )

    @Test
    fun testUpdateTreatmentCommand_ChestSealWithoutExtras() = testTreatment(
        CommonTreatments.CHEST_SEAL,
        "Updated Treatment Chest Seal"
    )

    @Test
    fun testUpdateTreatmentCommand_ChestSealWithExtras() = testTreatment(
        CommonTreatments.CHEST_SEAL,
        ChestSealData(ChestSealData.Location.LEFT_FRONT.dataString),
        "Updated Treatment Chest Seal: Left Front"
    )
    @Test
    fun testUpdateTreatmentCommand_EyeShieldWithoutExtras() = testTreatment(
        CommonTreatments.EYE_SHIELD,
        "Updated Treatment Eye Shield"
    )

    @Test
    fun testUpdateTreatmentCommand_EyeShieldWithExtras_NeitherEye() = testTreatment(
        CommonTreatments.EYE_SHIELD,
        EyeShieldData(false, false),
        "Updated Treatment Eye Shield"
    )

    @Test
    fun testUpdateTreatmentCommand_EyeShieldWithExtras_LeftEye() = testTreatment(
        CommonTreatments.EYE_SHIELD,
        EyeShieldData(true, false),
        "Updated Treatment Eye Shield: Left eye"
    )

    @Test
    fun testUpdateTreatmentCommand_EyeShieldWithExtras_RightEye() = testTreatment(
        CommonTreatments.EYE_SHIELD,
        EyeShieldData(false, true),
        "Updated Treatment Eye Shield: Right eye"
    )

    @Test
    fun testUpdateTreatmentCommand_EyeShieldWithExtras_BothEyes() = testTreatment(
        CommonTreatments.EYE_SHIELD,
        EyeShieldData(true, true),
        "Updated Treatment Eye Shield: Both eyes"
    )
    @Test
    fun testUpdateTreatmentCommand_SplintWithoutExtras() = testTreatment(
        CommonTreatments.SPLINT,
        "Updated Treatment Splint"
    )

    @Test
    fun testUpdateTreatmentCommand_SplintWithExtras_Present() = testTreatment(
        CommonTreatments.SPLINT,
        SplintData(true),
        "Updated Treatment Splint: Pulse present"
    )
    @Test
    fun testUpdateTreatmentCommand_SplintWithExtras_Present_Type() = testTreatment(
        CommonTreatments.SPLINT,
        SplintData(true, "Type"),
        "Updated Treatment Splint: Type, Pulse present"
    )

    @Test
    fun testUpdateTreatmentCommand_SplintWithExtras_NotPresent() = testTreatment(
        CommonTreatments.SPLINT,
        SplintData(false),
        "Updated Treatment Splint: Pulse not present"
    )
    @Test
    fun testUpdateTreatmentCommand_O2WithoutExtras() = testTreatment(
        CommonTreatments.O2,
        "Updated Treatment O2"
    )

    @Test
    fun testUpdateTreatmentCommand_O2WithExtras_Old() = testTreatment(
        CommonTreatments.O2,
        O2Data(10f, O2Data.DeliveryMethod.NRB.dataString),
        "Updated Treatment O2: Source: NRB, LPM: 10.0"
    )
    @Test
    fun testUpdateTreatmentCommand_O2WithExtras() = testTreatment(
        CommonTreatments.O2,
        O2Data(lpm = 10f, targetSpO2 = 98, fiO2 = 13f, route = O2Data.O2Route.VENTURI_MASK.dataString),
        "Updated Treatment O2: LPM: 10.0, Target SPO2: 98, FIO2: 13.0, Route: Venturi Mask"
    )
    @Test
    fun testUpdateTreatmentCommand_CricWithoutExtras() = testTreatment(
        CommonTreatments.CRIC,
        "Updated Treatment CRIC"
    )

    @Test
    fun testUpdateTreatmentCommand_CricWithExtras() = testTreatment(
        CommonTreatments.CRIC,
        TubeData(breathingConfirmation = BreathingConfirmation.BREATH_SOUNDS.dataString, size = 10f),
        "Updated Treatment CRIC: Confirmation: Breath Sounds, Size: 10"
    )
    @Test
    fun testUpdateTreatmentCommand_EscharatomyWithoutExtras() = testTreatment(
        CommonTreatments.ESCHARATOMY,
        "Updated Treatment Escharatomy"
    )

    @Test
    fun testUpdateTreatmentCommand_EscharatomyWithExtras() = testTreatment(
        CommonTreatments.ESCHARATOMY,
        EscharatomyData("Free text location"),
        "Updated Treatment Escharatomy: Free text location"
    )
    @Test
    fun testUpdateTreatmentCommand_DebridementWithoutExtras() = testTreatment(
        CommonTreatments.DEBRIDEMENT,
        "Updated Treatment Debridement"
    )

    @Test
    fun testUpdateTreatmentCommand_DebridementWithExtras() = testTreatment(
        CommonTreatments.DEBRIDEMENT,
        DebridementData("Free text location"),
        "Updated Treatment Debridement: Free text location"
    )
    @Test
    fun testUpdateTreatmentCommand_GastricTubeWithoutExtras() = testTreatment(
        CommonTreatments.GASTRIC_TUBE,
        "Updated Treatment Gastric Tube"
    )

    @Test
    fun testUpdateTreatmentCommand_GastricTubeWithExtras() = testTreatment(
        CommonTreatments.GASTRIC_TUBE,
        GastricTubeData(
            GastricTubeData.Type.NASAL.dataString,
            GastricTubeData.Side.LEFT.dataString,
            GastricTubeData.SuctionType.SUCTION.dataString,
            GastricTubeData.Interval.CONTINUOUS.dataString
        ),
        "Updated Treatment Gastric Tube: Type: Nasal, Side: Left side, Suction Type: Suction, Interval: Continuous"
    )
    @Test
    fun testUpdateTreatmentCommand_LateralCanthotomyWithoutExtras() = testTreatment(
        CommonTreatments.LATERAL_CANTHOTOMY,
        "Updated Treatment Lateral Canthotomy"
    )

    @Test
    fun testUpdateTreatmentCommand_LateralCanthotomyWithExtras() = testTreatment(
        CommonTreatments.LATERAL_CANTHOTOMY,
        LateralCanthotomyData(LateralCanthotomyData.Location.LEFT.dataString),
        "Updated Treatment Lateral Canthotomy: Left side"
    )
    @Test
    fun testUpdateTreatmentCommand_FasciotomyWithoutExtras() = testTreatment(
        CommonTreatments.FASCIOTOMY,
        "Updated Treatment Fasciotomy"
    )

    @Test
    fun testUpdateTreatmentCommand_FasciotomyWithExtras() = testTreatment(
        CommonTreatments.FASCIOTOMY,
        FasciotomyData("Free text Location"),
        "Updated Treatment Fasciotomy: Free text Location"
    )
    @Test
    fun testUpdateTreatmentCommand_FoleyCatheterWithoutExtras() = testTreatment(
        CommonTreatments.FOLEY_CATHETER,
        "Updated Treatment Foley Catheter"
    )

    @Test
    fun testUpdateTreatmentCommand_FoleyCatheterWithExtras() = testTreatment(
        CommonTreatments.FOLEY_CATHETER,
        FoleyCatheterData(
            10f,
            "G",
            FoleyCatheterData.Color.BLUE.dataString,
            "Character",
            "Assess"
        ),
        "Updated Treatment Foley Catheter: Size: 10 G, Type: Blue, Character: Character, Assess: Assess"
    )
    @Test
    fun testUpdateTreatmentCommand_DirectPressureWithoutExtras() = testTreatment(
        CommonTreatments.DIRECT_PRESSURE,
        "Updated Treatment Direct Pressure"
    )

    @Test
    fun testUpdateTreatmentCommand_DirectPressureWithExtras() = testTreatment(
        CommonTreatments.DIRECT_PRESSURE,
        DirectPressureData("Free text Location"),
        "Updated Treatment Direct Pressure: Free text Location"
    )
    @Test
    fun testUpdateTreatmentCommand_ForeignBodyRemovalWithoutExtras() = testTreatment(
        CommonTreatments.FOREIGN_BODY_REMOVAL,
        "Updated Treatment Foreign Body Removal"
    )

    @Test
    fun testUpdateTreatmentCommand_ForeignBodyRemovalWithExtras() = testTreatment(
        CommonTreatments.FOREIGN_BODY_REMOVAL,
        ForeignBodyRemovalData("Free text Location"),
        "Updated Treatment Foreign Body Removal: Free text Location"
    )
    @Test
    fun testUpdateTreatmentCommand_NPAWithoutExtras() = testTreatment(
        CommonTreatments.NPA,
        "Updated Treatment NPA"
    )

    @Test
    fun testUpdateTreatmentCommand_NPAWithExtras() = testTreatment(
        CommonTreatments.NPA,
        TubeData(BreathingConfirmation.BREATH_SOUNDS.dataString, 10f),
        "Updated Treatment NPA: Confirmation: Breath Sounds, Size: 10"
    )
    @Test
    fun testUpdateTreatmentCommand_OPAWithoutExtras() = testTreatment(
        CommonTreatments.OPA,
        "Updated Treatment OPA"
    )

    @Test
    fun testUpdateTreatmentCommand_OPAWithExtras() = testTreatment(
        CommonTreatments.OPA,
        TubeData(BreathingConfirmation.BREATH_SOUNDS.dataString, 10f),
        "Updated Treatment OPA: Confirmation: Breath Sounds, Size: 10"
    )
    @Test
    fun testUpdateTreatmentCommand_SGAWithoutExtras() = testTreatment(
        CommonTreatments.SGA,
        "Updated Treatment SGA"
    )

    @Test
    fun testUpdateTreatmentCommand_SGAWithExtras() = testTreatment(
        CommonTreatments.SGA,
        TubeData(BreathingConfirmation.BREATH_SOUNDS.dataString, 10f, "SGA Type"),
        "Updated Treatment SGA: Confirmation: Breath Sounds, Size: 10 SGA Type"
    )
    @Test
    fun testUpdateTreatmentCommand_TrachWithoutExtras() = testTreatment(
        CommonTreatments.TRACH,
        "Updated Treatment Trach"
    )

    @Test
    fun testUpdateTreatmentCommand_TrachWithExtras() = testTreatment(
        CommonTreatments.TRACH,
        TubeData(BreathingConfirmation.BREATH_SOUNDS.dataString, 10f),
        "Updated Treatment Trach: Confirmation: Breath Sounds, Size: 10"
    )
    @Test
    fun testUpdateTreatmentCommand_HypothermiaPreventionWithoutExtras() = testTreatment(
        CommonTreatments.HYPOTHERMIA_PREVENTION,
        "Updated Treatment Hypothermia Prevention"
    )

    @Test
    fun testUpdateTreatmentCommand_HypothermiaPreventionWithExtras() = testTreatment(
        CommonTreatments.HYPOTHERMIA_PREVENTION,
        HypothermiaPreventionData(HypothermiaPreventionData.Type.BLANKET.dataString),
        "Updated Treatment Hypothermia Prevention: Blanket"
    )
    @Test
    fun testUpdateTreatmentCommand_ImmobilizationWithoutExtras() = testTreatment(
        CommonTreatments.IMMOBILIZATION,
        "Updated Treatment Immobilization"
    )

    @Test
    fun testUpdateTreatmentCommand_ImmobilizationWithExtras() = testTreatment(
        CommonTreatments.IMMOBILIZATION,
        ImmobilizationData(ImmobilizationData.Type.C_COLLAR.dataString, "Subtype"),
        "Updated Treatment Immobilization: C-Collar"
    )

    @Test
    fun testUpdateTreatmentCommand_ImmobilizationOther() = testTreatment(
        CommonTreatments.IMMOBILIZATION,
        ImmobilizationData(
            type = ImmobilizationData.Type.SWATH.dataString,
            location = ImmobilizationData.Location.RIBS.dataString,
            neurovascularBefore = NeurovascularStatus(
                pulse = ImmobilizationData.TernaryStatus.YES,
                motor = ImmobilizationData.TernaryStatus.NO,
                sensory = ImmobilizationData.TernaryStatus.UNKNOWN
            ),
            neurovascularAfter = NeurovascularStatus(
                pulse = ImmobilizationData.TernaryStatus.YES,
                motor = ImmobilizationData.TernaryStatus.YES,
                sensory = ImmobilizationData.TernaryStatus.YES
            )
        ),
        "Updated Treatment Immobilization: Swath, Ribs" +
                ", Neuro Before - Pulse: Yes, Motor: No, Sensory: Unknown, Neuro After - Pulse: Yes, Motor: Yes, Sensory: Yes"
    )
    
    @Test
    fun testUpdateTreatmentCommand_SuctionWithoutExtras() = testTreatment(
        CommonTreatments.SUCTION,
        "Updated Treatment Suction"
    )

    @Test
    fun testUpdateTreatmentCommand_SuctionWithExtras() = testTreatment(
        CommonTreatments.SUCTION,
        SuctionData(
            SuctionData.Tool.YANKAUER.dataString,
            "Free Text Location"
        ),
        "Updated Treatment Suction: Yankauer"
    )
    @Test
    fun testUpdateTreatmentCommand_IntubatedByWithoutExtras() = testTreatment(
        CommonTreatments.INTUBATED_BY,
        "Updated Treatment Intubated By"
    )

    @Test
    fun testUpdateTreatmentCommand_IntubatedByWithExtras() = testTreatment(
        CommonTreatments.INTUBATED_BY,
        IntubatedByData("Intubater"),
        "Updated Treatment Intubated By: Intubater"
    )

    @Test
    fun testUpdateTreatmentCommand_LineWithoutExtras() = testTreatment(
        CommonTreatments.LINE,
        "Updated Treatment Line"
    )

    @Test
    fun testUpdateTreatmentCommand_LineWithExtras() = testTreatment(
        CommonTreatments.LINE,
        LineData("Type", "Side", "Location", 1f, null, 1f, null,"Subtype"),
        "Updated Treatment Line: Type: Type, SubType: Subtype, Location: Side Location, Gauge: 1, Size: 1"
    )

    @Test
    fun testRemovedFieldsShowInEvent(){
        val data = TqData(TqData.Location.EXTREMITY.dataString, subLocation = TqData.SubLocation.LUE.dataString)
        val oldTreatment = Treatment(CommonTreatments.TQ.dataString, treatmentData = data)
        val treatment = oldTreatment.copy(treatmentData = data.copy(tqType = TqData.Type.AAJT.dataString))

        val doc = Document().apply {
            treatments += oldTreatment
            handle(
                listOf(
                    buildDocumentCommand(DomainId.create(), buildCommandData(buildUpdateTreatmentCommand(treatment, oldTreatment)))
                )
            )
        }

        Assert.assertEquals(listOf("Updated Treatment TQ: Type: AAJT, Location: Extremity, Sub-Location: LUE"), doc.events.list.map { it.event })
    }

    @Test
    fun testUpdateTreatmentCommand_WoundPackingWithoutExtras() = testTreatment(
        CommonTreatments.WOUND_PACKING,
        "Updated Treatment Wound Packing"
    )

    @Test
    fun testUpdateTreatmentCommand_WoundPackingWithExtras() = testTreatment(
        CommonTreatments.WOUND_PACKING,
        WoundPackingData("Location", "Type"),
        "Updated Treatment Wound Packing: Type: Type, Location: Location"
    )

    @Test
    fun testUpdateTreatmentCommand_PelvicBinder() = testTreatment(
        CommonTreatments.PELVIC_BINDER,
        PelvicBinderData("Test"),
        "Updated Treatment Pelvic Binder: Test"
    )
}