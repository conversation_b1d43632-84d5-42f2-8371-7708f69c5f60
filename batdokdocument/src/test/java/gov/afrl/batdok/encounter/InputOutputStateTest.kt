package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.updateIOItem
import gov.afrl.batdok.encounter.commands.buildAddIOItemCommand
import gov.afrl.batdok.encounter.commands.buildRemoveIOItem
import gov.afrl.batdok.encounter.commands.buildUpdateIOItemCommand
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.encounter.ids.InputOutputId
import gov.afrl.batdok.encounter.ids.toByteString
import gov.afrl.batdok.util.buildCommandData
import gov.afrl.batdok.util.buildDocumentCommand
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class InputOutputStateTest {

    val docId = DomainId.create<DocumentId>()
    val inputOutputs = InputOutputs()

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS), commandId: CommandId = DomainId.create()) {
        inputOutputs.handlers.handle(docId, buildCommandData(subCommand, callsign, timestamp, commandId = commandId))
    }

    @Test
    fun testAddIOItem_input(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val itemId = DomainId.create<InputOutputId>()
        val item = InputOutput(
            itemId, timestamp, "Name", "Route", 1.0, "Unit", docId
        )
        Assert.assertEquals(0, inputOutputs.inputs.size)
        Assert.assertEquals(0, inputOutputs.outputs.size)
        handle(buildAddIOItemCommand(item, true), commandId = itemId.copy(), timestamp = timestamp)
        Assert.assertEquals(1, inputOutputs.inputs.size)
        Assert.assertEquals(0, inputOutputs.outputs.size)
        Assert.assertEquals(itemId, inputOutputs.inputs[0].id)
        val inputOutput = inputOutputs[itemId]
        Assert.assertNotNull(inputOutput)
        Assert.assertEquals("Name", inputOutput!!.name)
        Assert.assertEquals(timestamp, inputOutput.timestamp)
        Assert.assertEquals("Route", inputOutput.route)
        Assert.assertEquals(1.0, inputOutput.volume, 0.001)
        Assert.assertEquals("Unit", inputOutput.unit)
        Assert.assertEquals(docId, inputOutputs.inputs[0].documentId)
    }

    @Test
    fun testAddIOItem_output(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val itemId = DomainId.create<InputOutputId>()
        val item = InputOutput(
            itemId,
            timestamp,
            InputOutputType.URINE.dataString,
            InputOutputRoute.CATHETER.dataString,
            1.0,
            InputOutputUnit.CC.dataString,
            docId
        )
        Assert.assertEquals(0, inputOutputs.inputs.size)
        Assert.assertEquals(0, inputOutputs.outputs.size)
        handle(buildAddIOItemCommand(item, false), commandId = itemId.copy(), timestamp = timestamp)
        Assert.assertEquals(0, inputOutputs.inputs.size)
        Assert.assertEquals(1, inputOutputs.outputs.size)
        Assert.assertEquals(itemId, inputOutputs.outputs[0].id)
        val inputOutput = inputOutputs[itemId]
        Assert.assertNotNull(inputOutput)
        Assert.assertEquals("Urine", inputOutput!!.name)
        Assert.assertEquals(timestamp, inputOutput.timestamp)
        Assert.assertEquals("Catheter", inputOutput.route)
        Assert.assertEquals(1.0, inputOutput.volume, 0.001)
        Assert.assertEquals("cc", inputOutput.unit)
        Assert.assertEquals(docId, inputOutputs.outputs[0].documentId)
    }

    @Test
    fun testUpdateIOItem_input(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val itemId = DomainId.create<InputOutputId>()
        val item = InputOutput(
            itemId, timestamp, "Name", "Route", 1.0, "Unit", docId
        )
        inputOutputs.inputs += InputOutput(
            itemId,
            timestamp.minusMillis(1000),
            "Old Type",
            "Old Route",
            2.718281828459045,
            "Old Unit",
            docId
        )
        Assert.assertEquals(1, inputOutputs.inputs.size)
        Assert.assertEquals(0, inputOutputs.outputs.size)
        handle(buildUpdateIOItemCommand(item, true))
        Assert.assertEquals(1, inputOutputs.inputs.size)
        Assert.assertEquals(0, inputOutputs.outputs.size)
        Assert.assertEquals(itemId, inputOutputs.inputs[0].id)
        val inputOutput = inputOutputs[itemId]
        Assert.assertNotNull(inputOutput)
        Assert.assertEquals("Name", inputOutput!!.name)
        Assert.assertEquals(timestamp, inputOutput.timestamp)
        Assert.assertEquals("Route", inputOutput.route)
        Assert.assertEquals(1.0, inputOutput.volume, 0.001)
        Assert.assertEquals("Unit", inputOutput.unit)
        Assert.assertEquals(docId, inputOutputs.inputs[0].documentId)
    }

    @Test
    fun testUpdateIOItem_output(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val itemId = DomainId.create<InputOutputId>()
        val item = InputOutput(
            itemId,
            timestamp,
            InputOutputType.URINE.dataString,
            InputOutputRoute.CATHETER.dataString,
            1.0,
            InputOutputUnit.CC.dataString,
            docId
        )
        inputOutputs.outputs += InputOutput(
            itemId,
            timestamp.minusMillis(1000),
            "Old Type",
            "Old Route",
            2.718281828459045,
            "Old Unit",
            docId
        )
        Assert.assertEquals(0, inputOutputs.inputs.size)
        Assert.assertEquals(1, inputOutputs.outputs.size)
        handle(buildUpdateIOItemCommand(item, false))
        Assert.assertEquals(0, inputOutputs.inputs.size)
        Assert.assertEquals(1, inputOutputs.outputs.size)
        Assert.assertEquals(itemId, inputOutputs.outputs[0].id)
        val inputOutput = inputOutputs[itemId]
        Assert.assertNotNull(inputOutput)
        Assert.assertEquals("Urine", inputOutput!!.name)
        Assert.assertEquals(timestamp, inputOutput.timestamp)
        Assert.assertEquals("Catheter", inputOutput.route)
        Assert.assertEquals(1.0, inputOutput.volume, 0.001)
        Assert.assertEquals("cc", inputOutput.unit)
        Assert.assertEquals(docId, inputOutputs.outputs[0].documentId)
    }

    @Test
    fun testUpdateIOItem_output_someNewFields(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val itemId = DomainId.create<InputOutputId>()
        val item = InputOutput(
            itemId,
            timestamp,
            InputOutputType.URINE.dataString,
            InputOutputRoute.CATHETER.dataString,
            1.0,
            InputOutputUnit.CC.dataString,
            docId
        )
        val oldItem = InputOutput(
            itemId,
            timestamp,
            "Old Type",
            InputOutputRoute.CATHETER.dataString,
            1.0,
            InputOutputUnit.CC.dataString,
            docId
        )
        inputOutputs.outputs += oldItem
        Assert.assertEquals(0, inputOutputs.inputs.size)
        Assert.assertEquals(1, inputOutputs.outputs.size)
        handle(buildUpdateIOItemCommand(item, false, oldItem))
        Assert.assertEquals(0, inputOutputs.inputs.size)
        Assert.assertEquals(1, inputOutputs.outputs.size)
        Assert.assertEquals(itemId, inputOutputs.outputs[0].id)
        val inputOutput = inputOutputs[itemId]
        Assert.assertNotNull(inputOutput)
        Assert.assertEquals("Urine", inputOutput!!.name)
        Assert.assertEquals(timestamp, inputOutput.timestamp)
        Assert.assertEquals("Catheter", inputOutput.route)
        Assert.assertEquals(1.0, inputOutput.volume, 0.001)
        Assert.assertEquals("cc", inputOutput.unit)
        Assert.assertEquals(docId, inputOutputs.outputs[0].documentId)
    }

    /**
     * Test the rare use case of no timestamp being provided, and no previous item existing
     */
    @Test
    fun testUpdateIOItem_timestampWhenNoPreviousValue(){
        val itemId = DomainId.create<InputOutputId>()
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        handle(updateIOItem {
            this.itemId = itemId.toByteString()
            isInput = true
        }, timestamp = timestamp)
        Assert.assertEquals(timestamp, inputOutputs[itemId]!!.timestamp)
    }

    @Test
    fun testRemoveIOItem_input(){
        val itemId = DomainId.create<InputOutputId>()
        inputOutputs.inputs += InputOutput(
            itemId,
            Instant.now().truncatedTo(ChronoUnit.SECONDS),
            "Old Type",
            "Old Route",
            2.718281828459045,
            "Old Unit",
            docId
        )
        Assert.assertEquals(1, inputOutputs.inputs.size)
        Assert.assertEquals(0, inputOutputs.outputs.size)
        handle(buildRemoveIOItem(itemId))
        Assert.assertEquals(0, inputOutputs.inputs.size)
        Assert.assertEquals(0, inputOutputs.outputs.size)
    }

    @Test
    fun testRemoveIOItem_output(){
        val itemId = DomainId.create<InputOutputId>()
        inputOutputs.outputs += InputOutput(
            itemId,
            Instant.now().truncatedTo(ChronoUnit.SECONDS),
            "Old Type",
            "Old Route",
            2.718281828459045,
            "Old Unit",
            docId
        )
        Assert.assertEquals(0, inputOutputs.inputs.size)
        Assert.assertEquals(1, inputOutputs.outputs.size)
        handle(buildRemoveIOItem(itemId))
        Assert.assertEquals(0, inputOutputs.inputs.size)
        Assert.assertEquals(0, inputOutputs.outputs.size)
    }

    @Test
    fun testUpdateIOItemCommandSize(){
        //ASSIGN
        val itemId = DomainId.create<InputOutputId>()
        val item = InputOutput(
            itemId,
            Instant.now().truncatedTo(ChronoUnit.SECONDS),
            "Old Type",
            "Old Route",
            2.718281828459045,
            "Old Unit",
            docId
        )

        //ACT
        val command = buildUpdateIOItemCommand(item, true)
        val minimizedCommand = buildUpdateIOItemCommand(item, true, item)

        //ASSERT
        //Make sure the size is actually smaller when removing the unchanged data
        println("Full Data: " + command.serializedSize)
        println("Minimized Data: " + minimizedCommand.serializedSize)
        Assert.assertNotEquals(command.serializedSize, minimizedCommand.serializedSize)
        Assert.assertFalse(minimizedCommand.hasTime())
        Assert.assertTrue(minimizedCommand.hasData())
        with(minimizedCommand.data){
            Assert.assertFalse(hasName())
            Assert.assertFalse(hasRoute())
            Assert.assertFalse(hasValue())
            Assert.assertFalse(hasUnit())
        }
    }

    @Test
    fun testAddWithId(){
        val id = DomainId.create<DomainId>()
        val doc = Document()

        val item = InputOutput(
            id.copy(),
            Instant.now().truncatedTo(ChronoUnit.SECONDS),
            "Old Type",
            "Old Route",
            2.718281828459045,
            "Old Unit",
            docId
        )

        doc.handle(
            listOf(
                buildDocumentCommand(
                    DomainId.create(),
                    buildCommandData(buildAddIOItemCommand(item, true))
                ),
            )
        )
        Assert.assertEquals(id, doc.inputOutputs.inputs.first().id)
    }
}