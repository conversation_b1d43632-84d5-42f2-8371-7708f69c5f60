package gov.afrl.batdok.encounter

import gov.afrl.batdok.encounter.ids.EncounterVitalId
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant

class EventsTest {

    val classUnderTest = Events()

    @Test
    fun testGetEventByString(){
        //ASSIGN
        classUnderTest += listOf(
            Event("Test1", DomainId.create(), Instant.now(), eventType = "Test"),
            Event("Test2", DomainId.create(), Instant.now(), eventType = KnownEventTypes.SUBJECTIVE.dataString),
            Event("Test3", DomainId.create(), Instant.now(), eventType = "Test"),
            Event("Test4", DomainId.create(), Instant.now(), eventType = KnownEventTypes.SUBJECTIVE.dataString),
        )

        //ACT
        val list = classUnderTest["Test"]

        //ASSERT
        Assert.assertEquals(2, list.size)
        Assert.assertEquals("Test1", list[0].event)
        Assert.assertEquals("Test3", list[1].event)
    }

    @Test
    fun testGetEventByEnum(){
        //ASSIGN
        classUnderTest += listOf(
            Event("Test1", DomainId.create(), Instant.now(), eventType = "Test"),
            Event("Test2", DomainId.create(), Instant.now(), eventType = KnownEventTypes.SUBJECTIVE.dataString),
            Event("Test3", DomainId.create(), Instant.now(), eventType = "Test"),
            Event("Test4", DomainId.create(), Instant.now(), eventType = KnownEventTypes.SUBJECTIVE.dataString),
        )

        //ACT
        val list = classUnderTest[KnownEventTypes.SUBJECTIVE]

        //ASSERT
        Assert.assertEquals(2, list.size)
        Assert.assertEquals("Test2", list[0].event)
        Assert.assertEquals("Test4", list[1].event)
    }

    @Test
    fun testSortEvents(){
        //ASSIGN
        classUnderTest += listOf(
            Event("Test4", DomainId.create(), Instant.ofEpochSecond(4), eventType = KnownEventTypes.SUBJECTIVE.dataString),
            Event("Test2", DomainId.create(), Instant.ofEpochSecond(2), eventType = KnownEventTypes.SUBJECTIVE.dataString),
            Event("Test1", DomainId.create(), Instant.ofEpochSecond(1), eventType = "Test"),
            Event("Test3", DomainId.create(), Instant.ofEpochSecond(3), eventType = "Test"),
        )

        //ACT
        val list = classUnderTest.list.sorted()

        //ASSERT
        Assert.assertEquals(4, list.size)
        Assert.assertEquals("Test1", list[0].event)
        Assert.assertEquals("Test2", list[1].event)
        Assert.assertEquals("Test3", list[2].event)
        Assert.assertEquals("Test4", list[3].event)
    }

    @Test
    fun testGetComments(){
        //ASSIGN
        val referenceId = DomainId.create<EncounterVitalId>()
        classUnderTest += listOf(
            Event("Test4", DomainId.create(), Instant.ofEpochSecond(4), eventType = KnownEventTypes.SUBJECTIVE.dataString, referencedItem = referenceId),
            Event("Test2", DomainId.create(), Instant.ofEpochSecond(2), eventType = KnownEventTypes.SUBJECTIVE.dataString, referencedItem = referenceId),
            Event("Test1", DomainId.create(), Instant.ofEpochSecond(1), eventType = "Test"),
            Event("Test3", DomainId.create(), Instant.ofEpochSecond(3), eventType = "Test"),
        )

        //ACT
        val list = classUnderTest.commentsForId(referenceId)

        //ASSERT
        Assert.assertEquals(2, list.size)
        Assert.assertEquals("Test2", list[0].event)
        Assert.assertEquals("Test4", list[1].event)
    }


    @Test
    fun testGetComments_NoReferencedEventsForId(){
        //ASSIGN
        val referenceId = DomainId.create<EncounterVitalId>()
        classUnderTest += listOf(
            Event("Test4", DomainId.create(), Instant.ofEpochSecond(4), eventType = KnownEventTypes.SUBJECTIVE.dataString, referencedItem = referenceId),
            Event("Test2", DomainId.create(), Instant.ofEpochSecond(2), eventType = KnownEventTypes.SUBJECTIVE.dataString, referencedItem = referenceId),
            Event("Test1", DomainId.create(), Instant.ofEpochSecond(1), eventType = "Test"),
            Event("Test3", DomainId.create(), Instant.ofEpochSecond(3), eventType = "Test"),
        )
        //ACT
        val list = classUnderTest.commentsForId(DomainId.create())
        //ASSERT
        Assert.assertEquals(0, list.size)
    }
    @Test
    fun testGetComments_NoEvents(){
        //ASSIGN
        val referenceId = DomainId.create<EncounterVitalId>()
        classUnderTest += listOf()
        //ACT
        val list = classUnderTest.commentsForId(referenceId)
        //ASSERT
        Assert.assertEquals(0, list.size)
    }
}