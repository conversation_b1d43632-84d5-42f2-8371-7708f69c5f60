package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.commands.buildChecklistCommand
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class ChecklistStateTest {

    private val checklistItems = Checklist()

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS)) {
        checklistItems.handlers.handle(
            DomainId.nil(),
            buildCommandData(subCommand, callsign, timestamp)
        )
    }

    @Test
    fun testChecklistItem_known_checked(){
        Assert.assertEquals(0, checklistItems.size)
        handle(
            buildChecklistCommand(
                KnownChecklistItems.CHECK_DRESSINGS,
                true,
                Interval(KnownChecklistItems.CHECK_DRESSINGS.defaultInterval)
            )
        )
        Assert.assertEquals(1, checklistItems.size)
        with(checklistItems[0]){
            Assert.assertEquals(KnownChecklistItems.CHECK_DRESSINGS.dataString, message)
            Assert.assertEquals(KnownChecklistItems.CHECK_DRESSINGS.defaultInterval, intervalTime.toString())
            Assert.assertTrue(checked)
        }
    }

    @Test
    fun testChecklistItem_known_uncheck(){
        Assert.assertEquals(0, checklistItems.size)
        handle(
            buildChecklistCommand(
                KnownChecklistItems.CHECK_DRESSINGS,
                false,
                Interval(KnownChecklistItems.CHECK_DRESSINGS.defaultInterval)
            )
        )
        Assert.assertEquals(1, checklistItems.size)
        with(checklistItems[0]){
            Assert.assertEquals(KnownChecklistItems.CHECK_DRESSINGS.dataString, message)
            Assert.assertEquals(KnownChecklistItems.CHECK_DRESSINGS.defaultInterval, intervalTime.toString())
            Assert.assertFalse(checked)
        }
    }

    @Test
    fun testChecklistItem_known_uncheck_existing(){
        checklistItems += ChecklistItem(KnownChecklistItems.CHECK_DRESSINGS)
        Assert.assertEquals(1, checklistItems.size)
        handle(
            buildChecklistCommand(
                KnownChecklistItems.CHECK_DRESSINGS,
                false,
                Interval(KnownChecklistItems.CHECK_DRESSINGS.defaultInterval)
            )
        )
        Assert.assertEquals(1, checklistItems.size)
        with(checklistItems[0]){
            Assert.assertEquals(KnownChecklistItems.CHECK_DRESSINGS.dataString, message)
            Assert.assertEquals(KnownChecklistItems.CHECK_DRESSINGS.defaultInterval, intervalTime.toString())
            Assert.assertFalse(checked)
        }
    }

    @Test
    fun testChecklistItem_known_newInterval(){
        Assert.assertEquals(0, checklistItems.size)
        handle(
            buildChecklistCommand(
                KnownChecklistItems.CHECK_DRESSINGS,
                false,
                Interval(interval = "Custom")
            )
        )
        Assert.assertEquals(1, checklistItems.size)
        with(checklistItems[0]){
            Assert.assertEquals(KnownChecklistItems.CHECK_DRESSINGS.dataString, message)
            Assert.assertEquals("Custom", intervalTime.toString())
            Assert.assertFalse(checked)
        }
    }

    @Test
    fun testChecklistItem_known_newInterval_existing(){
        checklistItems += ChecklistItem(KnownChecklistItems.CHECK_DRESSINGS)
        Assert.assertEquals(1, checklistItems.size)
        handle(buildChecklistCommand(KnownChecklistItems.CHECK_DRESSINGS, null, Interval("Custom")))
        Assert.assertEquals(1, checklistItems.size)
        with(checklistItems[0]){
            Assert.assertEquals(KnownChecklistItems.CHECK_DRESSINGS.dataString, message)
            Assert.assertEquals("Custom", intervalTime.toString())
            Assert.assertTrue(checked)
        }
    }


    @Test
    fun testChecklistItem_custom_checked(){
        Assert.assertEquals(0, checklistItems.size)
        handle(buildChecklistCommand("Custom Item", true, Interval("Once")))
        Assert.assertEquals(1, checklistItems.size)
        with(checklistItems[0]){
            Assert.assertEquals("Custom Item", message)
            Assert.assertEquals("Once", intervalTime.toString())
            Assert.assertTrue(checked)
        }
    }

    @Test
    fun testChecklistItem_custom_uncheck(){
        Assert.assertEquals(0, checklistItems.size)
        handle(buildChecklistCommand("Custom Item", false, Interval("Once")))
        Assert.assertEquals(1, checklistItems.size)
        with(checklistItems[0]){
            Assert.assertEquals("Custom Item", message)
            Assert.assertEquals("Once", intervalTime.toString())
            Assert.assertFalse(checked)
        }
    }

    @Test
    fun testChecklistItem_custom_uncheck_existing(){
        checklistItems += ChecklistItem("Custom Item", Interval("Once"))
        Assert.assertEquals(1, checklistItems.size)
        handle(buildChecklistCommand("Custom Item", false, Interval("Once")))
        Assert.assertEquals(1, checklistItems.size)
        with(checklistItems[0]){
            Assert.assertEquals("Custom Item", message)
            Assert.assertEquals("Once", intervalTime.toString())
            Assert.assertFalse(checked)
        }
    }

    @Test
    fun testChecklistItem_custom_newInterval(){
        Assert.assertEquals(0, checklistItems.size)
        handle(buildChecklistCommand("Custom Item", null, Interval(interval = "Custom")))
        Assert.assertEquals(1, checklistItems.size)
        with(checklistItems[0]){
            Assert.assertEquals("Custom Item", message)
            Assert.assertEquals("Custom", intervalTime.toString())
            Assert.assertFalse(checked)
        }
    }

    @Test
    fun testChecklistItem_custom_newInterval_existing(){
        checklistItems += ChecklistItem("Custom Item", Interval("Once"))
        Assert.assertEquals(1, checklistItems.size)
        handle(buildChecklistCommand("Custom Item", null, Interval("Custom")))
        Assert.assertEquals(1, checklistItems.size)
        with(checklistItems[0]){
            Assert.assertEquals("Custom Item", message)
            Assert.assertEquals("Custom", intervalTime.toString())
            Assert.assertTrue(checked)
        }
    }
}