package gov.afrl.batdok.encounter.orders

import com.google.protobuf.Message
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.Contact
import gov.afrl.batdok.encounter.Events
import gov.afrl.batdok.encounter.Interval
import gov.afrl.batdok.encounter.KnownEventTypes
import gov.afrl.batdok.encounter.commands.orders.buildAddMedicineOrderLineCommand
import gov.afrl.batdok.encounter.commands.orders.buildUpdateMedicineOrderLineCommand
import gov.afrl.batdok.encounter.commands.orders.buildUpdateMedicineOrderLineStatusCommand
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.encounter.metadata.Signature
import gov.afrl.batdok.encounter.orders.Orders.Companion.includeOrdersEvents
import gov.afrl.batdok.util.Patterns
import gov.afrl.batdok.util.buildCommandData
import gov.afrl.batdok.util.format
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class MedicineOrdersEventTest {
    private val events = Events()
    private val orders = Orders()

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(
        ChronoUnit.SECONDS), commandId: CommandId = DomainId.create()) {
        EventCommandHandler(events) {
            includeOrdersEvents(orders)
        }.handle(DomainId.nil(), buildCommandData(subCommand, callsign, timestamp, commandId))
    }

    private fun createMedicineOrderLine() = MedicineOrderLine(
        id = DomainId.create(),
        timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS),
        instructions = "Take your meds",
        orderType = OrderType.MEDICATION.dataString,
        orderStatus = OrderStatus.IN_PROGRESS.dataString,
        frequency = Interval(1, 30),
        lastOccurrence = Instant.now().minusSeconds(3600).truncatedTo(ChronoUnit.SECONDS),
        provider = Contact("Dr Phil", "**********", "<EMAIL>"),
        signature = Signature("Dr Phil", Instant.now().truncatedTo(ChronoUnit.SECONDS), ByteArray(100)),
        orderedMedicine = OrderedMedicine(
            name = "Ibuprofen",
            ndc = "NDC",
            rxcui = "RXCUI",
            medId = DomainId.create("11111111"),
            route = "IO",
            volume = 200f,
            unit = "mg",
            serialNumber = "frootloops",
            expirationDate = "someday",
            type = "medicine",
            exportName = "Ibuprofen"
        )
    )

    private fun createBlankMedicineOrderLine() = MedicineOrderLine(
        instructions = "",
        orderType = "",
        frequency = Interval(""),
        orderedMedicine = createBlankMedicine()
    )

    private fun createBlankMedicine() = OrderedMedicine(
        name = "",
        ndc = "",
        rxcui = "",
        medId = DomainId.nil(),
        route = "",
        volume = 0f,
        unit = "",
        serialNumber = "",
        expirationDate = "",
        type = "",
        exportName = ""
    )

    private fun createUpdatedMedicine() = OrderedMedicine(
        name = "Acetaminophen",
        ndc = "NDC2",
        rxcui = "RXCUI2",
        medId = DomainId.create("12345678"),
        route = "IV",
        volume = 50f,
        unit = "cc",
        serialNumber = "frostedflakes",
        expirationDate = "whenever",
        type = "meds",
        exportName = "Acetaminophen"
    )

    @Test
    fun testAddMedicineOrderLine() {
        val newOrderLine = createMedicineOrderLine()
        val dateString = newOrderLine.lastOccurrence?.format(Patterns.mdhm_24_space_comma_colon, false)

        Assert.assertTrue(events.list.isEmpty())

        handle(buildAddMedicineOrderLineCommand(newOrderLine))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Added Medication Order:\nTitle: Ibuprofen, IO, 200 mg\nInstructions: Take your meds\nFrequency: Q1H30MIN\nLast Administration: $dateString\nProvider: Dr Phil", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.ORDERS.dataString, events.list[0].eventType)
    }

    @Test
    fun testAddMedicineOrderLine_ContactNameIsNull() {
        val newOrderLine = createMedicineOrderLine().copy(provider = Contact())
        val dateString = newOrderLine.lastOccurrence?.format(Patterns.mdhm_24_space_comma_colon, false)

        Assert.assertTrue(events.list.isEmpty())

        handle(buildAddMedicineOrderLineCommand(newOrderLine))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Added Medication Order:\nTitle: Ibuprofen, IO, 200 mg\nInstructions: Take your meds\nFrequency: Q1H30MIN\nLast Administration: $dateString", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.ORDERS.dataString, events.list[0].eventType)
    }

    @Test
    fun testAddOrderLine_Blank() {
        val newOrderLine = createBlankMedicineOrderLine()

        Assert.assertTrue(events.list.isEmpty())

        handle(buildAddMedicineOrderLineCommand(newOrderLine))
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Added Custom Order:\nTitle: \nInstructions: \nFrequency: ", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.ORDERS.dataString, events.list[0].eventType)
    }

    @Test
    fun testUpdateOrderLine() {
        val oldOrderLine = createMedicineOrderLine()
        orders += oldOrderLine
        Assert.assertTrue(events.list.isEmpty())

        val lastOccurrence = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        handle(
            buildUpdateMedicineOrderLineCommand(
                id = oldOrderLine.id,
                timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS).plusSeconds(60),
                instructions = "Do stuff",
                frequency = Interval("Once"),
                lastOccurrence = lastOccurrence,
                provider = Contact("Dr Strangelove", "**********", "<EMAIL>"),
                medicine = createUpdatedMedicine()
            )
        )
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Updated Medication Order:\nTitle: Acetaminophen, IV, 50 cc\nInstructions: Do stuff\nFrequency: Once\nLast Administration: ${lastOccurrence.format(Patterns.mdhm_24_space_comma_colon)}\nProvider: Dr Strangelove", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.ORDERS.dataString, events.list[0].eventType)
    }

    @Test
    fun testUpdateOrderLine_ContactNameNull() {
        val oldOrderLine = createMedicineOrderLine()
        orders += oldOrderLine
        Assert.assertTrue(events.list.isEmpty())

        val lastOccurrence = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        handle(
            buildUpdateMedicineOrderLineCommand(
                id = oldOrderLine.id,
                timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS).plusSeconds(60),
                instructions = "Do stuff",
                frequency = Interval("Once"),
                lastOccurrence = lastOccurrence,
                provider = Contact(null, "**********", "<EMAIL>"),
                medicine = createUpdatedMedicine()
            )
        )
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Updated Medication Order:\nTitle: Acetaminophen, IV, 50 cc\nInstructions: Do stuff\nFrequency: Once\nLast Administration: ${lastOccurrence.format(Patterns.mdhm_24_space_comma_colon)}", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.ORDERS.dataString, events.list[0].eventType)
    }

    @Test
    fun testUpdateOrderLine_NoPreExisting() {
        val oldOrderLine = createMedicineOrderLine()
        // old order line does not really exist, don't add it to the orders
        Assert.assertTrue(events.list.isEmpty())

        val lastOccurrence = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        handle(
            buildUpdateMedicineOrderLineCommand(
                id = oldOrderLine.id,
                timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS).plusSeconds(60),
                instructions = "Do stuff",
                frequency = Interval("Once"),
                lastOccurrence = lastOccurrence,
                provider = Contact("Dr Strangelove", "**********", "<EMAIL>"),
                medicine = createUpdatedMedicine()
            )
        )
        Assert.assertEquals(0, events.list.size)
    }

    @Test
    fun testUpdateOrderLine_NullValues() {
        val oldOrderLine = createMedicineOrderLine()
        orders += oldOrderLine
        Assert.assertTrue(events.list.isEmpty())

        handle(
            buildUpdateMedicineOrderLineCommand(
                id = oldOrderLine.id,
                timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS).plusSeconds(60),
                instructions = "",
                frequency = Interval(""),
                medicine = createBlankMedicine(),
            )
        )
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Updated Medication Order:\nTitle: \nInstructions: \nFrequency: ", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.ORDERS.dataString, events.list[0].eventType)
    }

    @Test
    fun testUpdateOrderLineStatus() {
        val oldOrderLine = createMedicineOrderLine()
        orders += oldOrderLine
        Assert.assertTrue(events.list.isEmpty())

        handle(
            buildUpdateMedicineOrderLineStatusCommand(
                id = oldOrderLine.id,
                orderStatus = OrderStatus.COMPLETE
            )
        )
        Assert.assertEquals(1, events.list.size)
        Assert.assertEquals("Updated Medication Order status from:\nIn Progress to Complete", events.list[0].event)
        Assert.assertEquals(KnownEventTypes.ORDERS.dataString, events.list[0].eventType)
    }
    @Test
    fun testUpdateOrderLineStatus_NoOrderLineExists() {
        Assert.assertTrue(events.list.isEmpty())

        handle(
            buildUpdateMedicineOrderLineStatusCommand(
                id = DomainId.create(),
                orderStatus = OrderStatus.COMPLETE
            )
        )
        Assert.assertEquals(0, events.list.size)
    }
}