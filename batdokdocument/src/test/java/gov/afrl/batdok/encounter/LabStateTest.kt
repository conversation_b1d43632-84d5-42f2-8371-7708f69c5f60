package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.commands.buildLogLabCommand
import gov.afrl.batdok.encounter.commands.buildRemoveLabCommand
import gov.afrl.batdok.encounter.commands.buildUpdateLabCommand
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.encounter.ids.LabId
import gov.afrl.batdok.encounter.panel.KnownLabs
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class LabStateTest {

    val docId = DomainId.create<DocumentId>()
    val labs = Labs()

    private fun handle(subCommand: Message, callsign: String = "CS", timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS), commandId: CommandId = DomainId.create()) {
        labs.handlers.handle(docId, buildCommandData(subCommand, callsign, timestamp, commandId = commandId))
    }

    @Test
    fun testLogLab(){
        val labId = DomainId.create<LabId>()
        val labList = listOf(
            IndividualLab(KnownLabs.K, "1.2", id = labId),
            IndividualLab("Test", "3.4", "Unit")
        )
        handle(buildLogLabCommand(labList))

        Assert.assertEquals(labId, labs.list[0].id)
        val parsedLab = labs
        Assert.assertEquals(2, parsedLab.list.size)
        Assert.assertTrue(KnownLabs.K in parsedLab)
        Assert.assertEquals("1.2", parsedLab[KnownLabs.K][0].value)
        Assert.assertTrue("Test" in parsedLab)
        Assert.assertEquals("3.4", parsedLab["Test"][0].value)
        Assert.assertEquals("Unit", parsedLab["Test"][0].unit)
        Assert.assertEquals(docId, parsedLab.list[0].documentId)
    }

    @Test
    fun testUpdateLab(){
        val labList = listOf(
            IndividualLab(KnownLabs.K, "1.2", documentId = docId),
            IndividualLab("Test", "3.4", documentId = docId)
        )
        labs += labList

        val preLab = labs
        Assert.assertEquals(2, preLab.list.size)
        Assert.assertTrue(KnownLabs.K in preLab)
        Assert.assertEquals("1.2", preLab[KnownLabs.K][0].value)
        Assert.assertTrue("Test" in preLab)
        Assert.assertEquals("3.4", preLab["Test"][0].value)
        Assert.assertEquals(docId, preLab.list[0].documentId)

        val newLabs = listOf(
            labList[0].copy(KnownLabs.K.dataString, "5.6"),
            labList[1].copy("Test", "3.4"),
            IndividualLab("New Item", "555.0", documentId = docId)
        )
        newLabs.forEach {
            handle(buildUpdateLabCommand(it))
        }

        val postLab = labs
        Assert.assertEquals(3, postLab.list.size)
        //Check new updates
        Assert.assertTrue(KnownLabs.K in postLab)
        Assert.assertEquals("5.6", postLab[KnownLabs.K][0].value)
        Assert.assertTrue("New Item" in postLab)
        Assert.assertEquals("555.0", postLab["New Item"][0].value)

        //Check old values
        Assert.assertTrue("Test" in postLab)
        Assert.assertEquals("3.4", postLab["Test"][0].value)
        Assert.assertEquals(docId, postLab.list[0].documentId)
    }

    @Test
    fun testUpdateLab_ClearValues(){
        val labList = listOf(
            IndividualLab(KnownLabs.K, "1.2", documentId = docId),
            IndividualLab("Test", "3.4", documentId = docId)
        )
        labs += labList

        val preLab = labs
        Assert.assertEquals(2, preLab.list.size)
        Assert.assertTrue(KnownLabs.K in preLab)
        Assert.assertEquals("1.2", preLab[KnownLabs.K][0].value)
        Assert.assertTrue("Test" in preLab)
        Assert.assertEquals("3.4", preLab["Test"][0].value)
        Assert.assertEquals(docId, preLab.list[0].documentId)

        val newLab = labList[1].copy("Test", null)
        handle(buildUpdateLabCommand(newLab))

        val postLab = labs
        Assert.assertEquals(1, postLab.list.size)

        //Check new updates
        Assert.assertFalse("Test" in postLab)

        //Check old values
        Assert.assertTrue(KnownLabs.K in postLab)
        Assert.assertEquals("1.2", postLab[KnownLabs.K][0].value)
        Assert.assertEquals(docId, postLab.list[0].documentId)
    }

    @Test
    fun testRemoveLabs(){
        val timestamp = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val labList = listOf(
            IndividualLab(KnownLabs.K, "1.2"),
            IndividualLab("Test", "3.4")
        )

        labs += labList

        val preLab = labs
        Assert.assertEquals(2, preLab.list.size)
        Assert.assertTrue(KnownLabs.K in preLab)
        Assert.assertEquals("1.2", preLab[KnownLabs.K][0].value)
        Assert.assertTrue("Test" in preLab)
        Assert.assertEquals("3.4", preLab["Test"][0].value)

        handle(buildRemoveLabCommand(labList[1].id))

        Assert.assertEquals(1, labs.list.size)
        Assert.assertTrue(KnownLabs.K in preLab)
        Assert.assertFalse("Test" in preLab)
    }

    @Test
    fun testDoNotSendPreviousData(){
        val lab = IndividualLab(KnownLabs.K, "1.2")

        val command = buildUpdateLabCommand(lab, lab)

        Assert.assertFalse(command.labValue.id.isEmpty)
        Assert.assertFalse(command.labValue.hasName())
        Assert.assertFalse(command.labValue.hasValue())
        Assert.assertFalse(command.labValue.hasTime())
        Assert.assertFalse(command.labValue.hasUnit())
    }
}