package gov.afrl.batdok

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.*
import gov.afrl.batdok.encounter.commands.*
import gov.afrl.batdok.encounter.ids.EventId
import gov.afrl.batdok.encounter.medicine.Medicine
import gov.afrl.batdok.encounter.observation.CommonObservations
import gov.afrl.batdok.encounter.panel.KnownLabs
import gov.afrl.batdok.util.buildCommandData
import mil.af.afrl.batman.batdokid.DomainId
import org.junit.Assert
import org.junit.Test
import java.time.Instant

class ExampleUseCases {

    val document = Document()

    fun handle(vararg message: Message, timestamp: Instant = Instant.now()){
        document.handle(message.map { buildCommandData(it, timestamp = timestamp) })
    }

    /**
     * Example of how to build up a complaint card using update functions and links
     */
    @Test
    fun testComplaintCardCollection(){
        var complaint = Complaint("")
        var relatedItems = listOf<Any>()
        fun getUpdatedComplaint() {
            complaint = document.subjective.complaints.list[0]
            relatedItems = complaint.getAssociatedItems(document)
        }
        //Create Complaint
        handle(buildAddComplaintCommand(CommonComplaints.BACK_PAIN.dataString))
        getUpdatedComplaint()
        Assert.assertEquals(CommonComplaints.BACK_PAIN.dataString, complaint.complaint)

        //Add History
        handle(buildUpdateComplaintCommand(complaint.id, history = "History"))
        getUpdatedComplaint()
        Assert.assertEquals("History", complaint.history)

        //Add Review of Systems
        //TODO

        //Add Assessments
        handle(buildLogObservationCommand(CommonObservations.HEAD_INJURY.dataString))
        val observation = document.observations.list[0]
        handle(complaint.buildAssociationCommand(observation))
        getUpdatedComplaint()
        Assert.assertEquals(1, relatedItems.size)
        Assert.assertEquals(observation, relatedItems[0])

        //Add Plan
        handle(buildLogMedicineCommand(Medicine("Medicine")))
        val medicine = document.medicines.list[0]
        val eventId = DomainId.create<EventId>()

        handle(
            buildAddEventCommand(Instant.now(), "Plan Text", false, KnownEventTypes.PLANNING, true, eventId),
            complaint.buildAssociationCommand(medicine),
            complaint.buildAssociationCommand(eventId)
        )

        val event = document.events[eventId]

        //Make sure all items are there that we expect
        getUpdatedComplaint()

        Assert.assertEquals(CommonComplaints.BACK_PAIN.dataString, complaint.complaint)
        Assert.assertEquals("History", complaint.history)
        //TODO: Assert Review of Systems
        Assert.assertEquals(3, relatedItems.size)
        Assert.assertEquals(observation, relatedItems[0])
        Assert.assertEquals(medicine, relatedItems[1])
        Assert.assertEquals(event, relatedItems[2])
    }

    @Test
    fun testLabsFlow(){
        var labs = Labs()
        var panels = listOf<LabPanel>()
        fun getUpdatedLab(){
            labs = document.labs
            panels = document.labs.getPanels(document.links)
        }
        //Create Labs
        val lab1 = IndividualLab(KnownLabs.K, "1234")
        val lab2 = IndividualLab("Custom", "Positive")

        handle(buildLogLabCommand(lab1, lab2))
        getUpdatedLab()

        Assert.assertEquals(2, labs.size)
        Assert.assertTrue(lab1.id in labs)
        Assert.assertTrue(lab2.id in labs)

        //Link together into a Panel
        handle(buildCreateLinkCommand(
            Link(listOf(lab1.id, lab2.id), CommonLinkRelationships.BASIC_METABOLIC_PANEL.dataString)
        ))

        getUpdatedLab()

        Assert.assertEquals(1, panels.size)
        Assert.assertEquals(2, panels[0].labs.size)

        //Update Lab Value
        handle(buildUpdateLabCommand(lab2.copy(value = "Negative")))
        getUpdatedLab()

        Assert.assertEquals(1, panels.size)
        Assert.assertEquals(2, panels[0].labs.size)
        Assert.assertEquals("Negative", labs[lab2.id]?.value)
        Assert.assertEquals("Negative", panels[0].labs[1].value)

        //Add Lab to Panel
        val lab3 = IndividualLab("Test", "Test")
        handle(buildLogLabCommand(lab3))
        handle(buildAddRemoveFromLinkCommand(panels[0].id, listOf(lab3.id), listOf()))
        getUpdatedLab()

        Assert.assertEquals(1, panels.size)
        Assert.assertEquals(3, panels[0].labs.size)
        Assert.assertEquals(3, labs.size)
        Assert.assertTrue(lab3.id in labs)

        //Remove Lab
        handle(buildRemoveLabCommand(lab1.id))
        getUpdatedLab()

        Assert.assertEquals(2, labs.size)
        Assert.assertEquals(1, panels.size)
        Assert.assertEquals(2, panels[0].labs.size)

        Assert.assertFalse(lab1.id in labs)
        Assert.assertTrue(lab2.id in labs)
        Assert.assertTrue(lab3.id in labs)
    }

    @Test
    fun testLabsFlow_BuildEntirePanelAtOnce(){
        var labs = Labs()
        var panels = listOf<LabPanel>()
        fun getUpdatedLab(){
            labs = document.labs
            panels = document.labs.getPanels(document.links)
        }
        //Create Labs
        val lab1 = IndividualLab(KnownLabs.K, "1234")
        val lab2 = IndividualLab("Custom", "Positive")
        handle(*Labs.buildPanelCommands(
            CommonLinkRelationships.BASIC_METABOLIC_PANEL.dataString,
            lab1, lab2
        ).toTypedArray())

        getUpdatedLab()

        Assert.assertEquals(2, labs.size)
        Assert.assertTrue(lab1.id in labs)
        Assert.assertTrue(lab2.id in labs)

        Assert.assertEquals(1, panels.size)
        Assert.assertEquals(2, panels[0].labs.size)
    }
}