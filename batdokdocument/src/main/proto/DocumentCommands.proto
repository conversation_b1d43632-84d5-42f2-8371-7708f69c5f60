syntax = "proto3";

option java_package = "gov.afrl.batdok.commands.proto";

import "google/protobuf/any.proto";
import "document/SharedProtobufObjects.proto";

message DocumentCommand{
  bytes encounterId = 1;
  bytes ownerId = 2;
  int32 checksum = 3;
  string mode = 4;
  repeated CommandData commands = 5;
}

message CommandData{
  bytes commandId = 1; // 16-byte UUID to identify the data
  bytes userId = 2; // Reference ID of who performed the action
  string callsign = 3;
  uint64 timestamp = 4;
  google.protobuf.Any data = 5;
  CompatibilityRange compatibleRange = 6;

  FlightMetadata flightMetadata = 7;
}

message FlightMetadata{
  bool isPreFlight = 1;
}