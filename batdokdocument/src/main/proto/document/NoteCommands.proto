syntax = "proto3";

option java_package = "gov.afrl.batdok.commands.proto";
import "document/SharedProtobufObjects.proto";
import "google/protobuf/wrappers.proto";

message Note {
  // ID of the note
  bytes noteId = 1;

  // data fields
  string message = 2;
  int64 timestamp = 3;
}

message AddNote {
  Note note = 1;
}

message UpdateNote {
  // new data of the note
  Note note = 1;
}

message RemoveNote {
  bytes noteId = 1;
}