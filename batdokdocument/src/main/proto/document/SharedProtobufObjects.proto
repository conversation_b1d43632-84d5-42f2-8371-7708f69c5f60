syntax = "proto3";

option java_package = "gov.afrl.batdok.commands.proto";

import "FieldOptions.proto";
import "google/protobuf/wrappers.proto";

//Use this to show which versions the command is compatible with
message CompatibilityRange{
    int32 inclusiveStart = 1;
    int32 exclusiveEnd = 2;
}

enum MedType {
    BLOOD_PRODUCT=0;
    FLUID=1;
    ANALGESIC=2;
    ANTIBIOTIC=3;
    OTHER_MED=4;
}

message MedNameOrNumber {
    oneof med {
        string medName = 1;
        int32 medNumber = 2;
    }
}

message CompatibleEnum {
    string string = 1;
    int32 enum = 2;
}

message Medicine {
    NullableStringValue ndc = 1;
    NullableStringValue rxcui = 2;
    google.protobuf.StringValue name = 3;
    CompatibleEnum route = 4;

    NullableFloatValue volume = 5;
    CompatibleEnum unit = 6;

    NullableStringValue serialNumber = 7;
    NullableStringValue expirationDate = 8;

    CompatibleEnum type = 9;

    bytes medId = 10;

    NullableStringValue exportName = 11;
}

message NullableInt64Value{
    google.protobuf.Int64Value value = 1;
}

message NullableInt32Value{
    google.protobuf.Int32Value value = 1;
}

message NullableStringValue{
    google.protobuf.StringValue value = 1;
}

message NullableBoolValue{
    google.protobuf.BoolValue value = 1;
}

message NullableFloatValue{
    google.protobuf.FloatValue value = 1;
}

message NullableDoubleValue{
    google.protobuf.DoubleValue value = 1;
}
message NullableEnumList{
    repeated CompatibleEnum value = 1;
}

message Contact {
    google.protobuf.StringValue name = 1;
    google.protobuf.StringValue phone = 2;
    google.protobuf.StringValue email = 3;
}

message PhoneNumbers {
    NullableStringValue dsn = 1;
    NullableStringValue comm = 2;
}