syntax = "proto3";

option java_package = "gov.afrl.batdok.commands.proto";
import "document/SharedProtobufObjects.proto";

message CreateLink{
  bytes linkId = 1;
  repeated bytes ids = 2;
  CompatibleEnum comment = 3;
}

message AddRemoveFromLink{
  bytes linkId = 1;
  repeated bytes addedIds = 2;
  repeated bytes removedIds = 3;
}

message UpdateLinkComment{
  bytes linkId = 1;
  CompatibleEnum comment = 2;
}

message DeleteLink{
  bytes linkId = 1;
}