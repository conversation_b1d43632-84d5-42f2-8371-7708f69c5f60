syntax = "proto3";

option java_package = "gov.afrl.batdok.commands.proto";
import "document/SharedProtobufObjects.proto";
import "google/protobuf/wrappers.proto";
import "document/Signatures.proto";
import "document/orders/OrderCommands.proto";


message AddMedicineOrderLine {
  AddOrderLine addOrderLine = 1;
  Medicine medicine = 2;
}

message UpdateMedicineOrderLine {
  UpdateOrderLine updateOrderLine = 1;
  Medicine medicine = 2;
}

message UpdateMedicineOrderLineStatus {
  bytes orderLineId = 1;
  CompatibleEnum orderStatus = 2;
}

message RemoveMedicineOrderLine {
  bytes orderLineId = 1;
  CompatibleEnum orderStatus = 2;
}