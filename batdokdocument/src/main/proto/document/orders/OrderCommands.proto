syntax = "proto3";

option java_package = "gov.afrl.batdok.commands.proto";
import "document/SharedProtobufObjects.proto";
import "google/protobuf/wrappers.proto";
import "document/Signatures.proto";


message AddOrderLine {
  // ID of the order line
  bytes orderLineId = 1;
  // data fields
  int64 timestamp = 2;
  string title = 3;
  string instructions = 4;
  CompatibleEnum type = 5;
  CompatibleEnum orderStatus = 6;
  string frequency = 7;
  NullableInt64Value lastOccurrence = 8;
  optional Contact provider = 9;
  optional Signature signature = 10;
}

message UpdateOrderLine {
  // ID of the order line
  bytes orderLineId = 1;
  // updatable data fields
  int64 timestamp = 2;
  string title = 3;
  string instructions = 4;
  string frequency = 5;
  NullableInt64Value lastOccurrence = 6;
  optional Contact provider = 7;
  optional Signature signature = 8;
}

message UpdateOrderLineStatus {
  bytes orderLineId = 1;
  CompatibleEnum orderStatus = 2;
}

message RemoveOrderLine {
  bytes orderLineId = 1;
  CompatibleEnum orderStatus = 2;
}



