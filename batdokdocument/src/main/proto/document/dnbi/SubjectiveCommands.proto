syntax = "proto3";

option java_package = "gov.afrl.batdok.commands.proto";

import "google/protobuf/any.proto";
import "google/protobuf/descriptor.proto";
import "google/protobuf/wrappers.proto";
import "EnumValueOptions.proto";
import "FieldOptions.proto";
import "document/SharedProtobufObjects.proto";


message UpdateChiefComplaintCommand {
  option deprecated = true;
  string symptom = 1;
  bool checked = 2;
}

message AddComplaintCommand {
  CompatibleEnum symptom = 1;
  string symptomHistory = 2;
  string reviewOfSystems = 4;
  CompatibleEnum disposition = 5;
  bytes id = 3;
}

message UpdateComplaintCommand {
  CompatibleEnum symptom = 1;
  google.protobuf.StringValue symptomHistory = 2;
  google.protobuf.StringValue reviewOfSystems = 4;
  CompatibleEnum disposition = 5;
  bytes id = 3;
}

message RemoveComplaintCommand {
  bytes id = 1;
}

message UpdateOpqrstlCommand {
  message QualityWrapper{
    repeated CompatibleEnum qualities = 1;
  }

  CompatibleEnum onset = 1;
  google.protobuf.StringValue provocationBetterWhen = 2;
  google.protobuf.StringValue provocationWorseWhen = 3;
  QualityWrapper qualities = 4;
  google.protobuf.Int32Value radiates = 5; // O=False, 1=True, -1=Clear
  google.protobuf.Int32Value severity = 6; // -1=Clear
  google.protobuf.Int64Value time = 7; // -1=Clear
  google.protobuf.Int64Value lastOralIntake = 8; // -1=Clear
}

message UpdateReviewOfSystemsCommand {
  option deprecated = true;
  string symptom = 1;
  google.protobuf.BoolValue checked = 2;
}

message UpdateHpiCommand {
  string history = 1;
}

message UpdatePastHistoryCommand {
  CompatibleEnum type = 1;
  string history = 2;
}

message UpdateHistoryLastEventCommand {
  CompatibleEnum type = 1;
  google.protobuf.Int64Value date = 2;
  google.protobuf.Any extras = 3;
}

message BloodDonationExtra {
  int32 volume = 1;
}