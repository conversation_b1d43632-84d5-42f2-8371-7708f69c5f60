syntax = "proto2";

option java_package = "gov.afrl.batdok.commands.proto";

import "FieldOptions.proto";
import "google/protobuf/wrappers.proto";

message StratevacVital {
  optional float cvp = 1 [default = -1, (introduced) = 2];
  optional float icp = 2 [default = -1, (introduced) = 2];
  optional float cpp = 3 [default = -1, (introduced) = 2];
  optional float map = 4 [default = -1, (introduced) = 2];
}

message ChangeCiteNumberCommand{
  optional string citeNumber = 1 [(introduced) = 2];
}

message ChangeFlightInfoCommand{
  optional bytes flightInfoId = 1 [(introduced) = 2];
  optional string tail = 2 [(introduced) = 2];
  optional string origin = 3 [(introduced) = 2];
  optional string destination = 4 [(introduced) = 2];
  optional google.protobuf.Int64Value takeoff = 5 [(introduced) = 2];
  optional google.protobuf.Int64Value landing = 6 [(introduced) = 2];
  optional google.protobuf.Int64Value maxAlt = 7 [(introduced) = 2];
  optional string unit = 8 [(introduced) = 2];
}