syntax = "proto3";

option java_package = "gov.afrl.batdok.commands.proto";

import "document/stratevac/StratevacRnCommands.proto";
import "document/SharedProtobufObjects.proto";
import "FieldOptions.proto";
import "google/protobuf/wrappers.proto";

message AddIOItem{
  option deprecated = true;
  bool isInput = 1;
  IOItem data = 2;
  bytes id = 3;
}

message UpdateIOItem{
  option deprecated = true;
  bytes itemId = 1;
  google.protobuf.Int64Value time = 2;

  bool isInput = 3;
  IOItem data = 4;
}

message RemoveIOItem{
  option deprecated = true;
  bytes itemId = 1;
}

message IOItem{
  option deprecated = true;
  CompatibleEnum name = 1;
  google.protobuf.DoubleValue value = 2;
  CompatibleEnum route = 3;
  CompatibleEnum unit = 4;
}

message IntakeOutputItem{
  google.protobuf.Int64Value timestamp = 1;
  CompatibleEnum type = 2;
  NullableStringValue label = 3;
  google.protobuf.DoubleValue volume = 4;
  CompatibleEnum unit = 5;
  google.protobuf.BoolValue isIntake = 6;
}

message AddIntakeOutputItem{
  IntakeOutputItem data = 1;
  bytes id = 2;
}


message UpdateIntakeOutputItem{
  bytes itemId = 1;
  IntakeOutputItem data = 2;
}

message RemoveIntakeOutputItem{
  bytes itemId = 1;
}