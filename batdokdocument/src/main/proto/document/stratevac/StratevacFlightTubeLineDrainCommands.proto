syntax = "proto2";

option java_package = "gov.afrl.batdok.commands.proto";

import "FieldOptions.proto";

message TubeLineDrainCommand {
  optional UpdateLineCommand updateLineCommand = 2 [(introduced) = 2];
  optional UpdateDrainCommand updateDrainCommand = 3 [(introduced) = 2];
}

message UpdateLineCommand {
  optional bool remove = 1 [(introduced) = 2];
  optional bytes itemId = 2 [(introduced) = 2];
  optional int64 timestamp = 3 [(introduced) = 2];

  oneof type {
    UpdateALineCommand updateALineCommand = 4 [(introduced) = 2];
    UpdateOtherLineCommand updateOtherLineCommand = 5 [(introduced) = 2];
  }
}

message UpdateALineCommand {
  optional string side = 1 [(introduced) = 2];
  optional string location = 2 [(introduced) = 2];
}

message UpdateOtherLineCommand{
   optional string name = 1 [(introduced) = 2];
   optional string side = 2 [(introduced) = 2];
   optional string location = 3 [(introduced) = 2];
}

message UpdateDrainCommand {
  optional bool remove = 1 [(introduced) = 2];
  optional bytes itemId = 2 [(introduced) = 2];
  optional int64 timestamp = 3 [(introduced) = 2];

  optional string name = 4 [(introduced) = 2];
  optional string side = 5 [(introduced) = 2];
  optional string function = 6 [(introduced) = 2];
  optional string location = 7 [(introduced) = 2];
}