syntax = "proto2";

option java_package = "gov.afrl.batdok.commands.proto";

import "document/SharedProtobufObjects.proto";
import "FieldOptions.proto";

message OrderCommand {
  oneof command {
    UpdateOrderCommand updateOrderCommand = 1 [(introduced) = 2];
    FulfillOrderCommand fulfillOrderCommand = 2 [(introduced) = 2];
    RemoveOrderCommand removeOrderCommand = 3 [(introduced) = 2];
  }
}

message MedOrder {
  optional Medicine medicine = 1 [(introduced) = 2];
  optional MedType logMedType = 2 [default = OTHER_MED, (introduced) = 2];
  optional string logMedTypeString = 3 [(introduced) = 2];
}

message UpdateOrderCommand {
  optional bytes orderId = 1 [(introduced) = 2];
  optional string message = 2 [(introduced) = 2];
  optional string interval = 3 [(introduced) = 2];
  oneof order {
    bytes protoCommand = 4 [(introduced) = 2];
    MedOrder medOrder = 5 [(introduced) = 2];
    string customOrder = 6 [(introduced) = 2];
    bool noChange = 7 [(introduced) = 2];  // Set if updating to keep previous extras
  }
  optional int64 creationTime = 8 [(introduced) = 2];
}

message FulfillOrderCommand {
  optional bytes orderId = 1 [(introduced) = 2];
}

message RemoveOrderCommand {
  optional bytes orderId = 1 [(introduced) = 2];
}
