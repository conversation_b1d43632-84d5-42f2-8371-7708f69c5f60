syntax = "proto3";

option java_package = "gov.afrl.batdok.commands.proto";

import "FieldOptions.proto";
import "document/SharedProtobufObjects.proto";
import "google/protobuf/any.proto";
import "google/protobuf/wrappers.proto";
import "document/EquipmentCommands.proto";

message ChangeDecisionCommand {
  optional CompatibleEnum decisionType = 1 [(introduced) = 2];
  optional bool checked = 2 [(introduced) = 2];
}

message ChangeCareCommand {
  optional CompatibleEnum careType = 1 [(introduced) = 2];
  optional bool checked = 2 [(introduced) = 2];
}

message ChangeInfectionControlPrecautionCommand {
  optional CompatibleEnum infectionControlPrecaution = 1 [(introduced) = 2];
  optional bool checked = 2 [(introduced) = 2];
}

message ChangeProcedureCommand {
  optional CompatibleEnum procedure = 1 [(introduced) = 2];
  optional bool checked = 2 [(introduced) = 2];
  optional google.protobuf.Any extras = 3;
}

message LineExtras{
  optional bool artLine = 1 [(introduced) = 2];
  optional bool centralLine = 2 [(introduced) = 2];
}

message AddRemoveMajorEventCommand {
  optional CompatibleEnum location = 1 [(introduced) = 2];
  repeated MajorEventItem addedItems = 2 [(introduced) = 2];
  repeated MajorEventItem removedItems = 3 [(introduced) = 2];
}

message MajorEventItem{
  optional CompatibleEnum type = 1 [(introduced) = 2];
  optional google.protobuf.Any extras = 2 [(introduced) = 2];
}

message ArrhythmiaExtras {
  optional google.protobuf.BoolValue tachy = 1 [(introduced) = 2];
  optional google.protobuf.BoolValue narrow = 2 [(introduced) = 2];
}

message BleedingExtras {
  optional google.protobuf.StringValue site = 1 [(introduced) = 2];
  optional google.protobuf.StringValue estVol = 2 [(introduced) = 2];
}

message ChangeInsuranceCommand {
  google.protobuf.StringValue companyName = 1;
  google.protobuf.StringValue companyAddress = 2;
  google.protobuf.StringValue companyPhone = 3;
  google.protobuf.StringValue policyNumber = 4;
  google.protobuf.StringValue relationToPolicyHolder = 5;
}

message ChangeAttendingPhysicianCommand {
  Contact physician = 1;
}
