syntax = "proto3";

option java_package = "gov.afrl.batdok.commands.proto";

import "FieldOptions.proto";
import "document/SharedProtobufObjects.proto";
import "google/protobuf/wrappers.proto";

message LogBloodCommand{
  BloodProduct bloodProduct = 2;
  bytes id = 3;
}

message UpdateBloodCommand{
  bytes id = 1;
  int64 timestamp = 2;
  BloodProduct bloodProduct = 3;
}

message RemoveBloodCommand{
  bytes id = 1;
  bool documentationError = 2;
}

message BloodProduct{
  CompatibleEnum bloodName = 1;
  NullableStringValue productCode = 2;
  NullableInt64Value volume = 3;
  CompatibleEnum unit = 4;
  NullableStringValue donationIdNumber = 5;
  NullableStringValue expirationDate = 6;
  CompatibleEnum bloodType = 7;
  NullableInt32Value bloodAge = 8;
  CompatibleEnum bloodAgeUnit = 11;
  NullableInt64Value collectionDate = 9;
  CompatibleEnum route = 10;
}