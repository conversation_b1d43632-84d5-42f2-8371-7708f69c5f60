syntax = "proto3";

option java_package = "gov.afrl.batdok.commands.proto";

import "FieldOptions.proto";
import "google/protobuf/wrappers.proto";
import "document/SharedProtobufObjects.proto";
import "document/LabCommands.proto";

message LogPanelCommand {
    option deprecated = true;
    repeated LabEntry labEntries = 1;
    bytes id = 2;
}

message UpdatePanelCommand {
    option deprecated = true;
    optional bytes panelid = 1;
    optional int64 time = 2;
    repeated LabEntry labEntries = 3;
}

message RemovePanelCommand {
    option deprecated = true;
    bytes panelId = 1;
}