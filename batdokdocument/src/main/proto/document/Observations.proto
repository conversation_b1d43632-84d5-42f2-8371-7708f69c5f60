syntax = "proto3";

option java_package = "gov.afrl.batdok.commands.proto";

import "FieldOptions.proto";
import "document/SharedProtobufObjects.proto";
import "google/protobuf/any.proto";
import "google/protobuf/wrappers.proto";

message LogObservation{
  Observation observation = 1;
  bytes id = 2;
  int64 timestamp = 3;
}

message UpdateObservation{
  bytes id = 1;
  int64 timestamp = 2;
  google.protobuf.Any data = 3;
}

message RemoveObservation{
  bytes id = 1;
  bool documentationError = 2;
}

message Observation{
  CompatibleEnum name = 1;
  google.protobuf.Any data = 2;
  int64 timestamp = 3;
}

message BooleanData{
  NullableBoolValue bool = 1;
}

message PupilDilation {
  NullableInt32Value perrlaSizeLeft = 1 [(introduced) = 2];
  NullableInt32Value perrlaSizeRight = 2 [(introduced) = 2];
}

message PupilReaction {
  NullableBoolValue side = 1 [(introduced) = 2];
  NullableBoolValue isReactive = 2 [(introduced) = 2];
}

message RespSideData {
  CompatibleEnum type = 1 [(introduced) = 2];
}

message PulseData{
  CompatibleEnum quality = 1;
  CompatibleEnum location = 2;
}

message PulseValuesData{
  CompatibleEnum brac = 1;
  CompatibleEnum car = 2;
  CompatibleEnum fem = 3;
  CompatibleEnum ped = 4;
  CompatibleEnum rad = 5;
  CompatibleEnum temp = 6;
}

message TransfusionIndicationData{
  google.protobuf.BoolValue amputation = 1;
  google.protobuf.BoolValue hrOver120 = 2;
  google.protobuf.BoolValue sbpUnder90 = 3;
}

message FeelingData{
  CompatibleEnum type = 1;
}

message RhythmData {
  CompatibleEnum type = 1;
}

message CapillaryRefillData {
  int32 time = 1;
}

message GastroData {
  CompatibleEnum type = 1;
}

message IntegData {
  CompatibleEnum type = 1;
}

message RespEffortExtras{
  CompatibleEnum effort = 1;
}

message UltrasoundData{
  NullableStringValue description = 1;
}

message CasualtyPPEData{
  NullableEnumList ppe = 1;
}

message OtherDiagnosticsData{
  NullableStringValue description = 1;
}

message TextData{
  NullableStringValue description = 1;
}

//This will become a lab instead
message BloodSugarData{
  option deprecated = true;
  NullableDoubleValue value = 1;
  CompatibleEnum unit = 2;
}

message BreathSoundsData {
  NullableEnumList types = 1;
}

message PhysicalExamData{
  NullableStringValue finding = 1;
  CompatibleEnum location = 2;
}

message ExamData{
  NullableStringValue name = 1;
  NullableStringValue result = 2;
}

message ChestRiseFallData{
  CompatibleEnum riseFall = 1;
}

message EFastExamData {
  CompatibleEnum leftLungSliding = 1;
  CompatibleEnum rightLungSliding = 2;
  CompatibleEnum pericardialFluid = 3;
  CompatibleEnum rightUpperQuadrant = 4;
  CompatibleEnum leftUpperQuadrant = 5;
  CompatibleEnum suprapubicFluid = 6;
  CompatibleEnum interpretation = 7;
}
