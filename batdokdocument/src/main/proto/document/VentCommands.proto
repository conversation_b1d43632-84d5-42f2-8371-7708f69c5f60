syntax = "proto2";

option java_package = "gov.afrl.batdok.commands.proto";

import "FieldOptions.proto";
import "document/SharedProtobufObjects.proto";
import "google/protobuf/wrappers.proto";

message LogVentCommand {
    required Vent vent = 1 [(introduced) = 2];
    optional bytes id = 2;
    optional google.protobuf.Int64Value time = 3 [(introduced) = 2];
}

message UpdateVentCommand {
    optional bytes itemId = 1 [(introduced) = 2];
    optional google.protobuf.Int64Value time = 2 [(introduced) = 2];
    required Vent vent = 3 [(introduced) = 2];
}

message RemoveVentCommand {
    required bytes ventId = 1 [(introduced) = 2];
}

message Vent {
    optional CompatibleEnum mode = 3 [(introduced) = 2];
    optional NullableDoubleValue fio2 = 4 [(introduced) = 2];
    optional NullableInt32Value rate = 5 [(introduced) = 2];
    optional NullableInt32Value tv = 6 [(introduced) = 2];
    optional NullableDoubleValue peep = 7 [(introduced) = 2];
    optional CompatibleEnum ventilator = 8 [(introduced) = 2];
    optional google.protobuf.BytesValue vitalId = 9 [(introduced) = 2];
}