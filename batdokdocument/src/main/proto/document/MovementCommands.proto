syntax = "proto3";

option java_package = "gov.afrl.batdok.commands.proto";

import "google/protobuf/descriptor.proto";
import "FieldOptions.proto";
import "EnumValueOptions.proto";
import "google/protobuf/wrappers.proto";
import "document/SharedProtobufObjects.proto";

message ChangeMedMissionNumber {
  string parenText = 1 [(introduced) = 2];
  string otherText = 2 [(introduced) = 2];
}

message ChangeTailToTail {
  google.protobuf.BoolValue tailToTail = 1 [(introduced) = 2];
}

message ChangeLegNumber {
  google.protobuf.Int32Value legNumber = 1 [(introduced) = 2];
  google.protobuf.Int32Value totalLegs = 2 [(introduced) = 2];
}

message ChangeNinelineTime {
  google.protobuf.Int64Value time = 1 [(introduced) = 2];
}

message ChangeNinelinePlatform {

  CompatibleEnum platform = 1 [(introduced) = 2];
}

message ChangeDispatchEvac {
  CompatibleEnum evac = 1 [(introduced) = 2];
  string evacString = 2 [(introduced) = 2];
}

message ChangeAssessedEvac {
  CompatibleEnum evac = 1 [(introduced) = 2];
  string evacString = 2 [(introduced) = 2];
}

message ChangeCapability {

  repeated CompatibleEnum addedCapabilities = 1 [(introduced) = 2];
  repeated CompatibleEnum removedCapabilities = 2 [(introduced) = 2];
}

message ChangeNinelineLocation {
  bool isPickup = 1 [(introduced) = 2];
  NinelineLocation location = 2 [(introduced) = 2];
}

message NinelineLocation {
  google.protobuf.Int64Value time = 1;
  CompatibleEnum role = 2;
  CompatibleEnum region = 3;
  string location = 4;
}

message ChangeOriginatingMtf {
  string originatingMtf = 1;
}

message ChangeDestinationMtf {
  string destinationMtf = 1;
}

message ChangeReasonRegulated {
  CompatibleEnum reasonRegulated = 1;
}

message ChangeMaxStops {
  option deprecated = true;
  google.protobuf.Int32Value maxStops = 1;
}

message ChangeMaxNumberOfStops {
    CompatibleEnum maxStops = 1;
}

message ChangeMaxRons {
  option deprecated = true;
  google.protobuf.Int32Value maxRons = 1;
}

message ChangeMaxNumberOfRons {
  CompatibleEnum maxNumberOfRons = 1;
}

message ChangeAltitudeRestrictions {
  string altitudeRestrictions = 1;
}

message ChangeFlightLevel {
  string flightLevel = 1;
}

message ChangeReadyDate {
  google.protobuf.Int64Value readyDate = 1;
}

message ChangeMedicalAttendantsNeeded {
  google.protobuf.Int32Value medicalAttendantsNeeded = 1;
}

message ChangeNonMedicalAttendantsNeeded {
  google.protobuf.Int32Value nonMedicalAttendantsNeeded = 1;
}

message ChangeAttendants {
  repeated Attendant addedAttendants = 1;
  repeated Attendant removedAttendants = 2;
}

message ChangeClassification {
  CompatibleEnum classification = 1;
}

message ChangePrecedence {
  string precedence = 1;
}

message ChangeCriticalCare {
  google.protobuf.BoolValue criticalCare = 1;
}

// attendants
message Attendant {
  string firstName = 1;
  string middleName = 2;
  string lastName = 3;
  string gender = 4;
  float weight = 5;
  string grade = 6;
  bool isMedical = 7;
}

// reusing InfoCommands for most of our attendants commands

message ChangeIsMedicalCommand {
  google.protobuf.BoolValue isMedical = 1;
}
// end attendants

message ChangeAcceptingPhysicianCommand {
  Contact physician = 1;
}

message ChangeOriginPhoneCommand {
  option deprecated = true;
  google.protobuf.StringValue originPhone = 1;
}

message ChangeOriginPhoneNumbersCommand {
  PhoneNumbers phoneNumbers = 1;
}

message ChangeDestinationPhoneCommand {
  option deprecated = true;
  google.protobuf.StringValue destinationPhone = 1;
}

message ChangeDestinationPhoneNumbersCommand {
  PhoneNumbers phoneNumbers = 1;
}

message AddRemoveWaiversCommand {
  option deprecated = true;
  repeated string addWaivers = 1;
  repeated string removeWaivers = 2;
}

message ChangeWaiversCommand {
  NullableStringValue waivers = 1;
}

message ChangeAmbulatoryOrLitterCommand {
  google.protobuf.StringValue ambulatoryOrLitter = 1;
}

message ClearPickupLocationCommand {
}

message ClearDropoffLocationCommand {
}