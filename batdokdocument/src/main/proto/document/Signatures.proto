syntax = "proto3";

option java_package = "gov.afrl.batdok.commands.proto";

import "FieldOptions.proto";
import "google/protobuf/wrappers.proto";

message SetSignaturePMRPhysicianCommand {
    Signature pmrPhysician = 1;
}

message RemoveSignaturePMRPhysicianCommand {
}

message SetSignaturePMRFlightSurgeonCommand {
    Signature pmrFlightSurgeon = 1;
}

message RemoveSignaturePMRFlightSurgeonCommand {
}

message Signature {
    google.protobuf.StringValue name = 1;
    google.protobuf.Int64Value timestamp = 2;
    google.protobuf.BytesValue signatureImage = 3;
}