syntax = "proto3";

option java_package = "gov.afrl.batdok.commands.proto";

import "document/SharedProtobufObjects.proto";
import "FieldOptions.proto";

message LogMedCommand {
    Medicine medicine = 1;
}

message LogMedWithIdCommand {
    bytes id = 1;
    int64 administrationTime = 2;
    Medicine medicine = 3;
}

message UpdateMedCommand {
    bytes id = 1;
    int64 timestamp = 2;
    Medicine medicine = 3;
}

message RemoveMedTreatmentCommand {
    bytes id = 1;
    bool documentationError = 2;
}

message CreateDrip{
    bytes id = 1;
    Medicine medicine = 2;
    int64 timestamp = 3;
}

message RemoveDrip{
    bytes id = 1;

}

message AddToDrip{
    bytes dripId = 1;
    Medicine dripMedicine = 2;
    bytes id = 3;
    Medicine medicine = 4;
}

message RemoveFromDrip{
    bytes dripId = 1;
    Medicine dripMedicine = 2;
    bytes id = 3;
    Medicine medicine = 4;
}

message StartDrip{
    bytes id = 1;
    Medicine medicine = 2;
    int64 timestamp = 3;
}

message StopDrip{
    bytes id = 1;
    Medicine medicine = 2;
    int64 timestamp = 3;
}