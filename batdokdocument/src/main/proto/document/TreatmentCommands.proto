syntax = "proto3";

option java_package = "gov.afrl.batdok.commands.proto";

import "google/protobuf/descriptor.proto";
import "google/protobuf/any.proto";
import "google/protobuf/wrappers.proto";
import "EnumValueOptions.proto";
import "FieldOptions.proto";
import "document/SharedProtobufObjects.proto";

message AddTreatment{
    CompatibleEnum treatment = 1;
    int64 timestamp = 2;
    google.protobuf.Any treatmentData = 3;
    bytes id = 4;
}

message UpdateTreatment {
    bytes treatmentId = 1;
    google.protobuf.Int64Value timestamp = 2;
    google.protobuf.Any treatmentData = 3;
}

message RemoveTreatment{
    bytes treatmentId = 1;
    bool documentationError = 2;
}

message TQ{
    CompatibleEnum location = 1;
    CompatibleEnum type = 2;
    CompatibleEnum sublocation = 3;
    NullableInt64Value reapplicationTime = 4;
    NullableInt64Value conversionTime = 5;
    NullableInt64Value removalTime = 6;
    NullableInt64Value reassessTime = 7;
    NullableFloatValue pointX = 8;
    NullableFloatValue pointY = 9;
    NullableStringValue pointLabel = 10;
}

message Dressing{
    CompatibleEnum type = 1;
    CompatibleEnum location = 2;
    CompatibleEnum subtype = 3;
}

message Tube{
    NullableFloatValue size = 1;
    NullableInt32Value depth = 2;
    CompatibleEnum location = 3;
    CompatibleEnum breathingConfirmation = 4;
    CompatibleEnum type = 5;
    CompatibleEnum sizeUnit = 6;
    CompatibleEnum depthUnit = 7;
}

message NeedleD{
    CompatibleEnum location = 1;
}

message FingerThor{
    CompatibleEnum location = 1;
}

message ChestTube{
    CompatibleEnum location = 1;
    NullableBoolValue suction = 2;
    NullableFloatValue suctionAmount = 3;
    CompatibleEnum suctionUnit = 5;
    NullableBoolValue airLeak = 4;
}

message ChestSeal{
    CompatibleEnum location = 1;
}

message EyeShield{
    google.protobuf.BoolValue left = 1;
    google.protobuf.BoolValue right = 2;
}

message Splint{
    NullableBoolValue pulsePresent = 1;
    CompatibleEnum type = 2;
}

message O2{
    NullableFloatValue volume = 1 [deprecated=true];
    CompatibleEnum deliveryMethod = 2 [deprecated=true];
    NullableFloatValue lpm = 3;
    NullableInt32Value targetSpo2 = 4;
    NullableFloatValue fio2 = 5;
    CompatibleEnum route = 6;
}

message Escharatomy{
    CompatibleEnum location = 1;
}

message Debridement{
    CompatibleEnum location = 1;
}

message GastricTube{
    CompatibleEnum type = 1;
    CompatibleEnum side = 2;
    CompatibleEnum suctionType = 3;
    CompatibleEnum interval = 4;
}

message LateralCanthotomy{
    CompatibleEnum location = 1;
}

message Fasciotomy{
    CompatibleEnum location = 1;
}

message FoleyCatheter{
    NullableFloatValue size = 1;
    CompatibleEnum sizeUnit = 5;
    CompatibleEnum color = 2;
    CompatibleEnum character = 3;
    CompatibleEnum assess = 4;
}

message Pericardiocentesis{
    NullableFloatValue volume = 1;
    CompatibleEnum volumeUnit = 2;
}

message DirectPressure{
    CompatibleEnum location = 1;
}

message ForeignBodyRemoval{
    CompatibleEnum location = 1;
}

message HypothermiaPrevention{
    CompatibleEnum type = 1;
}

message Immobilization{
    CompatibleEnum type = 1;
    CompatibleEnum subtype = 2;
    CompatibleEnum location = 3;

    CompatibleEnum neurovascular_before_pulse = 4;
    CompatibleEnum neurovascular_before_motor = 5;
    CompatibleEnum neurovascular_before_sensory = 6;

    CompatibleEnum neurovascular_after_pulse = 7;
    CompatibleEnum neurovascular_after_motor = 8;
    CompatibleEnum neurovascular_after_sensory = 9;
}

message Suction{
    CompatibleEnum tool = 1;
    CompatibleEnum location = 2;
}

message IntubatedBy{
    CompatibleEnum intubater = 1;
}

message LineData{
    CompatibleEnum type = 1;
    CompatibleEnum side = 2;
    CompatibleEnum location = 3;
    NullableFloatValue gauge = 4;
    CompatibleEnum gaugeUnit = 6;
    NullableFloatValue size = 7;
    CompatibleEnum sizeUnit = 8;
    CompatibleEnum subtype = 5;
}

message HyperthermiaPreventionData{
    CompatibleEnum type = 1;
}

message InboundHL7Data {
    string treatmentDetails = 1;
}

message PelvicBinderData {
    CompatibleEnum type = 1;
}

message WoundPackingData{
    CompatibleEnum location = 1;
    CompatibleEnum type = 2;
}

enum PpeItem {
    UNKNOWN_PPE = 0 [(displayName) = "", (introducedEnum) = 2];
    HELMET_BALLISTIC = 1 [(displayName)="Helmet, Ballistic", (introducedEnum) = 2];
    TACTICAL_VEST_IOTV = 2 [(displayName)="Tactical Vest (IOTV)", (introducedEnum) = 2];
    EYE_PROTECTION = 3 [(displayName)="Eye Protection", (introducedEnum) = 2];
    EAR_PROTECTION = 4 [(displayName)="Ear Protection", (introducedEnum) = 2];
    PLATE_FRONT = 5 [(displayName)="Plate Front", (introducedEnum) = 2];
    PLATE_BACK = 6 [(displayName)="Plate Back", (introducedEnum) = 2];
    PLATE_RIGHT = 7 [(displayName)="Plate Right Side", (introducedEnum) = 2];
    PLATE_LEFT = 8 [(displayName)="Plate Left Side", (introducedEnum) = 2];
    NECK_PROTECTOR_BACK = 9 [(displayName)="Neck Protector (Back)", (introducedEnum) = 2];
    THROAT_PROTECTOR_FRONT = 10 [(displayName)="Throat Protector (Front)", (introducedEnum) = 2];
    DELTOID_RIGHT = 11 [(displayName)="Deltoid Right", (introducedEnum) = 2];
    DELTOID_LEFT = 12 [(displayName)="Deltoid Left", (introducedEnum) = 2];
    GROIN_SHIELD = 13 [(displayName)="Groin Shield", (introducedEnum) = 2];
    PELVIC_UNDERGARMENT_1 = 14 [(displayName)="Pelvic Undergarment Tier 1", (introducedEnum) = 2];
    PELVIC_UNDERGARMENT_2 = 15 [(displayName)="Pelvic Undergarment Tier 2", (introducedEnum) = 2];
    BLAST_GAUGE = 16 [(displayName)="Blast Gauge", (introducedEnum) = 2];
    BLAST_SENSOR_HELMET = 17 [(displayName)="Blast Sensor Helmet", (introducedEnum) = 2];
    BLAST_SENSOR_OTHER = 18 [(displayName)="Blast Sensor Other", (introducedEnum) = 2];
}