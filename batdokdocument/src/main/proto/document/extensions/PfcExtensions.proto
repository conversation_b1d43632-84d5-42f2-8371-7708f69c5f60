syntax = "proto2";

option java_package = "gov.afrl.batdok.commands.proto";

enum PfcChecklistItem {
    UNKNOWN_PFC = 0;
    STOP_MASSIVE_BLEEDING = 1;
    OPEN_AIRWAY = 2;
    THORACOSTOMY_OR_NEEDLE_D = 3;
    INITIATE_BLOOD_TRANSFUSION = 4;
    FIRST_TXA_DOSE_ASAP = 5;
    CALCIUM = 6;
    SEND_MIST_REPORT = 7;
    REASSESS_TX = 8;
    EXPOSE = 9;
    DETAILED_EXAM = 10;
    PELVIC_BINDER = 11;
    HYPOTHERMIA_TX = 12;
    MONITORS = 13;
    SECOND_IV_IO = 14;
    GCS_NEURO_MACE = 15;
    ANALGESIA = 16;
    SEDATION = 17;
    NG_OG_TUBE = 18;
    UPGRADE_AIRWAY = 19;
    POST_CRIC_PFC_CHECKLIST = 20;
    VENT_OR_BVM_W_PEEP = 21;
    RECALC_TBSA_AND_FLUIDS = 22;
    ULTRASOUND_EFAST = 23;
    TELECONSULT = 24;
    PRESSORS_FOR_DISTRIBUTIVE_SHOCK = 25;
    CONVERT_TQ_4HRS = 26;
    FOLEY_BLADDER_TAP = 27;
    ADJUST_VENT_SETTINGS = 28;
    UA_DIPSTICK = 29;
    CLEAR_CSPINE = 30;
    POSITION_PAD_PATIENT = 31;
    PERIPHERAL_PULSES = 32;
    COMPARTMENT_SYNDROME = 33;
    ESCHAROTOMY = 34;
    REDUCE_SPLINT_FX = 35;
    DVT_PROPHYLAXIS = 36;
    ANTIBIOTIC_WAR_WOUND_TX = 37;
    TETANUS = 38;
    LABS = 39;
    XRAY_IMAGING = 40;
    PREOP_EVAL = 41;
    DEBRIDEMENT = 42;
    AMPUTATION_PFC = 43;
    FASCIOTOMY = 44;
    SHUNT = 45;
    PREPERITONEAL_PELVIC_PACKING = 46;
    VITALS = 47;
    FLUSH_SALINE_LOCKS = 48;
    SUCTION_ET_TUBE = 49;
    REPOSITION = 50;
    ORAL_CARE_HYGIENE = 51;
    FOLEY_CARE = 52;
    CHECK_PERIPHERAL_PULSE = 53;
}