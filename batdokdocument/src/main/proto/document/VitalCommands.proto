syntax = "proto3";

option java_package = "gov.afrl.batdok.commands.proto";

import "Vital.proto";
import "FieldOptions.proto";
import "google/protobuf/wrappers.proto";

message LogVitalCommand{
    Vital vital = 1;
    int64 timestamp = 2;
    bytes vitalId = 3;
}

message UpdateVitalCommand{
    bytes vitalId = 1;
    int64 timestamp = 2;
    Vital vital = 3;
}

message RemoveVitalCommand{
    bytes vitalId = 1;
}
