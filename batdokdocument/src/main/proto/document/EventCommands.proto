syntax = "proto3";

option java_package = "gov.afrl.batdok.commands.proto";

import "google/protobuf/wrappers.proto";
import "document/SharedProtobufObjects.proto";

message AddEventCommand {
    int64 timestamp = 2;
    string text = 3;
    bool showTime = 4;
    CompatibleEnum eventType = 5;
    bool customEvent = 6;
    bytes id = 7;
}

message EditEventCommand {
    bytes id = 1;
    google.protobuf.Int64Value timestamp = 2;
    google.protobuf.StringValue text = 3;
    google.protobuf.BoolValue showTime = 4;
    CompatibleEnum eventType = 5;
    google.protobuf.BoolValue customEvent = 6;
}

message RemoveEventCommand {
    bytes id = 1;
}

message AddComment{
    bytes referencedId = 1;
    string text = 2;
    int64 timestamp = 3;
    bytes id = 4;
}