syntax = "proto3";

option java_package = "gov.afrl.batdok.commands.proto";

import "document/SharedProtobufObjects.proto";
import "google/protobuf/descriptor.proto";
import "EnumValueOptions.proto";
import "FieldOptions.proto";
import "google/protobuf/wrappers.proto";

message ChangeInjuryCommand {
    CompatibleEnum injury = 1;
    bool checked = 2;
    string customAbbrev = 3;
    CompatibleEnum location = 4;
}

message ChangeMoiCommand {
    CompatibleEnum moi = 1;
    bool checked = 2;
    CompatibleEnum location = 3;
}

message AddPointWithLabelCommand {
    repeated Point point = 1;
    string label = 5;
}

message Point {
    float x = 1;
    float y = 2;
}

message ClearInjuryDrawingsCommand {
    string label = 1;
}

message ClearDrawViewCommand {
    string type = 1;
}

message UndoDrawViewCommand {}

message RemoveDrawPointCommand {
    int32 index = 1;
    Point point = 2;
}

message ChangeTBSACommand{
    google.protobuf.DoubleValue tbsa = 1 [(introduced) = 2];
}