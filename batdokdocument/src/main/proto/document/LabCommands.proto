syntax = "proto3";

option java_package = "gov.afrl.batdok.commands.proto";

import "FieldOptions.proto";
import "google/protobuf/wrappers.proto";
import "document/SharedProtobufObjects.proto";

message LogLabCommand {
    repeated LabEntry labValues = 1;
}

message UpdateLabCommand {
    LabEntry labValue = 3;
}

message RemoveLabCommand {
    bytes labId = 1;
}

message LabEntry {
    bytes id = 1;
    CompatibleEnum name = 2;
    NullableStringValue value = 3;
    NullableStringValue unit = 4;
    NullableInt64Value time = 5;
}