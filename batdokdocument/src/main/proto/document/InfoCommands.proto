syntax = "proto3";

option java_package = "gov.afrl.batdok.commands.proto";

import "FieldOptions.proto";
import "EnumValueOptions.proto";
import "document/SharedProtobufObjects.proto";
import "google/protobuf/wrappers.proto";

message ChangePatientIdCommand {
    bytes patientId = 1;
}

message AddMediaFile {
    string filename = 1;
}

message UpdateThumbnail {
    NullableStringValue filename = 1;
}

message ChangeNameCommand {
    string first = 1;
    string middle = 2;
    string last = 3;
}

message ChangeBattleRosterNumberCommand {
    string battleRosterNumber = 1;
}

message ChangeAliasCommand {
    string alias = 1;
}

message ChangeUnitCommand {
    string unit = 1;
}

message ChangeUnitPhoneNumberCommand {
    string unitPhoneNumber = 1;
}

message ChangeServiceCommand {
    CompatibleEnum service = 1;
}

message ChangePatcatCommand {
    CompatibleEnum service = 1;
    CompatibleEnum status = 2;
}

message ChangeSSNCommand {
    string ssn = 1;
}

message ChangeTattooCommand {
    string tattoo = 1 [(introduced) = 2];
}

message ChangeEvacCommand {
    option deprecated = true;
    CompatibleEnum evac = 1;
}

message ChangeTriageCommand {
    CompatibleEnum evac = 1;
}

message ChangeEvacTypeCommand {
    CompatibleEnum evac = 1;
}

message ChangeBloodTypeCommand {
    CompatibleEnum bloodType = 1;
    NullableBoolValue lowTiter = 2;
}

message ChangeGenderCommand {
    CompatibleEnum gender = 1;
}

message AddRemoveAllergyCommand{
    repeated CompatibleEnum addAllergy = 1;
    repeated CompatibleEnum removeAllergy = 2;
}

message AddRemoveMedicationsCommand{
    repeated CompatibleEnum addMedicine = 1;
    repeated CompatibleEnum removeMedicine = 2;
}

message ChangeInjuryTimeCommand {
    google.protobuf.Int64Value datetime = 1;
}

message ChangeDobCommand {
    google.protobuf.Int64Value dob = 1;
    bool estimate = 2;
}

message ChangeDodIdCommand {
    string dodId = 1;
}

message ChangeHandoffCommand {
    string callsign = 1;
}

message ChangeWeightCommand {
    google.protobuf.FloatValue weight = 1;
}

message ChangeHeightCommand {
    google.protobuf.FloatValue height = 1;
}

message ChangeDiagnosisCommand {
    CompatibleEnum diagnosis = 1;
}

message ChangeRankCommand {
    string rank = 1 [(introduced) = 2];
}

message ChangeGradeCommand {
    CompatibleEnum grade = 1;
}

message ChangeOpenCloseEncounterCommand{
    bool open = 1 [(introduced) = 2];
}

message ChangePatientMasking{
    bool enable = 1;
    string justification = 2;
}

message ChangeDisposition{
    CompatibleEnum disposition = 1;
}

message ChangeNationalityCommand {
    CompatibleEnum nationality = 1;
}

message UpdateImmunizationCommand{
    repeated Immunization addedImmunizations = 1;
    repeated Immunization removedImmunizations = 2;
}

message Immunization {
     string name = 1;
     string unit = 2;
     float volume = 3;
     optional NullableInt64Value date = 4;
}

message UpdateProceduresCommand{
    repeated Procedures addProcedure = 1;
    repeated Procedures removeProcedure = 2;
}

message Procedures {
    string name = 1;
    NullableInt64Value date = 2;
}

message UpdateProblemsCommand{
    repeated Problem addedProblems = 1;
    repeated bytes removedProblemIds = 2;
}

message Problem {
    string name = 1;
    CompatibleEnum status = 2;
    NullableInt64Value date = 3;
    bytes id = 4;
}

message UpdateCreationName {
        string first = 1;
        string middle = 2;
        string last = 3;
}

message UpdateCreationAlias {
    string alias = 1;
}