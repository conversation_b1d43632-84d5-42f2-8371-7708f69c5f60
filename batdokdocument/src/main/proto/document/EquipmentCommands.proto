syntax = "proto3";

option java_package = "gov.afrl.batdok.commands.proto";

import "FieldOptions.proto";
import "document/SharedProtobufObjects.proto";
import "google/protobuf/any.proto";
import "google/protobuf/wrappers.proto";

message AddRemoveEquipmentCommand{
  option deprecated = true;
  repeated EquipmentItem addedItems = 1 [(introduced) = 2];
  repeated EquipmentItem removedItems = 2 [(introduced) = 2];
}

message AddEquipmentCommand{
  EquipmentItem addedItem = 1;
}

message UpdateEquipmentCommand{
  bytes id = 1;
  google.protobuf.Int64Value timestamp = 2;
  EquipmentItem item = 3;
}

message RemoveEquipmentCommand{
  EquipmentItem item = 1 ;
  bool documentationError = 2;
}

message AddRemovePowerLossCommand{
  option deprecated = true;
  repeated EquipmentItem addedItems = 1 [(introduced) = 2];
  repeated EquipmentItem removedItems = 2 [(introduced) = 2];
}

message AddRemoveUserErrorCommand{
  option deprecated = true;
  repeated EquipmentItem addedItems = 1 [(introduced) = 2];
  repeated EquipmentItem removedItems = 2 [(introduced) = 2];
}

message AddRemoveEqFailureCommand{
  option deprecated = true;
  repeated EquipmentItem addedItems = 1 [(introduced) = 2];
  repeated EquipmentItem removedItems = 2 [(introduced) = 2];
}

message EquipmentItem{
  CompatibleEnum type = 1 [(introduced) = 2];
  google.protobuf.Any extras = 2 [(introduced) = 2];
  bytes id = 3 [(introduced) = 1];
}

message LoadingExtras {
  google.protobuf.StringValue eqInfo = 1 [(introduced) = 2];
  google.protobuf.BoolValue locFront = 2 [(introduced) = 2];
  google.protobuf.BoolValue locRight = 3 [(introduced) = 2];
  google.protobuf.BoolValue orientationHead = 4 [(introduced) = 2];
}

message OrthopedicExtras {
  CompatibleEnum type = 1;
  CompatibleEnum subtype = 2;
  CompatibleEnum location = 3;
}

