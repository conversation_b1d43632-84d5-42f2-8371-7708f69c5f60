syntax = "proto3";

option java_package = "gov.afrl.batdok.commands.proto";

import "EnumValueOptions.proto";
import "document/SharedProtobufObjects.proto";
import "FieldOptions.proto";
import "google/protobuf/any.proto";
import "google/protobuf/wrappers.proto";

message Vital {
  repeated google.protobuf.Any vitals = 1;
  map<string, float> extraVitals = 2;
}

message HR{
  google.protobuf.Int32Value pulseRate = 1;
  google.protobuf.StringValue location = 2;
}

message BP{
  google.protobuf.Int32Value bps = 1;
  google.protobuf.Int32Value bpd = 2;
  google.protobuf.StringValue location = 3;
}

message IBP{
  google.protobuf.Int32Value bps = 1;
  google.protobuf.Int32Value bpd = 2;
}

message Temp{
  google.protobuf.FloatValue temp = 1;
  google.protobuf.StringValue measurementMethod = 2;
}

message SpO2{
  google.protobuf.Int32Value spo2 = 1;
}

message Resp{
  google.protobuf.Int32Value resp = 1;
  google.protobuf.StringValue respQuality = 2;
}

message AVPU{
  CompatibleEnum value = 1;
}

message Pain{
  google.protobuf.Int32Value pain = 1;
}

message EtCO2{
  google.protobuf.Int32Value etco2 = 1;
}

message Output{
  google.protobuf.FloatValue output = 1;
  string unit = 2;
}

message Ecg{
  repeated int32 data = 1;
}

message CapillaryRefill{
  google.protobuf.FloatValue capRefill = 1;
}

message PIP{
  google.protobuf.Int32Value pip = 1;
}

message GCS {
  int32 eye = 1 [(introduced) = 2];
  int32 verbal = 2 [(introduced) = 2];
  int32 motor = 3 [(introduced) = 2];
  int32 intubated = 4 [(introduced) = 2];
}