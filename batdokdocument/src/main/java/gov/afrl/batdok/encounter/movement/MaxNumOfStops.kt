package gov.afrl.batdok.encounter.movement

import gov.afrl.batdok.commands.proto.MovementCommands
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.bloodProduct
import gov.afrl.batdok.encounter.observation.Blood
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.compatibleEnum
import gov.afrl.batdok.util.nullableInt32Value
import gov.afrl.batdok.util.nullableInt64Value
import gov.afrl.batdok.util.nullableStringValue
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto
import gov.afrl.batdok.util.toChangeOrClearEvent

enum class MaxNumOfStops(
override val protoIndex: Int,
override val dataString: String,
val buttonString: String = dataString
) : ProtoEnum {
    ONE(1, "1"),
    T<PERSON><PERSON>(2, "2"),
    <PERSON><PERSON><PERSON><PERSON>(3, "3"),
    FO<PERSON>(4, "4"),
    <PERSON>IVE(5, "5"),
    <PERSON><PERSON>(6, "6"),
    SEVEN(7, "7"),
    EIGHT(8, "8"),
    UNRESTRICTED(9, "Unrestricted");

    companion object {
        fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = MaxNumOfStops.values().protoToString(enum)
        fun fromString(string: String?) = MaxNumOfStops.values().stringToProto(string)
        fun toEvent(enum: SharedProtobufObjects.CompatibleEnum) = MaxNumOfStops.values()
            .toChangeOrClearEvent(enum, "Max # of Stops")
    }

}