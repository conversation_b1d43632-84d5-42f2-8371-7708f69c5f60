package gov.afrl.batdok.encounter.orders

import gov.afrl.batdok.encounter.*
import gov.afrl.batdok.encounter.ids.OrderLineId
import gov.afrl.batdok.encounter.metadata.Signature
import gov.afrl.batdok.util.Timestamped
import mil.af.afrl.batman.batdokid.DomainId
import java.io.Serializable
import java.time.Instant
import java.time.temporal.ChronoUnit

@Deprecated("Use CustomActionOrderLine", replaceWith = ReplaceWith("CustomActionOrderLine"))
typealias OrderLine = CustomActionOrderLine

data class CustomActionOrderLine(
    override val id: OrderLineId = DomainId.create(),
    override val timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS),
    override val title: String,
    override val instructions: String,
    override val orderType: String,
    override val orderStatus: String = OrderStatus.ORDERED.dataString,
    override val frequency: Interval,
    override val lastOccurrence: Instant? = null,
    override val provider: Contact? = null,
    override val signature: Signature? = null)
: IOrderLine {


}