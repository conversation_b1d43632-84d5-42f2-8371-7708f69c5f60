package gov.afrl.batdok.encounter.summary

import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import java.io.Serializable
import java.time.Instant

/**
 * ATMIST (Age, Time, Mechanism, Injuries, Signs, Treatment) summary data structure
 * for medical handover via QR codes using raw data format instead of HL7
 */
data class AtmistSummary(
    @SerializedName("age")
    val age: String? = null,

    @SerializedName("name")
    val name: String? = null,

    @SerializedName("time")
    val time: String? = null,

    @SerializedName("dodId")
    val dodId: String? = null,

    @SerializedName("sex")
    val sex: String? = null,

    @SerializedName("mechanism")
    val mechanism: List<String> = emptyList(),

    @SerializedName("injuries")
    val injuries: List<String> = emptyList(),

    @SerializedName("signs")
    val signs: AtmistSigns = AtmistSigns(),

    @SerializedName("treatment")
    val treatment: List<String> = emptyList(),

    @SerializedName("metadata")
    val metadata: AtmistMetadata = AtmistMetadata()
) : Serializable {
    
    fun toJson(): String = Gson().toJson(this)
    
    companion object {
        fun fromJson(json: String): AtmistSummary = Gson().fromJson(json, AtmistSummary::class.java)
    }
}

/**
 * Signs and symptoms data for ATMIST summary
 */
data class AtmistSigns(
    @SerializedName("vitals")
    val vitals: AtmistVitals? = null,
    
    @SerializedName("consciousness")
    val consciousness: String? = null,
    
    @SerializedName("observations")
    val observations: List<String> = emptyList(),
    
    @SerializedName("complaints")
    val complaints: List<String> = emptyList()
) : Serializable

/**
 * Vital signs data for ATMIST summary
 */
data class AtmistVitals(
    @SerializedName("heart_rate")
    val heartRate: Int? = null,
    
    @SerializedName("blood_pressure_systolic")
    val bloodPressureSystolic: Int? = null,
    
    @SerializedName("blood_pressure_diastolic")
    val bloodPressureDiastolic: Int? = null,
    
    @SerializedName("respiratory_rate")
    val respiratoryRate: Int? = null,
    
    @SerializedName("oxygen_saturation")
    val oxygenSaturation: Int? = null,
    
    @SerializedName("temperature")
    val temperature: Float? = null,
    
    @SerializedName("pain_scale")
    val painScale: Int? = null,
    
    @SerializedName("timestamp")
    val timestamp: String? = null
) : Serializable

/**
 * Metadata for ATMIST summary
 */
data class AtmistMetadata(
    @SerializedName("patient_id")
    val patientId: String? = null,
    
    @SerializedName("encounter_id")
    val encounterId: String? = null,
    
    @SerializedName("provider")
    val provider: String? = null,
    
    @SerializedName("unit")
    val unit: String? = null,
    
    @SerializedName("generated_at")
    val generatedAt: String? = null,
    
    @SerializedName("version")
    val version: String = "1.0"
) : Serializable
