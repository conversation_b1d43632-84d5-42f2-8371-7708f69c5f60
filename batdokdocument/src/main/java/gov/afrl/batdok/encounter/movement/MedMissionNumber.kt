package gov.afrl.batdok.encounter.movement

import gov.afrl.batdok.commands.proto.MovementCommands.ChangeMedMissionNumber
import java.io.Serializable

data class MedMissionNumber(val parenText: String = "", val otherText: String = "") : Serializable {
    constructor(proto: ChangeMedMissionNumber) : this(proto.parenText, proto.otherText)

    override fun toString() = listOfNotNull(
        "($parenText)".takeUnless { parenText.isEmpty() },
        otherText.takeUnless { it.isEmpty() }
    ).joinToString(" ")

    fun isEmpty() = parenText.isEmpty() && otherText.isEmpty()
}