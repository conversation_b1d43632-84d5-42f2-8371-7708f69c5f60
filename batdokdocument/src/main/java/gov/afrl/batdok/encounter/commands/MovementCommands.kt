package gov.afrl.batdok.encounter.commands

import gov.afrl.batdok.commands.eventHandler
import gov.afrl.batdok.commands.handler
import gov.afrl.batdok.commands.proto.*
import gov.afrl.batdok.encounter.*
import gov.afrl.batdok.encounter.movement.*
import gov.afrl.batdok.util.*
import java.time.Instant
import java.time.LocalDate

fun buildChangeAcceptingPhysicianCommand(physician: Physician) = changeAcceptingPhysicianCommand {
    this.physician = physician.toProto()
}
fun changeAcceptingPhysicianCommandHandler(movement: Movement) = handler<MovementCommands.ChangeAcceptingPhysicianCommand> {
    movement.acceptingPhysician = Physician(this.physician)
}
fun changeAcceptingPhysicianCommandEventHandler() = eventHandler<MovementCommands.ChangeAcceptingPhysicianCommand>(KnownEventTypes.MOVEMENT.dataString) {
    val acceptingPhysician = Physician(this.physician)
    changeOrClearEvent(!acceptingPhysician.isBlank(), acceptingPhysician.toString(), "Accepting Physician")
}

@Deprecated(
    message = "Use buildChangeOriginPhoneNumbersCommand(phone: PhoneNumbers)",
    replaceWith = ReplaceWith("buildChangeOriginPhoneNumbersCommand(phone = PhoneNumbers(comm = phone))")
)
fun buildChangeOriginPhoneCommand(phone: String?) = changeOriginPhoneCommand {
    phone?.let {
        if (it.isNotBlank()) this.originPhone = stringValue(phone)
    }
}
@Deprecated(
    message = "Use changeOriginPhoneNumbersCommandHandler",
    replaceWith = ReplaceWith("changeOriginPhoneNumbersCommandHandler(movement)")
)
fun changeOriginPhoneCommandHandler(movement: Movement) = handler<MovementCommands.ChangeOriginPhoneCommand> {
    movement.originPhone = this.originPhone.value.takeIf { this.hasOriginPhone() && this.originPhone.value.isNotBlank() }
}
@Deprecated(
    message = "Use changeOriginPhoneNumbersCommandEventHandler",
    replaceWith = ReplaceWith("changeOriginPhoneNumbersCommandEventHandler")
)
fun changeOriginPhoneCommandEventHandler() = eventHandler<MovementCommands.ChangeOriginPhoneCommand>(KnownEventTypes.MOVEMENT.dataString) {
    changeOrClearEvent(this.hasOriginPhone() && this.originPhone.value.isNotBlank(), this.originPhone.value, "Origin Phone")
}
fun buildChangeOriginPhoneNumbersCommand(phone: PhoneNumbers) = changeOriginPhoneNumbersCommand {
    phoneNumbers = phone.toProto()
}
fun changeOriginPhoneNumbersCommandHandler(movement: Movement) = handler<MovementCommands.ChangeOriginPhoneNumbersCommand> {
    movement.originPhoneNumbers = PhoneNumbers(phoneNumbers)
}
fun changeOriginPhoneNumbersCommandEventHandler() = eventHandler<MovementCommands.ChangeOriginPhoneNumbersCommand>(KnownEventTypes.MOVEMENT.dataString) {
    val phone = PhoneNumbers(phoneNumbers)
    changeOrClearEvent(!phone.isBlank(), phone.toString(), "Origin Phone" )
}
@Deprecated(
    message = "Use buildChangeDestinationPhoneNumbersCommand(phone: PhoneNumbers)",
    replaceWith = ReplaceWith("buildChangeDestinationPhoneNumbersCommand(phone = PhoneNumbers(comm = phone))")
)
fun buildChangeDestinationPhoneCommand(phone: String?) = changeDestinationPhoneCommand {
    phone?.let {
        if (it.isNotBlank()) this.destinationPhone = stringValue(phone)
    }
}
@Deprecated(
    message = "Use changeDestinationPhoneNumbersCommandHandler",
    replaceWith = ReplaceWith("changeDestinationPhoneNumbersCommandHandler(movement)")
)
fun changeDestinationPhoneCommandHandler(movement: Movement) = handler<MovementCommands.ChangeDestinationPhoneCommand> {
    movement.destinationPhone = this.destinationPhone.value.takeIf { this.hasDestinationPhone() && this.destinationPhone.value.isNotBlank() }
}
@Deprecated(
    message = "Use changeDestinationPhoneNumbersCommandEventHandler",
    replaceWith = ReplaceWith("changeDestinationPhoneNumbersCommandEventHandler")
)
fun changeDestinationPhoneCommandEventHandler() = eventHandler<MovementCommands.ChangeDestinationPhoneCommand>(KnownEventTypes.MOVEMENT.dataString) {
    changeOrClearEvent(this.hasDestinationPhone() && this.destinationPhone.value.isNotBlank(), this.destinationPhone.value, "Destination Phone")
}
fun buildChangeDestinationPhoneNumbersCommand(phone: PhoneNumbers) = changeDestinationPhoneNumbersCommand {
    phoneNumbers = phone.toProto()
}
fun changeDestinationPhoneNumbersCommandHandler(movement: Movement) = handler<MovementCommands.ChangeDestinationPhoneNumbersCommand> {
    movement.destinationPhoneNumbers = PhoneNumbers(phoneNumbers)
}
fun changeDestinationPhoneNumbersCommandEventHandler() = eventHandler<MovementCommands.ChangeDestinationPhoneNumbersCommand>(KnownEventTypes.MOVEMENT.dataString) {
    val phone = PhoneNumbers(phoneNumbers)
    changeOrClearEvent(!phone.isBlank(), phone.toString(), "Destination Phone" )
}

@Deprecated(
    message = "Use buildChangeWaiversCommand",
    replaceWith = ReplaceWith("buildChangeWaiversCommand")
)
fun buildAddRemoveWaiversCommand(addedWaivers: List<String>, removedWaivers: List<String>) = addRemoveWaiversCommand {
    this.addWaivers.addAll(addedWaivers)
    this.removeWaivers.addAll(removedWaivers)
}
@Deprecated(
    message = "Use changeWaiversCommandHandler",
    replaceWith = ReplaceWith("changeWaiversCommandHandler(movement)")
)
fun addRemoveWaiversCommandHandler(movement: Movement) = handler<MovementCommands.AddRemoveWaiversCommand>{
    movement.waivers += this.addWaiversList
    movement.waivers -= this.removeWaiversList
}
@Deprecated(
    message = "Use changeWaiversCommandEventHandler",
    replaceWith = ReplaceWith("changeWaiversCommandEventHandler")
)
fun addRemoveWaiversEventHandler() = eventHandler<MovementCommands.AddRemoveWaiversCommand>(
    KnownEventTypes.MOVEMENT.dataString){
    val addedWaivers = addWaiversList.joinToString(", ")
    val removedWaivers = removeWaiversList.joinToString(", ")
    val addString = if(addedWaivers.isNotEmpty()) "Added Waivers: $addedWaivers" else ""
    val removeString = if(removedWaivers.isNotEmpty()) "Removed Waivers: $removedWaivers" else ""
    when{
        addString.isNotEmpty() && removeString.isNotEmpty() -> addString + "\n" + removeString
        addString.isNotEmpty() -> addString
        removeString.isNotEmpty() -> removeString
        else -> null
    }
}
fun buildChangeWaiversCommand(waivers: String?) = changeWaiversCommand {
    val waiverToSet = if (waivers.isNullOrBlank()) null else waivers
    this.waivers = nullableStringValue(waiverToSet)
}
fun changeWaiversCommandHandler(movement: Movement) = handler<MovementCommands.ChangeWaiversCommand> {
    movement.waiversText = waivers.toPrimitive()
}
fun changeWaiversCommandEventHandler() = eventHandler<MovementCommands.ChangeWaiversCommand>(KnownEventTypes.MOVEMENT.dataString) {
    changeOrClearEvent(
        !this.waivers.toPrimitive().isNullOrBlank(),
        this.waivers.toPrimitive() ?: "",
        "Waivers"
    )
}
fun buildMedMissionNumberCommand(medMissionNumber: MedMissionNumber) = changeMedMissionNumber {
    medMissionNumber.parenText.let { this.parenText = it }
    medMissionNumber.otherText.let { this.otherText = it }
}

fun changeMedMissionNumberCommandHandler(movement: Movement) =
    handler<MovementCommands.ChangeMedMissionNumber> {
        movement.medMissionNumber = MedMissionNumber(parenText, otherText)
    }

fun changeMedMissionNumberEventHandler() =
    eventHandler<MovementCommands.ChangeMedMissionNumber>(KnownEventTypes.EVAC.dataString) {
        val medMissionNumber = MedMissionNumber(this)
        changeOrClearEvent(
            !medMissionNumber.isEmpty(),
            medMissionNumber.toString(),
            "Med Mission Number"
        )
    }

fun buildChangeTailToTailCommand(tailToTail: Boolean?) = changeTailToTail {
    tailToTail?.let { this.tailToTail = booleanValue(it) }
}

fun changeTailToTailCommandHandler(movement: Movement) =
    handler<MovementCommands.ChangeTailToTail> {
        movement.tailToTail = tailToTailOrNull?.value
    }

fun changeTailToTailEventHandler() =
    eventHandler<MovementCommands.ChangeTailToTail>(KnownEventTypes.EVAC.dataString) {
        when (tailToTailOrNull?.value) {
            true -> "Patient evac was tail to tail"
            false -> "Patient evac was not tail to tail"
            null -> "Clear tail to tail"
        }
    }

fun buildChangeLegNumberCommand(legNumber: Int?, totalLegs: Int?) = changeLegNumber {
    legNumber?.let { this.legNumber = int32Value(legNumber) }
    totalLegs?.let { this.totalLegs = int32Value(totalLegs) }
}

fun changeLegNumberCommandHandler(movement: Movement) =
    handler<MovementCommands.ChangeLegNumber> {
        movement.legNumber = legNumber.value.takeIf { hasLegNumber() }
        movement.totalLegs = totalLegs.value.takeIf { hasTotalLegs() }
    }

fun changeLegNumberEventHandler() =
    eventHandler<MovementCommands.ChangeLegNumber>(KnownEventTypes.EVAC.dataString) {
        changeOrClearEvent(
            hasLegNumber() || hasTotalLegs(),
            "${legNumber.value} of ${totalLegs.value}",
            "leg #"
        )
    }
fun buildChangeNinelineTimeCommand(ninelineTime: Instant?) = changeNinelineTime {
    ninelineTime?.let { this.time = int64Value(it.epochSecond) }
}

fun changeNinelineTimeCommandHandler(movement: Movement) =
    handler<MovementCommands.ChangeNinelineTime> {
        movement.ninelineTime =
            if (hasTime() && time.value != -1L) Instant.ofEpochSecond(time.value) else null
    }

fun changeNineLineTimeEventHandler() =
    eventHandler<MovementCommands.ChangeNinelineTime>(KnownEventTypes.EVAC.dataString) {
        changeOrClearEvent(
            hasTime() && time.value != -1L,
            Instant.ofEpochSecond(time.value).format(Patterns.mdhm_24_space_comma_colon),
            "9-Line time"
        )
    }

@Deprecated("Use buildUpdateDispatchedEvacCommand", ReplaceWith("buildUpdateDispatchedEvacCommand"))
fun buildChangeDispatchEvacCommand(evacCategory: String?) = changeDispatchEvac {
    evacCategory?.let { this.evacString = it }
}

fun buildUpdateDispatchedEvacCommand(evacStatus: EvacStatus) = changeDispatchEvac {
    evac = evacStatus.toProto()
}

fun buildUpdateDispatchedEvacCommand(evacStatus: String?) = changeDispatchEvac {
    evac = EvacStatus.fromString(evacStatus)
}

fun changeDispatchEvacCommandHandler(movement: Movement) =
    handler<MovementCommands.ChangeDispatchEvac> {
        movement.dispatchEvac = EvacStatus.fromProto(evac)
    }

fun changeDispatchEvacEventHandler() =
    eventHandler<MovementCommands.ChangeDispatchEvac>(KnownEventTypes.EVAC.dataString) {
        EvacStatus.toEvent(evac, "Dispatched Evac")
    }

@Deprecated("Use buildUpdateAssessedEvacCommand instead", ReplaceWith("buildUpdateAssessedEvacCommand"))
fun buildChangeAssessedEvacCommand(evacCategory: String?) = changeAssessedEvac {
    evacCategory?.let { this.evacString = it }
}


fun buildUpdateAssessedEvacCommand(evacStatus: EvacStatus) = changeAssessedEvac {
    evac = evacStatus.toProto()
}

fun buildUpdateAssessedEvacCommand(evacStatus: String?) = changeAssessedEvac {
    evac = EvacStatus.fromString(evacStatus)
}

fun changeAssessedEvacCommandHandler(movement: Movement) =
    handler<MovementCommands.ChangeAssessedEvac> {
        movement.assessedEvac = EvacStatus.fromProto(evac)
    }

fun changeAssessedEvacEventHandler() =
    eventHandler<MovementCommands.ChangeAssessedEvac>(KnownEventTypes.EVAC.dataString) {
        EvacStatus.toEvent(evac, "Assessed Evac")
    }

fun buildNinelinePlatformCommand (ninelinePlatform: String?) = changeNinelinePlatform {
    this.platform = NinelinePlatform.fromString(ninelinePlatform)
}

fun changeNinelinePlatformCommandHandler(movement: Movement) =
    handler<MovementCommands.ChangeNinelinePlatform> {
        movement.ninelinePlatform = NinelinePlatform.fromProto(platform)
    }

fun changeNinelinePlatformEventHandler() =
    eventHandler<MovementCommands.ChangeNinelinePlatform>(KnownEventTypes.EVAC.dataString) {
        NinelinePlatform.toEvent(platform)
    }
fun buildCapabilityCommand(addCapability: List<String>, removedCapabilities: List<String>) =
    changeCapability {
        this.addedCapabilities.addAll(addCapability.map { Capability.fromString(it) })
        this.removedCapabilities.addAll(removedCapabilities.map { Capability.fromString(it) })
    }

fun changeCapabilityCommandHandler(movement: Movement) =
    handler<MovementCommands.ChangeCapability> {
        val addedCapabilitiesList = addedCapabilitiesList.mapNotNull { Capability.fromProto(it) }
        val removedCapabilitiesList =
            removedCapabilitiesList.mapNotNull { Capability.fromProto(it) }

        movement.capability += addedCapabilitiesList
        movement.capability -= removedCapabilitiesList
    }

fun changeCapabilityEventHandler() =
    eventHandler<MovementCommands.ChangeCapability>(KnownEventTypes.EVAC.dataString) {
        val addedString =
            addedCapabilitiesList.mapNotNull { Capability.fromProto(it) }.joinToString(", ")
        val removedString =
            removedCapabilitiesList.mapNotNull { Capability.fromProto(it) }.joinToString(", ")

        var totalString = if (addedCapabilitiesList.isNotEmpty()) {
            "Added Evac Capabilities: $addedString"
        } else {
            ""
        }
        totalString += if (removedCapabilitiesList.isNotEmpty()) {
            "\nRemoved Evac Capabilities: $removedString"
        } else {
            ""
        }
        totalString.trim()
    }

fun buildNinelineLocationCommand(isPickup: Boolean, ninelineLocation: NinelineLocation) =
    changeNinelineLocation {
        this.isPickup = isPickup
        this.location = gov.afrl.batdok.commands.proto.ninelineLocation {
            ninelineLocation.time?.let { this.time = int64Value(it.epochSecond) }
            ninelineLocation.location?.let { this.location = it }
            this.region = Region.fromString(ninelineLocation.region)
            this.role = Role.fromString(ninelineLocation.role)
        }
    }

fun changeNinelineLocationCommandHandler (movement: Movement) =
    handler<MovementCommands.ChangeNinelineLocation> {
        val evacLocation = NinelineLocation(
            time = location.time?.value?.let { Instant.ofEpochSecond(it) },
            location = location.location,
            region = Region.fromProto(location.region),
            role = Role.fromProto(location.role)
        )
        if (isPickup) {
            movement.pickupLocation = evacLocation
        } else {
            movement.dropoffLocation = evacLocation
        }

    }

fun changeNinelineLocationEventHandler() =
    eventHandler<MovementCommands.ChangeNinelineLocation>(KnownEventTypes.EVAC.dataString) {
        val pickupOrDropoff = if (isPickup) "pickup" else "dropoff"
        val evacLocation = NinelineLocation(
            time = location.time?.value?.let { Instant.ofEpochSecond(it) },
            location = location.location,
            region = Region.fromProto(location.region),
            role = Role.fromProto(location.role)
        )
        "Changed $pickupOrDropoff location: $evacLocation"
    }

fun buildOriginatingMtfCommand(originatingMtf: String?) = changeOriginatingMtf {
    originatingMtf?.let { this.originatingMtf = it }
}

fun changeOriginatingMtfCommandHandler(movement: Movement) =
    handler<MovementCommands.ChangeOriginatingMtf> {
        movement.originatingMtf = originatingMtf
    }

fun changeOriginatingMtfEventHandler() =
    eventHandler<MovementCommands.ChangeOriginatingMtf>(KnownEventTypes.EVAC.dataString) {
        changeOrClearEvent(!originatingMtf.isNullOrBlank(), originatingMtf, "Originating MTF")
    }

fun buildDestinationMtfCommand(destinationMtf: String?) = changeDestinationMtf {
    destinationMtf?.let { this.destinationMtf = it }
}

fun changeDestinationMtfCommandHandler(movement: Movement) =
    handler<MovementCommands.ChangeDestinationMtf> {
        movement.destinationMtf = destinationMtf
    }

fun changeDestinationMtfEventHandler() =
    eventHandler<MovementCommands.ChangeDestinationMtf>(KnownEventTypes.EVAC.dataString) {
        changeOrClearEvent(!destinationMtf.isNullOrBlank(), destinationMtf, "Destination MTF")
    }

fun buildReasonRegulatedCommand(reasonRegulated: String?) = changeReasonRegulated {
    reasonRegulated?.let { this.reasonRegulated = ReasonRegulated.fromString(it) }
}

fun changeReasonRegulatedCommandHandler(movement: Movement) =
    handler<MovementCommands.ChangeReasonRegulated> {
        movement.reasonRegulated = ReasonRegulated.fromProto(reasonRegulated)
    }

fun changeReasonRegulatedEventHandler() =
    eventHandler<MovementCommands.ChangeReasonRegulated>(KnownEventTypes.EVAC.dataString) {
        ReasonRegulated.toEvent(reasonRegulated)
    }
@Deprecated(
    message = "Use buildMaxNumberOfStopsCommand",
    replaceWith = ReplaceWith("buildMaxNumberOfStopsCommand")
)
fun buildMaxStopsCommand(maxStops: Int?) = changeMaxStops {
    maxStops?.let { this.maxStops = int32Value(maxStops) }
}


@Deprecated(
    message = "Use changeMaxNumberOfStopsCommandHandler",
    replaceWith = ReplaceWith("changeMaxNumberOfStopsCommandHandler")
)
fun changeMaxStopsCommandHandler(movement: Movement) =
    handler<MovementCommands.ChangeMaxStops> {
        movement.maxStops = maxStops.value.takeIf { hasMaxStops() }
    }
@Deprecated(
    message = "Use changeMaxNumberOfStopsEventHandler",
    replaceWith = ReplaceWith("changeMaxNumberOfStopsEventHandler")
)
fun changeMaxStopsEventHandler() =
    eventHandler<MovementCommands.ChangeMaxStops>(KnownEventTypes.EVAC.dataString) {
        changeOrClearEvent(hasMaxStops(), maxStops.value.toString(), "Max Stops")
    }

fun buildMaxNumberOfStopsCommand(maxStops: MaxNumOfStops) = changeMaxNumberOfStops {
    this.maxStops = maxStops.toProto()
}

fun buildMaxNumberOfStopsCommand(maxStops: String?) = changeMaxNumberOfStops {
    this.maxStops = MaxNumOfStops.fromString(maxStops)
}

fun changeMaxNumberOfStopsCommandHandler(movement: Movement) =
    handler<MovementCommands.ChangeMaxNumberOfStops> {
        movement.maxNumOfStops = MaxNumOfStops.fromProto(maxStops)
    }

fun changeMaxNumberOfStopsEventHandler() =
    eventHandler<MovementCommands.ChangeMaxNumberOfStops>(KnownEventTypes.MOVEMENT.dataString) {
        MaxNumOfStops.toEvent(maxStops)
    }

@Deprecated(
    message = "Use buildMaxNumberOfRonsCommand",
    replaceWith = ReplaceWith("buildMaxNumberOfRonsCommand")
)
fun buildMaxRonsCommand(maxRons: Int?) = changeMaxRons {
    maxRons?.let { this.maxRons = int32Value(maxRons) }
}

@Deprecated(
    message = "Use changeMaxNumberOfRonsCommandHandler",
    replaceWith = ReplaceWith("changeMaxNumberOfRonsCommandHandler")
)
fun changeMaxRonsCommandHandler(movement: Movement) =
    handler<MovementCommands.ChangeMaxRons> {
        movement.maxRons = maxRons.value.takeIf { hasMaxRons() }
    }


@Deprecated(
    message = "Use changeMaxNumberOfRonsEventHandler",
    replaceWith = ReplaceWith("changeMaxNumberOfRonsEventHandler")
)
fun changeMaxRonsEventHandler() =
    eventHandler<MovementCommands.ChangeMaxRons>(KnownEventTypes.EVAC.dataString) {
        changeOrClearEvent(hasMaxRons(), maxRons.value.toString(), "Max RONs")
    }

fun buildMaxNumberOfRonsCommand(maxNumOfRons: MaxNumOfRons) = changeMaxNumberOfRons {
    this.maxNumberOfRons = maxNumOfRons.toProto()
}

fun buildMaxNumberOfRonsCommand(maxNumOfRons: String?) = changeMaxNumberOfRons {
    this.maxNumberOfRons = MaxNumOfRons.fromString(maxNumOfRons)
}

fun changeMaxNumberOfRonsCommandHandler(movement: Movement) =
    handler<MovementCommands.ChangeMaxNumberOfRons> {
        movement.maxNumOfRons = MaxNumOfRons.fromProto(maxNumberOfRons)
    }

fun changeMaxNumberOfRonsEventHandler() =
    eventHandler<MovementCommands.ChangeMaxNumberOfRons>(KnownEventTypes.MOVEMENT.dataString) {
        MaxNumOfRons.toEvent(maxNumberOfRons)
    }

fun buildAltitudeRestrictionsCommand(altitudeRestrictions: String?) = changeAltitudeRestrictions {
    altitudeRestrictions?.let { this.altitudeRestrictions = it }
}

fun changeAltitudeRestrictionsCommandHandler(movement: Movement) =
    handler<MovementCommands.ChangeAltitudeRestrictions> {
        movement.altitudeRestrictions = altitudeRestrictions
    }

fun changeAltitudeRestrictionsEventHandler() =
    eventHandler<MovementCommands.ChangeAltitudeRestrictions>(KnownEventTypes.EVAC.dataString) {
        changeOrClearEvent(
            !altitudeRestrictions.isNullOrBlank(),
            altitudeRestrictions,
            "Altitude Restrictions"
        )
    }

fun buildFlightLevelCommand(flightLevel: String?) = changeFlightLevel {
    flightLevel?.let { this.flightLevel = it }
}

fun changeFlightLevelCommandHandler(movement: Movement) =
    handler<MovementCommands.ChangeFlightLevel> {
        movement.flightLevel = flightLevel
    }

fun changeFlightLevelEventHandler() =
    eventHandler<MovementCommands.ChangeFlightLevel>(KnownEventTypes.EVAC.dataString) {
        changeOrClearEvent(!flightLevel.isNullOrBlank(), flightLevel, "Flight Level")
    }

@Deprecated(
    message = "Use buildUpdateReadyDateCommand",
    replaceWith = ReplaceWith("buildUpdateReadyDateCommand")
)
fun buildChangeReadyDateCommand(readyDate: Instant?) = changeReadyDate {
    readyDate?.let { this.readyDate = int64Value(it.epochSecond) }
}

fun buildUpdateReadyDateCommand(readyDate: LocalDate?) = changeReadyDate {
    readyDate?.let { <EMAIL> = int64Value(it.toEpochDay()) }
}

fun changeReadyDateCommandHandler(movement: Movement) =
    handler<MovementCommands.ChangeReadyDate> {
        movement.readyDate =
            if (hasReadyDate() && readyDate.value != -1L) Instant.ofEpochSecond(readyDate.value) else null

        movement.localReadyDate =
            if (hasReadyDate() && this.readyDate.value != -1L) LocalDate.ofEpochDay(this.readyDate.value) else null
    }

fun changeReadyDateEventHandler() =
    eventHandler<MovementCommands.ChangeReadyDate>(KnownEventTypes.EVAC.dataString) {
        changeOrClearEvent(
            hasReadyDate() && this.readyDate.value != -1L,
            LocalDate.ofEpochDay(this.readyDate.value).format(Patterns.mdy_dash),
            "Ready Date"
        )
    }

fun buildChangeMedicalAttendantsNeededCommand(medicalAttendantsNeeded: Int?) = changeMedicalAttendantsNeeded {
    medicalAttendantsNeeded?.let { this.medicalAttendantsNeeded = int32Value(medicalAttendantsNeeded) }
}

fun changeMedicalAttendantsNeededCommandHandler(movement: Movement) =
    handler<MovementCommands.ChangeMedicalAttendantsNeeded> {
        movement.medicalAttendantsNeeded = medicalAttendantsNeededOrNull?.value
    }

fun changeMedicalAttendantsNeededEventHandler() =
    eventHandler<MovementCommands.ChangeMedicalAttendantsNeeded>(KnownEventTypes.EVAC.dataString) {
        changeOrClearEvent(hasMedicalAttendantsNeeded(), medicalAttendantsNeeded.value.toString(), "Medical Attendants Needed")
    }

fun buildChangeNonMedicalAttendantsNeededCommand(nonMedicalAttendantsNeeded: Int?) = changeNonMedicalAttendantsNeeded {
    nonMedicalAttendantsNeeded?.let { this.nonMedicalAttendantsNeeded = int32Value(nonMedicalAttendantsNeeded) }
}

fun changeNonMedicalAttendantsNeededCommandHandler(movement: Movement) =
    handler<MovementCommands.ChangeNonMedicalAttendantsNeeded> {
        movement.nonMedicalAttendantsNeeded = nonMedicalAttendantsNeededOrNull?.value
    }

fun changeNonMedicalAttendantsNeededEventHandler() =
    eventHandler<MovementCommands.ChangeNonMedicalAttendantsNeeded>(KnownEventTypes.EVAC.dataString) {
        changeOrClearEvent(hasNonMedicalAttendantsNeeded(), nonMedicalAttendantsNeeded.value.toString(), "Non-Medical Attendants Needed")
    }

fun buildAttendantCommand(addAttendant: List<Attendant>, removedAttendants: List<Attendant>) =
    changeAttendants {
        this.addedAttendants.addAll(addAttendant.map { it.toProto() })
        this.removedAttendants.addAll(removedAttendants.map { it.toProto() })
    }

fun changeAttendantsCommandHandler(movement: Movement) =
    handler<MovementCommands.ChangeAttendants> {
        val addedAttendants = addedAttendantsList.mapNotNull { Attendant.fromProto(it) }
        val removedAttendants = removedAttendantsList.mapNotNull { Attendant.fromProto(it) }

        movement.attendants += addedAttendants
        movement.attendants -= removedAttendants
    }

fun changeAttendantsEventHandler() =
    eventHandler<MovementCommands.ChangeAttendants>(KnownEventTypes.EVAC.dataString) {
        val addedString =
            addedAttendantsList.mapNotNull { Attendant.fromProto(it).name?.toString(NameFormatter.Format.FIRST_MIDDLE_LAST) }
                .joinToString(", ")
        val removedString =
            removedAttendantsList.mapNotNull { Attendant.fromProto(it).name?.toString(NameFormatter.Format.FIRST_MIDDLE_LAST) }
                .joinToString(", ")

        var totalString = if (addedAttendantsList.isNotEmpty()) {
            "Added Evac Attendants: $addedString"
        } else {
            ""
        }
        totalString += if (removedAttendantsList.isNotEmpty()) {
            "\nRemoved Evac Attendants: $removedString"
        } else {
            ""
        }
        totalString.trim()
    }

fun buildClassificationCommand(classification: Classification) = changeClassification {
   this.classification = classification.toProto()
}

fun buildClassificationCommand(classification: String?) = changeClassification {
    this.classification = Classification.fromString(classification)
}

fun changeClassificationCommandHandler(movement: Movement) =
    handler<MovementCommands.ChangeClassification> {
        movement.classification = Classification.fromProto(classification)
    }

fun changeClassificationEventHandler() =
    eventHandler<MovementCommands.ChangeClassification>(KnownEventTypes.EVAC.dataString) {
        Classification.toEvent(classification)
    }

fun buildPrecedenceCommand(precedence: String?) = changePrecedence {
    precedence?.let { this.precedence = it }
}

fun changePrecedenceCommandHandler(movement: Movement) =
    handler<MovementCommands.ChangePrecedence> {
        movement.precedence = precedence
    }

fun changePrecedenceEventHandler() =
    eventHandler<MovementCommands.ChangePrecedence>(KnownEventTypes.EVAC.dataString) {
        changeOrClearEvent(!precedence.isNullOrBlank(), precedence, "Precedence")
    }

fun buildCriticalCareCommand(criticalCare: Boolean?) = changeCriticalCare {
    criticalCare?.let { this.criticalCare = booleanValue(criticalCare) }
}

fun changeCriticalCareCommandHandler(movement: Movement) =
    handler<MovementCommands.ChangeCriticalCare> {
        movement.criticalCare = criticalCare.value.takeIf { hasCriticalCare() }
    }

fun changeCriticalCareEventHandler() =
    eventHandler<MovementCommands.ChangeCriticalCare>(KnownEventTypes.EVAC.dataString) {
        when (criticalCareOrNull?.value) {
            true -> "Patient is Critical Care"
            false -> "Patient is not Critical Care"
            null -> "Cleared Critical Care"
        }
    }

// begin attendant commands
fun changeNameCommandHandler(attendant: Attendant) = handler<InfoCommands.ChangeNameCommand> {
    attendant.name = Name(first, middle, last)
}

fun changeGenderCommandHandler(attendant: Attendant) = handler<InfoCommands.ChangeGenderCommand> {
    attendant.gender = Gender.fromProto(gender)
}

fun changeWeightCommandHandler(attendant: Attendant) = handler<InfoCommands.ChangeWeightCommand> {
    attendant.weight = if (hasWeight() && weight.value != -1f) weight.value else null
}

fun changeGradeCommandHandler(attendant: Attendant) = handler<InfoCommands.ChangeGradeCommand> {
    attendant.grade = Grade.fromProto(grade)
}

fun buildIsMedicalCommand(isMedical: Boolean?) = changeIsMedicalCommand {
    isMedical?.let { this.isMedical = booleanValue(isMedical) }
}

fun changeIsMedicalCommandHandler(attendant: Attendant) =
    handler<MovementCommands.ChangeIsMedicalCommand> {
        attendant.isMedical = isMedical.value.takeIf { hasIsMedical() }
    }

fun changeIsMedicalEventHandler() =
    eventHandler<MovementCommands.ChangeIsMedicalCommand>(KnownEventTypes.EVAC.dataString) {
        when (isMedical?.value) {
            true -> "Attendant is medical"
            false -> "Attendant is not medical"
            null -> "Clear medical"
        }
    }


fun buildAmbulatoryOrLitterCommand(value: String?) = changeAmbulatoryOrLitterCommand {
    value?.let {
        this.ambulatoryOrLitter = stringValue(value)
    }
}

fun changeAmbulatoryOrLitterCommandHandler(movement: Movement) =  handler<MovementCommands.ChangeAmbulatoryOrLitterCommand>{
    movement.ambulatoryOrLitter = ambulatoryOrLitter.value.takeIf{hasAmbulatoryOrLitter()}
}

fun changeAmbulatoryOrLitterEventHandler() = eventHandler<MovementCommands.ChangeAmbulatoryOrLitterCommand>{
    changeOrClearEvent(hasAmbulatoryOrLitter(), ambulatoryOrLitter.value, "Ambulatory or Litter" )

}

fun buildClearPickupLocationCommand() = MovementCommands.ClearPickupLocationCommand.newBuilder()
    .build()

fun buildClearDropoffLocationCommand() = MovementCommands.ClearDropoffLocationCommand.newBuilder()
    .build()


fun clearPickupLocationCommandHandler(movement: Movement) = handler<MovementCommands.ClearPickupLocationCommand> {
    movement.pickupLocation = null
}

fun clearDropoffLocationCommandHandler(movement: Movement) = handler<MovementCommands.ClearDropoffLocationCommand> {
    movement.dropoffLocation = null
}

fun clearPickupLocationEventHandler() =
    eventHandler<MovementCommands.ClearPickupLocationCommand>("Pickup location cleared") {
        changeOrClearEvent(
            false,
            "Pickup location",
            "Pickup Location"
        )
    }

fun clearDropoffLocationEventHandler() =
    eventHandler<MovementCommands.ClearDropoffLocationCommand>("Drop-off location cleared") {
        changeOrClearEvent(
            false,
            "Drop-off location",
            "Drop-off Location"
        )
    }