package gov.afrl.batdok.encounter.movement

import gov.afrl.batdok.commands.proto.MovementCommands
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.bloodProduct
import gov.afrl.batdok.encounter.observation.Blood
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.compatibleEnum
import gov.afrl.batdok.util.nullableInt32Value
import gov.afrl.batdok.util.nullableInt64Value
import gov.afrl.batdok.util.nullableStringValue
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto
import gov.afrl.batdok.util.toChangeOrClearEvent

enum class MaxNumOfRons(
    override val protoIndex: Int,
    override val dataString: String,
    val buttonString: String = dataString
) : ProtoEnum {
    ZERO(1, "0"),
    ONE(2, "1"),
    T<PERSON><PERSON>(3, "2"),
    T<PERSON><PERSON><PERSON>(4, "3"),
    FO<PERSON>(5, "4"),
    FIVE(6, "5"),
    SIX(7, "6"),
    SEVEN(8, "7"),
    EIGHT(9, "8"),
    UNRESTRICTED(10, "Unrestricted");

    companion object {
        fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = MaxNumOfRons.values().protoToString(enum)
        fun fromString(string: String?) = MaxNumOfRons.values().stringToProto(string)
        fun toEvent(enum: SharedProtobufObjects.CompatibleEnum) = MaxNumOfRons.values()
            .toChangeOrClearEvent(enum, "Max RONS")
    }
}