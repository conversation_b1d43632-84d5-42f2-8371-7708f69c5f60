package gov.afrl.batdok.encounter.vitals

import gov.afrl.batdok.commands.proto.VitalOuterClass
import gov.afrl.batdok.commands.proto.output
import gov.afrl.batdok.encounter.IndividualVital
import gov.afrl.batdok.util.floatValue

class Output(val output: Float?): IndividualVital("Output"){
    constructor(output: VitalOuterClass.Output): this(
        output.output.value.takeIf { output.hasOutput() },
    )
    override fun toProtobuf() = output {
        <EMAIL>?.let { this.output = floatValue(it) }
    }
    override fun produceEmptyVital() = Output(null)

    override val eventMessage: String
        get() = output?.let { "%.1f".format(it) } ?: "--"

    override val isEmpty: Boolean
        get() = output == null
}