package gov.afrl.batdok.encounter.commands

import gov.afrl.batdok.commands.eventHandler
import gov.afrl.batdok.commands.handler
import gov.afrl.batdok.commands.handlerWithId
import gov.afrl.batdok.commands.proto.*
import gov.afrl.batdok.commands.proto.SubjectiveCommands.*
import gov.afrl.batdok.encounter.*
import gov.afrl.batdok.encounter.ids.toByteString
import gov.afrl.batdok.encounter.ids.toDomainId
import gov.afrl.batdok.encounter.ids.toDomainIdOrElse
import gov.afrl.batdok.util.addPreviousFields
import gov.afrl.batdok.util.booleanValue
import gov.afrl.batdok.util.removeDuplicates
import gov.afrl.batdok.util.stringValue
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant

@Deprecated("Use Add/Update/RemoveComplaintCommand instead")
fun buildUpdateChiefComplaintCommand(symptom: String, add: Boolean) = updateChiefComplaintCommand {
    this.symptom = symptom
    this.checked = add
}
@Deprecated("Use Add/Update/RemoveComplaintCommand instead")
fun updateChiefComplaintCommandHandler(subjective: Subjective) = handler<UpdateChiefComplaintCommand> {
    val symptom = this.symptom
    if(checked){
        subjective.addChiefComplaint(symptom)
    }else{
        subjective.removeChiefComplaint(symptom)
    }
}
@Deprecated("Use Add/Update/RemoveComplaintCommand instead")
fun updateChiefComplaintEventCommandHandler() = eventHandler<UpdateChiefComplaintCommand>(KnownEventTypes.SUBJECTIVE.dataString) {
    val action = if(this.checked) "Added" else "Removed"
    val symptom = this.symptom
    "$action Chief Complaint: $symptom"
}

fun buildAddComplaintCommand(symptom: String, history: String? = null, reviewOfSystems: String? = null, id: DomainId? = null, disposition: String? = null) = addComplaintCommand {
    this.symptom = CommonComplaints.fromString(symptom)
    history?.let { this.symptomHistory = it }
    reviewOfSystems?.let { this.reviewOfSystems = it }
    disposition?.let { this.disposition = Disposition.fromString(it) }
    id?.toByteString()?.let { this.id = it }
}
fun addComplaintCommandHandler(subjective: Subjective) = handlerWithId<AddComplaintCommand> { docId, command ->
    val complaint = Complaint(
        CommonComplaints.fromProto(this.symptom)?:"",
        this.symptomHistory,
        careProvider = command.callsign,
        reviewOfSystems = this.reviewOfSystems,
        documentId = docId,
        id = id.toDomainIdOrElse(command.commandId),
        disposition = Disposition.fromProto(this.disposition),
        timestamp = Instant.ofEpochSecond(command.timestamp)
    )
    subjective += complaint
}
fun addComplaintEventCommandHandler() = eventHandler<AddComplaintCommand>(KnownEventTypes.SUBJECTIVE.dataString) {
    val symptom = CommonComplaints.fromProto(symptom)
    val disposition = Disposition.fromProto(disposition)
    "Added Complaint: $symptom" + (if(symptomHistory.isNotEmpty()) " - $symptomHistory" else "") + (if(reviewOfSystems.isNotEmpty()) " - $reviewOfSystems" else "" + (if(!disposition.isNullOrEmpty()) " - $disposition" else ""))
}

/**
 * Builds an update  complaint command. Only pass in the data you changed. Set values to "" to clear
 */
fun buildUpdateComplaintCommand(id: DomainId? = null, symptom: String? = null, history: String? = null, reviewOfSystems: String? = null, disposition: String? = null) = updateComplaintCommand {
    symptom?.let { this.symptom = CommonComplaints.fromString(symptom) }
    history?.let { this.symptomHistory = stringValue(it) }
    reviewOfSystems?.let { this.reviewOfSystems = stringValue(it) }
    disposition?.let { this.disposition = Disposition.fromString(it) }
    id?.toByteString()?.let { this.id = it }
}
fun updateComplaintCommandHandler(subjective: Subjective) = handlerWithId<UpdateComplaintCommand> { docId, command ->
    val prevComplaint = subjective.complaints[id.toDomainId<DomainId>()]
    val complaint = Complaint(
        CommonComplaints.fromProto(symptom) ?: prevComplaint?.complaint ?: "" ,
        this.symptomHistoryOrNull?.value ?: prevComplaint?.history ?: "",
        careProvider = command.callsign,
        reviewOfSystems = this.reviewOfSystemsOrNull?.value ?: prevComplaint?.reviewOfSystems ?: "",
        documentId = docId,
        id = id.toDomainIdOrElse(command.commandId),
        disposition = if(dispositionOrNull == null) prevComplaint?.disposition else Disposition.fromProto(disposition),
        timestamp = Instant.ofEpochSecond(command.timestamp)
    )
    subjective += complaint
}
fun updateComplaintEventCommandHandler(subjective: Subjective) = eventHandler<UpdateComplaintCommand>(KnownEventTypes.SUBJECTIVE.dataString) {
    val prevComplaint = subjective.complaints[id.toDomainId<DomainId>()]
    val symptom = CommonComplaints.fromProto(symptom) ?: prevComplaint?.complaint ?: ""
    val history = this.symptomHistoryOrNull?.value ?: prevComplaint?.history ?: ""
    val reviewOfSystems = this.reviewOfSystemsOrNull?.value ?: prevComplaint?.reviewOfSystems ?: ""
    val disposition = (if(dispositionOrNull == null) prevComplaint?.disposition else Disposition.fromProto(disposition)) ?: ""
    "Updated Complaint: $symptom" + (if(history.isNotEmpty()) " - $history" else "") + (if(reviewOfSystems.isNotEmpty()) " - $reviewOfSystems" else "") + (if(disposition.isNotEmpty()) " - $disposition" else "")
}

fun buildRemoveComplaintCommand(id: DomainId) = removeComplaintCommand {
    this.id = id.toByteString()
}
fun removeComplaintCommandHandler(subjective: Subjective) = handler<RemoveComplaintCommand> {
    subjective.removeComplaint(id.toDomainId())
}
fun removeComplaintEventCommandHandler(subjective: Subjective) = eventHandler<RemoveComplaintCommand>(KnownEventTypes.SUBJECTIVE.dataString) {
    val complaint = subjective.complaints[id.toDomainId<DomainId>()] ?: return@eventHandler null
    "Removed Complaint: ${complaint.complaint}" + (if(complaint.history.isNotEmpty()) " - ${complaint.history}" else "") + (if(complaint.reviewOfSystems.isNotEmpty()) " - ${complaint.reviewOfSystems}" else "")
}


fun buildUpdateReviewOfSystemsCommand(symptom: String, reviewResult: Boolean?) = updateReviewOfSystemsCommand {
    this.symptom = symptom
    reviewResult?.let { this.checked = booleanValue(it) }
}
fun updateReviewOfSystemsCommandHandler(subjective: Subjective) = handler<UpdateReviewOfSystemsCommand> {
    val symptom = this.symptom
    if(hasChecked()){
        // Include both positive and negative symptoms in the list
        subjective += symptom to checked.value
    }else{
        //Only remove if `checked` was null
        subjective.removeSystemReview(symptom)
    }
}
fun updateReviewOfSystemsEventCommandHandler() = eventHandler<UpdateReviewOfSystemsCommand>(KnownEventTypes.SUBJECTIVE.dataString) {
    val action = when{
        hasChecked() && checked.value -> "Added positive"
        hasChecked() && !checked.value -> "Added negative"
        else -> "Removed"
    }
    val symptom = this.symptom
    "$action Review of System: $symptom"
}

fun buildUpdateOpqrstlCommand(opqrstl: Opqrstl, oldValues: Opqrstl? = null): UpdateOpqrstlCommand {
    val oldTreatmentProtobuf = oldValues?.toProtobuf()
    return opqrstl.toProtobuf().removeDuplicates(oldTreatmentProtobuf)
}
fun updateOpqrstlCommandHandler(subjective: Subjective) = handler<UpdateOpqrstlCommand>{
    val oldData = subjective.opqrstl
    val restoredData = UpdateOpqrstlCommand.parseFrom(this.addPreviousFields(oldData?.toProtobuf()).toByteArray())
    subjective.opqrstl = Opqrstl(restoredData)
}
fun updateOpqrstlEventCommandHandler(subjective: Subjective) = eventHandler<UpdateOpqrstlCommand>(KnownEventTypes.SUBJECTIVE.dataString){
    val oldData = subjective.opqrstl
    val restoredData = UpdateOpqrstlCommand.parseFrom(this.addPreviousFields(oldData?.toProtobuf()).toByteArray())
    val opqrstl = Opqrstl(restoredData)
    if(!opqrstl.isEmpty()) {
        "Updated OPQRST-L - ${Opqrstl(restoredData)}"
    }else{
        "Cleared OPQRST-L"
    }
}