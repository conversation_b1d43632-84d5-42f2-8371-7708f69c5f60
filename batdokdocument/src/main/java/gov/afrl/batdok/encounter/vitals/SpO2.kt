package gov.afrl.batdok.encounter.vitals

import gov.afrl.batdok.commands.proto.VitalOuterClass
import gov.afrl.batdok.commands.proto.spO2
import gov.afrl.batdok.encounter.IndividualVital
import gov.afrl.batdok.util.int32Value

class SpO2(val spo2: Int?): IndividualVital("SpO2"){
    constructor(spo2: VitalOuterClass.SpO2): this(
        spo2.spo2.value.takeIf { spo2.hasSpo2() },
    )
    override fun toProtobuf() = spO2 {
        this@SpO2.spo2?.let { this.spo2 = int32Value(it) }
    }
    override fun produceEmptyVital() = SpO2(null)
    override val eventMessage: String
        get() = "$spo2"

    override val isEmpty: Boolean
        get() = spo2 == null
}