package gov.afrl.batdok.encounter.commands

import gov.afrl.batdok.commands.eventHandler
import gov.afrl.batdok.commands.handler
import gov.afrl.batdok.commands.proto.*
import gov.afrl.batdok.commands.proto.MOICommands.*
import gov.afrl.batdok.encounter.*
import gov.afrl.batdok.util.DD1380InjuryBoundingBox
import gov.afrl.batdok.util.doubleValue

fun buildChangeInjuryCommand(
    injury: String, checked: Boolean, customAbbrev: String,
    location: String?
) = changeInjuryCommand {
    this.injury = Injury.Type.fromString(injury)
    this.checked = checked
    this.customAbbrev = customAbbrev
    this.location = InjuryLocation.fromString(location)
}

fun changeInjuryCommandHandler(injuries: Injuries) = handler<ChangeInjuryCommand> {
    val location = InjuryLocation.fromProto(this.location)
    val injury = Injury.Type.fromProto(this.injury) ?: return@handler
    val abbrev = Injury.Type.entries.find { it.dataString == injury }?.abbrev ?: customAbbrev
    if (this.checked) {
        //Then add it to the injury list for the given location
        injuries.addInjury(
            Injury(
                injury, abbrev
            ),
            location
        )
    } else {
        if (location == null){
            //Then remove the injury for all locations
            injuries.injuries.keys.forEach { location ->
                injuries.removeInjury(Injury(injury, abbrev), location)
            }
        }else {
            //Then remove it to the injury list for the given location
            injuries.removeInjury(Injury(injury, abbrev), location)
        }
        injuries.drawingPoints = injuries.drawingPoints.filter { it.label != this.customAbbrev }
    }
}

fun changeInjuryEventHandler() = eventHandler<ChangeInjuryCommand>(KnownEventTypes.MOI.dataString) {
    (if (checked) "Set" else "Unset") + " ${Injury.Type.fromProto(this.injury)} as Injury"
}

fun buildChangeMoiCommand(moi: String, checked: Boolean, location: String?) = changeMoiCommand {
    this.moi = Moi.fromString(moi)
    this.checked = checked
    this.location = InjuryLocation.fromString(location)
}

fun changeMoiCommandHandler(injuries: Injuries) = handler<ChangeMoiCommand> {
    val location = InjuryLocation.fromProto(this.location)
    val moi = Moi.fromProto(this.moi) ?: return@handler

    if (this.checked) {
        //Then add it to the moi list
        injuries.addMoi(moi, location)
    } else {
        //Then remove it from the moi list
        injuries.removeMoi(moi, location)
        //Also remove drawings associated with it
        injuries.drawingPoints = injuries.drawingPoints.filter { it.label != moi }
    }
}

fun changeMoiEventHandler() = eventHandler<ChangeMoiCommand>(KnownEventTypes.MOI.dataString) {
    (if (this.checked) "Set" else "Unset") + " ${Moi.fromProto(this.moi)} as MoI"
}

fun buildChangeTBSACommand(tbsa: Double?) =
    changeTBSACommand { tbsa?.let { this.tbsa = doubleValue(it) } }

fun changeTBSACommandHandler(injuries: Injuries) = handler<ChangeTBSACommand> {
    injuries.tbsa = this.tbsaOrNull?.value
}

fun changeTbsaEventHandler() = eventHandler<ChangeTBSACommand>(KnownEventTypes.MOI.dataString) {
    if (!hasTbsa()) {
        "Cleared Burn TBSA"
    } else {
        "Set Burn TBSA to ${tbsa.value}%"
    }
}

fun buildAddPointWithLabelCommand(point: DrawingPoint) = addPointWithLabelCommand {
    this.point.add(point {
        this.x = point.x
        this.y = point.y
    })
    this.label = point.label
}

fun addPointWithLabelEventHandler() = eventHandler<AddPointWithLabelCommand>(KnownEventTypes.MOI.dataString) {
    val dp = DrawingPoint(label, this.getPoint(0).x, this.getPoint(0).y)
    val location = DD1380InjuryBoundingBox.getFineLocation(dp)
    val expandedInjury = Injury.Type.expandAbbreviation(label) ?: label
    "Added $expandedInjury to body diagram on $location"
}

fun addPointWithLabelCommandHandler(injuries: Injuries) = handler<AddPointWithLabelCommand> {
    injuries.drawingPoints += DrawingPoint(
        this.label,
        this.getPoint(0).x,
        this.getPoint(0).y
    )
}

fun buildRemoveDrawPointCommand(point: DrawingPoint) = removeDrawPointCommand {
    this.point = point {
        this.x = point.x
        this.y = point.y
    }
}

fun removeDrawPointEventHandler() = eventHandler<RemoveDrawPointCommand>(KnownEventTypes.MOI.dataString) {
    // No label in removals
    val dp = DrawingPoint("", point.x, point.y)
    val location = DD1380InjuryBoundingBox.getFineLocation(dp)
    "Removed injury from body diagram on $location"
}

fun removeDrawPointCommandHandler(injuries: Injuries) = handler<RemoveDrawPointCommand> {
    val currentPoint = DrawingPoint(
        "",
        this.point.x,
        this.point.y
    )

    injuries -= currentPoint
}

fun buildUndoDrawViewCommand() = undoDrawViewCommand { }

fun undoDrawPointEventHandler() = eventHandler<UndoDrawViewCommand>(KnownEventTypes.MOI.dataString) {
    "Removed last drawing point from body diagram"
}

fun undoDrawViewCommandHandler(injuries: Injuries) = handler<UndoDrawViewCommand> {
    if (injuries.drawingPoints.isNotEmpty()) {
        injuries -= injuries.drawingPoints.last()
    }
}

fun buildClearInjuryDrawingsCommand(label: String?) = clearInjuryDrawingsCommand {
    if (label != null) {
        this.label = label
    }
}

fun clearInjuryDrawingsEventHandler() = eventHandler<ClearInjuryDrawingsCommand>(KnownEventTypes.MOI.dataString) {
    val expandedInjury = label?.let { Injury.Type.expandAbbreviation(label) ?: label }
    val eventStart = if (label.isNotEmpty()) "Removed $expandedInjury" else "Cleared"
    "$eventStart drawing points from body diagram"
}

fun clearInjuryDrawingsCommandHandler(injuries: Injuries) = handler<ClearInjuryDrawingsCommand> {
    if (label.isNotEmpty()) {
        if (this.label == "TQ") {
            // Remove all TQ drawings from the body diagram
            injuries.drawingPoints = injuries.drawingPoints.filter { !it.label.contains("TQ") }
            // Remove locations from all TQ treatments
            // TODO: Is a new task
        }
        injuries.drawingPoints = injuries.drawingPoints.filter { it.label != this.label }
    } else {
        injuries.drawingPoints = listOf()
    }
}

fun buildClearDrawViewCommand(type: String?) = clearDrawViewCommand {
    if (type != null) {
        this.type = type
    }
}

fun clearDrawViewEventHandler() = eventHandler<ClearDrawViewCommand>(KnownEventTypes.MOI.dataString) {
    "Cleared ${type?.takeIf { it.isNotEmpty() } ?: "all"} drawing points from body diagram"
}

fun clearDrawViewCommandHandler(injuries: Injuries) = handler<ClearDrawViewCommand> {
    if (type.isNotEmpty()) {
        injuries.drawingPoints = injuries.drawingPoints.filter { it.type.name != this.type }
    } else {
        injuries.drawingPoints = listOf()
    }
}