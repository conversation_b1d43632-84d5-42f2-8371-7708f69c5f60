package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.CommandHandler
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.commands.*
import gov.afrl.batdok.encounter.pampi.Immunization
import gov.afrl.batdok.encounter.pampi.Problem
import gov.afrl.batdok.encounter.pampi.Procedure
import gov.afrl.batdok.util.NameFormatter
import mil.af.afrl.batman.batdokid.DomainId
import java.io.Serializable
import java.time.Instant
import java.time.LocalDate

data class Name(val first: String?, val middle: String?, val last: String?): Serializable{
    companion object{
        // This can be called like a constructor
        operator fun invoke(fullName: String, format: NameFormatter.Format = NameFormatter.Format.FIRST_MIDDLE_LAST) =
            format.parse(fullName)
    }
    val isEmpty get() = listOf(first, middle, last).all { it.isNullOrBlank() }
    fun toString(format: NameFormatter.Format) = NameFormatter(
        Triple(first?: "", middle?: "", last?: "")
    )
        .toString(format)
}

class Info: Serializable {
    var name: Name? = null; internal set
    var ssn: String? = null; internal set
    var battleRosterNumber: String? = null; internal set
    var alias: String? = null; internal set
    var bloodType: String? = null; internal set
    var lowTiter: Boolean? = null; internal set
    var timeInfo: Instant? = null; internal set
    @Deprecated("Use patcat instead.", ReplaceWith("patcat"))
    var service: String? = null; internal set
    var patcat: Patcat? = null; internal set
    var unit: String? = null; internal set
    var unitPhoneNumber: String? = null; internal set
    @Deprecated(
        message = "Use grade instead.",
        replaceWith = ReplaceWith("grade")
    )
    var rank: String? = null; internal set
    var grade: String? = null; internal set
    var allergies: List<String> = listOf(); internal set
    var medications: List<String> = listOf(); internal set
    @Deprecated(
        message = "Use triage instead.",
        replaceWith = ReplaceWith("triage")
    )
    var evacCategory: String? = null; internal set
    var triage: String? = null; internal set
    var evacType: String? = null; internal set
    var gender: String? = null; internal set
    var weight: Float? = null; internal set
    var height: Float? = null; internal set
    var tattoo: String? = null; internal set
    @Deprecated(
        message = "Use dateOfBirth instead",
        replaceWith = ReplaceWith("dateOfBirth")
    )
    var dob: LocalDate? = null; internal set
    var dateOfBirth: DateOfBirth = DateOfBirth(); internal set
    var dodId: String? = null; internal set
    var diagnosis: String? = null; internal set
    var maskingJustification: String? = null; internal set
    var citeNumber: String? = null; internal set
    var patientId: DomainId? = null; internal set
    var disposition: String? = null; internal set

    var nationality: CountryData? = null; internal set

    var immunizations: List<Immunization> = listOf(); internal set

    var procedures: List<Procedure> = listOf(); internal set

    var problems: List<Problem> = listOf(); internal set

    @Transient internal val handlers = CommandHandler {
        val info = this@Info
        +changeNameHandler(info)
        +changeBattleRosterCommandHandler(info)
        +changeAliasCommandHandler(info)
        +changeSsnCommandHandler(info)
        +changeUnitCommandHandler(info)
        +changeUnitPhoneNumberCommandHandler(info)
        +changeRankCommandHandler(info)
        +changeGradeCommandHandler(info)
        +changeServiceCommandHandler(info)
        +changePatcatCommandHandler(info)
        +changeTattooCommandHandler(info)
        +changeEvacCommandHandler(info)
        +changeTriageCommandHandler(info)
        +changeEvacTypeCommandHandler(info)
        +changeBloodTypeCommandHandler(info)
        +changeGenderCommandHandler(info)
        +changeTimeInfoCommand(info)
        +changeDOBCommandHandler(info)
        +changeDodIdCommandHandler(info)
        +changeWeightCommandHandler(info)
        +changeHeightCommandHandler(info)
        +addRemoveAllergyCommandHandler(info)
        +addRemoveMedicationCommandHandler(info)
        +changePatientMaskingHandler(info)
        +changeCiteNumberCommandHandler(info)
        +changePatientIdCommandHandler(info)
        +changeDispositionCommandHandler(info)
        +changeNationalityCommandHandler(info)
        +updateImmunizationCommandHandler(info)
        +updateProceduresCommandHandler(info)
        +updateProblemsCommandHandler(info)
    }

    companion object{
        fun EventCommandHandler.includeInfoEvents(info: Info) = apply {
            +changeNameEventHandler()
            +changeBattleRosterNumberEventHandler()
            +changeAliasEventHandler()
            +changeUnitEventHandler()
            +changeUnitPhoneNumberEventHandler()
            +changeRankEventHandler()
            +changeGradeEventHandler()
            +changeServiceEventHandler()
            +changePatcatEventHandler()
            +changeSsnEventHandler()
            +changeTattooEventHandler()
            +changeEvacEventHandler()
            +changeTriageEventHandler()
            +changeEvacTypeEventHandler()
            +changeBloodTypeEventHandler()
            +changeGenderEventHandler()
            +changeInjuryTimeEventHandler()
            +changeDobEventHandler()
            +changeDodIdEventHandler()
            +changeWeightEventHandler()
            +changeHeightEventHandler()
            +addRemoveAllergyEventHandler()
            +addRemoveMedicationEventHandler()
            +changeOpenCloseEncounterEventHandler()
            +changeHandoffEventHandler()
            +changePatientMaskingEventHandler()
            +changeCiteNumberEventHandler()
            +changeDispositionEventHandler()
            +changeNationalityCommandEventHandler()
            +updateImmunizationCommandEventHandler()
            +updateProcedureCommandEventHandler()
            +updateProblemsCommandEventHandler(info)
        }
    }
}

//Region Event Helpers

fun heightToString(height: Float?): String?{
    val h = height ?: return null
    var ft = ((h * CM_TO_IN) / 12).toInt()
    var inch = ((h * CM_TO_IN) % 12)
    //If rounding the inches for inches would end up displaying 12, add 1 to the feet.
    if(inch + .001 > 12){
        ft += 1
        inch = 0f
    }
    return "%d' %.2f\" (%.2f cm)".format(ft, inch, h)
}

fun weightToString(weight: Float?): String?{
    val w = weight ?: return null
    val lbs = (w * KG_TO_LB)
    return "%.2f lbs (%.2f kg)".format(lbs, w)
}

val CM_TO_IN = 0.39370079f
val KG_TO_LB = 2.20462f
//endregion