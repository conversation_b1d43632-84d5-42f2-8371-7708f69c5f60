package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.CommandHandler
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.commands.handleChecklistCommand
import gov.afrl.batdok.encounter.commands.handleChecklistEventCommand
import java.io.Serializable
import java.time.Instant

data class ChecklistItem
@Deprecated(
    message = "Use intervalTime constructor",
    replaceWith = ReplaceWith("ChecklistItem(message, Interval(interval), timestamp, checked)")
)
constructor (
    val message: String,
    @Deprecated(
        message ="Use intervalTime",
        replaceWith = ReplaceWith("intervalTime")
    )
    val interval: String,
    val timestamp: Instant = Instant.now(),
    val checked: Boolean = true
): Serializable{
    val intervalTime: Interval = Interval(interval)

    constructor(
        message: String,
        intervalTime: Interval,
        timestamp: Instant = Instant.now(),
        checked: Boolean = true
    ) : this (message, intervalTime.toString(), timestamp, checked)

    constructor(
        knownItem: KnownChecklistItems,
        timestamp: Instant = Instant.now(),
        checked: Boolean = true
    ): this(
        knownItem.dataString,
        knownItem.defaultInterval,
        timestamp,
        checked
    )
}

class Checklist(checklistItems: List<ChecklistItem> = listOf()): Serializable {
    var checklistItems = checklistItems
        internal set

    val size: Int
        get() = checklistItems.size

    operator fun plusAssign(checklistItem: ChecklistItem){
        checklistItems = checklistItems.filter { it.message != checklistItem.message } + checklistItem
    }
    operator fun get(index: Int) = checklistItems[index]

    @Transient val handlers = CommandHandler().apply {
        +handleChecklistCommand(this@Checklist)
    }

    companion object {
        fun EventCommandHandler.includeChecklistEvents(){
            +handleChecklistEventCommand()
        }
    }
}