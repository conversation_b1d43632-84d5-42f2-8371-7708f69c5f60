package gov.afrl.batdok.encounter.treatment

import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.TreatmentCommands
import gov.afrl.batdok.commands.proto.pelvicBinderData
import gov.afrl.batdok.util.compatibleEnum
import gov.afrl.batdok.util.toPrimitive

data class PelvicBinderData(val type: String? = null): TreatmentData {
    constructor(data: TreatmentCommands.PelvicBinderData): this(data.type.toPrimitive())
    override fun toProtobuf(): Message {
        return pelvicBinderData {
            <EMAIL>?.let {
                type = compatibleEnum(<EMAIL>)
            }
        }
    }

    override fun toDetailString() = type ?: ""

}