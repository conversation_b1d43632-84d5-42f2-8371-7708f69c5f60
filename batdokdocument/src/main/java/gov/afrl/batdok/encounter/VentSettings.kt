package gov.afrl.batdok.encounter

import com.google.protobuf.bytesValue
import gov.afrl.batdok.commands.CommandHandler
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.commands.proto.VentCommands.Vent
import gov.afrl.batdok.commands.proto.vent
import gov.afrl.batdok.commands.proto.vitalIdOrNull
import gov.afrl.batdok.encounter.commands.*
import gov.afrl.batdok.encounter.ids.*
import gov.afrl.batdok.encounter.vitals.EtCO2
import gov.afrl.batdok.encounter.vitals.PIP
import gov.afrl.batdok.util.Patterns
import gov.afrl.batdok.util.compatibleEnum
import gov.afrl.batdok.util.format
import gov.afrl.batdok.util.nullableDoubleValue
import gov.afrl.batdok.util.nullableInt32Value
import gov.afrl.batdok.util.toPrimitive
import mil.af.afrl.batman.batdokid.DomainId
import java.io.Serializable
import java.time.Instant

class VentValues(val vitals: Vitals = Vitals()): Serializable {
    var ventSettingsList: List<VentSettings> = listOf()
        internal set


    operator fun plusAssign(ventSettings: VentSettings){
        ventSettingsList = ventSettingsList.filter { ventSettings.id != it.id } + ventSettings
    }
    operator fun minusAssign(id: VentId){
        ventSettingsList = ventSettingsList.filter { id != it.id }
    }
    operator fun get(domainId: VentId): VentSettings? = ventSettingsList.find { it.id == domainId }

    @Transient val handlers = CommandHandler().apply {
        val ventValues = this@VentValues
        +logVentCommandHandler(ventValues, vitals)
        +updateVentCommandHandler(ventValues, vitals)
        +removeVentCommandHandler(ventValues)
    }

    companion object{
        fun EventCommandHandler.includeVentEvents(ventValues: VentValues, vitals: Vitals) = apply{
            +logVentEventCommandHandler(vitals)
            +updateVentEventCommandHandler(ventValues, vitals)
            +removeVentEventCommandHandler(ventValues)
        }
    }
}

data class VentSettings(
    val id: VentId,
    val time: Instant? = Instant.now(),
    val mode: String? = null,
    val fio2: Double? = null,
    val rate: Int? = null,
    val tv: Int? = null,
    val peep: Double? = null,
    val ventilator: String? = null,
    val associatedVital: EncounterVital? = null,
    val documentId: DocumentId = DomainId.nil()
){
    constructor(id: VentId, documentId: DocumentId, time: Instant, vent: Vent, vitals: Vitals): this(
        id,
        time,
        vent.mode.toPrimitive(),
        vent.fio2.toPrimitive(),
        vent.rate.toPrimitive(),
        vent.tv.toPrimitive(),
        vent.peep.toPrimitive(),
        vent.ventilator.toPrimitive(),
        vent.vitalIdOrNull?.value?.toDomainId<EncounterVitalId>()?.let { vitals[it] },
        documentId
    )

    fun toProto() = vent {
        val setting = this@VentSettings
        this.mode = compatibleEnum(setting.mode)
        this.fio2 = nullableDoubleValue(setting.fio2)
        this.rate = nullableInt32Value(setting.rate)
        this.tv = nullableInt32Value(setting.tv)
        this.peep = nullableDoubleValue(setting.peep)
        this.ventilator = compatibleEnum(setting.ventilator)
        setting.associatedVital?.vitalId?.let { this.vitalId = bytesValue { value = it.toByteString() } }

    }

    fun toEvent(): String {
        val fieldString = listOfNotNull(
            ventilator?.let { "Type: $it" },
            mode?.let { "Mode: $it" },
            rate?.let { "Rate: $it" },
            tv?.let { "TV: $it" },
            peep?.let { "PEEP: $it" },
            fio2?.let { "FiO2: $it" },
            associatedVital?.get<EtCO2>()?.etco2?.let { "EtCO2: $it" },
            associatedVital?.get<PIP>()?.pip?.let { "PIP: $it" }
        ).joinToString(", ")
        return "Vent Settings - " +
            if(fieldString.isEmpty()) "Time: ${time?.format(Patterns.hm_24_colon)}"
            else "Time: ${time?.format(Patterns.hm_24_colon)}, $fieldString"
    }
}