package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

enum class Disposition(override val protoIndex: Int, override val dataString: String): ProtoEnum {
    DECEASED(1, "Deceased"),
    PROVIDER_NOW(2, "CAT I: Provider Now"),
    AEM_NOW(3, "CAT II: AEM Now"),
    MINOR_CARE_PROTOCOL(4, "CAT III: Minor Care Protocol"),
    SPECIALTY_REFERRAL(5, "CAT IV: Specialty Referral"),
    ;

    companion object{
        fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)
    }
}