package gov.afrl.batdok.encounter.metadata

import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.encounter.Moi
import gov.afrl.batdok.util.*

enum class OrthopedicType(
    override val protoIndex: Int,
    override val dataString: String,
    subTypes: List<OrthopedicSubtype> = listOf(),
    locations: List<OrthopedicLocation> = listOf())
: ProtoEnum {
    SPLINT(1, "Splint"),
    CAST(2, "Cast", subTypes = OrthopedicSubtype.castSubtypes, locations = OrthopedicLocation.castLocations),
    CANE(3, "Cane"),
    CRUTCHES(4, "Crutches"),
    HALO(5, "Halo"),
    STRYKER_FRAME(6, "Stryker Frame"),
    WHEELCHAIR(7, "Wheelchair", subTypes = OrthopedicSubtype.wheelchairSubtypes),
    BRACE(8, "Brace", locations = OrthopedicLocation.braceLocations),
    EXTERNAL_FIXATOR(9, "External Fixator");

    companion object {
        fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)
    }
}