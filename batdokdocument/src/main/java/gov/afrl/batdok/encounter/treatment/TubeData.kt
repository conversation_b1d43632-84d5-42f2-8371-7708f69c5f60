package gov.afrl.batdok.encounter.treatment

import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.*
import gov.afrl.batdok.commands.proto.TreatmentCommands.Tube
import gov.afrl.batdok.util.*

class TubeData(val breathingConfirmation: String? = null,
               val size: Float? = null,
               val sizeUnit: String? = null,
               val type: String? = null,
               val depth: Int? = null,
               val depthUnit: String? = null,
               val location: String? = null): TreatmentData{
    override fun toProtobuf(): Message = tube {
        size = nullableFloatValue(<EMAIL>)
        depth = nullableInt32Value(<EMAIL>)
        location = Location.fromString(<EMAIL>)
        type = compatibleEnum(<EMAIL>)
        breathingConfirmation = BreathingConfirmation.fromString(<EMAIL>)
        sizeUnit = Unit.fromString(<EMAIL>)
        depthUnit = Unit.fromString(<EMAIL>)
    }

    override fun toDetailString(): String {
        return listOfNotNull(
            type.toDetailString("Type"),
            location.toDetailString("Location"),
            breathingConfirmation.toDetailString("Confirmation"),
            depth.toDetailString("Depth", depthUnit),
            size.toDetailString("Size", sizeUnit)
        ).joinToString(", ")
    }

    companion object{
        fun fromProtobuf(tube: Tube): TubeData{
            return TubeData(
                BreathingConfirmation.fromProto(tube.breathingConfirmation),
                tube.sizeOrNull?.toPrimitive(),
                Unit.fromProto(tube.sizeUnit),
                tube.typeOrNull?.toPrimitive(),
                tube.depthOrNull?.toPrimitive(),
                Unit.fromProto(tube.depthUnit),
                Location.fromProto(tube.location)
            )
        }
    }
    enum class BreathingConfirmation(override val protoIndex: Int, override val dataString: String):
        ProtoEnum {
        ETCO2(1, "EtCO2"),
        BREATH_SOUNDS(2, "Breath Sounds"),
        VISUAL(3, "Visual"),
        ;

        companion object{
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }

    enum class Location(override val protoIndex: Int, override val dataString: String): ProtoEnum {
        GUMS(1, "Gums"),
        TEETH(2, "Teeth"),
        NARE(3, "Nare"),
        ;

        companion object{
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }

    enum class Unit(override val protoIndex: Int, override val dataString: String): ProtoEnum {
        CM(1, "cm"),
        MM(2, "mm"),
        FRENCH(3, "Fr"),
        ;

        companion object{
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }
}

