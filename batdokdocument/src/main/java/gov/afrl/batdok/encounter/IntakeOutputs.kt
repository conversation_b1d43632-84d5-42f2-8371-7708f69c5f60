package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.CommandHandler
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.SharedProtobufObjects.CompatibleEnum
import gov.afrl.batdok.commands.proto.StratevacRnIoCommands
import gov.afrl.batdok.commands.proto.StratevacRnIoCommands.IOItem
import gov.afrl.batdok.commands.proto.iOItem
import gov.afrl.batdok.commands.proto.intakeOutputItem
import gov.afrl.batdok.commands.proto.isIntakeOrNull
import gov.afrl.batdok.commands.proto.labelOrNull
import gov.afrl.batdok.commands.proto.volumeOrNull
import gov.afrl.batdok.encounter.commands.*
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.encounter.ids.InputOutputId
import gov.afrl.batdok.encounter.ids.IntakeOutputId
import gov.afrl.batdok.util.ILinkableList
import gov.afrl.batdok.util.LinkableList
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.SortedLinkableList
import gov.afrl.batdok.util.Timestamped
import gov.afrl.batdok.util.booleanValue
import gov.afrl.batdok.util.doubleValue
import gov.afrl.batdok.util.int64Value
import gov.afrl.batdok.util.nullableStringValue
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto
import gov.afrl.batdok.util.toPrimitive
import mil.af.afrl.batman.batdokid.DomainId
import java.io.Serializable
import java.time.Instant

class IntakeOutputs: Serializable, ILinkableList<IntakeOutput> by SortedLinkableList() {
    val inputs : List<IntakeOutput>
        get() = list.filter { it.isIntake }

    val outputs : List<IntakeOutput>
        get() = list.filter { !it.isIntake }


    @Transient val handlers = CommandHandler().apply {
        val ios = this@IntakeOutputs
        +addIntakeOutputItemCommandHandler(ios)
        +updateIntakeOutputItemCommandHandler(ios)
        +removeIntakeOutputItemCommandHandler(ios)
    }

    companion object{
        fun EventCommandHandler.includeIntakeOutputEvents(inputOutputs: IntakeOutputs) = apply {
            +addIntakeOutputItemEventCommandHandler()
            +updateIntakeOutputItemEventCommandHandler(inputOutputs)
            +removeIntakeOutputItemEventCommandHandler(inputOutputs)
        }
    }
}

data class IntakeOutput(
    override val id: IntakeOutputId,
    override val timestamp: Instant,
    val type: String,
    val isIntake: Boolean,
    val label: String?,
    val volume: Double,
    val unit: String,
    val documentId: DocumentId = DomainId.nil()
): Serializable, Linkable, Timestamped{
    internal constructor(id: IntakeOutputId, documentId: DocumentId, timestamp: Instant, data: StratevacRnIoCommands.IntakeOutputItem): this(
        id,
        timestamp,
        IntakeOutputType.fromProto(data.type) ?: "",
        data.isIntakeOrNull?.value ?: true,
        data.labelOrNull?.toPrimitive(),
        data.volumeOrNull?.value ?: 0.0,
        IntakeOutputUnit.fromProto(data.unit) ?: "",
        documentId
    )
    fun toProto() = intakeOutputItem{
        this.timestamp = int64Value(<EMAIL>)
        this.type = IntakeOutputType.fromString(<EMAIL>)
        this.volume = doubleValue(<EMAIL>)
        this.unit = IntakeOutputUnit.fromString(<EMAIL>)
        this.label = nullableStringValue(<EMAIL>)
        this.isIntake = booleanValue(<EMAIL>)
    }
    fun toEventString() = listOf(type, volume.toString(), unit, label)
        .filter { !it.isNullOrBlank() }
        .joinToString(" ")
}

enum class IntakeOutputType(override val protoIndex: Int, override val dataString: String, val isIntake: Boolean): ProtoEnum{
    ORAL(1, "Oral Fluid", true),
    PARENTERAL(2, "Parenteral Fluid", true),
    URINE(3, "Urine", false),
    DRAINAGE_CATHETER(4, "Drainage Catheter", false),
    EMESIS(5, "Emesis", false)
    ;

    companion object{
        fun fromProto(enum: CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)
    }
}

enum class IntakeOutputUnit(override val protoIndex: Int, override val dataString: String, val conversionFactor: Double): ProtoEnum{
    MILLILITER(1, "mL", 1.0),
    CC(2, "cc", 1.0),
    LITER(3, "L", 1000.0),
    ;

    companion object{
        fun fromProto(enum: CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)
    }
}