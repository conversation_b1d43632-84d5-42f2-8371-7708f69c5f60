package gov.afrl.batdok.encounter.treatment

import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.TreatmentCommands
import gov.afrl.batdok.commands.proto.foreignBodyRemoval
import gov.afrl.batdok.util.compatibleEnum
import gov.afrl.batdok.util.toPrimitive

class ForeignBodyRemovalData(val location: String? = null): TreatmentData{
    override fun toProtobuf(): Message = foreignBodyRemoval{
        location = compatibleEnum(<EMAIL>)
    }

    override fun toDetailString(): String {
        return location ?: ""
    }

    companion object{
        fun fromProtobuf(foreignBodyRemoval: TreatmentCommands.ForeignBodyRemoval): ForeignBodyRemovalData{
            return ForeignBodyRemovalData(foreignBodyRemoval.location.toPrimitive())
        }
    }
}