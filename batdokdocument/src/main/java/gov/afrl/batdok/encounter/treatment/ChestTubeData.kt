package gov.afrl.batdok.encounter.treatment

import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.*
import gov.afrl.batdok.util.*

class ChestTubeData(
    val location: String? = null,
    val suction: Boolean? = null,
    val suctionAmount: Float? = null,
    val suctionUnit: String? = null,
    val airLeak: Boolean? = null
): TreatmentData{
    override fun toProtobuf(): Message = chestTube{
        location = Location.fromString(<EMAIL>)
        this.suction = nullableBoolValue(<EMAIL>)
        this.suctionAmount = nullableFloatValue(<EMAIL>)
        this.airLeak = nullableBoolValue(<EMAIL>)
        this.suctionUnit = compatibleEnum(<EMAIL>)
    }

    override fun toDetailString(): String {
        return listOfNotNull(
            location.toDetailString("Location"),
            suction.toDetailString("Suction"),
            suctionAmount?.toString().toDetailString("Amount", suctionUnit),
            airLeak.toDetailString("Air Leak")
        ).joinToString(", ")
    }

    companion object{
        fun fromProtobuf(chestTube: TreatmentCommands.ChestTube): ChestTubeData{
            return ChestTubeData(
                chestTube.locationOrNull?.let { Location.fromProto(it) },
                chestTube.suctionOrNull?.toPrimitive(),
                chestTube.suctionAmountOrNull?.toPrimitive(),
                chestTube.suctionUnitOrNull?.toPrimitive(),
                chestTube.airLeakOrNull?.toPrimitive(),
            )
        }
    }

    enum class Location(override val protoIndex: Int, override val dataString: String): ProtoEnum {
        LEFT(1, "Left side"),
        RIGHT(2, "Right side"),
        ;

        companion object{
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }
}