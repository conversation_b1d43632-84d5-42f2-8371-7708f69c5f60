package gov.afrl.batdok.encounter.treatment

import gov.afrl.batdok.commands.proto.TreatmentCommands
import gov.afrl.batdok.commands.proto.inboundHL7Data

class InboundHL7Data(private val treatmentDetails: String) : TreatmentData {
    override fun toProtobuf() = inboundHL7Data {
        treatmentDetails = <EMAIL>
    }

    override fun toDetailString() = treatmentDetails

    companion object {
        fun fromProtobuf(inboundHL7Treatment: TreatmentCommands.InboundHL7Data) : InboundHL7Data {
            return InboundHL7Data(inboundHL7Treatment.treatmentDetails)
        }
    }
}
