### Basic structure for commands functions

- buildXCommand
  - Basic builder for the commmand
  - Pass in parameters needed to make the protobuf command
  - Return the protobuf command
  - Example:
    ```
    fun buildChangeNameCommand(name: String) = changeNameCommand{ this.name = name }
    ```
- XHandler
  - Basic handler for the command
  - Pass in the object being edited by the event
  - Create `handler<X>{ }` where inside the function you update the object
  - Example:
    ```
    fun changeNameHandler(info: Info) = handler<ChangeNameCommand>{ info.name = name }
    ```
- XEventHandler
  - Event Builder for the command
  - No parameters needed
  - Build an `eventHandler<X>{}` that will return the string event for the action
  - Example:
    ```
    fun changeNameEventHandler() = eventHandler<ChangeNameCommand>{ "Set name to $name" }
    ```
  - If needed, you can also provide an `idGenerator` parameter to use a different ID. By default, it uses the commandID.
  - Example:
    ```
    fun updateVitalEventHandler() = eventHandler<UpdateVitalCommand>(
        idGenerator = { vitalId.toDomainId() },
        handler = { "Log Vital" }
    )
    ```
- Special Cases:
  - Vital Editing only passes the changed vitals, so you will need to pass in the vital object to build its event
    - I think it might be best to have both eventHandlers, so that if you aren't keeping track of the vitals, you can still get an event explaining what happened.
    - Example:
      ```
      fun updateVitalEventHandler() = eventHandler<UpdateVitalCommand>(
          handler = { "Updated Vital @ <Time>: HR = X, BP = Y/Z ..." }
      )
      fun updateVitalEventHandler(vitals: Vitals) = eventHandler<UpdateVitalCommand>(
          idGenerator = { vitalId.toDomainId() },
          handler = {
              val existingVital = vitals.find { it.id == vitalId }
              //Update Existing Vital to get what the full new vital is
              "Log Vital: HR = X, SpO2 = A, Resp=B, BP = Y/Z"
          }
      )
      ```