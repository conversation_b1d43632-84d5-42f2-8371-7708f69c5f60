package gov.afrl.batdok.encounter.observation

import gov.afrl.batdok.commands.proto.Observations
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.rhythmData
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

class RhythmData(val type: String? = null): ObservationData {
    internal constructor(data: Observations.RhythmData): this(Type.fromProto(data.type))
    enum class Type(override val protoIndex: Int, override val dataString: String): ProtoEnum {
        @Deprecated("Changed to match CDP") REGULAR(1, "Regular"),
        @Deprecated("Changed to match CDP") IRREGULAR(2, "Irregular"),
        @Deprecated("Changed to match CDP") TACHY(3, "Tachy"),
        @Deprecated("Changed to match CDP") MURMURS(4, "Murmurs"),
        @Deprecated("Changed to match CDP") GALLOPS(5, "Gallops"),
        @Deprecated("Changed to match CDP") RUBS(6, "Rubs"),
        NSR(7, "NSR"),
        ST(8, "ST"),
        SB(9, "SB"),
        PEA(10, "PEA"),
        PACED(11, "Paced"),
        ASYSTOLE(12, "Asystole"),
        AFIB(13,"A-FIB"),
        AFLUT(14,"A-FLUT"),
        SVT(15,"SVT"),
        VT(16, "VT"),
        VF(17, "VF");

        companion object {
            fun allDiaplayStrings(): List<String>{
                return entries.map { it.dataString }
            }
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = entries.toTypedArray().protoToString(enum)
            fun fromString(string: String?) = entries.toTypedArray().stringToProto(string)
        }
    }

    override fun toProtobuf() = rhythmData {
        this.type = Type.fromString(<EMAIL>)
    }

    override fun toDetailString(): String {
        return this.type ?: ""
    }

}