package gov.afrl.batdok.encounter.treatment

import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.TreatmentCommands.NeedleD
import gov.afrl.batdok.commands.proto.needleD
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

class NeedleDData(val location: String? = null): TreatmentData{
    override fun toProtobuf(): Message = needleD{
        location = Location.fromString(<EMAIL>)
    }

    override fun toDetailString(): String {
        return location ?: ""
    }

    companion object{
        fun fromProtobuf(needleD: NeedleD): NeedleDData{
            return NeedleDData(Location.fromProto(needleD.location)?:"")
        }
    }

    enum class Location(override val protoIndex: Int, override val dataString: String): ProtoEnum {
        L_MID_CLAV(1, "L Mid-Clav"),
        R_MID_CLAV(2, "R Mid-Clav"),
        L_ANT_AX(3, "L Ant-Ax"),
        R_ANT_AX(4, "R Ant-Ax"),
        ;

        companion object{
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }
}