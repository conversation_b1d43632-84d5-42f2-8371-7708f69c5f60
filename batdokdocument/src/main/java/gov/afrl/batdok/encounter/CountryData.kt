package gov.afrl.batdok.encounter


import gov.afrl.batdok.commands.proto.SharedProtobufObjects.CompatibleEnum
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto
import gov.afrl.batdok.util.toChangeOrClearEvent

enum class CountryData(
    override val protoIndex: Int,
    val globalName: String,
    val regionCode: String,
    val regionName: String,
    val subRegionCode: String,
    val subRegionName: String,
    val intermediateRegionCode: String?,
    val intermediateRegionName: String?,
    override val dataString: String,
    val m49Code: String,
    val isoAlpha2Code: String,
    val abbreviation: String = dataString,
    val ldc: Boolean = false,
    val lldc: Boolean = false,
    val sids: Boolean = false
) : ProtoEnum {
    ALGERIA(
        1,
        "World",
        "002",
        "Africa",
        "015",
        "Northern Africa",
        null,
        null,
        "Algeria",
        "012",
        "DZ",
        "DZA"
    ),
    EGYPT(
        2,
        "World",
        "002",
        "Africa",
        "015",
        "Northern Africa",
        null,
        null,
        "Egypt",
        "818",
        "EG",
        "EGY"
    ),
    LIBYA(
        3,
        "World",
        "002",
        "Africa",
        "015",
        "Northern Africa",
        null,
        null,
        "Libya",
        "434",
        "LY",
        "LBY"
    ),
    MOROCCO(
        4,
        "World",
        "002",
        "Africa",
        "015",
        "Northern Africa",
        null,
        null,
        "Morocco",
        "504",
        "MA",
        "MAR"
    ),
    SUDAN(
        5,
        "World",
        "002",
        "Africa",
        "015",
        "Northern Africa",
        null,
        null,
        "Sudan",
        "729",
        "SD",
        "SDN",
        true
    ),
    TUNISIA(
        6,
        "World",
        "002",
        "Africa",
        "015",
        "Northern Africa",
        null,
        null,
        "Tunisia",
        "788",
        "TN",
        "TUN"
    ),
    WESTERN_SAHARA(
        7,
        "World",
        "002",
        "Africa",
        "015",
        "Northern Africa",
        null,
        null,
        "Western Sahara",
        "732",
        "EH",
        "ESH"
    ),
    BRITISH_INDIAN_OCEAN_TERRITORY(
        8,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "014",
        "Eastern Africa",
        "British Indian Ocean Territory",
        "086",
        "IO",
        "IOT"
    ),
    BURUNDI(
        9,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "014",
        "Eastern Africa",
        "Burundi",
        "108",
        "BI",
        "BDI",
        true,
        true
    ),
    COMOROS(
        10,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "014",
        "Eastern Africa",
        "Comoros",
        "174",
        "KM",
        "COM",
        true
    ),
    DJIBOUTI(
        11,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "014",
        "Eastern Africa",
        "Djibouti",
        "262",
        "DJ",
        "DJI"
    ),
    ERITREA(
        12,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "014",
        "Eastern Africa",
        "Eritrea",
        "232",
        "ER",
        "ERI"
    ),
    ETHIOPIA(
        13,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "014",
        "Eastern Africa",
        "Ethiopia",
        "231",
        "ET",
        "ETH",
        true,
        true
    ),
    FRENCH_SOUTHERN_TERRITORIES(
        14,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "014",
        "Eastern Africa",
        "French Southern Territories",
        "260",
        "TF",
        "ATF"
    ),
    KENYA(
        15,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "014",
        "Eastern Africa",
        "Kenya",
        "404",
        "KE",
        "KEN"
    ),
    MADAGASCAR(
        16,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "014",
        "Eastern Africa",
        "Madagascar",
        "450",
        "MG",
        "MDG"
    ),
    MALAWI(
        17,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "014",
        "Eastern Africa",
        "Malawi",
        "454",
        "MW",
        "MWI",
        true,
        true
    ),
    MAURITIUS(
        18,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "014",
        "Eastern Africa",
        "Mauritius",
        "480",
        "MU",
        "MUS",
        sids = true
    ),
    MAYOTTE(
        19,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "014",
        "Eastern Africa",
        "Mayotte",
        "175",
        "YT",
        "MYT"
    ),
    MOZAMBIQUE(
        20,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "014",
        "Eastern Africa",
        "Mozambique",
        "508",
        "MZ",
        "MOZ"
    ),
    REUNION(
        21,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "014",
        "Eastern Africa",
        "Reunion",
        "638",
        "RE",
        "REU"
    ),
    RWANDA(
        22,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "014",
        "Eastern Africa",
        "Rwanda",
        "646",
        "RW",
        "RWA",
        true,
        true
    ),
    SEYCHELLES(
        23,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "014",
        "Eastern Africa",
        "Seychelles",
        "690",
        "SC",
        "SYC",
        sids = true
    ),
    SOMALIA(
        24,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "014",
        "Eastern Africa",
        "Somalia",
        "706",
        "SO",
        "SOM"
    ),
    SOUTH_SUDAN(
        25,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "014",
        "Eastern Africa",
        "South Sudan",
        "728",
        "SS",
        "SSD",
        true,
        true
    ),
    UGANDA(
        26,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "014",
        "Eastern Africa",
        "Uganda",
        "800",
        "UG",
        "UGA"
    ),
    UNITED_REPUBLIC_OF_TANZANIA(
        27,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "014",
        "Eastern Africa",
        "United Republic of Tanzania",
        "834",
        "TZ",
        "TZA"
    ),
    ZAMBIA(
        28,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "014",
        "Eastern Africa",
        "Zambia",
        "894",
        "ZM",
        "ZMB"
    ),
    ZIMBABWE(
        29,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "014",
        "Eastern Africa",
        "Zimbabwe",
        "716",
        "ZW",
        "ZWE"
    ),
    ANGOLA(
        30,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "017",
        "Middle Africa",
        "Angola",
        "024",
        "AO",
        "AGO"
    ),
    CAMEROON(
        31,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "017",
        "Middle Africa",
        "Cameroon",
        "120",
        "CM",
        "CMR"
    ),
    CENTRAL_AFRICAN_REPUBLIC(
        32,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "017",
        "Middle Africa",
        "Central African Republic",
        "140",
        "CF",
        "CAF",
        true,
        true
    ),
    CHAD(
        33,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "017",
        "Middle Africa",
        "Chad",
        "148",
        "TD",
        "TCD"
    ),
    CONGO(
        34,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "017",
        "Middle Africa",
        "Congo",
        "178",
        "CG",
        "COG"
    ),
    DEMOCRATIC_REPUBLIC_OF_THE_CONGO(
        35,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "017",
        "Middle Africa",
        "Democratic Republic of the Congo",
        "180",
        "CD",
        "COD",
        true
    ),
    EQUATORIAL_GUINEA(
        36,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "017",
        "Middle Africa",
        "Equatorial Guinea",
        "226",
        "GQ",
        "GNQ"
    ),
    GABON(
        37,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "017",
        "Middle Africa",
        "Gabon",
        "266",
        "GA",
        "GAB"
    ),
    SAO_TOME_AND_PRINCIPE(
        38,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "017",
        "Middle Africa",
        "Sao Tome and Principe",
        "678",
        "ST",
        "STP",
        true
    ),
    BOTSWANA(
        39,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "018",
        "Southern Africa",
        "Botswana",
        "072",
        "BW",
        "BWA"
    ),
    ESWATINI(
        40,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "018",
        "Southern Africa",
        "Eswatini",
        "748",
        "SZ",
        "SWZ",
        ldc = true
    ),
    LESOTHO(
        41,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "018",
        "Southern Africa",
        "Lesotho",
        "426",
        "LS",
        "LSO",
        ldc = true
    ),
    NAMIBIA(
        42,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "018",
        "Southern Africa",
        "Namibia",
        "516",
        "NA",
        "NAM"
    ),
    SOUTH_AFRICA(
        43,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "018",
        "Southern Africa",
        "South Africa",
        "710",
        "ZA",
        "ZAF"
    ),
    SWAZILAND(
        44,
        "World",
        "002",
        "Africa",
        "202",
        "Sub-Saharan Africa",
        "018",
        "Southern Africa",
        "Swaziland",
        "748",
        "SZ",
        "SWZ"
    ),
    BENIN(
        45,
        "World",
        "002",
        "Africa",
        "011",
        "Western Africa",
        null,
        null,
        "Benin",
        "204",
        "BJ",
        "BEN",
        true
    ),
    BURKINA_FASO(
        46,
        "World",
        "002",
        "Africa",
        "011",
        "Western Africa",
        null,
        null,
        "Burkina Faso",
        "854",
        "BF",
        "BFA",
        true,
        true
    ),
    CABO_VERDE(
        47,
        "World",
        "002",
        "Africa",
        "011",
        "Western Africa",
        null,
        null,
        "Cabo Verde",
        "132",
        "CV",
        "CPV",
        true
    ),
    COTE_DIVOIRE(
        48,
        "World",
        "002",
        "Africa",
        "011",
        "Western Africa",
        null,
        null,
        "Cote d'Ivoire",
        "384",
        "CI",
        "CIV"
    ),
    GAMBIA(
        49,
        "World",
        "002",
        "Africa",
        "011",
        "Western Africa",
        null,
        null,
        "Gambia",
        "270",
        "GM",
        "GMB",
        true
    ),
    GHANA(
        50,
        "World",
        "002",
        "Africa",
        "011",
        "Western Africa",
        null,
        null,
        "Ghana",
        "288",
        "GH",
        "GHA"
    ),
    GUINEA(
        51,
        "World",
        "002",
        "Africa",
        "011",
        "Western Africa",
        null,
        null,
        "Guinea",
        "324",
        "GN",
        "GIN"
    ),
    GUINEA_BISSAU(
        52,
        "World",
        "002",
        "Africa",
        "011",
        "Western Africa",
        null,
        null,
        "Guinea-Bissau",
        "624",
        "GW",
        "GNB",
        true
    ),
    LIBERIA(
        53,
        "World",
        "002",
        "Africa",
        "011",
        "Western Africa",
        null,
        null,
        "Liberia",
        "430",
        "LR",
        "LBR"
    ),
    MALI(
        54,
        "World",
        "002",
        "Africa",
        "011",
        "Western Africa",
        null,
        null,
        "Mali",
        "466",
        "ML",
        "MLI",
        true,
        true
    ),
    MAURITANIA(
        55,
        "World",
        "002",
        "Africa",
        "011",
        "Western Africa",
        null,
        null,
        "Mauritania",
        "478",
        "MR",
        "MRT"
    ),
    NIGER(
        56,
        "World",
        "002",
        "Africa",
        "011",
        "Western Africa",
        null,
        null,
        "Niger",
        "562",
        "NE",
        "NER",
        true,
        true
    ),
    NIGERIA(
        57,
        "World",
        "002",
        "Africa",
        "011",
        "Western Africa",
        null,
        null,
        "Nigeria",
        "566",
        "NG",
        "NGA"
    ),
    SAINT_HELENA_ASCENSION_AND_TRISTAN_DA_CUNHA(
        58,
        "World",
        "002",
        "Africa",
        "011",
        "Western Africa",
        null,
        null,
        "Saint Helena, Ascension and Tristan da Cunha",
        "654",
        "SH",
        "SHN"
    ),
    SENEGAL(
        59,
        "World",
        "002",
        "Africa",
        "011",
        "Western Africa",
        null,
        null,
        "Senegal",
        "686",
        "SN",
        "SEN"
    ),
    SIERRA_LEONE(
        60,
        "World",
        "002",
        "Africa",
        "011",
        "Western Africa",
        null,
        null,
        "Sierra Leone",
        "694",
        "SL",
        "SLE"
    ),
    TOGO(
        61,
        "World",
        "002",
        "Africa",
        "011",
        "Western Africa",
        null,
        null,
        "Togo",
        "768",
        "TG",
        "TGO"
    ),
    ANGUILLA(
        62,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "029",
        "Caribbean",
        "Anguilla",
        "660",
        "AI",
        "AIA"
    ),
    ANTIGUA_AND_BARBUDA(
        63,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "029",
        "Caribbean",
        "Antigua and Barbuda",
        "028",
        "AG",
        "ATG"
    ),
    ARUBA(
        64,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "029",
        "Caribbean",
        "Aruba",
        "533",
        "AW",
        "ABW"
    ),
    BAHAMAS(
        65,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "029",
        "Caribbean",
        "Bahamas",
        "044",
        "BS",
        "BHS"
    ),
    BARBADOS(
        66,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "029",
        "Caribbean",
        "Barbados",
        "052",
        "BB",
        "BRB"
    ),
    BONAIRE_SINT_EUSTATIUS_AND_SABA(
        67,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "029",
        "Caribbean",
        "Bonaire, Sint Eustatius and Saba",
        "535",
        "BQ",
        "BES"
    ),
    BRITISH_VIRGIN_ISLANDS(
        68,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "029",
        "Caribbean",
        "British Virgin Islands",
        "092",
        "VG",
        "VGB"
    ),
    CAYMAN_ISLANDS(
        69,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "029",
        "Caribbean",
        "Cayman Islands",
        "136",
        "KY",
        "CYM"
    ),
    CUBA(
        70,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "029",
        "Caribbean",
        "Cuba",
        "192",
        "CU",
        "CUB"
    ),
    CURACAO(
        71,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "029",
        "Caribbean",
        "Curacao",
        "531",
        "CW",
        "CUW"
    ),
    DOMINICA(
        72,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "029",
        "Caribbean",
        "Dominica",
        "212",
        "DM",
        "DMA"
    ),
    DOMINICAN_REPUBLIC(
        73,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "029",
        "Caribbean",
        "Dominican Republic",
        "214",
        "DO",
        "DOM"
    ),
    GRENADA(
        74,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "029",
        "Caribbean",
        "Grenada",
        "308",
        "GD",
        "GRD"
    ),
    GUADELOUPE(
        75,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "029",
        "Caribbean",
        "Guadeloupe",
        "312",
        "GP",
        "GLP"
    ),
    HAITI(
        76,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "029",
        "Caribbean",
        "Haiti",
        "332",
        "HT",
        "HTI"
    ),
    JAMAICA(
        77,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "029",
        "Caribbean",
        "Jamaica",
        "388",
        "JM",
        "JAM"
    ),
    MARTINIQUE(
        78,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "029",
        "Caribbean",
        "Martinique",
        "474",
        "MQ",
        "MTQ"
    ),
    MONTSERRAT(
        79,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "029",
        "Caribbean",
        "Montserrat",
        "500",
        "MS",
        "MSR"
    ),
    PUERTO_RICO(
        80,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "029",
        "Caribbean",
        "Puerto Rico",
        "630",
        "PR",
        "PRI"
    ),
    SAINT_BARTHELEMY(
        81,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "029",
        "Caribbean",
        "Saint Barthelemy",
        "652",
        "BL",
        "BLM"
    ),
    SAINT_KITTS_AND_NEVIS(
        82,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "029",
        "Caribbean",
        "Saint Kitts and Nevis",
        "659",
        "KN",
        "KNA"
    ),
    SAINT_LUCIA(
        83,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "029",
        "Caribbean",
        "Saint Lucia",
        "662",
        "LC",
        "LCA"
    ),
    SAINT_MARTIN_FRENCH_PART(
        84,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "029",
        "Caribbean",
        "Saint Martin (French part)",
        "663",
        "MF",
        "MAF"
    ),
    SAINT_VINCENT_AND_THE_GRENADINES(
        85,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "029",
        "Caribbean",
        "Saint Vincent and the Grenadines",
        "670",
        "VC",
        "VCT"
    ),
    SINT_MAARTEN_DUTCH_PART(
        86,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "029",
        "Caribbean",
        "Sint Maarten (Dutch part)",
        "534",
        "SX",
        "SXM"
    ),
    TRINIDAD_AND_TOBAGO(
        87,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "029",
        "Caribbean",
        "Trinidad and Tobago",
        "780",
        "TT",
        "TTO"
    ),
    TURKS_AND_CAICOS_ISLANDS(
        88,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "029",
        "Caribbean",
        "Turks and Caicos Islands",
        "796",
        "TC",
        "TCA"
    ),
    UNITED_STATES_VIRGIN_ISLANDS(
        89,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "029",
        "Caribbean",
        "United States Virgin Islands",
        "850",
        "VI",
        "VIR"
    ),
    BELIZE(
        90,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "013",
        "Central America",
        "Belize",
        "084",
        "BZ",
        "BLZ"
    ),
    COSTA_RICA(
        91,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "013",
        "Central America",
        "Costa Rica",
        "188",
        "CR",
        "CRI"
    ),
    EL_SALVADOR(
        92,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "013",
        "Central America",
        "El Salvador",
        "222",
        "SV",
        "SLV"
    ),
    GUATEMALA(
        93,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "013",
        "Central America",
        "Guatemala",
        "320",
        "GT",
        "GTM"
    ),
    HONDURAS(
        94,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "013",
        "Central America",
        "Honduras",
        "340",
        "HN",
        "HND"
    ),
    MEXICO(
        95,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "013",
        "Central America",
        "Mexico",
        "484",
        "MX",
        "MEX"
    ),
    NICARAGUA(
        96,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "013",
        "Central America",
        "Nicaragua",
        "558",
        "NI",
        "NIC"
    ),
    PANAMA(
        97,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "013",
        "Central America",
        "Panama",
        "591",
        "PA",
        "PAN"
    ),
    ARGENTINA(
        98,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "005",
        "South America",
        "Argentina",
        "32",
        "AR",
        "ARG"
    ),
    BOLIVIA(
        99,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "005",
        "South America",
        "Bolivia (Plurinational State of)",
        "68",
        "BO",
        "BOL"
    ),
    BOUVET_ISLAND(
        100,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "005",
        "South America",
        "Bouvet Island",
        "74",
        "BV",
        "BVT"
    ),
    BRAZIL(
        101,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "005",
        "South America",
        "Brazil",
        "76",
        "BR",
        "BRA"
    ),
    CHILE(
        102,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "005",
        "South America",
        "Chile",
        "152",
        "CL",
        "CHL"
    ),
    COLOMBIA(
        103,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "005",
        "South America",
        "Colombia",
        "170",
        "CO",
        "COL"
    ),
    ECUADOR(
        104,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "005",
        "South America",
        "Ecuador",
        "218",
        "EC",
        "ECU"
    ),
    FALKLAND_ISLANDS(
        105,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "005",
        "South America",
        "Falkland Islands (Malvinas)",
        "238",
        "FK",
        "FLK"
    ),
    FRENCH_GUIANA(
        106,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "005",
        "South America",
        "French Guiana",
        "254",
        "GF",
        "GUF"
    ),
    GUYANA(
        107,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "005",
        "South America",
        "Guyana",
        "328",
        "GY",
        "GUY",
        true
    ),
    PARAGUAY(
        108,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "005",
        "South America",
        "Paraguay",
        "600",
        "PY",
        "PRY",
        true
    ),
    PERU(
        109,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "005",
        "South America",
        "Peru",
        "604",
        "PE",
        "PER"
    ),
    SOUTH_GEORGIA_AND_THE_SOUTH_SANDWICH_ISLANDS(
        110,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "005",
        "South America",
        "South Georgia and the South Sandwich Islands",
        "239",
        "GS",
        "SGS"
    ),
    SURINAME(
        111,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "005",
        "South America",
        "Suriname",
        "740",
        "SR",
        "SUR",
        true
    ),
    URUGUAY(
        112,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "005",
        "South America",
        "Uruguay",
        "858",
        "UY",
        "URY"
    ),
    VENEZUELA(
        113,
        "World",
        "019",
        "Americas",
        "419",
        "Latin America and the Caribbean",
        "005",
        "South America",
        "Venezuela (Bolivarian Republic of)",
        "862",
        "VE",
        "VEN"
    ),
    BERMUDA(
        114,
        "World",
        "019",
        "Americas",
        "021",
        "Northern America",
        null,
        null,
        "Bermuda",
        "060",
        "BM",
        "BMU"
    ),
    CANADA(
        115,
        "World",
        "019",
        "Americas",
        "021",
        "Northern America",
        null,
        null,
        "Canada",
        "124",
        "CA",
        "CAN"
    ),
    GREENLAND(
        116,
        "World",
        "019",
        "Americas",
        "021",
        "Northern America",
        null,
        null,
        "Greenland",
        "304",
        "GL",
        "GRL"
    ),
    SAINT_PIERRE_AND_MIQUELON(
        117,
        "World",
        "019",
        "Americas",
        "021",
        "Northern America",
        null,
        null,
        "Saint Pierre and Miquelon",
        "666",
        "PM",
        "SPM"
    ),
    UNITED_STATES_OF_AMERICA(
        118,
        "World",
        "019",
        "Americas",
        "021",
        "Northern America",
        null,
        null,
        "United States of America",
        "840",
        "US",
        "USA"
    ),
    ANTARCTICA(119, "World", "", "", "", "", null, null, "Antarctica", "010", "AQ", "ATA"),
    KAZAKHSTAN(
        120,
        "World",
        "142",
        "Asia",
        "143",
        "Central Asia",
        null,
        null,
        "Kazakhstan",
        "398",
        "KZ",
        "KAZ",
        true
    ),
    KYRGYZSTAN(
        121,
        "World",
        "142",
        "Asia",
        "143",
        "Central Asia",
        null,
        null,
        "Kyrgyzstan",
        "417",
        "KG",
        "KGZ",
        true
    ),
    TAJIKISTAN(
        122,
        "World",
        "142",
        "Asia",
        "143",
        "Central Asia",
        null,
        null,
        "Tajikistan",
        "762",
        "TJ",
        "TJK",
        true
    ),
    TURKMENISTAN(
        123,
        "World",
        "142",
        "Asia",
        "143",
        "Central Asia",
        null,
        null,
        "Turkmenistan",
        "795",
        "TM",
        "TKM",
        true
    ),
    UZBEKISTAN(
        124,
        "World",
        "142",
        "Asia",
        "143",
        "Central Asia",
        null,
        null,
        "Uzbekistan",
        "860",
        "UZ",
        "UZB",
        true
    ),
    CHINA(
        125,
        "World",
        "142",
        "Asia",
        "030",
        "Eastern Asia",
        null,
        null,
        "China",
        "156",
        "CN",
        "CHN"
    ),
    CHINA_HONG_KONG(
        126,
        "World",
        "142",
        "Asia",
        "030",
        "Eastern Asia",
        null,
        null,
        "China, Hong Kong Special Administrative Region",
        "344",
        "HK",
        "HKG"
    ),
    CHINA_MACAO(
        127,
        "World",
        "142",
        "Asia",
        "030",
        "Eastern Asia",
        null,
        null,
        "China, Macao Special Administrative Region",
        "446",
        "MO",
        "MAC"
    ),
    NORTH_KOREA(
        128,
        "World",
        "142",
        "Asia",
        "030",
        "Eastern Asia",
        null,
        null,
        "Democratic People's Republic of Korea",
        "408",
        "KP",
        "PRK"
    ),
    JAPAN(
        129,
        "World",
        "142",
        "Asia",
        "030",
        "Eastern Asia",
        null,
        null,
        "Japan",
        "392",
        "JP",
        "JPN"
    ),
    MONGOLIA(
        130,
        "World",
        "142",
        "Asia",
        "030",
        "Eastern Asia",
        null,
        null,
        "Mongolia",
        "496",
        "MN",
        "MNG",
        true
    ),
    SOUTH_KOREA(
        131,
        "World",
        "142",
        "Asia",
        "030",
        "Eastern Asia",
        null,
        null,
        "Republic of Korea",
        "410",
        "KR",
        "KOR"
    ),
    BRUNEI(
        132,
        "World",
        "142",
        "Asia",
        "035",
        "South-eastern Asia",
        null,
        null,
        "Brunei Darussalam",
        "096",
        "BN",
        "BRN"
    ),
    CAMBODIA(
        133,
        "World",
        "142",
        "Asia",
        "035",
        "South-eastern Asia",
        null,
        null,
        "Cambodia",
        "116",
        "KH",
        "KHM",
        ldc = true
    ),
    INDONESIA(
        134,
        "World",
        "142",
        "Asia",
        "035",
        "South-eastern Asia",
        null,
        null,
        "Indonesia",
        "360",
        "ID",
        "IDN"
    ),
    LAOS(
        135,
        "World",
        "142",
        "Asia",
        "035",
        "South-eastern Asia",
        null,
        null,
        "Lao People's Democratic Republic",
        "418",
        "LA",
        "LAO",
        ldc = true,
        lldc = true
    ),
    MALAYSIA(
        136,
        "World",
        "142",
        "Asia",
        "035",
        "South-eastern Asia",
        null,
        null,
        "Malaysia",
        "458",
        "MY",
        "MYS"
    ),
    MYANMAR(
        137,
        "World",
        "142",
        "Asia",
        "035",
        "South-eastern Asia",
        null,
        null,
        "Myanmar",
        "104",
        "MM",
        "MMR",
        ldc = true
    ),
    PHILIPPINES(
        138,
        "World",
        "142",
        "Asia",
        "035",
        "South-eastern Asia",
        null,
        null,
        "Philippines",
        "608",
        "PH",
        "PHL"
    ),
    SINGAPORE(
        139,
        "World",
        "142",
        "Asia",
        "035",
        "South-eastern Asia",
        null,
        null,
        "Singapore",
        "702",
        "SG",
        "SGP",
        sids = true
    ),
    THAILAND(
        140,
        "World",
        "142",
        "Asia",
        "035",
        "South-eastern Asia",
        null,
        null,
        "Thailand",
        "764",
        "TH",
        "THA"
    ),
    TIMOR_LESTE(
        141,
        "World",
        "142",
        "Asia",
        "035",
        "South-eastern Asia",
        null,
        null,
        "Timor-Leste",
        "626",
        "TL",
        "TLS",
        ldc = true,
        lldc = true
    ),
    VIETNAM(
        142,
        "World",
        "142",
        "Asia",
        "035",
        "South-eastern Asia",
        null,
        null,
        "Vietnam",
        "704",
        "VN",
        "VNM"
    ),
    AFGHANISTAN(
        143,
        "World",
        "142",
        "Asia",
        "034",
        "Southern Asia",
        null,
        null,
        "Afghanistan",
        "004",
        "AF",
        "AFG",
        ldc = true,
        lldc = true
    ),
    BANGLADESH(
        144,
        "World",
        "142",
        "Asia",
        "034",
        "Southern Asia",
        null,
        null,
        "Bangladesh",
        "050",
        "BD",
        "BGD",
        ldc = true
    ),
    BHUTAN(
        145,
        "World",
        "142",
        "Asia",
        "034",
        "Southern Asia",
        null,
        null,
        "Bhutan",
        "064",
        "BT",
        "BTN",
        ldc = true
    ),
    INDIA(
        146,
        "World",
        "142",
        "Asia",
        "034",
        "Southern Asia",
        null,
        null,
        "India",
        "356",
        "IN",
        "IND"
    ),
    IRAN(
        147,
        "World",
        "142",
        "Asia",
        "034",
        "Southern Asia",
        null,
        null,
        "Iran (Islamic Republic of)",
        "364",
        "IR",
        "IRN"
    ),
    MALDIVES(
        148,
        "World",
        "142",
        "Asia",
        "034",
        "Southern Asia",
        null,
        null,
        "Maldives",
        "462",
        "MV",
        "MDV",
        ldc = true
    ),
    NEPAL(
        149,
        "World",
        "142",
        "Asia",
        "034",
        "Southern Asia",
        null,
        null,
        "Nepal",
        "524",
        "NP",
        "NPL",
        ldc = true,
        lldc = true
    ),
    PAKISTAN(
        150,
        "World",
        "142",
        "Asia",
        "034",
        "Southern Asia",
        null,
        null,
        "Pakistan",
        "586",
        "PK",
        "PAK"
    ),
    SRI_LANKA(
        151,
        "World",
        "142",
        "Asia",
        "034",
        "Southern Asia",
        null,
        null,
        "Sri Lanka",
        "144",
        "LK",
        "LKA"
    ),
    ARMENIA(
        152,
        "World",
        "142",
        "Asia",
        "145",
        "Western Asia",
        null,
        null,
        "Armenia",
        "051",
        "AM",
        "ARM",
        ldc = true
    ),
    AZERBAIJAN(
        153,
        "World",
        "142",
        "Asia",
        "145",
        "Western Asia",
        null,
        null,
        "Azerbaijan",
        "031",
        "AZ",
        "AZE",
        ldc = true
    ),
    BAHRAIN(
        154,
        "World",
        "142",
        "Asia",
        "145",
        "Western Asia",
        null,
        null,
        "Bahrain",
        "048",
        "BH",
        "BHR"
    ),
    CYPRUS(
        155,
        "World",
        "142",
        "Asia",
        "145",
        "Western Asia",
        null,
        null,
        "Cyprus",
        "196",
        "CY",
        "CYP"
    ),
    GEORGIA(
        156,
        "World",
        "142",
        "Asia",
        "145",
        "Western Asia",
        null,
        null,
        "Georgia",
        "268",
        "GE",
        "GEO"
    ),
    IRAQ(
        157,
        "World",
        "142",
        "Asia",
        "145",
        "Western Asia",
        null,
        null,
        "Iraq",
        "368",
        "IQ",
        "IRQ"
    ),
    ISRAEL(
        158,
        "World",
        "142",
        "Asia",
        "145",
        "Western Asia",
        null,
        null,
        "Israel",
        "376",
        "IL",
        "ISR"
    ),
    JORDAN(
        159,
        "World",
        "142",
        "Asia",
        "145",
        "Western Asia",
        null,
        null,
        "Jordan",
        "400",
        "JO",
        "JOR"
    ),
    KUWAIT(
        160,
        "World",
        "142",
        "Asia",
        "145",
        "Western Asia",
        null,
        null,
        "Kuwait",
        "414",
        "KW",
        "KWT"
    ),
    LEBANON(
        161,
        "World",
        "142",
        "Asia",
        "145",
        "Western Asia",
        null,
        null,
        "Lebanon",
        "422",
        "LB",
        "LBN"
    ),
    OMAN(
        162,
        "World",
        "142",
        "Asia",
        "145",
        "Western Asia",
        null,
        null,
        "Oman",
        "512",
        "OM",
        "OMN"
    ),
    QATAR(
        163,
        "World",
        "142",
        "Asia",
        "145",
        "Western Asia",
        null,
        null,
        "Qatar",
        "634",
        "QA",
        "QAT"
    ),
    SAUDI_ARABIA(
        164,
        "World",
        "142",
        "Asia",
        "145",
        "Western Asia",
        null,
        null,
        "Saudi Arabia",
        "682",
        "SA",
        "SAU"
    ),
    PALESTINE(
        165,
        "World",
        "142",
        "Asia",
        "145",
        "Western Asia",
        null,
        null,
        "State of Palestine",
        "275",
        "PS",
        "PSE"
    ),
    SYRIA(
        166,
        "World",
        "142",
        "Asia",
        "145",
        "Western Asia",
        null,
        null,
        "Syrian Arab Republic",
        "760",
        "SY",
        "SYR"
    ),
    TURKEY(
        167,
        "World",
        "142",
        "Asia",
        "145",
        "Western Asia",
        null,
        null,
        "Turkey",
        "792",
        "TR",
        "TUR"
    ),
    UAE(
        168,
        "World",
        "142",
        "Asia",
        "145",
        "Western Asia",
        null,
        null,
        "United Arab Emirates",
        "784",
        "AE",
        "ARE"
    ),
    YEMEN(
        169,
        "World",
        "142",
        "Asia",
        "145",
        "Western Asia",
        null,
        null,
        "Yemen",
        "887",
        "YE",
        "YEM",
        ldc = true
    ),
    BELARUS(
        170,
        "World",
        "150",
        "Europe",
        "151",
        "Eastern Europe",
        null,
        null,
        "Belarus",
        "112",
        "BY",
        "BLR"
    ),
    BULGARIA(
        171,
        "World",
        "150",
        "Europe",
        "151",
        "Eastern Europe",
        null,
        null,
        "Bulgaria",
        "100",
        "BG",
        "BGR"
    ),
    CZECHIA(
        172,
        "World",
        "150",
        "Europe",
        "151",
        "Eastern Europe",
        null,
        null,
        "Czechia",
        "203",
        "CZ",
        "CZE"
    ),
    HUNGARY(
        173,
        "World",
        "150",
        "Europe",
        "151",
        "Eastern Europe",
        null,
        null,
        "Hungary",
        "348",
        "HU",
        "HUN"
    ),
    POLAND(
        174,
        "World",
        "150",
        "Europe",
        "151",
        "Eastern Europe",
        null,
        null,
        "Poland",
        "616",
        "PL",
        "POL"
    ),
    MOLDOVA(
        175,
        "World",
        "150",
        "Europe",
        "151",
        "Eastern Europe",
        null,
        null,
        "Republic of Moldova",
        "498",
        "MD",
        "MDA",
        ldc = true
    ),
    ROMANIA(
        176,
        "World",
        "150",
        "Europe",
        "151",
        "Eastern Europe",
        null,
        null,
        "Romania",
        "642",
        "RO",
        "ROU"
    ),
    RUSSIA(
        177,
        "World",
        "150",
        "Europe",
        "151",
        "Eastern Europe",
        null,
        null,
        "Russian Federation",
        "643",
        "RU",
        "RUS"
    ),
    SLOVAKIA(
        178,
        "World",
        "150",
        "Europe",
        "151",
        "Eastern Europe",
        null,
        null,
        "Slovakia",
        "703",
        "SK",
        "SVK"
    ),
    UKRAINE(
        179,
        "World",
        "150",
        "Europe",
        "151",
        "Eastern Europe",
        null,
        null,
        "Ukraine",
        "804",
        "UA",
        "UKR"
    ),
    ALAND_ISLANDS(
        180,
        "World",
        "150",
        "Europe",
        "154",
        "Northern Europe",
        null,
        null,
        "Aland Islands",
        "248",
        "AX",
        "ALA"
    ),
    DENMARK(
        181,
        "World",
        "150",
        "Europe",
        "154",
        "Northern Europe",
        null,
        null,
        "Denmark",
        "208",
        "DK",
        "DNK"
    ),
    ESTONIA(
        182,
        "World",
        "150",
        "Europe",
        "154",
        "Northern Europe",
        null,
        null,
        "Estonia",
        "233",
        "EE",
        "EST"
    ),
    FAROE_ISLANDS(
        183,
        "World",
        "150",
        "Europe",
        "154",
        "Northern Europe",
        null,
        null,
        "Faroe Islands",
        "234",
        "FO",
        "FRO"
    ),
    FINLAND(
        184,
        "World",
        "150",
        "Europe",
        "154",
        "Northern Europe",
        null,
        null,
        "Finland",
        "246",
        "FI",
        "FIN"
    ),
    GUERNSEY(
        185,
        "World",
        "150",
        "Europe",
        "154",
        "Northern Europe",
        null,
        null,
        "Guernsey",
        "831",
        "GG",
        "GGY"
    ),
    ICELAND(
        186,
        "World",
        "150",
        "Europe",
        "154",
        "Northern Europe",
        null,
        null,
        "Iceland",
        "352",
        "IS",
        "ISL"
    ),
    IRELAND(
        187,
        "World",
        "150",
        "Europe",
        "154",
        "Northern Europe",
        null,
        null,
        "Ireland",
        "372",
        "IE",
        "IRL"
    ),
    ISLE_OF_MAN(
        188,
        "World",
        "150",
        "Europe",
        "154",
        "Northern Europe",
        null,
        null,
        "Isle of Man",
        "833",
        "IM",
        "IMN"
    ),
    JERSEY(
        189,
        "World",
        "150",
        "Europe",
        "154",
        "Northern Europe",
        null,
        null,
        "Jersey",
        "832",
        "JE",
        "JEY"
    ),
    LATVIA(
        190,
        "World",
        "150",
        "Europe",
        "154",
        "Northern Europe",
        null,
        null,
        "Latvia",
        "428",
        "LV",
        "LVA"
    ),
    LITHUANIA(
        191,
        "World",
        "150",
        "Europe",
        "154",
        "Northern Europe",
        null,
        null,
        "Lithuania",
        "440",
        "LT",
        "LTU"
    ),
    NORWAY(
        192,
        "World",
        "150",
        "Europe",
        "154",
        "Northern Europe",
        null,
        null,
        "Norway",
        "578",
        "NO",
        "NOR"
    ),
    SVALBARD_AND_JAN_MAYEN_ISLANDS(
        193,
        "World",
        "150",
        "Europe",
        "154",
        "Northern Europe",
        null,
        null,
        "Svalbard and Jan Mayen Islands",
        "744",
        "SJ",
        "SJM"
    ),
    SWEDEN(
        194,
        "World",
        "150",
        "Europe",
        "154",
        "Northern Europe",
        null,
        null,
        "Sweden",
        "752",
        "SE",
        "SWE"
    ),
    UNITED_KINGDOM(
        195,
        "World",
        "150",
        "Europe",
        "154",
        "Northern Europe",
        null,
        null,
        "United Kingdom of Great Britain and Northern Ireland",
        "826",
        "GB",
        "GBR"
    ),
    ALBANIA(
        196,
        "World",
        "150",
        "Europe",
        "039",
        "Southern Europe",
        null,
        null,
        "Albania",
        "008",
        "AL",
        "ALB"
    ),
    ANDORRA(
        197,
        "World",
        "150",
        "Europe",
        "039",
        "Southern Europe",
        null,
        null,
        "Andorra",
        "020",
        "AD",
        "AND"
    ),
    BOSNIA_AND_HERZEGOVINA(
        198,
        "World",
        "150",
        "Europe",
        "039",
        "Southern Europe",
        null,
        null,
        "Bosnia and Herzegovina",
        "070",
        "BA",
        "BIH"
    ),
    CROATIA(
        199,
        "World",
        "150",
        "Europe",
        "039",
        "Southern Europe",
        null,
        null,
        "Croatia",
        "191",
        "HR",
        "HRV"
    ),
    GIBRALTAR(
        200,
        "World",
        "150",
        "Europe",
        "039",
        "Southern Europe",
        null,
        null,
        "Gibraltar",
        "292",
        "GI",
        "GIB"
    ),
    GREECE(
        201,
        "World",
        "150",
        "Europe",
        "039",
        "Southern Europe",
        null,
        null,
        "Greece",
        "300",
        "GR",
        "GRC"
    ),
    HOLY_SEE(
        202,
        "World",
        "150",
        "Europe",
        "039",
        "Southern Europe",
        null,
        null,
        "Holy See",
        "336",
        "VA",
        "VAT"
    ),
    ITALY(
        203,
        "World",
        "150",
        "Europe",
        "039",
        "Southern Europe",
        null,
        null,
        "Italy",
        "380",
        "IT",
        "ITA"
    ),
    MALTA(
        204,
        "World",
        "150",
        "Europe",
        "039",
        "Southern Europe",
        null,
        null,
        "Malta",
        "470",
        "MT",
        "MLT"
    ),
    MONTENEGRO(
        205,
        "World",
        "150",
        "Europe",
        "039",
        "Southern Europe",
        null,
        null,
        "Montenegro",
        "499",
        "ME",
        "MNE"
    ),
    NORTH_MACEDONIA(
        206,
        "World",
        "150",
        "Europe",
        "039",
        "Southern Europe",
        null,
        null,
        "North Macedonia",
        "807",
        "MK",
        "MKD"
    ),
    PORTUGAL(
        207,
        "World",
        "150",
        "Europe",
        "039",
        "Southern Europe",
        null,
        null,
        "Portugal",
        "620",
        "PT",
        "PRT"
    ),
    SAN_MARINO(
        208,
        "World",
        "150",
        "Europe",
        "039",
        "Southern Europe",
        null,
        null,
        "San Marino",
        "674",
        "SM",
        "SMR"
    ),
    SERBIA(
        209,
        "World",
        "150",
        "Europe",
        "039",
        "Southern Europe",
        null,
        null,
        "Serbia",
        "688",
        "RS",
        "SRB"
    ),
    SLOVENIA(
        210,
        "World",
        "150",
        "Europe",
        "039",
        "Southern Europe",
        null,
        null,
        "Slovenia",
        "705",
        "SI",
        "SVN"
    ),
    SPAIN(
        211,
        "World",
        "150",
        "Europe",
        "039",
        "Southern Europe",
        null,
        null,
        "Spain",
        "724",
        "ES",
        "ESP"
    ),
    AUSTRIA(
        212,
        "World",
        "150",
        "Europe",
        "155",
        "Western Europe",
        null,
        null,
        "Austria",
        "040",
        "AT",
        "AUT"
    ),
    BELGIUM(
        213,
        "World",
        "150",
        "Europe",
        "155",
        "Western Europe",
        null,
        null,
        "Belgium",
        "056",
        "BE",
        "BEL"
    ),
    FRANCE(
        214,
        "World",
        "150",
        "Europe",
        "155",
        "Western Europe",
        null,
        null,
        "France",
        "250",
        "FR",
        "FRA"
    ),
    GERMANY(
        215,
        "World",
        "150",
        "Europe",
        "155",
        "Western Europe",
        null,
        null,
        "Germany",
        "276",
        "DE",
        "DEU"
    ),
    LIECHTENSTEIN(
        216,
        "World",
        "150",
        "Europe",
        "155",
        "Western Europe",
        null,
        null,
        "Liechtenstein",
        "438",
        "LI",
        "LIE"
    ),
    LUXEMBOURG(
        217,
        "World",
        "150",
        "Europe",
        "155",
        "Western Europe",
        null,
        null,
        "Luxembourg",
        "442",
        "LU",
        "LUX"
    ),
    MONACO(
        218,
        "World",
        "150",
        "Europe",
        "155",
        "Western Europe",
        null,
        null,
        "Monaco",
        "492",
        "MC",
        "MCO"
    ),
    NETHERLANDS(
        219,
        "World",
        "150",
        "Europe",
        "155",
        "Western Europe",
        null,
        null,
        "Netherlands (Kingdom of the)",
        "528",
        "NL",
        "NLD"
    ),
    SWITZERLAND(
        220,
        "World",
        "150",
        "Europe",
        "155",
        "Western Europe",
        null,
        null,
        "Switzerland",
        "756",
        "CH",
        "CHE"
    ),
    AUSTRALIA(
        221,
        "World",
        "009",
        "Oceania",
        "053",
        "Australia and New Zealand",
        null,
        null,
        "Australia",
        "036",
        "AU",
        "AUS"
    ),
    CHRISTMAS_ISLAND(
        222,
        "World",
        "009",
        "Oceania",
        "053",
        "Australia and New Zealand",
        null,
        null,
        "Christmas Island",
        "162",
        "CX",
        "CXR"
    ),
    COCOS_ISLANDS(
        223,
        "World",
        "009",
        "Oceania",
        "053",
        "Australia and New Zealand",
        null,
        null,
        "Cocos (Keeling) Islands",
        "166",
        "CC",
        "CCK"
    ),
    HEARD_MCDONALD_ISLANDS(
        224,
        "World",
        "009",
        "Oceania",
        "053",
        "Australia and New Zealand",
        null,
        null,
        "Heard Island and McDonald Islands",
        "334",
        "HM",
        "HMD"
    ),
    NEW_ZEALAND(
        225,
        "World",
        "009",
        "Oceania",
        "053",
        "Australia and New Zealand",
        null,
        null,
        "New Zealand",
        "554",
        "NZ",
        "NZL"
    ),
    NORFOLK_ISLAND(
        226,
        "World",
        "009",
        "Oceania",
        "053",
        "Australia and New Zealand",
        null,
        null,
        "Norfolk Island",
        "574",
        "NF",
        "NFK"
    ),
    FIJI(
        227,
        "World",
        "009",
        "Oceania",
        "054",
        "Melanesia",
        null,
        null,
        "Fiji",
        "242",
        "FJ",
        "FJI"
    ),
    NEW_CALEDONIA(
        228,
        "World",
        "009",
        "Oceania",
        "054",
        "Melanesia",
        null,
        null,
        "New Caledonia",
        "540",
        "NC",
        "NCL"
    ),
    PAPUA_NEW_GUINEA(
        229,
        "World",
        "009",
        "Oceania",
        "054",
        "Melanesia",
        null,
        null,
        "Papua New Guinea",
        "598",
        "PG",
        "PNG"
    ),
    SOLOMON_ISLANDS(
        230,
        "World",
        "009",
        "Oceania",
        "054",
        "Melanesia",
        null,
        null,
        "Solomon Islands",
        "090",
        "SB",
        "SLB",
        true,
        false,
        true
    ),
    VANUATU(
        231,
        "World",
        "009",
        "Oceania",
        "054",
        "Melanesia",
        null,
        null,
        "Vanuatu",
        "548",
        "VU",
        "VUT",
        true,
        false,
        true
    ),
    GUAM(
        232,
        "World",
        "009",
        "Oceania",
        "057",
        "Micronesia",
        null,
        null,
        "Guam",
        "316",
        "GU",
        "GUM",
        true
    ),
    KIRIBATI(
        233,
        "World",
        "009",
        "Oceania",
        "057",
        "Micronesia",
        null,
        null,
        "Kiribati",
        "296",
        "KI",
        "KIR",
        true,
        false,
        true
    ),
    MARSHALL_ISLANDS(
        234,
        "World",
        "009",
        "Oceania",
        "057",
        "Micronesia",
        null,
        null,
        "Marshall Islands",
        "584",
        "MH",
        "MHL",
        true,
        false,
        true
    ),
    MICRONESIA(
        235,
        "World",
        "009",
        "Oceania",
        "057",
        "Micronesia",
        null,
        null,
        "Micronesia (Federated States of)",
        "583",
        "FM",
        "FSM",
        true,
        false,
        true
    ),
    NAURU(
        236,
        "World",
        "009",
        "Oceania",
        "057",
        "Micronesia",
        null,
        null,
        "Nauru",
        "520",
        "NR",
        "NRU",
        true,
        false,
        true
    ),
    NORTHERN_MARIANA_ISLANDS(
        237,
        "World",
        "009",
        "Oceania",
        "057",
        "Micronesia",
        null,
        null,
        "Northern Mariana Islands",
        "580",
        "MP",
        "MNP",
        true
    ),
    PALAU(
        238,
        "World",
        "009",
        "Oceania",
        "057",
        "Micronesia",
        null,
        null,
        "Palau",
        "585",
        "PW",
        "PLW",
        true,
        false,
        true
    ),
    US_MINOR_OUTLYING_ISLANDS(
        239,
        "World",
        "009",
        "Oceania",
        "057",
        "Micronesia",
        null,
        null,
        "United States Minor Outlying Islands",
        "581",
        "UM",
        "UMI"
    ),
    AMERICAN_SAMOA(
        240,
        "World",
        "009",
        "Oceania",
        "061",
        "Polynesia",
        null,
        null,
        "American Samoa",
        "016",
        "AS",
        "ASM",
        true
    ),
    COOK_ISLANDS(
        241,
        "World",
        "009",
        "Oceania",
        "061",
        "Polynesia",
        null,
        null,
        "Cook Islands",
        "184",
        "CK",
        "COK",
        true
    ),
    FRENCH_POLYNESIA(
        242,
        "World",
        "009",
        "Oceania",
        "061",
        "Polynesia",
        null,
        null,
        "French Polynesia",
        "258",
        "PF",
        "PYF",
        true
    ),
    NIUE(
        243,
        "World",
        "009",
        "Oceania",
        "061",
        "Polynesia",
        null,
        null,
        "Niue",
        "570",
        "NU",
        "NIU",
        true
    ),
    PITCAIRN(
        244,
        "World",
        "009",
        "Oceania",
        "061",
        "Polynesia",
        null,
        null,
        "Pitcairn",
        "612",
        "PN",
        "PCN"
    ),
    SAMOA(
        245,
        "World",
        "009",
        "Oceania",
        "061",
        "Polynesia",
        null,
        null,
        "Samoa",
        "882",
        "WS",
        "WSM",
        false,
        false,
        true
    ),
    TOKELAU(
        246,
        "World",
        "009",
        "Oceania",
        "061",
        "Polynesia",
        null,
        null,
        "Tokelau",
        "772",
        "TK",
        "TKL"
    ),
    TONGA(
        247,
        "World",
        "009",
        "Oceania",
        "061",
        "Polynesia",
        null,
        null,
        "Tonga",
        "776",
        "TO",
        "TON",
        false,
        false,
        true
    ),
    TUVALU(
        248,
        "World",
        "009",
        "Oceania",
        "061",
        "Polynesia",
        null,
        null,
        "Tuvalu",
        "798",
        "TV",
        "TUV",
        true,
        false,
        true
    ),
    WALLIS_FUTUNA_ISLANDS(
        249,
        "World",
        "009",
        "Oceania",
        "061",
        "Polynesia",
        null,
        null,
        "Wallis and Futuna Islands",
        "876",
        "WF",
        "WLF"
    );

    companion object {
        fun fromProto(enum: CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?, includeAnyway: List<CountryData> = listOf()) =
            values().stringToProto(string, includeAnyway)

        fun abbreviatedString(string: String?) =
            values().find { it.dataString == string }?.abbreviation ?: string

        fun toEvent(enum: CompatibleEnum) = values().toChangeOrClearEvent(enum, "country")
    }

}