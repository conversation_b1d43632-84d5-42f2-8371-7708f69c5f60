package gov.afrl.batdok.encounter.treatment

import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.TreatmentCommands.Splint
import gov.afrl.batdok.commands.proto.splint
import gov.afrl.batdok.util.*

class SplintData(val pulsePresent: Boolean?, val type: String? = null): TreatmentData{
    override fun toProtobuf(): Message = splint {
        <EMAIL>?.let { pulsePresent = nullableBoolValue(it) }
        <EMAIL>?.let { type = ImmobilizationData.Type.fromString(it) }
    }

    override fun toDetailString(): String {
        val pulseString = when(pulsePresent){
            null -> null
            true -> "Pulse present"
            false -> "Pulse not present"
        }
        return listOfNotNull(
            type,
            pulseString
        ).joinToString(", ")
    }

    enum class Type(override val dataString: String, override val protoIndex: Int): ProtoEnum {
        AIR("Air", 1),
        BOARD_WIRE("Board/Wire", 2),
        SAM("SAM", 3),
        TRACTION("Traction", 4),
        ;
        companion object{
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }

    companion object{
        fun fromProtobuf(splint: Splint) = SplintData(
            splint.pulsePresent.toPrimitive(),
            Type.fromProto(splint.type)
        )
    }
}