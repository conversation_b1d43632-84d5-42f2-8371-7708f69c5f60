package gov.afrl.batdok.encounter

import com.google.protobuf.GeneratedMessageV3
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.contact
import gov.afrl.batdok.util.stringValue
import java.io.Serializable

open class Contact(
    open val name: String? = null,
    open val phone: String? = null,
    open val email: String? = null
) : Serializable {
    constructor(proto: SharedProtobufObjects.Contact): this(
        proto.name.value.takeIf { proto.hasName() && proto.name.value.isNotBlank() },
        proto.phone.value.takeIf { proto.hasPhone() && proto.phone.value.isNotBlank() },
        proto.email.value.takeIf { proto.hasEmail() && proto.email.value.isNotBlank() }
    )

    override fun toString() = listOfNotNull(
        "Name: $name".takeUnless { name.isNullOrBlank() },
        "Phone: $phone".takeUnless { phone.isNullOrBlank() },
        "Email: $email".takeUnless { email.isNullOrBlank() }
    ).joinToString(" ")

    open fun isBlank() = name.isNullOrBlank()
            && phone.isNullOrBlank()
            && email.isNullOrBlank()

    open fun toProto() : SharedProtobufObjects.Contact {
        return contact {
            <EMAIL>?.let { if (it.isNotBlank()) name = stringValue(it) }
            <EMAIL>?.let { if (it.isNotBlank()) phone = stringValue(it) }
            <EMAIL>?.let { if (it.isNotBlank()) email = stringValue(it) }
        }
    }
}