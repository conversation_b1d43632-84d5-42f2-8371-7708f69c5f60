package gov.afrl.batdok.encounter.treatment

import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.TreatmentCommands
import gov.afrl.batdok.commands.proto.foleyCatheter
import gov.afrl.batdok.util.*

class FoleyCatheterData(
    val size: Float? = null,
    val sizeUnit: String? = null,
    val color: String? = null,
    val character: String? = null,
    val assess: String? = null
): TreatmentData{
    override fun toProtobuf(): Message = foleyCatheter{
        this.size = nullableFloatValue(<EMAIL>)
        this.sizeUnit = compatibleEnum(<EMAIL>)
        this.color = Color.fromString(<EMAIL>)
        this.character = compatibleEnum(<EMAIL>)
        this.assess = compatibleEnum(<EMAIL>)
    }

    override fun toDetailString(): String {
        return listOfNotNull(
            size.toDetailString("Size", sizeUnit),
            color.toDetailString("Type"),
            character.toDetailString("Character"),
            assess.toDetailString("Assess")
        ).joinToString(", ")
    }

    companion object{
        fun fromProtobuf(catheter: TreatmentCommands.FoleyCatheter): FoleyCatheterData{
            return FoleyCatheterData(
                catheter.size.toPrimitive(),
                catheter.sizeUnit.toPrimitive(),
                Color.fromProto(catheter.color),
                catheter.character.toPrimitive(),
                catheter.assess.toPrimitive(),
            )
        }
    }

    enum class Color(
        override val protoIndex: Int,
        override val dataString: String,
        val frenchUnits: Int,
        val mm: Float
    ): ProtoEnum {
        YELLOW_GREEN(1, "Yellow-green", 6, 2.0f),
        CORNFLOWER_BLUE(2, "Cornflower Blue", 8, 2.7f),
        BLACK(3, "Black", 10, 3.3f),
        WHITE(4, "White", 12, 4.0f),
        GREEN(5, "Green", 14, 4.7f),
        ORANGE(6, "Orange", 16, 5.3f),
        RED(7, "Red", 18, 6.0f),
        YELLOW(8, "Yellow", 20, 6.7f),
        PURPLE(9, "Purple", 22, 7.3f),
        BLUE(10, "Blue", 24, 8.0f),
        PINK(11, "Pink", 26, 8.7f),
        ;

        companion object{
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }
}