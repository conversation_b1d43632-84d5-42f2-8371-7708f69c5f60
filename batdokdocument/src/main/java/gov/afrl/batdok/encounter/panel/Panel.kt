package gov.afrl.batdok.encounter.panel

import gov.afrl.batdok.commands.proto.labEntry
import gov.afrl.batdok.encounter.Linkable
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.encounter.ids.PanelId
import gov.afrl.batdok.util.Timestamped
import gov.afrl.batdok.util.nullableStringValue
import mil.af.afrl.batman.batdokid.DomainId
import java.io.Serializable
import java.time.Instant

@Deprecated("Labs need to be logged individually. Use Individual instead", replaceWith = ReplaceWith("Individual"))
data class Panel(
    override val id: PanelId,
    override val timestamp: Instant,
    val labs: List<LabEntry>,
    val documentId: DocumentId = DomainId.nil()
): Serializable, Linkable, Timestamped {
    fun toProtoLabList(previousLab: Panel? = null) = labs.mapNotNull {
        //If the previous lab contains the same name/value/unit, don't include it
        previousLab?.get(it.name)?.let { prevLab ->
            if(prevLab.value == it.value && prevLab.unit == it.unit){
                return@mapNotNull null
            }
        }
        labEntry {
            name = KnownLabs.fromString(it.name)
            value = nullableStringValue(it.value)
            unit = nullableStringValue(it.unit)
        }
    }
    fun toEventString() = labs
        .sortedBy { lab ->
            KnownLabs.values().find { it.dataString == lab.name }?.protoIndex ?: Int.MAX_VALUE
        }.joinToString(", ")
    operator fun contains(name: String) = labs.any { it.name == name }
    operator fun contains(name: KnownLabs) = contains(name.dataString)
    operator fun get(name: String) = labs.find { it.name == name }
    operator fun get(name: KnownLabs) = get(name.dataString)
}