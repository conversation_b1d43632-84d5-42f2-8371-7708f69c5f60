package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

enum class KnownEventTypes(override val protoIndex: Int, override val dataString: String): ProtoEnum {
    INFO(1, "INFO"),
    EVAC(2, "EVAC"),
    MOI(3, "MOI"),
    VITALS(4, "VITALS"),
    TREAT(5, "TREATMENTS"),
    MEDS(6, "MEDS"),
    CASEVAC(7, "CASEVAC"),
    PCC_CHECKLIST(8, "PCC"),
    SUBJECTIVE(9, "SUB"),
    ASSESSMENT(10, "ASS"),
    OBJECTIVE(11, "OBJ"),
    PLANNING(12, "PLAN"),
    OTHER(13, "OTHER"),
    COMMENT(14, "COMMENT"),
    LABS(15, "LABS"),
    OBSERVATIONS(16, "OBSERVATIONS"),
    MOVEMENT(17, "MOVEMENT"),
    ORDERS(18, "ORDERS"),
    NOTE(19, "NOTE"),
    ACTION(20, "ACTION")
    ;
    companion object{
        fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = entries.toTypedArray().protoToString(enum)
        fun fromString(string: String?) = entries.toTypedArray().stringToProto(string)
    }
}