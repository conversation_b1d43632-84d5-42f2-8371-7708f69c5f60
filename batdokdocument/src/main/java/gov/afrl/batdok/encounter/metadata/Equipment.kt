package gov.afrl.batdok.encounter.metadata

import com.google.protobuf.Any
import com.google.protobuf.Message
import com.google.protobuf.kotlin.isA
import com.google.protobuf.kotlin.unpack
import gov.afrl.batdok.commands.proto.*
import gov.afrl.batdok.commands.proto.EquipmentCommands.*;
import gov.afrl.batdok.commands.proto.SharedProtobufObjects.NullableBoolValue
import gov.afrl.batdok.commands.proto.SharedProtobufObjects.NullableInt32Value
import gov.afrl.batdok.commands.proto.SharedProtobufObjects.NullableStringValue
import gov.afrl.batdok.encounter.ids.*
import gov.afrl.batdok.util.*
import mil.af.afrl.batman.batdokid.DomainId
import java.io.Serializable
import java.time.Instant


class Equipment(
    val name: String,
    val data: EquipmentData? = null,
    override val id: EquipmentId = DomainId.create(),
    override val timestamp: Instant = Instant.now(),
    override val documentId: DocumentId = DomainId.nil()
) : DocumentItem<EquipmentId>(id, documentId, name, timestamp), Serializable {
    fun toProto() = equipmentItem {
        this.type = KnownEquipment.fromString(<EMAIL>)
        data?.toProto()?.let { this.extras = Any.pack(it, "")}
        this.id = <EMAIL>()
    }

    override fun equals(other: kotlin.Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as Equipment

        if (name != other.name) return false

        return true
    }

    override fun hashCode(): Int {
        return name.hashCode()
    }

    override fun toString(): String {
        val extrasString = data?.toEventString()?.let {
            " ($it)"
        }?:""
        return "$name$extrasString"
    }

    companion object{
        fun fromProto(equipmentItem: EquipmentItem): Equipment? {
            val name = KnownEquipment.values().protoToString(equipmentItem.type) ?: return null
            val data = KnownEquipment.getExtras(equipmentItem.extras)
            val equipmentId =  equipmentItem.id.toDomainId<EquipmentId>()
            return Equipment(name, data, equipmentId)
        }
    }
}

enum class KnownEquipment(override val protoIndex: Int, override val dataString: String): ProtoEnum{
    PROPAQM(1, "Propaq M"),
    PROPAQMD(2, "Propaq MD"),
    HEARTSTART(3, "HeartStart MRx"),
    COLLAR(4, "C-Collar"),
    VENTRIC(5, "Ventric"),
    PARYNCH(6, "Parynch"),
    ICU(7, "ICU"),
    CODMAN(8, "Codman Exprs"),
    VSB(9, "VSB"),
    NATO(10, "Nato Litter"),
    OSL(11, "OSL"),
    SMEED(12, "SMEED"),
    MERK(13, "Merk"),
    IV_PUMP(14, "IV Pump"),
    AMBIT(15, "ambIT"),
    EZ_IO(16, "EZ-IO"),
    SUCTION(17, "Suction"),
    VAC(18, "VAC"),
    AC_POWER(19, "AC Power"),
    ECAS(20, "ECAS"),
    FREQ(21, "Freq"),
    PTLOX(22, "PTLOX"),
    AC_O2(23, "AC O2"),
    ISTAT(24, "i-Stat"),
    LOADING(25, "Loading"),
    NGTUBE(27, "NG Tube"),
    FOLEY(28,"Foley"),
    INCUBATOR(29, "Incubator"),
    TRACTION(30, "Traction"),
    MONITOR(31, "Monitor"),
    TRACH(32, "Trach"),
    ORTHOPEDIC_DEVICES(33, "Orthopedic Devices"),
    RESTRAINTS(34, "Restraints"),
    CHEST_TUBES(35, "Chest Tubes"),
    IV(36, "IV"),
    CAST(37, "Cast"),
    BIVALVED(38, "Bivalved"),
    VENTILATOR(39, "Ventilator"),
    WOUND_VAC(40, "Wound Vac"),
    OTHER(41, "Other")
    ;


    companion object{
        fun getExtras(extras: Any): EquipmentData? = with(extras){
            when{
                isA<LoadingExtras>() -> LitterData(unpack())
                isA<NullableInt32Value>() -> (IntegerEquipmentData(unpack()))
                isA<NullableStringValue>() -> (StringEquipmentData(unpack()))
                isA<OrthopedicExtras>() -> (OrthopedicData(unpack()))
                else -> null
            }
        }

        fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)
    }
}

abstract class EquipmentData{
    abstract fun toProto(): Message?
    open fun toEventString(): String? = toString()
}


class IntegerEquipmentData(val int: Int? = null): EquipmentData(){

    internal constructor(intValue: NullableInt32Value): this(intValue.toPrimitive())
    override fun toProto() = nullableInt32Value(<EMAIL>)

    override fun toEventString()  = int?.toString() ?: ""
}

class StringEquipmentData(val string: String? = null): EquipmentData(){

    internal constructor(stringValue: NullableStringValue): this(stringValue.toPrimitive())

    override fun toProto() = nullableStringValue(<EMAIL>)

    override fun toEventString()  = string ?: ""
}




class LitterData(val isFront: Boolean? = null, val isHeadFirst: Boolean? = null, val isRight: Boolean? = null): EquipmentData() {
    internal constructor(extras: LoadingExtras): this(extras.locFrontOrNull?.value, extras.orientationHeadOrNull?.value, extras.locRightOrNull?.value)

    override fun toProto() = loadingExtras {
        isFront?.let { locFront = booleanValue(it) }
        isRight?.let { locRight = booleanValue(it) }
        isHeadFirst?.let { orientationHead = booleanValue(it) }
    }

    override fun toEventString() = listOfNotNull(
        when(isFront){
            true -> "facing front"
            false -> "facing rear"
            else -> null
        },
        when(isHeadFirst){
            true -> "head first"
            false -> "feet first"
            else -> null
        },
        when(isRight){
            true -> "right side"
            false -> "left side"
            else -> null
        }
    ).joinToString(", ").takeIf { it.isNotEmpty() }
}

class OrthopedicData(val type: String? = null, val subtype: String? = null, val location: String? = null): EquipmentData() {
    internal constructor(extras: OrthopedicExtras): this(
        OrthopedicType.fromProto(extras.type),
        OrthopedicSubtype.fromProto(extras.subtype),
        OrthopedicLocation.fromProto(extras.location),
    )

    override fun toProto() = orthopedicExtras {
        this.type = OrthopedicType.fromString(<EMAIL>)
        this.subtype = OrthopedicSubtype.fromString(<EMAIL>)
        this.location = OrthopedicLocation.fromString(<EMAIL>)
    }

    override fun toEventString() = listOfNotNull(
        (type ?: "Orthopedic") + if (subtype != null) {": $subtype"} else "",
        when(location) {
            null -> null
            else -> "Location: $location"
        }
    ).joinToString(", ").takeIf { it.isNotEmpty() }
}