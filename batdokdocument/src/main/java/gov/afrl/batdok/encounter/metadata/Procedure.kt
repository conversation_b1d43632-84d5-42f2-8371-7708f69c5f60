package gov.afrl.batdok.encounter.metadata

import com.google.protobuf.Any
import com.google.protobuf.Message
import com.google.protobuf.kotlin.isA
import com.google.protobuf.kotlin.unpack
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.StratevacMissionDataCommand.ChangeProcedureCommand
import gov.afrl.batdok.commands.proto.StratevacMissionDataCommand.LineExtras
import gov.afrl.batdok.commands.proto.changeProcedureCommand
import gov.afrl.batdok.commands.proto.lineExtras
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto
import java.io.Serializable

class Procedure(val name: String, val data: ProcedureData? = null): Serializable {
    fun toProto() = changeProcedureCommand {
        this.procedure = KnownProcedures.fromString(name)
        data?.toProto()?.let { this.extras = Any.pack(it, "") }
        checked = true
    }

    override fun equals(other: kotlin.Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as Procedure

        if (name != other.name) return false

        return true
    }

    override fun hashCode(): Int {
        return name.hashCode()
    }

    fun toEventString(includeExtras: Boolean = true): String {
        val extrasString = data
            ?.takeIf { includeExtras }
            ?.toEventString()
            ?.let {
                " ($it)"
            } ?: ""
        return "$name$extrasString"
    }

    override fun toString() = toEventString(true)

    companion object{
        fun fromProto(procedureItem: ChangeProcedureCommand): Procedure? {
            val name = KnownProcedures.fromProto(procedureItem.procedure) ?: return null
            val data = KnownProcedures.getExtras(procedureItem.extras)
            return Procedure(name, data)
        }
    }
}

enum class KnownProcedures(override val protoIndex: Int, override val dataString: String): ProtoEnum{
    INTUBATE(1, "Intubate"),
    SURGICAL_AIRWAY(2, "Surgical Airway"),
    CPR(3, "CPR"),
    CHEST_TUBE(4, "Chest Tube"),
    SPLINT_MOD(5, "Splint Mod"),
    HEMORRHAGE_CONTROL(6, "Hemorrhage Control"),
    DRESSING_CHANGE(7, "Dressing Change"),
    NG_OG(8, "NG/OG"),
    FOLEY(9, "Foley"),
    ESCHAROTOMY(10, "Escharotomy"),
    PIV(11, "PIV"),
    ABG(12, "ABG"),
    VBG(13, "VBG"),
    LINE(14, "Line"),
    ;

    companion object{
        fun getExtras(extras: Any): ProcedureData? = with(extras){
            when{
                isA<LineExtras>() -> LineData(unpack())
                else -> null
            }
        }

        fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)
    }
}

abstract class ProcedureData: Serializable{
    abstract fun toProto(): Message?
    open fun toEventString(): String? = toString()
}

class LineData(val arterialLine: Boolean = false, val centralLine: Boolean = false): ProcedureData() {
    internal constructor(extras: LineExtras): this(extras.artLine, extras.centralLine)

    override fun toProto() = lineExtras {
        artLine = arterialLine
        centralLine = <EMAIL>
    }

    override fun toEventString() = listOfNotNull(
        "Arterial Line".takeIf { arterialLine },
        "Central Line".takeIf { centralLine }
    ).joinToString(", ").takeIf { it.isNotEmpty() }
}