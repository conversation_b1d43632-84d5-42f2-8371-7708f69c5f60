package gov.afrl.batdok.encounter.commands

import gov.afrl.batdok.commands.eventHandler
import gov.afrl.batdok.commands.handler
import gov.afrl.batdok.commands.handlerWithId
import gov.afrl.batdok.commands.proto.StratevacRnIoCommands.*
import gov.afrl.batdok.commands.proto.addIntakeOutputItem
import gov.afrl.batdok.commands.proto.removeIntakeOutputItem
import gov.afrl.batdok.commands.proto.updateIntakeOutputItem
import gov.afrl.batdok.encounter.IntakeOutput
import gov.afrl.batdok.encounter.IntakeOutputs
import gov.afrl.batdok.encounter.KnownEventTypes
import gov.afrl.batdok.encounter.ids.toByteString
import gov.afrl.batdok.encounter.ids.toDomainId
import gov.afrl.batdok.encounter.ids.toDomainIdOrElse
import gov.afrl.batdok.util.addPreviousFields
import gov.afrl.batdok.util.int64Value
import gov.afrl.batdok.util.removeDuplicates
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant

fun buildAddIntakeOutputItemCommand(item: IntakeOutput) = addIntakeOutputItem{
    this.data = item.toProto()
    this.id = item.id.toByteString()
}
fun addIntakeOutputItemCommandHandler(inputOutputs: IntakeOutputs) = handlerWithId<AddIntakeOutputItem> { docId, command ->
    val data = IntakeOutput(
        id.toDomainIdOrElse(command.commandId),
        docId,
        Instant.ofEpochSecond(command.timestamp),
        this.data
    )

    inputOutputs += data

}
fun addIntakeOutputItemEventCommandHandler() = eventHandler<AddIntakeOutputItem> (KnownEventTypes.TREAT.dataString){
    val data = IntakeOutput(
        it.commandId.toDomainId(),
        DomainId.nil(),
        Instant.ofEpochSecond(it.timestamp),
        this.data
    )
    val direction = if(data.isIntake) "Intake" else "Output"
    "Logged $direction:\n${data.toEventString()}"
}


private fun List<IntakeOutput>.withNew(io: IntakeOutput) = filter{ it.id != io.id } + io
fun buildUpdateIntakeOutputItemCommand(item: IntakeOutput, isInput: Boolean, oldValue: IntakeOutput? = null) = updateIntakeOutputItem{
    this.data = item.toProto().removeDuplicates(oldValue?.toProto())
    this.itemId = item.id.toByteString()
}
fun updateIntakeOutputItemCommandHandler(inputOutputs: IntakeOutputs) = handlerWithId<UpdateIntakeOutputItem> { docId, command ->
    val oldItem = inputOutputs[itemId.toDomainId()]
    val oldCommand = oldItem?.toProto()
    val restoredCommand = IntakeOutputItem.parseFrom(this.data.addPreviousFields(oldCommand).toByteArray())
    val data = IntakeOutput(
        itemId.toDomainId(),
        docId,
        Instant.ofEpochSecond(restoredCommand.timestamp.value),
        restoredCommand
    )

    inputOutputs += data

}
fun updateIntakeOutputItemEventCommandHandler(inputOutputs: IntakeOutputs) = eventHandler<UpdateIntakeOutputItem>(
    KnownEventTypes.EVAC.dataString,
    referenceId = { itemId.toDomainId() },
    handler = {
        val oldCommand = inputOutputs[itemId.toDomainId()]?.toProto()
        val restoredCommand = IntakeOutputItem.parseFrom(this.data.addPreviousFields(oldCommand).toByteArray())
        val data = IntakeOutput(
            itemId.toDomainId(),
            DomainId.nil(),
            Instant.ofEpochSecond(data.timestamp.value),
            restoredCommand
        )
        val direction = if(data.isIntake) "Intake" else "Output"
        "Updated $direction:\n${data.toEventString()}"
    }
)

fun buildRemoveIntakeOutputItem(id: DomainId) = removeIntakeOutputItem {
    this.itemId = id.toByteString()
}
fun removeIntakeOutputItemCommandHandler(inputOutputs: IntakeOutputs) = handler<RemoveIntakeOutputItem> {
    inputOutputs.removeItem(itemId.toDomainId(), Instant.ofEpochSecond(it.timestamp))
}
fun removeIntakeOutputItemEventCommandHandler(inputOutputs: IntakeOutputs) = eventHandler<RemoveIntakeOutputItem> (
    KnownEventTypes.TREAT.dataString,
    referenceId = { itemId.toDomainId() }
){
    val data = inputOutputs[itemId.toDomainId()] ?: return@eventHandler null
    val direction = if(data in inputOutputs.inputs) "Intake" else "Output"
    "Removed $direction:\n${data.toEventString()}"
}