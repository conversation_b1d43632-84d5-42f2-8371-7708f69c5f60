package gov.afrl.batdok.encounter.orders

import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto
import gov.afrl.batdok.util.toChangeOrClearEvent

enum class OrderType(override val dataString: String, override val protoIndex: Int): ProtoEnum {
    CUSTOM("Custom", 1),
    MEDICATION("Medication", 2);
    companion object{
        fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = OrderType.values().protoToString(enum) ?: CUSTOM.dataString
        fun fromString(string: String?) = OrderType.values().stringToProto(string)
        fun toEvent(enum: SharedProtobufObjects.CompatibleEnum) = OrderType.values()
            .toChangeOrClearEvent(enum, "order type")
    }
}