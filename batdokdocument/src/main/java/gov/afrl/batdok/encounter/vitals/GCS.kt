package gov.afrl.batdok.encounter.vitals

import gov.afrl.batdok.commands.proto.VitalOuterClass
import gov.afrl.batdok.commands.proto.gCS
import gov.afrl.batdok.encounter.IndividualVital

class GCS(var eye: Int = -1, var verbal: Int = -1, var motor: Int = -1, var intubated: Int = -1): IndividualVital("GCS") {

    constructor(gcs: VitalOuterClass.GCS): this(gcs.eye, gcs.verbal, gcs.motor, gcs.intubated)

    override fun produceEmptyVital() = GCS()

    override fun toProtobuf() = gCS {
        this.eye = <EMAIL>
        this.verbal = <EMAIL>
        this.motor = <EMAIL>
        this.intubated = <EMAIL>
    }

    override val eventMessage: String
        get() = getGCSEventString()

    override val isEmpty: Boolean
        get() = eye == -1 || verbal == -1 || motor == -1 || intubated == -1

    val total
        get() = eye + verbal + motor

    fun getGCSEventString(): String {
        if (eye == 0 || verbal == 0 || motor == 0) {
            return "E(${eye.takeUnless { it == 0 } ?: "NT"})V(${verbal.takeUnless { it == 0 } ?: "NT"})M(${motor.takeUnless { it == 0 } ?: "NT"})${if(intubated == 1) {"T"} else {""}}"
        }
        return "$total (E${eye} V${verbal} M${motor})${if(intubated == 1) {"T"} else {""}}"
    }
}