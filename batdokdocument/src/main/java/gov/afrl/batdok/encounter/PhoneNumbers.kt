package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.commOrNull
import gov.afrl.batdok.commands.proto.dsnOrNull
import gov.afrl.batdok.commands.proto.phoneNumbers
import gov.afrl.batdok.commands.proto.valueOrNull
import gov.afrl.batdok.util.nullableStringValue
import java.io.Serializable

data class PhoneNumbers(
    val dsn: String? = null,
    val comm: String? = null
) : Serializable {
    constructor(proto: SharedProtobufObjects.PhoneNumbers): this(
        proto.dsnOrNull?.valueOrNull?.value,
        proto.commOrNull?.valueOrNull?.value
    )

    override fun toString() = listOfNotNull(
        "DSN: $dsn".takeUnless { dsn.isNullOrBlank() },
        "Comm: $comm".takeUnless { comm.isNullOrBlank() }
    ).joinToString(", ")

    fun isBlank() = dsn.isNullOrBlank() && comm.isNullOrBlank()

    fun toProto() = phoneNumbers {
        dsn = nullableStringValue(<EMAIL>)
        comm = nullableStringValue(<EMAIL>)
    }
}
