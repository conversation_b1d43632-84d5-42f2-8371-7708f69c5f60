package gov.afrl.batdok.encounter.commands

import com.google.protobuf.Any
import com.google.protobuf.Empty
import com.google.protobuf.copy
import gov.afrl.batdok.commands.eventHandler
import gov.afrl.batdok.commands.handler
import gov.afrl.batdok.commands.handlerWithId
import gov.afrl.batdok.commands.proto.*
import gov.afrl.batdok.commands.proto.ObjectiveCommands.PhysicalExamFinding
import gov.afrl.batdok.commands.proto.Observations.*
import gov.afrl.batdok.encounter.KnownEventTypes
import gov.afrl.batdok.encounter.Links
import gov.afrl.batdok.encounter.Observations
import gov.afrl.batdok.encounter.ids.ObservationId
import gov.afrl.batdok.encounter.ids.toByteString
import gov.afrl.batdok.encounter.ids.toDomainId
import gov.afrl.batdok.encounter.ids.toDomainIdOrElse
import gov.afrl.batdok.encounter.observation.CommonObservations
import gov.afrl.batdok.encounter.observation.Observation
import gov.afrl.batdok.encounter.observation.ObservationData
import gov.afrl.batdok.util.addPreviousFields
import gov.afrl.batdok.util.removeDuplicates
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant

fun buildUpdatePhysicalExamFindingCommand(category: String, finding: String) = physicalExamFinding {
    this.tab = category
    this.finding = finding
}
fun updatePhysicalExamFindingCommandHandler(findings: MutableMap<String, String>) =
    handler<PhysicalExamFinding> { findings[tab] = finding }
fun updatePhysicalExamFindingEventCommandHandler() = eventHandler<PhysicalExamFinding>(KnownEventTypes.OBJECTIVE.dataString) {
    "$tab Physical Exam: $finding"
}

fun buildLogObservationCommand(name: String, data: ObservationData? = null, id: ObservationId = DomainId.nil(), timestamp: Instant? = Instant.now()) = logObservation {
    observation = observation {
        this.name = CommonObservations.fromString(name)
        data?.toProtobuf()?.let { this.data = Any.pack(it, "") }
        timestamp?.let { this.timestamp = it.epochSecond }
    }
    this.id = id.toByteString()
}
fun logObservationCommandHandler(observations: Observations) = handlerWithId<LogObservation>{ docId, command ->
    observations += Observation(
        CommonObservations.fromProto(observation.name),
        CommonObservations.getObservationData(observation.data),
        id.toDomainIdOrElse(command.commandId),
        Instant.ofEpochSecond(observation.timestamp),
        docId
    )
}
fun logObservationCommandEventHandler() = eventHandler<LogObservation>(KnownEventTypes.OBSERVATIONS.dataString) {
    "Added " + Observation(
        CommonObservations.fromProto(observation.name),
        CommonObservations.getObservationData(observation.data),
        it.commandId.toDomainId(),
        Instant.ofEpochSecond(it.timestamp)
    ).toEvent()
}

fun buildUpdateObservationCommand(observation: Observation, oldObservation: Observation? = null) = updateObservation {
    this.id = observation.id.toByteString()
    this.timestamp = observation.timestamp.epochSecond
    val oldTreatmentProtobuf = oldObservation?.observationData?.toProtobuf()
    observation.observationData?.toProtobuf()?.let { this.data = Any.pack(it.removeDuplicates(oldTreatmentProtobuf), "") }
}
fun updateObservationCommandHandler(observations: Observations) = handlerWithId<UpdateObservation>{ docId, _ ->
    val observation = observations[id.toDomainId<ObservationId>()]
    val restoredData = Empty.parseFrom(data.value).addPreviousFields(observation?.observationData?.toProtobuf())
    observations += Observation(
        observation?.name,
        CommonObservations.getObservationData(data.copy{
            value = restoredData.toByteString()
        }),
        id.toDomainId(),
        Instant.ofEpochSecond(timestamp),
        docId
    )
}
fun updateObservationCommandEventHandler(observations: Observations) = eventHandler<UpdateObservation>(
    KnownEventTypes.OBSERVATIONS.dataString,
    referenceId = { id.toDomainId() },
    handler = {
        val observation = observations[id.toDomainId<ObservationId>()]
        val restoredData = Empty.parseFrom(data.value).addPreviousFields(observation?.observationData?.toProtobuf())
        "Updated " + Observation(
            observation?.name,
            CommonObservations.getObservationData(data.copy{
                value = restoredData.toByteString()
            }),
            id.toDomainId(),
            Instant.ofEpochSecond(timestamp)
        ).toEvent()
    }
)

fun buildRemoveObservationCommand(id: DomainId, documentationError: Boolean) = removeObservation {
    this.id = id.toByteString()
    this.documentationError = documentationError
}
@Deprecated("Use removeObservationCommandHandler(observations, links) instead")
fun removeObservationCommandHandler(observations: Observations) = handler<RemoveObservation>{
    observations.removeItem(id.toDomainId(), Instant.ofEpochSecond(it.timestamp), documentationError)
}
internal fun removeObservationCommandHandler(observations: Observations, links: Links) = handler<RemoveObservation>{
    observations.removeItem(id.toDomainId(), Instant.ofEpochSecond(it.timestamp), documentationError)
    links.removeLinkedItem(id.toDomainId(), Instant.ofEpochSecond(it.timestamp))
}
fun removeObservationEventCommandHandler(observations: Observations) = eventHandler<RemoveObservation>(
    KnownEventTypes.OBSERVATIONS.dataString,
    referenceId = { id.toDomainId() }
){
    val observation = observations[id.toDomainId<ObservationId>()] ?: return@eventHandler null
    "Removed " + observation.toEvent() + (if (documentationError) " due to documentation error" else "")
}