package gov.afrl.batdok.encounter.observation

import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.StratevacRnBloodCommands
import gov.afrl.batdok.commands.proto.bloodProduct
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.util.*
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant
import java.time.LocalDate

data class Blood(
    val bloodId: DomainId = DomainId.create(),
    val administrationTime: Instant? = Instant.now(),
    val bloodProduct: String? = null,
    val productCode: String? = null,
    val bloodType: String? = null,
    val donationIdNumber: String? = null,
    val expirationDate: String? = null,
    val collectionDate: LocalDate? = null,
    val bloodAge: Int? = null,
    val bloodAgeUnit: String? = null,
    val volume: Long? = null,
    val unit: String? = null,
    val route: String? = null,
    val docId: DocumentId = DomainId.nil()
): DocumentItem<DomainId>(bloodId, docId, bloodProduct, administrationTime) {
    internal constructor(
        protoBlood: StratevacRnBloodCommands.BloodProduct,
        id: DomainId = DomainId.create(),
        docId: DocumentId = DomainId.nil(),
        startTime: Instant = Instant.now()
    ): this(
        bloodId = id,
        administrationTime = startTime,
        bloodProduct = BloodProduct.fromProto(protoBlood.bloodName),
        productCode = protoBlood.productCode?.toPrimitive(),
        bloodType = protoBlood.bloodType.toPrimitive(),
        donationIdNumber = protoBlood.donationIdNumber?.toPrimitive(),
        expirationDate = protoBlood.expirationDate?.toPrimitive(),
        collectionDate = protoBlood.collectionDate.toLocalDate(),
        bloodAge = protoBlood.bloodAge.toPrimitive(),
        bloodAgeUnit = AgeUnit.fromProto(protoBlood.bloodAgeUnit),
        volume = protoBlood.volume.toPrimitive(),
        unit = protoBlood.unit.toPrimitive(),
        route = protoBlood.route.toPrimitive(),
        docId = docId
    )

    enum class BloodProduct(override val protoIndex: Int, override val dataString: String): ProtoEnum{
        LTOWB(1, "Cold Stored - Low Titer Group O (LTOWB)"),
        PACKED_RED_BLOOD_CELLS(2, "Packed Red Blood Cells (PRBC)"),
        FRESH_WHOLE_BLOOD(3, "Fresh"),
        @Deprecated("Deprecated do not use")
        LIQUID_PLASMA(4, "Liquid Plasma"),
        THAWED_PLASMA(5, "Fresh Frozen Plasma (FFP)"),
        FREEZE_DRIED_PLASMA(6, "Freeze Dried Plasma (FDP)"),
        PLATELETS(7, "Platelets (plt)"),
        CRYOPRECIPITATE(8, "Cryoprecipitate"),
        CSWB(9, "Cold Stored"),
        @Deprecated("Deprecated do not use")
        LTOCSWB(10, "Low Titer O Cold Stored Whole Blood (LTOCSWB)"),
        FRESH_FROZEN_PLASMA(11, "Fresh Frozen Plasma"),
        ;

        companion object{
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = entries.toTypedArray().protoToString(enum)
            fun fromString(string: String?) = entries.toTypedArray().stringToProto(string)
        }
    }

    enum class AgeUnit(override val protoIndex: Int, override val dataString: String): ProtoEnum{
        DAYS(1, "days"),
        MONTHS(2, "months"),
        YEARS(3, "years");

        companion object{
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = entries.toTypedArray().protoToString(enum)
            fun fromString(string: String?) = entries.toTypedArray().stringToProto(string)
        }
    }

    fun toProto() = bloodProduct{
        bloodName = BloodProduct.fromString(<EMAIL>)
        productCode = nullableStringValue(<EMAIL>)
        donationIdNumber = nullableStringValue(<EMAIL>)
        expirationDate = nullableStringValue(<EMAIL>)
        collectionDate = nullableInt64Value(<EMAIL>?.toEpochDay())
        bloodType = compatibleEnum(<EMAIL>)
        bloodAge = nullableInt32Value(<EMAIL>)
        bloodAgeUnit = AgeUnit.fromString(<EMAIL>)
        volume = nullableInt64Value(<EMAIL>)
        unit = compatibleEnum(<EMAIL>)
        route = compatibleEnum(<EMAIL>)
    }

    fun toEventString(): String {
        val extras = listOfNotNull(
            //Only display blood age unit if both it and the age are not null
            bloodAge?.let { listOfNotNull(it, bloodAgeUnit).joinToString(" ") },
            expirationDate.takeUnless { it.isNullOrEmpty() }
        ).joinToString(", ")
        val bProduct = "$bloodProduct: ".takeUnless { bloodProduct.isNullOrEmpty() } ?: ": "
        return bProduct +
                listOfNotNull(
                    "Type: $bloodType".takeUnless { bloodType.isNullOrEmpty() },
                    donationIdNumber.takeUnless { donationIdNumber.isNullOrEmpty() },
                    getVolumeString().takeUnless { it.isEmpty() },
                    route.takeUnless { route.isNullOrEmpty() },
                    "($extras)".takeUnless { extras.isEmpty()}
                ).joinToString(", ")
    }

    fun getVolumeString() : String {
        return listOfNotNull(volume?.toString()?.removeSuffix(".0"), unit)
            .joinToString(" ")
            .trim()
    }
}