package gov.afrl.batdok.encounter

import com.google.protobuf.Any
import com.google.protobuf.Message
import com.google.protobuf.kotlin.isA
import com.google.protobuf.kotlin.unpack
import gov.afrl.batdok.commands.proto.SubjectiveCommands.BloodDonationExtra
import gov.afrl.batdok.commands.proto.bloodDonationExtra
import java.time.Instant

data class LastEventTime(val time: Instant, val extras: Extras? = null) {
    sealed interface Extras {
        fun toProtobuf(): Message
        fun toEventText(): String = ""
    }
    companion object {
        fun getExtras(any: Any) : Extras? = when {
            any.isA<BloodDonationExtra>() -> BloodDonationExtras(any.unpack())
            else -> null
        }
    }
}

data class BloodDonationExtras(val volume: Int) : LastEventTime.Extras {
    constructor(extras: BloodDonationExtra) : this(extras.volume)
    override fun toProtobuf() = bloodDonationExtra {
        this.volume = <EMAIL>
    }

    override fun toEventText(): String {
        return "$volume Units"
    }
}

