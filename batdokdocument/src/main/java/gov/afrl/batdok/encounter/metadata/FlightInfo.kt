package gov.afrl.batdok.encounter.metadata

import mil.af.afrl.batman.batdokid.DomainId
import java.io.Serializable
import java.time.Duration
import java.time.Instant

data class FlightInfo(val id: FlightInfoId = DomainId.nil(),
                      var tail: String = "",
                      var origin: String = "",
                      var destination: String = "",
                      var takeoff: Instant? = null,
                      var landing: Instant? = null,
                      var maxAlt: Long? = null,
                      var unit: String = "") : Serializable, Comparable<FlightInfo> {
    override fun compareTo(other: FlightInfo): Int {
        return when {
            (takeoff ?: Instant.now()) == (other.takeoff ?: Instant.now()) -> tail.compareTo(other.tail)
            takeoff != null && other.takeoff != null -> takeoff!!.compareTo(other.takeoff)
            takeoff == null -> -1
            other.takeoff == null -> 1
            else -> tail.compareTo(other.tail)
        }
    }

    @Deprecated("Use timeEnRoute() instead", ReplaceWith("timeEnRoute()"))
    fun hoursEnRoute(): Float? {
        return timeEnRoute()?.toMinutes()?.div(3600f)
    }
    fun timeEnRoute(): Duration? {
        val landing = landing?: Instant.now()
        val takeoff = takeoff?: return null
        return Duration.between(takeoff, landing)
    }
}

class FlightInfoId(data:ByteArray) : DomainId(data)