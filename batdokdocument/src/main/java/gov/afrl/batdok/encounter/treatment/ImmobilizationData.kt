package gov.afrl.batdok.encounter.treatment

import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.TreatmentCommands
import gov.afrl.batdok.commands.proto.immobilization
import gov.afrl.batdok.util.*

data class NeurovascularStatus(
    val pulse: ImmobilizationData.TernaryStatus? = null,
    val motor: ImmobilizationData.TernaryStatus? = null,
    val sensory: ImmobilizationData.TernaryStatus? = null,
)

class ImmobilizationData(
    val type: String? = null,
    val subtype: String? = null,
    var location: String? = null,
    val neurovascularBefore: NeurovascularStatus? = null,
    val neurovascularAfter: NeurovascularStatus? = null,
): TreatmentData {
    override fun toProtobuf(): Message = immobilization {
        type = Type.fromString(<EMAIL>)
        subtype = compatibleEnum(<EMAIL>)
        location = Location.fromString(<EMAIL>)

        neurovascularBeforePulse = compatibleEnum(neurovascularBefore?.pulse?.dataString)
        neurovascularBeforeMotor = compatibleEnum(neurovascularBefore?.motor?.dataString)
        neurovascularBeforeSensory = compatibleEnum(neurovascularBefore?.sensory?.dataString)

        neurovascularAfterPulse = compatibleEnum(neurovascularAfter?.pulse?.dataString)
        neurovascularAfterMotor = compatibleEnum(neurovascularAfter?.motor?.dataString)
        neurovascularAfterSensory = compatibleEnum(neurovascularAfter?.sensory?.dataString)
        
    }

    override fun toDetailString(): String {
        return listOfNotNull(
            type,
            subtype,
            location,
            neurovascularBefore?.toDetailString("Neuro Before"),
            neurovascularAfter?.toDetailString("Neuro After"),
            ).joinToString(", ")
    }

    fun NeurovascularStatus.toDetailString(prefix: String = ""): String? {
        val details = listOfNotNull(
            pulse?.dataString?.toDetailString("Pulse"),
            motor?.dataString?.toDetailString("Motor"),
            sensory?.dataString?.toDetailString("Sensory")
        ).joinToString(", ")

        return if (details.isNotBlank()) "$prefix - $details" else null
    }

    companion object {
        fun fromProtobuf(immobilization: TreatmentCommands.Immobilization): ImmobilizationData {
            return ImmobilizationData(
                Type.fromProto(immobilization.type),
                subtype = null,
                Location.fromProto(immobilization.location),
                neurovascularBefore = NeurovascularStatus(
                    pulse = TernaryStatus.fromString(immobilization.neurovascularBeforePulse.toPrimitive()),
                    motor = TernaryStatus.fromString(immobilization.neurovascularBeforeMotor.toPrimitive()),
                    sensory = TernaryStatus.fromString(immobilization.neurovascularBeforeSensory.toPrimitive())
                ),
                neurovascularAfter = NeurovascularStatus(
                    pulse = TernaryStatus.fromString(immobilization.neurovascularAfterPulse.toPrimitive()),
                    motor = TernaryStatus.fromString(immobilization.neurovascularAfterMotor.toPrimitive()),
                    sensory = TernaryStatus.fromString(immobilization.neurovascularAfterSensory.toPrimitive())
                )
            )
        }
    }

    enum class Type(override val protoIndex: Int, override val dataString: String) : ProtoEnum {
        C_COLLAR(1, "C-Collar"),
        @Deprecated("no longer an option within BATDOK")
        C_SPINE(2, "C-Spine"),
        SPINE_BOARD(3, "Spine Board"),
        @Deprecated(
            "Use standalone pelvic binder treatment instead",
            replaceWith = ReplaceWith("PELVIC_BINDER")
        )
        PELVIC_SPLINT(4, "Pelvic Splint"),
        @Deprecated("Use standalone pelvic binder treatment instead")
        PELVIC_BINDER(5, "Pelvic Binder"),
        @Deprecated("no longer an option within BATDOK")
        BUDDY_TAPE_TIE(6, "Buddy Tape/Tie"),
        @Deprecated("no longer an option within BATDOK")
        KED(7, "KED"),
        @Deprecated("no longer an option within BATDOK")
        LSB(8, "LSB"),
        @Deprecated("no longer an option within BATDOK")
        SWING_SWATH(9, "Swing/Swath"),
        AIR(10, "Air"), //added 5.1.0
        IMPROVISED(11, "Improvised"),
        SAM_SPLINT(12, "Sam Splint"),
        CT6(13, "CT6/Traction"),
        SWATH(14, "Swath"),
        ;

        companion object {
            fun find(dataString: String?) = Type.entries.find { it.dataString == dataString }
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = entries.toTypedArray().protoToString(enum)
            fun fromString(string: String?) =
                entries.toTypedArray()
                    .stringToProto(string, listOf(AIR, IMPROVISED, SAM_SPLINT, CT6, SWATH))
        }
    }

    enum class Location(override val protoIndex: Int, override val dataString: String) : ProtoEnum {
        RIGHT_UPPER_ARM(1, "Right Upper Arm"),
        RIGHT_LOWER_ARM(2, "Right Lower Arm"),
        RIGHT_ARM(3, "Right Arm"),
        RIGHT_UPPER_LEG(4, "Right Upper Leg"),
        RIGHT_LOWER_LEG(5, "Right Lower Leg"),
        LEFT_UPPER_ARM(6, "Left Upper Arm"),
        LEFT_LOWER_ARM(7, "Left Lower Arm"),
        LEFT_ARM(8, "Left Arm"),
        LEFT_UPPER_LEG(9, "Left Upper Leg"),
        LEFT_LOWER_LEG(10, "Left Lower Leg"),
        RIBS(11, "Ribs"),
        PELVIS(12, "Pelvis"),
        ;

        companion object {
            fun find(dataString: String?) = Location.entries.find { it.dataString == dataString }
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) =
                entries.toTypedArray().protoToString(enum)
            fun fromString(string: String?) = entries.toTypedArray().stringToProto(string)
        }
    }

    enum class TernaryStatus(
        val protoIndex: Int,
        val dataString: String,
        val boolValue: Boolean?
    ) {
        YES(1, "Yes", true),
        NO(2, "No", false),
        UNKNOWN(3, "Unknown", null);

        companion object {
            fun fromBool(value: Boolean?): TernaryStatus =
                entries.first { it.boolValue == value }

            fun fromString(value: String?): TernaryStatus? =
                entries.firstOrNull { it.dataString.equals(value, ignoreCase = true) }
        }
    }
}