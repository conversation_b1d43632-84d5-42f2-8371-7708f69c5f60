package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.proto.SharedProtobufObjects.CompatibleEnum

/** Patient category. Contains a service and a status. */
data class Patcat(
    var service: PatcatService?,
    var status: PatcatStatus?
    // TODO: add subcategory field
) {
    var serviceCode: String? = service?.dataString
    var statusCode: String? = status?.dataString

    constructor(serviceCode: String?, statusCode: String?): this(
        PatcatService.entries.find { it.dataString == serviceCode },
        PatcatStatus.entries.find { it.dataString == statusCode }
    ) {
        this.serviceCode = serviceCode
        this.statusCode = statusCode
    }

    /** An alphanumeric code used to represent this patcat (e.g. A11 or F13) */
    fun getCode() = (serviceCode ?: "") + (statusCode ?: "")

    fun getShortDescription() = listOfNotNull(service?.abbreviation ?: serviceCode, status?.shortDescription ?: statusCode).joinToString(" ")

    fun getFullDescription() = listOfNotNull(service?.abbreviation ?: serviceCode, status?.fullDescription ?: statusCode).joinToString(" ")

    fun toEvent(): String {
        return changeOrClearEvent(
            !isEmpty(),
            getEventDescription(),
            "patcat"
        )
    }

    fun getEventDescription() = "${getCode()} - ${getFullDescription()}"

    /** Determines whether the service is a valid [PatcatService] and the status is a valid [PatcatStatus] for that service. */
    fun isValid() = service != null && status != null && service!!.validStatuses.contains(status)

    fun isEmpty() = service == null && status == null && serviceCode == null && statusCode == null

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as Patcat

        if (service != other.service ||
            status != other.status ||
            serviceCode != other.serviceCode ||
            statusCode != other.statusCode)
            return false

        return true
    }

    override fun hashCode(): Int {
        var result = service?.hashCode() ?: 0
        result = 31 * result + (status?.hashCode() ?: 0)
        result = 31 * result + (serviceCode?.hashCode() ?: 0)
        result = 31 * result + (statusCode?.hashCode() ?: 0)

        return result
    }

    companion object {
        fun fromProto(service: CompatibleEnum, status: CompatibleEnum) = Patcat(PatcatService.fromProto(service), PatcatStatus.fromProto(status))

        fun getMapOfValidPatCats(): Map<String, Patcat>  {
            val result = mutableMapOf<String, Patcat>()
            PatcatService.entries.forEach { service ->
                service.validStatuses.forEach { status ->
                    val patcat = Patcat(service, status)
                    result[patcat.getEventDescription()] = patcat
                }
            }
            return result
        }
    }
}