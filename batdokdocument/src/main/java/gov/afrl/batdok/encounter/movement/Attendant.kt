package gov.afrl.batdok.encounter.movement

import gov.afrl.batdok.commands.CommandHandler
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.commands.proto.MovementCommands
import gov.afrl.batdok.commands.proto.attendant
import gov.afrl.batdok.encounter.Name
import gov.afrl.batdok.encounter.commands.*
import java.io.Serializable

class Attendant(
    var name: Name? = null,
    var gender: String? = "",
    var weight: Float? = null,
    var grade: String? = "",
    var isMedical: Boolean? = null
): Serializable {

    @Transient val handlers = CommandHandler().apply {
        val attendant = this@Attendant
        +changeNameCommandHandler(attendant)
        +changeGenderCommandHandler(attendant)
        +changeWeightCommandHandler(attendant)
        +changeGradeCommandHandler(attendant)
        +changeIsMedicalCommandHandler(attendant)
    }

    fun toProto() = attendant {
        <EMAIL>?.first?.let { this.firstName = it }
        <EMAIL>?.middle?.let {this.middleName = it }
        <EMAIL>?.last?.let {this.lastName = it }
        <EMAIL>?.let { this.gender = it }
        <EMAIL>?.let { this.weight = it }
        <EMAIL>?.let { this.grade = it }
        <EMAIL>?.let { this.isMedical = it }
    }

    companion object{
        fun fromProto(attendant: MovementCommands.Attendant) = Attendant(
                name = Name(attendant.firstName, attendant.middleName, attendant.lastName),
                gender = attendant.gender,
                weight = attendant.weight,
                grade = attendant.grade,
                isMedical = attendant.isMedical
            )
        fun EventCommandHandler.includeAttendantCommands(){
            +changeNameEventHandler()
            +changeGenderEventHandler()
            +changeWeightEventHandler()
            +changeGradeEventHandler()
            +changeIsMedicalEventHandler()
        }
    }
}