package gov.afrl.batdok.encounter.commands

import gov.afrl.batdok.commands.eventHandler
import gov.afrl.batdok.commands.handler
import gov.afrl.batdok.commands.handlerWithId
import gov.afrl.batdok.commands.proto.StratevacRnBloodCommands.*
import gov.afrl.batdok.commands.proto.logBloodCommand
import gov.afrl.batdok.commands.proto.removeBloodCommand
import gov.afrl.batdok.commands.proto.updateBloodCommand
import gov.afrl.batdok.encounter.KnownEventTypes
import gov.afrl.batdok.encounter.ids.toByteString
import gov.afrl.batdok.encounter.ids.toDomainId
import gov.afrl.batdok.encounter.ids.toDomainIdOrElse
import gov.afrl.batdok.encounter.observation.Blood
import gov.afrl.batdok.encounter.observation.BloodList
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant

fun buildLogBloodCommand(blood: Blood) = logBloodCommand{
    this.bloodProduct = blood.toProto()
    this.id = blood.id.toByteString()
}
fun logBloodCommandHandler(bloodList: BloodList) = handlerWithId<LogBloodCommand>{ docId, command ->
    bloodList += Blood(
        bloodProduct,
        id.toDomainIdOrElse(command.commandId),
        docId,
        // Use the timestamp from the command because we dont allow intial time to be anything else
        startTime = Instant.ofEpochSecond(command.timestamp)
    )
}

fun logBloodCommandEventHandler() = eventHandler<LogBloodCommand>(KnownEventTypes.MEDS.dataString){
    "Logged Blood Item ${Blood(bloodProduct).toEventString()}"
}
fun buildUpdateBloodCommand(blood: Blood) = updateBloodCommand {
    this.id = blood.bloodId.toByteString()
    blood.administrationTime?.let{ this.timestamp = it.epochSecond }
    this.bloodProduct = blood.toProto()
}
fun updateBloodCommandHandler(bloodList: BloodList) = handlerWithId<UpdateBloodCommand>{ docId, _ ->
    bloodList += Blood(
        bloodProduct,
        id.toDomainId(),
        docId,
        // Use the timestamp from the update blood command because it could be different than the command data time
        startTime = Instant.ofEpochSecond(timestamp)
    )
}
fun updateBloodCommandEventHandler() = eventHandler<UpdateBloodCommand>(
    KnownEventTypes.MEDS.dataString,
    referenceId = { id.toDomainId() }
){
    "Updated Blood Item ${Blood(bloodProduct).toEventString()}"
}
fun buildRemoveBloodCommand(id: DomainId, docError: Boolean) = removeBloodCommand {
    this.id = id.toByteString()
    this.documentationError = docError
}
fun removeBloodCommandHandler(bloodList: BloodList) = handler<RemoveBloodCommand> {
    bloodList.removeItem(DomainId(id.toByteArray()), Instant.ofEpochSecond(it.timestamp))
}
fun removeBloodCommandEventHandler(bloodList: BloodList) = eventHandler<RemoveBloodCommand> (
    KnownEventTypes.MEDS.dataString,
    referenceId = { id.toDomainId() }
){
    val blood = bloodList[id.toDomainId<DomainId>()] ?: return@eventHandler null
    "Removed Blood Item " + blood.toEventString() + (if (documentationError) " due to documentation error" else "")
}