package gov.afrl.batdok.encounter.treatment

import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.TreatmentCommands
import gov.afrl.batdok.commands.proto.woundPackingData
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

data class WoundPackingData(
    val location: String? = null,
    val type: String? = null,
): TreatmentData {

    constructor(dressing: TreatmentCommands.WoundPackingData):this(
        Location.fromProto(dressing.location),
        Type.fromProto(dressing.type),
    )

    override fun toProtobuf(): Message = woundPackingData{
        type = Type.fromString(<EMAIL>)
        location = Location.fromString(<EMAIL>)
    }

    override fun toDetailString(): String {
        return listOfNotNull(
            type.toDetailString("Type"),
            location.toDetailString("Location"),
        ).joinToString(", ")
    }


    enum class Location(override val protoIndex: Int, override val dataString: String): ProtoEnum {
        LUE(1, "LUE"),
        LLE(2, "LLE"),
        RUE(3, "RUE"),
        RLE(4, "RLE"),
        HEAD(5, "Head"),
        NECK(6, "Neck"),
        FRONT_TORSO(7, "Front Torso"),
        BACK_TORSO(8, "Back Torso"),
        JUNCTIONAL_LU(9, "Junctional - Left Upper"),
        JUNCTIONAL_RU(10, "Junctional - Right Upper"),
        JUNCTIONAL_LL(11, "Junctional - Left Lower"),
        JUNCTIONAL_RL(12, "Junctional - Right Lower"),
        ABDOMEN(13, "Abdomen"),
        ;

        companion object{
            fun find(dataString: String?) = Type.entries.find { it.dataString == dataString }
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = Type.entries.toTypedArray().protoToString(enum)
            fun fromString(string: String?) = Type.entries.toTypedArray().stringToProto(string)
        }
    }
    enum class Type(override val protoIndex: Int, override val dataString: String): ProtoEnum {
        HEMOSTATIC(1, "Hemostatic"),
        NONHEMOSTATIC(2, "Non Hemostatic"),
        ;

        companion object{
            fun find(dataString: String?) = entries.find { it.dataString == dataString }
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = entries.toTypedArray().protoToString(enum)
            fun fromString(string: String?) = entries.toTypedArray().stringToProto(string)
        }
    }
}