package gov.afrl.batdok.encounter.metadata

import com.google.protobuf.Any
import gov.afrl.batdok.commands.CommandHandler
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.commands.proto.majorEventItem
import gov.afrl.batdok.encounter.Name
import gov.afrl.batdok.encounter.Physician
import gov.afrl.batdok.encounter.commands.*
import gov.afrl.batdok.encounter.metadata.EquipmentLog.Companion.includeEquipmentEvents
import java.io.Serializable
import java.time.LocalDate

class Metadata: Serializable {
    var decisionsMade: List<String> = listOf()
        internal set
    var careProvided: List<String> = listOf()
        internal set
    var equipment: List<Equipment> = listOf()
        internal set
    @Deprecated("Use Metadata.equipment instead.")
    val equipmentLog: EquipmentLog = EquipmentLog()
    var proceduresDone: List<Procedure> = listOf()
        internal set
    var majorEvents = MajorEventLog()
    var flightInfo = FlightInfo()
    var medialist: List<String> = listOf()
        internal set
    var infectionControlPrecautions: List<String> = listOf()
        internal set
    var insurance: Insurance = Insurance()
        internal set
    var attendingPhysician: Physician = Physician()
        internal set
    val signatures: Signatures = Signatures()
    var deathDeclaration: DeathDeclaration? = null
    var appointmentDate: LocalDate? = null
    var thumbnailFileName: String? = null
    var creationName: Name? = null
    var creationAlias: String? = null

    @Transient val handlers = CommandHandler().apply {
        val metadata = this@Metadata
        +changeDecisionCommandHandler(metadata)
        +changeCareCommandHandler(metadata)
        +changeInfectionControlPrecautionCommandHandler(metadata)
        +changeProcedureCommandHandler(metadata)
        +addRemoveEquipmentCommandHandler(metadata)
        +addEquipmentCommandHandler(metadata)
        +updateEquipmentCommandHandler(metadata)
        +removeEquipmentCommandHandler(metadata)
        include(equipmentLog.handlers)
        +addRemoveMajorEventCommandHandler(metadata)
        +changeFlightInfoCommandHandler(metadata)
        +addMediaFileCommandHandler(metadata)
        +changeInsuranceCommandHandler(metadata)
        +changeAttendingPhysicianCommandHandler(metadata)
        +setSignaturePMRPhysicianCommandHandler(metadata)
        +setSignaturePMRFlightSurgeonCommandHandler(metadata)
        +removeSignaturePMRPhysicianCommandHandler(metadata)
        +removeSignaturePMRFlightSurgeonCommandHandler(metadata)
        +changeDeathDeclarationCommandHandler(metadata)
        +removeDeathDeclarationCommandHandler(metadata)
        +changeAppointmentDateCommandHandler(metadata)
        +removeAppointmentDateCommandHandler(metadata)
        +updateThumbnailCommandHandler(metadata)
        +updateCreationNameCommandHandler(metadata)
        +updateCreationAliasCommandHandler(metadata)
    }

    companion object{
        fun EventCommandHandler.includeMetadataEvents(metadata: Metadata){
            +changeDecisionEventCommandHandler()
            +changeCareEventCommandHandler()
            +changeInfectionControlPrecautionEventCommandHandler()
            +changeProcedureEventCommandHandler()
            +addRemoveMajorEventEventCommandHandler()
            includeEquipmentEvents()
            +changeFlightInfoCommandEventHandler()
            +addMediaFileEventHandler()
            +changeInsuranceCommandEventHandler()
            +changeAttendingPhysicianCommandEventHandler()
            +setSignaturePMRPhysicianCommandEventHandler()
            +setSignaturePMRFlightSurgeonCommandEventHandler()
            +removeSignaturePMRPhysicianCommandEventHandler()
            +removeSignaturePMRFlightSurgeonCommandEventHandler()
            +changeDeathDeclarationEventHandler(metadata)
            +removeDeathDeclarationCommandEventHandler()
            +changeDeathDeclarationEventHandler(metadata)
            +removeDeathDeclarationCommandEventHandler()
            +changeAppointmentDateEventHandler()
            +removeAppointmentDateCommandEventHandler()
        }
    }
}

@Deprecated("Use Metadata.equipment instead.")
class EquipmentLog(): Serializable{
    var used: List<Equipment> = listOf()
        internal set
    var powerLoss: List<Equipment> = listOf()
        internal set
    var userError: List<Equipment> = listOf()
        internal set
    var equipmentFailure: List<Equipment> = listOf()
        internal set
    @Transient val handlers = CommandHandler().apply {
        val equipment = this@EquipmentLog
        +addRemoveEquipmentCommandHandler(equipment)
        +addRemovePowerLossCommandHandler(equipment)
        +addRemoveEquipmentFailureCommandHandler(equipment)
        +addRemoveUserErrorCommandHandler(equipment)
    }
    companion object{
        fun EventCommandHandler.includeEquipmentEvents(){
            // TODO: create these handlers
            //+addEquipmentEventCommandHandler()
            //+updateEquipmentEventCommandHandler()
            //+removeEquipmentEventCommandHandler()
            +addEquipmentEventHandler()
            +removeEquipmentEventHandler()
            +modifyEquipimentEventHandler()
            +addRemoveEquipmentEventCommandHandler()
            +addRemovePowerLossEventCommandHandler()
            +addRemoveEquipmentFailureEventCommandHandler()
            +addRemoveUserErrorEventCommandHandler()
        }
    }
}

class MajorEventLog: Serializable{
    var events: Map<String?, List<MajorEvent>> = mapOf()
        internal set

    operator fun get(location: String?): List<MajorEvent> = events[location] ?: listOf()
    operator fun set(location: String?, events: List<MajorEvent>){
        this.events = this.events + Pair(location, events)
    }
}

class MajorEvent(val name: String, val data: MajorEventData? = null): Serializable{
    fun toProto() = majorEventItem {
        type = MajorEvents.fromString(name)
        data?.toProto()?.let { extras = Any.pack(it, "") }
    }

    fun toEventString(): String {
        val extrasString = data?.toEventString()?.let {
            " ($it)"
        }?:""
        return "$name$extrasString"
    }

    override fun equals(other: kotlin.Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as MajorEvent

        if (name != other.name) return false

        return true
    }

    override fun hashCode(): Int {
        return name.hashCode()
    }
}
