package gov.afrl.batdok.encounter.observation

import gov.afrl.batdok.commands.proto.*
import gov.afrl.batdok.util.*

class PhysicalExamData(val finding: String, val location: String? = null): ObservationData {
    internal constructor(data: Observations.PhysicalExamData): this(
        data.findingOrNull?.toPrimitive() ?: "",
        data.locationOrNull?.toPrimitive()
    )

    override fun toProtobuf() = physicalExamData {
        finding = nullableStringValue(<EMAIL>)
        location = Location.fromString(<EMAIL>)
    }

    override fun toDetailString(): String {
        return listOfNotNull(location, finding).joinToString(": ")
    }

    enum class Location(): ProtoEnum{
        ;
        companion object {
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }
}