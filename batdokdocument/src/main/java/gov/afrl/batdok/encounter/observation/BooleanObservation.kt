package gov.afrl.batdok.encounter.observation

import gov.afrl.batdok.commands.proto.Observations
import gov.afrl.batdok.commands.proto.booleanData
import gov.afrl.batdok.util.nullableBoolValue
import gov.afrl.batdok.util.toPrimitive

class BooleanObservation(val data: Boolean? = null): ObservationData {
    internal constructor(data: Observations.BooleanData): this(data.bool.toPrimitive())
    override fun toProtobuf() = booleanData{
        bool = nullableBoolValue(data)
    }

    override fun toDetailString() = when(data){
        null -> ""
        true -> "Yes"
        false -> "No"
    }
}