package gov.afrl.batdok.encounter.observation

import gov.afrl.batdok.commands.CommandHandler
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.commands.*
import gov.afrl.batdok.util.ICategorizedItemList
import gov.afrl.batdok.util.SortedCategorizedLinkableList
import java.io.Serializable

class BloodList: ICategorizedItemList<Blood> by SortedCategorizedLinkableList(), Serializable {

    @Transient internal val handlers = CommandHandler {
        val bloodList = this@BloodList
        +logBloodCommandHandler(bloodList)
        +updateBloodCommandHandler(bloodList)
        +removeBloodCommandHandler(bloodList)
    }
    companion object {
        fun EventCommandHandler.includeBloodEvents(bloodList: BloodList) = apply {
            +logBloodCommandEventHandler()
            +updateBloodCommandEventHandler()
            +removeBloodCommandEventHandler(bloodList)
        }
    }
}