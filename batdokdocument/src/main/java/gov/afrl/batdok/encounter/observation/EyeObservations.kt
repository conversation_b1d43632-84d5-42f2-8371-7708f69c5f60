package gov.afrl.batdok.encounter.observation

import gov.afrl.batdok.commands.proto.Observations
import gov.afrl.batdok.commands.proto.pupilDilation
import gov.afrl.batdok.commands.proto.pupilReaction
import gov.afrl.batdok.util.nullableBoolValue
import gov.afrl.batdok.util.nullableInt32Value
import gov.afrl.batdok.util.toPrimitive

class PupilDilationData(val perrlaSizeLeft: Int?, val perrlaSizeRight: Int?): ObservationData{
    internal constructor(command: Observations.PupilDilation): this(command.perrlaSizeLeft.toPrimitive(), command.perrlaSizeRight.toPrimitive())
    override fun toProtobuf() = pupilDilation {
        this.perrlaSizeLeft = nullableInt32Value(<EMAIL>)
        this.perrlaSizeRight = nullableInt32Value(<EMAIL>)
    }

    override fun toDetailString(): String {
        return listOfNotNull(
            "Left pupil dilated ${perrlaSizeLeft}mm".takeUnless { perrlaSizeLeft == null },
            "Right pupil dilated ${perrlaSizeRight}mm".takeUnless { perrlaSizeRight == null }
        ).joinToString(", ")
    }
}

//TODO: Combine dilation and reaction
class PupilReactionData(val leftSide: Boolean? = null, val reaction: Boolean? = null): ObservationData{
    internal constructor(command: Observations.PupilReaction): this(command.side.toPrimitive(), command.isReactive.toPrimitive())

    override fun toProtobuf() = pupilReaction {
        this.side = nullableBoolValue( leftSide)
        this.isReactive = nullableBoolValue(<EMAIL>)
    }

    override fun toDetailString(): String {
        return if(leftSide != null && reaction != null) {
            val side = if (leftSide) "Left" else "Right"
            val reactiveVerb = if (reaction) "is" else "is not"
            "$side pupil $reactiveVerb reactive"
        } else{
            ""
        }
    }
}