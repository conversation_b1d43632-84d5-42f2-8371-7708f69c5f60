package gov.afrl.batdok.encounter.vitals

import gov.afrl.batdok.commands.proto.VitalOuterClass
import gov.afrl.batdok.commands.proto.bP
import gov.afrl.batdok.encounter.IndividualVital
import gov.afrl.batdok.util.int32Value
import gov.afrl.batdok.util.stringValue

class BloodPressure(val systolic: Int?, val diastolic: Int?, val location: String? = null): IndividualVital("BP"){
    constructor(bp: VitalOuterClass.BP): this(
        bp.bps.value.takeIf { bp.hasBps() },
        bp.bpd.value.takeIf { bp.hasBpd() },
        bp.location.value.takeIf { bp.hasLocation() }
    )
    override fun toProtobuf() = bP {
        <EMAIL>?.let { this.bps = int32Value(it) }
        <EMAIL>?.let { this.bpd = int32Value(it) }
        <EMAIL>?.let { this.location = stringValue(it) }
    }

    override fun produceEmptyVital() = BloodPressure(null, null)

    override val eventMessage: String
        get() = ("%s/%s" + (location?.takeIf { it.isNotBlank() }?.let { " $it" } ?: ""))
            .format(
                systolic?.toString() ?: "--",
                diastolic?.toString() ?: "--"
            )

    override val isEmpty: Boolean
        get() = systolic == null && diastolic == null && location == null
}