package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.proto.SharedProtobufObjects.CompatibleEnum
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto
import gov.afrl.batdok.util.toChangeOrClearEvent

enum class BloodType(override val dataString: String, override val protoIndex: Int): ProtoEnum {
    O_POSITIVE("O+", 1),
    A_POSITIVE("A+", 2),
    B_POSITIVE("B+", 3),
    AB_POSITIVE("AB+", 4),
    O_NEGATIVE("O-", 5),
    A_NEGATIVE("A-", 6),
    B_NEGATIVE("B-", 7),
    AB_NEGATIVE("AB-", 8);

    companion object{
        fun fromProto(enum: CompatibleEnum) = BloodType.entries.toTypedArray().protoToString(enum)
        fun fromString(string: String?) = BloodType.entries.toTypedArray().stringToProto(string)
        fun toEvent(enum: CompatibleEnum, lowTiter: Boolean? = null) : String {
            val value = BloodType.entries.toTypedArray().protoToString(enum)
            return changeOrClearEvent(
                !value.isNullOrEmpty(),
                value?:"",
                "blood type"
            ) + if (lowTiter == true) " (Low Titer)" else ""
        }
    }
}