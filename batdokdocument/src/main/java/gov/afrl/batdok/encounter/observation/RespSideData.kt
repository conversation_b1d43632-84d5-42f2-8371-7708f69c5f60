package gov.afrl.batdok.encounter.observation

import gov.afrl.batdok.commands.proto.Observations
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.respSideData
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

class RespSideData(val type: String? = null): ObservationData {
    internal constructor(data: Observations.RespSideData): this(Type.fromProto(data.type))

    enum class Type(override val protoIndex: Int, override val dataString: String): ProtoEnum {
        LEFT(1, "Left"),
        RIGHT(2, "Right"),
        BOTH(3, "Both"),
        ;
        companion object {
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = Type.values().protoToString(enum)
            fun fromString(string: String?) = Type.values().stringToProto(string)
        }
    }

    override fun toProtobuf() = respSideData {
        this.type = Type.fromString(<EMAIL>)
    }

    override fun toDetailString(): String {
        val plural = if(this.type == "Both") "s" else ""
        return "on " + (this.type ?: "neither") + " side" + plural
    }
}