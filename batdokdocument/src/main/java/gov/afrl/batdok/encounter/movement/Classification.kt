package gov.afrl.batdok.encounter.movement

import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto
import gov.afrl.batdok.util.toChangeOrClearEvent

enum class Classification(override val dataString: String, override val protoIndex: Int): ProtoEnum {
    ONE_A("1A - Severe psychiatric litter patient requiring the use of restraining apparatus, sedation, and close supervision.", 1),
    ONE_B("1B - Psychiatric litter patient of intermediate severity.", 2),
    ONE_C("1C - Psychiatric walking patient of moderate severity.", 3),
    TWO_A("2A - Immobile litter patient, nonpsychiatric, who are not able to move about on own volition.", 4),
    TWO_B("2B - Mobile litter patient, nonpsychiatric, who are able to move about on own volition.", 5),
    THREE_A("3A - Ambulatory patient, nonpsychiatric and nonsubstance abuse, going for treatment or evaluation.", 6),
    THREE_B("3B - Recovered patient, returning to home station.", 7),
    THREE_C("3C - Ambulatory, drug, or alcohol (substance) abuse going for treatment.", 8),
    FOUR_A("4A - Infant under three years of age, occupying an aircraft seat, going for treatment.", 9),
    FOUR_B("4B - Recovered infant, under three years of age, occupying an aircraft seat.", 10),
    FOUR_C("4C - Infant requiring incubator.", 11),
    FOUR_D("4D - Infant under 3 years of age occupying a litter.", 12),
    FOUR_E("4E - Outpatient under 3 years of age.", 13),
    FIVE_A("5A - Outpatient ambulatory patient, nonpsychiatric and nonsubstance abuse, going for treatment.", 14),
    FIVE_B("5B - Outpatient ambulatory, drug or alcohol (substance) abuse, going for treatment.", 15),
    FIVE_C("5C - Psychiatric outpatient going for treatment or evaluation.", 16),
    FIVE_D("5D - Outpatient on litter for comfort going for treatment.", 17),
    FIVE_E("5E - Returning outpatient on litter for comfort.", 18),
    FIVE_F("5F - Returning outpatient.", 19),
    SIX_A("6A - Medical Attendant", 20),
    SIX_B("6B - Non-Medical Attendant", 21),
    NINE_V("9V - ITV Patient – Non-AE Movement", 22);

    companion object{
        fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)
        fun toEvent(enum: SharedProtobufObjects.CompatibleEnum) = values()
            .toChangeOrClearEvent(enum, "Classification")
    }
}