package gov.afrl.batdok.encounter.treatment

import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.*
import gov.afrl.batdok.commands.proto.TreatmentCommands.TQ
import gov.afrl.batdok.encounter.DrawingPoint
import gov.afrl.batdok.util.*
import java.time.Instant

data class TqData(
    val tqLocation: String,
    val tqType: String? = null,
    val subLocation: String? = null,
    val reapplicationTime: Instant? = null,
    val conversionTime: Instant? = null,
    val removalTime: Instant? = null,
    val reassessTime: Instant? = null,
    val drawingPoint: DrawingPoint? = null,
    val label: String? = null,
): TreatmentData{
    init {
        //If both Location and Sublocation are an enum value, make sure they are a valid pair
        SubLocation.find(subLocation)?.let { subLocation ->
            Location.find(tqLocation)?.let { location ->
                require(location == subLocation.location)
            }
        }
    }

    override fun toProtobuf(): Message = tQ{
        this.location = Location.fromString(tqLocation)
        this.type = Type.fromString(tqType)
        this.sublocation = SubLocation.fromString(<EMAIL>)
        this.reapplicationTime = nullableInt64Value(<EMAIL>)
        this.conversionTime = nullableInt64Value(<EMAIL>)
        this.removalTime = nullableInt64Value(<EMAIL>)
        this.reassessTime = nullableInt64Value(<EMAIL>)
        this.pointLabel = nullableStringValue(<EMAIL>)
        this.pointX = nullableFloatValue(<EMAIL>?.x)
        this.pointY = nullableFloatValue(<EMAIL>?.y)
    }

    override fun toDetailString(): String {
        val timeFormat = Patterns.hm_24_colon
        return listOfNotNull(
            tqType.toDetailString("Type"),
            tqLocation.toDetailString("Location"),
            subLocation?.let { "Sub-Location: $it" },
            reapplicationTime?.format(timeFormat, true).toDetailString("Reapplication"),
            conversionTime?.let { "Conversion: ${it.format(timeFormat, true)}" },
            removalTime?.let { "Removal: ${it.format(timeFormat, true)}" },
            reassessTime?.let { "Reassess: ${it.format(timeFormat, true)}" }
        ).joinToString(", ")
    }

    companion object{
        fun fromProtobuf(tq: TQ): TreatmentData{
            val hasPoint = listOfNotNull(
                tq.pointLabelOrNull?.valueOrNull,
                tq.pointXOrNull?.valueOrNull,
                tq.pointYOrNull?.valueOrNull
            ).isNotEmpty()
            return TqData(
                Location.fromProto(tq.location) ?: "",
                Type.fromProto(tq.type),
                SubLocation.fromProto(tq.sublocation),
                tq.reapplicationTimeOrNull?.toInstant(),
                tq.conversionTimeOrNull?.toInstant(),
                tq.removalTimeOrNull?.toInstant(),
                tq.reassessTimeOrNull?.toInstant(),
                DrawingPoint(
                    tq.pointLabel.toPrimitive() ?: "",
                    tq.pointX.toPrimitive()?: 0f,
                    tq.pointY.toPrimitive() ?: 0f
                ).takeIf { hasPoint },
                tq.pointLabel.toPrimitive()
            )
        }
    }

    enum class Location(override val protoIndex: Int, override val dataString: String): ProtoEnum {
        EXTREMITY(1, "Extremity"),
        JUNCTIONAL(2, "Junctional"),
        TRUNCAL(3, "Truncal");

        companion object{
            fun find(dataString: String) = values().find { it.dataString == dataString }
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }

    enum class SubLocation(override val protoIndex: Int, override val dataString: String, val location: Location): ProtoEnum {
        LUE(1, "LUE", Location.EXTREMITY),
        RUE(2, "RUE", Location.EXTREMITY),
        LLE(3, "LLE", Location.EXTREMITY),
        RLE(4, "RLE", Location.EXTREMITY),
        AXILLA(5, "Axilla", Location.JUNCTIONAL),
        INGUINAL(6, "Inguinal", Location.JUNCTIONAL),
        PELVIS(7, "Pelvis", Location.TRUNCAL),
        ;

        companion object{
            fun forLocation(location: Location) = values().filter { it.location == location }
            fun find(string: String?) = values().find { it.dataString == string }
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }

    enum class Type(override val protoIndex: Int, override val dataString: String): ProtoEnum{
        CAT_5(1, "CAT Gen 5"),
        CAT_6(2, "CAT Gen 6"),
        CAT_7(3, "CAT Gen 7"),
        RMT_T(4, "RMT-T"),
        SAM_XT(5, "SAM-XT"),
        TMT(6, "TMT"),
        TX2(7, "TX2"),
        TX3(8, "TX3"),
        TPT2(9, "TPT2"),
        Xstat(10, "Xstat"),
        SOFTT(11, "SOFTT"),
        AAJT(12, "AAJT"),
        CROC(13, "CRoC"),
        JETT(14, "JETT"),
        SAM(15, "SAM")
        ;

        companion object{
            fun find(string: String?) = values().find { it.dataString == string }
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }
}
