package gov.afrl.batdok.encounter.orders

import gov.afrl.batdok.encounter.ids.MedicineId
import gov.afrl.batdok.encounter.medicine.IMedicine
import gov.afrl.batdok.encounter.medicine.Medicine
import java.time.Instant
import java.time.temporal.ChronoUnit

data class OrderedMedicine(
    override val name: String,
    override val ndc: String?,
    override val rxcui: String?,
    override val medId: MedicineId,
    override val route: String?,
    override val volume: Float?,
    override val unit: String?,
    override val serialNumber: String?,
    override val expirationDate: String?,
    override val type: String?,
    override val exportName: String?
) : IMedicine {
    fun toMedicine() = Medicine(
        name = this.name,
        ndc = this.ndc,
        rxcui = this.rxcui,
        administrationTime = Instant.now().truncatedTo(ChronoUnit.MINUTES),
        route = this.route,
        volume = this.volume,
        unit = this.unit,
        serialNumber = this.serialNumber,
        expirationDate = this.expirationDate,
        type = this.type,
        exportName = this.exportName
    )
}