package gov.afrl.batdok.encounter.metadata

import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

enum class InfectionControl(override val protoIndex: Int, override val dataString: String): ProtoEnum {
    ST(1, "STANDARD"),
    DR(2, "DROPLET"),
    CO(3, "CONTACT"),
    AB(4, "AIRBORNE");

    companion object{
        fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = InfectionControl.values().protoToString(enum)
        fun fromString(string: String?) = InfectionControl.values().stringToProto(string)
    }
}