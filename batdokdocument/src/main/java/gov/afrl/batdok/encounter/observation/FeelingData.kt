package gov.afrl.batdok.encounter.observation

import gov.afrl.batdok.commands.proto.Observations.FeelingData
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.feelingData
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

class FeelingData(val type : String? = null): ObservationData {
    internal constructor(data: FeelingData): this(Type.fromProto(data.type))
    enum class Type(override val protoIndex: Int, override val dataString: String): ProtoEnum {
        WEAKNESS(1, "Weakness"),
        PAIN(2, "Pain"),
        NORMAL_SENSATION(3, "Normal Sensation"),
        NORMAL_ROM(4, "Normal Range of Motion");

        companion object{
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }

    }

    override fun toProtobuf() = feelingData {
        this.type = Type.fromString(<EMAIL>)
    }

    override fun toDetailString(): String {
        return type ?: ""
    }
}