package gov.afrl.batdok.encounter.commands

import gov.afrl.batdok.commands.eventHandler
import gov.afrl.batdok.commands.handler
import gov.afrl.batdok.commands.handlerWithId
import gov.afrl.batdok.commands.proto.*
import gov.afrl.batdok.commands.proto.MedTreatmentCommands.*
import gov.afrl.batdok.encounter.KnownEventTypes
import gov.afrl.batdok.encounter.ids.EventId
import gov.afrl.batdok.encounter.ids.MedicineId
import gov.afrl.batdok.encounter.ids.toByteString
import gov.afrl.batdok.encounter.ids.toDomainId
import gov.afrl.batdok.encounter.medicine.Drip
import gov.afrl.batdok.encounter.medicine.Drips
import gov.afrl.batdok.encounter.medicine.Medicine
import gov.afrl.batdok.encounter.medicine.Medicines
import gov.afrl.batdok.util.addPreviousFields
import gov.afrl.batdok.util.toNullableInstant
import gov.afrl.batdok.util.removeDuplicates
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant

fun buildLogMedicineCommand(medicine: Medicine) = logMedCommand{
    this.medicine = medicine.toProto()
}
fun logMedCommandHandler(medicines: Medicines) = handlerWithId<LogMedCommand>{ docId, command ->
    medicines += Medicine(
        medicine,
        documentId = docId,
        administrationTime = toNullableInstant(command.timestamp),
        id = MedicineId(command.commandId.toByteArray())
    )
}
fun logMedCommandEventHandler() = eventHandler<LogMedCommand>(KnownEventTypes.MEDS.dataString){
    "Logged Medicine${Medicine(medicine, DomainId.nil()).toEventString()}"
}

fun buildLogMedWithIdCommand(medicine: Medicine) = logMedWithIdCommand {
    this.id = medicine.id.toByteString()
    medicine.administrationTime?.let{this.administrationTime = it.epochSecond}
    this.medicine = medicine.toProto()
}

fun logMedWithIdCommandHandler(medicines: Medicines) = handlerWithId<LogMedWithIdCommand> { documentId, _ ->
    medicines += Medicine(
        medicine,
        documentId = documentId,
        administrationTime = toNullableInstant(administrationTime),
        id = id.toDomainId()
    )
}

fun logMedWithIdCommandEventHandler() = eventHandler<LogMedWithIdCommand>(
    eventType = KnownEventTypes.MEDS.dataString,
    timestampGenerator = {toNullableInstant(administrationTime)},
    referenceId = { id.toDomainId() }
) {
    "Logged Medicine${Medicine(medicine, DomainId.nil()).toEventString()}"
}

fun buildUpdateMedicineCommand(medicine: Medicine, oldMedicine: Medicine? = null) = updateMedCommand{
    this.id = medicine.id.toByteString()
    medicine.administrationTime?.let{this.timestamp = it.epochSecond}

    this.medicine = medicine.toProto().removeDuplicates(oldMedicine?.toProto())
}
fun updateMedCommandHandler(medicines: Medicines) = handlerWithId<UpdateMedCommand>{ docId, _ ->
    val oldMedicine = medicines[id.toDomainId<MedicineId>()]
    val restoredData = SharedProtobufObjects.Medicine.parseFrom(medicine.addPreviousFields(oldMedicine?.toProto()).toByteString())
    medicines += Medicine(restoredData, documentId = docId, administrationTime = toNullableInstant(timestamp), id.toDomainId())
}
fun updateMedCommandEventHandler(medicines: Medicines) = eventHandler<UpdateMedCommand>(
    KnownEventTypes.MEDS.dataString,
    referenceId = { id.toDomainId() },
    handler = {
        val oldMedicine = medicines[id.toDomainId<MedicineId>()]
        val restoredData = SharedProtobufObjects.Medicine.parseFrom(medicine.addPreviousFields(oldMedicine?.toProto()).toByteString())
        "Updated Medicine${
            Medicine(
                protoMed = restoredData, 
                administrationTime = toNullableInstant(timestamp),   
                id = id.toDomainId<MedicineId>(),
                documentId = DomainId.nil()
            ).toEventString()
        }"
    }
)

fun buildRemoveMedTreatmentCommand(id: MedicineId, docError: Boolean = false) = removeMedTreatmentCommand {
    this.id = id.toByteString()
    this.documentationError = docError
}
fun removeMedTreatmentCommandHandler(medicines: Medicines) = handler<RemoveMedTreatmentCommand>{
    medicines.removeItem(id.toDomainId(), Instant.ofEpochSecond(it.timestamp))
}
fun removeMedTreatmentCommandEventHandler(medicines: Medicines) = eventHandler<RemoveMedTreatmentCommand>(
    KnownEventTypes.MEDS.dataString,
    referenceId = { id.toDomainId() }
){
    val medicine = medicines[id.toDomainId<MedicineId>()] ?: return@eventHandler null
    "Removed Medicine" + medicine.toEventString() + (if (documentationError) " due to documentation error" else "")
}

fun buildCreateDrip(medicine: Medicine) = createDrip {
    this.id = medicine.id.toByteString()
    this.medicine = medicine.toProto()
    medicine.administrationTime?.let{this.timestamp = it.epochSecond}
}

fun createDripHandler(drips: Drips) = handlerWithId<CreateDrip> { docId, _ ->
    drips += Drip(medicine, documentId = docId, baseTimestamp = Instant.ofEpochSecond(timestamp), id = id.toDomainId())
}

fun createDripEventHandler() = eventHandler<CreateDrip>(KnownEventTypes.MEDS.dataString) {
    "Drip Created with Base${Medicine(medicine, DomainId.nil()).toEventString()}"
}

fun buildRemoveDrip(id: MedicineId) = removeDrip {
    this.id = id.toByteString()
}

fun removeDripHandler(drips: Drips) = handler<RemoveDrip> {
    drips -= id.toDomainId()
}

fun removeDripEventHandler() = eventHandler<RemoveDrip>(KnownEventTypes.MEDS.dataString) {
    "Removed Drip with Base Medicine id: ${id.toDomainId<EventId>().toHexString()}"
}

fun buildAddToDrip(drip: Drip, medicine: Medicine) = addToDrip {
    this.dripId = drip.dripId.toByteString()
    this.dripMedicine = drip.base.toProto()
    this.id = medicine.id.toByteString()
    this.medicine = medicine.toProto()
}

fun addToDripHandler(drips: Drips) = handlerWithId<AddToDrip> { docId, command ->
    drips.findById(dripId.toDomainId())
        .components.
        add(Medicine(
            protoMed = medicine,
            administrationTime = Instant.ofEpochSecond(command.timestamp),
            documentId = docId,
            id = this.id.toDomainId()
        ))
}

fun addToDripEventHandler() = eventHandler<AddToDrip>(KnownEventTypes.MEDS.dataString) {
    "Added Medicine${Medicine(medicine, documentId = DomainId.nil()).toEventString()} to Drip: ${dripMedicine.name.value}"
}

fun buildRemoveFromDrip(drip: Drip, medicine: Medicine) = removeFromDrip {
    this.dripId = drip.dripId.toByteString()
    this.dripMedicine = drip.base.toProto()
    this.id = medicine.id.toByteString()
    this.medicine = medicine.toProto()
}

fun removeFromDripHandler(drips: Drips) = handler<RemoveFromDrip> {
    drips.findById(dripId.toDomainId()).components.removeIf { it.id == id.toDomainId() }
}

fun removeFromDripEventHandler() = eventHandler<RemoveFromDrip>(KnownEventTypes.MEDS.dataString) {
    "Removed Medicine${Medicine(medicine, DomainId.nil()).toEventString()} from Drip ${dripMedicine.name.value}"
}

fun buildStartDrip(drip: Drip, timestamp: Instant) = startDrip {
    this.id = drip.dripId.toByteString()
    this.medicine = drip.base.toProto()
    this.timestamp = timestamp.epochSecond
}

fun startDripHandler(drips: Drips) = handler<StartDrip> {
    val drip = drips.findById(id.toDomainId())
    if (!drip.isStarted and !drip.isStopped){
        drip.startTime = Instant.ofEpochSecond(timestamp)
    } else if (drip.isStarted and drip.isStopped) {
        drip.startTime = Instant.ofEpochSecond(timestamp)
        drip.endTime = null
    }
}

fun startDripEventHandler() = eventHandler<StartDrip>(KnownEventTypes.MEDS.dataString) {
    "Started Drip${Medicine(medicine, DomainId.nil()).toEventString()} at $timestamp"
}

fun buildStopDrip(drip: Drip, timestamp: Instant) = stopDrip {
    this.id = drip.dripId.toByteString()
    this.medicine = drip.base.toProto()
    this.timestamp = timestamp.epochSecond
}

fun stopDripHandler(drips: Drips) = handler<StopDrip> {
    val drip = drips.findById(id.toDomainId())
    if (!drip.isStopped && drip.isStarted)
        drips.findById(id.toDomainId()).endTime = Instant.ofEpochSecond(timestamp)
}

fun stopDripEventHandler() = eventHandler<StopDrip>(KnownEventTypes.MEDS.dataString) {
    "Stopped Drip${Medicine(medicine, DomainId.nil()).toEventString()} at $timestamp"
}