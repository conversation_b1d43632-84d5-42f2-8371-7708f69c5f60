package gov.afrl.batdok.encounter.medicine

import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

enum class KnownMedTypes(override val protoIndex: Int, override val dataString: String):
    ProtoEnum {
    FLUID(1, "Fluid"),
    ANALGESIC(2, "Analgesic"),
    ANTIBIOTIC(3, "Antibiotic"),
    ;

    companion object{
        fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)
    }
}