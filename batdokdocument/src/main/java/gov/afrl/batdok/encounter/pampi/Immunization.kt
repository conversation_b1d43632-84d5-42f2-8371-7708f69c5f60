package gov.afrl.batdok.encounter.pampi

import gov.afrl.batdok.commands.proto.InfoCommands
import gov.afrl.batdok.commands.proto.immunization
import gov.afrl.batdok.util.nullableInt64Value
import gov.afrl.batdok.util.toInstant
import java.io.Serializable
import java.time.Instant

data class Immunization(
    var name: String,
    var unit: String,
    var volume: Float,
    var date: Instant? = null
) :
    Serializable {
    fun toProto() = immunization {
        val immunization = this@Immunization
        this.name = immunization.name
        this.unit = immunization.unit
        this.volume = immunization.volume
        this.date = nullableInt64Value(immunization.date)
    }

    override fun toString(): String {
        return listOf(
            name,
            volume.toString(),
            unit,
            date?.toString() ?: ""
        ).joinToString(" ")
    }

    companion object {
        fun fromProto(proto: InfoCommands.Immunization): Immunization {
            return Immunization(
                name = proto.name,
                unit = proto.unit,
                volume = proto.volume,
                date = proto.date.toInstant(),
            )
        }
    }
}