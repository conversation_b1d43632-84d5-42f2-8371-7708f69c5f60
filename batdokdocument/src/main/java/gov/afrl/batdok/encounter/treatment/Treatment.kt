package gov.afrl.batdok.encounter.treatment

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.CommonLinkRelationships
import gov.afrl.batdok.encounter.Document
import gov.afrl.batdok.encounter.Note
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.encounter.ids.TreatmentId
import gov.afrl.batdok.util.DocumentItem
import mil.af.afrl.batman.batdokid.DomainId
import java.io.Serializable
import java.time.Instant

data class Treatment(
    val name: String? = null,
    val treatmentData: TreatmentData? = null,
    override val id: TreatmentId = DomainId.create(),
    override val timestamp: Instant? = Instant.now(),
    override val documentId: DocumentId = DomainId.nil()
) : DocumentItem<TreatmentId>(id, documentId, name, timestamp) {
    /**
     * Return the data as the proper type. Return null if the type is not the right type
     */
    inline fun <reified T : TreatmentData> getData(): T? {
        return if (treatmentData is T) treatmentData else null
    }

    fun toEvent(): String {
        val extraDataText = treatmentData?.toEventString()
            ?.takeUnless { it.isEmpty() }
            ?.let { ": $it" }
            ?: ""
        return "Treatment $name$extraDataText"
    }

    fun getAssociatedNotes(document: Document) = with(document) {
        getLinkedItems(CommonLinkRelationships.NOTE).filterIsInstance<Note>()
    }
}

sealed interface TreatmentData : Serializable {
    fun toProtobuf(): Message
    fun toEventString(): String = toDetailString()
    fun toDetailString(): String

    fun String?.toDetailString(label: String, unit: String? = null) =
        "$label: $this${unit?.let { " $it" } ?: ""}".takeUnless { this.isNullOrBlank() }

    fun Boolean?.toDetailString(label: String) =
        this?.let { if (it) "Yes" else "No" }.toDetailString(label)

    fun Float?.toDetailString(label: String, unit: String? = null) =
        this?.toString()?.removeSuffix(".0").toDetailString(label, unit)

    fun Int?.toDetailString(label: String, unit: String? = null) =
        this?.toString().toDetailString(label, unit)
}