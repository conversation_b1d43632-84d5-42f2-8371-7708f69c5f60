package gov.afrl.batdok.encounter.commands

import gov.afrl.batdok.commands.eventHandler
import gov.afrl.batdok.commands.handler
import gov.afrl.batdok.commands.proto.AssessmentCommands.SelectProtocolCommand
import gov.afrl.batdok.commands.proto.AssessmentCommands.UpdateDifferentialsCommand
import gov.afrl.batdok.commands.proto.InfoCommands
import gov.afrl.batdok.commands.proto.changeDiagnosisCommand
import gov.afrl.batdok.commands.proto.selectProtocolCommand
import gov.afrl.batdok.commands.proto.updateDifferentialsCommand
import gov.afrl.batdok.encounter.Diagnosis
import gov.afrl.batdok.encounter.DiagnosisCategory
import gov.afrl.batdok.encounter.changeOrClearEvent

fun buildChangeDiagnosisCommand(diagnosis: DiagnosisCategory) = changeDiagnosisCommand {
    this.diagnosis = diagnosis.toProto()
}
fun buildChangeDiagnosisCommand(diagnosis: String?) = changeDiagnosisCommand {
    this.diagnosis = DiagnosisCategory.fromString(diagnosis)
}
fun changeDiagnosisCommandHandler(diagnosis: Diagnosis) = handler<InfoCommands.ChangeDiagnosisCommand>{
    diagnosis.category = DiagnosisCategory.fromProto(this.diagnosis)
}
fun changeDiagnosisEventCommandHandler() = eventHandler<InfoCommands.ChangeDiagnosisCommand> {
    DiagnosisCategory.toEvent(diagnosis)
}

fun buildUpdateDifferentialsCommand(differential: String, add: Boolean) = updateDifferentialsCommand {
    this.differential = differential
    this.checked = add
}
fun updateDifferentialsCommandHandler(diagnosis: Diagnosis) = handler<UpdateDifferentialsCommand> {
    if(!checked){
        diagnosis.differentials -= differential
        if(diagnosis.selectedProtocol == differential){
            diagnosis.selectedProtocol = null
        }
    }else{
        diagnosis.differentials += differential
    }
}
fun updateDifferentialsEventCommandHandler() = eventHandler<UpdateDifferentialsCommand> {
    val action = if(checked) "Added" else "Removed"
    "$action $differential as a differential diagnosis"
}

fun buildSelectProtocolCommand(protocol: String?) = selectProtocolCommand {
    protocol?.let { this.protocol = it }
}
fun selectProtocolCommandHandler(diagnosis: Diagnosis) = handler<SelectProtocolCommand>{
    diagnosis.selectedProtocol = protocol.takeIf { it.isNotEmpty() }
}
fun selectProtocolEventCommandHandler() = eventHandler<SelectProtocolCommand> {
    changeOrClearEvent(protocol.isNotEmpty(), protocol, "selected protocol")
}