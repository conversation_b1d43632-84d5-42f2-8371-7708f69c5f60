package gov.afrl.batdok.encounter.medicine

import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

enum class KnownUnits(override val protoIndex: Int, override val dataString: String): ProtoEnum {
    MG(1, "mg"),
    MCG(2, "mcg"),
    ML(3, "mL"),
    GM(4, "gm"),
    UNITS(5, "units"),
    CC(6, "cc"),
    MG_ML(7, "mg/mL"),
    MCG_ML(8, "mcg/mL"),
    MCG_KG_MIN(9, "mcg/kg/min"),
    MG_MIN(10, "mg/min"),
    MCG_KG(11, "mcg/kg"),
    MU_ML(12, "mU/mL"),
    U_MIN(13, "U/min"),
    ML_HR(14, "mL/hr"),
    ;

    companion object{
        fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)
    }
}