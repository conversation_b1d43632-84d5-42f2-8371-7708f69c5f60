package gov.afrl.batdok.encounter.commands

import gov.afrl.batdok.commands.eventHandler
import gov.afrl.batdok.commands.handler
import gov.afrl.batdok.commands.handlerWithId
import gov.afrl.batdok.commands.proto.StratevacRnIoCommands.*
import gov.afrl.batdok.commands.proto.addIOItem
import gov.afrl.batdok.commands.proto.removeIOItem
import gov.afrl.batdok.commands.proto.updateIOItem
import gov.afrl.batdok.encounter.InputOutput
import gov.afrl.batdok.encounter.InputOutputs
import gov.afrl.batdok.encounter.KnownEventTypes
import gov.afrl.batdok.encounter.ids.toByteString
import gov.afrl.batdok.encounter.ids.toDomainId
import gov.afrl.batdok.encounter.ids.toDomainIdOrElse
import gov.afrl.batdok.util.addPreviousFields
import gov.afrl.batdok.util.int64Value
import gov.afrl.batdok.util.removeDuplicates
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant

fun buildAddIOItemCommand(item: InputOutput, isInput: Boolean) = addIOItem{
    this.data = item.toProto()
    this.isInput = isInput
    this.id = item.id.toByteString()
}
fun addIOItemCommandHandler(inputOutputs: InputOutputs) = handlerWithId<AddIOItem> { docId, command ->
    val data = InputOutput(
        id.toDomainIdOrElse(command.commandId),
        docId,
        Instant.ofEpochSecond(command.timestamp),
        this.data
    )
    if(isInput){
        inputOutputs.inputs += data
    }else{
        inputOutputs.outputs += data
    }
}
fun addIOItemEventCommandHandler() = eventHandler<AddIOItem> (KnownEventTypes.TREAT.dataString){
    val data = InputOutput(
        it.commandId.toDomainId(),
        DomainId.nil(),
        Instant.ofEpochSecond(it.timestamp),
        this.data
    )
    val direction = if(isInput) "Input" else "Output"
    "Logged $direction: ${data.toEventString()}"
}


private fun List<InputOutput>.withNew(io: InputOutput) = filter{ it.id != io.id } + io
fun buildUpdateIOItemCommand(item: InputOutput, isInput: Boolean, oldValue: InputOutput? = null) = updateIOItem{
    this.data = item.toProto().removeDuplicates(oldValue?.toProto())
    this.isInput = isInput
    this.itemId = item.id.toByteString()
    if(item.timestamp != oldValue?.timestamp) this.time = int64Value(item.timestamp.epochSecond)
}
fun updateIOItemCommandHandler(inputOutputs: InputOutputs) = handlerWithId<UpdateIOItem> { docId, command ->
    val oldItem = inputOutputs[itemId.toDomainId()]
    val oldCommand = oldItem?.toProto()
    val restoredCommand = IOItem.parseFrom(this.data.addPreviousFields(oldCommand).toByteArray())
    val data = InputOutput(
        itemId.toDomainId(),
        docId,
        Instant.ofEpochSecond(time.value)
            .takeIf { hasTime() }
            ?: oldItem?.timestamp
            ?: Instant.ofEpochSecond(command.timestamp),
        restoredCommand
    )
    if(isInput){
        inputOutputs.inputs = inputOutputs.inputs.withNew(data)
    }else{
        inputOutputs.outputs = inputOutputs.outputs.withNew(data)
    }
}
fun updateIOItemEventCommandHandler(inputOutputs: InputOutputs) = eventHandler<UpdateIOItem>(
    KnownEventTypes.EVAC.dataString,
    referenceId = { itemId.toDomainId() },
    handler = {
        val oldCommand = inputOutputs[itemId.toDomainId()]?.toProto()
        val restoredCommand = IOItem.parseFrom(this.data.addPreviousFields(oldCommand).toByteArray())
        val data = InputOutput(
            itemId.toDomainId(),
            DomainId.nil(),
            Instant.ofEpochSecond(time.value),
            restoredCommand
        )
        val direction = if(isInput) "Input" else "Output"
        "Updated $direction: ${data.toEventString()}"
    }
)

fun buildRemoveIOItem(id: DomainId) = removeIOItem {
    this.itemId = id.toByteString()
}
fun removeIOItemCommandHandler(inputOutputs: InputOutputs) = handler<RemoveIOItem> {
    inputOutputs -= itemId.toDomainId()
}
fun removeIOItemEventCommandHandler(inputOutputs: InputOutputs) = eventHandler<RemoveIOItem> (
    KnownEventTypes.TREAT.dataString,
    referenceId = { itemId.toDomainId() }
){
    val data = inputOutputs[itemId.toDomainId()] ?: return@eventHandler null
    val direction = if(data in inputOutputs.inputs) "Input" else "Output"
    "Removed $direction: ${data.toEventString()}"
}