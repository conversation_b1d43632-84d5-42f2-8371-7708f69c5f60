package gov.afrl.batdok.encounter.pampi

import gov.afrl.batdok.commands.proto.InfoCommands
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.problem
import gov.afrl.batdok.encounter.ids.ProblemId
import gov.afrl.batdok.encounter.ids.toByteString
import gov.afrl.batdok.encounter.ids.toDomainId
import gov.afrl.batdok.encounter.metadata.KnownEquipment
import gov.afrl.batdok.util.*
import mil.af.afrl.batman.batdokid.DomainId
import java.io.Serializable
import java.time.Instant

data class Problem(
    var name: String,
    var status: String,
    var date: Instant? = null,
    var id: ProblemId = DomainId.create()
) : Serializable {
    fun toProto() = problem {
        val problem = this@Problem
        this.name = problem.name
        this.status = ProblemStatus.fromString(problem.status)
        this.date = nullableInt64Value(problem.date)
        this.id = problem.id.toByteString()
    }

    companion object {
        fun fromProto(proto: InfoCommands.Problem): Problem {
            return Problem( // KnownEquipment.values().protoToString(equipmentItem.type)
                name = proto.name,
                status = ProblemStatus.values().protoToString(proto.status) ?: "",
                id = proto.id.toDomainId(),
                date = proto.date.toInstant()
            )
        }
    }
}

enum class ProblemStatus(override val protoIndex: Int, override val dataString: String):
    ProtoEnum {
    ACTIVE(1, "ACTIVE"),
    CANCELLED(2, "CANCELLED"),
    PROPOSED(3, "PROPOSED"),
    INACTIVE(4, "INACTIVE"),
    RESOLVED(5, "RESOLVED"),
    ;

    companion object{
        fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)
    }
}