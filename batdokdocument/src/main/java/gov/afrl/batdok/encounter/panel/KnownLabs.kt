package gov.afrl.batdok.encounter.panel

import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

enum class KnownLabs(override val protoIndex: Int, override val dataString: String, val defaultUnit: String? = null): ProtoEnum {
    ABS_BASOPHIL(1, "Abs Basophil"),
    ABS_EOSINOPHIL(2, "Abs Eosinophil"),
    ABS_MONOCYCLE(3, "Abs Monocyte", "x1000/mcL"),
    ABS_NEUTROPHIL(4, "Abs Neutrophil"),
    ALBUMIN(5, "Albumin"),
    ALP(6, "ALP"),
    ALT(7, "ALT"),
    AST(8, "AST"),
    BASOPHIL(9, "Basophil"),
    @Deprecated("Use specific arterial or venous value") BE(10, "BE"),
    BILIRUBIN(11, "Bilirubin"),
    BUN(12, "BUN", "mg/dL"),
    CO2(13, "CO2"),
    CA(14, "CA"),
    CL(15, "Cl", "mmol/L"),
    CR(16, "Cr", "mg/dL"),
    @Deprecated("Use Cr") CREAT(17, "CREAT"),
    EGFR(18, "eGFR"),
    EOSINOPHIL(19, "Eosinophil"),
    @Deprecated("Use Electrolyte Glucose or Fingerstick Glucose") GLUCOSE(20, "Glucose", "mg/dL"),
    @Deprecated("Use specific arterial or venous value") HCO3(21, "HCO3"),
    HCT(22, "HCT", "%"),
    HGB(23, "Hgb", "g/dL"),
    ICA(24, "iCA", "mmol/L"),
    INR(25, "INR"), // no unit
    K(26, "K", "mmol/L"),
    LYMPHOCYTE(27, "Lymphocyte", "%"),
    MCHC(28, "MCHC"),
    MCV(29, "MCV", "fL"),
    MONOCYTE(30, "Monocyte", "%"),
    MPV(31, "MPV", "fL"),
    NA(32, "Na", "mmol/L"),
    NEUTROPHIL(33, "Neutrophil"),
    @Deprecated("Use specific arterial or venous value") PAO2(34, "PaO2"),
    @Deprecated("Use specific arterial or venous value") PACO2(35, "PaCO2"),
    @Deprecated("Use specific arterial or venous value") PH(36, "pH"),
    PLT(37, "PLT", "x1000/mcL"),
    PT(38, "PT", "seconds"),
    PTT(39, "PTT"),
    RBC(40, "RBC", "x1000000/mcL"),
    RDW(41, "RDW"),
    SAO2(42, "SaO2", "%"),
    TP(43, "tP"),
    WBC(44, "WBC", "x1000/mcL"),
    ABS_LYMPHOCYTE(45, "Abs Lymphocyte", "x1000/mcL"),
    ABS_GRANULOCYTE(46, "Abs Granulocyte", "x1000/mcL"),
    GRANULOCYTE(47, "Granulocyte", "%"),
    ANION_GAP(48, "Anion Gap", "mEq/L"),
    TOTAL_CO2(49, "Total CO2", "mmol/L"),
    GLUCOSE_ELECTROLYTE_PANEL(50, "Electrolyte Glucose", "mg/dL"),
    GLUCOSE_FINGERSTICK(51, "Glucometer", "mg/dL"),
    ARTERIAL_PH(52, "Arterial pH"),
    ARTERIAL_PACO2(53, "Arterial PaCO2", "mmHg"),
    ARTERIAL_PAO2(54, "Arterial PaO2", "mmHg"),
    ARTERIAL_HCO3(55, "Arterial HCO3", "mmol/L"),
    ARTERIAL_BE(56, "Arterial BE", "mmol/L"),
    ARTERIAL_LACTATE(57, "Arterial Lactate", "mmol/L"),
    VENOUS_PH(58, "Venous pH"),
    VENOUS_PACO2(59, "Venous PaCO2", "mmHg"),
    VENOUS_PAO2(60, "Venous PaO2", "mmHg"),
    VENOUS_HCO3(61, "Venous HCO3", "mmol/L"),
    VENOUS_BE(62, "Venous BE", "mmol/L"),
    VENOUS_LACTATE(63, "Venous Lactate", "mmol/L"),
    SVO2(64, "Venous O2 Sat", "%"),
    CKMB(65, "CK-MB", "ng/mL"),
    BNP(66, "BNP", "pg/mL"),
    TROPONIN_1(67, "Troponin I", "ng/mL"),
    GFAP(68, "GFAP", "pg/mL"),
    UCHL1(69, "UCH-L1", "pg/mL"),
    G6PD(70, "G6PD", "unit/L"),
    ETOH(71, "EtOH", "mg/dL"),
    UA_BLOOD(72, "Urinalysis Blood"),
    UA_KETONES(73, "Urinalysis Ketones"),
    UA_GLUCOSE(74, "Urinalysis Glucose"),
    UA_LEUKOCYTES(75, "Urinalysis Leukocytes"),
    UA_PROTEIN(76, "Urinalysis Protein"),
    UA_NITRITES(77, "Urinalysis Nitrites"),
    UA_BILIRUBIN(78, "Urinalysis Bilirubin"),
    UA_UROBILINOGEN(79, "Urinalysis Urobilinogen"),
    UA_PH(80, "Urinalysis pH"),
    UA_SPECIFIC_GRAVITY(81, "Specific Gravity"),
    ;

    companion object{
        fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)
    }
}