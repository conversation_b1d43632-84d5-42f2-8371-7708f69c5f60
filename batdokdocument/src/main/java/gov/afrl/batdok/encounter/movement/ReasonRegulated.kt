package gov.afrl.batdok.encounter.movement;

import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.util.*

enum class ReasonRegulated(override val protoIndex: Int, override val dataString: String) :
    ProtoEnum {
    AF(1, "After the Fact"),
    AR(2, "Administrative Requirements/Others"),
    AT(3, "Medical or Nonmedical Attendant"),
    BA(4, "Board Action"),
    CC(5, "Closest With Capability"),
    CR(6, "Other Clinical Reasons"),
    CT(7, "Continuity of Treatment"),
    DS(8, "Nearest MTF to Duty Station"),
    HU(9, "Humanitarian"),
    OP(10, "Outpatient"),
    PR(11, "Nearest MTF to Place of Residence"),
    SA(12, "Service Agreement"),
    TC(13, "Teaching Case"),
    XD(14, "Recovered Patient Returning to Duty Station"),
    XP(15, "Recovered Patient Returning to Place of Residence");

    companion object {
        fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) =
            entries.toTypedArray().protoToString(enum)

        fun fromString(string: String?) = entries.toTypedArray().stringToProto(string)

        fun toEvent(enum: SharedProtobufObjects.CompatibleEnum) = entries.toTypedArray()
            .toChangeOrClearEvent(enum, "Reason Regulated")
    }
}
