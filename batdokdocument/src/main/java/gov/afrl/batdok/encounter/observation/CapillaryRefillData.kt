package gov.afrl.batdok.encounter.observation

import gov.afrl.batdok.commands.proto.Observations
import gov.afrl.batdok.commands.proto.capillaryRefillData

class CapillaryRefillData(val data: Int): ObservationData {
    internal constructor(data: Observations.CapillaryRefillData): this(data.time)
    override fun toProtobuf() = capillaryRefillData { this.time = data }

    override fun toDetailString(): String {
        return (this.data).toString() + " seconds"
    }
}