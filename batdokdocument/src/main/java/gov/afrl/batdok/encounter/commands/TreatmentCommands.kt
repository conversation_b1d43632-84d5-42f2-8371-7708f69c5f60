package gov.afrl.batdok.encounter.commands

import com.google.protobuf.Any
import com.google.protobuf.Empty
import com.google.protobuf.copy
import gov.afrl.batdok.commands.eventHandler
import gov.afrl.batdok.commands.handler
import gov.afrl.batdok.commands.handlerWithId
import gov.afrl.batdok.commands.proto.TreatmentCommands
import gov.afrl.batdok.commands.proto.TreatmentCommands.AddTreatment
import gov.afrl.batdok.commands.proto.TreatmentCommands.UpdateTreatment
import gov.afrl.batdok.commands.proto.addTreatment
import gov.afrl.batdok.commands.proto.removeTreatment
import gov.afrl.batdok.commands.proto.updateTreatment
import gov.afrl.batdok.encounter.KnownEventTypes
import gov.afrl.batdok.encounter.Treatments
import gov.afrl.batdok.encounter.ids.TreatmentId
import gov.afrl.batdok.encounter.ids.toByteString
import gov.afrl.batdok.encounter.ids.toDomainId
import gov.afrl.batdok.encounter.ids.toDomainIdOrElse
import gov.afrl.batdok.encounter.treatment.CommonTreatments
import gov.afrl.batdok.encounter.treatment.Treatment
import gov.afrl.batdok.encounter.treatment.TreatmentData
import gov.afrl.batdok.util.addPreviousFields
import gov.afrl.batdok.util.toNullableInstant
import gov.afrl.batdok.util.int64Value
import gov.afrl.batdok.util.removeDuplicates
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant

fun buildAddTreatmentCommand(treatment: Treatment) = addTreatment {
    this.treatment = CommonTreatments.fromString(treatment.name)
    treatment.timestamp?.let { this.timestamp = it.epochSecond }
    treatment.treatmentData?.toProtobuf()?.let { this.treatmentData = Any.pack(it, "") }
    this.id = treatment.id.toByteString()
}

fun buildAddTreatmentCommand(name: String, data: TreatmentData? = null, timestamp: Instant? = Instant.now(), id: TreatmentId = DomainId.nil()) = addTreatment {
    this.treatment = CommonTreatments.fromString(name)
    timestamp?.let{ this.timestamp = it.epochSecond}
    data?.toProtobuf()?.let { this.treatmentData = Any.pack(it, "") }
    this.id = id.toByteString()
}
fun addTreatmentCommandHandler(treatments: Treatments) = handlerWithId<AddTreatment>{ docId, command ->
    treatments += Treatment(
        CommonTreatments.fromProto(treatment),
        CommonTreatments.getTreatmentData(treatmentData),
        id.toDomainIdOrElse(command.commandId),
        toNullableInstant(timestamp),
        docId
    )
}
fun addTreatmentEventCommandHandler() = eventHandler<AddTreatment>(KnownEventTypes.TREAT.dataString){
    "Added " + Treatment(
        CommonTreatments.fromProto(treatment),
        CommonTreatments.getTreatmentData(treatmentData),
        it.commandId.toDomainId(),
        toNullableInstant(timestamp)
    ).toEvent()
}

fun buildUpdateTreatmentCommand(treatment: Treatment, oldTreatment: Treatment? = null) = updateTreatment {
    treatment.timestamp?.let { this.timestamp = int64Value(it.epochSecond) }
    this.treatmentId = treatment.id.toByteString()
    val oldTreatmentProtobuf = oldTreatment?.treatmentData?.toProtobuf()
    treatment.treatmentData?.toProtobuf()?.let { this.treatmentData = Any.pack(it.removeDuplicates(oldTreatmentProtobuf), "") }
}
fun updateTreatmentCommandHandler(treatments: Treatments) = handlerWithId<UpdateTreatment>{ docId, _ ->
    val treatment = treatments[treatmentId.toDomainId<TreatmentId>()]
    val restoredData = Empty.parseFrom(treatmentData.value).addPreviousFields(treatment?.treatmentData?.toProtobuf())
    treatments += Treatment(
        treatment?.name,
        CommonTreatments.getTreatmentData(treatmentData.copy{
            value = restoredData.toByteString()
        }),
        treatmentId.toDomainId(),
        toNullableInstant(timestamp.value),
        docId
    )
}
fun updateTreatmentEventCommandHandler(treatments: Treatments) = eventHandler<UpdateTreatment>(
    KnownEventTypes.TREAT.dataString,
    referenceId = { treatmentId.toDomainId() },
    handler = {
        val treatment = treatments[treatmentId.toDomainId<TreatmentId>()]
        val restoredData = Empty.parseFrom(treatmentData.value).addPreviousFields(treatment?.treatmentData?.toProtobuf())
        "Updated " + Treatment(
            treatment?.name,
            CommonTreatments.getTreatmentData(treatmentData.copy{
                value = restoredData.toByteString()
            }),
            it.commandId.toDomainId(),
            toNullableInstant(timestamp.value)
        ).toEvent()
    }
)

fun buildRemoveTreatmentCommand(treatmentId: TreatmentId, docError: Boolean = false) = removeTreatment {
    this.treatmentId = treatmentId.toByteString()
    this.documentationError = docError
}
fun removeTreatmentCommandHandler(treatments: Treatments) = handler<TreatmentCommands.RemoveTreatment>{
    treatments.removeItem(treatmentId.toDomainId(), Instant.ofEpochSecond(it.timestamp), documentationError)
}

fun removeTreatmentEventCommandHandler(treatments: Treatments) = eventHandler<TreatmentCommands.RemoveTreatment>(
    KnownEventTypes.TREAT.dataString,
    referenceId = { treatmentId.toDomainId() }
) {
    val treatment = treatments[treatmentId.toDomainId<TreatmentId>()] ?: return@eventHandler null
    "Removed " + treatment.toEvent() + (if (documentationError) " due to documentation error" else "")
}