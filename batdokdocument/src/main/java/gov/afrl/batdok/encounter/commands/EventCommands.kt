package gov.afrl.batdok.encounter.commands

import gov.afrl.batdok.commands.handler
import gov.afrl.batdok.commands.handlerWithId
import gov.afrl.batdok.commands.proto.*
import gov.afrl.batdok.commands.proto.EventCommands.AddComment
import gov.afrl.batdok.encounter.Event
import gov.afrl.batdok.encounter.Events
import gov.afrl.batdok.encounter.KnownEventTypes
import gov.afrl.batdok.encounter.ids.EventId
import gov.afrl.batdok.encounter.ids.toByteString
import gov.afrl.batdok.encounter.ids.toDomainId
import gov.afrl.batdok.encounter.ids.toDomainIdOrElse
import gov.afrl.batdok.util.booleanValue
import gov.afrl.batdok.util.int64Value
import gov.afrl.batdok.util.stringValue
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant

fun buildAddEventCommand(
    timestamp: Instant?,
    text: String,
    showTime: Boolean,
    eventType: String,
    customEvent: Boolean,
    id: EventId = DomainId.nil()
): EventCommands.AddEventCommand = addEventCommand {
    timestamp?.let{ this.timestamp = it.epochSecond }
    this.text = text
    this.showTime = showTime
    this.eventType = KnownEventTypes.fromString(eventType)
    this.customEvent = customEvent
    this.id = id.toByteString()
}

fun buildAddEventCommand(
    timestamp: Instant?,
    text: String,
    showTime: Boolean,
    eventType: KnownEventTypes,
    customEvent: Boolean,
    id: EventId = DomainId.nil()
): EventCommands.AddEventCommand = buildAddEventCommand(timestamp, text, showTime, eventType.dataString, customEvent, id)

fun addEventCommandHandler(events: Events) = handlerWithId<EventCommands.AddEventCommand> { docId, command ->
    val event = Event(
        text,
        id.toDomainIdOrElse(command.commandId),
        Instant.ofEpochSecond(timestamp),
        docId,
        command.callsign,
        KnownEventTypes.fromProto(eventType),
        showTime,
        customEvent
    )
    events += event
}

fun buildEditEventCommand(
    id: EventId,
    timestamp: Instant?,
    text: String?,
    showTime: Boolean?,
    eventType: String?,
    customEvent: Boolean?
): EventCommands.EditEventCommand = editEventCommand {
    this.id = id.toByteString()
    timestamp?.let { this.timestamp = int64Value(it.epochSecond) }
    text?.let { this.text = stringValue(it) }
    showTime?.let { this.showTime = booleanValue(showTime) }
    eventType?.let { this.eventType = KnownEventTypes.fromString(it) }
    customEvent?.let { this.customEvent = booleanValue(it) }
}


fun editEventCommandHandler(events: Events) = handlerWithId<EventCommands.EditEventCommand> { docId, command ->
    val event = events[id.toDomainId<EventId>()] ?: Event(
        text.value,
        id.toDomainId(),
        Instant.ofEpochSecond(timestamp.value),
        docId,
        command.callsign,
        KnownEventTypes.fromProto(eventType),
        showTime.value,
        customEvent.value
    )
    events += Event(
        if(hasText()) text.value else event.event,
        event.id,
        if(hasTimestamp()) Instant.ofEpochSecond(<EMAIL>) else event.timestamp!!,
        event.documentId,
        event.callsign,
        if(hasEventType()) KnownEventTypes.fromProto(<EMAIL>) else event.eventType,
        if(hasShowTime()) showTime.value else event.showTime,
        if(hasCustomEvent()) customEvent.value else event.isCustom
    )
}

fun buildRemoveEventCommand(id: EventId): EventCommands.RemoveEventCommand =
    removeEventCommand { this.id = id.toByteString() }

fun removeEventCommandHandler(events: Events) = handler<EventCommands.RemoveEventCommand> {
    events.removeItem(id.toDomainId(), Instant.ofEpochSecond(it.timestamp))
}

fun buildAddCommentCommand(referencedId: DomainId, comment: String, timestamp: Instant, id: DomainId = DomainId.nil()) = addComment{
    this.referencedId = referencedId.toByteString()
    this.text = comment
    this.timestamp = timestamp.epochSecond
    this.id = id.toByteString()
}
fun addCommentCommandHandler(events: Events) = handlerWithId<AddComment>{ docId, command ->
    val event = Event(
        text,
        id.toDomainIdOrElse(command.commandId),
        Instant.ofEpochSecond(timestamp),
        docId,
        command.callsign,
        KnownEventTypes.COMMENT.dataString,
        false,
        true,
        referencedItem = referencedId.toDomainId()
    )
    events += event
}