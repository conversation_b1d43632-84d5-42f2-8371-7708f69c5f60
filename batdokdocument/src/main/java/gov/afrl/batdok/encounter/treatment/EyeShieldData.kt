package gov.afrl.batdok.encounter.treatment

import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.TreatmentCommands.EyeShield
import gov.afrl.batdok.commands.proto.eyeShield
import gov.afrl.batdok.util.booleanValue

class EyeShieldData(val left: <PERSON><PERSON><PERSON>,
                    val right: Boolean): TreatmentData{
    override fun toProtobuf(): Message = eyeShield {
        left = booleanValue(<EMAIL>)
        right = booleanValue(<EMAIL>)
    }

    override fun toDetailString(): String {
        return when{
            left && right -> "Both eyes"
            left -> "Left eye"
            right -> "Right eye"
            else -> ""
        }
    }

    companion object{
        fun fromProtobuf(eyeShield: EyeShield) = EyeShieldData(
            eyeShield.left.value,
            eyeShield.right.value,
        )
    }
}