package gov.afrl.batdok.encounter.metadata

import com.google.protobuf.Message
import com.google.protobuf.StringValue
import com.google.protobuf.kotlin.isA
import com.google.protobuf.kotlin.unpack
import gov.afrl.batdok.commands.proto.*
import gov.afrl.batdok.commands.proto.SharedProtobufObjects.CompatibleEnum
import gov.afrl.batdok.commands.proto.StratevacMissionDataCommand.*
import gov.afrl.batdok.util.*

enum class MajorEvents(override val protoIndex: Int, override val dataString: String): ProtoEnum {
    SPO2(1, "Spo2"),
    HR_CHANGE(2, "Hr change"),
    INCREASE_O2_REQUIRED(3, "Increase o2 required"),
    MAP_CHANGE(4, "Map change"),
    TEMP_LOW(5, "Temp low"),
    ET_CUFF(6, "Et cuff"),
    AIRWAY_LOSS(7, "Airway loss"),
    ICP_GREATER(8, "Icp greater"),
    CPP_LESS(9, "Cpp less"),
    CARDIAC_ARREST(10, "Cardiac arrest"),
    TRAUMA_CRITICAL(11, "Trauma critical"),
    INFLIGHT_EMERGENCY(12, "Inflight emergency"),
    RAPID_DECOMPRESSION(13, "Rapid decompression"),
    FIRE_FUMES(14, "Fire fumes"),
    AC_DIVERT(15, "Ac divert"),
    TAIL_SWAP(16, "Tail swap"),
    ARRHYTHMIA(17, "Arrhythmia"),
    LOSS_EXTREMITY(18, "Loss extremity"),
    LOSS_OF_LINE(19, "Loss of line"),
    LOSS_OF_TUBE(20, "Loss of tube"),
    BLEEDING(21, "Bleeding"),
    BLADDER_PRESSURE(22, "Bladder pressure"),
    CODE(23, "Code"),
    ;

    companion object{
        fun fromProto(enum: CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)

        fun getExtra(proto: MajorEventItem) = with(proto.extras){
            when{
                isA<StringValue>() -> FreeTextData(unpack())
                isA<ArrhythmiaExtras>() -> ArrhythmiaData(unpack())
                isA<BleedingExtras>() -> BleedingData(unpack())
                else -> null
            }
        }
    }
}

interface MajorEventData{
    fun toProto(): Message
    fun toEventString(): String?
}

class FreeTextData(val string: String): MajorEventData{
    internal constructor(proto: StringValue): this(proto.value)
    override fun toProto() = stringValue(string)

    override fun toEventString() = string
}

/**
 * Rhythm: True -> Tachycardia, False -> Bradycardia
 *
 * Width: True -> Narrow, False -> Wide
 */
class ArrhythmiaData(val rhythm: Boolean? = null, val width: Boolean? = null): MajorEventData{
    internal constructor(proto: ArrhythmiaExtras): this(proto.tachyOrNull?.value, proto.narrowOrNull?.value)
    override fun toProto() = arrhythmiaExtras {
        rhythm?.let { tachy = booleanValue(it) }
        width?.let { narrow = booleanValue(it) }
    }

    override fun toEventString(): String? {
        return listOfNotNull(
            when(rhythm){
                true -> "Tachycardia"
                false -> "Bradycardia"
                else -> null
            },
            when(width){
                true -> "Narrow"
                false -> "Wide"
                else -> null
            }
        ).joinToString(", ")
            .takeUnless { it.isEmpty() }
    }
}

class BleedingData(val site: String? = null, val estVol: String? = null): MajorEventData{
    internal constructor(proto: BleedingExtras): this(proto.siteOrNull?.value, proto.estVolOrNull?.value)
    override fun toProto() = bleedingExtras {
        <EMAIL>?.let { site = stringValue(it) }
        <EMAIL>?.let { estVol = stringValue(it) }
    }

    override fun toEventString(): String? {
        return listOfNotNull(site, estVol)
            .joinToString(", ")
            .takeUnless { it.isEmpty() }
    }
}