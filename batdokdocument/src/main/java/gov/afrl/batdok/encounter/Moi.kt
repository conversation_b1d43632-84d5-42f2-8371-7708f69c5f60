package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.proto.SharedProtobufObjects.CompatibleEnum
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto


enum class Moi(override val protoIndex: Int, override val dataString: String): ProtoEnum {
    // BURN, GSW, OTHER_MOI, CRUSH are all considered to be both a MOI and Injury
    @Deprecated("Deprecated do not use")
    ARTY(1, "Artillery"),
    BURN(2, "Burn"),
    @Deprecated("Deprecated do not use")
    GRENADE(3, "Grenade"),
    @Deprecated("Deprecated do not use")
    LANDMINE_DSMT(4, "Landmine (Dsmt)"),
    @Deprecated("Deprecated do not use")
    RPG(5, "RPG"),
    @Deprecated("Deprecated do not use")
    BLUNT(6, "Blunt"),
    FALL(7, "Fall"),
    GSW(8, "Gunshot Wound"),
    MVC(9, "Motor Vehicle Collision"),
    @Deprecated("Deprecated do not use")
    IED_DSMT(10, "IED (Dsmt)"),
    @Deprecated("Deprecated do not use")
    OTHER_MOI(11, "Other"),
    @Deprecated("Deprecated do not use")
    LANDMINE_MT(12, "Landmine (Mt)"),
    @Deprecated("Deprecated do not use")
    IED_MT(13, "IED (Mt)"),
    @Deprecated("Deprecated do not use")
    AIRBORNE_OPERATION(14, "Airborne Operation"),
    @Deprecated("Deprecated do not use")
    AIRCRAFT_CRASH(15, "Aircraft Crash"),
    BLAST(16, "Blast"),
    CRUSH(17, "Crush"),
    @Deprecated("Deprecated do not use")
    FIRE(18, "Fire"),
    @Deprecated("Deprecated do not use")
    ENVIRONMENTAL(19,"Environmental"),
    @Deprecated("Deprecated do not use")
    EXPLOSION (20,"Explosion"),
    @Deprecated("Deprecated do not use")
    INDIRECT_FIRE(21, "Indirect Fire"),
    @Deprecated("Deprecated do not use")
    FRAGMENTATION(22, "Fragmentation"),
    @Deprecated("Deprecated do not use")
    DROWNING(23, "Drowning"),
    @Deprecated("Deprecated do not use")
    PARACHUTE_INCIDENT(24, "Parachute Incident"),
    AIRCRAFT_INCIDENT(25,"Aircraft Incident"),
    ANIMAL_BITE(26,"Animal Bite/Mauling"),
    ASPHYXIATION(27,"Asphyxiation"),
    ASSAULT(28,"Assault"),
    CRBN(29, "CBRN"),
    IMPALEMENT(30, "Impalement"),
    PARACHUTE_INCIDENT_OPERATIONS(31, "Parachute Incident/Airborne Operations"),
    SPORTS(32,"Sports"),
    STAB_WOUND(33, "Stab Wound");

    companion object{
        fun fromProto(enum: CompatibleEnum) = Moi.values().protoToString(enum)
        fun fromString(string: String?) = Moi.values().stringToProto(string)
    }
}