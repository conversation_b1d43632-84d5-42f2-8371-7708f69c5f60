package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.CommandHandler
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.commands.*
import gov.afrl.batdok.encounter.treatment.CommonTreatments
import gov.afrl.batdok.encounter.treatment.DressingData
import gov.afrl.batdok.encounter.treatment.TqData
import gov.afrl.batdok.encounter.treatment.Treatment
import gov.afrl.batdok.util.ICategorizedItemList
import gov.afrl.batdok.util.SortedCategorizedLinkableList
import java.io.Serializable

class Treatments: ICategorizedItemList<Treatment> by SortedCategorizedLinkableList(), Serializable {

    @Transient val handlers = CommandHandler().apply {
        val treatments = this@Treatments
        +addTreatmentCommandHandler(treatments)
        +updateTreatmentCommandHandler(treatments)
        +removeTreatmentCommandHandler(treatments)
    }

    companion object{
        fun EventCommandHandler.includeTreatmentEvents(treatments: Treatments) = apply{
            +addTreatmentEventCommandHandler()
            +updateTreatmentEventCommandHandler(treatments)
            +removeTreatmentEventCommandHandler(treatments)
        }
    }

    /**
     * Get a comma separated String of all treatments
     */
    @JvmOverloads
    fun getTreatmentString(limitTreatments: Boolean, treatmentLimit: Int = 15, separator: String = ", "): String {
        val filteredTreatments = listOf(CommonTreatments.TQ.dataString, CommonTreatments.DRESSING.dataString)
        fun tqCountsByType(): Map<String?, Int>{
            return get(CommonTreatments.TQ)
                .groupBy { if(it.treatmentData is TqData) it.treatmentData.tqLocation else null }
                .mapValues { it.value.size }
                .filterKeys { it != null }
        }
        fun dressingsByType(): Map<String?, Int>{
            return get(CommonTreatments.DRESSING)
                .groupBy { if(it.treatmentData is DressingData) it.treatmentData.type else null }
                .mapValues { it.value.size }
                .filterKeys { it != null }
        }
        fun getTreatmentString(treatment: String, treatmentCount: Int) = "$treatment x$treatmentCount".takeIf { treatmentCount > 0 }


        val treatments = (tqCountsByType().map {
            getTreatmentString("TQ-${it.key}", it.value)
        } + dressingsByType().map {
            getTreatmentString(it.key + " Dressing", it.value)
        } + listOfNotNull(
            list
                .asSequence()
                .filter { !filteredTreatments.contains(it.name) }
                .mapNotNull { it.name }
                .distinct()
                .map { getTreatmentString(it, count(it)) }
                .joinToString(separator)
                .takeIf { it.isNotEmpty() }
        )).joinToString(separator)

        val treatmentList = treatments.split(separator)
        val treatmentsInRange = treatmentList.take(treatmentLimit).joinToString(
            separator,
            postfix = if(treatmentList.size > treatmentLimit) {
                "$separator(" + (treatmentList.size - treatmentLimit) + " more treatments can be viewed in BATDOK)"
            } else {
                ""
            }
        )

        return if (limitTreatments) {
            treatmentsInRange
        } else {
            treatments
        }
    }
}