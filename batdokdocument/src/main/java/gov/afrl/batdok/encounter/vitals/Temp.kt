package gov.afrl.batdok.encounter.vitals

import gov.afrl.batdok.commands.proto.VitalOuterClass
import gov.afrl.batdok.commands.proto.temp
import gov.afrl.batdok.encounter.IndividualVital
import gov.afrl.batdok.util.floatValue
import gov.afrl.batdok.util.stringValue

class Temp(
    val temp: Float?,
    val measurementMethod: String? = null
): IndividualVital("Temp"){
    constructor(temp: VitalOuterClass.Temp): this(
        temp.temp.value.takeIf { temp.hasTemp() },
        temp.measurementMethod.value.takeIf { temp.hasMeasurementMethod() }
    )
    override fun toProtobuf() = temp {
        <EMAIL>?.let { this.temp = floatValue(it) }
        <EMAIL>?.let { this.measurementMethod = stringValue(it) }
    }
    override fun produceEmptyVital() = Temp(null)

    private fun convertToCelsius(fahrenheit: Float): Float {
        return (fahrenheit - 32) * 5 / 9
    }

    override val eventMessage: String
        get() {
            val tempInFahrenheit = temp?.let { "%.1f (F)".format(it) } ?: "--"
            val tempInCelsius = temp?.let { "%.1f (C)".format(convertToCelsius(it)) } ?: "--"
            return listOfNotNull(
                tempInFahrenheit,
                tempInCelsius,
                measurementMethod?.takeIf { it.isNotBlank() }
            ).joinToString(" ")
        }


    override val isEmpty: Boolean
        get() = temp == null && measurementMethod == null
}