package gov.afrl.batdok.encounter.movement

import gov.afrl.batdok.commands.proto.SharedProtobufObjects.CompatibleEnum
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto
import gov.afrl.batdok.util.toChangeOrClearEvent

@Deprecated("Use TriageCategory", replaceWith = ReplaceWith("TriageCategory"))
typealias EvacCategory = TriageCategory

enum class TriageCategory(
    override val protoIndex: Int,
    override val dataString: String,
    val buttonString: String = dataString
) : ProtoEnum {
    @Deprecated("Use Immediate instead of Urgent", ReplaceWith("TriageCategory.IMMEDIATE"))
    URGENT(1, "Urgent"),
    @Deprecated("Use Delayed instead of Priority", ReplaceWith("TriageCategory.DELAYED"))
    PRIORITY(2, "Priority"),
    @Deprecated("Use Minimal instead of Routine", ReplaceWith("TriageCategory.MINIMAL"))
    ROUTINE(3, "Routine"),
    EXPECTANT(4, "Expectant", "Expect."),
    IMMEDIATE(5, "Immediate"),
    DELAYED(6, "Delayed"),
    MINIMAL(7, "Minimal");

    companion object {
        fun fromProto(enum: CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)
        fun toEvent(enum: CompatibleEnum) = values().toChangeOrClearEvent(enum, "triage status")
    }
}