package gov.afrl.batdok.encounter.commands

import com.google.protobuf.Int64Value
import gov.afrl.batdok.commands.eventHandler
import gov.afrl.batdok.commands.handler
import gov.afrl.batdok.commands.proto.*
import gov.afrl.batdok.commands.proto.DeathDeclarationCommands.ChangeDeathDeclaration
import gov.afrl.batdok.commands.proto.DeathDeclarationCommands.RemoveDeathDeclaration
import gov.afrl.batdok.commands.proto.InfoCommands.AddMediaFile
import gov.afrl.batdok.commands.proto.InfoCommands.UpdateCreationAlias
import gov.afrl.batdok.commands.proto.InfoCommands.UpdateThumbnail
import gov.afrl.batdok.commands.proto.InfoCommands.UpdateCreationName
import gov.afrl.batdok.commands.proto.Signatures.*
import gov.afrl.batdok.commands.proto.StratevacCommands.ChangeFlightInfoCommand
import gov.afrl.batdok.commands.proto.StratevacMissionDataCommand.*
import gov.afrl.batdok.encounter.Name
import gov.afrl.batdok.encounter.Physician
import gov.afrl.batdok.encounter.changeOrClearEvent
import gov.afrl.batdok.encounter.ids.toByteString
import gov.afrl.batdok.encounter.ids.toDomainId
import gov.afrl.batdok.encounter.metadata.*
import gov.afrl.batdok.encounter.metadata.Signature
import gov.afrl.batdok.util.*
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant
import java.time.LocalDate

fun buildChangeDecisionCommand(decision: String, added: Boolean) = changeDecisionCommand {
    this.decisionType = Decision.fromString(decision)
    checked = added
}
fun changeDecisionCommandHandler(metadata: Metadata) = handler<ChangeDecisionCommand> {
    val decision = Decision.fromProto(decisionType) ?: return@handler
    if(checked) {
        metadata.decisionsMade += decision
    }else{
        metadata.decisionsMade -= decision
    }
}
fun changeDecisionEventCommandHandler() = eventHandler<ChangeDecisionCommand> {
    val action = if(checked) "Added" else "Removed"
    "$action decision: ${Decision.fromProto(decisionType)}"
}

fun buildChangeCareCommand(careType: String, added: Boolean) = changeCareCommand {
    this.careType = CareType.fromString(careType)
    checked = added
}
fun changeCareCommandHandler(metadata: Metadata) = handler<ChangeCareCommand> {
    val careType = CareType.fromProto(careType) ?: return@handler
    if(checked) {
        metadata.careProvided += careType
    }else{
        metadata.careProvided -= careType
    }
}
fun changeCareEventCommandHandler() = eventHandler<ChangeCareCommand> {
    val action = if(checked) "Added" else "Removed"
    "$action care provided: ${CareType.fromProto(careType)}"
}
fun buildChangeInfectionControlPrecautionCommand(precaution: String, added: Boolean) = changeInfectionControlPrecautionCommand {
    this.infectionControlPrecaution = InfectionControl.fromString(precaution)
    checked = added
}
fun changeInfectionControlPrecautionCommandHandler(metadata: Metadata) = handler<ChangeInfectionControlPrecautionCommand> {
    val precaution = InfectionControl.fromProto(infectionControlPrecaution) ?: return@handler
    if (checked) {
        metadata.infectionControlPrecautions += precaution
    } else {
        metadata.infectionControlPrecautions -= precaution
    }
}
fun changeInfectionControlPrecautionEventCommandHandler() = eventHandler<ChangeInfectionControlPrecautionCommand> {
    val action = if(checked) "Added" else "Removed"
    "$action infection control precaution: ${InfectionControl.fromProto(infectionControlPrecaution)}"
}

fun buildChangeProcedureCommand(procedure: Procedure, add: Boolean = true) = procedure.toProto().copy {
    checked = add
}
fun changeProcedureCommandHandler(metadata: Metadata) = handler<ChangeProcedureCommand> {
    val procedure = Procedure.fromProto(this) ?: return@handler
    if(checked) {
        metadata.proceduresDone += procedure
    }else{
        metadata.proceduresDone -= procedure
    }
}
fun changeProcedureEventCommandHandler() = eventHandler<ChangeProcedureCommand> {
    val action = if(checked) "Added" else "Removed"
    val procedure = Procedure.fromProto(this)
    "$action procedure performed: ${procedure?.toEventString(checked)}"
}

fun buildAddRemoveMajorEventCommand(location: String?, added: List<MajorEvent>, removed: List<MajorEvent>) = addRemoveMajorEventCommand {
    this.location = MajorEventLocations.fromString(location)
    this.addedItems.addAll(added.map { it.toProto() })
    this.removedItems.addAll(removed.map { it.toProto() })
}
fun addRemoveMajorEventCommandHandler(metadata: Metadata) = handler<AddRemoveMajorEventCommand>{
    val location = MajorEventLocations.fromProto(location)
    fun List<MajorEventItem>.fromProto() = mapNotNull{
        MajorEvents.fromProto(it.type)?.let { name ->
            val data = MajorEvents.getExtra(it)
            MajorEvent(name, data)
        }
    }
    metadata.majorEvents[location] += addedItemsList.fromProto()
    metadata.majorEvents[location] -= removedItemsList.fromProto()
}
fun addRemoveMajorEventEventCommandHandler() = eventHandler<AddRemoveMajorEventCommand>{
    val location = MajorEventLocations.fromProto(location)
    fun List<MajorEventItem>.fromProto() = mapNotNull{
        MajorEvents.fromProto(it.type)?.let { name ->
            val data = MajorEvents.getExtra(it)
            MajorEvent(name, data).toEventString()
        }
    }.joinToString(", ")
    val addedItemString = "\nAdded ${addedItemsList.fromProto()}".takeIf { addedItemsCount > 0 } ?: ""
    val removedItemString = "\nRemoved ${removedItemsList.fromProto()}".takeIf { removedItemsCount > 0 } ?: ""
    "Updated events at $location$addedItemString$removedItemString"
}

fun buildChangeFlightInfoCommand(flightInfo: FlightInfo) = changeFlightInfoCommand {
    flightInfo.id.takeUnless { it.isNil() }?.toByteString()?.let { flightInfoId = it }
    tail = flightInfo.tail
    origin = flightInfo.origin
    destination = flightInfo.destination
    flightInfo.takeoff?.epochSecond?.let { takeoff = int64Value(it) }
    flightInfo.landing?.epochSecond?.let { landing = int64Value(it) }
    flightInfo.maxAlt?.let { maxAlt = int64Value(it) }
    unit = flightInfo.unit
}

private fun Int64Value.toInstant() = value.let { Instant.ofEpochSecond(it) }
fun changeFlightInfoCommandHandler(metadata: Metadata) = handler<ChangeFlightInfoCommand>{
    metadata.flightInfo = FlightInfo(
        flightInfoId.takeIf { hasFlightInfoId() }?.toDomainId()?: DomainId.nil(),
        tail,
        origin,
        destination,
        takeoffOrNull?.toInstant(),
        landingOrNull?.toInstant(),
        maxAltOrNull?.value,
        unit
    )
}

fun changeFlightInfoCommandEventHandler() = eventHandler<ChangeFlightInfoCommand> {
    if(!hasFlightInfoId()){
        return@eventHandler "Cleared flight info"
    }
    val values = listOfNotNull(
        "Tail: $tail".takeIf{ hasTail() && !tail.isNullOrBlank() },
        "Origin: $origin".takeIf{ hasOrigin() && !origin.isNullOrBlank() },
        "Destination: $destination".takeIf{ hasDestination() && !destination.isNullOrBlank() },
        "Takeoff: ${takeoffOrNull?.toInstant()?.format(Patterns.mdhm_24_space_comma_colon)}".takeIf{ hasTakeoff() },
        "Landing: ${landingOrNull?.toInstant()?.format(Patterns.mdhm_24_space_comma_colon)}".takeIf{ hasLanding() },
        "Max Altitude: ${maxAlt.value}".takeIf{ hasMaxAlt() },
        "Unit: $unit".takeIf{ hasUnit() && !unit.isNullOrBlank()}
    ).joinToString(", ")
    "Updated Flight Info: $values"
}

fun buildAddMediaFileCommand(filename: String) = addMediaFile {
    this.filename = filename
}
fun addMediaFileCommandHandler(metadata: Metadata) = handler<AddMediaFile>{
    metadata.medialist += filename
}
fun addMediaFileEventHandler() = eventHandler<AddMediaFile> {
    "Added Media File: $filename"
}

fun buildUpdateThumbnailCommand(filename: String?) = updateThumbnail {
    this.filename = nullableStringValue(filename)
}
fun updateThumbnailCommandHandler(metadata: Metadata) = handler<UpdateThumbnail>{
    metadata.thumbnailFileName = filename.toPrimitive()
}

fun buildChangeInsuranceCommand(insurance: Insurance) = changeInsuranceCommand {
    insurance.companyName?.let { if (it.isNotBlank()) companyName = stringValue(it) }
    insurance.companyAddress?.let { if (it.isNotBlank()) companyAddress = stringValue(it) }
    insurance.companyPhone?.let { if (it.isNotBlank()) companyPhone = stringValue(it) }
    insurance.policyNumber?.let { if (it.isNotBlank()) policyNumber = stringValue(it) }
    insurance.relationToPolicyHolder?.let { if (it.isNotBlank()) relationToPolicyHolder = stringValue(it) }
}
fun changeInsuranceCommandHandler(metadata: Metadata) = handler<ChangeInsuranceCommand> {
    metadata.insurance = Insurance(this)
}
fun changeInsuranceCommandEventHandler() = eventHandler<ChangeInsuranceCommand> {
    val insurance = Insurance(this)
    changeOrClearEvent(!insurance.isBlank(), insurance.toString(), "Insurance")
}
fun buildChangeAttendingPhysicianCommand(physician: Physician) = changeAttendingPhysicianCommand {
    this.physician = physician.toProto()
}
fun changeAttendingPhysicianCommandHandler(metadata: Metadata) = handler<ChangeAttendingPhysicianCommand> {
    metadata.attendingPhysician = Physician(this.physician)
}
fun changeAttendingPhysicianCommandEventHandler() = eventHandler<ChangeAttendingPhysicianCommand> {
    val attendingPhysician = Physician(this.physician)
    changeOrClearEvent(!attendingPhysician.isBlank(), attendingPhysician.toString(), "Attending Physician")
}
fun buildSetSignaturePMRPhysicianCommand(pmrPhysicianSignature: Signature) = setSignaturePMRPhysicianCommand {
    this.pmrPhysician = pmrPhysicianSignature.toProto()
}
fun setSignaturePMRPhysicianCommandHandler(metadata: Metadata) = handler<SetSignaturePMRPhysicianCommand> {
    metadata.signatures.pmrPhysicianSignature = Signature(this.pmrPhysician)
}
fun setSignaturePMRPhysicianCommandEventHandler() = eventHandler<SetSignaturePMRPhysicianCommand> {
    "Updated PMR Physician Signature"
}

fun buildSetSignaturePMRFlightSurgeonCommand(pmrFlightSurgeonSignature: Signature) = setSignaturePMRFlightSurgeonCommand {
    this.pmrFlightSurgeon = pmrFlightSurgeonSignature.toProto()
}
fun setSignaturePMRFlightSurgeonCommandHandler(metadata: Metadata) = handler<SetSignaturePMRFlightSurgeonCommand> {
    metadata.signatures.pmrFlightSurgeonSignature = Signature(this.pmrFlightSurgeon)
}
fun setSignaturePMRFlightSurgeonCommandEventHandler() = eventHandler<SetSignaturePMRFlightSurgeonCommand> {
    "Updated PMR Flight Surgeon Signature"
}

fun buildRemoveSignaturePMRPhysicianCommand() = removeSignaturePMRPhysicianCommand {  }

fun removeSignaturePMRPhysicianCommandHandler(metadata: Metadata) = handler<RemoveSignaturePMRPhysicianCommand> {
    metadata.signatures.pmrPhysicianSignature = null
}
fun removeSignaturePMRPhysicianCommandEventHandler() = eventHandler<RemoveSignaturePMRPhysicianCommand> {
    "Removed PMR Physician Signature"
}

fun buildRemoveSignaturePMRFlightSurgeonCommand() = removeSignaturePMRFlightSurgeonCommand {  }

fun removeSignaturePMRFlightSurgeonCommandHandler(metadata: Metadata) = handler<RemoveSignaturePMRFlightSurgeonCommand> {
    metadata.signatures.pmrFlightSurgeonSignature = null
}
fun removeSignaturePMRFlightSurgeonCommandEventHandler() = eventHandler<RemoveSignaturePMRFlightSurgeonCommand> {
    "Removed PMR Flight Surgeon Signature"
}

fun buildChangeDeathDeclarationCommand(deathDeclaration: DeathDeclaration, oldDeathDeclaration: DeathDeclaration? = null) = deathDeclaration.toProto().removeDuplicates(oldDeathDeclaration?.toProto())
fun changeDeathDeclarationCommandHandler(metadata: Metadata) = handler<ChangeDeathDeclaration>{
    val restoredCommand = ChangeDeathDeclaration.parseFrom(this.addPreviousFields(metadata.deathDeclaration?.toProto()).toByteArray())
    metadata.deathDeclaration = DeathDeclaration(restoredCommand)
}
fun changeDeathDeclarationEventHandler(metadata: Metadata) = eventHandler<ChangeDeathDeclaration> {
    val restoredCommand = ChangeDeathDeclaration.parseFrom(this.addPreviousFields(metadata.deathDeclaration?.toProto()).toByteArray())
    "Declared Time of Death: " + DeathDeclaration(restoredCommand).toEventString()
}

fun buildRemoveDeathDeclarationCommand(reason: String) = removeDeathDeclaration{
    this.reason = reason
}
fun removeDeathDeclarationCommandHandler(metadata: Metadata) = handler<RemoveDeathDeclaration>{
    metadata.deathDeclaration = null
}
fun removeDeathDeclarationCommandEventHandler() = eventHandler<RemoveDeathDeclaration>{
    "Removed Declaration of death" + if(reason.isNotEmpty()) ": $reason" else ""
}

fun buildChangeAppointmentDateCommand(appointmentDate: LocalDate) = changeAppointmentDateCommand {
    this.appointmentDate = int64Value(appointmentDate.toEpochDay())
}
fun changeAppointmentDateCommandHandler(metadata: Metadata) = handler<AppointmentDateCommands.ChangeAppointmentDateCommand>{
   metadata.appointmentDate = LocalDate.ofEpochDay(this.appointmentDate.value)
}
fun changeAppointmentDateEventHandler() = eventHandler<AppointmentDateCommands.ChangeAppointmentDateCommand> {
    "Set Appointment Date: " + LocalDate.ofEpochDay(this.appointmentDate.value).format(Patterns.ymd_dash)
}

fun buildRemoveAppointmentDateCommand() = removeAppointmentDateCommand{}
fun removeAppointmentDateCommandHandler(metadata: Metadata) = handler<AppointmentDateCommands.RemoveAppointmentDateCommand>{
    metadata.appointmentDate = null
}
fun removeAppointmentDateCommandEventHandler() = eventHandler<AppointmentDateCommands.RemoveAppointmentDateCommand>{
    "Cleared Appointment Date"
}

fun buildUpdateCreationNameCommand(first: String?, middle: String?, last: String?) = updateCreationName {
    first?.let { this.first = it }
    middle?.let { this.middle = it }
    last?.let { this.last = it }
}

fun updateCreationNameCommandHandler(metadata: Metadata) = handler<UpdateCreationName>{
    metadata.creationName =   Name(first, middle, last)
}

fun buildUpdateCreationAliasCommand(alias: String?) = updateCreationAlias {
    alias?.let { this.alias = alias }
}

fun updateCreationAliasCommandHandler(metadata: Metadata) = handler<UpdateCreationAlias>{
    metadata.creationAlias = this.alias
}