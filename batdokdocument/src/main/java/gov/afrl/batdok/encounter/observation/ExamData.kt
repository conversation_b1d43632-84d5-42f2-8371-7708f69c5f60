package gov.afrl.batdok.encounter.observation

import gov.afrl.batdok.commands.proto.Observations
import gov.afrl.batdok.commands.proto.examData
import gov.afrl.batdok.commands.proto.nameOrNull
import gov.afrl.batdok.commands.proto.resultOrNull
import gov.afrl.batdok.util.nullableStringValue
import gov.afrl.batdok.util.toPrimitive

class ExamData(val name: String, val result: String): ObservationData {
    internal constructor(data: Observations.ExamData): this(
        data.nameOrNull?.toPrimitive() ?: "",
        data.resultOrNull?.toPrimitive() ?: ""
    )

    override fun toProtobuf() = examData {
        name = nullableStringValue(<EMAIL>)
        result = nullableStringValue(<EMAIL>)
    }

    override fun toDetailString(): String {
        return listOfNotNull(name, result).joinToString(": ")
    }
}