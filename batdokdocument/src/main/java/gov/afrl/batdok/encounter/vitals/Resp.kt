package gov.afrl.batdok.encounter.vitals

import gov.afrl.batdok.commands.proto.VitalOuterClass
import gov.afrl.batdok.commands.proto.resp
import gov.afrl.batdok.encounter.IndividualVital
import gov.afrl.batdok.util.int32Value

class Resp(val resp: Int?): IndividualVital("Resp"){
    constructor(resp: VitalOuterClass.Resp): this(
        resp.resp.value.takeIf { resp.hasResp() },
    )
    override fun toProtobuf() = resp {
        <EMAIL>?.let { this.resp = int32Value(it) }
    }
    override fun produceEmptyVital() = Resp(null)
    override val eventMessage: String
        get() = "$resp"

    override val isEmpty: Boolean
        get() = resp == null
}