package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.CommandHandler
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.commands.proto.SharedProtobufObjects.CompatibleEnum
import gov.afrl.batdok.commands.proto.StratevacRnIoCommands.IOItem
import gov.afrl.batdok.commands.proto.iOItem
import gov.afrl.batdok.encounter.commands.*
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.encounter.ids.InputOutputId
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.doubleValue
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto
import mil.af.afrl.batman.batdokid.DomainId
import java.io.Serializable
import java.time.Instant

@Deprecated("Use IntakeOutputs instead", ReplaceWith("IntakeOutputs"))
class InputOutputs: Serializable {
    var inputs = listOf<InputOutput>()
        internal set
    var outputs = listOf<InputOutput>()
        internal set

    operator fun List<InputOutput>.minus(id: InputOutputId) = filter { it.id != id }

    operator fun minusAssign(id: InputOutputId){
        inputs -= id
        outputs -= id
    }

    operator fun get(id: InputOutputId) = (inputs + outputs).find { it.id == id }

    @Transient val handlers = CommandHandler().apply {
        val ios = this@InputOutputs
        +addIOItemCommandHandler(ios)
        +updateIOItemCommandHandler(ios)
        +removeIOItemCommandHandler(ios)
    }

    companion object{
        fun EventCommandHandler.includeInputOutputEvents(inputOutputs: InputOutputs) = apply {
            +addIOItemEventCommandHandler()
            +updateIOItemEventCommandHandler(inputOutputs)
            +removeIOItemEventCommandHandler(inputOutputs)
        }
    }
}
@Deprecated("Use IntakeOutput instead",ReplaceWith("IntakeOutput"))
data class InputOutput(
    val id: InputOutputId,
    val timestamp: Instant,
    val name: String,
    val route: String,
    val volume: Double,
    val unit: String,
    val documentId: DocumentId = DomainId.nil()
): Serializable{
    internal constructor(id: InputOutputId, documentId: DocumentId, timestamp: Instant, data: IOItem): this(
        id,
        timestamp,
        InputOutputType.fromProto(data.name) ?: "",
        InputOutputRoute.fromProto(data.route) ?: "",
        data.value.value,
        InputOutputUnit.fromProto(data.unit) ?: "",
        documentId
    )
    fun toProto() = iOItem{
        this.name = InputOutputType.fromString(<EMAIL>)
        this.route = InputOutputRoute.fromString(<EMAIL>)
        this.value = doubleValue(volume)
        this.unit = InputOutputUnit.fromString(<EMAIL>)
    }
    fun toEventString() = listOf(name, volume.toString(), unit, route)
        .filter { it.isNotBlank() }
        .joinToString(" ")
}

@Deprecated("Use IntakeOutputType instead", ReplaceWith("IntakeOutputType"))
enum class InputOutputType(override val protoIndex: Int, override val dataString: String): ProtoEnum{
    WATER(1, "Water"),
    SALINE(2, "Saline"),
    URINE(3, "Urine"),
    ;

    companion object{
        fun fromProto(enum: CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)
    }
}
@Deprecated("Use IntakeOutputUnit instead", ReplaceWith("IntakeOutputUnit"))
enum class InputOutputUnit(override val protoIndex: Int, override val dataString: String, val conversionFactor: Double): ProtoEnum{
    MILLILITER(1, "mL", 1.0),
    CC(2, "cc", 1.0),
    LITER(3, "L", 1000.0),
    ;

    companion object{
        fun fromProto(enum: CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)
    }
}


@Deprecated("No longer used")
enum class InputOutputRoute(override val protoIndex: Int, override val dataString: String): ProtoEnum{
    IV(1, "IV"),
    CATHETER(2, "Catheter"),
    ;

    companion object{
        fun fromProto(enum: CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)
    }
}