package gov.afrl.batdok.encounter.commands

import gov.afrl.batdok.commands.eventHandler
import gov.afrl.batdok.commands.handler
import gov.afrl.batdok.commands.proto.*
import gov.afrl.batdok.commands.proto.InfoCommands.*
import gov.afrl.batdok.commands.proto.StratevacCommands.ChangeCiteNumberCommand
import gov.afrl.batdok.encounter.*
import gov.afrl.batdok.encounter.pampi.Immunization
import gov.afrl.batdok.encounter.pampi.Procedure
import gov.afrl.batdok.encounter.pampi.Problem
import gov.afrl.batdok.encounter.ids.ProblemId
import gov.afrl.batdok.encounter.ids.toByteString
import gov.afrl.batdok.encounter.ids.toDomainId
import gov.afrl.batdok.encounter.movement.EvacCategory
import gov.afrl.batdok.encounter.movement.EvacType
import gov.afrl.batdok.encounter.movement.TriageCategory
import gov.afrl.batdok.util.*
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.Locale

fun buildChangeNameCommand(name: String) = changeNameCommand {
    val formatter = NameFormatter(name)
    first = formatter.first
    middle = formatter.middle
    last = formatter.last
}
fun buildChangeNameCommand(first: String?, middle: String?, last: String?) = changeNameCommand {
    first?.let { this.first = it }
    middle?.let { this.middle = it }
    last?.let { this.last = it }
}
fun changeNameHandler(info: Info) = handler<ChangeNameCommand> {
    info.name = Name(first, middle, last)
}
fun changeNameEventHandler() = eventHandler<ChangeNameCommand>(KnownEventTypes.INFO.dataString) {
    val name = Name(first, middle, last)
    changeOrClearEvent(!name.isEmpty, name.toString(NameFormatter.Format.FIRST_MIDDLE_LAST), "name")
}

fun buildChangeBattleRosterNumberCommand(battleRosterNumber: String) =
    changeBattleRosterNumberCommand { this.battleRosterNumber = battleRosterNumber }
fun changeBattleRosterCommandHandler(info: Info) = handler<ChangeBattleRosterNumberCommand> { info.battleRosterNumber = battleRosterNumber }
fun changeBattleRosterNumberEventHandler() = eventHandler<ChangeBattleRosterNumberCommand>(KnownEventTypes.INFO.dataString) {
    changeOrClearEvent(battleRosterNumber.isNotEmpty(), battleRosterNumber, "battle roster number")
}

fun buildChangeAliasCommand(alias: String) =
    changeAliasCommand {this.alias = alias}
fun changeAliasCommandHandler(info: Info) = handler<ChangeAliasCommand> {info.alias = alias}
fun changeAliasEventHandler() = eventHandler<ChangeAliasCommand>(KnownEventTypes.INFO.dataString) {
    changeOrClearEvent(alias.isNotEmpty(), alias, "alias")
}

fun buildChangeSsnCommand(ssn: String) = changeSSNCommand { this.ssn = ssn }
fun changeSsnCommandHandler(info: Info) = handler<ChangeSSNCommand> { info.ssn = ssn }
fun changeSsnEventHandler() = eventHandler<ChangeSSNCommand>(KnownEventTypes.INFO.dataString) {
    changeOrClearEvent(ssn.isNotEmpty(), ssn, "ssn")
}

fun buildChangeHandoffCommand(callsign: String) = changeHandoffCommand{ this.callsign = callsign }
fun changeHandoffEventHandler() = eventHandler<ChangeHandoffCommand>(KnownEventTypes.INFO.dataString) {
    "Received handoff from $callsign"
}

@Deprecated("Use buildChangeTriageCommand instead.", ReplaceWith("buildChangeTriageCommand"))
fun buildChangeEvacCommand(evacCategory: EvacCategory) = changeEvacCommand {
    evac = evacCategory.toProto()
}
@Deprecated("Use buildChangeTriageCommand instead.", ReplaceWith("buildChangeTriageCommand"))
fun buildChangeEvacCommand(evacCategory: String?) = changeEvacCommand {
    evac = EvacCategory.fromString(evacCategory)
}
@Deprecated("Use changeTriageCommandHandler instead.", ReplaceWith("changeTriageCommandHandler"))
fun changeEvacCommandHandler(info: Info) = handler<ChangeEvacCommand>{
    info.evacCategory = EvacCategory.fromProto(evac) }
@Deprecated("Use changeTriageEventHandler instead.", ReplaceWith("changeTriageEventHandler"))
fun changeEvacEventHandler() = eventHandler<ChangeEvacCommand>(KnownEventTypes.INFO.dataString) {
    EvacCategory.toEvent(evac)
}

fun buildChangeTriageCommand(triageCategory: TriageCategory) = changeTriageCommand {
    evac = triageCategory.toProto()
}

fun buildChangeTriageCommand(triageCategory: String?) = changeTriageCommand {
    evac = TriageCategory.fromString(triageCategory)
}

fun changeTriageCommandHandler(info: Info) = handler<ChangeTriageCommand>{
    info.triage = TriageCategory.fromProto(evac)
}

fun changeTriageEventHandler() = eventHandler<ChangeTriageCommand>(KnownEventTypes.INFO.dataString){
    TriageCategory.toEvent(evac)
}

fun buildChangeEvacTypeCommand(evacType: EvacType) = changeEvacTypeCommand {
    evac = evacType.toProto()
}
fun buildChangeEvacTypeCommand(evacCategory: String?) = changeEvacTypeCommand {
    evac = EvacType.fromString(evacCategory)
}
fun changeEvacTypeCommandHandler(info: Info) = handler<ChangeEvacTypeCommand>{ info.evacType = EvacType.fromProto(evac) }
fun changeEvacTypeEventHandler() = eventHandler<ChangeEvacTypeCommand>(KnownEventTypes.INFO.dataString) { EvacType.toEvent(evac) }

fun buildChangeTattooCommand(tattoo: String) = changeTattooCommand {
    this.tattoo = tattoo
}
fun changeTattooCommandHandler(info: Info) = handler<ChangeTattooCommand> { info.tattoo = tattoo }
fun changeTattooEventHandler() = eventHandler<ChangeTattooCommand>(KnownEventTypes.INFO.dataString) {
    changeOrClearEvent(tattoo.isNotEmpty(), tattoo, "tattoo")
}

fun buildChangeGenderCommand(gender: Gender) = changeGenderCommand{
    this.gender = gender.toProto()
}
fun buildChangeGenderCommand(gender: String?) = changeGenderCommand{
    this.gender = Gender.fromString(gender)
}
fun changeGenderCommandHandler(info: Info) = handler<ChangeGenderCommand> { info.gender = Gender.fromProto(gender) }
fun changeGenderEventHandler() = eventHandler<ChangeGenderCommand>(KnownEventTypes.INFO.dataString) { Gender.toEvent(gender) }

fun buildChangeDOBCommand(dob: LocalDate?, estimate: Boolean = false) = changeDobCommand{
    dob?.let { this.dob = int64Value(dob.toEpochDay()) }
    this.estimate = estimate
}
fun changeDOBCommandHandler(info: Info) = handler<ChangeDobCommand> {
    info.dob = if(hasDob() && dob.value != LocalDate.MIN.toEpochDay()) LocalDate.ofEpochDay(dob.value) else null
    info.dateOfBirth = DateOfBirth(
        dob = if(hasDob() && dob.value != LocalDate.MIN.toEpochDay()) LocalDate.ofEpochDay(dob.value) else null,
        estimate = estimate
    )
}
fun changeDobEventHandler() = eventHandler<ChangeDobCommand>(KnownEventTypes.INFO.dataString) {
    changeOrClearEvent(
        hasDob() && dob.value != LocalDate.MIN.toEpochDay(),
        DateTimeFormatter.ofPattern("yyyy-MM-dd", Locale.US).format(LocalDate.ofEpochDay(dob.value)) +
        if (estimate) " (Estimated)" else "",
        "DOB"
    )
}

//region Allergy Helpers

fun buildAddRemoveAllergyListCommand(addedAllergies: List<String> = listOf(), removedAllergies: List<String> = listOf()) = addRemoveAllergyCommand {
    val newAllergyList = listOf(
        CommonAllergies.OPIATES
    )
    this.addAllergy.addAll(addedAllergies.map { CommonAllergies.fromString(it, newAllergyList) })
    this.removeAllergy.addAll(removedAllergies.map { CommonAllergies.fromString(it, newAllergyList) })
}

fun addRemoveAllergyCommandHandler(info: Info) = handler<AddRemoveAllergyCommand>{
    val currentAllergies = info.allergies
    val addedAllergyList = addAllergyList.mapNotNull{ CommonAllergies.fromProto(it) }
    val removedAllergyList = removeAllergyList.mapNotNull{ CommonAllergies.fromProto(it) }

    info.allergies = currentAllergies.toMutableList().apply {
        if(addedAllergyList.contains(CommonAllergies.NKA.dataString)){
            clear()
        }else{
            removeAll(removedAllergyList.map { it })
        }
        addAll(addedAllergyList.map { it })
    }.distinct()
}
fun addRemoveAllergyEventHandler() = eventHandler<AddRemoveAllergyCommand>(KnownEventTypes.INFO.dataString){
    val addedAllergies = addAllergyList.mapNotNull{ CommonAllergies.fromProto(it) }.distinct().joinToString(", ")
    val removedAllergies = removeAllergyList.mapNotNull{ CommonAllergies.fromProto(it) }.distinct().joinToString(", ")
    val addString = if(addedAllergies.isNotEmpty()) "Added allergies: $addedAllergies" else ""
    val removeString = if(removedAllergies.isNotEmpty()) "Removed allergies: $removedAllergies" else ""
    when{
        addString == "Added allergies: ${CommonAllergies.NKA.dataString}" -> "Patient has no known allergies"
        addString.isNotEmpty() && removeString.isNotEmpty() -> addString + "\n" + removeString
        addString.isNotEmpty() -> addString
        removeString.isNotEmpty() -> removeString
        else -> null
    }
}
//endregion

fun buildAddRemoveMedicationCommand(addedMedications: List<String>, removedMedications: List<String>) = addRemoveMedicationsCommand {
    this.addMedicine.addAll(addedMedications.map { compatibleEnum { string = it } })
    this.removeMedicine.addAll(removedMedications.map { compatibleEnum { string = it } })
}
fun addRemoveMedicationCommandHandler(info: Info) = handler<AddRemoveMedicationsCommand>{
    info.medications += this.addMedicineList.mapNotNull { it.string.takeUnless { it.isNullOrEmpty() } }
    info.medications -= this.removeMedicineList.mapNotNull { it.string.takeUnless { it.isNullOrEmpty() } }
}
fun addRemoveMedicationEventHandler() = eventHandler<AddRemoveMedicationsCommand>(KnownEventTypes.INFO.dataString){
    val addedMedications = addMedicineList.mapNotNull{ it.string.takeUnless { it.isNullOrEmpty() } }.joinToString(", ")
    val removedMedications = removeMedicineList.mapNotNull{ it.string.takeUnless { it.isNullOrEmpty() } }.joinToString(", ")
    val addString = if(addedMedications.isNotEmpty()) "Added Medications: $addedMedications" else ""
    val removeString = if(removedMedications.isNotEmpty()) "Removed Medications: $removedMedications" else ""
    when{
        addString.isNotEmpty() && removeString.isNotEmpty() -> addString + "\n" + removeString
        addString.isNotEmpty() -> addString
        removeString.isNotEmpty() -> removeString
        else -> null
    }
}

@Deprecated("Use buildChangePatcatCommand instead.", ReplaceWith("buildChangePatcatCommand"))
fun buildChangeServiceCommand(service: Service) = changeServiceCommand {
    this.service = service.toProto()
}
@Deprecated("Use buildChangePatcatCommand instead.", ReplaceWith("buildChangePatcatCommand"))
fun buildChangeServiceCommand(service: String?) = changeServiceCommand {
    this.service = Service.fromString(service)
}
@Deprecated("Use changePatcatCommandHandler instead.", ReplaceWith("changePatcatCommandHandler"))
fun changeServiceCommandHandler(info: Info) = handler<ChangeServiceCommand>{ info.service = Service.fromProto(service) }
@Deprecated("Use changePatcatEventHandler instead.", ReplaceWith("changePatcatEventHandler"))
fun changeServiceEventHandler() = eventHandler<ChangeServiceCommand>(KnownEventTypes.INFO.dataString) {
    Service.toEvent(service)
}

fun buildChangePatcatCommand(patcat: Patcat?) = changePatcatCommand {
    patcat?.serviceCode?.let { this.service = PatcatService.fromString(it) }
    patcat?.statusCode?.let { this.status = PatcatStatus.fromString(it) }
}
fun changePatcatCommandHandler(info: Info) = handler<ChangePatcatCommand>{
    info.patcat = Patcat.fromProto(service, status).takeUnless { it.isEmpty() }
}
fun changePatcatEventHandler() = eventHandler<ChangePatcatCommand>(KnownEventTypes.INFO.dataString) {
    Patcat.fromProto(service, status).toEvent()
}

fun buildChangeUnitCommand(unit: String) = changeUnitCommand{
    if(unit.isNotEmpty()) this.unit = unit
}
fun changeUnitCommandHandler(info: Info) = handler<ChangeUnitCommand>{ info.unit = unit }
fun changeUnitEventHandler() = eventHandler<ChangeUnitCommand>(KnownEventTypes.INFO.dataString) {
    changeOrClearEvent(unit.isNotEmpty(), unit, "unit")
}

fun buildChangeUnitPhoneNumberCommand(unitPhoneNumber: String) = changeUnitPhoneNumberCommand{
    if(unitPhoneNumber.isNotEmpty()) this.unitPhoneNumber = unitPhoneNumber
}
fun changeUnitPhoneNumberCommandHandler(info: Info) = handler<ChangeUnitPhoneNumberCommand>{ info.unitPhoneNumber = unitPhoneNumber }
fun changeUnitPhoneNumberEventHandler() = eventHandler<ChangeUnitPhoneNumberCommand>(KnownEventTypes.INFO.dataString) {
    changeOrClearEvent(unitPhoneNumber.isNotEmpty(), unitPhoneNumber, "unit phone number")
}

@Deprecated(
    "Use buildChangeGradeCommand instead",
    ReplaceWith("buildChangeGradeCommand")
)

fun buildChangeRankCommand(rank: String) = changeRankCommand{
    if(rank.isNotEmpty()) this.rank = rank
}
@Deprecated(
    "Use changeGradeCommandHandler",
    ReplaceWith("changeGradeCommandHandler")
)
fun changeRankCommandHandler(info: Info) = handler<ChangeRankCommand> { info.rank = rank }
@Deprecated(
    "Use changeGradeEventHandler",
    ReplaceWith("changeGradeEventHandler")
)
fun changeRankEventHandler() = eventHandler<ChangeRankCommand>(KnownEventTypes.INFO.dataString) {
    changeOrClearEvent(rank.isNotEmpty(), rank, "rank")
}
fun buildChangeGradeCommand(grade: Grade) = changeGradeCommand {
    this.grade = grade.toProto()
}
fun buildChangeGradeCommand(grade: String?) = changeGradeCommand {
    this.grade = Grade.fromString(grade)
}
fun changeGradeCommandHandler(info: Info) = handler<ChangeGradeCommand> {
    info.grade = Grade.fromProto(grade)
}
fun changeGradeEventHandler() = eventHandler<ChangeGradeCommand>(KnownEventTypes.INFO.dataString) {
    Grade.toEvent(grade)
}
fun buildChangeBloodTypeCommand(bloodType: BloodType, lowTiter: Boolean? = null) = changeBloodTypeCommand{
    this.bloodType = bloodType.toProto()
    this.lowTiter = nullableBoolValue(lowTiter)
}
fun buildChangeBloodTypeCommand(bloodType: String?, lowTiter: Boolean? = null) = changeBloodTypeCommand{
    this.bloodType = BloodType.fromString(bloodType)
    this.lowTiter = nullableBoolValue(lowTiter)
}
fun changeBloodTypeCommandHandler(info: Info) = handler<ChangeBloodTypeCommand> {
    info.bloodType = BloodType.fromProto(bloodType)
    info.lowTiter = lowTiter.toPrimitive()
}
fun changeBloodTypeEventHandler() = eventHandler<ChangeBloodTypeCommand>(KnownEventTypes.INFO.dataString) { BloodType.toEvent(bloodType, lowTiter.toPrimitive()) }

fun buildChangeDodIdCommand(dodId: String?) = changeDodIdCommand{
    if(!dodId.isNullOrEmpty()) this.dodId = dodId
}
fun changeDodIdCommandHandler(info: Info) = handler<ChangeDodIdCommand> { info.dodId = dodId }
fun changeDodIdEventHandler() = eventHandler<ChangeDodIdCommand>(KnownEventTypes.INFO.dataString) {
    changeOrClearEvent(dodId.isNotEmpty(), dodId, "DoD ID")
}

fun buildChangeWeightCommand(weight: Float?) = changeWeightCommand {
    weight?.takeIf { it >= 0 }?.let { this.weight = floatValue(it) }
}
fun changeWeightCommandHandler(info: Info) = handler<ChangeWeightCommand> {
    info.weight = if(hasWeight() && weight.value != -1f) weight.value else null
}
fun changeWeightEventHandler() = eventHandler<ChangeWeightCommand>(KnownEventTypes.INFO.dataString) {
    changeOrClearEvent(hasWeight() && weight.value != -1f, weightToString(weight.value)!!, "weight")
}

fun buildChangeHeightCommand(height: Float?) = changeHeightCommand {
    height?.takeIf { it >= 0 }?.let { this.height = floatValue(it) }
}
fun changeHeightCommandHandler(info: Info) = handler<ChangeHeightCommand> {
    info.height = if(hasHeight() && height.value != -1f) height.value else null
}
fun changeHeightEventHandler() = eventHandler<ChangeHeightCommand>(KnownEventTypes.INFO.dataString) {
    changeOrClearEvent(hasHeight() && height.value != -1f, heightToString(height.value)!!, "height")
}

fun buildChangeInjuryTimeCommand(injuryTime: Instant?) = changeInjuryTimeCommand {
    injuryTime?.let { this.datetime = int64Value(it.epochSecond) }
}
fun changeTimeInfoCommand(info: Info) = handler<ChangeInjuryTimeCommand> {
    info.timeInfo = if(hasDatetime() && datetime.value != -1L) Instant.ofEpochSecond(datetime.value) else null
}
fun changeInjuryTimeEventHandler() = eventHandler<ChangeInjuryTimeCommand>(KnownEventTypes.INFO.dataString) {
    changeOrClearEvent(
        hasDatetime() && datetime.value != -1L,
        Instant.ofEpochSecond(datetime.value).format(Patterns.mdyhm_24_dash_comma_colon),
        "injury time"
    )
}

fun buildChangeOpenCloseEncounterCommand(open: Boolean) = changeOpenCloseEncounterCommand{ this.open = open }
fun changeOpenCloseEncounterEventHandler() = eventHandler<ChangeOpenCloseEncounterCommand>(KnownEventTypes.INFO.dataString){
    "Patient encounter " + if(open) "opened" else "closed"
}

fun buildPatientMaskingCommand(masked: Boolean, justification: String = "") = changePatientMasking {
    this.enable = masked
    this.justification = justification
}
fun changePatientMaskingHandler(info: Info) = handler<ChangePatientMasking>{
    info.maskingJustification = justification.takeIf { enable }
}
fun changePatientMaskingEventHandler() = eventHandler<ChangePatientMasking>(KnownEventTypes.INFO.dataString) {
    "Masking ${if(enable) "enabled" else "disabled"}. Justification: $justification"
}

fun buildChangeCiteNumberCommand(citeNumber: String) = changeCiteNumberCommand { this.citeNumber = citeNumber }
fun changeCiteNumberCommandHandler(info: Info) = handler<ChangeCiteNumberCommand> { info.citeNumber = citeNumber }
fun changeCiteNumberEventHandler() = eventHandler<ChangeCiteNumberCommand>(KnownEventTypes.INFO.dataString) {
    changeOrClearEvent(citeNumber.isNotEmpty(), citeNumber, "cite number")
}

fun buildChangePatientIdCommand(patientId: DomainId) = changePatientIdCommand {
    this.patientId = patientId.toByteString()
}
fun changePatientIdCommandHandler(info: Info) = handler<ChangePatientIdCommand>{ info.patientId = patientId.toDomainId() }


fun buildChangeDispositionCommand(disposition: String) = changeDisposition {
    this.disposition = Disposition.fromString(disposition)
}
fun changeDispositionCommandHandler(info: Info) = handler<ChangeDisposition>{
    info.disposition = Disposition.fromProto(disposition)
}
fun changeDispositionEventHandler() = eventHandler<ChangeDisposition>(KnownEventTypes.INFO.dataString){
    val disposition = Disposition.fromProto(disposition) ?: ""
    changeOrClearEvent(disposition.isNotEmpty(), disposition, "disposition")
}

fun buildChangeNationalityCommand(nationality: String?) = changeNationalityCommand {
    this.nationality = CountryData.fromString(nationality)
}

fun changeNationalityCommandHandler(info: Info) = handler<ChangeNationalityCommand> {
    val countryString = this.nationalityOrNull?.let { CountryData.fromProto(it) }
    info.nationality = countryString?.let { CountryData.values().single() { item -> item.dataString == it  } }
}

fun changeNationalityCommandEventHandler() = eventHandler<ChangeNationalityCommand>(KnownEventTypes.INFO.dataString){
    val nationalityStr = CountryData.fromProto(nationality) ?: ""
    changeOrClearEvent(nationalityStr.isNotEmpty(), nationalityStr, "nationality")
}

//Immunizations

fun buildUpdateImmunizationCommand(addedImmunizations: List<Immunization>, removedImmunizations: List<Immunization>) = updateImmunizationCommand{
    this.addedImmunizations.addAll(addedImmunizations.map{ it.toProto() })
    this.removedImmunizations.addAll(removedImmunizations.map{ it.toProto() })
}

fun updateImmunizationCommandHandler(info: Info) = handler<UpdateImmunizationCommand>{
    info.immunizations += addedImmunizationsList.map { Immunization.fromProto(it) }
    info.immunizations -= removedImmunizationsList.map { Immunization.fromProto(it) }
}

fun updateImmunizationCommandEventHandler() = eventHandler<UpdateImmunizationCommand>(KnownEventTypes.INFO.dataString){
    val addedImmunizations = addedImmunizationsList.map {
        "${it.name} ${it.volume}${it.unit}"
    }.joinToString(",")
    val removedImmunizations = removedImmunizationsList.map {
        "${it.name} ${it.volume}${it.unit}"
    }.joinToString(",")
    val addedString = if(addedImmunizations.isNotEmpty()) "Added immunizations: " + addedImmunizations else ""
    val removedString = if(removedImmunizations.isNotEmpty()) "Removed immunizations: " + removedImmunizations else ""
    when{
        addedString.isNotEmpty() && removedString.isNotEmpty() -> addedString + "\n" + removedString
        addedString.isNotEmpty() -> addedString
        removedString.isNotEmpty() -> removedString
        else -> null
    }

}

//Procedures
fun buildUpdateProceduresCommand(addedProcedures: List<Procedure>, removedProcedures: List<Procedure>) = updateProceduresCommand{
    this.addProcedure.addAll(addedProcedures.map { it.toProto() })
    this.removeProcedure.addAll(removedProcedures.map { it.toProto() })
}

fun updateProceduresCommandHandler(info: Info) = handler<UpdateProceduresCommand>{
    info.procedures += addProcedureList.map { Procedure.fromProto(it) }
    info.procedures -= removeProcedureList.map { Procedure.fromProto(it) }
}

fun updateProcedureCommandEventHandler() = eventHandler<UpdateProceduresCommand>{
    val addedProcedures = addProcedureList.map {
        it.name
    }.joinToString(",")

    val removedProcedures = removeProcedureList.map {
        it.name
    }.joinToString(",")
    val addedString = if(addedProcedures.isNotEmpty()) "Added procedures: " + addedProcedures else ""
    val removedString = if(removedProcedures.isNotEmpty()) "Removed procedures: " + removedProcedures else ""
    when{
        addedString.isNotEmpty() && removedString.isNotEmpty() -> addedString + "\n" + removedString
        addedString.isNotEmpty() -> addedString
        removedString.isNotEmpty() -> removedString
        else -> null
    }
}

fun buildUpdateProblemsCommand(problemsToAdd: List<Problem>?, problemsIdsToRemove: List<ProblemId>?) = updateProblemsCommand {
    problemsToAdd?.let {
        addedProblems.addAll(problemsToAdd.map { it.toProto() })
    }
    problemsIdsToRemove?.let { removedProblemIds.addAll(problemsIdsToRemove.map { it.toByteString() }) }
}

fun updateProblemsCommandHandler(info: Info) = handler<UpdateProblemsCommand>{
    info.problems += addedProblemsList.map { Problem.fromProto(it) }
    info.problems -= removedProblemIdsList.mapNotNull { idToRemove ->
        info.problems.firstOrNull{it.id.toByteString() == idToRemove}
    }
}

fun updateProblemsCommandEventHandler(info: Info) =
    eventHandler<UpdateProblemsCommand>(KnownEventTypes.INFO.dataString) {
        var addedEventString: String? = null
        if (addedProblemsList.isNotEmpty()) {
            val listOfProblemsAdded = addedProblemsList.map { it.name }
            addedEventString = "Added Problem${if(listOfProblemsAdded.size > 1) "s" else ""}: ${listOfProblemsAdded.joinToString(", ")}"
        }
        var removedEventString: String? = null
        if (removedProblemIdsList.isNotEmpty()) {
            val listOfProblemsRemoved = removedProblemIdsList.mapNotNull { idToRemove ->
                info.problems.find { it.id.toByteString() == idToRemove }
            }.map{ it.name }
            if (listOfProblemsRemoved.isNotEmpty()) {
                removedEventString = "Removed Problem${if(listOfProblemsRemoved.size > 1) "s" else "" }: ${listOfProblemsRemoved.joinToString(", ")}"
            }
        }
        listOfNotNull(addedEventString, removedEventString).joinToString("\n").takeIf { it.isNotEmpty() }
     }