package gov.afrl.batdok.encounter.commands

import gov.afrl.batdok.commands.handler
import gov.afrl.batdok.commands.proto.HL7Commands.HL7DataCommand
import gov.afrl.batdok.commands.proto.hL7DataCommand
import gov.afrl.batdok.encounter.HistoricalHL7Data

fun buildHL7DataCommand(hl7Message: String) = hL7DataCommand {
    messageData = hl7Message
}

fun hl7DataCommandHandler(hL7Data: HistoricalHL7Data) = handler<HL7DataCommand> {
    hL7Data.hl7Messages = messageData
}

// No need for event command here. This command is mainly for internal data
//  and the HL7 data this command handles will never be displayed to users.
