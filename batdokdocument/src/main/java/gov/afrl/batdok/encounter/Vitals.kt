package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.commands.CommandHandler
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.commands.*
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.encounter.ids.EncounterVitalId
import gov.afrl.batdok.util.ILinkableList
import gov.afrl.batdok.util.SortedLinkableList
import gov.afrl.batdok.util.Timestamped
import mil.af.afrl.batman.batdokid.DomainId
import java.io.Serializable
import java.time.Instant
import kotlin.reflect.KClass
import kotlin.reflect.safeCast

data class EncounterVital(
    val vitalId: EncounterVitalId,
    override var timestamp: Instant,
    val vitalMap: Map<KClass<out IndividualVital>, IndividualVital> = mapOf(),
    val documentId: DocumentId = DomainId.nil()
): Serializable, Linkable, Timestamped{
    override val id: DomainId = vitalId

    operator fun plus(vital: IndividualVital) = copy(
        vitalMap = vitalMap + (vital::class to vital)
    )
    operator fun plus(vital: List<IndividualVital>) = copy(
        vitalMap = vitalMap + vital.map { it::class to it }
    )
    fun update(vital: EncounterVital) = copy(
        vitalMap = this.vitalMap + vital.vitalMap //Take the new items, then add the original items that didn't update

    )
    fun withoutEmpties() = copy(
        vitalMap = this.vitalMap.filter { !it.value.isEmpty }
    )
    operator fun <T: IndividualVital> get(klass: KClass<out T>) = klass.safeCast(vitalMap[klass])
    inline fun <reified T: IndividualVital> get() = get(T::class)

    fun toEventString(includeClearMessage: Boolean = false) = vitalMap.values
        .sortedBy { it.name }
        .mapNotNull { it.toEventItem(includeClearMessage) }
        .joinToString("; ")
}

abstract class IndividualVital(val name: String): Serializable{
    abstract val isEmpty: Boolean
    abstract fun toProtobuf(): Message
    abstract fun produceEmptyVital(): IndividualVital

    abstract val eventMessage: String
    fun toEventItem(includeClearMessage: Boolean = false) = when{
        includeClearMessage && isEmpty -> "Cleared $name"
        !isEmpty -> "$name: $eventMessage"
        else -> null
    }
}

class Vitals: ILinkableList<EncounterVital> by SortedLinkableList(), Serializable {
    @Deprecated("Just use Vitals.list")
    val vitalList: List<EncounterVital>
        get() = list

    @Transient internal val handlers = CommandHandler{
        val vitals = this@Vitals
        +logVitalsCommandHandler(vitals)
        +updateVitalsCommandHandler(vitals)
        +removeVitalCommandHandler(vitals)
    }

    companion object{
        /**
         * Use this function if you want to have a single event for each vital
         */
        fun EventCommandHandler.includeVitalEvents(vitals: Vitals) {
            +logVitalsEventCommandHandler()
            +updateVitalEventCommandHandler(vitals)
            +removeVitalEventCommandHandler(vitals)
        }
    }
}