package gov.afrl.batdok.encounter.metadata

import gov.afrl.batdok.commands.proto.SharedProtobufObjects.CompatibleEnum
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

enum class MajorEventLocations(override val protoIndex: Int, override val dataString: String): ProtoEnum {
    PREFLIGHT_FACILITY(1, "Preflight facility"),
    POSTFLIGHT_FACILITY(2, "Postflight facility"),
    ENPLANING(3, "Enplaning"),
    DEPLANING(4, "Deplaning"),
    ASCENT(5, "Ascent"),
    DESCENT(6, "Descent"),
    AMBUS_FROM(7, "Ambus from"),
    AMBUS_TO(8, "Ambus to"),
    ALTITUDE(9, "Altitude"),
    ;

    companion object{
        fun fromProto(enum: CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)
    }
}