package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.proto.NoteCommands
import gov.afrl.batdok.commands.proto.note
import gov.afrl.batdok.encounter.commands.buildLinkFromLinkables
import gov.afrl.batdok.encounter.ids.NoteId
import gov.afrl.batdok.encounter.ids.toByteString
import gov.afrl.batdok.encounter.ids.toDomainId
import gov.afrl.batdok.encounter.orders.IOrderLine
import gov.afrl.batdok.util.Timestamped
import mil.af.afrl.batman.batdokid.DomainId
import java.io.Serializable
import java.time.Instant
import java.time.temporal.ChronoUnit

/**
 * A note which can be attached to a domain object to describe it.
 * @param id: Id of note
 * @param message The note text.
 * @param timeStamp The date/time at which the note was entered.
 * @param callsign The callsign of the user who entered the note.
 */
data class Note(
    override val id: NoteId = DomainId.create(),
    val message: String,
    @Deprecated("Use timestamp (No capital S)")
    val timeStamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS),
    val callsign: String
) : Linkable, Timestamped {
    override val timestamp = timeStamp
    constructor(noteProto: NoteCommands.Note, callsign: String) :
        this(
            id = noteProto.noteId.toDomainId(),
            message = noteProto.message,
            timeStamp = Instant.ofEpochSecond(noteProto.timestamp),
            callsign = callsign
        )
    fun toProto() = note {
        noteId = <EMAIL>()
        message = <EMAIL>
        timestamp = <EMAIL>
    }

    fun createLinkCommand(orderLine: IOrderLine) = buildLinkFromLinkables(CommonLinkRelationships.FULFILL_ORDERLINE, this, orderLine)
}