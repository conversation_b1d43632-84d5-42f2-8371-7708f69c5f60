package gov.afrl.batdok.encounter.commands.orders

import gov.afrl.batdok.commands.eventHandler
import gov.afrl.batdok.commands.handlerWithId
import gov.afrl.batdok.commands.proto.*
import gov.afrl.batdok.encounter.Interval
import gov.afrl.batdok.encounter.KnownEventTypes
import gov.afrl.batdok.encounter.ids.OrderLineId
import gov.afrl.batdok.encounter.ids.toByteString
import gov.afrl.batdok.encounter.metadata.Signature
import gov.afrl.batdok.encounter.orders.*
import gov.afrl.batdok.util.Patterns
import gov.afrl.batdok.util.format
import gov.afrl.batdok.util.nullableInt64Value
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant


fun buildAddOrderLineCommand(orderLine: OrderLine) = addOrderLine {
    this.orderLineId = orderLine.id.toByteString()
    this.orderStatus = OrderStatus.fromString(orderLine.orderStatus)
    this.frequency = orderLine.frequency.toString()
    this.instructions = orderLine.instructions
    this.title = orderLine.title
    this.lastOccurrence = nullableInt64Value(orderLine.lastOccurrence?.epochSecond)
    if (orderLine.provider != null) {
        this.provider = orderLine.provider.toProto()
    }
    if (orderLine.signature != null) {
        this.signature = orderLine.signature.toProto()
    }
    this.timestamp = orderLine.timestamp.epochSecond
    this.type = OrderType.fromString(orderLine.orderType)
}

fun addOrderLineCommandHandler(orders: Orders) =
    handlerWithId<OrderCommands.AddOrderLine> { _, _ ->
        orders += this.toOrderLine()
    }

fun addOrderLineCommandEventHandler() =
    eventHandler<OrderCommands.AddOrderLine>(KnownEventTypes.ORDERS.dataString) { _ ->
        val orderLine = this.toOrderLine()
        val description = listOfNotNull(
            "Title: ${orderLine.title}",
            "Instructions: ${orderLine.instructions}",
            "Frequency: ${orderLine.frequency}",
            orderLine.lastOccurrence?.let { "Last Administration: ${it.format(Patterns.mdhm_24_space_comma_colon)}" },
            orderLine.provider?.name?.let { "Provider: $it" }
        ).joinToString("\n")
        "Added Order:\n$description"
    }

private fun OrderCommands.AddOrderLine.toOrderLine() = OrderLine(
    id = DomainId.create(orderLineId.toByteArray()),
    orderStatus = OrderStatus.fromProto(orderStatus),
    frequency = Interval(frequency),
    instructions = instructions,
    title = title,
    lastOccurrence = lastOccurrence.valueOrNull?.let { Instant.ofEpochSecond(it.value) },
    provider = providerOrNull?.let { gov.afrl.batdok.encounter.Contact(it) },
    signature = signatureOrNull?.let { Signature(it) },
    timestamp = Instant.ofEpochSecond(timestamp),
    orderType = OrderType.fromProto(type)
)

fun buildUpdateOrderLineCommand(
    id: OrderLineId,
    timestamp: Instant,
    title: String,
    instructions: String,
    frequency: Interval,
    lastOccurrence: Instant? = null,
    provider: gov.afrl.batdok.encounter.Contact? = null,
    signature: Signature? = null
) = updateOrderLine {
    this.orderLineId = id.toByteString()
    this.frequency = frequency.toString()
    this.instructions = instructions
    this.title = title
    this.lastOccurrence = nullableInt64Value(lastOccurrence?.epochSecond)
    if (provider != null) {
        this.provider = provider.toProto()
    }
    if (signature != null) {
        this.signature = signature.toProto()
    }
    this.timestamp = timestamp.epochSecond
}

@Deprecated("To remove once Document.orders is removed")
fun updateOrderLineCommandHandler(orders: Orders) =
    handlerWithId<OrderCommands.UpdateOrderLine> { _, _ ->
        val id = DomainId.create<OrderLineId>(orderLineId.toByteArray())
        val oldOrderLine = orders[id]
        if (oldOrderLine != null) {
            orders += OrderLine(
                id = id,
                orderStatus = oldOrderLine.orderStatus,
                frequency = Interval(frequency),
                instructions = instructions,
                title = title,
                lastOccurrence = lastOccurrence.valueOrNull?.let { Instant.ofEpochSecond(it.value) },
                provider = providerOrNull?.let { gov.afrl.batdok.encounter.Contact(it) },
                signature = signatureOrNull?.let { Signature(it) },
                timestamp = Instant.ofEpochSecond(timestamp),
                orderType = oldOrderLine.orderType
            )
        }
    }

fun updateOrderLineCommandEventHandler(orders: Orders) =
    eventHandler<OrderCommands.UpdateOrderLine>(KnownEventTypes.ORDERS.dataString)
    { _ ->
        val id = DomainId.create<OrderLineId>(orderLineId.toByteArray())
        val orderLine = orders[id]
        if (orderLine != null) {
            val lastOccurrence = this.lastOccurrence.valueOrNull?.let {
                Instant.ofEpochSecond(it.value).format(Patterns.mdhm_24_space_comma_colon)
            }
            val description = listOfNotNull(
                "Title: ${this.title}",
                "Instructions: ${this.instructions}",
                "Frequency: ${this.frequency}",
                lastOccurrence?.let { "Last Administration: $it" },
                this.provider?.name?.let { if (it.value.isBlank()) null else "Provider: ${it.value}" }
            ).joinToString("\n")
            "Updated Order:\n$description"
        } else {
            null
        }
    }

fun buildUpdateOrderLineStatusCommand(
    id: OrderLineId,
    orderStatus: OrderStatus
) = updateOrderLineStatus {
    this.orderLineId = id.toByteString()
    this.orderStatus = orderStatus.toProto()
}

fun updateOrderLineStatusCommandHandler(orders: Orders) =
    handlerWithId<OrderCommands.UpdateOrderLineStatus> { _, _ ->
        val id = DomainId.create<OrderLineId>(orderLineId.toByteArray())
        val oldOrderLine = orders[id]
        if (oldOrderLine != null) {
            orders += OrderLine(
                id = id,
                orderStatus = OrderStatus.fromProto(orderStatus),
                frequency = oldOrderLine.frequency,
                instructions = oldOrderLine.instructions,
                title = oldOrderLine.title,
                lastOccurrence = oldOrderLine.lastOccurrence,
                provider = oldOrderLine.provider,
                signature = oldOrderLine.signature,
                timestamp = oldOrderLine.timestamp,
                orderType = oldOrderLine.orderType
            )
        }
    }

fun updateOrderLineStatusCommandEventHandler(orders: Orders) =
    eventHandler<OrderCommands.UpdateOrderLineStatus>(KnownEventTypes.ORDERS.dataString)
    { _ ->
        val id = DomainId.create<OrderLineId>(orderLineId.toByteArray())
        val orderLine = orders[id]
        if (orderLine != null) {
            "Updated Order status from:\n${orderLine.orderStatus} to ${
                OrderStatus.fromProto(
                    this.orderStatus
                )
            }"
        } else {
            null
        }
    }