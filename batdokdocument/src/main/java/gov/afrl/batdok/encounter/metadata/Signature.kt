package gov.afrl.batdok.encounter.metadata

import com.google.protobuf.bytesValue
import com.google.protobuf.kotlin.toByteString
import gov.afrl.batdok.commands.proto.Signatures
import gov.afrl.batdok.commands.proto.nameOrNull
import gov.afrl.batdok.commands.proto.signature
import gov.afrl.batdok.commands.proto.signatureImageOrNull
import gov.afrl.batdok.commands.proto.timestampOrNull
import gov.afrl.batdok.util.int64Value
import gov.afrl.batdok.util.stringValue
import java.io.Serializable
import java.time.Instant

data class Signature(
    val name: String? = null,
    val timeStamp: Instant? = null,
    val signature: ByteArray? = null
): Serializable {
    constructor(proto: Signatures.Signature): this(
        proto.nameOrNull?.value,
        proto.timestampOrNull?.value?.let { Instant.ofEpochSecond(it) },
        proto.signatureImageOrNull?.value?.toByteArray()
    )

    fun toProto() = signature {
        <EMAIL>?.let { name = stringValue(it) }
        <EMAIL>?.let { timestamp = int64Value(it.epochSecond) }
        <EMAIL>?.let { signatureImage = bytesValue { value = it.toByteString()  }}
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as Signature

        if (name != other.name) return false
        if (timeStamp != other.timeStamp) return false
        if (signature != null) {
            if (other.signature == null) return false
            if (!signature.contentEquals(other.signature)) return false
        } else if (other.signature != null) return false

        return true
    }

    override fun hashCode(): Int {
        var result = name?.hashCode() ?: 0
        result = 31 * result + (timeStamp?.hashCode() ?: 0)
        result = 31 * result + (signature?.contentHashCode() ?: 0)
        return result
    }
}