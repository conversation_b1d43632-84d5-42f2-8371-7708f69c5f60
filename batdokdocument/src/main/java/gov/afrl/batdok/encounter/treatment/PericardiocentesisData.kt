package gov.afrl.batdok.encounter.treatment

import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.TreatmentCommands
import gov.afrl.batdok.commands.proto.pericardiocentesis
import gov.afrl.batdok.encounter.treatment.FoleyCatheterData.Color
import gov.afrl.batdok.util.compatibleEnum
import gov.afrl.batdok.util.nullableFloatValue
import gov.afrl.batdok.util.toPrimitive

class PericardiocentesisData(
    val volume: Float? = null,
    val volumeUnit: String? = null
) : TreatmentData {
    override fun toProtobuf(): Message = pericardiocentesis {
        this.volume = nullableFloatValue(<EMAIL>)
        this.volumeUnit = compatibleEnum(<EMAIL>)
    }

    override fun toDetailString(): String {
        return listOfNotNull(
            volume.toDetailString("Volume", volumeUnit),
        ).joinToString(", ")
    }

    companion object{
        fun fromProtobuf(data: TreatmentCommands.Pericardiocentesis): PericardiocentesisData{
            return PericardiocentesisData(
                data.volume.toPrimitive(),
                data.volumeUnit.toPrimitive(),
            )
        }
    }
}