package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.proto.CustomActionCommands
import gov.afrl.batdok.commands.proto.customAction
import gov.afrl.batdok.encounter.commands.buildLinkFromLinkables
import gov.afrl.batdok.encounter.ids.CustomActionId
import gov.afrl.batdok.encounter.ids.toByteString
import gov.afrl.batdok.encounter.ids.toDomainId
import gov.afrl.batdok.encounter.orders.IOrderLine
import gov.afrl.batdok.util.Categorized
import gov.afrl.batdok.util.stringValue
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant
import java.time.temporal.ChronoUnit

data class CustomAction(
    override val id: CustomActionId = DomainId.create(),
    override val timestamp: Instant? = Instant.now().truncatedTo(ChronoUnit.SECONDS),
    val description: String? = null,
    val message: String,
    val callSign: String
) : Administrable, Categorized {

    override val category: String?
        get() = description

    constructor(customActionProto: CustomActionCommands.CustomAction, callsign: String) :
            this(
                id = customActionProto.customActionId.toDomainId(),
                timestamp = Instant.ofEpochSecond(customActionProto.timestamp),
                description = customActionProto.description.value.takeIf
                {
                    customActionProto.hasDescription() && customActionProto.description.value.isNotBlank()
                },
                message = customActionProto.message,
                callSign = callsign
            )

    fun toProto() = customAction {
        customActionId = <EMAIL>()
        <EMAIL>?.let { timestamp = it.epochSecond }
        <EMAIL>?.let { if (it.isNotBlank()) description = stringValue(it) }
        message = <EMAIL>
    }

    override fun createLinkCommand(orderLine: IOrderLine) = buildLinkFromLinkables(CommonLinkRelationships.FULFILL_ORDERLINE, this, orderLine)
}