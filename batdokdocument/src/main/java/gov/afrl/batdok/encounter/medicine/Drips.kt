package gov.afrl.batdok.encounter.medicine

import gov.afrl.batdok.commands.CommandHandler
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.commands.*
import gov.afrl.batdok.encounter.ids.MedicineId
import java.io.Serializable

class Drips (
    drips: List<Drip> = listOf(),
): Serializable {
    var drips = drips
        internal set

    operator fun get(id: MedicineId) = drips.find { it.dripId == id }
    operator fun plusAssign(drip: Drip) {
        drips = drips.filter { drip.dripId != it.dripId } + drip
    }
    operator fun List<Drip>.minus(id: MedicineId) = filter { it.dripId != id }
    operator fun minus(id: MedicineId) = drips - id
    operator fun minusAssign(id: MedicineId){ drips -= id}
    fun findById(id: MedicineId): Drip {
        val drip = drips.filter { it.dripId == id }
        return drip[0]
    }

    @Transient internal val handlers = CommandHandler {
        val drips = this@Drips
        +createDripHandler(drips)
        +removeDripHandler(drips)
        +addToDripHandler(drips)
        +removeFromDripHandler(drips)
        +startDripHandler(drips)
        +stopDripHandler(drips)
    }

    companion object {
        fun EventCommandHandler.includeDripEvents() = apply {
            +createDripEventHandler()
            +removeDripEventHandler()
            +addToDripEventHandler()
            +removeFromDripEventHandler()
            +startDripEventHandler()
            +stopDripEventHandler()
        }
    }
}