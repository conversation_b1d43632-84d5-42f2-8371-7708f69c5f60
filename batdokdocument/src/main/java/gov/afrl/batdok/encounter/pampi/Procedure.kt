package gov.afrl.batdok.encounter.pampi

import gov.afrl.batdok.commands.proto.InfoCommands
import gov.afrl.batdok.commands.proto.procedures
import gov.afrl.batdok.util.nullableInt64Value
import gov.afrl.batdok.util.toInstant
import java.io.Serializable
import java.time.Instant

data class Procedure(var name: String, var date: Instant? = null) : Serializable {
    fun toProto() = procedures {
        val procedure = this@Procedure
        this.name = procedure.name
        this.date = nullableInt64Value(procedure.date)
    }

    companion object {
        fun fromProto(proto: InfoCommands.Procedures): Procedure {
            return Procedure(
                name = proto.name,
                date = proto.date.toInstant()
            )
        }
    }
}