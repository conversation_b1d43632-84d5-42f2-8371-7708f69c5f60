package gov.afrl.batdok.encounter.treatment

import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.TreatmentCommands
import gov.afrl.batdok.commands.proto.hyperthermiaPreventionData
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

class HyperthermiaPreventionData(val type: String? = null): TreatmentData{
    constructor(hyperPrev: TreatmentCommands.HyperthermiaPreventionData): this(Type.fromProto(hyperPrev.type)?:"")
    override fun toProtobuf(): Message = hyperthermiaPreventionData{
        type = Type.fromString(<EMAIL>)
    }

    override fun toDetailString(): String {
        return listOfNotNull(type).joinToString(" - ")
    }

    enum class Type(override val protoIndex: Int, override val dataString: String): ProtoEnum {
        EXTERNAL_COOLING(1, "External Cooling"),
        ;

        companion object{
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }
}