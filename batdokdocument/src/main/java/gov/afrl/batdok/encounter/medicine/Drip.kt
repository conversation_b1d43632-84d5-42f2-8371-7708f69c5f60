package gov.afrl.batdok.encounter.medicine

import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.encounter.ids.MedicineId
import mil.af.afrl.batman.batdokid.DomainId
import java.io.Serializable
import java.time.Instant

data class Drip(
    val base: Medicine,
    val baseTimestamp: Instant?,
    var components: MutableList<Medicine>,
    var startTime: Instant? = null,
    var endTime: Instant? = null,
    val documentId: DocumentId = DomainId.nil()
): Serializable {
    internal constructor(
        protoBase: SharedProtobufObjects.Medicine,
        documentId: DocumentId,
        id: MedicineId,
        baseTimestamp: Instant?,
        startTime: Instant? = null,
        endTime: Instant? = null
    ): this(
        base = Medicine(protoMed = protoBase, administrationTime = baseTimestamp, documentId = documentId, id = id),
        baseTimestamp = baseTimestamp,
        components = MutableList(1){Medicine(protoMed = protoBase, administrationTime = baseTimestamp, documentId = documentId, id = id)},
        startTime = startTime,
        endTime = endTime,
        documentId = documentId
    )
    val dripId
        get() = base.id
    val isStarted
        get() = startTime != null
    val isStopped
        get() = endTime != null
}