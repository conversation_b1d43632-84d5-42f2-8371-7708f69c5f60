package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.CommandHandler
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.encounter.commands.*
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto
import java.io.Serializable
import java.util.Locale
import java.util.Objects

class Injuries: Serializable {
    var mechanismsOfInjury: Map<String?, List<String>> = mapOf()
    var injuries: Map<String?, List<Injury>> = mapOf()
    var drawingPoints: List<DrawingPoint> = listOf(); internal set
    var tbsa: Double? = null; internal set

    internal fun addMoi(moi: String, location: String? = null){
        val currentMoi = mechanismsOfInjury[location] ?: listOf()
        if (moi !in currentMoi) {
            mechanismsOfInjury += (location to (currentMoi + moi))
        }
    }

    internal fun addInjury(injury: Injury, location: String? = null){
        val currentInjuries = injuries[location]?: listOf()
        if (injury !in currentInjuries) {
            injuries += (location to (currentInjuries + injury))
        }
    }

    internal fun removeMoi(moi: String, location: String? = null){
        val currentMoi = mechanismsOfInjury[location]?: listOf()
        mechanismsOfInjury += (location to currentMoi.filterNot { it == moi })

        if(mechanismsOfInjury[location]?.isEmpty() == true) {
            mechanismsOfInjury -= location
        }
    }

    internal fun removeInjury(moi: Injury, location: String? = null){
        val currentMoi = injuries[location]?: listOf()
        injuries += (location to currentMoi.filterNot { it == moi })

        if(injuries[location]?.isEmpty() == true) {
            injuries -= location
        }
    }

    fun hasMoi(vararg moi: String) = mechanismsOfInjury.any { it.value.any { it in moi } }
    fun hasMoi(vararg moi: Moi) = hasMoi(*moi.map{ it.dataString }.toTypedArray())

    fun hasInjury(vararg injury: String) = injuries.any { it.value.any { it.injury in injury } }
    fun hasInjury(vararg injury: Injury.Type) = hasInjury(*injury.map { it.dataString }.toTypedArray())

    fun allMoiString() = mechanismsOfInjury.flatMap { it.value }.distinct().joinToString(", ").ifEmpty { "None" }
    fun allInjuryString() = injuries.flatMap { it.value }.distinct().joinToString(", "){ it.injury ?: "" }.ifEmpty { "None" }

    fun allMoiInjuryString() = "Mois: ${allMoiString()}; Injuries: ${allInjuryString()}"

    fun getAllMoisExcept(vararg mois: String) = mechanismsOfInjury.flatMap { it.value }.filter { it !in mois }
    fun getAllMoisExcept(vararg mois: Moi) = getAllMoisExcept(*mois.map { it.dataString }.toTypedArray())

    fun getAllInuriesExcept(vararg injuryStringList: String) = injuries.flatMap { it.value }.filter { it.injury !in injuryStringList }
    fun getAllInuriesExcept(vararg injuries: Injury.Type) = getAllInuriesExcept(*injuries.map { it.dataString }.toTypedArray())


    @Transient internal val handlers = CommandHandler {
        val injuries = this@Injuries
        +addPointWithLabelCommandHandler(injuries)
        +changeTBSACommandHandler(injuries)
        +removeDrawPointCommandHandler(injuries)
        +undoDrawViewCommandHandler(injuries)
        +clearInjuryDrawingsCommandHandler(injuries)
        +clearDrawViewCommandHandler(injuries)
        +changeMoiCommandHandler(injuries)
        +changeInjuryCommandHandler(injuries)
    }

    companion object{
        fun EventCommandHandler.includeInjuryEvents() = apply {
            +changeTbsaEventHandler()
            +changeMoiEventHandler()
            +changeInjuryEventHandler()
            +addPointWithLabelEventHandler()
            +removeDrawPointEventHandler()
            +undoDrawPointEventHandler()
            +clearInjuryDrawingsEventHandler()
            +clearDrawViewEventHandler()
        }
    }

    operator fun minusAssign(point: DrawingPoint) {
        // we want to keep where at least 1 of the 2 coords are different
        drawingPoints = drawingPoints.filter { it != point }
    }

}

data class Injury(val injury: String?, val abbreviation: String?) {
    enum class Type(override val protoIndex: Int, override val dataString: String, val abbrev: String): ProtoEnum {
        AMPUTATION(1, "Amputation", "AMP"),
        @Deprecated("Deprecated do not use")
        BLEEDING(2, "Bleeding", "BL"),
        BURN(3, "Burn", "BU"),
        CREPITUS(4, "Crepitus", "C"),
        @Deprecated("Deprecated do not use")
        CRUSH(5, "Crush", "CR"),
        @Deprecated("Deprecated do not use")
        DEFORMITY(6, "Deformity", "D"),
        DEGLOVING(7, "Degloving", "DG"),
        ECCHYMOSIS(8, "Ecchymosis", "E"),
        EVISCERATION(9, "Evisceration", "EV"),
        @Deprecated("Deprecated do not use")
        FRACTURE(10, "Fracture", "FX"),
        GSW(11, "Gunshot Wound", "GSW"),
        HEMATOMA(12, "Hematoma", "H"),
        IMPALED_OBJECT(13, "Impaled Object", "IMP"),
        LACERATION(14, "Laceration", "LAC"),
        PAIN(15, "Pain", "P"),
        PEPPERING(16, "Peppering", "PP"),
        @Deprecated("Deprecated do not use")
        PENET(17, "Penetrating", "PEN"),
        PUNCTURE_WOUND(18, "Puncture Wound", "PW"),
        SUBCUTANEOUS_AIR(19, "Subcutaneous Air", "SQA"),
        SUSPECT(20, "TBI Suspect", "TBI"),
        OTHER_MOI(21, "Other", "OTHER"),
        PARTIAL_AMPUTATION(22, "Partial Amputation", "PA"),
        OPEN_FRACTURE(23, "Open Fracture", "OF"),
        @Deprecated("Deprecated do not use")
        CLOSE_FRACTURE(24, "Close Fracture", "CF"),
        CLOSED_FRACTURE(25, "Closed Fracture", "CF"),
        FOREIGN_BODY(26, "Foreign Body", "FB"),
        ;

        companion object{
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = entries.toTypedArray().protoToString(enum)
            fun fromString(string: String?) = entries.toTypedArray().stringToProto(string)
            fun expandAbbreviation(abbr: String) : String? {
                // Return a BATDOK abbreviation if it exists
                entries.find { it.abbrev == abbr }?.dataString?.let { return it }
                // Handle extra CDP abbreviations
                return when (abbr.uppercase()) {
                    // Guarding and rigidity map to ABD (abdominal)
                    "ABR" -> "Abrasion"
                    "ACU" -> "Distention"
                    "APP" -> "Leg shortening"
                    // "Blood at meatus" and "vaginal bleeding" both BLE
                    "BUB" -> "Bubbling"
                    "CON" -> "Stepoff"
                    "DIS" -> "Decreased rectal tone"
                    "EXT" -> "External leg rotation"
                    "FLA" -> "Flail segment"
                    "FOR" -> "Foreign body"
                    "FRA" -> "Orbital fracture"
                    "FRO" -> "Frostbite"
                    "HIG" -> "High riding prostate"
                    // INJ will be electrical, dental, eye, nasal
                    "INS" -> "Instability"
                    "INT" -> "Internal leg rotation"
                    "MAN" -> "Mangled extremity"
                    "OPE" -> "Open fracture"
                    "PAR" -> "Paradoxical movement"
                    "PRE" -> "Pressure injury"
                    "REB" -> "Rebound tenderness"
                    "REC" -> "Rectal bleeding"
                    "SEA" -> "Seatbelt sign"
                    "SWE" -> "Swelling"
                    "TBI" -> "TBI Suspect"
                    "TEN" -> "Tenderness"
                    "TOR" -> "Torticollis"
                    else -> "Unknown injury"
                }
            }
        }
    }
}

data class DrawingPoint(
    val label: String,
    /** the Normalized X pos of the point*/
    val x: Float,
    /** the Normalized Y pos of the point*/
    val y: Float
): Serializable {
    constructor(
        label: String,
        x: Int,
        y: Int,
        canvasWidth: Int,
        canvasHeight: Int
    ): this(label, x/canvasWidth.toFloat(), y/canvasHeight.toFloat())

    val type: Type get() {
        return Type.fromString(label)
    }
    val color: Int get() = when(type){
        Type.TQ -> -16777088 // Dark Blue
        else -> 0xFF000000.toInt()
    }

    enum class Type{
        INJURY, TQ;

        companion object {
            @JvmStatic
            fun fromString(type: String): Type {
                return if (type.uppercase(Locale.US).contains("TQ")) {
                    TQ
                } else {
                    INJURY
                }
            }
        }
    }

    /**
     * The X value scaled to the given [width]
     */
    fun getScaledX(width: Int) = x * width

    /**
     * The Y value scaled to the given [height]
     */
    fun getScaledY(height: Int) = y * height

    override fun equals(other: Any?): Boolean {
        if(other !is DrawingPoint) return false
        return other.x.compareTo(x) == 0 && other.y.compareTo(y) == 0
    }

    override fun hashCode(): Int {
        return Objects.hash(x, y)
    }
}