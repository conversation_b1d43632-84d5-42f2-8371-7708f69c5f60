package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto
import gov.afrl.batdok.util.toChangeOrClearEvent

enum class Grade(override val dataString: String, val shortString: String, override val protoIndex: Int): ProtoEnum {
    A00("OSI, CID, NIS","SA", 1),
    C00("Aviation Cadet, Academy Cadet, ROTC Cadet","Cadet", 2),
    E01("Enlisted Grade E1","E-1", 3),
    E02("Enlisted Grade E2", "E-2",4),
    E03("Enlisted Grade E3","E-3", 5),
    E04("Enlisted Grade E4","E-4", 6),
    E05("Enlisted Grade E5", "E-5",7),
    E06("Enlisted Grade E6","E-6", 8),
    E07("Enlisted Grade E7","E-7", 9),
    E08("Enlisted Grade E8", "E-8", 10),
    E09("Enlisted Grade E9","E-9", 11),
    N00("Dependents, DoD Civilian Employees, Contract Surgeons","Civ", 12),
    O01("Officer Grade O1","O-1" ,13),
    O02("Officer Grade O2","O-2",14),
    O03("Officer Grade O3","O-3", 15),
    O04("Officer Grade O4","O-4", 16),
    O05("Officer Grade O5","O-5", 17),
    O06("Officer Grade O6","O-6", 18),
    O07("Officer Grade O7", "O-7",19),
    O08("Officer Grade O8", "O-8",20),
    O09("Officer Grade O9", "O-9",21),
    O10("Officer Grade O10","O-10", 22),
    W01("Warrant Officer Grade W1","W-1", 23),
    W02("Warrant Officer Grade W2","W-2", 24),
    W03("Warrant Officer Grade W3","W-3", 25),
    W04("Warrant Officer Grade W4", "W-4",26),
    W05("Warrant Officer Grade W5", "W-5",27);

    companion object{
        fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = Grade.values().protoToString(enum)
        fun fromString(string: String?) = Grade.values().stringToProto(string)
        fun toEvent(enum: SharedProtobufObjects.CompatibleEnum) = Grade.values()
            .toChangeOrClearEvent(enum, "grade")
    }
}