package gov.afrl.batdok.encounter.commands

import gov.afrl.batdok.commands.handler
import gov.afrl.batdok.commands.handlerWithId
import gov.afrl.batdok.commands.proto.*
import gov.afrl.batdok.commands.proto.LinkCommands.*
import gov.afrl.batdok.encounter.*
import gov.afrl.batdok.encounter.ids.toByteString
import gov.afrl.batdok.encounter.ids.toDomainId
import gov.afrl.batdok.encounter.ids.toDomainIdOrElse
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant

fun buildCreateLinkCommand(link: Link) = createLink{
    this.linkId = link.id.toByteString()
    this.comment = CommonLinkRelationships.fromString(link.relationship)
    this.ids.addAll(link.ids.map { it.toByteString() })
}

internal fun buildLinkFromLinkables(linkRelationship: CommonLinkRelationships, vararg items: Linkable) = buildLinkFromIds(linkRelationship, *items.map { it.id }.toTypedArray())

internal fun buildLinkFromIds(linkRelationship: CommonLinkRelationships, vararg ids: DomainId): LinkCommands.CreateLink {
    val link = Link(ids = ids.toList(), relationship = linkRelationship.dataString)
    return buildCreateLinkCommand(link)
}

fun createLinkCommandHandler(links: Links) = handlerWithId<LinkCommands.CreateLink> { documentId, commandData ->
    links += Link(
        ids = idsList.map { it.toDomainId() },
        relationship = CommonLinkRelationships.fromProto(comment),
        id = linkId.toDomainIdOrElse(commandData.commandId),
        timestamp = Instant.ofEpochSecond(commandData.timestamp),
        documentId = documentId
    )
}

fun buildAddRemoveFromLinkCommand(linkId: LinkId, idsToAdd: List<DomainId>, idsToRemove: List<DomainId>) = addRemoveFromLink {
    this.linkId = linkId.toByteString()
    this.addedIds.addAll(idsToAdd.map { it.toByteString() })
    this.removedIds.addAll(idsToRemove.map { it.toByteString() })
}

fun addRemoveFromLinkCommandHandler(links: Links) = handler<AddRemoveFromLink> {
    val link = links[linkId.toDomainId<LinkId>()] ?: return@handler
    val idList = link.ids.toMutableList()
    idList.addAll(this.addedIdsList.map { it.toDomainId() })
    idList.removeAll { this.removedIdsList.contains(it.toByteString()) }

    links += link.copy(ids = idList)
}

fun buildUpdateLinkCommentCommand(id: LinkId, newComment: String) = updateLinkComment {
    this.linkId = id.toByteString()
    this.comment = CommonLinkRelationships.fromString(newComment)
}

fun updateLinkCommentCommandHandler(links: Links) = handler<UpdateLinkComment> {
    val link = links[linkId.toDomainId<LinkId>()] ?: return@handler
    links += link.copy(relationship = CommonLinkRelationships.fromProto(comment))
}

fun buildDeleteLinkCommand(id: LinkId) = deleteLink{
    this.linkId = id.toByteString()
}

fun deleteLinkCommandHandler(links: Links) = handler<DeleteLink> {
    links.removeItem(this.linkId.toDomainId(), Instant.ofEpochSecond(it.timestamp))
}
