package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.CommandHandler
import gov.afrl.batdok.encounter.commands.addCommentCommandHandler
import gov.afrl.batdok.encounter.commands.addEventCommandHandler
import gov.afrl.batdok.encounter.commands.editEventCommandHandler
import gov.afrl.batdok.encounter.commands.removeEventCommandHandler
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.encounter.ids.EventId
import gov.afrl.batdok.util.DocumentItem
import gov.afrl.batdok.util.ICategorizedItemList
import gov.afrl.batdok.util.SortedCategorizedLinkableList
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant

class Event(
    var event: String,
    val eventId: EventId,
    timestamp: Instant?,
    documentId: DocumentId = DomainId.nil(),
    val callsign: String? = null,
    var eventType: String? = null,
    var showTime: Boolean = true,
    var isCustom: Boolean = false,
    val referencedItem: DomainId? = null
): DocumentItem<EventId>(eventId, documentId, eventType, timestamp)

class Events: ICategorizedItemList<Event> by SortedCategorizedLinkableList(){

    fun commentsForId(vararg referenceIds: DomainId) = list.filter { it.referencedItem in referenceIds }

    @Transient internal val handlers = CommandHandler {
        val events = this@Events
        +addEventCommandHandler(events)
        +editEventCommandHandler(events)
        +removeEventCommandHandler(events)
        +addCommentCommandHandler(events)
    }
}


fun changeOrClearEvent(changedText: Boolean, newText: String, item: String): String{
    return if(changedText){
        "Set $item to $newText"
    }else{
        "Cleared $item"
    }
}
