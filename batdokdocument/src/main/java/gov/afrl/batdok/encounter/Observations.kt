package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.CommandHandler
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.commands.*
import gov.afrl.batdok.encounter.observation.Observation
import gov.afrl.batdok.util.ICategorizedItemList
import gov.afrl.batdok.util.SortedCategorizedLinkableList
import java.io.Serializable

class Observations: ICategorizedItemList<Observation> by SortedCategorizedLinkableList(), Serializable {

    /**
     * A basic exam map of Location -> Finding
     */
    @Deprecated("Use Physical Exam Observation instead")
    val basicPhysicalExam: MutableMap<String, String> = mutableMapOf()

    @Transient val handlers = CommandHandler().apply{
        val observations = this@Observations
        +updatePhysicalExamFindingCommandHandler(basicPhysicalExam)
        +logObservationCommandHandler(observations)
        +updateObservationCommandHandler(observations)
    }

    internal fun linkHandlers(links: Links) = CommandHandler().apply {
        val observations = this@Observations
        +removeObservationCommandHandler(observations, links)
    }

    companion object{
        fun EventCommandHandler.includeObservationEvents(observations: Observations) = apply {
            +updatePhysicalExamFindingEventCommandHandler()
            +logObservationCommandEventHandler()
            +updateObservationCommandEventHandler(observations)
            +removeObservationEventCommandHandler(observations)
        }
    }
}