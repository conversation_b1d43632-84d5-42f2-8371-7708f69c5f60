package gov.afrl.batdok.encounter.observation

import gov.afrl.batdok.commands.proto.Observations
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.eFastExamData
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

class EFastExamData(
    val leftLungSliding: String? = null,
    val rightLungSliding: String? = null,
    val pericardialFluid: String? = null,
    val rightUpperQuadrant: String? = null,
    val leftUpperQuadrant: String? = null,
    val suprapubicFluid: String? = null,
    val interpretation: String? = null,
) : ObservationData {

    internal constructor(data: Observations.EFastExamData) : this(
        Type.fromProto(data.leftLungSliding),
        Type.fromProto(data.rightLungSliding),
        Type.fromProto(data.pericardialFluid),
        Type.fromProto(data.rightUpperQuadrant),
        Type.fromProto(data.leftUpperQuadrant),
        Type.fromProto(data.suprapubicFluid),
        Type.fromProto(data.interpretation),
    )

    enum class Type(override val protoIndex: Int, override val dataString: String): ProtoEnum {
        POSITIVE(1, "Positive"),
        NEGATIVE(2, "Negative"),
        EQUIVOCAL(3, "Equivocal");

        companion object {
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }

    override fun toProtobuf() = eFastExamData {
        leftLungSliding = Type.fromString(<EMAIL>)
        rightLungSliding = Type.fromString(<EMAIL>)
        pericardialFluid = Type.fromString(<EMAIL>)
        rightUpperQuadrant = Type.fromString(<EMAIL>)
        leftUpperQuadrant = Type.fromString(<EMAIL>)
        suprapubicFluid = Type.fromString(<EMAIL>)
        interpretation = Type.fromString(<EMAIL>)
    }

    override fun toDetailString(): String {
        return interpretation ?: "Incomplete result"
    }

    override fun toEventText(): String {
        return listOfNotNull(
            leftLungSliding?.let { "Left Lung Sliding - $it" },
            rightLungSliding?.let { "Right Lung Sliding - $it" },
            pericardialFluid?.let { "Pericardial Fluid - $it" },
            rightUpperQuadrant?.let { "Right Upper Quadrant - $it" },
            leftUpperQuadrant?.let { "Left Upper Quadrant - $it" },
            suprapubicFluid?.let { "Suprapubic Fluid - $it" },
            interpretation?.let { "Interpretation - $it" },
        ).joinToString()
    }
}
