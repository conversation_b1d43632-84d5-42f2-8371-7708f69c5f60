package gov.afrl.batdok.encounter.movement

import gov.afrl.batdok.commands.proto.SharedProtobufObjects.CompatibleEnum
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto
import gov.afrl.batdok.util.toChangeOrClearEvent

enum class EvacType(override val protoIndex: Int, override val dataString: String) : ProtoEnum {
    FIXED(1, "Fixed"),
    ROTARY(2, "Rotary"),
    GROUND(3, "Ground"),
    MEDEVAC(4, "MEDEVAC"),
    CASEVAC(5, "CASEVAC");

    companion object {
        fun fromProto(enum: CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)
        fun toEvent(enum: CompatibleEnum) = values().toChangeOrClearEvent(enum, "evac type")
    }
}