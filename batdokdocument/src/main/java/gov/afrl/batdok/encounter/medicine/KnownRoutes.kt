package gov.afrl.batdok.encounter.medicine

import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

enum class KnownRoutes(override val protoIndex: Int, override val dataString: String): ProtoEnum {
    IV(1, "IV"),
    IO(2, "IO"),
    IM(3, "IM"),
    PO(4, "PO"),
    PR(5, "PR"),
    SL(6, "SL"),
    SQ(7, "SQ"),
    IN(8, "IN"),
    ;

    companion object{
        fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)
    }
}