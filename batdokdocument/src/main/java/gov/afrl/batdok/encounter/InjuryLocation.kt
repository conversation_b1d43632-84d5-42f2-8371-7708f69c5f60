package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.proto.SharedProtobufObjects.CompatibleEnum
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto


enum class InjuryLocation(override val protoIndex: Int, override val dataString: String): ProtoEnum {
    GENERAL_APPEARANCE(1, "General Appearance"),
    HEAD(2, "Head"),
    NECK(3, "Neck"),
    EYES(4, "Eyes"),
    EARS(5, "Ears"),
    NOSE(6, "Nose"),
    THROAT(7, "Throat"),
    LYMPH_NODES(8, "Lymph Nodes"),
    RIGHT_ARM(9, "Right Arm"),
    LEFT_ARM(10, "Left Arm"),
    CHEST(11, "Chest"),
    BREAST(12, "Breast"),
    LUNGS(13, "Lungs"),
    CARDIOVASC<PERSON><PERSON><PERSON>(14, "Cardiovascular"),
    BACK(15, "Back"),
    ABDOM<PERSON>(16, "Abdomen"),
    URINARY_SYSTEM(17, "Urinary System"),
    GENITAL_FINDINGS(18, "Genital Findings"),
    PELVIC_EXAM(19, "Pelvic Exam"),
    PERINEUM(20, "Perineum"),
    RECTAL_EXAM(21, "Rectal Exam"),
    MUSCULOSKELETAL(22, "Musculoskeletal"),
    RIGHT_LEG(23, "Right Leg"),
    LEFT_LEG(24, "Left Leg"),
    NEUROLOGICAL(25, "Neurological"),
    PSYCHIATRIC(26, "Psychiatric"),
    INTEGUMENTARY(27, "Integumentary"),
    SPIRITUAL(28, "Spiritual");

    companion object{
        fun fromProto(enum: CompatibleEnum) = Moi.values().protoToString(enum)
        fun fromString(string: String?) = Moi.values().stringToProto(string)
    }
}