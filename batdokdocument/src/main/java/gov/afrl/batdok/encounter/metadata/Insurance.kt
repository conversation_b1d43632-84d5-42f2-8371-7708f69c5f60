package gov.afrl.batdok.encounter.metadata

import gov.afrl.batdok.commands.proto.StratevacMissionDataCommand.ChangeInsuranceCommand
import java.io.Serializable

/**
 * Holds insurance information
 */
data class Insurance(
    val companyName: String? = null,
    val companyAddress: String? = null,
    val companyPhone: String? = null,
    val policyNumber: String? = null,
    val relationToPolicyHolder: String? = null
) : Serializable {
    constructor(proto: ChangeInsuranceCommand): this(
        proto.companyName.value.takeIf { proto.hasCompanyName() && proto.companyName.value.isNotBlank() },
        proto.companyAddress.value.takeIf { proto.hasCompanyAddress() && proto.companyAddress.value.isNotBlank() },
        proto.companyPhone.value.takeIf { proto.hasCompanyPhone() && proto.companyPhone.value.isNotBlank() },
        proto.policyNumber.value.takeIf { proto.hasPolicyNumber() && proto.policyNumber.value.isNotBlank() },
        proto.relationToPolicyHolder.value.takeIf { proto.hasRelationToPolicyHolder() && proto.relationToPolicyHolder.value.isNotBlank() })

    override fun toString() = listOfNotNull(
        "Company: $companyName".takeUnless { companyName.isNullOrBlank() },
        "Address: $companyAddress".takeUnless { companyAddress.isNullOrBlank() },
        "Phone: $companyPhone".takeUnless { companyPhone.isNullOrBlank() },
        "Policy Number: $policyNumber".takeUnless { policyNumber.isNullOrBlank() },
        "Relation to Policy Holder: $relationToPolicyHolder".takeUnless { relationToPolicyHolder.isNullOrBlank() }
    ).joinToString(" ")

    fun isBlank() = companyName.isNullOrBlank()
            && companyAddress.isNullOrBlank()
            && companyPhone.isNullOrBlank()
            && policyNumber.isNullOrBlank()
            && relationToPolicyHolder.isNullOrBlank()
}