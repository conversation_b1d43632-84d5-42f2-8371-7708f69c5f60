package gov.afrl.batdok.encounter.vitals

import gov.afrl.batdok.commands.proto.VitalOuterClass
import gov.afrl.batdok.commands.proto.hR
import gov.afrl.batdok.encounter.IndividualVital
import gov.afrl.batdok.util.int32Value
import gov.afrl.batdok.util.stringValue

class HR(val pulse: Int?, val pulseLocation: String? = null): IndividualVital("HR"){
    constructor(hr: VitalOuterClass.HR): this(
        hr.pulseRate.value.takeIf { hr.hasPulseRate() },
        hr.location.value.takeIf { hr.hasLocation() }
    )
    override fun toProtobuf() = hR {
        pulse?.let { this.pulseRate = int32Value(it) }
        pulseLocation?.let { this.location = stringValue(it) }
    }

    override fun produceEmptyVital() = HR(null)

    override val eventMessage: String
        get() = listOfNotNull(
            pulse?.toString(),
            pulseLocation.takeUnless { pulseLocation.isNullOrEmpty() }
        ).joinToString(" ")

    override val isEmpty: <PERSON><PERSON><PERSON>
        get() = pulse == null && pulseLocation == null
}