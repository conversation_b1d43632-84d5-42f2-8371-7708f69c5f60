package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import com.google.protobuf.int64Value
import gov.afrl.batdok.commands.CommandHandler
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.commands.proto.SharedProtobufObjects.CompatibleEnum
import gov.afrl.batdok.commands.proto.SubjectiveCommands.UpdateOpqrstlCommand
import gov.afrl.batdok.commands.proto.UpdateOpqrstlCommandKt
import gov.afrl.batdok.commands.proto.updateOpqrstlCommand
import gov.afrl.batdok.encounter.commands.*
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.encounter.observation.ExamData
import gov.afrl.batdok.encounter.observation.Observation
import gov.afrl.batdok.util.*
import mil.af.afrl.batman.batdokid.DomainId
import java.io.Serializable
import java.time.Instant

class Subjective(
    chiefComplaints: List<String> = listOf(),
    reviewOfSystems: List<Pair<String, Boolean>> = listOf(),
    opqrstl: Opqrstl? = null,
    complaints: Complaints = Complaints()
): Serializable {
    @Deprecated("Use complaints instead")
    var chiefComplaints: List<String> = chiefComplaints
        internal set
    var reviewOfSystems: List<Pair<String, Boolean>> = reviewOfSystems
        internal set
    var opqrstl: Opqrstl? = opqrstl
        internal set
    var complaints :Complaints = complaints
        internal set


    @Deprecated("Use plusAssign(Complaint) instead")
    internal fun addChiefComplaint(complaint: String){
        if(complaint !in chiefComplaints) {
            chiefComplaints += complaint
        }
    }

    internal operator fun plusAssign(complaint: Complaint){
        complaints += complaint
    }

    internal operator fun plusAssign(systemReview: Pair<String, Boolean>){
        reviewOfSystems = reviewOfSystems.filter { it.first != systemReview.first } + systemReview
    }

    internal fun removeComplaint(id: DomainId){
        complaints.removeItem(id, Instant.now())
    }

    @Deprecated("Use removeComplaint instead")
    internal fun removeChiefComplaint(symptom: String){
        chiefComplaints -= symptom
    }

    internal fun removeSystemReview(systemReview: String){
        reviewOfSystems = reviewOfSystems.filter { it.first != systemReview }
    }

    @Transient val handlers = CommandHandler().apply {
        val subj = this@Subjective
        +updateChiefComplaintCommandHandler(subj)
        +addComplaintCommandHandler(subj)
        +removeComplaintCommandHandler(subj)
        +updateReviewOfSystemsCommandHandler(subj)
        +updateOpqrstlCommandHandler(subj)
        +updateComplaintCommandHandler(subj)
    }

    companion object{
        fun EventCommandHandler.includeSubjectiveEvents(subjective: Subjective){
            +updateChiefComplaintEventCommandHandler()
            +addComplaintEventCommandHandler()
            +removeComplaintEventCommandHandler(subjective)
            +updateReviewOfSystemsEventCommandHandler()
            +updateOpqrstlEventCommandHandler(subjective)
            +updateComplaintEventCommandHandler(subjective)
        }
    }
}

data class Complaint(
    val complaint: String,
    val history: String = "",
    val careProvider: String = "",
    val reviewOfSystems: String = "",
    override val id: DomainId = DomainId.create(),
    override val timestamp: Instant = Instant.now(),
    override val documentId: DocumentId = DomainId.nil(),
    val disposition: String? = null
): DocumentItem<DomainId>(id, documentId, complaint, timestamp) {

    fun buildAssociationCommand(linkable: Linkable, relationship: CommonLinkRelationships = CommonLinkRelationships.ASSOC_WITH_COMPLAINT) =
        buildAssociationCommand(linkable.id, relationship)
    fun buildAssociationCommand(id: DomainId, relationship: CommonLinkRelationships = CommonLinkRelationships.ASSOC_WITH_COMPLAINT) =
        buildLinkFromIds(relationship, this.id, id)
    fun buildSetDispositionCommand(disposition: String?) = buildUpdateComplaintCommand(id, disposition = disposition ?: "")

    fun getAssociatedItems(document: Document) = with(document) {
        getLinkedItems(CommonLinkRelationships.ASSOC_WITH_COMPLAINT)
    }

    fun getSymptoms(document: Document) = with(document){
        getLinkedItems(CommonLinkRelationships.SYMPTOMS).filterIsInstance<Observation>()
    }
    fun getRedFlags(document: Document) = with(document){
        getLinkedItems(CommonLinkRelationships.RED_FLAGS).filterIsInstance<Observation>()
    }
    fun getExams(document: Document) = getAssociatedItems(document).filterIsInstance<Observation>()
        .mapNotNull { it.getData<ExamData>() }

    @Deprecated("Use buildAssociationCommand instead")
    fun buildLinkCommand(vararg documentItem: DocumentItem<*>, document: Document) =
        buildLinkCommand(*documentItem.map { it.id}.toTypedArray(), document = document)

    @Deprecated("Use buildAssociationCommand instead")
    fun buildLinkCommand(vararg ids: DomainId, document: Document): Message {
        // does link exist for complaint
        return if (getLinks(document).none { it.relationship == CommonLinkRelationships.ASSOC_WITH_COMPLAINT.dataString }) {
            // if not, create new link command
            buildCreateLinkCommand(Link(listOf(id) + ids, relationship = CommonLinkRelationships.ASSOC_WITH_COMPLAINT.dataString))
        } else {
            // otherwise, add item to existing link
            val link = getLinks(document).first { it.relationship == CommonLinkRelationships.ASSOC_WITH_COMPLAINT.dataString }
            buildAddRemoveFromLinkCommand(link.id, ids.toList(), listOf())
        }
    }

    @Deprecated("Just delete the item. That will unlink it")
    fun buildUnLinkCommand(vararg documentItem: DocumentItem<*>, document: Document) =
        buildUnLinkCommand(*documentItem.map { it.id}.toTypedArray(), document = document)

    @Deprecated("Just delete the item. That will unlink it")
    fun buildUnLinkCommand(vararg ids: DomainId, document: Document): Message? {
        // does link exist
        val link = getLinks(document).firstOrNull { it.relationship == CommonLinkRelationships.ASSOC_WITH_COMPLAINT.dataString } ?: return null
        return buildAddRemoveFromLinkCommand(link.id, listOf(), ids.toList())
    }

}

class Complaints(): ICategorizedItemList<Complaint> by SortedCategorizedLinkableList(), Serializable

enum class CommonComplaints(override val protoIndex: Int, override val dataString: String): ProtoEnum{
    SORE_THROAT(1, "Sore Throat/Hoarseness"),
    EAR_PAIN(2, "Ear Pain/Drainage/Trauma"),
    COLD_SX(3, "Cold Symptoms/Allergies/Cough"),
    HEARING_PROBLEMS(4, "Ringing in the Ears/Hearing Problem"),
    NOSE_BLEED(5, "Nosebleed/Nose Trauma"),
    BACK_PAIN(6, "Back Pain"),
    NECK_PAIN(7, "Neck Pain"),
    SHOULDER_PAIN(8, "Shoulder Pain"),
    ELBOW_PAIN(9, "Elbow Pain"),
    WRIST_PAIN(10, "Wrist Pain"),
    HAND_PAIN(11, "Hand Pain"),
    HIP_PAIN(12, "Hip Pain"),
    KNEE_PAIN(13, "Knee Pain"),
    ANKLE_PAIN(14, "Ankle Pain"),
    FOOT_PAIN(15, "Foot Pain"),
    EXTREMITY_PAIN(16, "Extremity, Non-joint Pain"),
    NAUSEA(17, "Nausea/Vomiting"),
    DIARRHEA(18, "Diarrhea"),
    ABDOMINAL_PAIN(19, "Abdominal and Flank Pain"),
    RECTAL_PAIN(20, "Rectal Pain/Itching/Bleeding"),
    CONSTIPATION(21, "Constipation"),
    SWALLOWING_DIFFICULTY(22, "Difficulty When Swallowing"),
    HEARTBURN(23, "Heartburn"),
    SHORTNESS_OF_BREATH(24, "Shortness of Breath"),
    CHEST_PAIN(25, "Chest Pain"),
    PAINFUL_URINATION(26, "Painful/Frequent Urination"),
    GROIN_PAIN_OR_DISCHARGE(27, "Groin/Testicular Pain or Urethral Discharge"),
    STI(28, "Sexually Transmitted Infection (STI)"),
    VOIDING_PROBLEMS(29, "Problems with Voiding"),
    DIZZINESS(30, "Dizziness/Faintness/Blackout"),
    HEADACHE(31, "Headache"),
    NUMBNESS(32, "Numbness/Tingling/Paralysis/Weakness"),
    DROWSINESS(33, "Drowsiness/Confusion"),
    DEPRESSION(34, "Depression/Nervousness/Anxiety/Tension"),
    MINOR_TBI(35, "Minor Traumatic Brain Injury"),
    FATIGUE(36, "Fatigue"),
    FEVER(37, "Fever/Chills"),
    EYE_PAIN(38, "Eye Pain/Redness/Discharge/Itching/Injury"),
    EYELID_PROBLEM(39, "Eyelid Problem"),
    DECREASED_VISION(40, "Decreased Vision, Seeing Spots, Request for Glasses"),
    DIPLOPIA(41, "Seeing Double (Diplopia)"),
    BREAST_PROBLEMS(42, "Breast Problems"),
    PREGNANCY(43, "Suspects Pregnancy"),
    MENSTRUAL_PROBLEMS(44, "Menstrual Problems, Vaginal Bleeding"),
    VAGINAL_DISCHARGE(45, "Vaginal Discharge, Itching, Irritation, or Pain"),
    PAP_REQUEST(46, "Request for PAP or Routine Pelvic Examination"),
    CONTRACEPTION_REQUEST(47, "Request for Information on Contraception"),
    SKIN_DISORDER(48, "Unknown Cause of Skin Disorder/Complaint"),
    ACNE(49, "Acne"),
    INGROWN_HAIRS(50, "Shaving Problem-Pseudofolliculitis Barbae (Ingrown Hairs)"),
    DANDRUFF(51, "Dandruff (Scaling of the Scalp)"),
    HAIR_LOSS(52, "Hair Loss"),
    ATHLETES_FOOT(53, "Athlete's Foot (Tinea Pedis)"),
    JOCK_ITCH(54, "Jock Itch (Tinea Cruris)"),
    TINEA_VERSICOLOR(55, "Scaling, Depigmented Spots on Chest, Back,and Upper Arms (Tinea Versicolor)"),
    BOILS(56, "Boils"),
    COLD_SORES(57, "Fever Blisters (Cold Sores)"),
    SKIN_ABRASION(58, "Skin Abrasion/Laceration"),
    SUTURE_REMOVAL(59, "Suture Removal"),
    DRUG_RASH(60, "Drug Rash, Contact Dermatitis"),
    SUNBURN(61, "Burns/Sunburn"),
    FRICTION_BLISTERS_ON_FEET(62, "Friction Blisters on Feet"),
    CORNS_ON_FEET(63, "Corns on Feet"),
    PLANTER_WARTS(64, "Cutaneous (Plantar) Warts"),
    INGROWN_TOENAILS(65, "Ingrown Toenail"),
    HEAT_ILLNESS(66, "Exertional Heat Illness Heat Injury/Hyperthermia (Heat Cramps, Heat Exhaustion, Heat Stroke)"),
    HYPOTHERMIA(67, "Hypothermia"),
    IMMERSION_FOOT(68, "Immersion Foot"),
    CHAPPED_SKIN(69, "Chapped Skin/Windburn"),
    FROSTBITE(70, "Frostbite"),
    CRABS_LICE(71, "Crabs/Lice (Pediculosis)"),
    INSECT_BITES(72, "Insect Bites (Not Crabs/Lice)"),
    HEPATITIS_HIV(73, "Exposed to Hepatitis or HIV"),
    DENTAL_PROBLEMS(74, "Dental Problems"),
    MOUTH_SORES(75, "Sores in the Mouth"),
    RX_REFILL(76, "Prescription Refill"),
    VASECTOMY_REQUEST(77, "Requests a Vasectomy"),
    IMMUNIZATION(78, "Needs an Immunization"),
    LYMPH_NODE_ENLARGEMENT(79, "Lymph Node Enlargement"),
    BP_CHECK(80, "Blood Pressure Check"),
    PCS_SCREENING(81, "Medical Screening for Overseas PCS"),
    WEIGHT_REDUCTION(82, "Weight Reduction"),
    NOT_ON_LIST(83, "Complaint Not on the List"),
    REQUEST_FOR_NON_RX_MED(84, "Request for Nonprescription or Traveling Medication"),
    NO_SIGNS_OF_IMPROVEMENT(85, "No Signs of Improvement"),
    RETURN_REQUESTED(86, "Return Requested by Provider"),
    ;

    companion object{
        fun fromProto(enum: CompatibleEnum) = entries.toTypedArray().protoToString(enum)
        fun fromString(string: String?) = entries.toTypedArray().stringToProto(string)
    }
}

class Opqrstl(
    val onset: String? = null,
    val provocationBetterWhen: String = "",
    val provocationWorseWhen: String = "",
    val qualities: List<String> = listOf(),
    val radiates: Boolean? = null, //Sent in Protobuf as an int. 0=False, 1=True, 2=Clear
    val severity: Int? = null, //-1=clear
    val time: Long? = null,
    val lastOralIntake: Long? = null
): Serializable{
    internal constructor(command: UpdateOpqrstlCommand): this(
        Onset.fromProto(command.onset),
        command.provocationBetterWhen.value.takeIf { command.hasProvocationBetterWhen() } ?: "",
        command.provocationWorseWhen.value.takeIf { command.hasProvocationWorseWhen() } ?: "",
        command.qualities.qualitiesList.mapNotNull { Quality.fromProto(it) },
        command.radiates.value.takeIf { command.hasRadiates() && it != 2 }?.let { it == 1 },
        command.severity.value.takeIf { command.hasSeverity() && it != -1 },
        command.time.value.takeIf { command.hasTime() && it != 0L },
        command.lastOralIntake.value.takeIf { command.hasLastOralIntake() && it != 0L }
    )

    fun toProtobuf() = updateOpqrstlCommand{
        onset = Onset.fromString(<EMAIL>)
        provocationBetterWhen = stringValue(<EMAIL>)
        provocationWorseWhen = stringValue(<EMAIL>)
        qualities = UpdateOpqrstlCommandKt.qualityWrapper { qualities.addAll(<EMAIL> { Quality.fromString(it) }) }
        radiates = int32Value(when(<EMAIL>){
            false -> 0
            true -> 1
            null -> 2
        })
        severity = int32Value(<EMAIL> ?: -1)
        time = int64Value { <EMAIL>?.let { value = it } }
        lastOralIntake = int64Value { <EMAIL>?.let { value = it } }
    }

    fun isEmpty() = listOfNotNull(
        onset,
        provocationBetterWhen.takeIf { it.isNotBlank() },
        provocationWorseWhen.takeIf { it.isNotBlank() },
        qualities.takeIf { it.isNotEmpty() },
        radiates,
        severity,
        time,
        lastOralIntake
    ).isEmpty()

    override fun toString(): String {
        return listOfNotNull(
            onset?.let { "Onset: $it" },
            provocationBetterWhen.takeIf { it.isNotBlank() }?.let { "Better when: $it" },
            provocationWorseWhen.takeIf { it.isNotBlank() }?.let { "Worse when: $it" },
            qualities.takeIf { it.isNotEmpty() }?.let { "Qualities: ${it.joinToString(", ")}" },
            radiates?.let { if(it) "Radiates" else "Does not Radiate" },
            severity?.let { "Severity: $it" },
            time?.let { "Time: ${formatDuration(it)}" },
            lastOralIntake?.let { "Last Oral Intake: ${Instant.ofEpochMilli(it).format(Patterns.mdhm_24_space_comma_colon)}" }
        ).joinToString("; ")
    }
}

enum class Onset(override val protoIndex: Int, override val dataString: String): ProtoEnum{
    SUDDEN(1, "Sudden"),
    GRADUAL(2, "Gradual")
    ;

    companion object{
        fun fromProto(enum: CompatibleEnum) = entries.toTypedArray().protoToString(enum)
        fun fromString(string: String?) = entries.toTypedArray().stringToProto(string)
    }
}

enum class Quality(override val protoIndex: Int, override val dataString: String): ProtoEnum{
    SHARP(1, "Sharp"),
    DULL(2, "Dull"),
    POUNDING(3, "Pounding"),
    STABBING(4, "Stabbing"),
    THROBBING(5, "Throbbing"),
    HOT(6, "Hot"),
    CONSTANT(7, "Constant"),
    INTERMITTENT(8, "Intermittent"),
    BETTER_NOW(9, "Better now"),
    WORSE_NOW(10, "Worse now"),
    ;


    companion object{
        fun fromProto(enum: CompatibleEnum) = entries.toTypedArray().protoToString(enum)
        fun fromString(string: String?) = entries.toTypedArray().stringToProto(string)
    }
}