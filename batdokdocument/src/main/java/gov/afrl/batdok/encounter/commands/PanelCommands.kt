package gov.afrl.batdok.encounter.commands

import gov.afrl.batdok.commands.eventHandler
import gov.afrl.batdok.commands.handler
import gov.afrl.batdok.commands.handlerWithId
import gov.afrl.batdok.commands.proto.*
import gov.afrl.batdok.encounter.KnownEventTypes
import gov.afrl.batdok.encounter.Panels
import gov.afrl.batdok.encounter.ids.*
import gov.afrl.batdok.encounter.panel.KnownLabs
import gov.afrl.batdok.encounter.panel.LabEntry
import gov.afrl.batdok.encounter.panel.Panel
import gov.afrl.batdok.util.toPrimitive
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant

private fun List<LabCommands.LabEntry>.toEncounterLab(id: PanelId, documentId: DocumentId, timestamp: Instant, previousLabs: List<LabEntry>? = null): Panel {
    val individualLabsFromProto = mapNotNull { lab ->
        KnownLabs.fromProto(lab.name)?.let {
            LabEntry(
                it,
                lab.valueOrNull?.toPrimitive(),
                lab.unitOrNull?.toPrimitive().takeUnless { it.isNullOrBlank() }
            )
        }
    }
    val oldLabs = previousLabs?:listOf()
    return Panel(
        id,
        timestamp,
        (individualLabsFromProto + oldLabs)
            .distinctBy { it.name } //Remove old labs that were updated from proto
            .filter { it.value != null }, //Remove any labs that are now null
        documentId
    )
}

@Deprecated("Labs need to be logged individually. Use buildLogLabCommand instead", replaceWith = ReplaceWith("buildLogLabCommand"))
fun buildLogPanelCommand(panel: Panel) = logPanelCommand {
    this.labEntries.addAll(panel.toProtoLabList())
    this.id = panel.id.toByteString()
}

fun logPanelCommandHandler(panels: Panels) =
    handlerWithId<PanelCommands.LogPanelCommand> { docId, command ->
        val encounterPanel = labEntriesList.toEncounterLab(
            id.toDomainIdOrElse(command.commandId),
            docId,
            Instant.ofEpochSecond(command.timestamp)
        )
        panels += encounterPanel
    }

fun logPanelEventCommandHandler() =
    eventHandler<PanelCommands.LogPanelCommand>(KnownEventTypes.LABS.dataString) { fullCommand ->
        val encounterLab = labEntriesList.toEncounterLab(
            fullCommand.commandId.toDomainId(),
            DomainId.nil(),
            Instant.ofEpochSecond(fullCommand.timestamp)
        )
        "Logged Panel: " + encounterLab.toEventString()
    }

@Deprecated("Labs need to be updated individually. Use buildUpdateLabCommand instead", replaceWith = ReplaceWith("buildUpdateLabCommand"))
fun buildUpdatePanelCommand(lab: Panel, previousLab: Panel? = null) = updatePanelCommand {
    this.panelid = lab.id.toByteString()
    this.time = lab.timestamp.epochSecond
    this.labEntries.addAll(lab.toProtoLabList(previousLab))
}

fun updatePanelsCommandHandler(panels: Panels) =
    handlerWithId<PanelCommands.UpdatePanelCommand> { docId, _ ->
        val id = panelid.toDomainId<PanelId>()
        panels += labEntriesList.toEncounterLab(
            id,
            docId,
            Instant.ofEpochSecond(time),
            panels[id]?.labs
        )
    }

fun updatePanelEventCommandHandler(labs: Panels) = eventHandler<PanelCommands.UpdatePanelCommand>(
    KnownEventTypes.LABS.dataString,
    referenceId = { panelid.toDomainId() },
    handler = {
        val id = panelid.toDomainId<PanelId>()
        val encounterLab = labEntriesList.toEncounterLab(
            id,
            DomainId.nil(),
            Instant.ofEpochSecond(time),
            labs[id]?.labs
        )
        "Updated Panel: " + encounterLab.toEventString()
    }
)

@Deprecated("Labs need to be removed individually. Use buildRemoveLabCommand instead", replaceWith = ReplaceWith("buildRemoveLabCommand"))
fun buildRemovePanelCommand(panelId: PanelId) = removePanelCommand {
    this.panelId = panelId.toByteString()
}

fun removePanelCommandHandler(panels: Panels) = handler<PanelCommands.RemovePanelCommand> {
    panels.removeItem(panelId.toDomainId(), Instant.ofEpochSecond(it.timestamp))
}

fun removePanelEventCommandHandler(labs: Panels) = eventHandler<PanelCommands.RemovePanelCommand>(
    referenceId = { panelId.toDomainId() }
) {
    val id = panelId.toDomainId<PanelId>()
    val encounterLab = labs[id] ?: return@eventHandler null
    "Removed Panel: " + encounterLab.toEventString()
}