package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.CommandHandler
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.commands.*
import java.io.Serializable

class Diagnosis: Serializable {
    var category: String? = null
        internal set
    var differentials: List<String> = listOf()
        internal set
    var selectedProtocol: String? = null
        internal set

    @Transient val handlers = CommandHandler().apply {
        val diagnosis = this@Diagnosis
        +changeDiagnosisCommandHandler(diagnosis)
        +updateDifferentialsCommandHandler(diagnosis)
        +selectProtocolCommandHandler(diagnosis)
    }

    companion object{
        fun EventCommandHandler.includeDiagnosisCommands(){
            +changeDiagnosisEventCommandHandler()
            +updateDifferentialsEventCommandHandler()
            +selectProtocolEventCommandHandler()
        }
    }
}