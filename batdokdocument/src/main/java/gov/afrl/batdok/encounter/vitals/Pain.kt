package gov.afrl.batdok.encounter.vitals

import gov.afrl.batdok.commands.proto.VitalOuterClass
import gov.afrl.batdok.commands.proto.pain
import gov.afrl.batdok.encounter.IndividualVital
import gov.afrl.batdok.util.int32Value

class Pain(val pain: Int?): IndividualVital("Pain Scale"){
    constructor(pain: VitalOuterClass.Pain): this(
        pain.pain.value.takeIf { pain.hasPain() },
    )
    override fun toProtobuf() = pain {
        <EMAIL>?.let { this.pain = int32Value(it) }
    }
    override fun produceEmptyVital() = Pain(null)

    override val eventMessage: String
        get() = "$painScore"

    override val isEmpty: Boolean
        get() = pain == null

    val painScore
        get() = if (pain == -1) "NT" else pain.toString()
}