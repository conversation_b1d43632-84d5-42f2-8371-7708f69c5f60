package gov.afrl.batdok.encounter.observation

import gov.afrl.batdok.commands.proto.Observations
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.gastroData
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

class GastroData(val type: String? = null): ObservationData {
    internal constructor(data: Observations.GastroData): this(Type.fromProto(data.type))

    enum class Type(override val protoIndex: Int, override val dataString: String): ProtoEnum {
        OPENABDOMEN(1, "Open Abdomen"),
        SOFT(2, "Soft"),
        DISTENDED(3, "Distended"),
        TYMPANITIC(4, "Tympanitic"),
        PAIN(5, "Pain"),
        MASS(6, "Mass"),
        HERNIA(7, "Hernia"),
        BOWELSOUNDS(8, "Bowel Sounds"),
        ;
        companion object {
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values()
                .protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }

    override fun toProtobuf() = gastroData {
        this.type = Type.fromString(<EMAIL>)
    }

    override fun toDetailString(): String {
        return this.type ?: ""
    }
}