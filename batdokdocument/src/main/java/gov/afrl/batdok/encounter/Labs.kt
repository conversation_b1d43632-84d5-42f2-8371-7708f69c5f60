package gov.afrl.batdok.encounter

import com.google.protobuf.Message
import gov.afrl.batdok.commands.CommandHandler
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.labEntry
import gov.afrl.batdok.commands.proto.logLabCommand
import gov.afrl.batdok.encounter.commands.*
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.encounter.ids.LabId
import gov.afrl.batdok.encounter.ids.toByteString
import gov.afrl.batdok.util.*
import mil.af.afrl.batman.batdokid.DomainId
import java.io.Serializable
import java.time.Instant

data class LabPanel(val id: LinkId, val name: String, val timestamp: Instant, val labs: List<IndividualLab>)
class Labs: Serializable, ICategorizedItemList<IndividualLab> by SortedCategorizedLinkableList() {
    fun getPanels(links: Links): List<LabPanel> {
        return links.getLinks(list.map { it.id }).map { link ->
            LabPanel(link.id, link.relationship, link.timestamp, link.ids.mapNotNull { this[it] })
        }
    }

    @Transient val handlers = CommandHandler().apply {
        val labs = this@Labs
        +logLabsCommandHandler(labs)
        +updateLabsCommandHandler(labs)
        +removeLabCommandHandler(labs)
    }

    companion object{
        fun EventCommandHandler.includeLabHandlers(labs: Labs){
            +logLabsEventCommandHandler()
            +updateLabEventCommandHandler(labs)
            +removeLabEventCommandHandler(labs)
        }

        fun buildPanelCommands(panelName: String, vararg labEntries: IndividualLab): List<Message> {
            return listOf(
                logLabCommand {
                    this.labValues.addAll(labEntries.map { it.toProto() })
                },
                buildCreateLinkCommand(
                    Link(
                        labEntries.map { it.id },
                        panelName
                    )
                )
            )
        }
    }
}

data class IndividualLab(
    val name: String,
    val value: String?,
    val unit: String? = null,
    override val id: LabId = DomainId.create(),
    override val timestamp: Instant = Instant.now(),
    val documentId: DocumentId = DomainId.nil()
)
    : Timestamped, Linkable, Categorized{
    override val category: String?
        get() = name

    constructor(
        name: gov.afrl.batdok.encounter.panel.KnownLabs,
        value: String?,
        unit: String? = null,
        id: LabId = DomainId.create(),
        timestamp: Instant = Instant.now(),
        documentId: DocumentId = DomainId.nil()
    ): this(name.dataString, value, unit, id, timestamp, documentId)

    override fun toString(): String {
        return "$name: $value${" $unit".takeUnless { unit.isNullOrBlank() }?:""}"
    }

    @Deprecated("This isn't quite removed from BATDOK yet. Use the other KnownLabs instead")
    enum class KnownLabs(override val protoIndex: Int, override val dataString: String): ProtoEnum{
        PH(1, "pH"),
        PAO2(2, "PaO2"),
        NA(3, "Na"),
        HGB(4, "Hgb"),
        ICA(5, "iCa"),
        BE(6, "BE"),
        PCO2(7, "PCO2"),
        K(8, "K"),
        HCT(9, "Hct"),
        HCO3(10, "HCO3"),
        GLUCOSE(11, "Glucose"),
        CL(12, "Cl"),
        BUN(13, "BUN"),
        CO2(14, "CO2"),
        CREAT(15, "CREAT"),
        WBC(16, "WBC"),
        PLT(17, "PLT"),
        ;
        companion object{
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }

    fun toProto() = labEntry {
        id = <EMAIL>()
        name = gov.afrl.batdok.encounter.panel.KnownLabs.fromString(<EMAIL>)
        value = nullableStringValue(<EMAIL>)
        unit = nullableStringValue(<EMAIL>)
        time = nullableInt64Value(<EMAIL>)
    }
}

