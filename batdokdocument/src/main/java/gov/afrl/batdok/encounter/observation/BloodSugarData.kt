package gov.afrl.batdok.encounter.observation

import gov.afrl.batdok.commands.proto.Observations
import gov.afrl.batdok.commands.proto.bloodSugarData
import gov.afrl.batdok.util.compatibleEnum
import gov.afrl.batdok.util.nullableDoubleValue
import gov.afrl.batdok.util.toPrimitive

@Deprecated("This should use the Glucose Lab instead, but we haven't quite figured it out yet")
class BloodSugarData(val value: Double? = null, val unit: String? = null): ObservationData {
    internal constructor(data: Observations.BloodSugarData): this(data.value.toPrimitive(), data.unit.toPrimitive())
    override fun toProtobuf() = bloodSugarData{
        this.value = nullableDoubleValue(<EMAIL>)
        this.unit = compatibleEnum(<EMAIL>)
    }

    override fun toDetailString(): String {
        return listOfNotNull(
            // format to one decimal place
            value?.let { String.format("%.1f", it) },
            unit
        ).joinToString(" ")
    }
}