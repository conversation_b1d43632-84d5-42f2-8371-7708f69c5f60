package gov.afrl.batdok.encounter.treatment

import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.TreatmentCommands.IntubatedBy
import gov.afrl.batdok.commands.proto.intubatedBy
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

class IntubatedByData(val intubater: String? = null): TreatmentData{
    constructor(intubatedBy: IntubatedBy): this(Intubater.fromProto(intubatedBy.intubater))
    override fun toProtobuf(): Message = intubatedBy{
        intubater = Intubater.fromString(<EMAIL>)
    }

    override fun toDetailString(): String {
        return intubater ?: ""
    }

    enum class Intubater(override val dataString: String, override val protoIndex: Int): ProtoEnum{
        PRIOR_TO_TRANSPORT("Prior to transport", 1),
        BY_TRANSPORT_CREW("By transport crew", 2);

        companion object{
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = entries.toTypedArray()
                .protoToString(enum)
            fun fromString(string: String?) = entries.toTypedArray().stringToProto(string)
        }
    }
}