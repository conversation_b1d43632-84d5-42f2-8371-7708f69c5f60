package gov.afrl.batdok.encounter.medicine

import gov.afrl.batdok.encounter.ids.MedicineId
import java.io.Serializable

interface IMedicine: Serializable {
    val name: String
    val ndc: String?
    val rxcui: String?
    val medId: MedicineId
    val route: String?
    val volume: Float?
    val unit: String?
    val serialNumber: String?
    val expirationDate: String?
    val type: String?
    val exportName: String?

    /** Name, route, volume unit */
    val title: String
        get() = listOfNotNull(
            name.takeIf { it.isNotEmpty() },
            route.takeIf { !it.isNullOrEmpty() },
            listOfNotNull(
                volume?.let { if(it > 0F ) createVolumeString(it) else null },
                unit.takeIf { !it.isNullOrEmpty() }
            ).joinToString(" ").takeIf { it.isNotEmpty() }
        ).joinToString()
}


private fun createVolumeString(volume: Float): String {
    var volumestr = volume.toString()

    if (volumestr.endsWith(".0")) {
        volumestr = volumestr.replace(".0", "")
    }
        return volumestr
}

