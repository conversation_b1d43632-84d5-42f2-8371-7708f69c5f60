package gov.afrl.batdok.encounter.observation

import gov.afrl.batdok.commands.proto.Observations
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.pulseData
import gov.afrl.batdok.commands.proto.pulseValuesData
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

class PulseTypeData(val quality: String? = null, val location: String? = null): ObservationData {
    internal constructor(data: Observations.PulseData) : this(
        Quality.fromProto(data.quality),
        Location.fromProto(data.location)
    )

    enum class Quality(override val protoIndex: Int, override val dataString: String) : ProtoEnum {
        NORMAL(1, "Normal"),
        DECR(2, "Decreasing"),
        ABSENT(3, "Absent");

        companion object {

            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }

    }

    enum class Location(override val protoIndex: Int, override val dataString: String) : ProtoEnum {
        CAROTID_LEFT(1, "Carotid Left"),
        CAROTID_RIGHT(2, "Carotid Right"),
        RADIAL_LEFT(3, "Radial Left"),
        RADIAL_RIGHT(4, "Radial Right"),
        FEM_LEFT(5, "Femoral Left"),
        FEM_RIGHT(6, "Femoral Right"),
        PEDAL_LEFT(7, "Pedal Left"),
        PEDAL_RIGHT(8, "Pedal Right");

        companion object {

            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }

    }

    override fun toProtobuf() = pulseData {
        this.quality = Quality.fromString(<EMAIL>)
        this.location = Location.fromString(<EMAIL>)
    }

    override fun toDetailString(): String {
        val qualityText = "Quality: ${quality ?: "N/A"}"
        val locationText = "Location: ${location ?: "N/A"}"

        return "$qualityText, $locationText"
    }
}

class PulseValuesData(
    val brachial: String? = null,
    val carotid: String? = null,
    val femoral: String? = null,
    val pedal: String? = null,
    val radial: String? = null,
    val temperature: String? = null,
): ObservationData {
    internal constructor(extras: Observations.PulseValuesData): this(
        Quality.fromProto(extras.brac),
        Quality.fromProto(extras.car),
        Quality.fromProto(extras.fem),
        Quality.fromProto(extras.ped),
        Quality.fromProto(extras.rad),
        Quality.fromProto(extras.temp)
    )

    override fun toProtobuf() = pulseValuesData {
        brac = Quality.fromString(brachial)
        car = Quality.fromString(carotid)
        fem = Quality.fromString(femoral)
        ped = Quality.fromString(pedal)
        rad = Quality.fromString(radial)
        temp = Quality.fromString(temperature)
    }

    enum class Quality(override val protoIndex: Int, override val dataString: String): ProtoEnum{
        A(1, "A"),
        D(2, "D"),
        ONE(3, "+1"),
        TWO(4, "+2"),
        THREE(5, "+3"),
        ;

        companion object {
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }

    override fun toDetailString() = listOfNotNull(
        brachial.toDetailString("Brachial"),
        carotid.toDetailString("Carotid"),
        femoral.toDetailString("Femoral"),
        pedal.toDetailString("Pedal"),
        radial.toDetailString("Radial"),
        temperature.toDetailString("Temperature")
    ).joinToString(", ")
}