package gov.afrl.batdok.encounter.commands

import com.google.protobuf.Any
import com.google.protobuf.Message
import com.google.protobuf.kotlin.isA
import com.google.protobuf.kotlin.unpack
import gov.afrl.batdok.commands.eventHandler
import gov.afrl.batdok.commands.handler
import gov.afrl.batdok.commands.handlerWithId
import gov.afrl.batdok.commands.proto.*
import gov.afrl.batdok.commands.proto.VitalCommands.*
import gov.afrl.batdok.commands.proto.VitalOuterClass.*
import gov.afrl.batdok.encounter.EncounterVital
import gov.afrl.batdok.encounter.IndividualVital
import gov.afrl.batdok.encounter.KnownEventTypes
import gov.afrl.batdok.encounter.Vitals
import gov.afrl.batdok.encounter.ids.*
import gov.afrl.batdok.encounter.vitals.*
import gov.afrl.batdok.encounter.vitals.EtCO2
import gov.afrl.batdok.encounter.vitals.GCS
import gov.afrl.batdok.encounter.vitals.HR
import gov.afrl.batdok.encounter.vitals.Output
import gov.afrl.batdok.encounter.vitals.PIP
import gov.afrl.batdok.encounter.vitals.Pain
import gov.afrl.batdok.encounter.vitals.Resp
import gov.afrl.batdok.encounter.vitals.SpO2
import gov.afrl.batdok.encounter.vitals.Temp
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant

fun VitalKt.Dsl.addVital(vital: Message) = vitals.add(Any.pack(vital, ""))
fun VitalKt.Dsl.addVital(vital: IndividualVital) = addVital(vital.toProtobuf())

fun Vital.toEncounterVital(id: EncounterVitalId, documentId: DocumentId, timestamp: Instant): EncounterVital{
    var vitals = EncounterVital(id, timestamp, documentId = documentId)
    vitals += vitalsList.mapNotNull {
        when{
            it.isA<VitalOuterClass.HR>() -> HR(it.unpack())
            it.isA<VitalOuterClass.SpO2>() -> SpO2(it.unpack())
            it.isA<VitalOuterClass.Resp>() -> Resp(it.unpack())
            it.isA<BP>() -> BloodPressure(it.unpack())
            it.isA<AVPU>() -> Avpu(it.unpack<AVPU>())
            it.isA<VitalOuterClass.GCS>() -> GCS(it.unpack())
            it.isA<VitalOuterClass.Pain>() -> Pain(it.unpack())
            it.isA<VitalOuterClass.EtCO2>() -> EtCO2(it.unpack())
            it.isA<VitalOuterClass.Temp>() -> Temp(it.unpack())
            it.isA<IBP>() -> InvasiveBloodPressure(it.unpack())
            it.isA<VitalOuterClass.Output>() -> Output(it.unpack())
            it.isA<CapillaryRefill>() -> CapRefill(it.unpack())
            it.isA<VitalOuterClass.PIP>() -> PIP(it.unpack())
            else -> null
        }
    }
    return vitals
}

//region Log Vitals Command

fun buildLogVitalCommand(timestamp: Instant?, vitalId: EncounterVitalId = DomainId.nil(), func: VitalKt.Dsl.() -> Unit) = logVitalCommand{
    timestamp?.let{ this.timestamp = it.epochSecond }
    this.vital = vital(func)
    this.vitalId = vitalId.toByteString()
}
fun logVitalsCommandHandler(vitals: Vitals) = handlerWithId<LogVitalCommand>{ docId, command ->
    val encounterVital = this.vital.toEncounterVital(vitalId.toDomainIdOrElse(command.commandId), docId, Instant.ofEpochSecond(timestamp))
    vitals += encounterVital.withoutEmpties()
}
fun logVitalsEventCommandHandler() = eventHandler<LogVitalCommand>(
    KnownEventTypes.VITALS.dataString,
    timestampGenerator = { Instant.ofEpochSecond(timestamp)} ) { fullCommand ->
    val encounterVital = this.vital.toEncounterVital(fullCommand.commandId.toDomainId(), DomainId.nil(), Instant.ofEpochSecond(timestamp))
    "Logged Vital: " + encounterVital.toEventString()
}

//endregion

//region Update Vitals Command

fun buildUpdateVitalCommand(vitalId: EncounterVitalId, timestamp: Instant?, func: VitalKt.Dsl.() -> Unit) = updateVitalCommand{
    this.vitalId = vitalId.toByteString()
    timestamp?.let {this.timestamp = it.epochSecond }
    this.vital = vital(func)
}
fun updateVitalsCommandHandler(vitals: Vitals) = handlerWithId<UpdateVitalCommand>{ docId, _ ->
    var encounterVital = this.vital.toEncounterVital(vitalId.toDomainId(), docId, Instant.ofEpochSecond(timestamp))
    encounterVital = vitals[vitalId.toDomainId()]?.update(encounterVital) ?: encounterVital
    encounterVital.timestamp = Instant.ofEpochSecond(timestamp)
    vitals += encounterVital.withoutEmpties()
}
fun updateVitalEventCommandHandler() = eventHandler<UpdateVitalCommand>(KnownEventTypes.VITALS.dataString){
    val encounterVital = this.vital.toEncounterVital(vitalId.toDomainId(), DomainId.nil(), Instant.ofEpochSecond(timestamp))
    "Updated Vital ${encounterVital.vitalId.toHexString()}: " + encounterVital.toEventString(true)
}
fun updateVitalEventCommandHandler(vitals: Vitals) = eventHandler<UpdateVitalCommand>(
    KnownEventTypes.VITALS.dataString,
    referenceId = { vitalId.toDomainId() },
    timestampGenerator = { Instant.ofEpochSecond(timestamp) },
    handler = {
        var encounterVital = this.vital.toEncounterVital(it.commandId.toDomainId(), DomainId.nil(), Instant.ofEpochSecond(timestamp))
        encounterVital = vitals[vitalId.toDomainId()]?.update(encounterVital) ?: encounterVital
        "Updated Vital: " + encounterVital.toEventString(true)
    }
)

//endregion

//region Remove Vitals Command

fun buildRemoveVitalCommand(vitalId: EncounterVitalId) = removeVitalCommand {
    this.vitalId = vitalId.toByteString()
}
fun removeVitalCommandHandler(vitals: Vitals) = handler<RemoveVitalCommand> {
    vitals.removeItem(vitalId.toDomainId(), Instant.ofEpochSecond(it.timestamp))
}
fun removeVitalEventCommandHandler(vitals: Vitals) = eventHandler<RemoveVitalCommand>(
    KnownEventTypes.VITALS.dataString,
    referenceId = { vitalId.toDomainId()}
) {
    var encounterVital = vitals[vitalId.toDomainId()] ?: return@eventHandler null
    "Removed Vital: " + encounterVital.toEventString(true)
}

//endregion