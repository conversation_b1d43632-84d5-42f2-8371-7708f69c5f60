package gov.afrl.batdok.encounter.observation

import gov.afrl.batdok.commands.proto.*
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

class CasualtyPPEData(val ppeList: List<String> = listOf()): ObservationData {
    internal constructor(data: Observations.CasualtyPPEData): this(data.ppeOrNull?.valueList?.mapNotNull { Type.fromProto(it) }?: listOf())

    enum class Type(override val dataString: String, override val protoIndex: Int): ProtoEnum {
        HELMET_BALLISTIC("Helmet, Ballistic", 1),
        TACTICAL_VEST_IOTV("Tactical Vest (IOTV)", 2),
        EYE_PROTECTION("Eye Protection", 3),
        EAR_PROTECTION("Ear Protection", 4),
        PLATE_FRONT("Plate Front", 5),
        PLATE_BACK("Plate Back", 6),
        PLATE_RIGHT("Plate Right Side", 7),
        PLATE_LEFT("Plate Left Side", 8),
        NECK_PROTECTOR_BACK("Neck Protector (Back)", 9),
        THROAT_PROTECTOR_FRONT("Throat Protector (Front)", 10),
        DELTOID_RIGHT("Deltoid Right", 11),
        DELTOID_LEFT("Deltoid Left", 12),
        GROIN_SHIELD("Groin Shield", 13),
        PELVIC_UNDERGARMENT_1("Pelvic Undergarment Tier 1", 14),
        PELVIC_UNDERGARMENT_2("Pelvic Undergarment Tier 2", 15),
        BLAST_GAUGE("Blast Gauge", 16),
        BLAST_SENSOR_HELMET("Blast Sensor Helmet", 17),
        BLAST_SENSOR_OTHER("Blast Sensor Other", 18),
        ;
        companion object {
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = Type.values().protoToString(enum)
            fun fromString(string: String?) = Type.values().stringToProto(string)
        }
    }

    override fun toProtobuf() = casualtyPPEData {
        this.ppe = nullableEnumList { value.addAll(<EMAIL> { Type.fromString(it) }) }
    }

    override fun toDetailString(): String {
        return ppeList.joinToString(", ")
    }
}