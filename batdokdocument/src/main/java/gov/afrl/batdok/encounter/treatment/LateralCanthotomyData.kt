package gov.afrl.batdok.encounter.treatment

import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.TreatmentCommands
import gov.afrl.batdok.commands.proto.lateralCanthotomy
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

class LateralCanthotomyData(val location: String? = null): TreatmentData{
    override fun toProtobuf(): Message = lateralCanthotomy{
        location = Location.fromString(<EMAIL>)
    }

    override fun toDetailString(): String {
        return location ?: ""
    }

    companion object{
        fun fromProtobuf(lateralCanthotomy: TreatmentCommands.LateralCanthotomy): LateralCanthotomyData{
            return LateralCanthotomyData(Location.fromProto(lateralCanthotomy.location)?:"")
        }
    }

    enum class Location(override val protoIndex: Int, override val dataString: String): ProtoEnum {
        LEFT(1, "Left side"),
        RIGHT(2, "Right side"),
        ;

        companion object{
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }
}