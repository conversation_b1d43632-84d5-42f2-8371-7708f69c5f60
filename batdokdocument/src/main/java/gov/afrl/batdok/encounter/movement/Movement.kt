package gov.afrl.batdok.encounter.movement

import gov.afrl.batdok.commands.CommandHandler
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.PhoneNumbers
import gov.afrl.batdok.encounter.Physician
import gov.afrl.batdok.encounter.commands.*
import java.io.Serializable
import java.time.Instant
import java.time.LocalDate

class Movement : Serializable {
    var medMissionNumber: MedMissionNumber? = null; internal set
    var tailToTail: Boolean? = null; internal set
    var legNumber: Int? = null; internal set
    var totalLegs: Int? = null; internal set
    var ninelineTime: Instant? = null; internal set
    var dispatchEvac: String? = null; internal set
    var assessedEvac: String? = null; internal set
    var pickupLocation: NinelineLocation? = null; internal set
    var dropoffLocation: NinelineLocation? = null; internal set
    var capability: List<String> = listOf(); internal set
    var ninelinePlatform: String? = null; internal set
    var originatingMtf: String? = null; internal set
    var destinationMtf: String? = null; internal set
    var reasonRegulated: String? = null; internal set
    @Deprecated(
        message = "Use maxNumOfStops instead.",
        replaceWith = ReplaceWith("maxNumOfStops")
    )
    var maxStops: Int? = null; internal set
    var maxNumOfStops: String? = null; internal set
    @Deprecated(
        message = "Use maxNumOfRons instead.",
        replaceWith = ReplaceWith("maxNumOfRons")
    )
    var maxRons: Int? = null; internal set
    var maxNumOfRons: String? = null; internal set
    var altitudeRestrictions: String? = null; internal set
    var flightLevel: String? = null; internal set
    @Deprecated(
        message = "Use localReadyDate instead",
        replaceWith = ReplaceWith("localReadyDate")
    )
    var readyDate: Instant? = null; internal set
    var localReadyDate: LocalDate? = null; internal set
    var medicalAttendantsNeeded: Int? = null; internal set
    var nonMedicalAttendantsNeeded: Int? = null; internal set
    var attendants: List<Attendant> = listOf(); internal set
    var classification: String? = null; internal set
    var precedence: String? = null; internal set
    var criticalCare: Boolean? = null; internal set

    fun getMedicalAttendants() = attendants.count {it.isMedical == true}
    fun getNonMedicalAttendants() = attendants.count {it.isMedical == false}
    fun clearPickupLocation() {pickupLocation = null}
    fun clearDropoffLocation() {dropoffLocation = null}
    var acceptingPhysician: Physician = Physician()
    @Deprecated(
        message = "Use originPhoneNumbers instead.",
        replaceWith = ReplaceWith("originPhoneNumbers")
    )
    var originPhone: String? = null
        internal set
    var originPhoneNumbers: PhoneNumbers = PhoneNumbers(); internal set
    @Deprecated(
        message = "Use destinationPhoneNumbers instead.",
        replaceWith = ReplaceWith("destinationPhoneNumbers")
    )
    var destinationPhone: String? = null
        internal set
    var destinationPhoneNumbers: PhoneNumbers = PhoneNumbers(); internal set
    @Deprecated(
        message = "Use waiversText instead.",
        replaceWith = ReplaceWith("waiversText")
    )
    var waivers: List<String> = listOf()
    var waiversText: String? = null
        internal set

    var ambulatoryOrLitter: String? = null

    @Transient val handlers = CommandHandler().apply {
        val movement = this@Movement
        +changeMedMissionNumberCommandHandler(movement)
        +changeTailToTailCommandHandler(movement)
        +changeLegNumberCommandHandler(movement)
        +changeDispatchEvacCommandHandler(movement)
        +changeAssessedEvacCommandHandler(movement)
        +changeNinelinePlatformCommandHandler(movement)
        +changeCapabilityCommandHandler(movement)
        +changeNinelineLocationCommandHandler(movement)
        +changeNinelineTimeCommandHandler(movement)
        +changeOriginatingMtfCommandHandler(movement)
        +changeDestinationMtfCommandHandler(movement)
        +changeReasonRegulatedCommandHandler(movement)
        +changeMaxStopsCommandHandler(movement)
        +changeMaxNumberOfStopsCommandHandler(movement)
        +changeMaxRonsCommandHandler(movement)
        +changeMaxNumberOfRonsCommandHandler(movement)
        +changeAltitudeRestrictionsCommandHandler(movement)
        +changeFlightLevelCommandHandler(movement)
        +changeReadyDateCommandHandler(movement)
        +changeMedicalAttendantsNeededCommandHandler(movement)
        +changeNonMedicalAttendantsNeededCommandHandler(movement)
        +changeAttendantsCommandHandler(movement)
        +changeClassificationCommandHandler(movement)
        +changePrecedenceCommandHandler(movement)
        +changeCriticalCareCommandHandler(movement)
        +changeAcceptingPhysicianCommandHandler(movement)
        +changeOriginPhoneCommandHandler(movement)
        +changeDestinationPhoneCommandHandler(movement)
        +changeOriginPhoneNumbersCommandHandler(movement)
        +changeDestinationPhoneNumbersCommandHandler(movement)
        +addRemoveWaiversCommandHandler(movement)
        +changeWaiversCommandHandler(movement)
        +changeAmbulatoryOrLitterCommandHandler(movement)
        +clearPickupLocationCommandHandler(movement)
        +clearDropoffLocationCommandHandler(movement)
    }

    companion object {
        fun EventCommandHandler.includeMovementEvents(){
            +changeMedMissionNumberEventHandler()
            +changeTailToTailEventHandler()
            +changeLegNumberEventHandler()
            +changeNineLineTimeEventHandler()
            +changeDispatchEvacEventHandler()
            +changeAssessedEvacEventHandler()
            +changeNinelineLocationEventHandler()
            +changeCapabilityEventHandler()
            +changeNinelinePlatformEventHandler()
            +changeOriginatingMtfEventHandler()
            +changeDestinationMtfEventHandler()
            +changeReasonRegulatedEventHandler()
            +changeMaxStopsEventHandler()
            +changeMaxNumberOfStopsEventHandler()
            +changeMaxRonsEventHandler()
            +changeMaxNumberOfRonsEventHandler()
            +changeAltitudeRestrictionsEventHandler()
            +changeFlightLevelEventHandler()
            +changeReadyDateEventHandler()
            +changeMedicalAttendantsNeededEventHandler()
            +changeNonMedicalAttendantsNeededEventHandler()
            +changeAttendantsEventHandler()
            +changeClassificationEventHandler()
            +changePrecedenceEventHandler()
            +changeCriticalCareEventHandler()
            +changeAcceptingPhysicianCommandEventHandler()
            +changeOriginPhoneCommandEventHandler()
            +changeDestinationPhoneCommandEventHandler()
            +changeOriginPhoneNumbersCommandEventHandler()
            +changeDestinationPhoneNumbersCommandEventHandler()
            +addRemoveWaiversEventHandler()
            +changeWaiversCommandEventHandler()
            +changeAmbulatoryOrLitterEventHandler()
            +clearPickupLocationEventHandler()
            +clearDropoffLocationEventHandler()
        }
    }
}