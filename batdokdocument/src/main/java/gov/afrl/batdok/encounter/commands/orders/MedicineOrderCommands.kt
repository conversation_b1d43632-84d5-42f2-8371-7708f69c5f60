package gov.afrl.batdok.encounter.commands.orders

import gov.afrl.batdok.commands.eventHandler
import gov.afrl.batdok.commands.handlerWithId
import gov.afrl.batdok.commands.proto.*
import gov.afrl.batdok.encounter.Interval
import gov.afrl.batdok.encounter.KnownEventTypes
import gov.afrl.batdok.encounter.ids.MedicineId
import gov.afrl.batdok.encounter.ids.OrderLineId
import gov.afrl.batdok.encounter.ids.toByteString
import gov.afrl.batdok.encounter.medicine.KnownMedTypes
import gov.afrl.batdok.encounter.medicine.KnownRoutes
import gov.afrl.batdok.encounter.medicine.KnownUnits
import gov.afrl.batdok.encounter.metadata.Signature
import gov.afrl.batdok.encounter.orders.*
import gov.afrl.batdok.util.*
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant

fun buildAddMedicineOrderLineCommand(orderLine: MedicineOrderLine) = addMedicineOrderLine {
    this.addOrderLine = addOrderLine {
        this.orderLineId = orderLine.id.toByteString()
        this.orderStatus = OrderStatus.fromString(orderLine.orderStatus)
        this.frequency = orderLine.frequency.toString()
        this.instructions = orderLine.instructions
        this.title = orderLine.title
        this.lastOccurrence = nullableInt64Value(orderLine.lastOccurrence?.epochSecond)
        if (orderLine.provider != null) {
            this.provider = orderLine.provider.toProto()
        }
        if (orderLine.signature != null) {
            this.signature = orderLine.signature.toProto()
        }
        this.timestamp = orderLine.timestamp.epochSecond
        this.type = OrderType.fromString(orderLine.orderType)
    }
    this.medicine = medicine {
        this.ndc = nullableStringValue(orderLine.orderedMedicine.ndc)
        this.name = stringValue(orderLine.orderedMedicine.name)
        this.medId = orderLine.orderedMedicine.medId.toByteString()
        this.route = KnownRoutes.fromString(orderLine.orderedMedicine.route)
        this.volume = nullableFloatValue(orderLine.orderedMedicine.volume)
        this.unit = KnownUnits.fromString(orderLine.orderedMedicine.unit)
        this.serialNumber = nullableStringValue(orderLine.orderedMedicine.serialNumber)
        this.expirationDate = nullableStringValue(orderLine.orderedMedicine.expirationDate)
        this.type = KnownMedTypes.fromString(orderLine.orderedMedicine.expirationDate)
        this.exportName = nullableStringValue(orderLine.orderedMedicine.exportName)
    }
}

fun addMedicineOrderLineCommandHandler(orders: Orders) =
    handlerWithId<MedicineOrderCommands.AddMedicineOrderLine> { _, _ ->
        orders += this.toMedicineOrderLine()
    }

fun addMedicineOrderLineCommandEventHandler() =
    eventHandler<MedicineOrderCommands.AddMedicineOrderLine>(KnownEventTypes.ORDERS.dataString) { _ ->
        val orderLine = this.toMedicineOrderLine()
        val description = listOfNotNull(
            "Title: ${orderLine.title}",
            "Instructions: ${orderLine.instructions}",
            "Frequency: ${orderLine.frequency}",
            orderLine.lastOccurrence?.let { "Last Administration: ${it.format(Patterns.mdhm_24_space_comma_colon)}" },
            orderLine.provider?.name?.let { "Provider: $it" }
        ).joinToString("\n")
        "Added ${orderLine.orderType} Order:\n$description"
    }

private fun MedicineOrderCommands.AddMedicineOrderLine.toMedicineOrderLine() = MedicineOrderLine(
    id = DomainId.create(addOrderLine.orderLineId.toByteArray()),
    orderStatus = OrderStatus.fromProto(addOrderLine.orderStatus),
    frequency = Interval(addOrderLine.frequency),
    instructions = addOrderLine.instructions,
    lastOccurrence = addOrderLine.lastOccurrence.valueOrNull?.let { Instant.ofEpochSecond(it.value) },
    provider = addOrderLine.providerOrNull?.let { gov.afrl.batdok.encounter.Contact(it) },
    signature = addOrderLine.signatureOrNull?.let { Signature(it) },
    timestamp = Instant.ofEpochSecond(addOrderLine.timestamp),
    orderType = OrderType.fromProto(addOrderLine.type),
    orderedMedicine = OrderedMedicine(
        name = medicine.name.value,
        ndc = medicine.ndc.valueOrNull?.value,
        rxcui = medicine.rxcui.valueOrNull?.value,
        medId = DomainId.create<MedicineId>(medicine.medId.toByteArray()),
        route = KnownRoutes.fromProto(medicine.route),
        volume = medicine.volume.valueOrNull?.value,
        unit = KnownUnits.fromProto(medicine.unit),
        serialNumber = medicine.serialNumber.valueOrNull?.value,
        expirationDate = medicine.expirationDate.valueOrNull?.value,
        type = KnownMedTypes.fromProto(medicine.type),
        exportName = medicine.exportName.valueOrNull?.value
    )
)

fun buildUpdateMedicineOrderLineCommand(
    id: OrderLineId,
    timestamp: Instant,
    instructions: String,
    frequency: Interval,
    lastOccurrence: Instant? = null,
    provider: gov.afrl.batdok.encounter.Contact? = null,
    signature: Signature? = null,
    medicine: OrderedMedicine
) = updateMedicineOrderLine {
    this.updateOrderLine = updateOrderLine {
        this.orderLineId = id.toByteString()
        this.frequency = frequency.toString()
        this.instructions = instructions
        this.lastOccurrence = nullableInt64Value(lastOccurrence?.epochSecond)
        if (provider != null) {
            this.provider = provider.toProto()
        }
        if (signature != null) {
            this.signature = signature.toProto()
        }
        this.timestamp = timestamp.epochSecond
    }
    this.medicine = medicine {
        this.ndc = nullableStringValue(medicine.ndc)
        this.name = stringValue(medicine.name)
        this.medId = medicine.medId.toByteString()
        this.route = KnownRoutes.fromString(medicine.route)
        this.volume = nullableFloatValue(medicine.volume)
        this.unit = KnownUnits.fromString(medicine.unit)
        this.serialNumber = nullableStringValue(medicine.serialNumber)
        this.expirationDate = nullableStringValue(medicine.expirationDate)
        this.type = KnownMedTypes.fromString(medicine.expirationDate)
        this.exportName = nullableStringValue(medicine.exportName)
    }
}

fun updateMedicineOrderLineCommandHandler(orders: Orders) =
    handlerWithId<MedicineOrderCommands.UpdateMedicineOrderLine> { _, _ ->
        val id = DomainId.create<OrderLineId>(updateOrderLine.orderLineId.toByteArray())
        val oldOrderLine = orders[id]
        if (oldOrderLine != null) {
            orders += MedicineOrderLine(
                id = id,
                orderStatus = oldOrderLine.orderStatus,
                frequency = Interval(updateOrderLine.frequency),
                instructions = updateOrderLine.instructions,
                lastOccurrence = updateOrderLine.lastOccurrence.valueOrNull?.let { Instant.ofEpochSecond(it.value) },
                provider = updateOrderLine.providerOrNull?.let { gov.afrl.batdok.encounter.Contact(it) },
                signature = updateOrderLine.signatureOrNull?.let { Signature(it) },
                timestamp = Instant.ofEpochSecond(updateOrderLine.timestamp),
                orderType = oldOrderLine.orderType,
                orderedMedicine = OrderedMedicine(
                    name = medicine.name.value,
                    ndc = medicine.ndc.valueOrNull?.value,
                    rxcui = medicine.rxcui.valueOrNull?.value,
                    medId = DomainId.create<MedicineId>(medicine.medId.toByteArray()),
                    route = KnownRoutes.fromProto(medicine.route),
                    volume = medicine.volume.valueOrNull?.value,
                    unit = KnownUnits.fromProto(medicine.unit),
                    serialNumber = medicine.serialNumber.valueOrNull?.value,
                    expirationDate = medicine.expirationDate.valueOrNull?.value,
                    type = KnownMedTypes.fromProto(medicine.type),
                    exportName = medicine.exportName.valueOrNull?.value
                )
            )
        }
    }

fun updateMedicineOrderLineCommandEventHandler(orders: Orders) =
    eventHandler<MedicineOrderCommands.UpdateMedicineOrderLine>(KnownEventTypes.ORDERS.dataString)
    { _ ->
        val id = DomainId.create<OrderLineId>(updateOrderLine.orderLineId.toByteArray())
        val orderLine = orders[id]
        if (orderLine != null) {
            orderLine as MedicineOrderLine
            val lastOccurrence = this.updateOrderLine.lastOccurrence.valueOrNull?.let {
                Instant.ofEpochSecond(it.value).format(Patterns.mdhm_24_space_comma_colon)
            }
            val medicine = OrderedMedicine(
                name = medicine.name.value,
                ndc = medicine.ndc.valueOrNull?.value,
                rxcui = medicine.rxcui.valueOrNull?.value,
                medId = DomainId.create<MedicineId>(medicine.medId.toByteArray()),
                route = KnownRoutes.fromProto(medicine.route),
                volume = medicine.volume.valueOrNull?.value,
                unit = KnownUnits.fromProto(medicine.unit),
                serialNumber = medicine.serialNumber.valueOrNull?.value,
                expirationDate = medicine.expirationDate.valueOrNull?.value,
                type = KnownMedTypes.fromProto(medicine.type),
                exportName = medicine.exportName.valueOrNull?.value
            )
            val description = listOfNotNull(
                "Title: ${medicine.title}",
                "Instructions: ${this.updateOrderLine.instructions}",
                "Frequency: ${this.updateOrderLine.frequency}",
                lastOccurrence?.let { "Last Administration: $it" },
                this.updateOrderLine.provider?.name?.let { if (it.value.isBlank()) null else "Provider: ${it.value}" }
            ).joinToString("\n")
            "Updated ${orderLine.orderType} Order:\n$description"
        } else {
            null
        }
    }

// update medicine order line status is identical to update order line status
// so we can reuse the existing protobuf objects
fun buildUpdateMedicineOrderLineStatusCommand(
    id: OrderLineId,
    orderStatus: OrderStatus
) = updateMedicineOrderLineStatus {
    this.orderLineId = id.toByteString()
    this.orderStatus = orderStatus.toProto()
}

// update medicine order line status is identical to update order line status
// so we can reuse the existing protobuf objects
fun updateMedicineOrderLineStatusCommandHandler(orders: Orders) =
    handlerWithId<MedicineOrderCommands.UpdateMedicineOrderLineStatus> { _, _ ->
        val id = DomainId.create<OrderLineId>(orderLineId.toByteArray())
        val oldOrderLine = orders[id]
        if (oldOrderLine is MedicineOrderLine) {
            orders += MedicineOrderLine(
                id = id,
                orderStatus = OrderStatus.fromProto(orderStatus),
                frequency = oldOrderLine.frequency,
                instructions = oldOrderLine.instructions,
                lastOccurrence = oldOrderLine.lastOccurrence,
                provider = oldOrderLine.provider,
                signature = oldOrderLine.signature,
                timestamp = oldOrderLine.timestamp,
                orderType = oldOrderLine.orderType,
                orderedMedicine = oldOrderLine.orderedMedicine
            )
        }
    }

// update medicine order line status is identical to update order line status
// so we can reuse the existing protobuf objects
fun updateMedicineOrderLineStatusCommandEventHandler(orders: Orders) =
    eventHandler<MedicineOrderCommands.UpdateMedicineOrderLineStatus>(KnownEventTypes.ORDERS.dataString)
    { _ ->
        val id = DomainId.create<OrderLineId>(orderLineId.toByteArray())
        val orderLine = orders[id]
        if (orderLine != null) {
            "Updated ${orderLine.orderType} Order status from:\n${orderLine.orderStatus} to ${
                OrderStatus.fromProto(
                    this.orderStatus
                )
            }"
        } else {
            null
        }
    }