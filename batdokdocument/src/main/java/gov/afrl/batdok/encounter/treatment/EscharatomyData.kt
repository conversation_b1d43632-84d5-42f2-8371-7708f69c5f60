package gov.afrl.batdok.encounter.treatment

import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.TreatmentCommands
import gov.afrl.batdok.commands.proto.escharatomy
import gov.afrl.batdok.util.compatibleEnum
import gov.afrl.batdok.util.toPrimitive

class EscharatomyData(val location: String?): TreatmentData{
    override fun toProtobuf(): Message = escharatomy {
        this.location = compatibleEnum(<EMAIL>)
    }

    override fun toDetailString(): String {
        return location ?: ""
    }

    companion object{
        fun fromProtobuf(escharatomy: TreatmentCommands.Escharatomy) = EscharatomyData(
            escharatomy.location.toPrimitive(),
        )
    }
}