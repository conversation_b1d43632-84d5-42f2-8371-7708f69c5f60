package gov.afrl.batdok.encounter.observation

import gov.afrl.batdok.commands.proto.Observations
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.respEffortExtras
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

class RespEffortData(val effort: String?): ObservationData {
    internal constructor(data: Observations.RespEffortExtras): this(Effort.fromProto(data.effort))

    enum class Effort(override val protoIndex: Int, override val dataString: String): ProtoEnum {
        UNLABORED(1, "Unlabored"),
        LABORED(2, "Labored"),
        SPONTANEOUS(3, "Spontaneous"),
        AGONAL(4, "Agonal"),
        ASSISTED(5, "Assisted"),
        BVM(6, "Assisted with BVM"),
        ;

        companion object {
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = RespSideData.Type.values().protoToString(enum)
            fun fromString(string: String?) = RespSideData.Type.values().stringToProto(string)
        }
    }

    override fun toDetailString(): String {
        return effort ?: ""
    }

    override fun toProtobuf() = respEffortExtras {
        this.effort = Effort.fromString(<EMAIL>)
    }
}