package gov.afrl.batdok.encounter.panel

/**
 * Replacement for [gov.afrl.batdok.encounter.IndividualLab]
 * Holds individual lab data.
 */
data class LabEntry(val name: String, val value: String?, val unit: String? = null) {
    constructor(name: KnownLabs, value: String?, unit: String? = null): this(name.dataString, value, unit)

    override fun toString(): String {
        return "$name: $value${" $unit".takeUnless { unit.isNullOrBlank() }?:""}"
    }
}