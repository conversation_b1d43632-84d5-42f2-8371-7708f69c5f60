package gov.afrl.batdok.encounter.observation

import gov.afrl.batdok.commands.proto.Observations
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.chestRiseFallData
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

class ChestRiseFallData(val riseOrFall: String?): ObservationData {
    internal constructor(data: Observations.ChestRiseFallData): this(Type.fromProto(data.riseFall))

    enum class Type(override val protoIndex: Int, override val dataString: String): ProtoEnum{
        L_R(1, "L > R"),
        SYMMETRICAL(2, "Symmetrical"),
        R_L(3, "R > L"),;

        companion object {
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = Type.values().protoToString(enum)
            fun fromString(string: String?) = Type.values().stringToProto(string)
        }
    }

    override fun toDetailString(): String {
        return riseOrFall ?: ""
    }

    override fun toProtobuf() = chestRiseFallData {
        this.riseFall = Type.fromString(<EMAIL>)
    }
}