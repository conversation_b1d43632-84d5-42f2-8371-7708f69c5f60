package gov.afrl.batdok.encounter.movement

import gov.afrl.batdok.commands.proto.SharedProtobufObjects.CompatibleEnum
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto
import gov.afrl.batdok.util.toChangeOrClearEvent

enum class EvacStatus(
    override val protoIndex: Int,
    override val dataString: String,
): ProtoEnum {
    URGENT(1, "Urgent"),
    URGENTSURGICAL(2, "Urgent Surgical"),
    PRIORITY(3, "Priority"),
    ROUTINE(4, "Routine"),
    CONVENIENCE(5, "Convenience");

    companion object {
        fun fromProto(enum: CompatibleEnum) = EvacStatus.values().protoToString(enum)
        fun fromString(string: String?) = EvacStatus.values().stringToProto(string)
        fun toEvent(enum: CompatibleEnum, type: String) =
            EvacStatus.values().toChangeOrClearEvent(enum, type)
    }
}