package gov.afrl.batdok.encounter

import mil.af.afrl.batman.batdokid.DomainId
import java.io.Serializable

interface Linkable: Serializable {
    val id: DomainId
    @Deprecated("Use links.getLinksContainingThis instead in the object itself")
    fun getLinks(links: Links) : List<DomainId> = with(links){
        return getLinksContainingThis().flatMap { it.ids }.distinct()
    }
    @Deprecated("Use links.getLinksContainingThis instead in the object itself")
    fun getLinkedItems(doc: Document) : List<Linkable> {
        return doc.getLinkableObjects(id)
    }
}
