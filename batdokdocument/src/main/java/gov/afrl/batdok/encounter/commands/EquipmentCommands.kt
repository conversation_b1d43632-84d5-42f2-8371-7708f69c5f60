package gov.afrl.batdok.encounter.commands

import gov.afrl.batdok.commands.eventHandler
import gov.afrl.batdok.commands.handler
import gov.afrl.batdok.commands.proto.*
import gov.afrl.batdok.commands.proto.EquipmentCommands.*
import gov.afrl.batdok.encounter.ids.EquipmentId
import gov.afrl.batdok.encounter.ids.toByteString
import gov.afrl.batdok.encounter.ids.toDomainId
import gov.afrl.batdok.encounter.metadata.Equipment
import gov.afrl.batdok.encounter.metadata.EquipmentLog
import gov.afrl.batdok.encounter.metadata.Metadata
import gov.afrl.batdok.util.int64Value

fun addRemoveEquipmentCommandHandler(metadata: Metadata) = handler<AddRemoveEquipmentCommand> {
    metadata.equipment += addedItemsList.mapNotNull { Equipment.fromProto(it) }
    metadata.equipment -= removedItemsList.mapNotNull { Equipment.fromProto(it) }
}

fun addEquipmentCommandHandler(metadata: Metadata) = handler<AddEquipmentCommand> {
    var equipment = Equipment.fromProto(addedItem)
    if(equipment != null){
        metadata.equipment += equipment
    }
}

fun updateEquipmentCommandHandler(metadata: Metadata) = handler<UpdateEquipmentCommand> {
    var equipmentToRemove = metadata.equipment.filter{ it.id == id.toDomainId() }
    metadata.equipment -= equipmentToRemove
    var updatedEquipment = Equipment.fromProto(item)
    if(updatedEquipment != null){
        metadata.equipment +=  updatedEquipment
    }

}

fun removeEquipmentCommandHandler(metadata: Metadata) = handler<RemoveEquipmentCommand> {
    var equipment = Equipment.fromProto(this.item)
    if(equipment != null){
        metadata.equipment -= metadata.equipment.filter { it.id == equipment.id }
    }

}


fun buildUpdateEquipmentCommand(equipmentToUpdate: Equipment) = updateEquipmentCommand{
    this.timestamp = int64Value(equipmentToUpdate.timestamp.epochSecond)
    this.id = equipmentToUpdate.id.toByteString()
    this.item = equipmentToUpdate.toProto()

}


fun buildAddEquipmentCommand(equipment: Equipment) = addEquipmentCommand{
    addedItem = equipment.toProto()
}

fun buildRemoveEquipmentCommand(equipment: Equipment, documentationError: Boolean) = removeEquipmentCommand {
   this.item= equipment.toProto()
    this.documentationError = documentationError
}

fun addEquipmentEventHandler() = eventHandler<AddEquipmentCommand>{
    "Added equipment: " +  Equipment.fromProto(this.addedItem)
}

fun removeEquipmentEventHandler() = eventHandler<RemoveEquipmentCommand>{

    var documentationErrorString = ""
    if(documentationError){
        documentationErrorString = " due to documentation error"
    }
    "Removed equipment: " + Equipment.fromProto(this.item)+ documentationErrorString
}

fun modifyEquipimentEventHandler() = eventHandler<UpdateEquipmentCommand>{
    "Modified equipment: " + Equipment.fromProto(this.item)
}



// region deprecated equipment log
@Deprecated("Equipment log is deprecated, use Metadata.equipment")
fun buildAddRemoveEquipmentCommands(
    addedEquipment: List<Equipment>,
    removedEquipment: List<Equipment>
) = addRemoveEquipmentCommand {
    this.addedItems.addAll(addedEquipment.map { it.toProto() })
    this.removedItems.addAll(removedEquipment.map { it.toProto() })
}

@Deprecated("Equipment log is deprecated, use Metadata.equipment")
fun addRemoveEquipmentCommandHandler(equipmentLog: EquipmentLog) =
    handler<AddRemoveEquipmentCommand> {
        equipmentLog.used += addedItemsList.mapNotNull { Equipment.fromProto(it) }
        equipmentLog.used -= removedItemsList.mapNotNull { Equipment.fromProto(it) }
    }

@Deprecated("Equipment log is deprecated, use Metadata.equipment")
fun addRemoveEquipmentEventCommandHandler() = eventHandler<AddRemoveEquipmentCommand> {
    fun List<EquipmentItem>.toEventString() =
        mapNotNull { Equipment.fromProto(it).toString() }.joinToString(", ")

    val added = "Added equipment: ${addedItemsList.toEventString()}".takeIf { addedItemsCount > 0 }
    val removed =
        "Removed equipment: ${removedItemsList.toEventString()}".takeIf { removedItemsCount > 0 }
    listOfNotNull(added, removed).joinToString("\n")
}

@Deprecated("Equipment log is deprecated, use Metadata.equipment")
fun buildAddRemovePowerLossCommands(
    addedEquipment: List<Equipment>,
    removedEquipment: List<Equipment>
) = addRemovePowerLossCommand {
    this.addedItems.addAll(addedEquipment.map { it.toProto() })
    this.removedItems.addAll(removedEquipment.map { it.toProto() })
}

@Deprecated("Equipment log is deprecated, use Metadata.equipment")
fun addRemovePowerLossCommandHandler(equipmentLog: EquipmentLog) =
    handler<AddRemovePowerLossCommand> {
        equipmentLog.powerLoss += addedItemsList.mapNotNull { Equipment.fromProto(it) }
        equipmentLog.powerLoss -= removedItemsList.mapNotNull { Equipment.fromProto(it) }
    }

@Deprecated("Equipment log is deprecated, use Metadata.equipment")
fun addRemovePowerLossEventCommandHandler() = eventHandler<AddRemovePowerLossCommand> {
    fun List<EquipmentItem>.toEventString() =
        mapNotNull { Equipment.fromProto(it).toString() }.joinToString(", ")

    val added =
        "Added equipment power loss: ${addedItemsList.toEventString()}".takeIf { addedItemsCount > 0 }
    val removed =
        "Removed equipment power loss: ${removedItemsList.toEventString()}".takeIf { removedItemsCount > 0 }
    listOfNotNull(added, removed).joinToString("\n")
}

@Deprecated("Equipment log is deprecated, use Metadata.equipment")
fun buildAddRemoveEquipmentFailureCommands(
    addedEquipment: List<Equipment>,
    removedEquipment: List<Equipment>
) = addRemoveEqFailureCommand {
    this.addedItems.addAll(addedEquipment.map { it.toProto() })
    this.removedItems.addAll(removedEquipment.map { it.toProto() })
}

@Deprecated("Equipment log is deprecated, use Metadata.equipment")
fun addRemoveEquipmentFailureCommandHandler(equipmentLog: EquipmentLog) =
    handler<AddRemoveEqFailureCommand> {
        equipmentLog.equipmentFailure += addedItemsList.mapNotNull { Equipment.fromProto(it) }
        equipmentLog.equipmentFailure -= removedItemsList.mapNotNull { Equipment.fromProto(it) }
    }

@Deprecated("Equipment log is deprecated, use Metadata.equipment")
fun addRemoveEquipmentFailureEventCommandHandler() = eventHandler<AddRemoveEqFailureCommand> {
    fun List<EquipmentItem>.toEventString() =
        mapNotNull { Equipment.fromProto(it).toString() }.joinToString(", ")

    val added =
        "Added equipment failure: ${addedItemsList.toEventString()}".takeIf { addedItemsCount > 0 }
    val removed =
        "Removed equipment failure: ${removedItemsList.toEventString()}".takeIf { removedItemsCount > 0 }
    listOfNotNull(added, removed).joinToString("\n")
}

@Deprecated("Equipment log is deprecated, use Metadata.equipment")
fun buildAddRemoveUserErrorCommands(
    addedEquipment: List<Equipment>,
    removedEquipment: List<Equipment>
) = addRemoveUserErrorCommand {
    this.addedItems.addAll(addedEquipment.map { it.toProto() })
    this.removedItems.addAll(removedEquipment.map { it.toProto() })
}

@Deprecated("Equipment log is deprecated, use Metadata.equipment")
fun addRemoveUserErrorCommandHandler(equipmentLog: EquipmentLog) =
    handler<AddRemoveUserErrorCommand> {
        equipmentLog.userError += addedItemsList.mapNotNull { Equipment.fromProto(it) }
        equipmentLog.userError -= removedItemsList.mapNotNull { Equipment.fromProto(it) }
    }

@Deprecated("Equipment log is deprecated, use Metadata.equipment")
fun addRemoveUserErrorEventCommandHandler() = eventHandler<AddRemoveUserErrorCommand> {
    fun List<EquipmentItem>.toEventString() =
        mapNotNull { Equipment.fromProto(it).toString() }.joinToString(", ")

    val added =
        "Added equipment user error: ${addedItemsList.toEventString()}".takeIf { addedItemsCount > 0 }
    val removed =
        "Removed equipment user error: ${removedItemsList.toEventString()}".takeIf { removedItemsCount > 0 }
    listOfNotNull(added, removed).joinToString("\n")
}
// endregion