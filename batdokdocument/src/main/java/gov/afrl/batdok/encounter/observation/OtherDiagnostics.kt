package gov.afrl.batdok.encounter.observation

import gov.afrl.batdok.commands.proto.Observations
import gov.afrl.batdok.commands.proto.descriptionOrNull
import gov.afrl.batdok.commands.proto.otherDiagnosticsData
import gov.afrl.batdok.util.nullableStringValue
import gov.afrl.batdok.util.toPrimitive

data class OtherDiagnostics(val description: String? = null): ObservationData {
    constructor(proto: Observations.OtherDiagnosticsData): this(proto.descriptionOrNull?.toPrimitive())
    override fun toProtobuf() = otherDiagnosticsData {
        description = nullableStringValue(<EMAIL>)
    }

    override fun toDetailString(): String {
        return this.description ?: ""
    }
}