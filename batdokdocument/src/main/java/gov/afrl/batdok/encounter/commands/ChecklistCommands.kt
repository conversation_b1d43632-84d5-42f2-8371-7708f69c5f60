package gov.afrl.batdok.encounter.commands

import gov.afrl.batdok.commands.eventHandler
import gov.afrl.batdok.commands.handler
import gov.afrl.batdok.commands.proto.ChecklistItem.ChecklistCommand
import gov.afrl.batdok.commands.proto.checklistCommand
import gov.afrl.batdok.encounter.Checklist
import gov.afrl.batdok.encounter.ChecklistItem
import gov.afrl.batdok.encounter.Interval
import gov.afrl.batdok.encounter.KnownChecklistItems
import gov.afrl.batdok.encounter.KnownEventTypes
import gov.afrl.batdok.util.booleanValue
import java.time.Instant

@Deprecated(
    message = "Use buildChecklistCommand with Interval interval",
    replaceWith = ReplaceWith("buildChecklistCommand(checklistItem, checked, Interval(interval))")
)
fun buildChecklistCommand(checklistItem: KnownChecklistItems, checked: Boolean? = null, interval: String = checklistItem.defaultInterval) =
    buildChecklistCommand(checklistItem, checked, Interval(interval))

fun buildChecklistCommand(checklistItem: KnownChecklistItems, checked: Boolean? = null, interval: Interval = Interval(checklistItem.defaultInterval)) = checklistCommand {
    this.checklistItem = KnownChecklistItems.fromString(checklistItem.dataString)
    this.interval = interval.toString()
    checked?.let { this.checked = booleanValue(it) }
}

@Deprecated(
    message = "Use buildChecklistCommand with Interval interval",
    replaceWith = ReplaceWith("buildChecklistCommand(customItem, checked, Interval(interval))")
)
fun buildChecklistCommand(customItem: String, checked: Boolean? = null, interval: String = "Once") =
    buildChecklistCommand(customItem, checked, Interval(interval))

fun buildChecklistCommand(customItem: String, checked: Boolean? = null, interval: Interval = Interval("Once")) = checklistCommand {
    this.checklistItem = KnownChecklistItems.fromString(customItem)
    this.interval = interval.toString()
    checked?.let { this.checked = booleanValue(it) }
}

fun handleChecklistCommand(checklist: Checklist) = handler<ChecklistCommand>{ fullCommand ->
    val message = KnownChecklistItems.fromProto(checklistItem) ?: return@handler
    val existingItem = checklist.checklistItems.find { it.message == message }
    val item = if(hasChecked() || existingItem == null){
        ChecklistItem(
            message,
            Interval(interval),
            timestamp = Instant.ofEpochSecond(fullCommand.timestamp),
            checked = checked.value
        )
    }else{
        //If "checked" field isn't set, that means keep the previous value, just change the interval
        existingItem.copy(interval = interval)
    }
    checklist += item
}

fun handleChecklistEventCommand() = eventHandler<ChecklistCommand>(KnownEventTypes.PCC_CHECKLIST.dataString) {
    KnownChecklistItems.fromProto(checklistItem)
        ?.takeIf { hasChecked() } //If the checklist is not updating the checked state, don't create an event.
        ?.let { itemName ->
            val checkedString = if (checked.value) "Selected" else "Unselected"
            "PCC Checklist: $itemName $checkedString"
        }
}