package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.CommandHandler
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.commands.proto.DocumentCommands.CommandData
import gov.afrl.batdok.commands.proto.DocumentCommands.DocumentCommand
import gov.afrl.batdok.encounter.Checklist.Companion.includeChecklistEvents
import gov.afrl.batdok.encounter.CustomActions.Companion.includeCustomActionHandlers
import gov.afrl.batdok.encounter.Diagnosis.Companion.includeDiagnosisCommands
import gov.afrl.batdok.encounter.History.Companion.includeHistoryEvents
import gov.afrl.batdok.encounter.Info.Companion.includeInfoEvents
import gov.afrl.batdok.encounter.Injuries.Companion.includeInjuryEvents
import gov.afrl.batdok.encounter.InputOutputs.Companion.includeInputOutputEvents
import gov.afrl.batdok.encounter.IntakeOutputs.Companion.includeIntakeOutputEvents
import gov.afrl.batdok.encounter.Labs.Companion.includeLabHandlers
import gov.afrl.batdok.encounter.Links.Companion.includeLinkEvents
import gov.afrl.batdok.encounter.Notes.Companion.includeNoteEvents
import gov.afrl.batdok.encounter.Observations.Companion.includeObservationEvents
import gov.afrl.batdok.encounter.Panels.Companion.includePanelHandlers
import gov.afrl.batdok.encounter.Subjective.Companion.includeSubjectiveEvents
import gov.afrl.batdok.encounter.Treatments.Companion.includeTreatmentEvents
import gov.afrl.batdok.encounter.VentValues.Companion.includeVentEvents
import gov.afrl.batdok.encounter.Vitals.Companion.includeVitalEvents
import gov.afrl.batdok.encounter.ids.*
import gov.afrl.batdok.encounter.medicine.Drips
import gov.afrl.batdok.encounter.medicine.Drips.Companion.includeDripEvents
import gov.afrl.batdok.encounter.medicine.Medicines
import gov.afrl.batdok.encounter.medicine.Medicines.Companion.includeMedicineEvents
import gov.afrl.batdok.encounter.metadata.Metadata
import gov.afrl.batdok.encounter.metadata.Metadata.Companion.includeMetadataEvents
import gov.afrl.batdok.encounter.movement.Movement
import gov.afrl.batdok.encounter.movement.Movement.Companion.includeMovementEvents
import gov.afrl.batdok.encounter.observation.BloodList
import gov.afrl.batdok.encounter.observation.BloodList.Companion.includeBloodEvents
import gov.afrl.batdok.encounter.orders.Orders
import gov.afrl.batdok.encounter.orders.Orders.Companion.includeOrdersEvents
import gov.afrl.batdok.util.isCompatible
import mil.af.afrl.batman.batdokid.DomainId
import java.io.Serializable

class Document(val id: DocumentId = DomainId.create()):Serializable{
    val info: Info = Info()
    val diagnosis: Diagnosis = Diagnosis()
    val injuries: Injuries = Injuries()
    val treatments: Treatments = Treatments()
    val subjective: Subjective = Subjective()
    val history: History = History()
    val events: Events = Events()
    val checklist: Checklist = Checklist()
    val vitals: Vitals = Vitals()
    val ventValues: VentValues = VentValues(vitals)
    @Deprecated("use intakeOutputs instead", replaceWith = ReplaceWith("intakeOutputs"))
    val inputOutputs = InputOutputs()
    val intakeOutputs = IntakeOutputs()
    val labs: Labs = Labs()
    val panels: Panels = Panels()
    val metadata: Metadata = Metadata()
    val drips: Drips = Drips()
    val observations: Observations = Observations()
    val bloodList: BloodList = BloodList()
    val medicines: Medicines = Medicines()
    val movement: Movement = Movement()
    val links: Links = Links()
    val notes: Notes = Notes()
    val customActions: CustomActions = CustomActions()
    val orders: Orders = Orders()
    val historicalHL7Data = HistoricalHL7Data()

    @Transient internal val handlers = CommandHandler().apply {
        include(info.handlers)
        include(diagnosis.handlers)
        include(injuries.handlers)
        include(treatments.handlers)
        include(checklist.handlers)
        include(vitals.handlers)
        include(ventValues.handlers)
        include(subjective.handlers)
        include(history.handlers)
        include(inputOutputs.handlers)
        include(intakeOutputs.handlers)
        include(labs.handlers)
        include(panels.handlers)
        include(metadata.handlers)
        include(observations.handlers)
        include(observations.linkHandlers(links))
        include(bloodList.handlers)
        include(medicines.handlers)
        include(drips.handlers)
        include(events.handlers)
        include(movement.handlers)
        include(links.handlers)
        include(notes.handlers)
        include(customActions.handlers)
        include(orders.handlers)
        include(historicalHL7Data.handlers)
    }
    @Transient private val eventHandlers = EventCommandHandler(events).apply {
        includeInfoEvents(info)
        includeDiagnosisCommands()
        includeInjuryEvents()
        includeTreatmentEvents(treatments)
        includeChecklistEvents()
        includeVitalEvents(vitals)
        includeVentEvents(ventValues, vitals)
        includeSubjectiveEvents(subjective)
        includeHistoryEvents()
        includeInputOutputEvents(inputOutputs)
        includeIntakeOutputEvents(intakeOutputs)
        includeLabHandlers(labs)
        includePanelHandlers(panels)
        includeMetadataEvents(metadata)
        includeObservationEvents(observations)
        includeBloodEvents(bloodList)
        includeMedicineEvents(medicines)
        includeDripEvents()
        includeMovementEvents()
        includeLinkEvents(this@Document)
        includeNoteEvents(notes)
        includeCustomActionHandlers(customActions)
        includeOrdersEvents(orders)
    }

    /**
     * Update an Encounter by performing the passed-in [actions]
     */
    fun handle(actions: List<CommandData>){
        actions.filter { it.isCompatible() }.forEach {
            eventHandlers.handle(id, it)
            handlers.handle(id, it)
        }
    }

    /**
     * Update an Encounter by performing the passed-in [actions].
     *
     * Use this function to pass in historic encounters' commands
     */
    fun handle(multiEncounterCommands: Collection<DocumentCommand>){
        multiEncounterCommands.forEach { documentCommand ->
            documentCommand.commandsList.filter { it.isCompatible() }.forEach {
                eventHandlers.handle(documentCommand.encounterId.toDomainId(), it)
                handlers.handle(documentCommand.encounterId.toDomainId(), it)
            }
        }
    }

    internal fun getItem(id: DomainId): Any? = vitals[id.copy()]
        ?: subjective.complaints[id]
        ?: treatments[id.copy<TreatmentId>()]
        ?: ventValues[id.copy()]
        ?: inputOutputs[id.copy()]
        ?: labs[id.copy<LabId>()]
        ?: panels[id.copy()]
        ?: drips[id.copy()]
        ?: observations[id.copy<ObservationId>()]
        ?: bloodList[id.copy<MedicineId>()]
        ?: medicines[id.copy<MedicineId>()]
        ?: notes[id.copy()]
        ?: customActions[id.copy<CustomActionId>()]
        ?: orders[id.copy()]
        ?: events[id.copy<EventId>()] //events needs to be last because it copies the ids from other classes. This way, you'll get the actual object first, then you can grab from the events directly if that's what you wanted instead

    /**
     * Gets all items linked to the provided id.
     *
     * Returns a map of each item to the description of how it's linked to the provided id
     */
    @Deprecated("Use getLinkedItems instead")
    fun getLinkedObjects(id: DomainId): List<Pair<Any, String>>{
        return links.getLinks(id).flatMap {  link ->
            //For each id in each list, find the corresponding item
            link.ids.mapNotNull { getItem(it) }.map { it to (link.relationship) }
        }
    }
    @Deprecated("Use getLinkedItems instead")
    fun getLinkableObjects(id: DomainId): List<Linkable> {
        return links.getLinks(id).flatMap {  link ->
                link.ids
                    .mapNotNull { getItem(it) }
                    .filterIsInstance<Linkable>()
            }.distinctBy { it.id }
    }

    internal fun Linkable.getLinkedItems(relationshipFilter: CommonLinkRelationships) = getLinkedItems(relationshipFilter.dataString)
    internal fun Linkable.getLinkedItems(relationshipFilter: String? = null): List<Linkable> {
        return with(links) {
            getLinksContainingThis(relationshipFilter)
                .filter { relationshipFilter == null || it.relationship == relationshipFilter }
                .flatMap { it.ids }
                .map { getItem(it) }
                .filterIsInstance<Linkable>()
                .filterNot { it == this@getLinkedItems }
        }
    }
}