package gov.afrl.batdok.encounter.medicine

import gov.afrl.batdok.commands.proto.*
import gov.afrl.batdok.encounter.Administrable
import gov.afrl.batdok.encounter.CommonLinkRelationships
import gov.afrl.batdok.encounter.commands.buildLinkFromLinkables
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.encounter.ids.MedicineId
import gov.afrl.batdok.encounter.orders.IOrderLine
import gov.afrl.batdok.util.DocumentItem
import gov.afrl.batdok.util.nullableFloatValue
import gov.afrl.batdok.util.nullableStringValue
import gov.afrl.batdok.util.stringValue
import mil.af.afrl.batman.batdokid.DomainId
import java.io.Serializable
import java.time.Instant

data class Medicine(
    override val name: String,
    override val ndc: String? = null,
    override val rxcui: String? = null,
    val administrationTime: Instant? = null, // Not used for setting, only getting
    override val medId: MedicineId = DomainId.create(),
    override val route: String? = null,
    override val volume: Float? = null,
    override val unit: String? = null,
    override val serialNumber: String? = null,
    override val expirationDate: String? = null,
    override val type: String? = null,
    private val docId: DocumentId = DomainId.nil(),
    override val timestamp: Instant? = administrationTime,
    override val exportName: String? = null
): DocumentItem<MedicineId>(medId, docId, type, administrationTime), Administrable, Serializable, IMedicine {
    internal constructor(
        protoMed: SharedProtobufObjects.Medicine,
        documentId: DocumentId,
        administrationTime: Instant? = Instant.now(),
        id: MedicineId = DomainId.create()
    ): this(
        protoMed.nameOrNull?.value ?: "",
        ndc = protoMed.ndcOrNull?.valueOrNull?.value,
        rxcui = protoMed.rxcuiOrNull?.valueOrNull?.value,
        administrationTime = administrationTime,
        medId = id,
        route = KnownRoutes.fromProto(protoMed.route),
        volume = protoMed.volumeOrNull?.valueOrNull?.value,
        unit = KnownUnits.fromProto(protoMed.unit),
        serialNumber = protoMed.serialNumber.valueOrNull?.value,
        expirationDate = protoMed.expirationDate.valueOrNull?.value,
        type = KnownMedTypes.fromProto(protoMed.type),
        docId = documentId,
        exportName = protoMed.exportName?.valueOrNull?.value
    )

    fun toProto() = medicine{
        this.ndc = nullableStringValue(<EMAIL>)
        rxcui = nullableStringValue(<EMAIL>)
        name = stringValue(<EMAIL>)
        route = KnownRoutes.fromString(<EMAIL>)
        volume = nullableFloatValue(<EMAIL>)
        unit = KnownUnits.fromString(<EMAIL>)
        serialNumber = nullableStringValue(<EMAIL>)
        expirationDate = nullableStringValue(<EMAIL>)
        type = KnownMedTypes.fromString(<EMAIL>)
        exportName = nullableStringValue(<EMAIL>)
    }

    fun toEventString(): String {
        val extras = listOfNotNull(
            serialNumber.takeUnless { it.isNullOrEmpty() },
            expirationDate.takeUnless { it.isNullOrEmpty() }
        ).joinToString(", ")
        return listOfNotNull(
            " $category:".takeUnless { category.isNullOrEmpty() } ?: ":",
            name.takeUnless { it.isEmpty() },
            route.takeUnless { it.isNullOrEmpty() },
            volume.takeUnless { it == 0.0f }?.let { "$volume" },
            unit.takeUnless { it.isNullOrEmpty() },
            "($extras)".takeUnless { extras.isEmpty()}
        ).joinToString(" ")
    }

    fun getVolumeString() : String {
        return listOfNotNull(volume?.toString()?.removeSuffix(".0"), unit)
            .joinToString(" ")
            .trim()
    }

    override fun createLinkCommand(orderLine: IOrderLine) = buildLinkFromLinkables(
        CommonLinkRelationships.FULFILL_ORDERLINE, this, orderLine)
}