package gov.afrl.batdok.encounter.treatment

import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.TreatmentCommands
import gov.afrl.batdok.commands.proto.dressing
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

class DressingData(
    val type: String,
    val location: String? = null,
    val subtype: String? = null
): TreatmentData{
    init {
        //If both Type and SubType are an enum value, make sure they are a valid pair
        SubType.find(subtype)?.let { subLocation ->
            Type.find(type)?.let { location ->
                require(location == subLocation.type)
            }
        }
    }

    override fun toProtobuf(): Message = dressing{
        type = Type.fromString(<EMAIL>)
        location = Location.fromString(<EMAIL>)
        this.subtype = SubType.fromString(<EMAIL>)
    }

    override fun toDetailString(): String {
        return listOfNotNull(
            type.toDetailString("Type"),
            location.toDetailString("Location"),
            subtype.toDetailString("Subtype")
        ).joinToString(" - ")
    }

    companion object{
        fun fromProtobuf(dressing: TreatmentCommands.Dressing): DressingData{
            return DressingData(
                Type.fromProto(dressing.type)?:"",
                Location.fromProto(dressing.location)?:"",
                SubType.fromProto(dressing.subtype)
            )
        }
    }

    enum class Location(override val protoIndex: Int, override val dataString: String): ProtoEnum {
        LUE(1, "LUE"),
        LLE(2, "LLE"),
        RUE(3, "RUE"),
        RLE(4, "RLE"),
        HEAD(5, "Head"),
        NECK(6, "Neck"),
        FRONT_TORSO(7, "Front Torso"),
        BACK_TORSO(8, "Back Torso"),
        @Deprecated("Use a more specific junctional location")
        JUNCTIONAL(9, "Junctional"),
        JUNCTIONAL_LU(10, "Junctional - Left Upper"),
        JUNCTIONAL_RU(11, "Junctional - Right Upper"),
        JUNCTIONAL_LL(12, "Junctional - Left Lower"),
        JUNCTIONAL_RL(13, "Junctional - Right Lower"),
        ABDOMEN(14, "Abdomen"),
        ;

        companion object{
            fun find(dataString: String?) = Type.values().find { it.dataString == dataString }
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = Type.values().protoToString(enum)
            fun fromString(string: String?) = Type.values().stringToProto(string)
        }
    }

    enum class Type(override val protoIndex: Int, override val dataString: String): ProtoEnum {
        HEMOSTATIC(1, "Hemostatic"),
        PRESSURE(2, "Pressure"),
        @Deprecated("Use Nonhemostatic instead")
        KERLIX(3, "Kerlix"),
        ABDOMINAL(4, "Abdominal"),
        BURN(5, "Burn"),
        NONHEMOSTATIC(6, "Non Hemostatic (Kerlix)"),
        ;

        companion object{
            fun find(dataString: String?) = values().find { it.dataString == dataString }
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }

    enum class SubType(override val protoIndex: Int, override val dataString: String, val type: Type): ProtoEnum {
        XSTAT(1, "Xstat", Type.HEMOSTATIC),
        COMMERCIAL(2, "Commercial", Type.PRESSURE),
        ELASTIC(3, "Elastic/Gauze", Type.PRESSURE),
        ;

        companion object{
            fun find(dataString: String?) = values().find { it.dataString == dataString }
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }
}