package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.proto.SharedProtobufObjects

class Physician(
    name: String? = null,
    phone: String? = null,
    email: String? = null
) : Contact(name, phone, email) {
    constructor(proto: SharedProtobufObjects.Contact): this(
        proto.name.value.takeIf { proto.hasName() && proto.name.value.isNotBlank() },
        proto.phone.value.takeIf { proto.hasPhone() && proto.phone.value.isNotBlank() },
        proto.email.value.takeIf { proto.hasEmail() && proto.email.value.isNotBlank() }
    )
}