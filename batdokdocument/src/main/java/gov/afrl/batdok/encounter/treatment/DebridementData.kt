package gov.afrl.batdok.encounter.treatment

import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.TreatmentCommands
import gov.afrl.batdok.commands.proto.debridement
import gov.afrl.batdok.util.compatibleEnum
import gov.afrl.batdok.util.toPrimitive

class DebridementData(val location: String? = null): TreatmentData{
    override fun toProtobuf(): Message = debridement {
        this.location = compatibleEnum(<EMAIL>)
    }

    override fun toDetailString(): String {
        return location ?: ""
    }

    companion object{
        fun fromProtobuf(debridement: TreatmentCommands.Debridement) = DebridementData(
            debridement.location.toPrimitive(),
        )
    }
}