package gov.afrl.batdok.encounter.observation

import com.google.protobuf.Any
import com.google.protobuf.kotlin.isA
import com.google.protobuf.kotlin.unpack
import gov.afrl.batdok.commands.proto.Observations
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

enum class CommonObservations(override val protoIndex: Int, override val dataString: String): ProtoEnum {
    SEDATION(1, "Sedation"),
    PARALYZATION(2, "Paralyzation"),
    PUPIL_DILATION(3, "Pupil Dilation"),
    PUPIL_REACTION(4, "Pupil Reaction"),
    RHYTHM(5, "Rhythm"),
    CAP_REFILL(6, "Capillary Refill"),
    GASTRO(7, "<PERSON><PERSON>"),
    WHEEZING(8, "Wheezing"),
    RH<PERSON><PERSON><PERSON>(9, "Rhonchi"),
    RALES(10, "Rales"),
    INTACT_AIRWAY(11, "Intact Airway"),
    NOT_PATENT(12, "Not Patent/Unable to Maintain"),
    CHEST_EQUAL_RISE_FALL(13, "Chest Equal Rise & Fall"),
    RESP_EFFORT(14, "Respiratory Effort"),
    PULSE_VALUES(15, "Pulse Values"),
    PULSE_TYPES(16, "Pulse Types"),
    INTEG(17, "Integ"),
    FEELING(18, "Feeling"),
    TRANSFUSION_INDICATION(19, "Transfusion Indication"),
    ULTRASOUND(20, "Ultrasound"),
    CASUALTY_PPE(21, "Casualty PPE"),
    HEAD_INJURY(22, "Head Injury"),
    @Deprecated("This will soon become a lab")
    BLOOD_SUGAR(23, "Blood Sugar"),
    @Deprecated("Just send the diagnostic as the observation string")
    OTHER_DIAGNOSTICS(24, "Other Diagnostics"),
    BREATH_SOUNDS(25, "Breath Sounds"),
    HEARING_IMPAIRED(26, "Hearing Impaired"),
    COMMUNICATION_BARRIERS(27, "Communication Barriers"),
    VISION_IMPAIRED(28, "Vision Impaired"),
    CARDIAC_HX(29, "Cardiac Hx"),
    DIABETES(30, "Diabetes"),
    MOTION_SICKNESS(31, "Motion Sickness"),
    EARS_SINUS_PROBLEMS(32, "Ears / Sinus Problems"),
    HYPERTENSION(33, "Hypertension"),
    DIZZINESS(34, "Dizziness"),
    VOIDING_DIFFICULTY(35, "Voiding Difficulty"),
    TAKES_LONGTERM_MEDS(36, "Takes Long-Term Meds"),
    WILL_SELF_MEDICATE(37, "Will Self Medicate"),
    HAS_ADEQUATE_SUPPLY_OF_MEDS(38, "Has Adequate Supply of Meds"),
    VERBALIZED_KNOWS_HOW_TO_TAKE_MEDS(39, "Verbalized Knows How to Take Meds"),
    MEDICATION_LISTED_ON_PHYSICIANS_ORDERS(40, "Medications Listed on Physicians Orders"),
    PHYSICAL_EXAM(41, "Physical Exam"),
    EXAM(42, "Exam"),
    EFAST_EXAM(43, "EFAST Exam")
    ;

    companion object{
        fun getObservationData(any: Any) = when {
            any.isA<Observations.BooleanData>() -> BooleanObservation(any.unpack())
            any.isA<Observations.TextData>() -> TextData(any.unpack())
            any.isA<Observations.PupilDilation>() -> PupilDilationData(any.unpack())
            any.isA<Observations.PupilReaction>() -> PupilReactionData(any.unpack())
            any.isA<Observations.RhythmData>() -> RhythmData(any.unpack())
            any.isA<Observations.CapillaryRefillData>() -> CapillaryRefillData(any.unpack())
            any.isA<Observations.PulseData>() -> PulseTypeData(any.unpack())
            any.isA<Observations.FeelingData>() -> FeelingData(any.unpack())
            any.isA<Observations.RespSideData>() -> RespSideData(any.unpack())
            any.isA<Observations.GastroData>() -> GastroData(any.unpack())
            any.isA<Observations.IntegData>() -> IntegData(any.unpack())
            any.isA<Observations.RespEffortExtras>() -> RespEffortData(any.unpack())
            any.isA<Observations.PulseValuesData>() -> PulseValuesData(any.unpack())
            any.isA<Observations.TransfusionIndicationData>() -> TransfusionIndicationData(any.unpack<Observations.TransfusionIndicationData>())
            any.isA<Observations.UltrasoundData>() -> UltrasoundData(any.unpack())
            any.isA<Observations.CasualtyPPEData>() -> CasualtyPPEData(any.unpack<Observations.CasualtyPPEData>())
            any.isA<Observations.BloodSugarData>() -> BloodSugarData(any.unpack())
            any.isA<Observations.OtherDiagnosticsData>() -> OtherDiagnostics(any.unpack())
            any.isA<Observations.BreathSoundsData>() -> BreathSoundsData(any.unpack<Observations.BreathSoundsData>())
            any.isA<Observations.PhysicalExamData>() -> PhysicalExamData(any.unpack())
            any.isA<Observations.ExamData>() -> ExamData(any.unpack())
            any.isA<Observations.ChestRiseFallData>() -> ChestRiseFallData(any.unpack())
            any.isA<Observations.EFastExamData>() -> EFastExamData(any.unpack())
            else -> null
        }

        fun find(enum: SharedProtobufObjects.CompatibleEnum) = values().find { it.protoIndex == enum.enum || it.dataString == enum.string }
        fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values()
            .protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)
    }
}