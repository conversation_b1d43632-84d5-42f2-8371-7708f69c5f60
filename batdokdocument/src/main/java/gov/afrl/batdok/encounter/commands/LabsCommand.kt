package gov.afrl.batdok.encounter.commands

import gov.afrl.batdok.commands.eventHandler
import gov.afrl.batdok.commands.handler
import gov.afrl.batdok.commands.handlerWithId
import gov.afrl.batdok.commands.proto.*
import gov.afrl.batdok.commands.proto.LabCommands.*
import gov.afrl.batdok.encounter.IndividualLab
import gov.afrl.batdok.encounter.KnownEventTypes
import gov.afrl.batdok.encounter.Labs
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.encounter.ids.LabId
import gov.afrl.batdok.encounter.ids.toByteString
import gov.afrl.batdok.encounter.ids.toDomainId
import gov.afrl.batdok.encounter.panel.KnownLabs
import gov.afrl.batdok.util.addPreviousFields
import gov.afrl.batdok.util.removeDuplicates
import gov.afrl.batdok.util.toPrimitive
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant

fun LabEntry.toIndividualLab(documentId: DocumentId, timestamp: Instant): IndividualLab? {
    return KnownLabs.fromProto(name)?.let {
        IndividualLab(
            it,
            valueOrNull?.toPrimitive(),
            unitOrNull?.toPrimitive().takeUnless { it.isNullOrBlank() },
            this.id.toDomainId(),
            timestamp,
            documentId
        )
    }
}

//region Log Labs Command

fun buildLogLabCommand(vararg labs: IndividualLab) = buildLogLabCommand(labs.toList())
fun buildLogLabCommand(labs: Collection<IndividualLab>) = logLabCommand{
    this.labValues.addAll(labs.map { it.toProto() })
}

fun logLabsCommandHandler(labs: Labs) = handlerWithId<LogLabCommand>{ docId, command ->
    val encounterLab = labValuesList.map{ it.toIndividualLab(docId, Instant.ofEpochSecond(command.timestamp)) }
    labs += encounterLab
}

fun logLabsEventCommandHandler() = eventHandler<LogLabCommand>(KnownEventTypes.LABS.dataString) { fullCommand ->
    val encounterLab = labValuesList.map{ it.toIndividualLab(DomainId.create(), Instant.ofEpochSecond(fullCommand.timestamp)) }
    "Logged Lab: " + encounterLab.joinToString(", ")
}

//endregion

//region Update Labs Command

fun buildUpdateLabCommand(lab: IndividualLab, previousLab: IndividualLab? = null) = updateLabCommand{
    labValue = lab.toProto().removeDuplicates(previousLab?.toProto()).copy {
        //The id would be removed by the duplicates, but we need it, so add it back in
        id = lab.id.toByteString()
    }
}

fun updateLabsCommandHandler(labs: Labs) = handlerWithId<UpdateLabCommand>{ docId, fullCommand ->
    val previousLabProto = labs[labValue.id.toDomainId<DomainId>()]?.toProto()
    val restoredLabValue = LabEntry.parseFrom(labValue.addPreviousFields(previousLabProto).toByteString())
    val lab = restoredLabValue.toIndividualLab(docId, Instant.ofEpochSecond(fullCommand.timestamp)) ?: return@handlerWithId
    if(lab.value == null && lab.unit == null){
        labs.removeItem(lab.id, Instant.ofEpochSecond(fullCommand.timestamp))
    }else {
        labs += lab
    }
}

fun updateLabEventCommandHandler(labs: Labs) =  eventHandler<UpdateLabCommand>(
    KnownEventTypes.LABS.dataString,
    referenceId = { labValue.id.toDomainId() },
    handler = {
        val previousLab = labs[labValue.id.toDomainId<DomainId>()]
            val restoredLabValue = LabEntry.parseFrom(labValue.addPreviousFields(previousLab?.toProto()).toByteString())
        val lab = restoredLabValue.toIndividualLab(DomainId.create(), Instant.ofEpochSecond(it.timestamp)) ?: return@eventHandler null
        if(lab.value == null && lab.unit == null){
            "Removed Lab: $previousLab"
        }else {
            "Updated Lab: $lab"
        }
    }
)

//endregion

//region Remove Labs Command

fun buildRemoveLabCommand(itemId: LabId) = removeLabCommand {
    labId = itemId.toByteString()
}

fun removeLabCommandHandler(labs: Labs) = handler<RemoveLabCommand> {
    labs.removeItem(labId.toDomainId(), Instant.ofEpochSecond(it.timestamp))
}

fun removeLabEventCommandHandler(labs: Labs) = eventHandler<RemoveLabCommand>(
    referenceId = { labId.toDomainId() }
){
    val id = labId.toDomainId<LabId>()
    val encounterLab = labs[id] ?: return@eventHandler null
    "Removed Lab: $encounterLab"
}

//endregion