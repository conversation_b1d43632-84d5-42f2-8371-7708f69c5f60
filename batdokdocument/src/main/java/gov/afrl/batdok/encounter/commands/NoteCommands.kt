package gov.afrl.batdok.encounter.commands

import gov.afrl.batdok.commands.eventHandler
import gov.afrl.batdok.commands.handler
import gov.afrl.batdok.commands.handlerWithId
import gov.afrl.batdok.commands.proto.NoteCommands
import gov.afrl.batdok.commands.proto.addNote
import gov.afrl.batdok.commands.proto.removeNote
import gov.afrl.batdok.commands.proto.updateNote
import gov.afrl.batdok.encounter.KnownEventTypes
import gov.afrl.batdok.encounter.Note
import gov.afrl.batdok.encounter.Notes
import gov.afrl.batdok.encounter.ids.NoteId
import gov.afrl.batdok.encounter.ids.toByteString
import gov.afrl.batdok.encounter.ids.toDomainId
import java.time.Instant

fun buildAddNoteCommand(note: Note) = addNote {
    this.note = note.toProto()
}

fun addNoteCommandHandler(notes: Notes) = handlerWithId<NoteCommands.AddNote> { _, command ->
    notes += Note(noteProto = this.note, callsign = command.callsign)
}

fun addNoteCommandEventHandler() = eventHandler<NoteCommands.AddNote>(
    eventType = KnownEventTypes.NOTE.toString(),
    referenceId = {this.note.noteId.toDomainId()}
) { command ->
        val note = Note(noteProto = this.note, callsign = command.callsign)
        "Added Note:\n${note.message}"
    }

fun buildUpdateNoteCommand(note: Note) = updateNote {
    this.note = note.toProto()
}

fun updateNoteCommandHandler(notes: Notes) = handlerWithId<NoteCommands.UpdateNote> { _, command ->
    val newNote = Note(noteProto = this.note, callsign = command.callsign)
    notes += newNote
}

fun updateNoteCommandEventHandler(notes: Notes) = eventHandler<NoteCommands.UpdateNote>(KnownEventTypes.OTHER.dataString)
{command ->
    val newNote = Note(noteProto = this.note, callsign = command.callsign)
    val oldNote = notes[newNote.id]
    if (oldNote != null) {
        "Updated Note from:\n\"${oldNote.message}\"\nto:\n\"${newNote.message}\""
    } else {
        "Added Note:\n${newNote.message}"
    }

}

fun buildRemoveNoteCommand(noteId: NoteId) = removeNote {
    this.noteId = noteId.toByteString()
}

fun removeNoteCommandHandler(notes: Notes) = handler<NoteCommands.RemoveNote> {
    val noteId:NoteId = this.noteId.toDomainId()
    notes.removeItem(noteId, Instant.ofEpochSecond(it.timestamp))
}

fun removeNoteCommandEventHandler(notes: Notes) = eventHandler<NoteCommands.RemoveNote>(KnownEventTypes.OTHER.dataString,)
{
    val note = notes[this.noteId.toDomainId()]

    if (note != null) {
        "Removed Note:\n\"${note.message}\""
    } else {
        null
    }
}