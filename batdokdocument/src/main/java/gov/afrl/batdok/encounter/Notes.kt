package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.CommandHandler
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.commands.*
import gov.afrl.batdok.util.ILinkableList
import gov.afrl.batdok.util.SortedLinkableList
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant

class Notes: ILinkableList<Note>  {
    val _list = SortedLinkableList<Note>()

    @Transient val handlers = CommandHandler().apply {
        val notes = this@Notes
        +addNoteCommandHandler(notes)
        +updateNoteCommandHandler(notes)
        +removeNoteCommandHandler(notes)
    }

    companion object{
        fun EventCommandHandler.includeNoteEvents(notes: Notes){
            +addNoteCommandEventHandler()
            +updateNoteCommandEventHandler(notes)
            +removeNoteCommandEventHandler(notes)
        }
    }

    override val list: List<Note>
        get() = _list.list
    override val onErrorRemovedItems: List<Pair<Instant, Note>>
        get() = _list.onErrorRemovedItems
    override val onProperRemovedItems: List<Pair<Instant, Note>>
        get() = _list.onProperRemovedItems

    override fun removeItem(itemId: DomainId, timestamp: Instant, documentationError: Boolean) {
        _list.removeItem(itemId, timestamp, documentationError)
    }

    override fun plusAssign(item: Note?) {
        _list.plusAssign(item)
    }
}
