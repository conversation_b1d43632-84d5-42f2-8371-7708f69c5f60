package gov.afrl.batdok.encounter.metadata

import gov.afrl.batdok.commands.proto.SharedProtobufObjects.CompatibleEnum
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

enum class Decision(override val protoIndex: Int, override val dataString: String): ProtoEnum {
    CARDIO(1, "Cardiovascular"),
    PULMONARY(2, "Pulmonary"),
    THORACIC(3, "Thoracic"),
    NEUROLOGIC(4, "Neurologic"),
    ABDOMINAL(5, "Abdominal"),
    RENAL(6, "Renal"),
    ANALGESIA(7, "Analgesia/Sedation"),
    ;

    companion object{
        fun fromProto(enum: CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)
    }
}