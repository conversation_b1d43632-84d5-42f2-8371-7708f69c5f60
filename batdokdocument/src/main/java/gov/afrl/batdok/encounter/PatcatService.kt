package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.proto.SharedProtobufObjects.CompatibleEnum
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto
import gov.afrl.batdok.util.toChangeOrClearEvent

enum class PatcatService(
    override val protoIndex: Int,
    override val dataString: String,
    val abbreviation: String,
    val shortName: String = abbreviation,
    val fullDescription: String = shortName,
    val validStatuses: List<PatcatStatus> = listOf(),
    val validStatusesShortlist: List<PatcatStatus> = listOf()
) : ProtoEnum {
    ARMY(1, "A", "USA", "Army", "United States Army", validStatuses = PatcatStatus.armyList, validStatusesShortlist = PatcatStatus.militaryARNShortlist),
    NOAA(2, "B", "NOAA", validStatuses = PatcatStatus.noaaList, validStatusesShortlist = PatcatStatus.militaryAShortlist),
    COAST_GUARD(3, "C", "USCG", "Coast Guard", validStatuses = PatcatStatus.coastGuardList, validStatusesShortlist = PatcatStatus.militaryARShortlist),
    AIR_FORCE(4, "F", "USAF", "Air Force", validStatuses = PatcatStatus.airForceList, validStatusesShortlist = PatcatStatus.militaryARNShortlist),
    CIVILIAN(5, "K", "Civilian", validStatuses = PatcatStatus.civilianList, validStatusesShortlist = PatcatStatus.otherShortList),
    MARINE_CORPS(6, "M", "USMC", validStatuses = PatcatStatus.marineList, validStatusesShortlist = PatcatStatus.militaryARShortlist),
    NAVY(7, "N", "USN", validStatuses = PatcatStatus.navyList, validStatusesShortlist = PatcatStatus.militaryARShortlist),
    USPHS(8, "P", "USPHS", validStatuses = PatcatStatus.usphsList, validStatusesShortlist = PatcatStatus.militaryARShortlist),
    FOREIGN_NATIONAL(9, "R", "Foreign National", validStatuses = PatcatStatus.foreignNationalsList),
    SPACE_FORCE(10, "S", "USSF", validStatuses = PatcatStatus.spaceForceList, validStatusesShortlist = PatcatStatus.militaryARNShortlist),
    VETERANS_AFFAIRS(11, "V", "VA", validStatuses = PatcatStatus.veteransAffairsList);

    companion object {
        fun fromProto(enum: CompatibleEnum) = entries.toTypedArray().protoToString(enum)

        fun fromString(string: String?, includeAnyway: List<PatcatService> = listOf()) =
            entries.toTypedArray().stringToProto(string, includeAnyway)

        fun abbreviatedString(string: String?) = entries.find { it.dataString == string }?.abbreviation ?: string

        fun toEvent(enum: CompatibleEnum) = entries.toTypedArray().toChangeOrClearEvent(enum, "service")
    }
}