package gov.afrl.batdok.encounter.vitals

import gov.afrl.batdok.commands.proto.VitalOuterClass
import gov.afrl.batdok.commands.proto.capillaryRefill
import gov.afrl.batdok.encounter.IndividualVital
import gov.afrl.batdok.util.floatValue

class CapRefill(val capRefill: Float?): IndividualVital("Capillary Refill"){
    constructor(capRefill: VitalOuterClass.CapillaryRefill): this(
        capRefill.capRefill.value.takeIf { capRefill.hasCapRefill() },
    )
    override fun toProtobuf() = capillaryRefill {
        <EMAIL>?.let { this.capRefill = floatValue(it) }
    }
    override fun produceEmptyVital() = CapRefill(null)

    override val eventMessage: String
        get() = capRefill?.let { "%.1f".format(it) } ?: "--"

    override val isEmpty: Boolean
        get() = capRefill == null
}