package gov.afrl.batdok.encounter.vitals

import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.VitalOuterClass
import gov.afrl.batdok.commands.proto.aVPU
import gov.afrl.batdok.encounter.IndividualVital
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

//region Individual Vitals
class Avpu(val avpu: String?): IndividualVital("AVPU"){
    constructor(avpu: VitalOuterClass.AVPU): this(
        Level.fromProto(avpu.value),
    )
    override fun toProtobuf() = aVPU {
        <EMAIL>?.let { this.value = Level.fromString(avpu) }
    }

    override fun produceEmptyVital() = Avpu(null)

    override val eventMessage: String
        get() = "$avpu"

    override val isEmpty: Boolean
        get() = avpu == null

    enum class Level(override val protoIndex: Int, override val dataString: String): ProtoEnum {
        ALERT(1, "Alert"),
        VERBAL(2, "Verbal"),
        PAIN(3, "Pain"),
        UNRESPONSIVE(4, "Unresponsive")
        ;

        companion object{
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = entries.toTypedArray().protoToString(enum)
            fun fromString(string: String?) = entries.toTypedArray().stringToProto(string)
        }
    }
}