package gov.afrl.batdok.encounter

import java.io.Serializable
import java.time.Instant
import java.time.ZoneId

class Interval: Serializable {
    companion object{
        private val hourMinuteRegex = "Q(([0-9]+)H)?(([0-9]+)MIN)?".toRegex()
    }
    private var intervalMessage: String

    constructor(interval: String) {
        intervalMessage = interval
    }

    constructor(hour: Int, minute: Int) {
        val timeString =  listOfNotNull(
            "${hour}H".takeIf { hour > 0 },
            "${minute}MIN".takeIf { minute > 0 }
        ).joinToString("")
        intervalMessage = if (timeString.isNotBlank()) "Q${timeString}" else ""
    }

    override fun toString(): String {
        return intervalMessage
    }

    fun isHourMinuteInterval(): Boolean {
        return getMatchResult() != null
    }

    fun nextDue(lastAdministered: Instant): Instant? {
        val result = getMatchResult() ?: return null
        val hours = result.groups[2]?.value?.toLong() ?: 0
        val min = result.groups[4]?.value?.toLong() ?: 0
        return lastAdministered.atZone(ZoneId.systemDefault())
            .plusHours(hours)
            .plusMinutes(min)
            .toInstant()
    }

    private fun getMatchResult(): MatchResult? {
        if(!intervalMessage.startsWith("Q")) return null

        return hourMinuteRegex.matchEntire(intervalMessage)
    }

    override fun equals(other: Any?): Boolean {
        return if (other is Interval) {
            intervalMessage == other.intervalMessage
        } else {
            false
        }
    }

    override fun hashCode(): Int {
        return intervalMessage.hashCode()
    }
}