package gov.afrl.batdok.encounter.treatment

import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.TreatmentCommands.DirectPressure
import gov.afrl.batdok.commands.proto.directPressure
import gov.afrl.batdok.util.compatibleEnum
import gov.afrl.batdok.util.toPrimitive

class DirectPressureData(val location: String? = null): TreatmentData{
    override fun toProtobuf(): Message = directPressure{
        location = compatibleEnum(<EMAIL>)
    }

    override fun toDetailString(): String {
        return location ?: ""
    }

    companion object{
        fun fromProtobuf(directPressure: DirectPressure): DirectPressureData{
            return DirectPressureData(directPressure.location.toPrimitive())
        }
    }
}