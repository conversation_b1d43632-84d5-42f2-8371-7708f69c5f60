package gov.afrl.batdok.encounter.orders

import gov.afrl.batdok.encounter.*
import gov.afrl.batdok.encounter.ids.OrderLineId
import gov.afrl.batdok.encounter.metadata.Signature
import gov.afrl.batdok.util.Timestamped
import java.io.Serializable
import java.time.Instant

interface IOrderLine : Linkable, Serializable, Timestamped {
    override val id: OrderLineId
    override val timestamp: Instant
    val title: String
    val instructions: String
    val orderType: String
    val orderStatus: String
    val frequency: Interval
    val lastOccurrence: Instant?
    val provider: Contact?
    val signature: Signature?

    /**
     * Using the supplied [document], provides the list of all [Event] items that are
     * associated with objects that are related to an [OrderLine].
     *
     * Currently, those items are [Administrable] and [Note] events.
     *
     * The returned list is sorted by timestamp, oldest to newest.
     */
    fun getOrderHistory(document: Document) : List<Event>{
        return with(document.links) {
            getLinksContainingThis()
                .flatMap { it.ids }
                .distinct()
                .flatMap { document.events.commentsForId(it) }
                .sortedBy { it.timestamp }
        }
    }

    /**
     * Retrieves all the [Administrable] items that are linked to this order, orders them by timestamp, and returns
     * the latest one, if any exists.
     */
    fun getLastAdministration(document: Document): Administrable?{
        return with(document){
            getLinkedItems(CommonLinkRelationships.FULFILL_ORDERLINE)
                .filterIsInstance<Administrable>()
                .maxOrNull()
        }
    }
}