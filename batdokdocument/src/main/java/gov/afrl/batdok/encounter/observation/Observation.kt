package gov.afrl.batdok.encounter.observation

import com.google.protobuf.Message
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.encounter.ids.ObservationId
import gov.afrl.batdok.util.DocumentItem
import mil.af.afrl.batman.batdokid.DomainId
import java.io.Serializable
import java.time.Instant

data class Observation(
    val name: String? = null,
    val observationData: ObservationData? = null,
    override val id: ObservationId = DomainId.create(),
    override val timestamp: Instant = Instant.now(),
    override val documentId: DocumentId = DomainId.nil()
): DocumentItem<ObservationId>(id, documentId, name, timestamp), Serializable {
    /**
     * Return the data as the proper type. Return null if the type is not the right type
     */
    inline fun <reified T: ObservationData> getData(): T?{
        return if(observationData is T) observationData else null
    }
    fun toEvent(): String{
        val extraDataText = observationData?.toEventText()
            ?.takeUnless { it.isEmpty() }
            ?.let { ": $it" }
            ?: ""
        return "Observation: $name$extraDataText"
    }
}

sealed interface ObservationData: Serializable{
    fun toProtobuf(): Message
    fun toEventText(): String = toDetailString()
    fun toDetailString(): String = ""

    fun String?.toDetailString(label: String) = "$label: $this".takeUnless { this.isNullOrBlank() }
}