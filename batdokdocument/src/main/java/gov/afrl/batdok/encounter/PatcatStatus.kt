package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.proto.SharedProtobufObjects.CompatibleEnum
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto
import gov.afrl.batdok.util.toChangeOrClearEvent

// TODO: some of the description fields in this enum (especially the short descriptions) are imprecise because they need to vary by subcategory
// but we aren't tracking patcat subcategories yet, thus they are made more generic temporarily until we can add subcategories
enum class PatcatStatus(
    override val protoIndex: Int,
    override val dataString: String,
    // TODO: add name field once we have subcategories, it always varies by subcategory
    val simpleStatus: String,
    val shortDescription: String,
    val fullDescription: String = shortDescription
) : ProtoEnum {
    // used by military branches and civilians
    DECEASED_SPONSOR(1, "00","Other", "DEC SPONSOR", "DECEASED SPONSOR"),

    // used by military branches (services A/B/C/F/M/N/P)
    ACTIVE_DUTY(2, "11", "Active", "AD", "ACTIVE DUTY"),
    ACTIVE_DUTY_RESERVE(3, "12","Reserve", "AD RES", "ACTIVE DUTY RES"),
    ACTIVE_DUTY_RECRUIT(4, "13","Active", "AD RECRUIT"),
    CADET(5, "14", "Other","CADET"),
    NATIONAL_GUARD(6, "15","Guard", "NG", "NATIONAL GUARD"),
    ROTC(7, "21", "Other","ROTC"),
    RESERVE_INACTIVE(8, "22","Reserve", "RES INACT", "RES INACT DUTY TRG"),
    NATIONAL_GUARD_INACTIVE(9, "23", "Guard","NG INACT", "NATL GUARD INACT DUTY TRG"),
    FORMER_ACTIVE_DUTY_TAMP(10, "24", "Other","FRM AD-TAMP", "FRM AD-TRANS ASSIST MGMT PRGM"),
    FAMILY_MEMBER_FORMER_ACTIVE_DUTY_TAMP(11, "25","Other", "FAM MBR FAD-TAMP", "FAM MBR FAD-TRANS ASSIST MGMT PRGM"),
    APPLICANT(12, "26", "Other","APPLICANT", "APPLICANT/REGISTRANT"),
    FORMER_MEMBER_MATERNITY_CARE(13, "27","Other", "FRM MBR-MATERNITY", "FRM MEMBER-MATERNITY CARE"),
    NEWBORN_OF_FORMER_SERVICE_MEMBER(14, "28","Other", "NB FRM MBR", "NEWBORN OF FRM SERVICE MBR"),
    NEWBORN_OF_SPONSORS_DAUGHTER(15, "29","Other", "NB SPON DAUGHTER", "NEWBORN OF SPONSOR'S DAUGHTER"),
    NEWBORN_OF_SPOUSE_OF_FORMER_SERVICE_MEMBER(16, "30","Other", "NB_SP FRM MBR", "NEWBORN OF SPOUSE OF FRM SERVICE MBR"),
    RETIRED_LOS(17, "31", "Retired","RET LOS"),
    RETIRED_PDRL(18, "32","Retired", "RET PDRL"),
    RETIRED_TDRL(19, "33","Retired", "RET TDRL"),
    TRS_TRR_MEMBER(20, "36","Other", "TRS/TRR-MEMBER", "TRICARE RES SELECT/ RETIRED RES-MBR"),
    TRS_TRR_FAMILY_MEMBER(21, "37", "Other","TRS/TRR-FAM MBR", "TRICARE RES SELECT/ RET RES-FAM MBR"),
    FAMILY_MEMBER_ACTIVE_DUTY(22, "41","Other", "FAM MBR AD"),
    FAMILY_MEMBER_RETIRED(23, "43","Other", "FAM MBR RET"),
    FAMILY_MEMBER_TRANSITIONAL_COMP(24, "44", "Other","FAM MBR TRNS CMP", "FAM MBR TRANS COMP"),
    FAMILY_MEMBER_DECEASED_ACTIVE_DUTY(25, "45", "Other","FAM MBR DEC AD", "FAM MBR DECEASED AD"),
    FAMILY_MEMBER_DECEASED_RETIRED(26, "47","Other", "FAM MBR DEC RET", "FAM MBR DECEASED RETIRED"),
    UNREMARRIED_FORMER_SPOUSE(27, "48", "Other","FORMER SPOUSE", "UNREMARRIED FRM SPOUSE"),
    FAMILY_MEMBER_OF_UNREMARRIED_FORMER_SPOUSE(28, "49","Other", "FAM MBR FRM SP", "FAM MBR UNREMAR FRM SPOUSE"),

    // used by civilians (service K)
    STATE_DEPARTMENT_EMPLOYEE_OUTSIDE_US(29, "51", "Civilian","STATE DEPT OCONUS", "STATE DEPT EMPLOYEE - OUTSIDE US"),
    STATE_DEPARTMENT_FAMILY_MEMBER_OUTSIDE_US(30, "52", "Other","STATE DEPT FAM MBR", "STATE DEPT FAM MBR - OUTSIDE US"),
    OTHER_FEDERAL_AGENCY_DEPARTMENT_EMPLOYEE(31, "53", "Civilian","OTHER FED AGENCY/DEPT EMPLOYEE"),
    OTHER_FEDERAL_AGENCY_DEPARTMENT_FAMILY_MEMBER(32, "54", "Other", "OTHER FED AGENCY/DEPT - FAM MBR"),
    DOD_EMPLOYEE_REMOTE_AREA_IN_US(33, "55", "Civilian","DOD EMPL REMOTE US", "DOD EMPLOYEE REMOTE AREA IN US"),
    DOD_FAMILY_MEMBER_REMOTE_AREA_IN_US(34, "56","Other", "DOD FAM MBR REMOTE US", "DOD FAM MBR REMOTE AREA IN US"),
    DOD_EMPLOYEE_OCCUPATIONAL_HEALTH(35, "57", "Civilian","OCCUPATIONAL HEALTH"),
    DISABILITY_RETIREMENT_EXAM(36, "58","Other", "DISABIL RET", "DISABILITY RETIREMENT EXAM"),
    OTHER_FEDERAL_EMPLOYEE_OR_FAMILY_MEMBER(37, "59","Other", "FED EMPL/FAM MBR REMOTE", "OTHER US CIVILIAN EMPL/FAM MBR"),
    VETERANS_AFFAIRS(38, "61", "Civilian","VETERANS AFFAIRS"),
    WORKERS_COMPENSATION(39, "62","Other", "WORKERS COMPENSATION"),
    SERVICE_HOME(40, "63","Other", "SERVICE HOME", "SERVICE HOME - OTHER THAN MIL RET"),
    OTHER_FEDERAL_AGENCY_DEPARTMENT(41, "64", "Civilian","OTHER FED AGENCY/DEPT"),
    CONTRACT_EMPLOYEE_OR_FAMILY_MEMBER(42, "65", "DoD Contr", "OTHER FED AGENCY/DEPT"),
    FEDERAL_PRISONER(43, "66","Other", "FEDERAL PRISONER"),
    USPHS_BENEFICIARY(44, "67", "Other","USPHS BENEFICIARY", "AMERICAN INDIAN, ALEUT, ESKIMO"),
    PACIFIC_ISLAND_NATION(45, "68","Other", "PACIFIC ISLAND NATION", "MICRONESIAN, SAMOAN, PAC ISL NATION"),
    OTHER_US_GOVERNMENT_BENEFICIARY(46, "69", "Other","OTHER US GOVT BENEFICIARY"),
    FOREIGN_NATIONAL_IMET_SALES(47, "71", "Other","FOREIGN NATIONAL - IMET SALES"),
    // the next four are also used by foreign nationals (service R)
    NATO_MILITARY(48, "72", "Other","NATO MILITARY"),
    NATO_FAMILY_MEMBER(49, "73", "Other","NATO FAM MBR", "NATO FAMILY MEMBER"),
    NON_NATO_MILITARY(50, "74", "Other","NON-NATO MILITARY"),
    NON_NATO_FAMILY_MEMBER(51, "75", "Other","NON-NATO FAM MBR", "NON-NATO FAMILY MEMBER"),
    // back to civilians (service K) only
    FOREIGN_CIVILIAN(52, "76", "Other","FRGN CIV", "FOREIGN CIVILIAN"),
    FOREIGN_NATIONAL_POW_INTERNEE(53, "78", "Other","FRGN NAT POW/INTERNEE", "FOREIGN NATIONAL - POW/INTERNEE"),
    US_CIVILIAN_EMPLOYEE_OR_FAMILY_MEMBER_CC_SHP(54, "79", "Other","US CIV EMPL/FAM MBR (CC-SHP)", "US CIVILIAN EMPL/FAM MBR (CC-SHP)"),
    NATO_COALITION_FORCES(55, "80", "Other","NATO COALITION FORCES", "NATO COALITION FORCES MIL AND CIV"),
    NON_NATO_COALITION_FORCES(56, "81", "Other","NATO COALITN FRCS", "NATO COALITION FRCS MIL AND CIV"),
    SECRETARY_OF_THE_ARMY_DESIGNEE(57, "82", "Other","ARMY SEC DES", "SECRETARY OF THE ARMY DESIGNEE"),
    SECRETARY_OF_THE_NAVY_DESIGNEE(58, "83", "Other","NAVY SEC DES", "SECRETARY OF THE NAVY DESIGNEE"),
    SECRETARY_OF_THE_AIR_FORCE_DESIGNEE(59, "84", "Other","AF SEC DES", "SECRETARY OF THE AF DESIGNEE"),
    NDAA_2017_SECTION_717_PATIENT(60, "85", "Other","NDAA17 Sec717 PATIENT", "NDAA 2017 Sec717 PATIENT"),
    SECRETARY_OF_THE_DOD_DESIGNEE(61, "86", "Other","DOD_SEC_DES", "SECRETARY OF THE DOD DESIGNEE"),
    CIVILIAN_HUMANITARIAN_OR_REFUGEE(62, "91", "Other","CIV HUM", "CIVILIAN HUMANITARIAN"),
    CIVILIAN_EMERGENCY_CARE(63, "92", "Other","CIVILIAN EMERGENCY", "EMERGENCY CARE"),
    MEDICARE_CIVILIAN_EMERGENCY(64, "93", "Other","MEDICARE", "EMERGENCY CARE"),
    MEDICAID_CIVILIAN_EMERGENCY(65, "94", "Other","STATE MEDICAID PATNT", "EMERGENCY CARE"),
    STATE_CHILDRENS_HEALTH_INSURANCE_PROGRAM(66, "95", "Other","STATE CHIP", "EMERGENCY CARE"),
    PATIENT_NOT_ELSEWHERE_CLASSIFIED(67, "99", "Other","PATIENT NEC", "PATIENT NOT ELSEWHERE CLASSIFIED"),

    // used by Veterans Affairs (service V)
    VA_CHOICE_SERVICE_CONNECTED(68, "01", "Other","SRVC CONN, VA CHOICE", "CHOICE SERVICE CONNECTED"),
    VA_CHOICE_NON_SERVICE_CONNECTED(69, "02", "Other","NON-SRVC CONN, CHOICE", "CHOICE NON-SERVICE CONNECTED");


    companion object {
        fun fromProto(enum: CompatibleEnum) = entries.toTypedArray().protoToString(enum)

        fun fromString(string: String?, includeAnyway: List<PatcatStatus> = listOf()) =
            entries.toTypedArray().stringToProto(string, includeAnyway)

        fun shortString(string: String?) = entries.find { it.dataString == string }?.shortDescription ?: string

        fun fullString(string: String?) = entries.find { it.dataString == string }?.fullDescription ?: string

        fun toEvent(enum: CompatibleEnum) = entries.toTypedArray().toChangeOrClearEvent(enum, "status")

        /** Statuses that apply to military branches (services A/B/C/F/M/N/P). */
        val armyList = listOf(
            DECEASED_SPONSOR,
            ACTIVE_DUTY,
            ACTIVE_DUTY_RESERVE,
            ACTIVE_DUTY_RECRUIT,
            CADET,
            NATIONAL_GUARD,
            ROTC,
            RESERVE_INACTIVE,
            NATIONAL_GUARD_INACTIVE,
            FORMER_ACTIVE_DUTY_TAMP,
            FAMILY_MEMBER_FORMER_ACTIVE_DUTY_TAMP,
            APPLICANT,
            FORMER_MEMBER_MATERNITY_CARE,
            NEWBORN_OF_FORMER_SERVICE_MEMBER,
            NEWBORN_OF_SPONSORS_DAUGHTER,
            NEWBORN_OF_SPOUSE_OF_FORMER_SERVICE_MEMBER,
            RETIRED_LOS,
            RETIRED_PDRL,
            RETIRED_TDRL,
            TRS_TRR_MEMBER,
            TRS_TRR_FAMILY_MEMBER,
            FAMILY_MEMBER_ACTIVE_DUTY,
            FAMILY_MEMBER_RETIRED,
            FAMILY_MEMBER_TRANSITIONAL_COMP,
            FAMILY_MEMBER_DECEASED_ACTIVE_DUTY,
            FAMILY_MEMBER_DECEASED_RETIRED,
            UNREMARRIED_FORMER_SPOUSE,
            FAMILY_MEMBER_OF_UNREMARRIED_FORMER_SPOUSE
        )

        val noaaList = listOf(
            DECEASED_SPONSOR,
            ACTIVE_DUTY,
            APPLICANT,
            FORMER_MEMBER_MATERNITY_CARE,
            NEWBORN_OF_FORMER_SERVICE_MEMBER,
            NEWBORN_OF_SPONSORS_DAUGHTER,
            NEWBORN_OF_SPOUSE_OF_FORMER_SERVICE_MEMBER,
            RETIRED_LOS,
            RETIRED_PDRL,
            RETIRED_TDRL,
            FAMILY_MEMBER_ACTIVE_DUTY,
            FAMILY_MEMBER_RETIRED,
            FAMILY_MEMBER_TRANSITIONAL_COMP,
            FAMILY_MEMBER_DECEASED_ACTIVE_DUTY,
            FAMILY_MEMBER_DECEASED_RETIRED,
            UNREMARRIED_FORMER_SPOUSE,
            FAMILY_MEMBER_OF_UNREMARRIED_FORMER_SPOUSE
        )

        val coastGuardList = listOf(
            DECEASED_SPONSOR,
            ACTIVE_DUTY,
            ACTIVE_DUTY_RESERVE,
            ACTIVE_DUTY_RECRUIT,
            CADET,
            RESERVE_INACTIVE,
            FORMER_ACTIVE_DUTY_TAMP,
            FAMILY_MEMBER_FORMER_ACTIVE_DUTY_TAMP,
            APPLICANT,
            FORMER_MEMBER_MATERNITY_CARE,
            NEWBORN_OF_FORMER_SERVICE_MEMBER,
            NEWBORN_OF_SPONSORS_DAUGHTER,
            NEWBORN_OF_SPOUSE_OF_FORMER_SERVICE_MEMBER,
            RETIRED_LOS,
            RETIRED_PDRL,
            RETIRED_TDRL,
            TRS_TRR_MEMBER,
            TRS_TRR_FAMILY_MEMBER,
            FAMILY_MEMBER_ACTIVE_DUTY,
            FAMILY_MEMBER_RETIRED,
            FAMILY_MEMBER_TRANSITIONAL_COMP,
            FAMILY_MEMBER_DECEASED_ACTIVE_DUTY,
            FAMILY_MEMBER_DECEASED_RETIRED,
            UNREMARRIED_FORMER_SPOUSE,
            FAMILY_MEMBER_OF_UNREMARRIED_FORMER_SPOUSE
        )

        val airForceList = listOf(
            DECEASED_SPONSOR,
            ACTIVE_DUTY,
            ACTIVE_DUTY_RESERVE,
            ACTIVE_DUTY_RECRUIT,
            CADET,
            NATIONAL_GUARD,
            ROTC,
            RESERVE_INACTIVE,
            NATIONAL_GUARD_INACTIVE,
            FORMER_ACTIVE_DUTY_TAMP,
            FAMILY_MEMBER_FORMER_ACTIVE_DUTY_TAMP,
            APPLICANT,
            FORMER_MEMBER_MATERNITY_CARE,
            NEWBORN_OF_FORMER_SERVICE_MEMBER,
            NEWBORN_OF_SPONSORS_DAUGHTER,
            NEWBORN_OF_SPOUSE_OF_FORMER_SERVICE_MEMBER,
            RETIRED_LOS,
            RETIRED_PDRL,
            RETIRED_TDRL,
            TRS_TRR_MEMBER,
            TRS_TRR_FAMILY_MEMBER,
            FAMILY_MEMBER_ACTIVE_DUTY,
            FAMILY_MEMBER_RETIRED,
            FAMILY_MEMBER_TRANSITIONAL_COMP,
            FAMILY_MEMBER_DECEASED_ACTIVE_DUTY,
            FAMILY_MEMBER_DECEASED_RETIRED,
            UNREMARRIED_FORMER_SPOUSE,
            FAMILY_MEMBER_OF_UNREMARRIED_FORMER_SPOUSE
        )

        val marineList = listOf(
            DECEASED_SPONSOR,
            ACTIVE_DUTY,
            ACTIVE_DUTY_RESERVE,
            ACTIVE_DUTY_RECRUIT,
            RESERVE_INACTIVE,
            FORMER_ACTIVE_DUTY_TAMP,
            FAMILY_MEMBER_FORMER_ACTIVE_DUTY_TAMP,
            APPLICANT,
            FORMER_MEMBER_MATERNITY_CARE,
            NEWBORN_OF_FORMER_SERVICE_MEMBER,
            NEWBORN_OF_SPONSORS_DAUGHTER,
            NEWBORN_OF_SPOUSE_OF_FORMER_SERVICE_MEMBER,
            RETIRED_LOS,
            RETIRED_PDRL,
            RETIRED_TDRL,
            TRS_TRR_MEMBER,
            TRS_TRR_FAMILY_MEMBER,
            FAMILY_MEMBER_ACTIVE_DUTY,
            FAMILY_MEMBER_RETIRED,
            FAMILY_MEMBER_TRANSITIONAL_COMP,
            FAMILY_MEMBER_DECEASED_ACTIVE_DUTY,
            FAMILY_MEMBER_DECEASED_RETIRED,
            UNREMARRIED_FORMER_SPOUSE,
            FAMILY_MEMBER_OF_UNREMARRIED_FORMER_SPOUSE
        )

        val navyList = listOf(
            DECEASED_SPONSOR,
            ACTIVE_DUTY,
            ACTIVE_DUTY_RESERVE,
            ACTIVE_DUTY_RECRUIT,
            CADET,
            ROTC,
            RESERVE_INACTIVE,
            FORMER_ACTIVE_DUTY_TAMP,
            FAMILY_MEMBER_FORMER_ACTIVE_DUTY_TAMP,
            APPLICANT,
            FORMER_MEMBER_MATERNITY_CARE,
            NEWBORN_OF_FORMER_SERVICE_MEMBER,
            NEWBORN_OF_SPONSORS_DAUGHTER,
            NEWBORN_OF_SPOUSE_OF_FORMER_SERVICE_MEMBER,
            RETIRED_LOS,
            RETIRED_PDRL,
            RETIRED_TDRL,
            TRS_TRR_MEMBER,
            TRS_TRR_FAMILY_MEMBER,
            FAMILY_MEMBER_ACTIVE_DUTY,
            FAMILY_MEMBER_RETIRED,
            FAMILY_MEMBER_TRANSITIONAL_COMP,
            FAMILY_MEMBER_DECEASED_ACTIVE_DUTY,
            FAMILY_MEMBER_DECEASED_RETIRED,
            UNREMARRIED_FORMER_SPOUSE,
            FAMILY_MEMBER_OF_UNREMARRIED_FORMER_SPOUSE
        )

        val usphsList = listOf(
            DECEASED_SPONSOR,
            ACTIVE_DUTY,
            ACTIVE_DUTY_RESERVE,
            RESERVE_INACTIVE,
            APPLICANT,
            FORMER_MEMBER_MATERNITY_CARE,
            NEWBORN_OF_FORMER_SERVICE_MEMBER,
            NEWBORN_OF_SPONSORS_DAUGHTER,
            NEWBORN_OF_SPOUSE_OF_FORMER_SERVICE_MEMBER,
            RETIRED_LOS,
            RETIRED_PDRL,
            RETIRED_TDRL,
            FAMILY_MEMBER_ACTIVE_DUTY,
            FAMILY_MEMBER_RETIRED,
            FAMILY_MEMBER_TRANSITIONAL_COMP,
            FAMILY_MEMBER_DECEASED_ACTIVE_DUTY,
            FAMILY_MEMBER_DECEASED_RETIRED,
            UNREMARRIED_FORMER_SPOUSE,
            FAMILY_MEMBER_OF_UNREMARRIED_FORMER_SPOUSE
        )

        val spaceForceList = listOf(
            DECEASED_SPONSOR,
            ACTIVE_DUTY,
            ACTIVE_DUTY_RESERVE,
            ACTIVE_DUTY_RECRUIT,
            CADET,
            NATIONAL_GUARD,
            ROTC,
            RESERVE_INACTIVE,
            NATIONAL_GUARD_INACTIVE,
            FORMER_ACTIVE_DUTY_TAMP,
            FAMILY_MEMBER_FORMER_ACTIVE_DUTY_TAMP,
            APPLICANT,
            FORMER_MEMBER_MATERNITY_CARE,
            NEWBORN_OF_FORMER_SERVICE_MEMBER,
            NEWBORN_OF_SPONSORS_DAUGHTER,
            NEWBORN_OF_SPOUSE_OF_FORMER_SERVICE_MEMBER,
            RETIRED_LOS,
            RETIRED_PDRL,
            RETIRED_TDRL,
            TRS_TRR_MEMBER,
            TRS_TRR_FAMILY_MEMBER,
            FAMILY_MEMBER_ACTIVE_DUTY,
            FAMILY_MEMBER_RETIRED,
            FAMILY_MEMBER_TRANSITIONAL_COMP,
            FAMILY_MEMBER_DECEASED_ACTIVE_DUTY,
            FAMILY_MEMBER_DECEASED_RETIRED,
            UNREMARRIED_FORMER_SPOUSE,
            FAMILY_MEMBER_OF_UNREMARRIED_FORMER_SPOUSE
        )

        val militaryARNShortlist = listOf(
            ACTIVE_DUTY,
            ACTIVE_DUTY_RESERVE,
            NATIONAL_GUARD
        )

        val militaryARShortlist = listOf(
            ACTIVE_DUTY,
            ACTIVE_DUTY_RESERVE
        )

        val militaryAShortlist = listOf(
            ACTIVE_DUTY
        )

        val otherShortList = listOf(
            NATO_MILITARY,
            NON_NATO_MILITARY,
            STATE_DEPARTMENT_EMPLOYEE_OUTSIDE_US,
            CONTRACT_EMPLOYEE_OR_FAMILY_MEMBER,
            OTHER_US_GOVERNMENT_BENEFICIARY,
            FOREIGN_CIVILIAN,
            FEDERAL_PRISONER
        )

        /** Statuses that apply to civilians (service K). */
        val civilianList = listOf(
            DECEASED_SPONSOR,
            STATE_DEPARTMENT_EMPLOYEE_OUTSIDE_US,
            STATE_DEPARTMENT_FAMILY_MEMBER_OUTSIDE_US,
            OTHER_FEDERAL_AGENCY_DEPARTMENT_EMPLOYEE,
            OTHER_FEDERAL_AGENCY_DEPARTMENT_FAMILY_MEMBER,
            DOD_EMPLOYEE_REMOTE_AREA_IN_US,
            DOD_FAMILY_MEMBER_REMOTE_AREA_IN_US,
            DOD_EMPLOYEE_OCCUPATIONAL_HEALTH,
            DISABILITY_RETIREMENT_EXAM,
            OTHER_FEDERAL_EMPLOYEE_OR_FAMILY_MEMBER,
            VETERANS_AFFAIRS,
            WORKERS_COMPENSATION,
            SERVICE_HOME,
            OTHER_FEDERAL_AGENCY_DEPARTMENT,
            CONTRACT_EMPLOYEE_OR_FAMILY_MEMBER,
            FEDERAL_PRISONER,
            USPHS_BENEFICIARY,
            PACIFIC_ISLAND_NATION,
            OTHER_US_GOVERNMENT_BENEFICIARY,
            FOREIGN_NATIONAL_IMET_SALES,
            NATO_MILITARY,
            NATO_FAMILY_MEMBER,
            NON_NATO_MILITARY,
            NON_NATO_FAMILY_MEMBER,
            FOREIGN_CIVILIAN,
            FOREIGN_NATIONAL_POW_INTERNEE,
            US_CIVILIAN_EMPLOYEE_OR_FAMILY_MEMBER_CC_SHP,
            NATO_COALITION_FORCES,
            NON_NATO_COALITION_FORCES,
            SECRETARY_OF_THE_ARMY_DESIGNEE,
            SECRETARY_OF_THE_NAVY_DESIGNEE,
            SECRETARY_OF_THE_AIR_FORCE_DESIGNEE,
            NDAA_2017_SECTION_717_PATIENT,
            SECRETARY_OF_THE_DOD_DESIGNEE,
            CIVILIAN_HUMANITARIAN_OR_REFUGEE,
            CIVILIAN_EMERGENCY_CARE,
            MEDICARE_CIVILIAN_EMERGENCY,
            MEDICAID_CIVILIAN_EMERGENCY,
            STATE_CHILDRENS_HEALTH_INSURANCE_PROGRAM,
            PATIENT_NOT_ELSEWHERE_CLASSIFIED,
        )

        /** Statuses that apply to foreign nationals (service R). */
        val foreignNationalsList = listOf(
            NATO_MILITARY,
            NATO_FAMILY_MEMBER,
            NON_NATO_MILITARY,
            NON_NATO_FAMILY_MEMBER,
        )

        /** Statuses that apply to Veterans Affairs (service V). */
        val veteransAffairsList = listOf(
            VA_CHOICE_SERVICE_CONNECTED,
            VA_CHOICE_NON_SERVICE_CONNECTED
        )
    }
}