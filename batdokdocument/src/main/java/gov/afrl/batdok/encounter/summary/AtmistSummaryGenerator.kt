package gov.afrl.batdok.encounter.summary

import gov.afrl.batdok.encounter.Document
import gov.afrl.batdok.encounter.vitals.*
import gov.afrl.batdok.util.NameFormatter
import java.time.Instant
import java.time.LocalDate
import java.time.Period
import java.time.ZoneId
import java.time.format.DateTimeFormatter

/**
 * Generator for ATMIST (Age, Time, Mechanism, Injuries, Signs, Treatment) summaries
 * from BATDOK Document data for QR code transfer
 */
class AtmistSummaryGenerator {

    companion object {
        private val TIME_FORMATTER = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm'Z'")
            .withZone(ZoneId.of("UTC"))
        private val TIMESTAMP_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'")
            .withZone(ZoneId.of("UTC"))
    }

    /**
     * Generate ATMIST summary from a BATDOK Document
     */
    fun generateAtmistSummary(document: Document, providerId: String? = null): AtmistSummary {


        val age = extractAge(document)
        val name = extractName(document)
        val time = extractTime(document)
        val dodId = extractDodID(document)
        val sex = extractSex(document)
        val mechanisms = extractMechanisms(document)
        val injuries = extractInjuries(document)
        val signs = extractSigns(document)
        val treatments = extractTreatments(document)
        val metadata = extractMetadata(document, providerId)

        val summary = AtmistSummary(
            age = age,
            name = name,
            time = time,
            dodId = dodId,
            sex = sex,
            mechanism = mechanisms,
            injuries = injuries,
            signs = signs,
            treatment = treatments,
            metadata = metadata
        )

        return summary
    }

    private fun extractDodID(document: Document): String? {
        val dodID = document.info.dodId

        return dodID;
    }

    /**
     * Extract patient sex/gender
     */
    private fun extractSex(document: Document): String? {
        val sex = document.info.gender

        return sex
    }

    /**
     * Extract age from patient date of birth
     */
    private fun extractAge(document: Document): String? {
        val dob = document.info.dob

        return dob?.let {
            val age = Period.between(it, LocalDate.now()).years

            age.toString()
        }
    }

    /**
     * Extract patient name
     */
    private fun extractName(document: Document): String? {
        val name = document.info.name?.toString(NameFormatter.Format.FIRST_MIDDLE_LAST)

        return name
    }

    /**
     * Extract incident/encounter time
     */
    private fun extractTime(document: Document): String? {
        val timeInfo = document.info.timeInfo

        return timeInfo?.let {
            val formattedTime = TIME_FORMATTER.format(it)

            formattedTime
        }
    }

    /**
     * Extract mechanisms of injury
     */
    private fun extractMechanisms(document: Document): List<String> {
        val mechanisms = mutableListOf<String>()


        // Extract from injuries.mechanismsOfInjury
        document.injuries.mechanismsOfInjury.values.forEach { moiList ->

            mechanisms.addAll(moiList)
        }

        val distinctMechanisms = mechanisms.distinct()

        return distinctMechanisms
    }

    /**
     * Extract injuries
     */
    private fun extractInjuries(document: Document): List<String> {
        val injuries = mutableListOf<String>()


        // Extract from injuries.injuries
        document.injuries.injuries.values.forEach { injuryList ->

            injuryList.forEach { injury ->

                injury.injury?.let { injuries.add(it) }
            }
        }

        val distinctInjuries = injuries.distinct()

        return distinctInjuries
    }

    /**
     * Extract signs and symptoms
     */
    private fun extractSigns(document: Document): AtmistSigns {
        val latestVital = document.vitals.list.maxByOrNull { it.timestamp }

        val vitals = latestVital?.let { vital ->
            val hr = vital.get<HR>()?.pulse
            val bpSys = vital.get<BloodPressure>()?.systolic
            val bpDia = vital.get<BloodPressure>()?.diastolic
            val resp = vital.get<Resp>()?.resp
            val spo2 = vital.get<SpO2>()?.spo2
            val temp = vital.get<Temp>()?.temp
            val pain = vital.get<Pain>()?.pain
            val timestamp = vital.timestamp?.let { TIMESTAMP_FORMATTER.format(it) }

            AtmistVitals(
                heartRate = hr,
                bloodPressureSystolic = bpSys,
                bloodPressureDiastolic = bpDia,
                respiratoryRate = resp,
                oxygenSaturation = spo2,
                temperature = temp,
                painScale = pain,
                timestamp = timestamp
            )
        }

        val consciousness = latestVital?.get<Avpu>()?.avpu


        // Extract observations
        val observations = mutableListOf<String>()


        document.observations.list.forEach { observation ->

            observation.name?.let { observations.add(it) }
        }

        // Extract subjective complaints
        val complaints = mutableListOf<String>()


        document.subjective.complaints.list.forEach { complaint ->

            complaint.complaint?.let { complaints.add(it) }
        }

        val distinctObservations = observations.distinct()
        val distinctComplaints = complaints.distinct()



        return AtmistSigns(
            vitals = vitals,
            consciousness = consciousness,
            observations = distinctObservations,
            complaints = distinctComplaints
        )
    }

    /**
     * Extract treatments
     */
    private fun extractTreatments(document: Document): List<String> {
        val treatments = mutableListOf<String>()


        // Extract treatments
        document.treatments.list.forEach { treatment ->

            treatment.name?.let { treatments.add(it) }
        }


        // Extract medications
        document.medicines.list.forEach { medicine ->

            val medString = buildString {
                append(medicine.name)
                medicine.volume?.let { volume ->
                    medicine.unit?.let { unit ->
                        append(" ${volume}${unit}")
                    }
                }
                medicine.route?.let { route ->
                    append(" ($route)")
                }
            }

            treatments.add(medString)
        }

        val distinctTreatments = treatments.distinct()

        return distinctTreatments
    }

    /**
     * Extract metadata
     */
    private fun extractMetadata(document: Document, providerId: String?): AtmistMetadata {
        val patientId = document.info.dodId ?: document.info.ssn
        val encounterId = document.id.toString()
        val unit = document.info.unit
        val generatedAt = TIMESTAMP_FORMATTER.format(Instant.now())



        return AtmistMetadata(
            patientId = patientId,
            encounterId = encounterId,
            provider = providerId,
            unit = unit,
            generatedAt = generatedAt,
            version = "1.0"
        )
    }
}
