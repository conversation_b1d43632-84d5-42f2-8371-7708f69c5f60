package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.proto.SharedProtobufObjects.CompatibleEnum
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto
import gov.afrl.batdok.util.toChangeOrClearEvent

enum class Service(override val protoIndex: Int, override val dataString: String, val abbreviation: String = dataString): ProtoEnum {
    AIR_FORCE(1,"U.S. Air Force", "USAF"),
    ARMY(2,"U.S. Army"),
    NAVY(3,"U.S. Navy"),
    MARINES(4,"U.S. Marines", "USMC"),
    USCG(5,"U.S. Coast Guard"),
    EPW(6,"EPW"),
    NATO(7,"NATO Military", "NATO"),
    NON_NATO(8,"Non-NATO Military", "NonNATO"),
    <PERSON><PERSON>ER(9,"Other"),
    USP<PERSON>(10,"U.S. Public Health Service"),
    CONTRACTOR(11,"U.S. DOD Contractor", "CTR"),
    US_CIVILIAN(12,"U.S. Civilian"),
    PARTNER_FORCE(13,"Partner Force", "PF"),
    NONCOALITION_FORCE(14, "Non-Coalition Force"),
    OPPOSING_FORCE_DETAINEE(15, "Opposing Force/Detainee"),
    CHILD(16, "Child"),
    OTHER_CIVILIAN(17,"Civ-Other"),
    USSF(18, "U.S. Space Force"),
    NOAA(19, "U.S. NOAA"),
    DOS(20, "DOS"),
    LOCAL_NATIONAL(21, "Local National"),
    POW(22, "Prisoner of War (POW)");

    companion object{
        fun fromProto(enum: CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?, includeAnyway: List<Service> = listOf()) = values().stringToProto(string, includeAnyway)
        fun abbreviatedString(string: String?) = values().find { it.dataString == string }?.abbreviation ?: string
        fun toEvent(enum: CompatibleEnum) = values().toChangeOrClearEvent(enum, "service")
    }
}