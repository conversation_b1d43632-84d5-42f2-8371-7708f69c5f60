package gov.afrl.batdok.encounter.commands

import gov.afrl.batdok.commands.eventHandler
import gov.afrl.batdok.commands.handler
import gov.afrl.batdok.commands.handlerWithId
import gov.afrl.batdok.commands.proto.VentCommands.*
import gov.afrl.batdok.commands.proto.logVentCommand
import gov.afrl.batdok.commands.proto.removeVentCommand
import gov.afrl.batdok.commands.proto.updateVentCommand
import gov.afrl.batdok.encounter.KnownEventTypes
import gov.afrl.batdok.encounter.VentSettings
import gov.afrl.batdok.encounter.VentValues
import gov.afrl.batdok.encounter.Vitals
import gov.afrl.batdok.encounter.ids.toByteString
import gov.afrl.batdok.encounter.ids.toDomainId
import gov.afrl.batdok.encounter.ids.toDomainIdOrElse
import gov.afrl.batdok.util.addPreviousFields
import gov.afrl.batdok.util.int64Value
import gov.afrl.batdok.util.removeDuplicates
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant

fun buildLogVentCommand(vent: VentSettings) = logVentCommand {
    this.vent = vent.toProto()
    this.id = vent.id.toByteString()
    vent.time?.let{ this.time = int64Value(it.epochSecond) }
}
fun logVentCommandHandler(ventValues: VentValues, vitals: Vitals) = handlerWithId<LogVentCommand> { docId, command ->
    ventValues += VentSettings(id.toDomainIdOrElse(command.commandId), docId, Instant.ofEpochSecond(time.value), vent, vitals)
}
fun logVentEventCommandHandler(vitals: Vitals) = eventHandler<LogVentCommand>(KnownEventTypes.TREAT.dataString) {
    "Logged " + VentSettings(it.commandId.toDomainId(), DomainId.nil(), Instant.ofEpochSecond(time.value), vent, vitals).toEvent()
}

fun buildUpdateVentCommand(vent: VentSettings, oldVentSettings: VentSettings? = null) = updateVentCommand {
    this.itemId = vent.id.toByteString()
    vent.time?.let{ this.time = int64Value(it.epochSecond) }
    this.vent = vent.toProto().removeDuplicates(oldVentSettings?.toProto())
}
fun updateVentCommandHandler(ventValues: VentValues, vitals: Vitals) = handlerWithId<UpdateVentCommand>{ docId, _ ->
    val oldProto = ventValues[itemId.toDomainId()]?.toProto()
    ventValues += VentSettings(itemId.toDomainId(), docId, Instant.ofEpochSecond(time.value), Vent.parseFrom(vent.addPreviousFields(oldProto).toByteArray()), vitals)
}
fun updateVentEventCommandHandler(ventValues: VentValues, vitals: Vitals) = eventHandler<UpdateVentCommand>(
    KnownEventTypes.TREAT.dataString,
    referenceId = { itemId.toDomainId() },
    handler =  {
        val oldProto = ventValues[itemId.toDomainId()]?.toProto()
        "Updated " + VentSettings(it.commandId.toDomainId(), DomainId.nil(), Instant.ofEpochSecond(time.value), Vent.parseFrom(vent.addPreviousFields(oldProto).toByteArray()), vitals).toEvent()
    }
)

fun buildRemoveVentCommand(id: DomainId) = removeVentCommand {
    this.ventId = id.toByteString()
}
fun removeVentCommandHandler(ventValues: VentValues) = handler<RemoveVentCommand>{
    ventValues -= ventId.toDomainId()
}
fun removeVentEventCommandHandler(ventValues: VentValues) = eventHandler<RemoveVentCommand>(
    KnownEventTypes.TREAT.dataString,
    referenceId = { ventId.toDomainId() }
) {
    val vent = ventValues[ventId.toDomainId()] ?: return@eventHandler null
    "Removed " + vent.toEvent()
}