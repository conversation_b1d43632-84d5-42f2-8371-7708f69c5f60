package gov.afrl.batdok.encounter.metadata

import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.util.*

enum class OrthopedicLocation(override val protoIndex: Int, override val dataString: String) :
    ProtoEnum {
    // cast
    RUE(1, "RUE"),
    LUE(2, "LUE"),
    RLE(3, "RLE"),
    LLE(4, "LLE"),
    PELVIS(5, "Pelvis"),

    // brace
    NECK(6, "Neck"),
    TORSO(7, "Torso");

    companion object {
        val castLocations = listOf(
            RUE,
            LUE,
            RLE,
            LLE,
            PELVIS
        )
        val braceLocations = listOf(NECK, TORSO)

        fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = OrthopedicType.values().protoToString(enum)
        fun fromString(string: String?) = OrthopedicType.values().stringToProto(string)
    }
}