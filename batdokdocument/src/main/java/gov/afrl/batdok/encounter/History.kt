package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.CommandHandler
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.commands.proto.SharedProtobufObjects.CompatibleEnum
import gov.afrl.batdok.encounter.commands.historyOfPresentIllnessCommandHandler
import gov.afrl.batdok.encounter.commands.historyOfPresentIllnessEventCommandHandler
import gov.afrl.batdok.encounter.commands.lastEventTimeCommandHandler
import gov.afrl.batdok.encounter.commands.lastEventTimeEventCommandHandler
import gov.afrl.batdok.encounter.commands.pastHistoryCommandHandler
import gov.afrl.batdok.encounter.commands.pastHistoryEventCommandHandler
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto
import java.io.Serializable
import java.time.Instant

class History(
    historyOfPresentIllness: String? = null,
    histories: Map<String?, String> = mapOf(),
    lastEventTimes: Map<String?, LastEventTime> = mapOf()
): Serializable {
    var historyOfPresentIllness = historyOfPresentIllness
        internal set
    var histories = histories
        internal set
    var lastEventTimes = lastEventTimes
        internal set

    internal fun updateHistory(type: String?, history: String?){
        history?.let {
            histories += (type to it)
        } ?: run{
            histories -= type
        }
    }

    operator fun get(type: String?) = histories[type]
    operator fun get(type: HistoryType) = histories[type.dataString]

    internal fun updateLastEventTime(type: String?, date: Instant?, extras: LastEventTime.Extras? = null) {
        date?.let {
            lastEventTimes += (type to LastEventTime(it, extras))
        } ?: run {
            lastEventTimes -= type
        }
    }
    fun getLastEventTime(type: String?) = lastEventTimes[type]

    fun getLastEventTime(type: HistoryLastEventType) = lastEventTimes[type.dataString]

    @Transient val handlers = CommandHandler().apply {
        val history = this@History
        +historyOfPresentIllnessCommandHandler(history)
        +pastHistoryCommandHandler(history)
        +lastEventTimeCommandHandler(history)
    }

    companion object{
        fun EventCommandHandler.includeHistoryEvents(){
            +historyOfPresentIllnessEventCommandHandler()
            +pastHistoryEventCommandHandler()
            +lastEventTimeEventCommandHandler()
        }
    }
}

enum class HistoryType(override val protoIndex: Int, override val dataString: String): ProtoEnum{
    SURGICAL(1, "Surgical"),
    FAMILY(2, "Family"),
    MEDICAL(3, "Medical"),
    SOCIAL(4, "Social"),
    ;

    companion object{
        fun fromProto(enum: CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)
    }
}

enum class HistoryLastEventType(override val protoIndex: Int, override val dataString: String): ProtoEnum{
    MENSTRUAL(1, "Menstrual Period"),
    BOWEL(2, "Bowel Movement"),
    BLOOD_DONATION(3, "Blood Donation");

    companion object{
        fun fromProto(enum: CompatibleEnum) = HistoryLastEventType.values().protoToString(enum)
        fun fromString(string: String?) = HistoryLastEventType.values().stringToProto(string)
    }
}

