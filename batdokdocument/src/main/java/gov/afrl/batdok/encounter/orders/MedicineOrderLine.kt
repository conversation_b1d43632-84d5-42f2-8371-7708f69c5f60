package gov.afrl.batdok.encounter.orders

import gov.afrl.batdok.encounter.Contact
import gov.afrl.batdok.encounter.Interval
import gov.afrl.batdok.encounter.ids.OrderLineId
import gov.afrl.batdok.encounter.metadata.Signature
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant
import java.time.temporal.ChronoUnit

data class MedicineOrderLine(
    override val id: OrderLineId = DomainId.create(),
    override val timestamp: Instant = Instant.now().truncatedTo(ChronoUnit.SECONDS),
    override val instructions: String,
    override val orderType: String,
    override val orderStatus: String = OrderStatus.ORDERED.dataString,
    override val frequency: Interval,
    override val lastOccurrence: Instant? = null,
    override val provider: Contact? = null,
    override val signature: Signature? = null,
    val orderedMedicine: OrderedMedicine
) : IOrderLine {
    override val title = orderedMedicine.title
}