package gov.afrl.batdok.encounter.metadata

import gov.afrl.batdok.commands.proto.DeathDeclarationCommands.ChangeDeathDeclaration
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.changeDeathDeclaration
import gov.afrl.batdok.util.*
import java.io.Serializable
import java.time.Instant

data class DeathDeclaration(
    val timeOfDeath: Instant,
    val declarer: String,
    val certification: String,
    val verificationMethod: String,
): Serializable{
    internal constructor(proto: ChangeDeathDeclaration): this(
        Instant.ofEpochSecond(proto.timeOfDeath.value),
        proto.declarer.value,
        CertificationLevel.fromProto(proto.certificationLevel) ?: "",
        VerificationMethod.fromProto(proto.verificationMethod) ?: ""
    )

    fun toEventString(): String{
        val timeDisplay = timeOfDeath.format(Patterns.mdyhm_24_space_comma_space_colon)
        return "$timeDisplay Declarer: $declarer, Level of Medicine: $certification, Verification Method: $verificationMethod"
    }

    fun toProto() = changeDeathDeclaration {
        this.timeOfDeath = int64Value(<EMAIL>)
        this.declarer = stringValue(<EMAIL>)
        this.certificationLevel = CertificationLevel.fromString(<EMAIL>)
        this.verificationMethod = VerificationMethod.fromString(<EMAIL>)
    }
}

enum class CertificationLevel(override val protoIndex: Int, override val dataString: String): ProtoEnum{
    ;
    companion object{
        fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)
    }
}

enum class VerificationMethod(override val protoIndex: Int, override val dataString: String): ProtoEnum{
    ;
    companion object{
        fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)
    }
}