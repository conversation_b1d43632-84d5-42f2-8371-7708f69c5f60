package gov.afrl.batdok.encounter.treatment

import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.TreatmentCommands
import gov.afrl.batdok.commands.proto.fingerThor
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

class FingerThorData(val location: String? = null): TreatmentData{
    override fun toProtobuf(): Message = fingerThor{
        location = Location.fromString(<EMAIL>)
    }

    override fun toDetailString(): String {
        return location ?: ""
    }

    companion object{
        fun fromProtobuf(fingerThor: TreatmentCommands.FingerThor): FingerThorData{
            return FingerThorData(Location.fromProto(fingerThor.location)?:"")
        }
    }

    enum class Location(override val protoIndex: Int, override val dataString: String): ProtoEnum {
        LEFT(1, "Left side"),
        RIGHT(2, "Right side"),
        ;

        companion object{
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }
}