package gov.afrl.batdok.encounter.treatment

import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.*
import gov.afrl.batdok.util.*

class LineData(
    val type: String? = null,
    val side: String? = null,
    val location: String? = null,
    val gauge: Float? = null,
    val gaugeUnit: String? = null,
    val size: Float? = null,
    val sizeUnit: String? = null,
    val subtype: String? = null
) : TreatmentData {
    constructor(lineData: TreatmentCommands.LineData) : this(
        Type.fromProto(lineData.type),
        Side.fromProto(lineData.side),
        Location.fromProto(lineData.location),
        lineData.gaugeOrNull?.toPrimitive(),
        lineData.gaugeUnitOrNull?.toPrimitive(),
        lineData.sizeOrNull?.toPrimitive(),
        lineData.sizeUnitOrNull?.toPrimitive(),
        Subtype.fromProto(lineData.subtype)
    )

    override fun toProtobuf(): Message = lineData {
        type = Type.fromString(<EMAIL>)
        side = Side.fromString(<EMAIL>)
        location = Location.fromString(<EMAIL>)
        gauge = nullableFloatValue(<EMAIL>)
        gaugeUnit = compatibleEnum(<EMAIL>)
        size = nullableFloatValue(<EMAIL>)
        sizeUnit = compatibleEnum(<EMAIL>)
        subtype = Subtype.fromString(<EMAIL>)
    }

    override fun toDetailString(): String {
        return listOfNotNull(
            type.toDetailString("Type"),
            subtype.toDetailString("SubType"),
            listOfNotNull(side, location).joinToString(" ").toDetailString("Location"),
            gauge.toDetailString("Gauge", gaugeUnit),
            size.toDetailString("Size", sizeUnit)
        ).joinToString(", ")
    }

    enum class Side(override val protoIndex: Int, override val dataString: String) : ProtoEnum {
        LEFT(1, "Left"),
        RIGHT(2, "Right"),
        ;

        companion object {
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }

    enum class Location(override val dataString: String, override val protoIndex: Int) : ProtoEnum {
        @Deprecated("Use HUMERAL_HEAD")
        HUMERUS("Humerus", 1),

        @Deprecated("Use proximal or distal Tibia enum")
        TIBIA("Tibia", 2),
        STERNUM("Sternum", 3),
        HAND("Hand", 4),
        ARM("Arm", 5),
        EJ("EJ", 6),
        WRIST("Wrist", 7),
        GROIN("Groin", 8),
        HUMERAL_HEAD("Humeral Head", 9),
        PROXIMAL_TIBIA("Proximal Tibia", 10),
        DISTAL_TIBIA("Distal Tibia", 11),
        FEM("Fem", 12),
        IJ("IJ", 13),
        SUBCLAV("Subclav", 14),
        FOOT("Foot", 15),
        LEG("Leg", 16)
        ;

        companion object {
            fun find(dataString: String?) = entries.find { it.dataString == dataString }
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }

    enum class Type(override val protoIndex: Int, override val dataString: String) : ProtoEnum {
        ARTERIAL(1, "Arterial"),
        PERIPHERAL(2, "Peripheral"),
        CENTRAL(3, "Central"),
        EPIDURAL(4, "Epidural"),
        IO(5, "IO"),
        ;

        companion object {
            fun find(dataString: String?) = entries.find { it.dataString == dataString }
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }

    enum class Subtype(override val protoIndex: Int, override val dataString: String) : ProtoEnum {
        FAST_1(1, "Fast-1"),
        EZ_IO(2, "EZ-IO"),
        TRIPLE_LUMEN(3, "Triple Lumen"),
        CORDIS(4, "Cordis"),
        PA_CATHETER(5, "PA Catheter"),
        CVC_SINGLE_LUMEN(6, "CVC (Single lumen)"),
        CVC_DOUBLE_LUMEN(7, "CVC (Double lumen)"),
        CVC_TRIPLE_LUMEN(8, "CVC (Triple lumen)"),
        CVC_QUAD_LUMEN(9, "CVC (Quad lumen)"),
        ECMO_OUTFLOW(10, "ECMO Outflow"),
        ECMO_INFLOW_VENOUS(11, "ECMO Inflow Venous"),
        ECMO_INFLOW_ARTERIAL(12, "ECMO Inflow Arterial"),
        TUNNELED(13, "Tunneled"),
        ;

        companion object {
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }

    enum class Size(val colorString: String, val sizeInMm: Int) {
        RED("Red", 15),
        BLUE("Blue", 25),
        YELLOW("Yellow", 45),
    }

    companion object {
        val ValidPeripheralLocations = listOf(
            Location.HAND, Location.ARM, Location.EJ
        )
        val ValidArterialLineLocations = listOf(
            Location.WRIST, Location.GROIN
        )
        val ValidCentralSubtypes = listOf(
            Subtype.PA_CATHETER,
            Subtype.CVC_SINGLE_LUMEN,
            Subtype.CVC_DOUBLE_LUMEN,
            Subtype.CVC_TRIPLE_LUMEN,
            Subtype.CVC_QUAD_LUMEN,
            Subtype.ECMO_OUTFLOW,
            Subtype.ECMO_INFLOW_VENOUS,
            Subtype.ECMO_INFLOW_ARTERIAL,
            Subtype.TUNNELED
        )
        val ValidCentralLocations = listOf(
            Location.FEM,
            Location.IJ,
            Location.SUBCLAV
        )
        val ValidIOSubtypes = listOf(
            Subtype.FAST_1,
            Subtype.EZ_IO,
        )
        val ValidIOLocations = listOf(
            Location.HUMERAL_HEAD,
            Location.PROXIMAL_TIBIA,
            Location.DISTAL_TIBIA,
            Location.FOOT,
            Location.LEG,
            Location.STERNUM
        )
    }

}