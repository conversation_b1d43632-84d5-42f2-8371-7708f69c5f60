package gov.afrl.batdok.encounter.observation

import gov.afrl.batdok.commands.proto.Observations
import gov.afrl.batdok.commands.proto.descriptionOrNull
import gov.afrl.batdok.commands.proto.ultrasoundData
import gov.afrl.batdok.util.nullableStringValue
import gov.afrl.batdok.util.toPrimitive

data class UltrasoundData(val description: String? = null): ObservationData {
    constructor(proto: Observations.UltrasoundData): this(proto.descriptionOrNull?.toPrimitive())
    override fun toProtobuf() = ultrasoundData {
        description = nullableStringValue(<EMAIL>)
    }

    override fun toDetailString(): String {
        return this.description ?: ""
    }
}