package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.CommandHandler
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.commands.*
import gov.afrl.batdok.encounter.panel.Panel
import gov.afrl.batdok.util.ILinkableList
import gov.afrl.batdok.util.SortedLinkableList
import java.io.Serializable

@Deprecated("Labs need to be logged individually. Use Labs instead", replaceWith = ReplaceWith("Labs"))
class Panels: Serializable, ILinkableList<Panel> by SortedLinkableList() {
    val panels: List<Panel>
        get() = list

    @Transient val handlers = CommandHandler().apply {
        val labs = this@Panels
        +logPanelCommandHandler(labs)
        +updatePanelsCommandHandler(labs)
        +removePanelCommandHandler(labs)
    }

    companion object{
        fun EventCommandHandler.includePanelHandlers(labs: Panels){
            +logPanelEventCommandHandler()
            +updatePanelEventCommandHandler(labs)
            +removePanelEventCommandHandler(labs)
        }
    }
}