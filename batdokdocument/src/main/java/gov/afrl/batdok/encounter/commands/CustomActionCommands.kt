package gov.afrl.batdok.encounter.commands

import gov.afrl.batdok.commands.eventHandler
import gov.afrl.batdok.commands.handler
import gov.afrl.batdok.commands.handlerWithId
import gov.afrl.batdok.commands.proto.CustomActionCommands
import gov.afrl.batdok.commands.proto.addCustomAction
import gov.afrl.batdok.commands.proto.removeCustomAction
import gov.afrl.batdok.commands.proto.updateCustomAction
import gov.afrl.batdok.encounter.CustomAction
import gov.afrl.batdok.encounter.CustomActions
import gov.afrl.batdok.encounter.KnownEventTypes
import gov.afrl.batdok.encounter.ids.CustomActionId
import gov.afrl.batdok.encounter.ids.toByteString
import gov.afrl.batdok.encounter.ids.toDomainId
import java.time.Instant

fun buildAddCustomActionCommand(customAction: CustomAction) = addCustomAction {
    this.customAction = customAction.toProto()
}

fun addCustomActionCommandHandler(customActions: CustomActions) = handlerWithId<CustomActionCommands.AddCustomAction> { _, command ->
    customActions += CustomAction(customActionProto = this.customAction, callsign = command.callsign)
}

fun addCustomActionCommandEventHandler() =
    eventHandler<CustomActionCommands.AddCustomAction>(
        KnownEventTypes.ACTION.toString(),
        timestampGenerator = { Instant.ofEpochSecond(customAction.timestamp) },
        referenceId = {customAction.customActionId.toDomainId()}
    ) { command ->
        val customAction = CustomAction(customActionProto = this.customAction, callsign = command.callsign)
        "Added Action:\n${customAction.message}"
    }

fun buildUpdateCustomActionCommand(customAction: CustomAction) = updateCustomAction {
    this.customAction = customAction.toProto()
}

fun updateCustomActionCommandHandler(customActions: CustomActions) = handlerWithId<CustomActionCommands.UpdateCustomAction> { _, command ->
    val newCustomAction = CustomAction(customActionProto = this.customAction, callsign = command.callsign)
    customActions += newCustomAction
}

fun updateCustomActionCommandEventHandler(customActions: CustomActions) = eventHandler<CustomActionCommands.UpdateCustomAction>(
    KnownEventTypes.OTHER.dataString)
{command ->
    val newCustomAction = CustomAction(customActionProto = this.customAction, callsign = command.callsign)
    val oldCustomAction = customActions[newCustomAction.id]
    if (oldCustomAction != null) {
        "Updated Action from:\n\"${oldCustomAction.message}\"\nto:\n\"${newCustomAction.message}\""
    } else {
        "Added Action:\n${newCustomAction.message}"
    }
}

fun buildRemoveCustomActionCommand(customActionId: CustomActionId) = removeCustomAction {
    this.customActionId = customActionId.toByteString()
}

fun removeCustomActionCommandHandler(customActions: CustomActions) = handler<CustomActionCommands.RemoveCustomAction> {
    val customActionId: CustomActionId = this.customActionId.toDomainId()
    customActions.removeItem(customActionId, Instant.ofEpochSecond(it.timestamp))
}

fun removeCustomActionCommandEventHandler(customActions: CustomActions) = eventHandler<CustomActionCommands.RemoveCustomAction>(
    KnownEventTypes.OTHER.dataString,)
{
    val customAction = customActions[this.customActionId.toDomainId<CustomActionId>()]

    if (customAction != null) {
        "Removed Action:\n\"${customAction.message}\""
    } else {
        null
    }
}