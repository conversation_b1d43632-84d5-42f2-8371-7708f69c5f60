package gov.afrl.batdok.encounter.treatment

import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.TreatmentCommands.Fasciotomy
import gov.afrl.batdok.commands.proto.fasciotomy
import gov.afrl.batdok.util.compatibleEnum
import gov.afrl.batdok.util.toPrimitive

class FasciotomyData(val location: String? = null): TreatmentData{
    override fun toProtobuf(): Message = fasciotomy {
        this.location = compatibleEnum(<EMAIL>)
    }

    override fun toDetailString(): String {
        return location ?: ""
    }

    companion object{
        fun fromProtobuf(fasciotomy: Fasciotomy) = FasciotomyData(
            fasciotomy.location.toPrimitive(),
        )
    }
}