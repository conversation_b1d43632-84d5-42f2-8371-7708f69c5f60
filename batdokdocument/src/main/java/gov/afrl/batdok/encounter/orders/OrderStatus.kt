package gov.afrl.batdok.encounter.orders

import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto
import gov.afrl.batdok.util.toChangeOrClearEvent

enum class OrderStatus(override val dataString: String, override val protoIndex: Int):ProtoEnum {

    ORDERED("Ordered", 1),
    IN_PROGRESS("In Progress", 2),
    ON_HOLD("On Hold", 3),
    COMPLETE("Complete", 4),
    DISCONTINUED("Discontinued", 5),
    REFUSED("Refused", 6);

    companion object{
        fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = OrderStatus.values().protoToString(enum) ?: ORDERED.dataString
        fun fromString(string: String?) = OrderStatus.values().stringToProto(string)
        fun toEvent(enum: SharedProtobufObjects.CompatibleEnum) = OrderStatus.values()
            .toChangeOrClearEvent(enum, "order status")
    }
}