package gov.afrl.batdok.encounter.treatment

import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.TreatmentCommands.O2
import gov.afrl.batdok.commands.proto.o2
import gov.afrl.batdok.util.*

class O2Data(
    @Deprecated("Use lpm", replaceWith = ReplaceWith("lpm")) val volume: Float? = null,
    @Deprecated("Use route", replaceWith = ReplaceWith("route")) val deliveryMethod: String? = null,
    val lpm: Float? = null,
    val targetSpO2: Int? = null,
    val fiO2: Float? = null,
    val route: String? = null
): TreatmentData{
    override fun toProtobuf(): Message = o2 {
        this.volume = nullableFloatValue(<EMAIL>)
        this.deliveryMethod = DeliveryMethod.fromString(<EMAIL>)
        this.lpm = nullableFloatValue(<EMAIL>)
        this.targetSpo2 = nullableInt32Value(this@O2Data.targetSpO2)
        this.fio2 = nullableFloatValue(this@O2Data.fiO2)
        this.route = O2Route.fromString(<EMAIL>)
    }

    override fun toDetailString(): String {
        return listOfNotNull(
            deliveryMethod.toDetailString("Source"),
            volume?.takeUnless { it <= 0 }?.toString().toDetailString("LPM"),
            lpm?.takeUnless { it <= 0 }?.toString().toDetailString("LPM"),
            targetSpO2?.takeUnless { it <= 0 }?.toString().toDetailString("Target SPO2"),
            fiO2?.takeUnless { it <= 0 }?.toString().toDetailString("FIO2"),
            route.toDetailString("Route")
        ).joinToString(", ")
    }

    companion object{
        fun fromProtobuf(o2: O2) = O2Data(
            volume = o2.volume.toPrimitive(),
            deliveryMethod =  DeliveryMethod.fromProto(o2.deliveryMethod),
            lpm = o2.lpm.toPrimitive(),
            targetSpO2 = o2.targetSpo2.toPrimitive(),
            fiO2 = o2.fio2.toPrimitive(),
            route = O2Route.fromProto(o2.route)
        )
    }

    @Deprecated("Use enum class O2Route", replaceWith = ReplaceWith("O2Route"))
    enum class DeliveryMethod(override val protoIndex: Int, override val dataString: String): ProtoEnum{
        BVM(1, "BVM"),
        NRB(2, "NRB"),
        NC(3, "NC"),
        VENT(4, "Vent"),
        ;

        companion object{
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }

    enum class O2Route(override val protoIndex: Int, override val dataString: String): ProtoEnum{
        VENTILATOR(1, "Ventilator"),
        NC(2, "Nasal Canula (NC)"),
        FM(3, "Face Mask (FM)"),
        NRB(4, "Non-Rebreather (NRB)"),
        VENTURI_MASK(5, "Venturi Mask"),
        ETT(6, "ETT"),
        TRACH(7, "Trach")
        ;

        companion object{
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }
}