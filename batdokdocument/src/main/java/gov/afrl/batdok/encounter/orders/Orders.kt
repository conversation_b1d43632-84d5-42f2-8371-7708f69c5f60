package gov.afrl.batdok.encounter.orders

import gov.afrl.batdok.commands.CommandHandler
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.commands.orders.*
import gov.afrl.batdok.util.ILinkableList
import gov.afrl.batdok.util.SortedLinkableList
import java.io.Serializable
import java.time.Instant

/**
 * Maintains the list of orders, sorted by timestamp (newest to oldest)
 */
class Orders : Serializable, ILinkableList<IOrderLine> by SortedLinkableList() {
    @Deprecated("Use Orders.list to get all IOrderLines.")
    val orderLines: List<OrderLine>
        get() = list.filterIsInstance<OrderLine>()

    @Deprecated("Use getSortedOrderLinesInstances", ReplaceWith("getSortedOrderLinesInstances<IOrderLine>()"))
    fun getSortedOrderLines(): List<OrderLine> {
        return getSortedOrderLinesInstances<OrderLine>()
    }
    inline fun <reified T: IOrderLine>getSortedOrderLinesInstances(): List<T>{
        return list.filterIsInstance<T>().sortedWith (compareBy(
            {
                when(it.orderStatus) {
                    OrderStatus.IN_PROGRESS.dataString, OrderStatus.ORDERED.dataString -> 0
                    OrderStatus.ON_HOLD.dataString -> 1
                    OrderStatus.REFUSED.dataString -> 2
                    OrderStatus.COMPLETE.dataString -> 3
                    OrderStatus.DISCONTINUED.dataString -> 4
                    else -> 999
                }
            },
            {
                when {
                    //Sort order: Once, Interval, Continuous, Per Instruction, PRN
                    it.frequency.toString() == "Once" -> 0
                    it.frequency.isHourMinuteInterval() -> 1
                    it.frequency.toString() == "Per Instruction" -> 2
                    it.frequency.toString() == "PRN" -> 3
                    else -> 999
                }
            },
            {
                when(it.orderStatus) {
                    OrderStatus.IN_PROGRESS.dataString -> 0
                    OrderStatus.ORDERED.dataString -> 1
                    else -> 999
                }
            },
            {
                if (it.lastOccurrence != null) {
                    // then sort by due date
                    it.frequency.nextDue(it.lastOccurrence!!)
                } else {
                    // no last occurrence, put it after the order lines that do have one
                    Instant.MAX
                }
            }
        ))
    }

    @Transient val handlers = CommandHandler().apply {
        val orders = this@Orders
        +addOrderLineCommandHandler(orders)
        +addMedicineOrderLineCommandHandler(orders)
        +updateOrderLineCommandHandler(orders)
        +updateMedicineOrderLineCommandHandler(orders)
        +updateOrderLineStatusCommandHandler(orders)
        +updateMedicineOrderLineStatusCommandHandler(orders)
    }

    companion object {
        internal fun EventCommandHandler.includeOrdersEvents(orders: Orders) {
            +addOrderLineCommandEventHandler()
            +addMedicineOrderLineCommandEventHandler()
            +updateOrderLineCommandEventHandler(orders)
            +updateMedicineOrderLineCommandEventHandler(orders)
            +updateOrderLineStatusCommandEventHandler(orders)
            +updateMedicineOrderLineStatusCommandEventHandler(orders)
        }
    }
}