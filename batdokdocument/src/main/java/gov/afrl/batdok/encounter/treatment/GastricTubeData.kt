package gov.afrl.batdok.encounter.treatment

import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.TreatmentCommands
import gov.afrl.batdok.commands.proto.gastricTube
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

class GastricTubeData(val type: String? = null,
                      val side: String? = null,
                      val suctionType: String? = null,
                      val interval: String? = null): TreatmentData{
    override fun toProtobuf(): Message = gastricTube {
        type = Type.fromString(<EMAIL>)
        side = Side.fromString(<EMAIL>)
        suctionType = SuctionType.fromString(<EMAIL>)
        interval = Interval.fromString(<EMAIL>)
    }

    override fun toDetailString(): String {
        return listOfNotNull(
            type.toDetailString("Type"),
            side.toDetailString("Side"),
            suctionType.toDetailString("Suction Type"),
            interval.toDetailString("Interval")
        ).joinToString(", ")
    }

    companion object{
        fun fromProtobuf(gastricTube: TreatmentCommands.GastricTube): GastricTubeData{
            return GastricTubeData(
                Type.fromProto(gastricTube.type),
                Side.fromProto(gastricTube.side),
                SuctionType.fromProto(gastricTube.suctionType),
                Interval.fromProto(gastricTube.interval)
            )
        }
    }

    enum class Type(override val protoIndex: Int, override val dataString: String): ProtoEnum {
        NASAL(1, "Nasal"),
        ORAL(2, "Oral"),
        ;

        companion object{
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }

    enum class Side(override val protoIndex: Int, override val dataString: String): ProtoEnum {
        LEFT(1, "Left side"),
        RIGHT(2, "Right side"),
        ;

        companion object{
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }

    enum class SuctionType(override val protoIndex: Int, override val dataString: String): ProtoEnum {
        SUCTION(1, "Suction"),
        GRAVITY(2, "Gravity"),
        ;

        companion object{
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }

    enum class Interval(override val protoIndex: Int, override val dataString: String): ProtoEnum {
        INTERMITTENT(1, "Intermittent"),
        CONTINUOUS(2, "Continuous"),
        ;

        companion object{
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }
}