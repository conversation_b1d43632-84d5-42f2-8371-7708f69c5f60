package gov.afrl.batdok.encounter.ids

import mil.af.afrl.batman.batdokid.DomainId

class DocumentId(unique: ByteArray): DomainId(unique)

class CommandId(unique: ByteArray): DomainId(unique)
class EncounterVitalId(unique: ByteArray): DomainId(unique)
class EquipmentId(unique: ByteArray): DomainId(unique)
class EventId(unique: ByteArray): DomainId(unique)
@Deprecated("Use IntakeOutputId", replaceWith = ReplaceWith("IntakeOutputId"))
class InputOutputId(unique: ByteArray): DomainId(unique)
class IntakeOutputId(unique: ByteArray): DomainId(unique)
class LabId(unique: ByteArray): DomainId(unique)
@Deprecated("Use LabId", replaceWith = ReplaceWith("LabId"))
class PanelId(unique: ByteArray): DomainId(unique)
class ObservationId(unique: ByteArray): DomainId(unique)
class OwnerId(unique: ByteArray): DomainId(unique)
class TreatmentId(unique: ByteArray): DomainId(unique)
class VentId(unique: ByteArray): DomainId(unique)
class MedicineId(unique: ByteArray): DomainId(unique)
class OrderLineId(unique: ByteArray): DomainId(unique)
class CustomActionId(unique: ByteArray): DomainId(unique)
class NoteId(unique: ByteArray): DomainId(unique)
class ProblemId(unique: ByteArray): DomainId(unique)