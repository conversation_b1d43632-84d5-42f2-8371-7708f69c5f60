package gov.afrl.batdok.encounter.treatment

import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.TreatmentCommands
import gov.afrl.batdok.commands.proto.chestSeal
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

class ChestSealData(val location: String? = null): TreatmentData{
    override fun toProtobuf(): Message = chestSeal{
        location = Location.fromString(<EMAIL>)
    }

    override fun toDetailString(): String {
        return location ?: ""
    }

    companion object{
        fun fromProtobuf(chestSeal: TreatmentCommands.ChestSeal): ChestSealData{
            return ChestSealData(Location.fromProto(chestSeal.location)?:"")
        }
    }

    enum class Location(override val protoIndex: Int, override val dataString: String): ProtoEnum {
        LEFT_FRONT(1, "Left Front"),
        RIGHT_FRONT(2, "Right Front"),
        LEFT_BACK(3, "Left Back"),
        RIGHT_BACK(4, "Right Back"),
        ;

        companion object{
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }
}