package gov.afrl.batdok.encounter.observation

import gov.afrl.batdok.commands.proto.Observations
import gov.afrl.batdok.commands.proto.descriptionOrNull
import gov.afrl.batdok.commands.proto.textData
import gov.afrl.batdok.util.nullableStringValue
import gov.afrl.batdok.util.toPrimitive

data class TextData(val description: String? = null): ObservationData {
    constructor(proto: Observations.TextData): this(proto.descriptionOrNull?.toPrimitive())
    override fun toProtobuf() = textData {
        description = nullableStringValue(<EMAIL>)
    }

    override fun toDetailString(): String {
        return this.description ?: ""
    }
}