package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.CommandHandler
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.encounter.commands.addRemoveFromLinkCommandHandler
import gov.afrl.batdok.encounter.commands.createLinkCommandHandler
import gov.afrl.batdok.encounter.commands.deleteLinkCommandHandler
import gov.afrl.batdok.encounter.commands.updateLinkCommentCommandHandler
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.util.*
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant

class LinkId(val bytes: ByteArray): DomainId(bytes)

data class Link(
    val ids: List<DomainId>,
    val relationship: String = CommonLinkRelationships.UNKNOWN.dataString,
    override val id: LinkId = DomainId.create(),
    override val timestamp: Instant = Instant.now(),
    override val documentId: DocumentId = DomainId.nil()
): DocumentItem<LinkId>(id, documentId, relationship, timestamp)

class Links: ICategorizedItemList<Link> by CategorizedItemList(){
    @Transient internal val handlers = CommandHandler().apply {
        val links = this@Links
        +createLinkCommandHandler(links)
        +addRemoveFromLinkCommandHandler(links)
        +updateLinkCommentCommandHandler(links)
        +deleteLinkCommandHandler(links)
    }
    companion object{
        internal fun EventCommandHandler.includeLinkEvents(document: Document) {
        }
    }

    internal fun Linkable.getLinksContainingThis(relationshipFilter: String? = null) = list.filter {
        it.ids.contains(id) && (relationshipFilter == null || it.relationship == relationshipFilter)
    }

    /**
     *  Find linkages that include the provided [id].
     *  Either the link id is what you're looking for, or the ids inside the link are
     */
    @Deprecated("Internally, either use [id] to get a link by its id or getLinksContainingItem for links containing that item. Externally, use functions provided by the object you're using.")
    fun getLinks(id: DomainId): List<Link> {
        return list.filter {
            it.id == id || it.ids.contains(id)
        }
    }

    /**
     *  Find linkages that include any of the provided [ids].
     *  Either the link id is what you're looking for, or the ids inside the link are
     */
    @Deprecated("Internally, either use [id] to get a link by its id or getLinksContainingItem for links containing that item. Externally, use functions provided by the object you're using.")
    fun getLinks(vararg ids: DomainId): List<Link> = getLinks(ids.toList())
    /**
     *  Find linkages that include any of the provided [ids].
     *  Either the link id is what you're looking for, or the ids inside the link are
     */
    @Deprecated("Internally, either use [id] to get a link by its id or getLinksContainingItem for links containing that item. Externally, use functions provided by the object you're using.")
    fun getLinks(ids: Collection<DomainId>): List<Link>{
        return ids.flatMap { getLinks(it) }.distinctBy { it.id }
    }

    internal fun removeLinkedItem(id: DomainId, timestamp: Instant = Instant.now()) {
        val linksToEdit = mutableListOf<Link>()
        val linksToDelete = mutableListOf<Link>()

        list.forEach {
            if(id in it.ids){
                if(it.ids.size <= 2){
                    // If after removing there will only be one item left, delete the link
                    linksToDelete += it
                }else{
                    // Otherwise, just remove the link from the list
                    linksToEdit += it
                }
            }
        }

        this += linksToEdit.map { it.copy(ids = it.ids - id) }

        linksToDelete.forEach {
            removeItem(it.id, timestamp)
        }
    }
}


enum class CommonLinkRelationships(override val protoIndex: Int, override val dataString: String): ProtoEnum {
    ASSOC_WITH_COMPLAINT(1, "Associated with Complaint"),
    @Deprecated("Just use a string, it doesn't need to be an enum") UNKNOWN(2, "Unknown relationship"),
    NOTE(3, "Note"),
    @Deprecated("Use Just Note instead", replaceWith = ReplaceWith("NOTE")) NOTE_TO_ORDERLINE(3, "Associate Note to OrderLine"),
    @Deprecated("Associate all items with orderlines the same way", replaceWith = ReplaceWith("FULFILL_ORDERLINE")) ACTION_TO_ORDERLINE(4, "Associate Action to OrderLine"),
    CBC_PANEL(5, "Complete Blood Count Panel"),
    BASIC_METABOLIC_PANEL(6, "Basic Metabolic Panel"),
    COMPREHENSIVE_METABOLIC_PANEL(7, "Comprehensive Metabolic Panel"),
    BLOOD_GAS_ANALYSIS_PANEL(8, "Blood Gas Analysis Panel"),
    COAGULATION_PANEL(9, "Coagulation Panel"),
    @Deprecated("Associate all items with orderlines the same way", replaceWith = ReplaceWith("FULFILL_ORDERLINE")) MEDICINE_TO_ORDERLINE(10, "Associate Medicine to OrderLine"),
    FULFILL_ORDERLINE(11, "Associated with OrderLine"),
    SYMPTOMS(12, "Symptoms"),
    RED_FLAGS(13, "Red Flags")
    ;
    companion object{
        fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum) ?: UNKNOWN.dataString
        fun fromString(string: String?, includeStringAnyway: List<CommonLinkRelationships> = listOf()) =
            values().stringToProto(string, includeStringAnyway)
    }
}