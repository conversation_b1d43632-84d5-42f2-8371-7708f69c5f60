package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.proto.SharedProtobufObjects.CompatibleEnum
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

enum class CommonAllergies(override val protoIndex: Int, override val dataString: String): ProtoEnum {
    NKA(1, "No Known Allergies"),
    ANTIBIOTICS(2, "Antibiotics"),
    ASPIRIN(3, "Aspirin"),
    NSAID(4, "NSAID"),
    IBUPROFEN(5, "Ibuprofen"),
    NAPROXEN(6, "Naproxen"),
    SULFA_DRUGS(7, "Sulfa Drugs"),
    ANTI_SEIZURE_DRUGS(8, "Anti-seizure Drugs"),
    ATRACURIUM(9, "Atracurium"),
    SUCCINYLCHOLINE(10, "Succinylcholine"),
    VECURONIUM(11, "Vecuronium"),
    PENICILLIN(12, "Penicillin"),
    OPIATES(13, "Opiates"),
    ACETAMINOPHEN(14, "Acetaminophen"),
    AMOXICILLIN(15, "Amoxicillin"),
    CODEINE(16, "Codeine"),
    ERYTHROMYCIN(17, "Erythromycin"),
    MORPHINE(18, "Morphine"),
    SULFAMETHOXAZOLE(19, "Sulfamethoxazole"),
    TETRACYCLINE(20, "Tetracycline"),
    TRIMETHOPRIM(21, "Trimethoprim"),
    HYMENOPTRA(22, "Hymenoptra"),
    SHELLFISH(23, "Shellfish");

    companion object{
        fun fromProto(enum: CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?, includeStringAnyway: List<CommonAllergies> = listOf()) =
            values().stringToProto(string, includeStringAnyway)
    }
}