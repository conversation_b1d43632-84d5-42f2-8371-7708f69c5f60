package gov.afrl.batdok.encounter.metadata

import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.util.*

enum class OrthopedicSubtype(override val protoIndex: Int, override val dataString: String) :
    ProtoEnum {
    // cast
    PLASTER(1, "Plaster"),
    FIBERGLASS(2, "Fiberglass"),
    SPICA(3, "Spica"),
    SPICA_SPLIT_BIVALVE(4, "Spica Split Bivalve"),
    PLASTER_BIVALVE(5, "Plaster Bivalve"),
    FIBERGLASS_BIVALVE(6, "Fiberglass Bivalve"),

    // wheelchair
    ELECTRIC(7, "Electric"),
    MANUAL(8, "Manual");

    companion object {
        val castSubtypes = listOf(
            PLASTER,
            FIBERGLASS,
            SPICA,
            SPICA_SPLIT_BIVALVE,
            PLASTER_BIVALVE,
            FIBERGLASS_BIVALVE
        )
        val wheelchairSubtypes = listOf(ELECTRIC, MANUAL)

        fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = OrthopedicType.values().protoToString(enum)
        fun fromString(string: String?) = OrthopedicType.values().stringToProto(string)
    }
}