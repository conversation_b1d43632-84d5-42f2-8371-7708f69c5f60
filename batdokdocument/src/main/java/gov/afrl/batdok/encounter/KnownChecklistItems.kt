package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

enum class KnownChecklistItems(
    override val protoIndex: Int,
    override val dataString: String,
    val defaultInterval: String = "Once",
    val category: String? = null
): ProtoEnum {

    //region PFC Nursing Care Plan Items

    //region Vital Checks
    VITALS(
        1,
        "Check BP/HR/RR/T/SPO2/ETCO2",
        "Q1H",
        category = "Vitals"
    ),

    PERIPHERAL_PULSES(
        2,
        "Peripheral Pulses",
        "Q1H",
        category = "Vitals"
    ),

    CHECK_SKIN_TEMP_AND_COLOR(
        3,
        "Check Skin Temp and Color",
        "Q1H",
        category = "Vitals"
    ),
    CHECK_LACTATE(4, "Check Lactate", "Q4H", category = "Vitals"),
    CHECK_BLOOD_GLUCOSE(5, "Check Blood Glucose", "Q8H", category = "Vitals"),

    //endregion

    //region Ins/Outs Checks
    CHECK_DRIP_RATESFLUIDS_IN(
        6,
        "Check Drip Rates/Fluids In",
        "Q1H",
        category = "Ins/Outs"
    ),

    CHECK_URINE_OUTPUT(7, "Check Urine Output", "Q1H", category = "Ins/Outs"),

    UA_DIPSTICK(
        8,
        "Check Urine Dipstick",
        "Q1H",
        category = "Ins/Outs"
    ),

    NG_OG(
        9,
        "Perform NG/OG Tube Care",
        "Q2H",
        category = "Ins/Outs"
    ),

    FOLEY_BLADDER_TAP(
        10,
        "Foley / Bladder Tap",
        "Q24H",
        category = "Ins/Outs"
    ),

    FLUSH_PRN_LOCKS(11, "Flush PRN Locks", "Q8H", category = "Ins/Outs"),
    //endregion

    //region Pain/Sedation

    GCS_NEURO_MACE(
        12,
        "Check GCS/RASS/PAIN",
        "Q1H",
        category = "Pain/Sedation"
    ),

    GIVE_PAIN_RX(13, "Give Pain Rx", "per Rx", category = "Pain/Sedation"),
    GIVE_SEDATION_RX(14, "Give Sedation Rx", "per Rx", category = "Pain/Sedation"),

    //endregion

    //region HEENT

    PERFORM_TUBE_SUCTIONING(15, "Perform Tube Suctioning", "PRN", category = "HEENT"),
    PERFORM_ORAL_SUCTIONING(16, "Perform Oral Suctioning", "PRN", category = "HEENT"),

    PERFORM_NASAL_CARE_MOISTEN(
        17,
        "Perform Nasal Care/Moisten",
        "Q4H",
        category = "HEENT"
    ),

    PERFORM_ORAL_CARE_MOISTEN(
        18,
        "Perform Oral Care/Moisten",
        "Q4H",
        category = "HEENT"
    ),

    APPLY_LIP_BALM(19, "Apply Lip Balm", "Q1H", category = "HEENT"),

    APPLY_EYE_OINTMENT_DROPS(
        20,
        "Apply Eye Ointment/Drops",
        "per Rx",
        category = "HEENT"
    ),

    BRUSH_TEETH(21, "Brush Teeth", "Q12H", category = "HEENT"),
    CHANGE_ALL_TAPE(22, "Change All Tape", "Q24H", category = "HEENT"),

    //endregion

    //region Respiratory

    CHECK_VENTILATOR_SETTINGS(
        23,
        "Check Ventilator Settings",
        "Q1H",
        category = "Respiratory"
    ),

    AUSCULTATE_LUNGS(24, "Auscultate Lungs", "Q1H", category = "Respiratory"),

    TURN_COUGH_DEEP_BREATHE(
        25,
        "Turn, Cough, Deep Breathe",
        "Q1H",
        category = "Respiratory"
    ),

    CHECK_CHEST_DRAINAGE(26, "Check Chest Drainage", "Q1H", category = "Respiratory"),

    //endregion

    //region Integumentary

    COMPARTMENT_SYNDROME(
        27,
        "Check S/S Compartment Syndrome",
        "Q2H",
        category = "Integumentary"
    ),

    REPOSITION(
        28,
        "Reposition (30° Each side)",
        "Q2H",
        category = "Integumentary"
    ),

    CHECK_PADDING(29, "Check Padding", "Q2H", category = "Integumentary"),
    PERFORM_LE_MASSAGE(30, "Perform LE Massage", "Q2H", category = "Integumentary"),
    CHECK_DRESSINGS(31, "Check Dressings", "Q4H", category = "Integumentary"),
    DO_A_P_LIMB_ROM(32, "Do A/P Limb ROM", "Q8H", category = "Integumentary"),
    WASH_AND_DRY_SKIN(33, "Wash and Dry Skin", "Q24H", category = "Integumentary"),

    PERFORM_BURN_SKIN_CARE(
        34,
        "Perform Burn Skin Care",
        "Q24H",
        category = "Integumentary"
    ),

    IRRIGATE_WOUNDS(35, "Irrigate Wounds", "Q24H", category = "Integumentary"),

    DEBRIDEMENT(
        36,
        "Debridement",
        "Q24H",
        category = "Integumentary"
    ),

    CHANGE_DRESSINGS(37, "Change Dressings", "Q24H", category = "Integumentary"),
    GIVE_ANTIBIOTICS_RX(38, "Give Antibiotics Rx", "Q24H", category = "Integumentary"),

    //endregion

    //region Gastrointestinal

    GIVE_PPI_RX_IF_INDICATED(
        39,
        "Give PPI Rx if Indicated",
        "per Rx",
        category = "Gastrointestinal"
    ),

    GIVE_ANTIEMETIC_RX(
        40,
        "Give Antiemetic Rx",
        "per Rx",
        category = "Gastrointestinal"
    ),

    AUSCULTATE_ABDOMEN(41, "Auscultate Abdomen", "Q2H", category = "Gastrointestinal"),
    PALPATE_ABDOMEN(42, "Palpate Abdomen", "Q2H", category = "Gastrointestinal"),

    GIVE_FOOD_NUTRITION(
        43,
        "Give Food/Nutrition",
        "Q8H",
        category = "Gastrointestinal"
    ),

    //endregion

    //region Extra Stuff

    CHECK_O2_SUPPLY(44, "Check O2 Supply", category = "Extra Stuff"),
    CHECK_CHANGE_BATTERIES(45, "Check/change batteries", category = "Extra Stuff"),
    COMPRESSION_SOCKS_STOCKINGS(46, "Compression Socks/Stockings", category = "Extra Stuff"),

    //endregion

    //endregion

    //region PFC Card items

    STOP_MASSIVE_BLEEDING(
        47,
        "Stop Massive Bleeding",
        defaultInterval = "PRN"
    ),

    OPEN_AIRWAY(
        48,
        "Open Airway",
        defaultInterval = "PRN"
    ),

    THORACOSTOMY_OR_NEEDLE_D(
        49,
        "Thoracostomy or Needle D",
        defaultInterval = "PRN"
    ),

    FIRST_TXA_DOSE_ASAP(
        50,
        "1st TXA Dose ASAP",
        defaultInterval = "PRN"
    ),

    INITIATE_BLOOD_TRANSFUSION(
        51,
        "Initiate Blood Transfusion",
        defaultInterval = "PRN"
    ),

    CALCIUM(
        52,
        "Calcium",
        defaultInterval = "PRN"
    ),

    REASSESS_TX(
        53,
        "Reassess Tx",
    ),

    CLEAR_CSPINE(
        54,
        "Clear C-Spine",
    ),

    EXPOSE(
        55,
        "Expose",
    ),

    DETAILED_EXAM(
        56,
        "Detailed Exam",
    ),

    PELVIC_BINDER(
        57,
        "Pelvic Binder",
        defaultInterval = "PRN"
    ),

    ADJUST_VENT_SETTINGS(
        58,
        "Adjust Vent Settings",
        defaultInterval = "Q1H"
    ),

    ANTIBIOTIC_WAR_WOUND_TX(
        59,
        "Antibiotic War Wound Tx",
        defaultInterval = "PRN"
    ),

    MIST_REPORT(
        60,
        "Send MIST Report",
        defaultInterval = "PRN"
    ),

    SECOND_IV_IO(
        61,
        "2nd IV / IO",
        defaultInterval = "PRN"
    ),

    MONITORS(
        62,
        "Monitors",
    ),

    UPGRADE_AIRWAY(
        63,
        "Upgrade Airway",
    ),

    POST_CRIC(
        64,
        "Post Cric Checklist",
    ),

    VENT_PEEP(
        65,
        "Vent or BVM w/ PEEP",
        defaultInterval = "PRN"
    ),

    HYPOTHERMIA_TX(
        66,
        "Hypothermia Tx",
        defaultInterval = "PRN"
    ),

    RECALC(
        67,
        "Recalc TBSA and Fluids",
        defaultInterval = "Q24H"
    ),

    ULTRASOUND(
        68,
        "Ultrasound EFAST",
        defaultInterval = "PRN"
    ),

    CONVERT_TQ(
        69,
        "Convert TQ <4hrs",
    ),

    POSITION_PAD_PATIENT(
        70,
        "Position Pad Patient",
        defaultInterval = "Q2H"
    ),

    ESCHAROTOMY(
        71,
        "Escharotomy",
        defaultInterval = "PRN"
    ),

    REDUCE_FX(
        72,
        "Reduce Splint Fx",
        defaultInterval = "PRN"
    ),

    DVT_PROPHYLAXIS(
        73,
        "DVT Prophylaxis",
        defaultInterval = "Q2H"
    ),

    TETANUS(
        74,
        "Tetanus",
        defaultInterval = "PRN"
    ),

    TELECONSULT(
        75,
        "Teleconsult",
        defaultInterval = "PRN"
    ),

    PRESSORS_FOR_DISTRIBUTIVE_SHOCK(
        76,
        "Pressors for Distributive Shock",
        defaultInterval = "PRN"
    ),

    LABS(
        77,
        "Labs",
        defaultInterval = "PRN"
    ),

    XRAY_IMAGING(
        78,
        "X-Ray / Imaging",
        defaultInterval = "PRN"
    ),

    PREOP_EVAL(
        79,
        "PreOp Eval",
        defaultInterval = "PRN"
    ),

    AMPUTATION(
        80,
        "Amputation",
        defaultInterval = "PRN"
    ),

    SHUNT(
        81,
        "Shunt",
        defaultInterval = "PRN"
    ),

    FASCIOTOMY(
        81,
        "Fasciotomy",
        defaultInterval = "PRN"
    ),

    PREPERITONEAL_PELVIC_PACKING(
        82,
        "Preperitoneal Pelvic Packing",
        defaultInterval = "PRN"
    ),

    FLUSH_SALINE_LOCKS(
        83,
        "Flush Saline Locks",
        defaultInterval = "Q8H",
        category = "Ins/Outs"
    ),

    SUCTION_ET_TUBE(
        84,
        "Suction ET Tube",
        defaultInterval = "PRN"
    ),

    ORAL_CARE_HYGIENE(
        85,
        "Oral Care / Hygiene",
        defaultInterval = "Q4H",
        category = "HEENT"
    ),

    FOLEY_CARE(
        86,
        "Foley Care",
        defaultInterval = "Q4H",
        category = "Ins/Outs"
    ),

    CHECK_PERIPHERAL_PULSES(
        87,
        "Check Peripheral Pulse",
        defaultInterval = "Q1H",
        category = "Vitals"
    ),

    //endregion
    ;


    companion object {
        fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)
    }
}