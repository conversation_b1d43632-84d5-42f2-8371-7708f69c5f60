package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.proto.SharedProtobufObjects.CompatibleEnum
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto
import gov.afrl.batdok.util.toChangeOrClearEvent

enum class DiagnosisCategory(override val protoIndex: Int, override val dataString: String): ProtoEnum {
    DISEASE_NON_BATTLE_INJURY(1, "DNBI"),
    NON_BATTLE_INJURY(2, "NBI"),
    BATTLE_INJURY(3, "BI");


    companion object{
        fun fromProto(enum: CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)
        fun toEvent(enum: CompatibleEnum) = values().toChangeOrClearEvent(enum, "diagnosis")
    }
}