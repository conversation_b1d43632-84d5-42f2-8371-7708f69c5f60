package gov.afrl.batdok.encounter.vitals

import gov.afrl.batdok.commands.proto.VitalOuterClass
import gov.afrl.batdok.commands.proto.iBP
import gov.afrl.batdok.encounter.IndividualVital
import gov.afrl.batdok.util.int32Value

class InvasiveBloodPressure(val systolic: Int?, val diastolic: Int?): IndividualVital("iBP"){
    constructor(bp: VitalOuterClass.IBP): this(
        bp.bps.value.takeIf { bp.hasBps() },
        bp.bpd.value.takeIf { bp.hasBpd() },
    )
    override fun toProtobuf() = iBP {
        <EMAIL>?.let { this.bps = int32Value(it) }
        <EMAIL>?.let { this.bpd = int32Value(it) }
    }
    override fun produceEmptyVital() = InvasiveBloodPressure(null, null)

    override val eventMessage: String
        get() = "%s/%s".format(systolic?.toString() ?: "--", diastolic?.toString() ?: "--")

    override val isEmpty: Boolean
        get() = systolic == null && diastolic == null
}