package gov.afrl.batdok.encounter.observation

import gov.afrl.batdok.commands.proto.Observations
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.integData
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

class IntegData(val type: String? = null): ObservationData {
    internal constructor(data: Observations.IntegData) : this(Type.fromProto(data.type))

    enum class Type(override val protoIndex: Int, override val dataString: String) : ProtoEnum {
        WARM(1, "Warm"),
        COOL(2, "Cool"),
        DRY(3, "Dry"),
        DIAPHORETIC(4, "Diaphoretic"),
        RASH(5, "Rash"),
        FREQ_POSITION_CHANGE(6, "Frequent Position Change"),
        SKIN_BREAKDOWN_NOTED(7, "Skin Breakdown Noted"),
        ;

        companion object {
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = Type.values().protoToString(enum)
            fun fromString(string: String?) = Type.values().stringToProto(string)
        }
    }

    override fun toProtobuf() = integData {
        this.type = Type.fromString(<EMAIL>)
    }

    override fun toDetailString(): String {
        return this.type ?: ""
    }
}