package gov.afrl.batdok.encounter.vitals

import gov.afrl.batdok.commands.proto.VitalOuterClass
import gov.afrl.batdok.commands.proto.pIP
import gov.afrl.batdok.encounter.IndividualVital
import gov.afrl.batdok.util.int32Value

class PIP(val pip: Int?): IndividualVital("PIP"){
    constructor(pain: VitalOuterClass.PIP): this(
        pain.pip.value.takeIf { pain.hasPip() },
    )
    override fun toProtobuf() = pIP {
        <EMAIL>?.let { this.pip = int32Value(it) }
    }

    override fun produceEmptyVital() = PIP(null)
    override val eventMessage: String
        get() = "$pip"

    override val isEmpty: Boolean
        get() = pip == null
}