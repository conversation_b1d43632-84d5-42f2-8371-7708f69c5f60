package gov.afrl.batdok.encounter.ids

import com.google.protobuf.ByteString
import com.google.protobuf.kotlin.toByteString
import mil.af.afrl.batman.batdokid.DomainId

fun DomainId.toByteString() = unique.toByteString()
inline fun <reified T: DomainId> ByteString.toDomainId() = DomainId.create<T>(toByteArray())

/**
 * Return a domain id created from [this], or return [ifNil] if [this] would create a nil id.
 */
inline fun <reified T: DomainId> ByteString.toDomainIdOrElse(ifNil: ByteString) = (takeUnless { it.isEmpty || it.toDomainId<T>().isNil() } ?: ifNil).toDomainId<T>()