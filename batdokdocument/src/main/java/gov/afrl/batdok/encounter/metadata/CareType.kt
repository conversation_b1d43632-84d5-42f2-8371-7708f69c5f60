package gov.afrl.batdok.encounter.metadata

import gov.afrl.batdok.commands.proto.SharedProtobufObjects.CompatibleEnum
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

enum class CareType(override val protoIndex: Int, override val dataString: String): ProtoEnum {
    O2_ADMIN(1, "O2 Administration"),
    STD_MECH(2, "Std Mech Ventilation"),
    ADV_MECH(3, "Adv Mech Ventilation"),
    J_TUBE_FEEDING(4, "J-Tube Feeding"),
    ET_CUFF_PRESSURE(5, "ET Cuff Pressure Check"),
    ORAL_CARE(6, "Oral Care"),
    WRIGHT_RESP(7, "Wright Respirometer"),
    VENTRIC(8, "Ventric"),
    PARYNCH(9, "Parynch"),
    IBP_MONITOR(10, "IBP Monitor"),
    NIBP_MONITOR(11, "NIBP Monitor"),
    ICP_MONITOR(12, "ICP Monitor"),
    CHEST_TUBE_MANAGEMENT(13, "Chest Tube Management"),
    DRAIN_MANAGEMENT(14, "Drain Management"),
    BLOOD_PRODUCT_TRANSFUSED(15, "Blood Product Transfused"),
    ANTIBIOTICS(16, "Antibiotics"),
    IV_NARCOTIC_ANALGESIA(17, "IV Narcotic Analgesia"),
    IV_SEDATION(18, "IV Sedation"),
    NMB(19, "NMB"),
    COMPLEX_WOUND_DRESSING(20, "Complex Wound Dressing Δ"),
    VAC_CHANGE(21, "VAC Δ"),
    NACL(22, "3% NaCl"),
    MANNITOL(23, "Mannitol"),
    INHALED_BROCH(24, "Inhaled Bronchodilators"),
    DVT_PROPHYLAXIS(25, "DVT Prophylaxis"),
    BURN_FLOWSHEET(26, "Burn Flowsheet"),
    ;

    companion object{
        fun fromProto(enum: CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)
    }
}