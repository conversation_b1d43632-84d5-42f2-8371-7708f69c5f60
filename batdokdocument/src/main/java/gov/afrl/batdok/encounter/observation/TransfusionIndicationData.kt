package gov.afrl.batdok.encounter.observation

import gov.afrl.batdok.commands.proto.*
import gov.afrl.batdok.util.booleanValue

class TransfusionIndicationData(val amputation: <PERSON>olean = false, val hrOver120: <PERSON><PERSON>an = false, val sbpUnder90: Boolean = false): ObservationData {
    internal constructor(data: Observations.TransfusionIndicationData): this(data.amputationOrNull?.value?:false, data.hrOver120OrNull?.value?:false, data.sbpUnder90OrNull?.value?:false)

    override fun toProtobuf() = transfusionIndicationData {
        amputation = booleanValue(<EMAIL>)
        hrOver120 = booleanValue(this@TransfusionIndicationData.hrOver120)
        sbpUnder90 = booleanValue(this@TransfusionIndicationData.sbpUnder90)
    }

    override fun toDetailString(): String {
        val indications = listOfNotNull("Amputation".takeIf { amputation }, "HR > 120".takeIf { hrOver120 }, "SBP < 90".takeIf { sbpUnder90 })
        return indications.joinToString(", ").takeUnless { it.isEmpty() } ?: "No indication"
    }
}