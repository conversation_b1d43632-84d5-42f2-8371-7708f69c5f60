package gov.afrl.batdok.encounter.treatment

import com.google.protobuf.Any
import com.google.protobuf.kotlin.isA
import com.google.protobuf.kotlin.unpack
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.TreatmentCommands
import gov.afrl.batdok.commands.proto.TreatmentCommands.*
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

enum class CommonTreatments(override val protoIndex: Int, override val dataString: String): ProtoEnum {
    //region Treatments
    TQ(1, "TQ"),
    DRESSING(2, "Dressing"),
    INTACT(3, "Intact"),
    NPA(4, "NPA"),
    CRIC(5, "CRIC"),
    SGA(6, "SGA"),
    COMBAT_PILL_PACK(7, "Combat-Pill-Pack"),
    HYPOTHERMIA_PREVENTION(8, "Hypothermia Prevention"),
    DIRECT_PRESSURE(9, "Direct Pressure"),
    ET_TUBE(10, "ET-Tube"),
    NEEDLE_D(11, "Needle-D"),
    FINGER_THOR(12, "Finger Thor"),
    CHEST_SEAL(13, "Chest Seal"),
    CHEST_TUBE(14, "Chest Tube"),
    EYE_SHIELD(15, "Eye Shield"),
    SPLINT(16, "Splint"),
    O2(17, "O2"),
    ESCHARATOMY(18, "Escharatomy"),
    DEBRIDEMENT(19, "Debridement"),
    GASTRIC_TUBE(20, "Gastric Tube"),
    LATERAL_CANTHOTOMY(21, "Lateral Canthotomy"),
    REBOA(22, "REBOA"),
    FASCIOTOMY(23, "Fasciotomy"),
    FOLEY_CATHETER(24, "Foley Catheter"),
    OPA(25, "OPA"),
    TRACH(26, "Trach"),
    BLOOD_FLUID_WARMER(27, "Blood/Fluid Warmer"),
    PROTECTIVE_EYEWEAR(28, "Protective Eyewear"),
    HOB_AT_30(29, "HOB @ 30"),
    BANDAID(30, "Band Aid"),
    IMMOBILIZATION(31, "Immobilization"),
    STABILIZE_PENETRATING_INJURY(32, "Stabilize Penetrating Injury"),
    BURP_CHEST_SEALS(33, "Burp Chest Seals"),
    SUCTION(34, "Suction"),

    /**
     * Use this if the patient was intubated before being in your care, but there is no history of it.
     */
    INTUBATED_BY(35, "Intubated By"),
    FOREIGN_BODY_REMOVAL(36, "Foreign Body Removal"),
    LINE(37, "Line"),
    WOUND_VAC_TUBE(38, "Wound Vac Tube"),
    HYPERTHERMIA_PREVENTION(39, "Hyperthermia Prevention"),
    SALINE_LOCK(40, "Saline Lock"),
    PELVIC_BINDER(41, "Pelvic Binder"),
    PERICARDIOCENTESIS(42, "Pericardiocentesis"),
    WOUND_PACKING(43, "Wound Packing")
    ;

    //endregion

    companion object{
        fun getTreatmentData(any: Any) = when {
            any.isA<TreatmentCommands.TQ>() -> TqData.fromProtobuf(any.unpack())
            any.isA<Dressing>() -> DressingData.fromProtobuf(any.unpack())
            any.isA<Tube>() -> TubeData.fromProtobuf(any.unpack())
            any.isA<NeedleD>() -> NeedleDData.fromProtobuf(any.unpack())
            any.isA<FingerThor>() -> FingerThorData.fromProtobuf(any.unpack())
            any.isA<ChestTube>() -> ChestTubeData.fromProtobuf(any.unpack())
            any.isA<ChestSeal>() -> ChestSealData.fromProtobuf(any.unpack())
            any.isA<EyeShield>() -> EyeShieldData.fromProtobuf(any.unpack())
            any.isA<Splint>() -> SplintData.fromProtobuf(any.unpack())
            any.isA<TreatmentCommands.O2>() -> O2Data.fromProtobuf(any.unpack())
            any.isA<Escharatomy>() -> EscharatomyData.fromProtobuf(any.unpack())
            any.isA<Debridement>() -> DebridementData.fromProtobuf(any.unpack())
            any.isA<GastricTube>() -> GastricTubeData.fromProtobuf(any.unpack())
            any.isA<LateralCanthotomy>() -> LateralCanthotomyData.fromProtobuf(any.unpack())
            any.isA<Fasciotomy>() -> FasciotomyData.fromProtobuf(any.unpack())
            any.isA<Pericardiocentesis>() -> PericardiocentesisData.fromProtobuf(any.unpack())
            any.isA<FoleyCatheter>() -> FoleyCatheterData.fromProtobuf(any.unpack())
            any.isA<DirectPressure>() -> DirectPressureData.fromProtobuf(any.unpack())
            any.isA<ForeignBodyRemoval>() -> ForeignBodyRemovalData.fromProtobuf(any.unpack())
            any.isA<HypothermiaPrevention>() -> HypothermiaPreventionData.fromProtobuf(any.unpack())
            any.isA<Immobilization>() -> ImmobilizationData.fromProtobuf(any.unpack())
            any.isA<Suction>() -> SuctionData.fromProtobuf(any.unpack())
            any.isA<IntubatedBy>() -> IntubatedByData(any.unpack())
            any.isA<TreatmentCommands.LineData>() -> LineData(any.unpack())
            any.isA<TreatmentCommands.HyperthermiaPreventionData>() -> HyperthermiaPreventionData(any.unpack())
            any.isA<TreatmentCommands.InboundHL7Data>() -> InboundHL7Data.fromProtobuf(any.unpack())
            any.isA<TreatmentCommands.PelvicBinderData>() -> PelvicBinderData(any.unpack())
            any.isA<TreatmentCommands.WoundPackingData>() -> WoundPackingData(any.unpack())
            else -> null
        }
        fun find(enum: SharedProtobufObjects.CompatibleEnum) = values().find { it.protoIndex == enum.enum || it.dataString == enum.string }
        fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)
    }
}
