package gov.afrl.batdok.encounter.medicine

import gov.afrl.batdok.commands.CommandHandler
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.commands.*
import gov.afrl.batdok.util.ICategorizedItemList
import gov.afrl.batdok.util.SortedCategorizedLinkableList
import java.io.Serializable

class Medicines(): ICategorizedItemList<Medicine> by SortedCategorizedLinkableList(), Serializable {

    @Transient internal val handlers = CommandHandler {
        val medicines = this@Medicines
        +logMedCommandHandler(medicines)
        +logMedWithIdCommandHandler(medicines)
        +updateMedCommandHandler(medicines)
        +removeMedTreatmentCommandHandler(medicines)
    }

    companion object {
        fun EventCommandHandler.includeMedicineEvents(medicines: Medicines) = apply {
            +logMedCommandEventHandler()
            +logMedWithIdCommandEventHandler()
            +updateMedCommandEventHandler(medicines)
            +removeMedTreatmentCommandEventHandler(medicines)
        }
    }
}