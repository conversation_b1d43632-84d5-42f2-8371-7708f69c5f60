package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.proto.SharedProtobufObjects.CompatibleEnum
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto
import gov.afrl.batdok.util.toChangeOrClearEvent

enum class Capability(override val dataString: String, override val protoIndex: Int) :
    ProtoEnum {
    EMTB("EMT-B", 1),
    EMTI("EMT-I", 2),
    EMTP("EMT-P", 3),
    EMTFPC("EMT-FPC", 4),
    RN("RN", 5),
    CRNA("CRNA", 6),
    PA("PA", 7),
    MDDO("MD/DO", 8);

    companion object {
        fun fromProto(enum: CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?, includeStringAnyway: List<Capability> = listOf()) =
            values().stringToProto(string, includeStringAnyway)
        fun toEvent(enum: CompatibleEnum) = values()
            .toChangeOrClearEvent(enum, "capability")
    }
}