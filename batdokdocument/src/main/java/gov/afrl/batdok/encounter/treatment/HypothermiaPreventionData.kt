package gov.afrl.batdok.encounter.treatment

import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.TreatmentCommands
import gov.afrl.batdok.commands.proto.hypothermiaPrevention
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

class HypothermiaPreventionData(val type: String? = null): TreatmentData{
    override fun toProtobuf(): Message = hypothermiaPrevention{
        type = Type.fromString(<EMAIL>)
    }

    override fun toDetailString(): String {
        return type ?: ""
    }

    companion object{
        fun fromProtobuf(hypoPrev: TreatmentCommands.HypothermiaPrevention): HypothermiaPreventionData{
            return HypothermiaPreventionData(Type.fromProto(hypoPrev.type)?:"")
        }
    }

    enum class Type(override val protoIndex: Int, override val dataString: String): ProtoEnum {
        BLANKET(1, "Blanket"),
        SLEEPING_BAG(2, "Sleeping Bag"),
        BLIZZARD_BLANKET(3, "Blizzard Blanket"),
        MAX_HEAT_IN_CABIN(4, "Max heat in cabin"),
        REMOVE_WET_CLOTHING(5, "Remove Wet Clothing"),
        ;

        companion object{
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }
}