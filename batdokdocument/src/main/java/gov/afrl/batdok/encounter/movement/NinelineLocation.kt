package gov.afrl.batdok.encounter.movement

import gov.afrl.batdok.commands.proto.SharedProtobufObjects.CompatibleEnum
import gov.afrl.batdok.util.*
import java.io.Serializable
import java.time.Instant

enum class Role(override val dataString: String, override val protoIndex: Int) :
    ProtoEnum {
    POI("1-POI", 1),
    TWO("2", 2),
    THREE("3", 3),
    FOUR("4", 4),
    AID_STATION("1-Aid Station", 5);

    companion object {
        fun fromProto(enum: CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)
        fun toEvent(enum: CompatibleEnum) = values()
            .toChangeOrClearEvent(enum, "Role")
    }
}

enum class Region(override val dataString: String, override val protoIndex: Int) :
    ProtoEnum {
    IRAQ("Iraq", 1),
    K<PERSON><PERSON>IT("Kuwait", 2),
    RC_EAST("RC-East", 3),
    RC_NORTH("RC-North", 4),
    RC_SOUTH("RC-South", 5),
    RC_SOUTHWEST("RC-Southwest", 6),
    RC_WEST("RC-West", 7);

    companion object {
        fun fromProto(enum: CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)
    }
}

data class NinelineLocation(
    val time: Instant? = null,
    val role: String? = "",
    val region: String? = "",
    val location: String? = ""
) : Serializable {
    override fun toString() = listOfNotNull(
        time?.let { "Time: ${time.format(Patterns.mdhm_24_space_comma_colon, true)}" },
        role.takeUnless { it.isNullOrBlank() }?.let { "Role: $role" },
        region.takeUnless { it.isNullOrBlank() }?.let { "Region: $region" },
        location.takeUnless { it.isNullOrBlank() }?.let { "Location: $location" },
    ).joinToString(", ")
}
