package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.CommandHandler
import gov.afrl.batdok.commands.EventCommandHandler
import gov.afrl.batdok.encounter.commands.*
import gov.afrl.batdok.util.ICategorizedItemList
import gov.afrl.batdok.util.SortedCategorizedLinkableList
import mil.af.afrl.batman.batdokid.DomainId
import java.io.Serializable
import java.time.Instant

class CustomActions: Serializable, ICategorizedItemList<CustomAction> {
    val _list = SortedCategorizedLinkableList<CustomAction>()
    val actions: List<CustomAction>
        get() = list

    @Transient val handlers = CommandHandler().apply {
        val customActions = this@CustomActions
        +addCustomActionCommandHandler(customActions)
        +updateCustomActionCommandHandler(customActions)
        +removeCustomActionCommandHandler(customActions)
    }

    companion object{
        fun EventCommandHandler.includeCustomActionHandlers(customActions: CustomActions){
            +addCustomActionCommandEventHandler()
            +updateCustomActionCommandEventHandler(customActions)
            +removeCustomActionCommandEventHandler(customActions)
        }
    }

    override val list: List<CustomAction>
        get() =_list.list
    override val onErrorRemovedItems: List<Pair<Instant, CustomAction>>
        get() = _list.onErrorRemovedItems
    override val onProperRemovedItems: List<Pair<Instant, CustomAction>>
        get() = _list.onProperRemovedItems

    override fun removeItem(itemId: DomainId, timestamp: Instant, documentationError: Boolean) {
        _list.removeItem(itemId, timestamp, documentationError)
    }

    override fun plusAssign(item: CustomAction?) {
        _list.plusAssign(item)
    }
}