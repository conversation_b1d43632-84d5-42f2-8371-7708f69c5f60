package gov.afrl.batdok.encounter.observation

import gov.afrl.batdok.commands.proto.*
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto

class BreathSoundsData(val typeList: List<String> = listOf()): ObservationData {
    internal constructor(data: Observations.BreathSoundsData):
            this(data.typesOrNull?.valueList?.mapNotNull { Type.fromProto(it) } ?: listOf())
    enum class Type(override val protoIndex: Int, override val dataString: String): ProtoEnum {
        LEFT_ABSENT(1, "Left Absent"),
        LEFT_DIMINISHED(2, "Left Diminished"),
        LEFT_NORMAL(3, "Left Normal"),
        RIGHT_ABSENT(4, "Right Absent"),
        RIGHT_DIMINISHED(5, "Right Diminished"),
        RIGHT_NORMAL(6, "Right Normal");

        companion object {
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }

    override fun toProtobuf() = breathSoundsData {
        this.types = nullableEnumList { value.addAll(<EMAIL> { Type.fromString(it) }) }
    }

    override fun toDetailString(): String {
        return typeList.joinToString(", ")
    }

}