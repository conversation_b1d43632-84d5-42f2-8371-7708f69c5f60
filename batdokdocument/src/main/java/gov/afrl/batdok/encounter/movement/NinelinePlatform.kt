package gov.afrl.batdok.encounter.movement

import gov.afrl.batdok.commands.proto.SharedProtobufObjects.CompatibleEnum
import gov.afrl.batdok.util.ProtoEnum
import gov.afrl.batdok.util.protoToString
import gov.afrl.batdok.util.stringToProto
import gov.afrl.batdok.util.toChangeOrClearEvent

enum class NinelinePlatform(override val dataString: String, override val protoIndex: Int) :
    ProtoEnum {
    C2("C-2 (Greyhound)", 1),
    C130("C-130 (Hercules)", 2),
    C17("C-17 (Globemaster)", 3),
    C212("C-212 (Casa)", 4),
    C23("C-23 (Sherpa)", 5),
    C5("C-5 (Galaxy)", 6),
    C9("C-9 (Skytrain)", 7),
    C12("C-12 (Huron)", 8),
    CH46("CH-46 (Sea Knight)", 9),
    CH47("CH-47 (Chinook)", 10),
    CH53("CH-53 (Sea Stallion)", 11),
    HH60("HH-60 (<PERSON><PERSON> / Jayhawk)", 12),
    HH65("HH-65 (Dolphin)", 13),
    MC("MC (Talon)", 14),
    MEDEVAC("MEDEVAC", 15),
    MH47("MH-47 (Chinook)", 16),
    MH53("MH-53 (Pave Low)", 17),
    MH6("MH-6 (Little Bird)", 18),
    MH60B("MH-60 (Blackhawk)", 19),
    MH60S("MH-60 (Seahawk)", 20),
    MI8("MI-8 (HIP)", 21),
    UH1I("UH-1 (Iroquois)", 22),
    UH1Y("UH-1 (Yankee)", 23),
    UH60("UH-60 (Blackhawk)", 24),
    V22("V-22 (Osprey)", 25);

    companion object {
        fun fromProto(enum: CompatibleEnum) = values().protoToString(enum)
        fun fromString(string: String?) = values().stringToProto(string)
        fun toEvent(enum: CompatibleEnum) = values().toChangeOrClearEvent(enum, "Nineline Platform")
    }
}
