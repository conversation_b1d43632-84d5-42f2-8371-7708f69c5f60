package gov.afrl.batdok.encounter.treatment

import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.TreatmentCommands.Suction
import gov.afrl.batdok.commands.proto.suction
import gov.afrl.batdok.util.*

class SuctionData(val tool: String? = null, val location: String?): TreatmentData{
    override fun toProtobuf(): Message = suction{
        tool = Tool.fromString(<EMAIL>)
        location = compatibleEnum(<EMAIL>)
    }

    override fun toDetailString(): String {
        return tool ?: ""
    }

    companion object{
        fun fromProtobuf(suction: Suction): SuctionData{
            return SuctionData(
                Tool.fromProto(suction.tool)?:"",
                suction.location.toPrimitive()
            )
        }
    }

    enum class Tool(override val protoIndex: Int, override val dataString: String): ProtoEnum {
        CATHETER(1, "Catheter"),
        YAN<PERSON>UE<PERSON>(2, "<PERSON><PERSON><PERSON>"),
        ETT(3, "ETT"),
        ;

        companion object{
            fun fromProto(enum: SharedProtobufObjects.CompatibleEnum) = values().protoToString(enum)
            fun fromString(string: String?) = values().stringToProto(string)
        }
    }
}