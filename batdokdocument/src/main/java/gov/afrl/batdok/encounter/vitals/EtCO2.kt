package gov.afrl.batdok.encounter.vitals

import gov.afrl.batdok.commands.proto.VitalOuterClass
import gov.afrl.batdok.commands.proto.etCO2
import gov.afrl.batdok.encounter.IndividualVital
import gov.afrl.batdok.util.int32Value

class EtCO2(val etco2: Int?): IndividualVital("EtCO2"){
    constructor(etco2: VitalOuterClass.EtCO2): this(
        etco2.etco2.value.takeIf { etco2.hasEtco2() },
    )
    override fun toProtobuf() = etCO2 {
        this@EtCO2.etco2?.let { this.etco2 = int32Value(it) }
    }
    override fun produceEmptyVital() = EtCO2(null)

    override val eventMessage: String
        get() = "$etco2"

    override val isEmpty: Boolean
        get() = etco2 == null
}