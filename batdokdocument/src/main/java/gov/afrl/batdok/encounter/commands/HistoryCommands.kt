package gov.afrl.batdok.encounter.commands

import com.google.protobuf.Any
import gov.afrl.batdok.commands.eventHandler
import gov.afrl.batdok.commands.handler
import gov.afrl.batdok.commands.proto.SubjectiveCommands.UpdateHistoryLastEventCommand
import gov.afrl.batdok.commands.proto.SubjectiveCommands.UpdateHpiCommand
import gov.afrl.batdok.commands.proto.SubjectiveCommands.UpdatePastHistoryCommand
import gov.afrl.batdok.commands.proto.dateOrNull
import gov.afrl.batdok.commands.proto.updateHistoryLastEventCommand
import gov.afrl.batdok.commands.proto.updateHpiCommand
import gov.afrl.batdok.commands.proto.updatePastHistoryCommand
import gov.afrl.batdok.encounter.History
import gov.afrl.batdok.encounter.HistoryLastEventType
import gov.afrl.batdok.encounter.HistoryType
import gov.afrl.batdok.encounter.KnownEventTypes
import gov.afrl.batdok.encounter.LastEventTime
import gov.afrl.batdok.encounter.changeOrClearEvent
import gov.afrl.batdok.util.Patterns
import gov.afrl.batdok.util.format
import gov.afrl.batdok.util.int64Value
import java.time.Instant

fun buildHistoryOfPresentIllnessCommand(hpi: String?) = updateHpiCommand {
    hpi?.let { this.history = it }
}
fun historyOfPresentIllnessCommandHandler(history: History) = handler<UpdateHpiCommand> {
    history.historyOfPresentIllness = this.history.takeIf { it.isNotBlank() }
}
fun historyOfPresentIllnessEventCommandHandler() = eventHandler<UpdateHpiCommand>(KnownEventTypes.SUBJECTIVE.dataString) {
    changeOrClearEvent(history.isNotBlank(), history, "history of present illness")
}

fun buildPastHistoryCommand(type: String, history: String?) = updatePastHistoryCommand {
    history?.let { this.history = it }
    this.type = HistoryType.fromString(type)
}
fun pastHistoryCommandHandler(history: History) = handler<UpdatePastHistoryCommand> {
    history.updateHistory(HistoryType.fromProto(this.type), this.history.takeIf { it.isNotBlank() })
}
fun pastHistoryEventCommandHandler() = eventHandler<UpdatePastHistoryCommand>(KnownEventTypes.SUBJECTIVE.dataString) {
    changeOrClearEvent(history.isNotBlank(), history, "past " + HistoryType.fromProto(this.type)?.lowercase() + " history")
}

fun buildLastEventTimeCommand(type: String, date: Instant?, extras: LastEventTime.Extras? = null) = updateHistoryLastEventCommand {
    date?.let { this.date = int64Value(date.epochSecond)}
    extras?.toProtobuf()?.let { this.extras = Any.pack(it, "") }
    this.type = HistoryLastEventType.fromString(type)
}

fun lastEventTimeCommandHandler(history: History) = handler<UpdateHistoryLastEventCommand> {
    history.updateLastEventTime(
        HistoryLastEventType.fromProto(this.type),
        dateOrNull?.value?.let { Instant.ofEpochSecond(it) },
        LastEventTime.getExtras(this.extras)
    )
}

fun lastEventTimeEventCommandHandler() = eventHandler<UpdateHistoryLastEventCommand>(KnownEventTypes.SUBJECTIVE.dataString) {
    val isAdd = hasDate()
    val extrasString = LastEventTime.getExtras(this.extras)?.toEventText()?.let {
        " ($it)"
    } ?: ""
    changeOrClearEvent(isAdd,
        Instant.ofEpochSecond(date.value).format(Patterns.mdy_dash),
        "Last " + HistoryLastEventType.fromProto(this.type) + extrasString
    )
}