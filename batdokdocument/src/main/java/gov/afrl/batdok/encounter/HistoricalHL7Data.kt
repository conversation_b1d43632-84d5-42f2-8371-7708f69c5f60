package gov.afrl.batdok.encounter

import gov.afrl.batdok.commands.CommandHandler
import gov.afrl.batdok.encounter.commands.hl7DataCommandHandler
import java.io.Serializable

class HistoricalHL7Data : Serializable {
    var hl7Messages = ""
        internal set

    @Transient internal val handlers = CommandHandler {
        val hl7Data = this@HistoricalHL7Data
         + hl7DataCommandHandler(hl7Data)
    }
}
