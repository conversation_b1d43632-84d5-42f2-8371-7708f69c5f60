package gov.afrl.batdok.commands

import com.google.protobuf.Internal
import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.DocumentCommands.CommandData
import gov.afrl.batdok.encounter.ids.DocumentId

open class CommandHandler(){
    constructor(func: CommandHandler.() -> Unit): this() { func() }
    private val handlers = mutableMapOf<String, List<Handler<*>>>()

    internal inline fun <reified T: Message> hasHandlerFor(klass: Class<T> = T::class.java): Boolean{
        val name = Internal.getDefaultInstance(klass).descriptorForType.fullName
        return name in handlers.keys
    }

    operator fun Handler<*>.unaryPlus(){
        val typeName = Internal.getDefaultInstance(this.klass).descriptorForType.fullName
        val typeHandlers = handlers[typeName]
        if (typeHandlers != null) {
            handlers[typeName] = typeHandlers + this@unaryPlus
        } else {
            handlers += typeName to listOf(this@unaryPlus)
        }
    }

    fun include(handlers: CommandHandler){
        handlers.handlers.forEach {
            val typeName = it.key
            val typeHandlers = this.handlers[typeName]
            if (typeHandlers != null) {
                this.handlers[typeName] = typeHandlers + it.value
            } else {
                this.handlers += typeName to it.value
            }
        }
    }

    open fun handle(documentId: DocumentId, command: CommandData) {
        val commandClassName = command.data.typeUrl.split("/").lastOrNull() ?: ""
        // nullable for backwards compatibility. old BATDOK may not know about all the commands
        handlers[commandClassName]?.forEach {
            it.handle(documentId, command)
        }
    }
}

abstract class Handler<T: Message>(val klass: Class<T>){
    fun handle(documentId: DocumentId, command: CommandData){
        if(command.data.`is`(klass)){
            safeHandle(documentId, command, command.data.unpack(klass))
        }
    }
    abstract fun safeHandle(documentId: DocumentId, command: CommandData, subCommand: T)
}

inline fun <reified T: Message> handlerWithId(crossinline handler: T.(DocumentId, CommandData) -> Unit) = object: Handler<T>(T::class.java){
    override fun safeHandle(documentId: DocumentId, command: CommandData, subCommand: T) {
        subCommand.handler(documentId, command)
    }
}

inline fun <reified T: Message> handler(crossinline handler: T.(CommandData) -> Unit) = object: Handler<T>(T::class.java){
    override fun safeHandle(documentId: DocumentId, command: CommandData, subCommand: T) {
        subCommand.handler(command)
    }
}