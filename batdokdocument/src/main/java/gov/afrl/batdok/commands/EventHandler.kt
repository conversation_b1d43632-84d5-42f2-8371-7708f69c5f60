package gov.afrl.batdok.commands


import com.google.protobuf.Internal
import com.google.protobuf.Message
import gov.afrl.batdok.commands.proto.DocumentCommands.CommandData
import gov.afrl.batdok.encounter.Event
import gov.afrl.batdok.encounter.Events
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.encounter.ids.EventId
import gov.afrl.batdok.encounter.ids.toDomainId
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant

/**
 * Handle Commands by adding their events to the [events] list
 */
class EventCommandHandler(val events: Events){
    constructor(events: Events, func: EventCommandHandler.() -> Unit): this(events){
        func()
    }
    private val handlers = mutableMapOf<String, EventHandler<*>>()

    operator fun EventHandler<*>.unaryPlus(){
        handlers += Internal.getDefaultInstance(this.klass).descriptorForType.fullName to this@unaryPlus
    }

    fun handle(documentId: DocumentId, command: CommandData) {
        val commandClassName = command.data.typeUrl.split("/").lastOrNull() ?: ""
        // not all actions put an event in the Event Log so nullable is OK here
        events += handlers[commandClassName]?.handle(documentId, command)
    }
}

abstract class EventHandler<T: Message>(val klass: Class<T>, val eventType: String? = null){
    fun handle(documentId: DocumentId, command: CommandData): Event? {
        return if(command.data.`is`(klass)){
            createEventString(command, command.data.unpack(klass))?.let { message ->
                Event(
                    message,
                    generateEventId(command),
                    generateTimestamp(command),
                    documentId,
                    command.callsign,
                    eventType,
                    referencedItem = getReferenceId(command)
                )
            }
        } else {
            null
        }
    }

    open fun generateEventId(command: CommandData): EventId{
        return command.commandId.toDomainId()
    }
    open fun generateTimestamp(command: CommandData): Instant{
        return Instant.ofEpochSecond(command.timestamp)
    }

    abstract fun createEventString(fullData: CommandData, subCommand: T): String?

    open fun getReferenceId(command: CommandData): DomainId? = null
}

inline fun <reified T: Message> eventHandler(
    eventType: String? = null,
    crossinline referenceId: T.() -> DomainId? = { null },
    crossinline timestampGenerator: (T.() -> Instant?) = { null },
    crossinline handler: T.(fullData: CommandData) -> String?
) =
    object: EventHandler<T>(T::class.java, eventType) {
        override fun createEventString(fullData: CommandData, subCommand: T): String? {
            return subCommand.handler(fullData)
        }

        override fun generateTimestamp(command: CommandData) = command.data.unpack(klass).timestampGenerator()
            ?: super.generateTimestamp(command)

        override fun getReferenceId(command: CommandData) = command.data.unpack(klass).referenceId()
    }