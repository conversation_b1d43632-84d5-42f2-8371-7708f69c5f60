package gov.afrl.batdok.util

import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant

@Deprecated("DocumentItem was too broad, use CategorizedLinkableList instead", ReplaceWith("CategorizedLinkableList"))
abstract class DocumentList<ID: DomainId, T: DocumentItem<ID>> {
    var list: List<T> = listOf()
        internal set(value){
            field = value.sorted()
        }

    /**
     * A timestamped list of items removed because they were added in error
     */
    val onErrorRemovedItems = mutableListOf<Pair<Instant, T>>()

    /**
     * A timestamped list of items properly removed
     */
    val onProperRemovedItems = mutableListOf<Pair<Instant, T>>()

    operator fun get(id: ID) = list.find { it.id == id }
    operator fun get(type: ProtoEnum) = list.filter { it.category == type.dataString }
    operator fun get(name: String) = list.filter { it.category == name }
    operator fun contains(name: String) = list.any { it.category == name }
    operator fun contains(name: ProtoEnum) = contains(name.dataString)
    operator fun contains(id: ID) = list.any { it.id == id }

    fun getAll(vararg types: String) = list.filter { it.category in types }
    fun getAll(vararg type: ProtoEnum) = getAll(*type.map{it.dataString}.toTypedArray())

    fun getAllExcept(vararg name: ProtoEnum) = getAllExcept(*name.map{ it.dataString }.toTypedArray())
    fun getAllExcept(vararg name: String) = list.filter { t -> t.category !in name }

    fun count(name: String) = list.count { it.category == name }
    fun count(name: ProtoEnum) = count(name.dataString)
    val size: Int get() = list.size

    /**
     * Add or Update the event list. If null is passed, nothing happens
     */
    operator fun plusAssign(item: T?){
        item?.let { nonNullTreatment ->
            list = (list - nonNullTreatment.id) + nonNullTreatment
        }
    }
    operator fun List<T>.minus(id: ID) = filter { it.id != id }
    operator fun minus(id: ID) = list - id

    fun removeItem(itemId: ID, timestamp: Instant, documentationError: Boolean = false){
        val removedItem = get(itemId) ?: return
        list = list - itemId
        if(documentationError){
            onErrorRemovedItems
        }else{
            onProperRemovedItems
        }.add(Pair(timestamp, removedItem))
    }
}