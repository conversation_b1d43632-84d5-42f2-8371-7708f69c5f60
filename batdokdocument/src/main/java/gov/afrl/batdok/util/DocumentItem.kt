package gov.afrl.batdok.util

import com.google.gson.annotations.SerializedName
import gov.afrl.batdok.encounter.Document
import gov.afrl.batdok.encounter.Linkable
import gov.afrl.batdok.encounter.ids.DocumentId
import mil.af.afrl.batman.batdokid.DomainId
import java.io.Serializable
import java.time.Instant

@Deprecated("Use Linkable, Categorized, and Timestamped instead for new classes. Old ones need to stay as this class until DocumentItem no longer used in batdok")
abstract class DocumentItem<T: DomainId>(
    @SerializedName("id_base")
    override val id: T,
    @SerializedName("documentId_base")
    open val documentId: DocumentId,
    @SerializedName("category_base")
    override val category: String?,
    @SerializedName("timestamp_base")
    override val timestamp: Instant?
): Timestamped, Serializable, Categorized, Linkable {
    fun getLinks(document: Document) = document.links.list.filter { id in it.ids }
    @Deprecated("Use document.getLinkedItems() or Links.getLinksContainingThis() or object specific getter")
    fun getRelatedItems(document: Document) = document.getLinkedObjects(id).filter { it.first != this }
}