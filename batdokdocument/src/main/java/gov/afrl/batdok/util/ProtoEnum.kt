package gov.afrl.batdok.util

import gov.afrl.batdok.commands.proto.SharedProtobufObjects
import gov.afrl.batdok.commands.proto.compatibleEnum
import gov.afrl.batdok.encounter.changeOrClearEvent

/**
 * Implement this interface with an enum to handle an enum with a backup string for compatibility
 *
 * Each should have a unique dataString (don't use the empty string) and protoIndex (don't use 0)
 */
interface ProtoEnum{
    val dataString: String
    val protoIndex: Int
    fun toProto(includeStringAnyway: List<ProtoEnum> = listOf()) = compatibleEnum {
        enum = protoIndex
        if(this@ProtoEnum in includeStringAnyway){
            this.string = dataString
        }
    }
}

//Don't use these functions directly in most code. Implement them in a companion object of your enum

fun Array<out ProtoEnum>.protoToString(enum: SharedProtobufObjects.CompatibleEnum): String? {
    return find { it.protoIndex == enum.enum } //If it matches an enum, use that
        ?.dataString
        ?: enum.string.takeUnless { it.isNullOrEmpty() } //Otherwise, try the string value
}

fun <T: ProtoEnum> Array<T>.stringToProto(string: String?, includeStringAnyway: List<T> = listOf()) = compatibleEnum {
    val enumValue = find { it.dataString == string }
    if(enumValue != null){
        enum = enumValue.protoIndex
    }
    if((enumValue == null || enumValue in includeStringAnyway) && string != null){
        this.string = string
    }
}

fun Array<out ProtoEnum>.toChangeOrClearEvent(enum: SharedProtobufObjects.CompatibleEnum, type: String): String{
    val value = protoToString(enum)
    return changeOrClearEvent(
        !value.isNullOrEmpty(),
        value?:"",
        type
    )
}