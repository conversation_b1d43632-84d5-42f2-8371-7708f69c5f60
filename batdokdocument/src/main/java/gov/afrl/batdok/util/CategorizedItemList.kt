package gov.afrl.batdok.util

import gov.afrl.batdok.encounter.Linkable

interface ICategorizedItemList<T>: ILinkableList<T> where T: Categorized, T: Linkable {
    operator fun get(type: ProtoEnum) = list.filter { it.category == type.dataString }
    operator fun get(name: String) = list.filter { it.category == name }
    operator fun contains(name: String) = list.any { it.category == name }
    operator fun contains(name: ProtoEnum) = contains(name.dataString)

    fun getAll(vararg types: String) = list.filter { it.category in types }
    fun getAll(vararg type: ProtoEnum) = getAll(*type.map{it.dataString}.toTypedArray())

    fun getAllExcept(vararg name: ProtoEnum) = getAllExcept(*name.map{ it.dataString }.toTypedArray())
    fun getAllExcept(vararg name: String) = list.filter { t -> t.category !in name }

    fun count(name: String) = list.count { it.category == name }
    fun count(name: ProtoEnum) = count(name.dataString)
}

open class CategorizedItemList<T>: LinkableList<T>(), ICategorizedItemList<T> where T: Categorized, T: Linkable

class SortedCategorizedLinkableList<T>: CategorizedItemList<T>() where T: Categorized, T: Comparable<T>, T: Linkable{
    override val list: List<T>
        get() = super.list.sorted()
}