package gov.afrl.batdok.util

import java.time.*
import java.time.format.DateTimeFormatter
import java.util.Locale

enum class Patterns(val pattern: String) {
    dmy_space("dd MMM yyyy"),
    dmy_slash("dd/MM/yyyy"),
    dmy_dash("dd-MMM-yy"),
    ymd_dash("yyyy-MM-dd"),
    ymd("yyyyMMdd"),
    mdy_slash("MM/dd/yyyy"),
    mdy_dash("MM-dd-yyyy"),
    ymd_hms_24("yyyyMMdd_HHmmss"),
    ymdhms_24("yyyyMMddHHmmss"),
    ymdhms_24_dash_space_colon("yyyy-MM-dd HH:mm:ss"),
    ymdhmsS_24_dash_space_colon_dot("yyyy-MM-dd HH:mm:ss.S"),
    ISO_8601("yyyy-MM-dd'T'kk:mm:ss"),
    ymdhm_24_dash_space_colon("yyyy-MM-dd HH:mm"),
    ymd_hm_24("yyyyMMdd_HHmm"),
    mdyhm_24_dash_space_colon("MM-dd-yyyy HH:mm"),
    mdyhm_24_dash_comma_colon("MM-dd-yyyy, HH:mm"),
    mdyhms_24_dash_comma_colon("MM-dd-yyyy, HH:mm:ss"),
    mdhm_24_backslash_space_colon("MM/dd HH:mm"),
    mdyhm_24_space_comma_space_colon("MMM dd, yyyy HH:mm"),
    hmmdy_24_dash_space_colon_newline_utc("HH:mm\nMM-dd-yyyy"),
    hmmdy_24_dash_space_colon_newline("HH:mm\nMM-dd-yyyy"),
    mdhm_dash_24_space_comma_colon("MMM dd, HH:mm - "),
    mdhm_dash_24_space_colon("MMM dd HH:mm - "),
    mdhm_24_space_comma_colon("MMM dd, HH:mm"),
    mdhm_24_slash_space_colon("MM/dd HH:mm"),
    mdhm_24_slash_comma("MM/dd,HHmm"),
    dmyhms_24_dash_score_dash("dd-MM-yyyy_HH-mm-ss"),
    dmyhm_24_space_colon("dd MMM yyyy HH:mm"),
    dmyhms_24_dash_colon("dd-MM-YYYY HH:mm:ss"),
    hmmdy_24_space_comma("HHmm MMM dd, yyyy"),
    hm_24_colon("HH:mm"),
    hms_24_colon("HH:mm:ss"),
    h_24("HH"),
    m_24("mm"),
    emdhmsy_space_space_space_colon_colon_space_space("EEE MMM dd HH:mm:ss yyyy")
}

internal fun formatDuration(
    millis: Long,
    includeDays: Boolean = true,
    includeSeconds: Boolean = false
): String{
    val duration = Duration.ofMillis(millis)
    return listOfNotNull(
        duration.toDays()
            .takeIf { includeDays && it != 0L }
            ?.let { "${it}d" },
        duration.toHours()
            .takeIf { (it %24) != 0L }
            ?.let { if(includeDays) it % 24 else it }
            ?.let { "${it}h" },
        duration.toMinutes()
            .takeIf { (it%60) != 0L }
            ?.let { "${it % 60}m" },
        duration.seconds
            .takeIf { includeSeconds && (it%60) != 0L }
            ?.let { "${it % 60}s" },
    ).joinToString(" ")
}

/**
 * Will provide the proper format, to include additional formatting for zulu versus local system time
 * @param format includes the date/time pattern desired for the return value
 * @param useZuluTimeFormatting if true, will setup return string to format the returning time
 *                              to be in UTC, and if applicable, will append formatted string
 *                              with the 'Z' to indicate Zulu. if false, local system time
 *                              will be used with the format supplied.
 * @return The base [Instant] will be formatted with the pattern from [format], with edits if [useZuluTimeFormatting] is true.
 */
fun Instant.format(format: Patterns, useZuluTimeFormatting: Boolean = false): String {
    var dateTimeFormatter = DateTimeFormatter.ofPattern(format.pattern, Locale.US).withZone(ZoneId.systemDefault())
    if (useZuluTimeFormatting) {
        dateTimeFormatter = setupFormatterForZulu(format)
    }
    return atZone(dateTimeFormatter.zone).format(dateTimeFormatter)
}

/**
 * Method will return a formatted string representing the [LocalDate] receiver using the [format]
 * provided, as long as that format only contains Year,Month,Day elements. If the [format] includes
 * Hour/minute/second information, then an empty string is returned.
 */
fun LocalDate.format(format: Patterns) : String {
    val dateTimeFormatter = DateTimeFormatter.ofPattern(format.pattern, Locale.US)
    var stringValue = ""
    try {
        stringValue = format(dateTimeFormatter)
    } catch (_: DateTimeException){ /*ignore exception*/ }
    return stringValue
}

private fun setupFormatterForZulu(format: Patterns): DateTimeFormatter {
    val formatString =
        when (format) {
            Patterns.m_24, Patterns.h_24, Patterns.hmmdy_24_dash_space_colon_newline_utc -> {
                format.pattern
            }
            Patterns.mdhm_dash_24_space_comma_colon -> "MMM dd, HH:mm'Z' - "
            Patterns.mdhm_dash_24_space_colon -> "MMM dd HH:mm'Z' - "
            else -> format.pattern + "'Z'"
        }
    return DateTimeFormatter.ofPattern(formatString, Locale.US).withZone(ZoneOffset.UTC)
}

fun toNullableInstant(timestamp: Long): Instant?{
    if (timestamp == 0L) {
        return null
    } else {
        return Instant.ofEpochSecond(timestamp)
    }
}