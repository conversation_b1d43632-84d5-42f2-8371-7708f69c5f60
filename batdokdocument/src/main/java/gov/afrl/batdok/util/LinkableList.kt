package gov.afrl.batdok.util

import gov.afrl.batdok.encounter.Linkable
import mil.af.afrl.batman.batdokid.DomainId
import java.io.Serializable
import java.time.Instant

interface ILinkableList<T: Linkable>: Serializable {
    val list: List<T>
    val size: Int
        get() = list.size

    /**
     * A timestamped list of items removed because they were added in error
     */
    val onErrorRemovedItems: List<Pair<Instant, T>>

    /**
     * A timestamped list of items properly removed
     */
    val onProperRemovedItems: List<Pair<Instant, T>>

    operator fun get(id: DomainId) = list.find { it.id == id }
    operator fun contains(id: DomainId) = list.any { it.id == id }

    /**
     * Add or Update the event list. If null is passed, nothing happens
     */
    operator fun plusAssign(item: T?)
    operator fun plusAssign(items: Collection<T?>){
        items.forEach{ plusAssign(it) }
    }
    operator fun List<T>.minus(id: DomainId) = filter { it.id != id }
    operator fun minus(id: DomainId) = list - id

    fun removeItem(itemId: DomainId, timestamp: Instant, documentationError: Boolean = false)
}

open class LinkableList<T: Linkable>: ILinkableList<T>{
    var _list = mutableListOf<T>()
    override val list: List<T>
        get() = _list

    val _onErrorRemovedItems = mutableListOf<Pair<Instant, T>>()
    val _onProperRemovedItems = mutableListOf<Pair<Instant, T>>()
    override val onErrorRemovedItems: List<Pair<Instant, T>>
        get() = _onErrorRemovedItems
    override val onProperRemovedItems: List<Pair<Instant, T>>
        get() = _onProperRemovedItems

    override fun removeItem(itemId: DomainId, timestamp: Instant, documentationError: Boolean) {
        val removedItem = get(itemId) ?: return
        _list = (list - itemId).toMutableList()
        if(documentationError){
            _onErrorRemovedItems
        }else{
            _onProperRemovedItems
        }.add(Pair(timestamp, removedItem))
    }

    override fun plusAssign(item: T?) {
        item?.let { nonNullTreatment ->
            _list = (this - item.id + nonNullTreatment).toMutableList()
        }
    }
}

class SortedLinkableList<T>: LinkableList<T>() where T: Linkable, T: Comparable<T>{
    override val list: List<T>
        get() = _list.sorted()
}