package gov.afrl.batdok.util

import gov.afrl.batdok.encounter.Name

/**
 * Finds the first, middle, middle initial, and last names from a given name.
 * Capable of safely handling multiple middle names, missing names, and any whitespace.
 *
 * - If only one name is present it will be assumed to be the last name.
 * - If only two names are present they will be assumed to be the first and last names, in that order
 * - If three or more names are present, they will be assumed to be first middle [...] last, in that order
 *
 * [first] will contain the first name.
 * [middle] will contain one or more middle names.
 * [middleInitial] will contain the first letter of the first middle name.
 * [last] will contain the last name.
 *
 * Any name which is not present in the provided string will be set to an empty string
 *
 * @param name The name to be formatted, in First Middle Last format.
 */
class NameFormatter(val name: String, callsign: String = "") {
    constructor(names: Triple<String, String, String>): this("${names.first} ${names.second} ${names.third}")

    val first: String
    val firstInitial: String
    val middle: String
    val middleInitial: String
    val last: String
    private val isNameCallsign: Boolean = name.split("_").firstOrNull() == callsign

    init {
        val names = if (name.startsWith(" "))
            listOf("") + (name.trim().split(" ").filter { it != "" })
        else
            name.trim().split(" ").filter { it != "" }
        last = if (names.isNotEmpty()) names.last() else ""
        first = if (names.size > 1) names.first() else ""
        firstInitial = if (first.isNotEmpty()) first.first().toString() else ""
        middle = if (names.size > 2) names.drop(1).dropLast(1).joinToString(" ").trim() else ""
        middleInitial = if (middle.isNotEmpty()) middle.first().toString() else ""
    }

    fun toTriple() = Triple(first, middle, last)

    fun toString(format: Format): String{
        return if (!isNameCallsign) {
            when(format){
                Format.FIRST_LAST -> "$first $last".trim()
                Format.FIRST_MIDDLE_LAST -> "$first $middle $last".trim().replace(" +".toRegex(), " ")
                Format.LAST_FIRST_INITIAL -> "$last, $firstInitial.".trim().removeSuffix(", .")
                Format.LAST_FIRST_MIDDLE -> "$last, $first $middle".trim().removeSuffix(",").removePrefix(", ")
                Format.LAST_FIRST_MIDDLE_INITIAL -> "$last, $first $middleInitial".trim().removeSuffix(",").removePrefix(", ")
                Format.LAST_FIRST -> "$last, $first".trim().removeSuffix(",")
            }
        } else {
            name
        }
    }

    enum class Format(private val parsingRegex: Regex){
        FIRST_LAST("(?:(\\w+)\\s)?(\\w+)".toRegex()), // 1: First, 2: last
        FIRST_MIDDLE_LAST("(?:(\\w+)\\s)?(?:(\\w+)\\s)?(\\w+)".toRegex()), //1: First 2: Middle 3: Last
        LAST_FIRST_MIDDLE("(\\w+)(?:,(?: (\\w+))?(?: (\\w+))?)?".toRegex()), // 1: Last, 2: First, 3: Middle
        LAST_FIRST_MIDDLE_INITIAL("(\\w+)(?:,(?: (\\w{2,}))?(?: (\\w))?)?".toRegex()), // 1: Last, 2: First, 3: Middle Initial
        LAST_FIRST_INITIAL("(\\w+)(?:, (\\w))?".toRegex()),// 1: Last, 2: First Initial
        LAST_FIRST("(\\w+)(?:, (\\w+))?".toRegex())// 1: Last, 2: First
        ;

        /**
         * Parses a name string into a Name object. When a name string is ambiguous about being a first, middle, or last name
         * the function will assume Last name, then first name, then middle name.
         * (e.g. For the format FIRST_LAST, the string "Name" will save as a last name)
         */
        fun parse(fullName: String): Name {
            val groups = parsingRegex.find(fullName.trim())?.groups
            val name = when(this){
                FIRST_LAST -> Name(groups?.get(1)?.value, null,  groups?.get(2)?.value)
                FIRST_MIDDLE_LAST -> Name(groups?.get(1)?.value, groups?.get(2)?.value, groups?.get(3)?.value)
                LAST_FIRST_MIDDLE -> Name(groups?.get(2)?.value, groups?.get(3)?.value, groups?.get(1)?.value)
                LAST_FIRST_MIDDLE_INITIAL -> Name(groups?.get(2)?.value, groups?.get(3)?.value, groups?.get(1)?.value)
                LAST_FIRST_INITIAL -> Name(groups?.get(2)?.value, null, groups?.get(1)?.value)
                LAST_FIRST -> Name(groups?.get(2)?.value, null, groups?.get(1)?.value)
            }
            return name
        }
    }
}