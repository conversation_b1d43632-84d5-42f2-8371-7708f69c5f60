package gov.afrl.batdok.util

import gov.afrl.batdok.encounter.DrawingPoint

/**
 * Class is responsible for setting up the bounding boxes containing each body part on the diagram
 */
class DD1380InjuryBoundingBox(
    private val x1: Float,
    private val y1: Float,
    private val x2: Float,
    private val y2: Float,
    val text: String
) {

    /**
     * Returns whether or not a point is within the bounding box
     *
     * @param point - The point to check if it is within the bounds of the box
     * @return - True if the point is within bounds, false otherwise
     */
    operator fun contains(point: DrawingPoint): Boolean {
        val minX = minOf(x1, x2)
        val maxX = maxOf(x1, x2)
        val minY = minOf(y1, y2)
        val maxY = maxOf(y1, y2)

        return !(point.x < minX || point.x > maxX || point.y < minY || point.y > maxY)
    }

    companion object {
        /**
         * Takes in a simple point and will return which fine location the point is located in
         * @param point - the point to check
         * @return - Returns the arm or leg associated with the point, "Other" otherwise
         */
        fun getFineLocation(point: DrawingPoint, k9: Boolean = false): String? {
            for (box in if(k9) allK9Boxes else allBoxes) {
                if (box.contains(point)) return box.text
            }
            return null
        }

        /**
         * Gets all the bounding boxes associated with human body parts
         */
        val allBoxes = listOf(
            DD1380InjuryBoundingBox(.03f, .5f, .11f, .6f, "Anterior Right Hand"),
            DD1380InjuryBoundingBox(.38f, .5f, .47f, .6f, "Anterior Left Hand"),
            DD1380InjuryBoundingBox(.54f, .5f, .63f, .6f, "Posterior Left Hand"),
            DD1380InjuryBoundingBox(.9f, .5f, .98f, .6f, "Posterior Right Hand"),

            DD1380InjuryBoundingBox(.17f, .9f, .25f, .95f, "Anterior Right Foot"),
            DD1380InjuryBoundingBox(.25f, .9f, .33f, .95f, "Anterior Left Foot"),
            DD1380InjuryBoundingBox(.69f, .9f, .765f, .95f, "Posterior Left Foot"),
            DD1380InjuryBoundingBox(.765f, .9f, .84f, .95f, "Posterior Right Foot"),

            DD1380InjuryBoundingBox(.18f, .86f, .24f, .9f, "Anterior Right Ankle"),
            DD1380InjuryBoundingBox(.255f, .86f, .315f, .9f, "Anterior Left Ankle"),
            DD1380InjuryBoundingBox(.7f, .86f, .755f, .9f, "Posterior Left Ankle"),
            DD1380InjuryBoundingBox(.765f, .86f, .83f, .9f, "Posterior Right Ankle"),

            DD1380InjuryBoundingBox(.15f, .71f, .24f, .86f, "Anterior Right Lower Leg"),
            DD1380InjuryBoundingBox(.26f, .71f, .35f, .86f, "Anterior Left Lower Leg"),
            DD1380InjuryBoundingBox(.67f, .71f, .75f, .86f, "Posterior Left Lower Leg"),
            DD1380InjuryBoundingBox(.77f, .71f, .86f, .86f, "Posterior Right Lower Leg"),

            DD1380InjuryBoundingBox(.155f, .655f, .235f, .72f, "Anterior Right Knee"),
            DD1380InjuryBoundingBox(.255f, .655f, .34f, .72f, "Anterior Left Knee"),
            DD1380InjuryBoundingBox(.67f, .655f, .76f, .72f, "Posterior Left Knee"),
            DD1380InjuryBoundingBox(.77f, .655f, .85f, .72f, "Posterior Right Knee"),

            DD1380InjuryBoundingBox(.14f, .5f, .2451f, .66f, "Anterior Right Upper Leg"),
            DD1380InjuryBoundingBox(.2451f, .5f, .355f, .66f, "Anterior Left Upper Leg"),
            DD1380InjuryBoundingBox(.65f, .5f, .765f, .66f, "Posterior Left Upper Leg"),
            DD1380InjuryBoundingBox(.765f, .5f, .87f, .66f, "Posterior Right Upper Leg"),

            DD1380InjuryBoundingBox(.14f, .44f, .21f, .5f, "Anterior Right Hip"),
            DD1380InjuryBoundingBox(.285f, .44f, .355f, .5f, "Anterior Left Hip"),
            DD1380InjuryBoundingBox(.655f, .44f, .72f, .5f, "Posterior Left Hip"),
            DD1380InjuryBoundingBox(.80f, .44f, .87f, .5f, "Posterior Right Hip"),

            DD1380InjuryBoundingBox(.21f, .44f, .285f, .55f, "Anterior Groin"),
            DD1380InjuryBoundingBox(.72f, .44f, .80f, .5f, "Posterior Lower Torso"),

            DD1380InjuryBoundingBox(.06f, .47f, .125f, .5f, "Anterior Right Wrist"),
            DD1380InjuryBoundingBox(.375f, .47f, .445f, .5f, "Anterior Left Wrist"),
            DD1380InjuryBoundingBox(.575f, .47f, .635f, .5f, "Posterior Left Wrist"),
            DD1380InjuryBoundingBox(.89f, .47f, .95f, .5f, "Posterior Right Wrist"),

            DD1380InjuryBoundingBox(.07f, .39f, .16f, .44f, "Anterior Right Lower Arm"),
            DD1380InjuryBoundingBox(.07f, .44f, .14f, .47f, "Anterior Right Lower Arm"),
            DD1380InjuryBoundingBox(.355f, .44f, .43f, .47f, "Anterior Left Lower Arm"),
            DD1380InjuryBoundingBox(.34f, .39f, .42f, .44f, "Anterior Left Lower Arm"),
            DD1380InjuryBoundingBox(.575f, .44f, .655f, .47f, "Posterior Left Lower Arm"),
            DD1380InjuryBoundingBox(.58f, .39f, .67f, .44f, "Posterior Left Lower Arm"),
            DD1380InjuryBoundingBox(.87f, .44f, .94f, .47f, "Posterior Right Lower Arm"),
            DD1380InjuryBoundingBox(.855f, .39f, .935f, .44f, "Posterior Right Lower Arm"),

            DD1380InjuryBoundingBox(.085f, .3f, .165f, .39f, "Anterior Right Upper Arm"),
            DD1380InjuryBoundingBox(.325f, .3f, .41f, .39f, "Anterior Left Upper Arm"),
            DD1380InjuryBoundingBox(.595f, .3f, .675f, .39f, "Posterior Left Upper Arm"),
            DD1380InjuryBoundingBox(.85f, .3f, .925f, .39f, "Posterior Right Upper Arm"),

            DD1380InjuryBoundingBox(.1f, .2f, .165f, .3f, "Anterior Right Shoulder"),
            DD1380InjuryBoundingBox(.325f, .2f, .395f, .3f, "Anterior Left Shoulder"),
            DD1380InjuryBoundingBox(.62f, .2f, .675f, .3f, "Posterior Left Shoulder"),
            DD1380InjuryBoundingBox(.85f, .2f, .905f, .3f, "Posterior Right Shoulder"),

            DD1380InjuryBoundingBox(.165f, .33f, .325f, .44f, "Anterior Lower Torso"),
            DD1380InjuryBoundingBox(.165f, .21f, .325f, .33f, "Anterior Upper Torso"),
            DD1380InjuryBoundingBox(.675f, .33f, .85f, .44f, "Posterior Lower Torso"),
            DD1380InjuryBoundingBox(.675f, .21f, .85f, .33f, "Posterior Upper Torso"),

            DD1380InjuryBoundingBox(.2f, .17f, .3f, .21f, "Anterior Neck"),
            DD1380InjuryBoundingBox(.19f, .05f, .31f, .17f, "Anterior Head"),
            DD1380InjuryBoundingBox(.71f, .16f, .81f, .21f, "Posterior Neck"),
            DD1380InjuryBoundingBox(.7f, .05f, .82f, .16f, "Posterior Head"),
        )

        /**
         * Gets all the bounding boxes associated with K9 body parts
         */
        val allK9Boxes = listOf<DD1380InjuryBoundingBox>()
    }
}