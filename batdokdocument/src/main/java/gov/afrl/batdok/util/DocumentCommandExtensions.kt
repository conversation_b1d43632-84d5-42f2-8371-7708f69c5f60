package gov.afrl.batdok.util

import gov.afrl.batdok.commands.proto.DocumentCommands.DocumentCommand
import gov.afrl.batdok.commands.proto.copy
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.encounter.ids.toDomainId
import kotlin.jvm.Throws

/**
 * Turn a document command (consisting of multiple actions) into a list of document commands each consisting of one action
 */
fun DocumentCommand.flatten(): List<DocumentCommand> = this.commandsList.map {
    this.copy {
        commands.clear()
        commands.add(it)
    }
}

/**
 * Turn a list of document commands (consisting of multiple actions) into a list of document commands each consisting of one action
 */
fun List<DocumentCommand>.flatten(): List<DocumentCommand> = flatMap { it.flatten() }

/**
 * Turn a list of document commands into a single document command consisting of multiple actions
 * Null if the list is empty.
 */
fun List<DocumentCommand>.combine(): DocumentCommand? = if (this.isEmpty()) {
    null
} else {
    this[0].copy {
        commands.clear()
        commands.addAll(<EMAIL> { it.commandsList })
    }
}

/**
 * Turn a list of document commands into a single document command consisting of multiple actions
 *
 * Grouped by encounter
 */
fun List<DocumentCommand>.combineByEncounter(): Map<DocumentId, DocumentCommand?> =
    groupBy { it.encounterId.toDomainId<DocumentId>() }
        .mapValues { it.value.combine() }

/**
 * Takes a list of document commands and turns it into a set of single document commands with distinct ids
 */
fun List<DocumentCommand>.toDistinctSet() = flatten().distinctBy { it.commandsList[0].commandId.toDomainId<CommandId>() }.toSet()

/**
 * Calculate the checksum of the command by adding up the command IDs
 */
fun DocumentCommand.calculateChecksum() = commandsList.sumOf { it.commandId.toByteArray().sum() }.toByte()