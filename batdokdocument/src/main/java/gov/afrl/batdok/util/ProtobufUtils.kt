package gov.afrl.batdok.util

import com.google.protobuf.*
import com.google.protobuf.Descriptors.FieldDescriptor
import gov.afrl.batdok.commands.proto.*
import gov.afrl.batdok.commands.proto.DocumentCommands.CommandData
import gov.afrl.batdok.commands.proto.SharedProtobufObjects.*
import gov.afrl.batdok.encounter.ids.CommandId
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.encounter.ids.OwnerId
import gov.afrl.batdok.encounter.ids.toByteString
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant
import java.time.LocalDate

fun int32Value(int: Int) = int32Value { value = int }
fun int64Value(long: Long) = int64Value { value = long }
fun stringValue(string: String) = stringValue { value = string }
fun floatValue(float: Float) = floatValue { value = float }
fun doubleValue(double: Double) = doubleValue { value = double }
fun booleanValue(boolean: Boolean) = boolValue { value = boolean }

fun nullableInt64Value(long: Long?) = nullableInt64Value { long?.let { value = int64Value(it) } }
fun nullableInt64Value(instant: Instant?) = nullableInt64Value { instant?.let { value = int64Value(it.epochSecond) } }
fun NullableInt64Value.toPrimitive(): Long? = valueOrNull?.value
fun NullableInt64Value.toInstant() = toPrimitive()?.let { Instant.ofEpochSecond(it) }
fun NullableInt64Value.toLocalDate() = toPrimitive()?.let { LocalDate.ofEpochDay(it) }

fun nullableInt32Value(int: Int?) = nullableInt32Value { int?.let { value = int32Value(it) } }
fun NullableInt32Value.toPrimitive(): Int? = valueOrNull?.value

fun nullableStringValue(string: String?) = nullableStringValue { string?.let { value = stringValue(it) } }
fun NullableStringValue.toPrimitive(): String? = valueOrNull?.value

fun compatibleEnum(string: String?) = compatibleEnum { string?.let { this.string = it } }
fun CompatibleEnum?.toPrimitive(): String? = this?.string.takeUnless { it.isNullOrBlank() }

fun nullableBoolValue(bool: Boolean?) = nullableBoolValue { bool?.let { value = booleanValue(it) } }
fun NullableBoolValue.toPrimitive(): Boolean? = valueOrNull?.value

fun nullableFloatValue(float: Float?) = nullableFloatValue { float?.let { value = floatValue(it) } }
fun NullableFloatValue.toPrimitive(): Float? = valueOrNull?.value

fun nullableDoubleValue(double: Double?) = nullableDoubleValue { double?.let { value = doubleValue(it) } }
fun NullableDoubleValue.toPrimitive(): Double? = valueOrNull?.value

// v5.0 = Encounter Version 1
val CURRENT_ENCOUNTER_VERSION = 1

fun CompatibilityRange.isCompatible(): Boolean{
    val start = inclusiveStart
    val end = exclusiveEnd.takeUnless { it == 0 } ?: Int.MAX_VALUE
    return CURRENT_ENCOUNTER_VERSION in (start until end)
}
fun CommandData.isCompatible(): Boolean{
    return if(hasCompatibleRange()){
        compatibleRange.isCompatible()
    }else{
        true
    }
}

fun buildCommandData(
    subCommand: Message,
    callsign: String = "",
    timestamp: Instant? = Instant.now(),
    commandId: CommandId = DomainId.create(),
    compatibilityRange: Pair<Int?, Int?>? = null
) = commandData {
    data = com.google.protobuf.Any.pack(subCommand, "")
    this.callsign = callsign
    timestamp?.let{
        this.timestamp = it.epochSecond
    }
    this.commandId = commandId.toByteString()
    compatibilityRange?.let{
        this.compatibleRange = compatibilityRange {
            it.first?.let { inclusiveStart = it }
            it.second?.let { exclusiveEnd = it }
        }
    }
}

fun buildDocumentCommand(
    encounterId: DocumentId,
    ownerId: OwnerId = DomainId.nil(),
    checksum: Byte = 0,
    mode: String = "",
    commandData: List<CommandData>
) = documentCommand {
    this.encounterId = encounterId.toByteString()
    this.ownerId = ownerId.toByteString()
    this.checksum = checksum.toInt()
    this.mode = mode
    this.commands.addAll(commandData)
}

fun buildDocumentCommand(
    encounterId: DocumentId,
    vararg commandData: CommandData
) = buildDocumentCommand(encounterId, commandData = commandData.toList())

object ProtobufUtils {

    /**
     * Recursively goes through the [message] to find any fields matching the [fieldName]. Returns a list of the values of the matching fields
     */
    fun getFieldsWithName(message: Message, fieldName: String): List<Any?> {
        return message.allFields.flatMap {
            when {
                it.key.name == fieldName ->
                    if (it.key.isRepeated) {
                        (it.value as List<*>)
                    } else {
                        listOf(it.value)
                    }
                it.value is Message -> getFieldsWithName(it.value as Message, fieldName)
                it.key.isRepeated -> (it.value as List<*>)
                    .filterIsInstance<Message>()
                    .flatMap { message -> getFieldsWithName(message, fieldName) }
                else -> listOf()
            }
        }
    }

    /**
     * Recursively goes through the [message] to find any unknown fields. Returns true if any are found
     */
    fun hasUnknownFields(message: Message): Boolean {
        return if (message.unknownFields.asMap().isNotEmpty()) {
            true
        } else {
            message.allFields
                .filter { it.value is Message }
                .any {
                    if (it.key.isRepeated) {
                        (it.value as List<*>).any { message -> hasUnknownFields(message as Message) }
                    } else {
                        hasUnknownFields(it.value as Message)
                    }
                }
        }
    }
}

/**
 * Build a message of the same type without the fields that were set in the previous message
 */
fun <T: Message> T.removeDuplicates(oldMessage: Message?): T{
    val builder = this.toBuilder()
    oldMessage?.allFields?.forEach{ (descriptor, any) ->
        //If they are the same as the new protobuf...
        if(any == getField(descriptor)){
            builder.clearField(descriptor)
        }else if(descriptor.type == FieldDescriptor.Type.MESSAGE && !descriptor.isRepeated){
            if(hasField(descriptor)) {
                val newField = getField(descriptor) as Message
                val oldField = oldMessage.getField(descriptor) as Message
                builder.setField(descriptor, newField.removeDuplicates(oldField))
            }
        }
    }
    return builder.build() as T
}

/**
 * Create an [UnknownFieldSet] combining the two messages based on the following rules:
 * 1. Start with the complete old message
 * 2. Add all of the fields of the new message to the old message.
 * 3. Where two fields are the same index, take the new message's value.
 */
fun <T: Message> T.addPreviousFields(oldMessage: Message?): UnknownFieldSet{
    return UnknownFieldSet.parseFrom(oldMessage?.toByteArray()?: byteArrayOf())
        .toBuilder()
        .apply {
            <EMAIL> { (descriptor, _) ->
                clearField(descriptor.number)
            }
            <EMAIL>().forEach { (number, _) ->
                clearField(number)
            }
            mergeFrom(<EMAIL>())
        }
        .build()
}