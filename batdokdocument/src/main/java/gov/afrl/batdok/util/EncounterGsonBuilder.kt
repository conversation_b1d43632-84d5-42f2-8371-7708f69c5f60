package gov.afrl.batdok.util

import com.google.gson.*
import com.google.gson.stream.JsonReader
import com.google.gson.stream.JsonWriter
import gov.afrl.batdok.encounter.treatment.*
import java.io.*
import java.lang.reflect.Type
import java.time.Instant
import java.time.LocalDate

class EncounterGsonBuilder {
    companion object {
        class TreatmentDataAdapter : JsonSerializer<TreatmentData>,
            JsonDeserializer<TreatmentData> {
            override fun serialize(src: TreatmentData, typeOfSrc: Type, context: JsonSerializationContext): JsonElement {
                val jsonObject = JsonObject()
                jsonObject.addProperty("type", src::class.java.simpleName)
                jsonObject.add("data", context.serialize(src))
                return jsonObject
            }

            override fun deserialize(json: JsonElement, typeOfT: Type, context: JsonDeserializationContext): TreatmentData {
                val jsonObject = json.asJsonObject
                val type = jsonObject.get("type").asString
                val data = jsonObject.get("data")
                return when (jsonObject.get("type").asString) {
                    "FingerThorData" -> context.deserialize(data, FingerThorData::class.java)
                    "EscharatomyData" -> context.deserialize(data, EscharatomyData::class.java)
                    "ImmobilizationData" -> context.deserialize(data, ImmobilizationData::class.java)
                    "FasciotomyData" -> context.deserialize(data, FasciotomyData::class.java)
                    "ChestSealData" -> context.deserialize(data, ChestSealData::class.java)
                    "SplintData" -> context.deserialize(data, SplintData::class.java)
                    "DirectPressureData" -> context.deserialize(data, DirectPressureData::class.java)
                    "HypothermiaPreventionData" -> context.deserialize(data, HypothermiaPreventionData::class.java)
                    "DebridementData" -> context.deserialize(data, DebridementData::class.java)
                    "SuctionData" -> context.deserialize(data, SuctionData::class.java)
                    "IntubatedByData" -> context.deserialize(data, IntubatedByData::class.java)
                    "LateralCanthotomyData" -> context.deserialize(data, LateralCanthotomyData::class.java)
                    "HyperthermiaPreventionData" -> context.deserialize(data, HyperthermiaPreventionData::class.java)
                    "ForeignBodyRemovalData" -> context.deserialize(data, ForeignBodyRemovalData::class.java)
                    "NeedleDData" -> context.deserialize(data, NeedleDData::class.java)
                    "TqData" -> context.deserialize(data, TqData::class.java)
                    else -> throw JsonParseException("Unknown type: ${type}")
                }
            }
        }
        fun build(gsonBuilder: GsonBuilder = GsonBuilder()): GsonBuilder = gsonBuilder.setPrettyPrinting()
            .registerTypeAdapter(Instant::class.java, object : TypeAdapter<Instant>() {
                override fun write(out: JsonWriter?, value: Instant?) {
                    out?.beginObject()
                    out?.name("seconds")?.value(value?.epochSecond)
                    out?.name("nanos")?.value(value?.nano)
                    out?.endObject()
                }

                override fun read(`in`: JsonReader?): Instant? {
                    var seconds: Long? = null
                    var nanos: Int? = null

                    `in`?.beginObject()
                    while (`in`?.hasNext() == true) {
                        when (`in`.nextName()) {
                            "seconds" -> seconds = `in`.nextLong()
                            "nanos" -> nanos = `in`.nextInt()
                        }
                    }
                    `in`?.endObject()

                    return if (seconds != null && nanos != null) {
                        Instant.ofEpochSecond(seconds, nanos.toLong())
                    } else {
                        null
                    }
                }
            })
            .registerTypeAdapter(LocalDate::class.java,object: TypeAdapter<LocalDate>(){
                override fun write(out: JsonWriter?, value: LocalDate?) {
                    out?.value(value?.toEpochDay())
                }

                override fun read(`in`: JsonReader?): LocalDate? {
                    return `in`?.nextLong()?.let{
                        LocalDate.ofEpochDay(it)
                    }
                }

            })
            .registerTypeAdapter(TreatmentData::class.java, TreatmentDataAdapter())
    }
}