package mil.afrl.batdok.pdfrobots

import com.tom_roush.pdfbox.pdmodel.PDDocument

fun cg5214(document: PDDocument, func: CG5214PdfRobot.()->Unit) = CG5214PdfRobot(document).apply(func)
class CG5214PdfRobot(document: PDDocument): PdfRobot(document) {
    private fun assertField(field: String, value: String) = assertFields{
        field matches value
    }

    fun assertPatientName(name: String) = assertField("VictimName1", name)
    fun assertSex(isMale: Boolean?) {
        when(isMale){
            true -> assertField("Male", "X")
            false -> assertField("Female", "X")
            null -> {
                assertField("Male", "")
                assertField("Female", "")
            }
        }
    }
    fun assertProviderName(name: String) = assertField("RescuerName", name)
    fun assertProviderUnit(unit: String) = assertField("RescuerUnit", unit)
    fun assertIncidentDate(date: String) = assertField("IncidentDate", date)
    fun assertIncidentTime(time: String) = assertField("IncidentTime", time)
    fun assertAviationChecked(isChecked: Boolean) = assertField("Aviation", if(isChecked) "X" else "")
    fun assertAutoChecked(isChecked: Boolean) = assertField("Auto", if(isChecked) "X" else "")
    fun assertOtherChecked(isChecked: Boolean) = assertField("Other", if(isChecked) "X" else "")
    fun assertNatureOfEmergency(nom: String) = assertField("NatureOfEmergency", nom)
    fun assertIncidentLocation(loc: String) = assertField("IncidentLocation1", loc)
    fun assertDressingChecked(isChecked: Boolean) = assertField("Dressing", if(isChecked) "X" else "")
    fun assertSplintChecked(isChecked: Boolean) = assertField("Splint", if(isChecked) "X" else "")
    fun assertTourniquetChecked(isChecked: Boolean) = assertField("Tourniquet", if(isChecked) "X" else "")
    fun assertOxygenChecked(isChecked: Boolean) = assertField("Oxygen", if(isChecked) "X" else "")
    fun assertCCollarChecked(isChecked: Boolean) = assertField("CCollar", if(isChecked) "X" else "")
    fun assertVitalTime(time: String) = assertField("Time1", time)
    fun assertVerbalChecked(isChecked: Boolean) = assertField("Verbal1", if(isChecked) "X" else "")
    fun assertPulseRate(vital: String) = assertField("PulseRate1", vital)
    fun assertRespRate(vital: String) = assertField("RespRate1", vital)
    fun assertSystolic(vital: String) = assertField("Systolic1", vital)
    fun assertDiastolic(vital: String) = assertField("Diastolic1", vital)
    fun assertTemp(temp: String) = assertField("OralTemp1", temp)
    fun assertAllergies(text: String) = assertField("Allergies", text)
    fun assertMedTime(text: String) = assertField("MedTime1", text)
    fun assertMedName(text: String) = assertField("MedName1", text)
    fun assertMedDose(text: String) = assertField("MedDose1", text)
    fun assertPriorityChecked(priorityNumber: String) = assertField("Priority$priorityNumber", "X")
    fun assertMedicalHistory(text: String) = assertField("MedicalHistory", text)

}