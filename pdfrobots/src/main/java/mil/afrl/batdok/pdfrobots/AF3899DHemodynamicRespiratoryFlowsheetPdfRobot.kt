package mil.afrl.batdok.pdfrobots

import com.tom_roush.pdfbox.pdmodel.PDDocument

fun af3899D(document: PDDocument, func: AF3899DHemodynamicRespiratoryFlowsheetPdfRobot.() -> Unit) = AF3899DHemodynamicRespiratoryFlowsheetPdfRobot(document).apply(func)

class AF3899DHemodynamicRespiratoryFlowsheetPdfRobot(document: PDDocument): PdfRobot(document){
    private fun assertField(field: String, value: String) = assertFields{
        // test the value set to the field
        field matches value
    }


    fun assertNamePage1(name: String) = assertField("NAME", name)
    fun assertCiteSSNPage1(citeSsn: String)= assertField("CITE SSN", citeSsn)

    fun assertNamePage2(name: String) = assertField("NAME_2", name)
    fun assertCiteSSNPage2(citeSsn: String)= assertField("CITE SSN_2", citeSsn)
}