package mil.afrl.batdok.pdfrobots

import com.tom_roush.pdfbox.pdmodel.PDDocument

fun af3899I(document: PDDocument, func: AF3899IPdfRobot.() -> Unit) = AF3899IPdfRobot(document).apply(func)

class AF3899IPdfRobot(document: PDDocument): PdfRobot(document){
    private fun assertField(field: String, value: String) = assertFields{
        // test the value set to the field
        field matches value
    }

    val sectionOnePage1Name = "SectionOnePage1Name"
    val sectionOnePage2Name = "SectionOnePage2Name"

    val sectionOnePage1Allergies = "SectionOnePage1Allergies"
    val sectionOnePage2Allergies = "SectionOnePage2Allergies"

    val sectionOnePage1CiteSSN = "SectionOnePage1CiteSSN"
    val sectionOnePage2CiteSSN = "SectionOnePage2CiteSSN"

    val sectionOnePage1Grade = "SectionOnePage1Grade"
    val sectionOnePage2Grade = "SectionOnePage2Grade"

    val originationMTF = "OriginationMTF"
    val destinationMTF = "DestinationMTF"


    fun assertName(name: String) = assertField(sectionOnePage1Name, name)
    fun assertName2(name: String) = assertField(sectionOnePage2Name, name)

    fun assertAllergies(allergies: String) = assertField(sectionOnePage1Allergies, allergies)
    fun assertAllergies2(allergies: String) = assertField(sectionOnePage2Allergies, allergies)

    fun assertCiteSSN(citeSSN: String) = assertField(sectionOnePage1CiteSSN, citeSSN)
    fun assertCiteSSN2(citeSSN: String) = assertField(sectionOnePage2CiteSSN, citeSSN)

    fun assertGrade(grade: String) = assertField(sectionOnePage1Grade, grade)
    fun assertGrade2(grade: String) = assertField(sectionOnePage2Grade, grade)

    fun assertOriginationMTF(origMTF: String) = assertField(originationMTF, origMTF)
    fun assertDestinationMTF(destMTF: String) = assertField(destinationMTF, destMTF)
}