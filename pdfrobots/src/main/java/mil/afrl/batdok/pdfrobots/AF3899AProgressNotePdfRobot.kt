package mil.afrl.batdok.pdfrobots

import com.tom_roush.pdfbox.pdmodel.PDDocument



@Deprecated("Use af3899AProgressNote instead", ReplaceWith("af3899AProgressNote"))
fun ae3899AProgressNote(document: PDDocument, func: AE3899AProgressNotePdfRobot.() -> Unit) = AE3899AProgressNotePdfRobot(document).apply(func)
fun af3899AProgressNote(document: PDDocument, func: AF3899AProgressNotePdfRobot.() -> Unit) = AF3899AProgressNotePdfRobot(document).apply(func)

@Deprecated("Use AF3899AProgressNotePdfRobot instead", ReplaceWith("AF3899AProgressNotePdfRobot"))
typealias AE3899AProgressNotePdfRobot = AF3899AProgressNotePdfRobot
class AF3899AProgressNotePdfRobot(document: PDDocument): PdfRobot(document) {
    private fun assertField(field: String, value: String) = assertFields{
        // test the value set to the field
        field matches value
    }

    // Section One
    val dodIdField = "SSN"
    val statusField = "STATUS"
    val serviceField = "SERVICE"

    fun getNameFieldKey(page: Int): String {
        return "NAME Last First Middle Initial$page"
    }

    fun getCiteNumKey(page: Int): String{
        return "CITE SSN$page"
    }

    fun notesFieldByRow(rowNumber: Int): String {
        return "NOTESRow$rowNumber"
    }

    fun dateFieldByRow(rowNumber: Int): String {
        return "DateRow$rowNumber"
    }

    fun timeFieldByRow(rowNumber: Int): String {
        return "TimeRow$rowNumber"
    }

    fun assertNameOnPage(name: String, page: Int) {
        assertField("NAME Last First Middle Initial$page", name)
    }
    fun assertCiteNumberOnPage(number: String, page: Int) = assertField("CITE SSN$page", number)
    fun assertDodId(dodId: String) = assertField(dodIdField,"DoD ID: $dodId")
    fun assertStatus(status: String) = assertField(statusField, status)
    fun assertService(service: String) = assertField(serviceField, service)

    fun assertNotesRowText(text: String,rowNumber: Int){
        assertField(notesFieldByRow(rowNumber), text)
    }

    fun assertDateRowText(text: String, rowNumber: Int){
        assertField(dateFieldByRow(rowNumber), text)
    }

    fun assertTimeRowText(text: String, rowNumber: Int){
        assertField(timeFieldByRow(rowNumber), text)
    }

    fun assertControlledRDrugAccountability(rownum: Int, drugAccountability: String) =
        assertField("Controlled Drug Accountability MANDATORYRow$rownum", drugAccountability)
}