package mil.afrl.batdok.pdfrobots

import com.tom_roush.pdfbox.pdmodel.PDDocument

fun af3899H(document: PDDocument, func: AF3899HNeuroAssessmentPdfRobot.() -> Unit) = AF3899HNeuroAssessmentPdfRobot(document).apply(func)

class AF3899HNeuroAssessmentPdfRobot(document: PDDocument): PdfRobot(document){
    private fun assertField(field: String, value: String) = assertFields{
        // test the value set to the field
        field matches value
    }

    val neuroAssessmentName = "Name"

    fun assertName(name: String) = assertField(neuroAssessmentName, name)

    val neuroAssessmentSSN = "SSN"

    fun assertCiteSSN(ssn: String) = assertField(neuroAssessmentSSN, ssn)

    val neuroAssessmentDate = "DATE"

    fun assertDate(date: String) = assertField(neuroAssessmentDate, date)
}