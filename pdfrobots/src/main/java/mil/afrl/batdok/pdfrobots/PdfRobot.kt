package mil.afrl.batdok.pdfrobots

import android.graphics.Bitmap
import android.graphics.Point
import android.graphics.RectF
import android.os.Environment
import android.util.Log
import com.tom_roush.pdfbox.pdmodel.PDDocument
import com.tom_roush.pdfbox.rendering.PDFRenderer
import com.tom_roush.pdfbox.text.PDFTextStripper
import com.tom_roush.pdfbox.text.PDFTextStripperByArea
import org.junit.Assert
import java.io.File
import java.io.FileOutputStream

abstract class PdfRobot(val document: PDDocument){
    data class Field(val name: String, val location: RectF? = null, val pageNumber: Int = 0)
    open val customFields: List<Field> = listOf()
    val fields: List<Field> by lazy {
        (document.documentCatalog?.acroForm?.fields?.map {
            val page = it.widgets[0].page
            Field(
                    it.fullyQualifiedName,
                    it.widgets[0].rectangle?.run { RectF(lowerLeftX, page.mediaBox.height - upperRightY, upperRightX, page.mediaBox.height - lowerLeftY) },
                    document.pages.indexOf(it.widgets[0].page)
            )
        } ?: listOf()) + customFields
    }

    fun assertPageCount(count: Int){
        Assert.assertEquals(count, document.pages.count)
    }

    fun field(name: String) = fields.find { it.name == name }

    var flattened: Boolean = false

    fun printFields(){
        fields.forEach { field ->
            Log.e("Field", field.toString() + " - " + (document.documentCatalog?.acroForm?.fields?.find { it.fullyQualifiedName == field.name }?.valueAsString ?: "Not an Acroform field"))
        }
    }

    fun savePageAsBitmap(pageNumber: Int, filename: String){
        flatten()
        val renderer = PDFRenderer(document)
        val bitmap = renderer.renderImage(pageNumber)

        FileOutputStream(File(Environment.getExternalStorageDirectory(), filename)).use{
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, it) // save bit map to file
        }
        bitmap.recycle()
    }

    // Used to test field values that are generated dynamically by the builders
    fun assertFieldContainsText(fieldName: String ,text: String) {

        val foundField = document.documentCatalog?.acroForm?.fields?.find {
            it.fullyQualifiedName == fieldName
        }
        if (foundField != null) {
            Assert.assertTrue(foundField.valueAsString.contains(text))
        }else{
            Assert.fail("Unable to find field: $fieldName")
        }

    }

    interface AssertDsl{
        infix fun String.matches(fieldValue: String?)
        infix fun String.matches(regex: Regex)
        infix fun String.tryMatch(fieldValue: String?): Throwable? {
            return try{
                matches(fieldValue)
                null
            }catch (ex: Throwable){
                ex
            }
        }
    }
    inner class AssertFieldDsl: AssertDsl {
        override infix fun String.matches(fieldValue: String?){
            assert(!flattened){ "Document must not be flattened yet" }
            val fieldName = field(this)?.name
            val foundField = document.documentCatalog?.acroForm?.fields?.find { it.fullyQualifiedName == fieldName }
            Assert.assertNotNull("Field with name $this doesn't exist", foundField)
            Assert.assertEquals(
                    fieldName,
                    fieldValue,
                    foundField?.valueAsString
            )
        }

        override infix fun String.matches(regex: Regex){
            assert(!flattened){ "Document must not be flattened yet" }
            val fieldName = field(this)?.name
            val foundField = document.documentCatalog?.acroForm?.fields?.find {
                it.fullyQualifiedName == fieldName
            }
            Assert.assertNotNull("$fieldName doesn't exist", foundField)
            Assert.assertTrue(
                    "$fieldName expected to match Regex \"${regex.pattern}\", but was \"${foundField?.valueAsString}\"",
                    regex.matches(foundField?.valueAsString?:"")
            )
        }
    }
    inner class AssertOcrDsl: AssertDsl {
        private val valueMatches = mutableListOf<Pair<Field, String?>>()
        private val regexMatches = mutableListOf<Pair<Field, Regex>>()

        override infix fun String.matches(fieldValue: String?){
            field(this)?.let { valueMatches.add(it to fieldValue) }
        }

        override infix fun String.matches(regex: Regex){
            field(this)?.let { regexMatches.add(it to regex) }
        }

        fun finalize(){
            flatten()

            fun buildStripper(page: Int, tests: List<Pair<Field, *>>): PDFTextStripperByArea {
                return PDFTextStripperByArea().apply {
                    tests.map { it.first }.filter { it.location != null }.forEach {
                        addRegion(it.name, it.location)
                    }
                    extractRegions(document.getPage(page))
                }
            }

            fun <T> List<Pair<Field, T>>.assertByPage(assert: PDFTextStripperByArea.(Pair<Field, T>) -> Unit){
                groupBy { it.first.pageNumber }.forEach{ (page, tests) ->
                    val stripper = buildStripper(page, tests)
                    tests.forEach{ stripper.assert(it) }
                }
            }

            valueMatches.assertByPage { assertRegion(it.first.name, it.second?: "") }
            regexMatches.assertByPage { assertRegion(it.first.name, it.second) }
        }
    }

    inner class AssertPixelDsl(val pageBitmap: Bitmap){
        infix fun Point.matches(color: Int){
            Assert.assertEquals(color, pageBitmap.getPixel(x, y))
        }
    }

    fun assertFields(func: AssertDsl.() -> Unit): AssertFieldDsl = AssertFieldDsl().apply(func)
    fun assertOcr(func: AssertDsl.() -> Unit): AssertOcrDsl = AssertOcrDsl().apply{
        func()
        finalize()
    }
    fun assertPixels(pageNumber: Int, func: AssertPixelDsl.() -> Unit) {
        flatten()
        val renderer = PDFRenderer(document)
        val bitmap = renderer.renderImage(pageNumber)
        AssertPixelDsl(bitmap).apply(func)
        bitmap.recycle()
    }

    private fun flatten() {
        if(!flattened) document.documentCatalog?.acroForm?.flatten()
        flattened = true
    }

    private fun PDFTextStripperByArea.assertRegion(regionName: String, expectedText: String){
        Assert.assertEquals(regionName, expectedText, getTextForRegion(regionName).trim())
    }

    private fun PDFTextStripperByArea.assertRegion(regionName: String, regex: Regex){
        val value = getTextForRegion(regionName).trim()
        Assert.assertTrue(
                "$regionName expected to match Regex \"${regex.pattern}\", but was \"${value}\"",
                regex.matches(value)
        )
    }

    fun assertAppendixLines(expectedList: List<String>){
        val reader = PDFTextStripper()
        reader.startPage = document.numberOfPages
        reader.endPage = document.numberOfPages
        val appendixTextList = reader.getText(document).split("\n").filter { it.isNotBlank() }
        expectedList.forEachIndexed { index, item ->
            // Iterate through the lists for more descriptive errors
            Assert.assertEquals(item, appendixTextList[index])
        }
        Assert.assertEquals(expectedList.size, appendixTextList.size)
    }
}