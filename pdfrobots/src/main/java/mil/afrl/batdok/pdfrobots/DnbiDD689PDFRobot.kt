package mil.afrl.batdok.pdfrobots

import com.tom_roush.pdfbox.pdmodel.PDDocument

fun dnbiDD689(document: PDDocument, func: DnbiDD689PDFRobot.() -> Unit) = DnbiDD689PDFRobot(document).apply(func)

class DnbiDD689PDFRobot (document: PDDocument): PdfRobot(document) {

    private fun assertField(field: String, value: String) = assertFields{
        // test the value set to the field
        field matches value
    }

    fun assertMedicalDescription(description: String) = assertField("MEDICALDESCRIPTION",description)

    fun assertIllnessCheckBoxNotChecked()  {
        assertField("ILLNESS", "Off")
    }

    fun assertInjuryCheckBoxNotChecked()  {
        assertField("INJURY", "Off")
    }

    fun assertName(name: String) = assertField("NAME", name)

    fun assertDodNumber(dodNumber: String) = assertField("DoDIDNUMBER", dodNumber)

    fun assertGradeRank(gradeRank: String) = assertField("GRADERANK", gradeRank)

    fun assertOrganizationAndStation(organization: String) = assertField("ORGANIZATIONANDSTATION", organization)

    fun assertDispositionOfPatientChecked(disposition: DispositionOptions){
        assertField(disposition.value, "On")
    }

    fun assertDispositionOfPatientNotChecked(disposition: DispositionOptions){
        assertField(disposition.value, "")
    }

    fun assertRemarksText(remarks: String){
        assertField("OFFICERSREMARKS", remarks)
    }

}

enum class DispositionOptions(val value: String) {
    DUTY("DUTY"),
    QUARTERS("QUARTERS"),
    SICK_BAY("SICK BAY"),
    HOSPITAL("HOSPITAL"),
    NOTE_EXAMINED("NOT EXAMINED"),
    OTHER("OTHER")
}