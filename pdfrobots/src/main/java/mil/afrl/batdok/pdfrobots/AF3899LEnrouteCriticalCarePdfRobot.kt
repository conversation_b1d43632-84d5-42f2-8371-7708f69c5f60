package mil.afrl.batdok.pdfrobots

import com.tom_roush.pdfbox.pdmodel.PDDocument

fun af3899L(document: PDDocument, func: AF3899LEnrouteCriticalCarePdfRobot.() -> Unit) = AF3899LEnrouteCriticalCarePdfRobot(document).apply(func)

class AF3899LEnrouteCriticalCarePdfRobot(document: PDDocument): PdfRobot(document){
    private fun assertField(field: String, value: String) = assertFields{
        // test the value set to the field
        field matches value
    }

    fun assertNamePage1(name: String) = assertField("1Name", name)
    fun assertNamePage2(name: String) = assertField("1NamePage2", name)

    //Ticket: 22820 DoDID should be inplace of SSN
    fun assertSSNPage1(dodId: String) = assertField("2SSNPage1", dodId)
    fun assertSSNPage2(dodId: String) = assertField("2SSNPage2", dodId)

    fun assertCitNumberPage1(citeNum: String)= assertField("3Cite Number", citeNum)
    fun assertCitNumberPage2(citeNum: String) = assertField("3Cite NumberPage2", citeNum)
    fun assertAge(age: String) = assertField("4Age", age)
    fun assertSex(sex: String) = assertField("5Sex", sex)
    fun assertWeight(weight: String) = assertField("6Weight", weight)
    fun assertAllergies(allergies: String) = assertField("8Allergies", allergies)
    fun assertPrecedence(precedence: String) = assertField("9Precedence", precedence)
    fun assertOriginatingFacility(originatingFacility: String) = assertField("11Originating Facility", originatingFacility)
    fun assertDestinationFacility(destinationFacility: String) = assertField("12Destination Facility", destinationFacility)
    fun assertTailNum(tailNum: String) = assertField("13AC/Tail #", tailNum)
    fun assertUnit(unit: String) = assertField("14AE/CCATT Unit", unit)
    fun assertHeight(height: String) = assertField("7Height", height)
    fun assertHoursEnRoute(hoursEnRoute: String) = assertField("16Hours En Route", hoursEnRoute)

}