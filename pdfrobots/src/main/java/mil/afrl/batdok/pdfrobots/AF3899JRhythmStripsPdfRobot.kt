package mil.afrl.batdok.pdfrobots

import com.tom_roush.pdfbox.pdmodel.PDDocument

fun af3899J(document: PDDocument, func: AF3899JRhythmStripsPdfRobot.() -> Unit) = AF3899JRhythmStripsPdfRobot(document).apply(func)

class AF3899JRhythmStripsPdfRobot(document: PDDocument): PdfRobot(document){
    private fun assertField(field: String, value: String) = assertFields{
        // test the value set to the field
        field matches value
    }

    val patientName = "PatientName"
    val patientName2 = "PatientName2"

    val citeNumberSSN = "SSN"
    val citeNumberSSN2 = "SSN2"

    fun assertName(name: String) = assertField(patientName, name)
    fun assertName2(name: String) = assertField(patientName2, name)

    fun assertCiteNumberSSN(citeNumber: String) = assertField(citeNumberSSN, citeNumber)
    fun assertCiteNumberSSN2(citeNumber: String) = assertField(citeNumberSSN2, citeNumber)

}