package mil.afrl.batdok.pdfrobots

import android.graphics.RectF
import android.util.Log
import com.tom_roush.pdfbox.pdmodel.PDDocument

fun dnbiSf600(document: PDDocument, func: DnbiSF600PdfRobot.() -> Unit) = DnbiSF600PdfRobot(document).apply(func)
class DnbiSF600PdfRobot(document: PDDocument): PdfRobot(document) {
    override val customFields: List<Field> = listOf(
        Field(name="HOSPITAL OR MEDICAL FACILITY_0", location= RectF(21.72f, 608f, 235.92f, 623.16003f), pageNumber=0),
        Field(name="STATUS_0", location=RectF(237.72f, 608f, 351.12f, 623.16003f), pageNumber=0),
        <PERSON>(name="DEPARTMENTSERVICE_0", location=RectF(352.92f, 608f, 480.6f, 623.16003f), pageNumber=0),
        Field(name="RECORDS MAINTAINED AT_0", location=RectF(482.4f, 608f, 591.36f, 623.16003f), pageNumber=0),
        Field(name="SPONSORS NAME_0", location=RectF(21.72f, 632f, 235.92f, 645.6f), pageNumber=0),
        Field(name="SOCIAL SECURITYID  NUMBER_0", location=RectF(237.72f, 632f, 351.12f, 645.6f), pageNumber=0),
        Field(name="RELATIONSHIP TO SPONSOR_0", location=RectF(352.92f, 632f, 591.0f, 645.6f), pageNumber=0),
        Field(name="REGISTER NUMBER_0", location=RectF(401.64f, 655f, 524.64f, 673.2f), pageNumber=0),
        Field(name="WARD NUMBER_0", location=RectF(526.68f, 655f, 591.24f, 673.2f), pageNumber=0),
        Field(name="Vitals_0", location=RectF(23.16f, 192.96002f, 120.475f, 378.0f), pageNumber=0),
        Field(name="Medications_0", location=RectF(23.1624f, 378.0f, 120.478f, 486.0f), pageNumber=0),
        Field(name="Allergies_0", location=RectF(23.1624f, 486.0f, 120.478f, 597.273f), pageNumber=0),
        Field(name="SOAP_1_0", location=RectF(122.4f, 176.70502f, 591.48f, 552.763f), pageNumber=0),
        Field(name="PatientID_0", location=RectF(21.6f, 664f, 372.6f, 738.0f), pageNumber=0),
        Field(name="Date_0", location=RectF(22.08f, 176.40002f, 120.84f, 192.23999f), pageNumber=0),
        Field(name="SignedBy_0", location=RectF(123.055f, 552.442f, 591.82f, 597.348f), pageNumber=0)
    )

    private fun assertOcrField(field: String, value: String) = assertOcr {
        field matches value
    }

    private fun assertField(field: String, value: String) = assertFields{
        field matches value
    }

    private fun matchOnAllPages(baseFieldName: String, expectedValue: String){
        repeat(document.documentCatalog.acroForm.fields.maxOf { it.partialName.last().digitToInt() } + 1){
            assertField(baseFieldName+"_$it", expectedValue)
        }
    }

    fun assertDate(date: String) = matchOnAllPages("Date", date)
    fun assertVitals(value: String) = matchOnAllPages("Vitals", value)
    fun assertService(service: String) = matchOnAllPages("DEPARTMENTSERVICE", service)
    fun assertStatus(status: String) = matchOnAllPages("STATUS", status)
    fun assertSSNOrId(ssnOrId: String) = matchOnAllPages("SOCIAL SECURITYID  NUMBER", ssnOrId)
    fun assertMedications(vararg meds: String, page: Int) = assertField("Medications_$page", "Medications:${if(meds.isNotEmpty()) "\n" else ""}" + meds.joinToString("\n"))
    fun assertAllergies(vararg allergies: String, page: Int) = assertField("Allergies_$page", "Allergies:${if(allergies.isNotEmpty()) "\n" else ""}" + allergies.joinToString("\n"))
    fun assertProviderInfo(providerInfo: String) = matchOnAllPages("SignedBy", providerInfo)
    fun assertPatientInfo(patientInfo: String) = matchOnAllPages("PatientID", patientInfo)

    fun assertSOAP(content: String, page: Int) {
        val fieldName = "SOAP_${if(page==0) 1 else 2}"
        assertField("${fieldName}_$page", content)
    }

    // Page 0 has different field sizes than the other pages, so this is all checking page 0
    fun assertDateOcr(date: String) = assertOcrField("Date_0", date)
    fun assertVitalsOcr(value: String) = assertOcrField("Vitals_0", value)
    fun assertServiceOcr(service: String) = assertOcrField("DEPARTMENTSERVICE_0", service)
    fun assertStatusOcr(status: String) = assertOcrField("STATUS_0", status)
    fun assertSSNOrIdOcr(ssnOrId: String) = assertOcrField("SOCIAL SECURITYID  NUMBER_0", ssnOrId)
    fun assertAllergiesOcr(vararg allergies: String) = assertOcrField("Allergies_0", "Allergies:${if(allergies.isNotEmpty()) "\n" else ""}" + allergies.joinToString("\n"))
    fun assertMedicationsOcr(vararg meds: String) = assertOcrField("Medications_0", "Medications:${if(meds.isNotEmpty()) "\n" else ""}" + meds.joinToString("\n"))
    fun assertProviderInfoOcr(providerInfo: String) = assertOcrField("SignedBy_0", providerInfo)
    fun assertPatientInfoOcr(patientInfo: String) = assertOcrField("PatientID_0", patientInfo)
    fun assertSOAPOcr(content: String) = assertOcrField("SOAP_1_0", content)
}