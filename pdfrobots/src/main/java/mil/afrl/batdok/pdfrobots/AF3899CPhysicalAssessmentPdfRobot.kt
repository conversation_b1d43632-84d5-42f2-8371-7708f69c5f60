package mil.afrl.batdok.pdfrobots

import com.tom_roush.pdfbox.pdmodel.PDDocument
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Assert.fail

fun af3899C(document: PDDocument, func: AF3899CPhysicalAssessmentPdfRobot.() -> Unit) = AF3899CPhysicalAssessmentPdfRobot(document).apply(func)

class AF3899CPhysicalAssessmentPdfRobot(document: PDDocument): PdfRobot(document){
    private fun assertField(field: String, value: String) = assertFields{
        // test the value set to the field
        field matches value
    }

    val namePage1 = "NamePage1"
    val citeSsnPage1 = "CITESSN"

    val namePage2 = "NAME  Last First Middle Initial_2"
    val citeSsnPage2 = "CITESSN_2"


    fun assertNamePage1(name: String) = assertField(namePage1, name)
    fun assertCiteSsnPage1(citeSsn: String) = assertField(citeSsnPage1, citeSsn)

    fun assertNamePage2(name: String) = assertField(namePage2, name)
    fun assertCiteSsnPage2(citeSsn: String) = assertField(citeSsnPage2, citeSsn)

    fun assertPrintProvidersNameSignatureInitialUnitLocation(fieldText: String) =
        assertField("Print Providers NameSignatureInitialsUnit  Location", fieldText)


}