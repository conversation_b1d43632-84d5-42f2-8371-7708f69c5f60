package mil.afrl.batdok.pdfrobots

import com.tom_roush.pdfbox.pdmodel.PDDocument

fun af3899K(document: PDDocument, func: AF3899KResuscitationFlowsheetPdfRobot.() -> Unit) = AF3899KResuscitationFlowsheetPdfRobot(document).apply(func)

class AF3899KResuscitationFlowsheetPdfRobot(document: PDDocument): PdfRobot(document){
    private fun assertField(field: String, value: String) = assertFields{
        // test the value set to the field
        field matches value
    }

    val resuscitationFlowsheetName = "PatientName"
    val resuscitationFlowsheetName2 = "PatientName2"

    val citeSsn = "SSN"
    val citeSsn2 = "SSN2"

    val date1 = "Date"
    val date2 = "Date2"

    val patientSex = "Sex"
    val patientAge = "Age"

    val missionNumber = "MissionNumberDestination"


    fun assertName(name: String) = assertField(resuscitationFlowsheetName, name)
    fun assertName2(name: String) = assertField(resuscitationFlowsheetName2, name)

    fun assertCiteSSN(ssn: String) = assertField(citeSsn, ssn)
    fun assertCiteSSN2(ssn: String) = assertField(citeSsn2, ssn)

    fun assertDate(date: String) = assertField(date1, date)
    fun assertDate2(date: String) = assertField(date2, date)

    fun assertPatientSex(sex: String) = assertField(patientSex, sex)
    fun assertPatientAge(age: String) = assertField(patientAge, age)
    fun assertMissionNumDest(missionNumDest: String) = assertField(missionNumber, missionNumDest)


}