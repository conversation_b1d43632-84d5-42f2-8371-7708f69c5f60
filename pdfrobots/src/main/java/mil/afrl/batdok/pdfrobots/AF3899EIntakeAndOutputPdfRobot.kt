package mil.afrl.batdok.pdfrobots

import com.tom_roush.pdfbox.pdmodel.PDDocument

fun af3899E(document: PDDocument, func: AF3899EIntakeAndOutputPdfRobot.() -> Unit) = AF3899EIntakeAndOutputPdfRobot(document).apply(func)

class AF3899EIntakeAndOutputPdfRobot(document: PDDocument): PdfRobot(document){
    private fun assertField(field: String, value: String) = assertFields{
        // test the value set to the field
        field matches value
    }


    fun assertName(name: String) = assertField("NameRow", name)
    fun assertCiteSSN(citeSSN: String) = assertField("SSNRow", citeSSN)
}