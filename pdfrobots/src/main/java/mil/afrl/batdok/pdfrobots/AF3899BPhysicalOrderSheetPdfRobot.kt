package mil.afrl.batdok.pdfrobots

import com.tom_roush.pdfbox.pdmodel.PDDocument

fun af3899B(document: PDDocument, func: AF3899BPhysicalOrderSheetPdfRobot.() -> Unit) = AF3899BPhysicalOrderSheetPdfRobot(document).apply(func)

class AF3899BPhysicalOrderSheetPdfRobot(document: PDDocument): PdfRobot(document){
    private fun assertField(field: String, value: String) = assertFields{
        // test the value set to the field
        field matches value
    }

    private val physicalOrderName = "NAME"
    private val citeSsn = "CITESSN"
    private val allergies = "ALLERGIES"
    private val destinationFacility = "DESTINATION FACILITY"
    private val originationFacility = "ORIGINATING FACILITY"
    private val lastMenstrualPeriod = "LAST MENTRUAL PERIOD"

    fun assertName(name: String) = assertField(physicalOrderName, name)

    fun assertCiteSsn(cite: String) = assertField(citeSsn, cite)

    fun assertAllergies(allergy: String) = assertField(allergies, allergy)

    fun assertDestinationFacility(facility: String) = assertField(destinationFacility, facility)

    fun assertOrginationFacility(facility: String) = assertField(originationFacility, facility)

    fun assertLastMenstrualTime(time: String) = assertField(lastMenstrualPeriod, time)
}