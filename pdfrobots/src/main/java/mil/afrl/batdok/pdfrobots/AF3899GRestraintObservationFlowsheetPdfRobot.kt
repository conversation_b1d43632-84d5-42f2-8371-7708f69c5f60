package mil.afrl.batdok.pdfrobots

import com.tom_roush.pdfbox.pdmodel.PDDocument

fun af3899G(document: PDDocument, func: AF3899GRestraintObservationFlowsheetPdfRobot.() -> Unit) = AF3899GRestraintObservationFlowsheetPdfRobot(document).apply(func)

class AF3899GRestraintObservationFlowsheetPdfRobot(document: PDDocument): PdfRobot(document){
    private fun assertField(field: String, value: String) = assertFields{
        // test the value set to the field
        field matches value
    }

    fun assertName(name: String) = assertField("PatientName", name)

    fun assertCiteSSN(citeSSN: String) = assertField("SSN", citeSSN )
}