package mil.afrl.batdok.pdfrobots

import com.tom_roush.pdfbox.pdmodel.PDDocument

fun af3899PMR(document: PDDocument, func: AF3899PMRPdfRobot.() -> Unit) = AF3899PMRPdfRobot(document).apply(func)
@Deprecated("Use af3899PMR instead", ReplaceWith("af3899PMR"))
fun ae3899PMR(document: PDDocument, func: AE3899PMRPdfRobot.() -> Unit) = AE3899PMRPdfRobot(document).apply(func)

@Deprecated("Use AF3899AProgressNotePdfRobot instead", ReplaceWith("AF3899AProgressNotePdfRobot"))
typealias AE3899PMRPdfRobot = AF3899PMRPdfRobot
class AF3899PMRPdfRobot(document: PDDocument): PdfRobot(document) {

    private fun assertField(field: String, value: String) = assertFields{
        // test the value set to the field
        field matches value
    }

    val nameField = "name"
    val ssnField = "ssn"
    val dobField = "DATE OF BIRTH"
    val ageField = "Age"
    val sexField = "sex"
    val serviceField = "Service"
    val statusField = "status"
    val gradeField = "Grade"
    val unitOfRecordField = "unit"
    val unitOfRecordPhoneNumberField = "unit phone"
    val citeNumberField = "CITE NUMBER"

    // region Section I
    fun assertName(name: String) = assertField(nameField, name)
    fun assertSsn(ssnOrId: String) = assertField(ssnField, ssnOrId)
    fun assertDob(dob: String) = assertField(dobField, dob)
    fun assertAge(age: String) = assertField(ageField, age)
    fun assertSex(sex: String) = assertField(sexField, sex)
    fun assertService(service: String) = assertField(serviceField, "[$service]") // extra square brackets for dropdown
    fun assertStatus(status: String) = assertField(statusField, "[$status]") // extra square brackets for dropdown
    fun assertGrade(grade: String) = assertField(gradeField, "[$grade]")  // extra square brackets for dropdown
    fun assertUnitOfRecord(unitOfRecord: String) = assertField(unitOfRecordField, unitOfRecord)
    fun assertUnitOfRecordPhoneNumber(unitOfRecordPhoneNumber: String) = assertField(unitOfRecordPhoneNumberField, unitOfRecordPhoneNumber)
    fun assertCiteNumber(citeNumber: String) = assertField(citeNumberField, citeNumber)
    // endregion
}