pluginManagement {
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
        maven {
            // for the job token to work the project package repository needs to enable other projects
            // to read the package with the job token.
            // For group-level package repositories, you'll need either:
            // A deploy token with group-level access specified with gitlabUsername/gitlabPassword
            // Or enable the "CI_JOB_TOKEN_SCOPE" feature to allow cross-project/group access (admin would need to enable)
            // If you want to use CI_JOB_TOKEN, you would need to:
            // Go to Settings > CI/CD > Token Access of the project you are pulling from
            // Enable the "Allow CI job tokens to access all packages within this group" option
            // or specify the group/projects you want to allow the token to access
            url = uri(providers.environmentVariable("CI_API_V4_URL")
                          .orElse("https://gitlab-ultimate.dle.afrl.af.mil/api/v4").get()+"/groups/145/-/packages/maven")
            name = "Git<PERSON>ab"
            credentials {
                username = providers.gradleProperty("gitlabUsername")
                    .orElse(providers.environmentVariable("CI_REGISTRY_USER"))
                    .get()
                password = providers.gradleProperty("gitlabPassword")
                    .orElse(providers.environmentVariable("CI_JOB_TOKEN"))
                    .get()
                println("Logging into $name with $username and $password")
            }
        }
        mavenLocal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    pluginManagement.repositories.findByName("GitLab")?.let { repositories.add(it) }
    repositories {
        google()
        mavenCentral()
        mavenLocal()
    }
}

rootProject.name = "BatdokBoms"
include (":internal")
include (":external")
include (":batdokdata")
include (":batdokstorageroom")
include (":batdokstoragetest")
include (":exportlibrary")
include (":pdfrobots")
include (":batdokdocument")
include (":batdokprotobuf")
include (":hl7library")
