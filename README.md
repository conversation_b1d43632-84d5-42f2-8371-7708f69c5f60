# BATDOK BOM

## What's a BOM?

A Bill Of Materials (BOM) is a collection of libraries within a single project. 
It shows that these versions of libraries work together, and allows higher projects to reference just one version
and give you all of the compatible libraries by just referencing their names

This project has two BOMs: Internal and External:

- Internal
  - Made up of the modules within this project.
  - It's all of the Data related libraries for BATDOK
- External
  - Made up of all of the libraries our internal module uses

## Versioning the BOM

How does our versioning work?

1. In [build.gradle.kts], each of our libraries has a defined version
2. When you make a change to a library, update the library's version to an alpha version
    - Examples:
      - Patch Update: X.Y.Z -> X.Y.Z+1-alpha1
      - Minor Update: X.Y.Z -> X.Y+1.0-alpha1
      - Major Update: X.Y.Z -> X+1.0.0-alpha1
      - Patch update after a minor update: X.Y.0-alpha1 -> X.Y.0-alpha2
      - Minor update after a patch update: X.Y.Z-alpha1 -> X.Y+1.0-alpha1
3. You may also need to update other libraries that depend on the library that you changed.
   They should update the same section as your base library.
4. Make your BOM MR (But don't merge it yet). This publishes your version update.
5. Update BATDOK with the new alpha version.
   - To update the version, just add (or overwrite) the version in the toml file:
   - `batdokdata = { module="mil.af.afrl:batdokdata" }` -> `batdokdata = { module = "mil.af.afrl:batdokdata", version = "X.Y.Z-alpha1" }`
   - `batdokdata = { module="mil.af.afrl:batdokdata", version = "X.Y.Z.alpha1 }` -> `batdokdata = { module = "mil.af.afrl:batdokdata", version = "X.Y.Z-alpha2" }`
6. Make the changes to BATDOK to complete your task
7. Make your BATDOK MR and have it be reviewed and merged 
8. Once it's merged, you can also merge your BOM MR
9. After every sprint, when we finalize the alpha build of BATDOK, we'll update the BOM version to match BATDOK's version and remove the alpha versions

## Library Descriptions

- [Batdok Document](batdokdocument) - A library holding all of the data objects and protobuf commands for our Document
- [Batdok Protobuf](batdokprotobuf) - A library holding all of the protobuf commands for our networking/Contactless transfer
- [Batdok Data](batdokdata) - A library holding the data types that we want to store in Long Term Storage (LTS) as well as the Datastore interfaces to interact with that LTS
- [Batdok Storage-Room](batdokstorageroom) - A library implementing the Datastores as a Room Database
- [Export](exportlibrary) - A library holding various export capabilities such as PDFs and File IO
  - [PDF Robots](pdfrobots) - A library holding the Robots for parsing/verifying the PDFs from the Export Library
- [External BOM](external) - A library holding the gradle file defining the BOM of the external libraries we use in this project
- [Internal BOM](internal) - A library holding the gradle file defining the BOM of the internal libraries listed above