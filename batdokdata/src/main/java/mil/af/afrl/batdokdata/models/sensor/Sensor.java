package mil.af.afrl.batdokdata.models.sensor;

import kotlin.Deprecated;
import mil.af.afrl.batdokdata.id.EncounterId;
import mil.af.afrl.batdokdata.id.SensorId;
import mil.af.afrl.batdokdata.models.patient.EncounterModel;

/**
 * Object for connection information on a given sensor
 */
@Deprecated(message = "Use SensorInfo instead")
public class Sensor {
    public boolean isConnected;
    /* Indicates if sensor was found by device */
    public boolean wasFound = false;
    private String alias;

   public static final String STATE_LOCAL = "local";
   public static final String STATE_NETWORK = "network";
   public static final String STATE_DISCONNECTED = "disconnected";

    private SensorId id;
    private String name;
    private String address;
    private boolean autoConnect;
    private boolean isFloating;
    private String sensorType;
    private String state;
    private EncounterId encounterId;
    private String pluginName;
    protected int rank;

    public SensorId getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getAddress() {
        return address;
    }

    public String getSensorType() {
        return sensorType;
    }

    public String getState() {
        return state;
    }

    public boolean getAutoConnect() {
        return autoConnect;
    }

    public EncounterId getEncounterId() {
        return encounterId;
    }

    public boolean getIsFloating() {
        return isFloating;
    }

    public String getAlias() {
        return alias;
    }

    public void setAutoConnect(boolean autoConnect) {
        this.autoConnect = autoConnect;
    }

    public void setState(String state) {
        this.state = state;
    }

    public void setSensorType(String type){
        this.sensorType = type;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public void setIsFloating(boolean isFloating) {
        this.isFloating = isFloating;
    }

    public int getRank() {
        return rank;
    }

    public String getPluginName() {
        return pluginName;
    }

    /**
     * Constructor for a Sensor
     * @param id The sensor's ID
     * @param name The name of the sensor
     * @param address The MAC address of the sensor
     * @param sensorType The type of sensor
     * @param state The current state of the sensor
     * @param autoConnect If the sensor auto connects or not
     * @param isFloating If the sensor is a floating sensor
     * @param encounterId The associated encounter's ID
     * @param rank The rank of this sensor
     */
    public Sensor(SensorId id, String name, String address, String sensorType, String state, boolean autoConnect,
                  boolean isFloating, EncounterId encounterId, int rank, String pluginName) {
        this.id = id;
        this.name = name;
        this.address = address;
        this.sensorType = sensorType;
        this.state = state;
        this.autoConnect = autoConnect;
        this.encounterId = encounterId;
        this.isFloating = isFloating;
        this.rank = rank;
        this.pluginName = pluginName;
    }


    @Override
    public boolean equals(Object o) {
        if(o == null){
            return false;
        }
        return o.getClass() == this.getClass() && this.address.equals(((Sensor) o).getAddress());
    }

    @Override
    public int hashCode() {
        return id.hashCode();
    }


    /**
     * Attaches this sensor to the given encounter
     * @param encounter The encounter that this sensor is to be connected to
     * @param rank The rank position that the sensor was added.
     */
    public void attachToEncounter(EncounterModel encounter, int rank) {
        this.encounterId = encounter.getId();
        this.rank = rank;
    }

    /**
     * Detaches the sensor from the encounter that it is currently attached to
     */
    public void detachFromEncounter() {
        this.encounterId = null;
    }
}
