package mil.af.afrl.batdokdata.models.burnsheet

import mil.af.afrl.batman.batdokid.DomainId
import java.io.Serializable

/**
 * This class contains the information to fill out the flow rows on the Burn Sheet
 */
class MedCardFlowRowDataModel(
    @JvmField var id: DomainId,
    @JvmField var ownerId: DomainId?,
    @JvmField var treatmentSiteOrTeam: String?,
    @JvmField var localTime: String?,
    @JvmField var baseDeficitLactat: String?,
    @JvmField var heartRate: String?,
    @JvmField var pressorsAndBladderPressure: String?,
    crystalloid: String?,
    colloid: String?,
    @JvmField var ccTotal: String?,
    @JvmField var map: String?,
    @JvmField var cvp: String?,
    @JvmField var uop: String?
) : Serializable {

    var crystalloid: String? = crystalloid
        set(value){
            field = value
            updateCCTotal()
        }

    var colloid: String? = colloid
        set(value){
            field = value
            updateCCTotal()
        }

    /**
     * Updates CCTotal field to sum of crystaloid and colloid.
     */
    private fun updateCCTotal() {
        val crysInt = crystalloid?.toIntOrNull() ?: 0
        val collInt = colloid?.toIntOrNull() ?: 0
        val total = crysInt + collInt
        val ccTotalText = if (total > 0) total.toString() else ""
        ccTotal = ccTotalText
    }
}