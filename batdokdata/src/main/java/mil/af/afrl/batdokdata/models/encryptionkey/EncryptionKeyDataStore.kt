package mil.af.afrl.batdokdata.models.encryptionkey

/**
 * This class is an interface for datastores that access encryption keys
 */
interface EncryptionKeyDataStore {
    // Internal BATDOK encryption keys
    suspend fun getKey(keyType: String): String?
    suspend fun getKey(keyType: EncryptionKey.KeyType): String?
    suspend fun setKey(keyType: String, key: String)
    suspend fun setKey(keyType: EncryptionKey.KeyType, key: String)
    suspend fun upsertKey(key: EncryptionKey)
}
