package mil.af.afrl.batdokdata.models.sensor

import mil.af.afrl.batdokdata.id.EncounterId
import mil.af.afrl.batdokdata.id.SensorId
import mil.af.afrl.batman.batdokid.DomainId

/**
 * Metadata about sensors
 */
data class SensorInfo(
    val id: SensorId = DomainId.create(),
    val address: String = "",
    val name: String = "Unknown Sensor",
    val alias: String? = null,
    val autoConnect: Boolean = false,
    val isFloating: Boolean = true,
    val encounterId: EncounterId? = null,
    val rank: Int = 0,
    val extras: Map<String, String> = mapOf(),
    val color: String? = null
)