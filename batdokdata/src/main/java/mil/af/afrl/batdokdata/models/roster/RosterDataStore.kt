package mil.af.afrl.batdokdata.models.roster

import kotlinx.coroutines.flow.Flow

interface RosterDataStore {
    suspend fun getRosters(): List<RosterEntry>
    fun getLiveRosters(): Flow<List<RosterEntry>>
    suspend fun getRostersWithBloodTypes(): List<RosterWithBloodTypes>
    fun getLiveRostersWithBloodTypes(): Flow<List<RosterWithBloodTypes>>
    suspend fun addRosters(rosters: List<RosterEntry>)
    suspend fun addRostersTakeChanges(rosters: List<RosterEntry>)
    suspend fun addRostersIgnoreChanges(rosters: List<RosterEntry>)
    suspend fun overwriteRosters(newRosters: List<RosterEntry>)
    suspend fun removeAllRosters()
    suspend fun removeRoster(roster: RosterEntry)
}