package mil.af.afrl.batdokdata.models.patient

import com.batman.batdok.infrastructure.network.EndpointId
import mil.af.afrl.batdokdata.id.EncounterId
import mil.af.afrl.batdokdata.id.PatientId
import mil.af.afrl.batdokdata.models.vital.EncounterVitals
import mil.af.afrl.batman.batdokid.DomainId
import java.io.Serializable
import java.time.Instant
import java.util.concurrent.CopyOnWriteArraySet
import kotlin.reflect.KMutableProperty

/**
 * This is a Holder for all of the information a Patient needs
 *
 * Created on 9/21/17.
 */

const val UNKNOWN_MODE = "Unknown Mode"
const val MY_PATIENT = 1
const val NETWORK_PATIENT = 2
@Deprecated("Use Patient State instead")
const val ARCHIVED_PATIENT = 3
@Deprecated("Use Patient State instead")
const val CLOSED_PATIENT = 3
@Deprecated("Use Patient State instead")
const val HANDED_OFF = 4

enum class EncounterState{
    ACTIVE, INACTIVE, CLOSED
}

class EncounterModel @JvmOverloads constructor(
    id: EncounterId,
    name: PatientName = PatientName("Patient", "Patient"),
    type: Int = MY_PATIENT,
    owner: EndpointId = DomainId.nil(),
    triage: Triage = Triage(),
    vitals: EncounterVitals = EncounterVitals(),
    vitalThresholds: VitalThresholds = VitalThresholds(),
    vitalThresholdsAlert: VitalThresholdsAlert = VitalThresholdsAlert(),
    hasNewDocumentation: Boolean = false,
    geoTag: GeoTag = GeoTag(),
    isTrainingMode: Boolean = false,
    platformStatus: String? = null,
    mode: PatientMode = PatientMode(),
    autoLog: AutoLog = AutoLog(),
    active: Boolean = false,
    commandChecksum: Byte = 0,
    created: Instant = Instant.now(),
    exportStatus: ExportStatus = ExportStatus(),
    maskingJustification: String? = null,
    patientId: PatientId = DomainId.create(),
    externalIds: Map<String, String> = mapOf(),  // External sys name, ID value
    handedOffTime: Instant? = null,
    encounterState: EncounterState = EncounterState.ACTIVE,
    encounterRank: Int = 0
): Comparable<EncounterModel>, Serializable {
    @Deprecated("This will no longer be used to figure out what to update")
    val changedFields = CopyOnWriteArraySet<KMutableProperty<*>>()

    @Deprecated("Handled in aggregated object")
    var id by TrackFieldChangeDelegate(this,id,changedFields); private set

    var name by TrackFieldChangeDelegate(this,name,changedFields)
        @Deprecated("Patient is going to be immutable", ReplaceWith("copy")) set

    @Deprecated("Exists in Document")
    var triage by TrackFieldChangeDelegate(this,triage,changedFields)

    @Deprecated("Handled in aggregated object")
    var vitals = vitals // do not track

    @Deprecated("Handled in aggregated object")
    var trendsVitals = vitals // do not track

    var exportStatus by TrackFieldChangeDelegate(this,exportStatus,changedFields)
        @Deprecated("Patient is going to be immutable", ReplaceWith("copy")) set

    @Deprecated("Handled in aggregated object")
    var vitalThresholds by TrackFieldChangeDelegate(this,vitalThresholds,changedFields)
    {
        val changed = this.value != it
        this.value = it
        // do not validate thresholds here
        // we might be dealing with data coming from the database
        // which does not have up to date vitals readings
        // validate as needed in calling code
        return@TrackFieldChangeDelegate changed
    }

    var hasNewDocumentation by TrackFieldChangeDelegate(this,hasNewDocumentation,changedFields)
        @Deprecated("Patient is going to be immutable", ReplaceWith("copy")) set

    var geoTag by TrackFieldChangeDelegate(this,geoTag,changedFields)
        @Deprecated("Patient is going to be immutable", ReplaceWith("copy")) set

    @Deprecated("This doesn't need to be set, just read")
    var vitalThresholdsAlert = vitalThresholdsAlert; private set  //do not track

    var isTrainingMode by TrackFieldChangeDelegate(this,isTrainingMode,changedFields); private set

    @Deprecated("Exists in Document")
    var maskingJustification by TrackFieldChangeDelegate(this, maskingJustification, changedFields)

    var patientId by TrackFieldChangeDelegate(this, patientId, changedFields)
        @Deprecated("Patient is going to be immutable", ReplaceWith("copy")) set

    var type by TrackFieldChangeDelegate(this,type,changedFields)
        @Deprecated("Patient is going to be immutable", ReplaceWith("copy")) set

    var owner by TrackFieldChangeDelegate(this,owner,changedFields)
        @Deprecated("Patient is going to be immutable", ReplaceWith("copy")) set

    @Deprecated("Handled in aggregated object")
    var status by TrackFieldChangeDelegate(this, platformStatus, changedFields)

    var mode by TrackFieldChangeDelegate(this,mode,changedFields)
        @Deprecated("Patient is going to be immutable", ReplaceWith("copy")) set

    var autoLog by TrackFieldChangeDelegate(this,autoLog,changedFields)//FIXME RAS was internal set
        @Deprecated("Patient is going to be immutable", ReplaceWith("copy")) set

    @Deprecated("Active is no longer a thing. It's handled by encounterState")
    var active by TrackFieldChangeDelegate(this,active,changedFields)

    var commandChecksum by TrackFieldChangeDelegate(this,commandChecksum,changedFields)
        @Deprecated("Patient is going to be immutable", ReplaceWith("copy")) set

    var created by TrackFieldChangeDelegate(this,created,changedFields)
    var externalIds by TrackFieldChangeDelegate(this, externalIds, changedFields)
        @Deprecated("Patient is going to be immutable", ReplaceWith("copy")) set

    var handedOffTime by TrackFieldChangeDelegate(this,handedOffTime,changedFields)
        @Deprecated("Patient is going to be immutable", ReplaceWith("copy")) set

    var encounterState by TrackFieldChangeDelegate(this, encounterState, changedFields)
        @Deprecated("Patient is going to be immutable", ReplaceWith("copy")) set

    val encounterRank by TrackFieldChangeDelegate(this, encounterRank, changedFields)

    val TAG: String = EncounterModel::class.java.simpleName

    /**
     * Sets patient to archived patient type, stops logging trends, and stops logging document vitals
     *
     */
    @Deprecated("Patient is becoming immutable.", ReplaceWith("copy"))
    fun archive() {
        type = ARCHIVED_PATIENT
        encounterState = EncounterState.INACTIVE
        vitals.status = VitalStatus.NONE
        vitalThresholdsAlert = VitalThresholdsAlert()
        vitals = EncounterVitals()
    }
    @Deprecated("Patient is becoming immutable.", ReplaceWith("copy"))
    fun deactivate(){
        type = ARCHIVED_PATIENT
        encounterState = EncounterState.INACTIVE
        vitals.status = VitalStatus.NONE
        vitalThresholdsAlert = VitalThresholdsAlert()
        vitals = EncounterVitals()
    }
    /**
     * Claims this patient as current user's patient
     *
     */
    @Deprecated("Patient is becoming immutable.", ReplaceWith("copy"))
    fun claim(proposedOwner: EndpointId) {
        if(type != ARCHIVED_PATIENT) {
            type = MY_PATIENT
        }
        owner = proposedOwner
    }

    /**
     * Sets patient as network patient
     *
     */
    @Deprecated("Patient is becoming immutable.", ReplaceWith("copy"))
    fun release(proposedOwner: EndpointId) {
        if(type != ARCHIVED_PATIENT) {
            type = NETWORK_PATIENT
        }
        owner = proposedOwner
    }

    /**
     * Changes the type to the value sent in. This is currently used to unarchive a patient, but
     * you need to know whether the patient is local or networked. currently used by PatientRepository
     * only. This field should be protected so that Patient Repository is the only one able to make
     * this change...
     *
     *@param patientType if not one of the three accepted types, then this is ignored.
     */

    /**
     * Validates thresholds for patient vitals. Ensures they are within threshold regions.
     *
     */
    @Deprecated("VitalThresholdsAlert can just be calculated from the vitals and the thresholds")
    fun validateThresholds() {
        val hrStatus = validateSingleThreshold(vitals.hr, vitalThresholds.hrThreshold)
        val spo2Status = validateSingleThreshold(vitals.spo2, vitalThresholds.spo2Threshold)
        val respStatus = validateSingleThreshold(vitals.resp, vitalThresholds.respThreshold)
        val bpdStatus = validateSingleThreshold(vitals.bpd, vitalThresholds.bpdThreshold)
        val bpsStatus = validateSingleThreshold(vitals.bps, vitalThresholds.bpsThreshold)
        val etco2Status = validateSingleThreshold(vitals.etco2, vitalThresholds.etco2Threshold)
        val ibpdStatus = validateSingleThreshold(vitals.ibpd, vitalThresholds.ibpdThreshold)
        val ibpsStatus = validateSingleThreshold(vitals.ibps, vitalThresholds.ibpsThreshold)
        val tempStatus = validateSingleThreshold(vitals.temp, vitalThresholds.tempThreshold)

        vitalThresholdsAlert = VitalThresholdsAlert(hrStatus, spo2Status, respStatus, bpsStatus, bpdStatus, etco2Status, ibpsStatus, ibpdStatus, tempStatus)
    }

    /**
     * Checks if any vitals are outside of their thresholds
     *
     */
    @Deprecated("Just call vitalThresholdsAlert.any()")
    fun isVitalThresholdAlerts(): Boolean {
        validateThresholds()
        return vitalThresholdsAlert.any()
    }

    @Deprecated("Patient is becoming immutable.", ReplaceWith("copy"))
    fun incrementCommandChecksum(commandChecksum: Byte) {
        this.commandChecksum = (this.commandChecksum + commandChecksum).toByte()
    }

    override fun equals(other: Any?): Boolean {
        if (other !is EncounterModel) {
            return false
        }
        return id == other.id
    }

    override fun hashCode(): Int {
        return id.hashCode()
    }

    override fun compareTo(other: EncounterModel): Int {
        return if (this.active && !other.active) -1
                else if (other.active && !this.active) 1
                else if (this.created != other.created) this.created.compareTo(other.created)
                else this.id.compareTo(other.id)
    }

    //This function can be deleted when we switch to a data class. It will be autogenerated then
    fun copy(
        name: PatientName = this.name,
        triage: Triage = this.triage,
        vitals: EncounterVitals = this.vitals,
        exportStatus: ExportStatus = this.exportStatus,
        vitalThresholds: VitalThresholds = this.vitalThresholds,
        hasNewDocumentation: Boolean = this.hasNewDocumentation,
        geoTag: GeoTag = this.geoTag,
        vitalThresholdsAlert: VitalThresholdsAlert = this.vitalThresholdsAlert,
        isTrainingMode: Boolean = this.isTrainingMode,
        maskingJustification: String? = this.maskingJustification,
        patientId: PatientId = this.patientId,
        type: Int = this.type,
        owner: EndpointId = this.owner,
        platformStatus: String? = this.status,
        mode: PatientMode = this.mode,
        autoLog: AutoLog = this.autoLog,
        active: Boolean = this.active,
        commandChecksum: Byte = this.commandChecksum,
        created: Instant = this.created,
        externalIds: Map<String, String> = this.externalIds,
        handedOffTime: Instant? = this.handedOffTime,
        encounterState: EncounterState = this.encounterState,
        encounterRank: Int = this.encounterRank
    ) = EncounterModel(
        this.id,
        name,
        type,
        owner,
        triage,
        vitals,
        vitalThresholds,
        vitalThresholdsAlert,
        hasNewDocumentation,
        geoTag,
        isTrainingMode,
        platformStatus,
        mode,
        autoLog,
        active,
        commandChecksum,
        created,
        exportStatus,
        maskingJustification,
        patientId,
        externalIds,
        handedOffTime,
        encounterState,
        encounterRank
    )
}
