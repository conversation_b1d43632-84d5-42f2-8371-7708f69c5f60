package mil.af.afrl.batdokdata.models.batdoktrainer

import mil.af.afrl.batdokdata.id.BatdokTrainerSensorId

/**
 * Provide the access necessary to [create], [read], [update], and [delete] [BatdokTrainerSensors][BatdokTrainerSensor]
 * for a database.
 */
interface BatdokTrainerSensorDataStore {
    /**
     * Create a database entry for a [BatdokTrainerSensor].
     */
    suspend fun create (sensor: BatdokTrainerSensor)

    /**
     * Read all of the database entries for [BatdokTrainerSensors][BatdokTrainerSensor].
     */
    suspend fun read   ( ) : List<BatdokTrainerSensor>

    /**
     * Update the database entry for a [BatdokTrainerSensor].
     */
    suspend fun update (sensor: BatdokTrainerSensor)

    /**
     * Delete the database entry for a [BatdokTrainerSensor].
     */
    suspend fun delete (id: BatdokTrainerSensorId)
}