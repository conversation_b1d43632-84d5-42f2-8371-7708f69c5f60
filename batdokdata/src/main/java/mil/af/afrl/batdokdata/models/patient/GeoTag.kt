package mil.af.afrl.batdokdata.models.patient

import java.time.Instant
import java.io.Serializable

/**
 * This class contains the information for patient location.
 *
 * Params:
 * - [isTagged] - Whether or not the Geotag has a valid location
 * - [isAttached] - Whether or not the Geotag should update as the phone location updates
 * - [location] - The current location of the Patient
 * - [markTime] - The time in which the Location changes take effect
 *
 */

data class GeoTag(val isTagged: Boolean = false, val isAttached: Boolean = false, val location: Location = Location(), val markTime: Instant? = null) : Serializable
