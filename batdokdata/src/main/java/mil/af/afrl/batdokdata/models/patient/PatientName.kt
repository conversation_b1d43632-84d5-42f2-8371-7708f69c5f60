package mil.af.afrl.batdokdata.models.patient

import java.io.Serializable

/**
 * A Patient has two names, a constant [name] that is based on the order the patient was created, and a modifiable [displayName] that can be set by the user.
 *
 * Created on 9/23/17.
 */

data class PatientName @Deprecated("displayName is going away use buildWithAlias instead") constructor(
    val name: String = "Patient",
    @Deprecated("don't use this anymore, read from alias instead") val displayName: String = name
) : Serializable {
    val alias = displayName
    @Deprecated("use Encounter.getPatientIdentifier()")
    fun toChooserName() : String {
        return if (displayName == name) {
            displayName.substringBefore(" ")
        } else {
            displayName
        }
    }

    companion object {
        fun buildWithAlias(name: String, alias: String = name) = PatientName(name, alias)
    }
}

