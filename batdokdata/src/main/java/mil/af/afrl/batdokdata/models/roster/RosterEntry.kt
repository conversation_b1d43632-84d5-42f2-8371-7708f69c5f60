package mil.af.afrl.batdokdata.models.roster

import com.batman.batdok.infrastructure.network.commands.RosterOuterClass
import java.time.Instant
import java.time.LocalDate

class RosterEntry @JvmOverloads constructor(
    @JvmField var first: String = "",
    @JvmField var last: String = "",
    @JvmField var lastFour: String = "",
    @JvmField var battleRoster: String = "",
    @JvmField var gender: String? = null,
    @JvmField var service: String = "",
    @JvmField var unit: String = "",
    @JvmField var allergies: List<String> = listOf(),
    @JvmField var bloodType: String? = null,
    @JvmField var bloodDate: Instant? = null,
    @JvmField var bloodLT: Boolean = false,
    @JvmField var dob: LocalDate? = null,
    @JvmField var dodId: String = "",
    @JvmField var weight: Float? = null,
    @JvmField var height: Float? = null,
    @JvmField var rank: String? = null,
    @JvmField var nationality: String? = null,
    @JvmField var status: String = "",
    @JvmField var unitPhoneNumber: String = ""
) {

    /**
     * Instantiates a new Roster entry from the Roster Protobuf Message.
     *
     * @param roster the roster data
     */
    constructor(roster: RosterOuterClass.Roster): this(){
        fillFromProtobuf(roster.data)
    }

    private fun fillFromProtobuf(roster: RosterOuterClass.RosterData){
        if(roster.hasFirstName()) {
            first = roster.firstName
        }
        if(roster.hasLastName()) {
            last = roster.lastName
        }
        if(roster.hasSsn()) {
            lastFour = roster.ssn
        }
        if(roster.hasBattleRosterNumber()){
            battleRoster = roster.battleRosterNumber
        }
        if(roster.hasGender()){
            gender = roster.gender
        }else{
            gender = null
        }
        if(roster.hasService()) {
            service = roster.service
        }
        if(roster.hasUnit()) {
            unit = roster.unit
        }
        if(roster.allergiesCount > 0) {
            allergies = roster.allergiesList
        }
        if(roster.hasBloodType()) {
            bloodType = roster.bloodType
        }
        if(roster.hasBloodDate()) {
            bloodDate = Instant.ofEpochSecond(roster.bloodDate)
        }
        if(roster.hasBloodLT()) {
            bloodLT = roster.bloodLT
        }
        if(roster.hasDob()) {
            dob = LocalDate.ofEpochDay(roster.dob)
        }
        if(roster.hasDodId()) {
            dodId = roster.dodId
        }
        if(roster.hasWeight()) {
            weight = roster.weight
        }
        if(roster.hasHeight()) {
            height = roster.height
        }
        if(roster.hasRank()) {
            rank = roster.rank
        }
        if (roster.hasNationality()) {
            nationality = roster.nationality
        }
        if (roster.hasStatus()) {
            status = roster.status
        }
        if (roster.hasUnitPhoneNumber()){
            unitPhoneNumber = roster.unitPhoneNumber
        }
    }

    fun toProtobufItem(): RosterOuterClass.Roster {
        val data = RosterOuterClass.RosterData.newBuilder()
        if (first.isNotEmpty()) {
            data.firstName = first
        }
        if (last.isNotEmpty()) {
            data.lastName = last
        }
        if (lastFour.isNotEmpty()) {
            data.ssn = lastFour
        }
        if (battleRoster.isNotEmpty()) {
            data.battleRosterNumber = battleRoster
        }
        if (gender != null) {
            data.gender = gender!!
        }
        if (service.isNotEmpty()) {
            data.service = service
        }
        if (unit.isNotEmpty()) {
            data.unit = unit
        }
        if (allergies.isNotEmpty()) {
            data.addAllAllergies(allergies)
        }
        if (bloodType != null) {
            data.bloodType = bloodType
        }
        if (bloodDate != null) {
            data.bloodDate = bloodDate!!.epochSecond
        }
        if (bloodLT) {
            data.bloodLT = bloodLT
        }
        if (dob != null) {
            data.dob = dob!!.toEpochDay()
        }
        if (dodId.isNotEmpty()) {
            data.dodId = dodId
        }
        if (weight != null) {
            data.weight = weight!!
        }
        if (height != null) {
            data.height = height!!
        }
        if (rank != null){
            data.rank = rank
        }
        if (nationality != null) {
            data.nationality = nationality
        }
        if (status.isNotEmpty()){
            data.status = status
        }
        if (unitPhoneNumber.isNotEmpty()){
            data.unitPhoneNumber = unitPhoneNumber
        }
        return RosterOuterClass.Roster.newBuilder()
                .setData(data)
                .build()
    }

    override fun equals(other: Any?): Boolean {
        if(other == null || other !is RosterEntry){
            return false
        }
        return this.first == other.first &&
                this.last == other.last &&
                this.lastFour == other.lastFour &&
                this.battleRoster == other.battleRoster &&
                this.dodId == other.dodId
    }

    //Generated by Android Studio
    override fun hashCode(): Int {
        var result = first.hashCode()
        result = 31 * result + last.hashCode()
        result = 31 * result + lastFour.hashCode()
        result = 31 * result + battleRoster.hashCode()
        result = 31 * result + dodId.hashCode()
        return result
    }


}

const val MALE = true
const val FEMALE = false