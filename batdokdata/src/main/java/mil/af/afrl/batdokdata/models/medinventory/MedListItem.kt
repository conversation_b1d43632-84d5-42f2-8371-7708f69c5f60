package mil.af.afrl.batdokdata.models.medinventory

import gov.afrl.batdok.encounter.medicine.KnownMedTypes

/**
 * The object for a DD1380 Med List Item
 */
/**
 * Constructor
 * @param id the id of the med list item
 * @param type the type of the med list item
 * @param name the name of the med list item
 */
class MedListItem @JvmOverloads constructor(val id: Int,
                                            val type: String,
                                            val name: String,
                                            val mode: String? = null) : Comparable<MedListItem> {
    override fun compareTo(other: MedListItem): Int {
        return name.compareTo(other.name)
    }

    companion object {
        /**
         * The default list of Meds in the list of Meds
         */
        @JvmField
        val defaultItems: List<MedListItem> = listOf(
            MedListItem(0, KnownMedTypes.FLUID.dataString, "23.4% Hypertonic Saline (Sodium Chloride, 23.4%)"),
            MedListItem(0, KnownMedTypes.FLUID.dataString, "Lactated Ringers"),
            MedListItem(0, KnownMedTypes.FLUID.dataString, "0.9% Normal Saline (0.9% Sodium Chloride)"),
            MedListItem(0, KnownMedTypes.FLUID.dataString, "Plasma-Lyte A"),
            MedListItem(0, KnownMedTypes.ANALGESIC.dataString, "Tylenol (Acetaminophen)"),
            MedListItem(0, KnownMedTypes.ANALGESIC.dataString, "Fentanyl"),
            MedListItem(0, KnownMedTypes.ANALGESIC.dataString, "Ketamine"),
            MedListItem(0, KnownMedTypes.ANALGESIC.dataString, "Toradol (Ketorolac)"),
            MedListItem(0, KnownMedTypes.ANALGESIC.dataString, "Mobic (Meloxicam)"),
            MedListItem(0, KnownMedTypes.ANALGESIC.dataString, "Versed (Midazolam)"),
            MedListItem(0, KnownMedTypes.ANTIBIOTIC.dataString, "Ertapenem"),
            MedListItem(0, KnownMedTypes.ANTIBIOTIC.dataString, "Moxifloxacin"),
        )
    }
}