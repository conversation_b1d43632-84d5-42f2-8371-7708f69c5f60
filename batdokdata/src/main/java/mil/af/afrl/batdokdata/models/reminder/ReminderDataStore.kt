package mil.af.afrl.batdokdata.models.reminder

import kotlinx.coroutines.flow.Flow
import mil.af.afrl.batman.batdokid.DomainId
import mil.af.afrl.batdokdata.id.EncounterId
import java.time.Duration
import java.time.Instant

/**
 * This class is an interface for datastores that access Medicine Reminders
 */
interface ReminderDataStore {
    suspend fun create(
        encounterId : EncounterId,
        description : String,
        timestamp: Instant? = Instant.now(),
        reminderTime: Duration = Duration.ZERO,
        reminderType: String = Reminder.REMINDER_TYPE
    ) : Reminder
    suspend fun insert(reminder: <PERSON>minder)
    suspend fun delete(id: DomainId)
    fun reminders() : Flow<List<Reminder>>
    suspend fun updateReminder(reminder: Reminder)
}