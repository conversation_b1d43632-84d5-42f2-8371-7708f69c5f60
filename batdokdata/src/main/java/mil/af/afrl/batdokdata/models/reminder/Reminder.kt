package mil.af.afrl.batdokdata.models.reminder

import gov.afrl.batdok.util.Patterns
import gov.afrl.batdok.util.format
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Duration
import java.time.Instant

/**
 * Model for med reminders
 * @property id String identifying the treatment reminder is set for
 * @property encounterId EncounterId the reminder is for
 * @property description String describing the reminder
 * @property timestamp Date when the reminder was made
 * @property reminderTime Long when to make the reminder
 * @property isCustom Boolean if the med is custom
 * @constructor creates a new MedReminderModel
 */
data class Reminder(
    val id: DomainId = DomainId.nil(),
    val encounterId: DomainId = DomainId.nil(),
    val description: String = "",
    val timestamp: Instant? = Instant.now(),
    val reminderTime: Duration = Duration.ZERO,
    val reminderType: String = REMINDER_TYPE
): Comparable<Reminder> {

    companion object {
        const val REMINDER_TYPE = "CUSTOM"
    }

    fun isCustom() : Boolean {
        return reminderType.equals(REMINDER_TYPE, true)
    }

    override fun hashCode(): Int {
        return id.hashCode()
    }

    override fun equals(other: Any?): Boolean {
        if(other !is Reminder){
            return false
        }
        return id == other.id
    }

    fun deepEquals(other: Any?): Boolean{
        if(other !is Reminder){
            return false
        }

        return id == other.id &&
                encounterId == other.encounterId &&
                description == other.description &&
                timestamp == other.timestamp &&
                reminderTime == other.reminderTime &&
                reminderType == other.reminderType
    }

    fun getStartTime(): String{
        return timestamp?.format(Patterns.hm_24_colon)?:""
    }

    /**
     * Returns the time since the reminder was created
     * @return String formatted as "%d:%02d:%02d" or "%02d:%02d" for time since created
     */
    fun getUpdatedTimeSince(): String {
        val time = Duration.between(timestamp, Instant.now()).seconds
        return if (time / 3600 > 0) {
            String.format("%d:%02d:%02d", time / 3600, time / 60 % 60, Math.abs(time) % 60)
        } else {
            String.format("%02d:%02d", time / 60, Math.abs(time) % 60)
        }
    }

    /**
     * Returns the remaining time on the timer
     * @return String formatted as "%d:%02d:%02d" or "%02d:%02d" for the countdown timer
     */
    @JvmOverloads
    fun getUpdatedTimer(asOf: Instant = Instant.now()): String {
        val countDownTime = getCountdownTime(asOf)
        val timer = "--:--"
        return when {
            countDownTime.seconds < 0 -> timer
            countDownTime.seconds / 3600 > 0 -> String.format("%d:%02d:%02d", countDownTime.seconds / 3600, countDownTime.seconds / 60 % 60, Math.abs(countDownTime.seconds) % 60)
            else -> String.format("%02d:%02d", countDownTime.seconds / 60, Math.abs(countDownTime.seconds) % 60)
        }
    }

    fun isTimerExpired(): Boolean {
        return getCountdownTime() < Duration.ZERO
    }

    private fun getCountdownTime(asOf: Instant = Instant.now()) : Duration {
        return (reminderTime.minus(Duration.between(timestamp, asOf)))
    }

    override fun compareTo(other: Reminder): Int {
        val otherTimestamp = other.timestamp ?: Instant.MIN
        val myTimestamp = timestamp ?: Instant.MIN
        return myTimestamp.compareTo(otherTimestamp)
    }
}
