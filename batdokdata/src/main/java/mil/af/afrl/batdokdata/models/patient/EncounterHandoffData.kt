package mil.af.afrl.batdokdata.models.patient
import mil.af.afrl.batdokdata.id.EncounterId

/**
 * Object of data used for patient handoff
 *
 * @property age The patient's age
 * @property stability The patient's current stability (stable or unstable)
 * @property injury Injuries that the patient has
 */
data class EncounterHandoffData @JvmOverloads constructor(
    @Deprecated("This will be handled by the Encounter object") val id: EncounterId,
    val age: String? = null,
    val stability: Boolean? = null,
    val injury: String? = null
){
    @Deprecated("Just use copy", replaceWith = ReplaceWith("copy"))
    fun copyChangeAge(age: String?): EncounterHandoffData {
        return this.copy(age=age)
    }
}