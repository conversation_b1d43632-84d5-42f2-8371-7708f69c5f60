package mil.af.afrl.batdokdata.models.othertreatments

import kotlinx.coroutines.flow.Flow

interface OtherTreatmentsDataStore {
    suspend fun getOtherTreatments(): List<String>
    fun getOtherTreatmentsFlow(tab: String): Flow<List<String>>
    suspend fun setOtherTreatments(otherTreatments: List<String>, tab: String)
    suspend fun getOtherTreatmentsByTab(tab: String): List<String>
}

enum class SubSection{
    M, A, R, C, H, PA, WS, OTHER
}