package mil.af.afrl.batdokdata.models.otherinjury

import kotlinx.coroutines.flow.Flow

/**
 * This interface provides the functions for "Other" Moi datastores
 */
interface OtherInjuryDataStore {
    suspend fun getOtherInjuries(): List<OtherInjury>
    fun getOtherInjuriesFlow(): Flow<List<OtherInjury>>
    suspend fun setOtherInjuries(otherInjuries: kotlin.collections.List<mil.af.afrl.batdokdata.models.otherinjury.OtherInjury>)
}
