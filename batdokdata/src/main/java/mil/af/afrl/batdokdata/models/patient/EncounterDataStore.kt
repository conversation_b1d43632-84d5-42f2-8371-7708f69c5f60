package mil.af.afrl.batdokdata.models.patient

import androidx.lifecycle.LiveData
import com.batman.batdok.infrastructure.network.EndpointId
import kotlinx.coroutines.flow.Flow
import mil.af.afrl.batdokdata.id.EncounterId
import mil.af.afrl.batman.batdokid.DomainId
import java.time.Instant

/**
 * This class is an interface for datastores that access BATDOK Encounters
 */
interface EncounterDataStore {

    suspend fun createNew(encounterId: EncounterId = DomainId.create()): EncounterId

    fun aggregatedEncounterStream(): Flow<List<Encounter>>

    suspend fun update(encounterId: EncounterId, block: Encounter.() -> Encounter):Boolean

    suspend fun remove(id: EncounterId)

    @Deprecated("Use aggregatedEncounterStream instead")
    suspend fun encounter(id: EncounterId): EncounterModel?

    @Deprecated("Use aggregatedEncounterStream instead")
    fun encounterFlow(id: EncounterId): Flow<EncounterModel?>

    @Deprecated("Use aggregatedEncounterStream instead")
    fun liveEncounters(): Flow<List<EncounterModel>>

    @Deprecated("Use aggregatedEncounterStream instead")
    suspend fun encounters(): List<EncounterModel>

    @Deprecated("Use aggregatedEncounterStream instead")
    fun encountersFlow(): Flow<List<EncounterModel>>

    @Deprecated("Use aggregatedEncounterStream instead")
    suspend fun encountersByDateRange(startDate: Instant, endDate: Instant): List<EncounterModel>

    @Deprecated("Use createNew() and then update(encounterId, Encounter.() -> Encounter) instead")
    suspend fun add(encounter: EncounterModel)

    @Deprecated("Use update(EncounterId, Encounter.() -> Encounter) instead")
    suspend fun update(encounter: EncounterModel):Boolean

    @Deprecated("Use update(EncounterId, Encounter.() -> Encounter) instead")
    suspend fun updateHandoff(id: EncounterId, handoffData: EncounterHandoffData)

    @Deprecated("Use aggregatedEncounterStream instead")
    suspend fun getHandoffData(id: EncounterId): EncounterHandoffData?

    @Deprecated("Use aggregatedEncounterStream instead")
    fun getLiveHandoffData(id: EncounterId): LiveData<EncounterHandoffData>

    @Deprecated("Use aggregatedEncounterStream instead")
    fun getFlowHandoffData(id: EncounterId): Flow<EncounterHandoffData>

    @Deprecated("Use aggregatedEncounterStream instead")
    suspend fun getOwner(id: EncounterId): EndpointId?

    @Deprecated("Use update(EncounterId, Encounter.() -> Encounter) instead")
    suspend fun setOwner(id: EncounterId, newOwner: String)

    @Deprecated("Use aggregatedEncounterStream instead")
    suspend fun checksum(id: EncounterId): Byte?

    @Deprecated("Use aggregatedEncounterStream instead")
    suspend fun exists(id: EncounterId): Boolean

    @Deprecated("Use update(EncounterId, Encounter.() -> Encounter) instead")
    suspend fun updateChecksum(id: EncounterId, newChecksum: Byte)
}
