package mil.af.afrl.batdokdata.models.sensor

import mil.af.afrl.batdokdata.id.SensorId


/**
 * This class is an interface for datastores that access BATDOK Sensors
 */
@Deprecated("Use SensorInfoDataStore instead", ReplaceWith("SensorInfoDataStore"))
interface SensorDataStore {

    suspend fun sensors(): List<Sensor>

    suspend fun add(sensor: Sensor)

    suspend fun update(sensor: Sensor)

    suspend fun remove(sensorId: SensorId)

    suspend fun purgeUnconnectedSensors()
}
