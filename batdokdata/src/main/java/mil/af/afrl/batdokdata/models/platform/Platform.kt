package mil.af.afrl.batdokdata.models.platform

import com.batman.documentcommandslibrary.command.util.PlatformStatus
import mil.af.afrl.batdokdata.id.EncounterId
import mil.af.afrl.batdokdata.id.PlatformId
import mil.af.afrl.batman.batdokid.DomainId

/**
 * Object for platforms that store patients for CCP
 *
 */
class Platform(
    @JvmField var id: PlatformId = DomainId.create(),
    @JvmField var name: String = "",
    patients: List<PlatformPatient> = listOf(),
    var outTime: Long = 0,
    var status: String? = null,
    @JvmField var isTutorialPlatform: Boolean = false,
    var destination: String? = null
): Comparable<Platform> {
    var patients: MutableList<PlatformPatient> = patients.toMutableList()

    /**
     * Function to remove patients from platform using a list of IDs
     * @param encounterIds List of patient IDs to be removed from platform
     * @return number of patients removed from the platform.
     */
    fun removePatientsByIds(encounterIds: List<EncounterId?>): Int {
        val patientsToRemove = patients.filter { it.id in encounterIds }
        patients.removeAll(patientsToRemove)
        return patientsToRemove.size
    }

    /**
     * Function to check two platforms against each other for equality
     * @param other Second platform to check against
     * @return True if the two platforms have equal IDs, false otherwise
     */
    override fun equals(other: Any?): Boolean {
        return if (other !is Platform) {
            false
        } else id == other.id
    }

    /**
     * Function to generate a hashCode of the ID for this platform
     * @return The hashcode for the ID of this platform
     */
    override fun hashCode(): Int {
        return id.hashCode()
    }

    fun hasSameProperties(pm: Platform): Boolean {
        return id == pm.id && name == pm.name && patients == pm.patients && outTime == pm.outTime &&
            status == pm.status && isTutorialPlatform == pm.isTutorialPlatform && destination == pm.destination
    }

    /** {@inheritDoc}  */
    override fun compareTo(other: Platform): Int {
        if (outTime == -1L) {
            return if (other.outTime == -1L) 0 else 1
        }
        return if (other.outTime == -1L) {
            -1
        } else outTime.compareTo(other.outTime)
    }

    fun hasStatus(): Boolean {
        return status !in listOf(null, PlatformStatus.UNKNOWN.dataString, "")
    }
}