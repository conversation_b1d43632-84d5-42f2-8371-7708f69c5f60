package mil.af.afrl.batdokdata.models.medinventory

import gov.afrl.batdok.encounter.ids.MedicineId

/**
 * This class holds the information needed to create a fast med.
 *
 * This class is also used for Combat pill pack
 */
open class SavedFastMed @JvmOverloads constructor(var id: MedicineId,
                                                  var medName: String,
                                                  var route: String,
                                                  var dose: String,
                                                  var unit: String,
                                                  var type: String,
                                                  var description: String,
                                                  var mode: String? = null) {

    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        return if (other !is SavedFastMed) {
            false
        } else {
            id == other.id
        }
    }

    override fun hashCode(): Int {
        return id.hashCode()
    }

    override fun toString(): String {
        return medName + " - " + route + " - " + dose.removeSuffix(".0") + " " + unit + (if (description.isNotEmpty()) " - $description" else "")
    }
}