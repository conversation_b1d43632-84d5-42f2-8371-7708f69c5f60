package mil.af.afrl.batdokdata.models.patient

import gov.afrl.batdok.commands.proto.DocumentCommands.DocumentCommand
import gov.afrl.batdok.encounter.Document
import gov.afrl.batdok.util.NameFormatter
import gov.afrl.batdok.util.buildDocumentCommand
import mil.af.afrl.batdokdata.id.EncounterId
import mil.af.afrl.batdokdata.id.PatientId
import mil.af.afrl.batdokdata.models.platform.Platform
import mil.af.afrl.batdokdata.models.sensor.SensorInfo
import mil.af.afrl.batdokdata.models.vital.EncounterVitals
import mil.af.afrl.batman.batdokid.DomainId

data class Patient(val id: PatientId, val encounters: List<Encounter>)

fun List<Encounter>.toPatients() = groupBy { it.data.patientId }.map { Patient(it.key, it.value) }

data class Encounter
@Deprecated("Use the constructor without document. Document wil be calculated by the commands") constructor(
    val id: EncounterId = DomainId.create(),
    val data: EncounterModel = EncounterModel(id),
    val document: Document = Document(),
    val assignedPlatform: Platform? = null,
    val encounterHandoffData: EncounterHandoffData = EncounterHandoffData(id),
    val currentVitals: EncounterVitals = EncounterVitals(),
    val trendsVitals: List<EncounterVitals> = listOf(),
    val vitalThresholds: VitalThresholds = VitalThresholds(),
    val commands: DocumentCommand = buildDocumentCommand(id.copy()),
    val sensorInfo: List<SensorInfo> = listOf()
) {
    constructor(
        id: EncounterId = DomainId.create(),
        data: EncounterModel = EncounterModel(id),
        commands: DocumentCommand = buildDocumentCommand(id.copy()),
        assignedPlatform: Platform? = null,
        encounterHandoffData: EncounterHandoffData = EncounterHandoffData(id),
        currentVitals: EncounterVitals = EncounterVitals(),
        trendsVitals: List<EncounterVitals> = listOf(),
        vitalThresholds: VitalThresholds = VitalThresholds(),
        sensorInfo: List<SensorInfo> = listOf()
    ) : this(
        id,
        data,
        Document().apply { handle(commands.commandsList) },
        assignedPlatform,
        encounterHandoffData,
        currentVitals,
        trendsVitals,
        vitalThresholds,
        commands,
        sensorInfo
    )

    val vitalAlerts: VitalThresholdsAlert
        get() {
            val hrStatus = validateSingleThreshold(currentVitals.hr, vitalThresholds.hrThreshold)
            val spo2Status = validateSingleThreshold(currentVitals.spo2, vitalThresholds.spo2Threshold)
            val respStatus = validateSingleThreshold(currentVitals.resp, vitalThresholds.respThreshold)
            val bpdStatus = validateSingleThreshold(currentVitals.bpd, vitalThresholds.bpdThreshold)
            val bpsStatus = validateSingleThreshold(currentVitals.bps, vitalThresholds.bpsThreshold)
            val etco2Status = validateSingleThreshold(currentVitals.etco2, vitalThresholds.etco2Threshold)
            val ibpdStatus = validateSingleThreshold(currentVitals.ibpd, vitalThresholds.ibpdThreshold)
            val ibpsStatus = validateSingleThreshold(currentVitals.ibps, vitalThresholds.ibpsThreshold)
            val tempStatus = validateSingleThreshold(currentVitals.temp, vitalThresholds.tempThreshold)

            return VitalThresholdsAlert(hrStatus, spo2Status, respStatus, bpsStatus, bpdStatus, etco2Status, ibpsStatus, ibpdStatus, tempStatus)
        }

    fun getPatientIdentifier(identifier: PatientIdentification): String {
        val name = document.info.name?.toString(NameFormatter.Format.LAST_FIRST_MIDDLE)?.takeUnless { it.isBlank() } ?: data.name.name
        val alias = document.info.alias?.takeUnless { it.isEmpty() } ?: data.name.alias
        val brn = document.info.battleRosterNumber?.takeUnless { it.isEmpty() }

        return when (identifier) {
            PatientIdentification.NAME -> name
            PatientIdentification.ALIAS -> alias
            PatientIdentification.BRN -> brn ?: name
        }
    }
}

enum class PatientIdentification(val id: String) {
    NAME("Patient Name"),
    ALIAS("Alias"),
    BRN("Battle Roster #");

    companion object {
        fun fromId(id: String) = entries.find { it.id == id }
    }
}