package mil.af.afrl.batdokdata.models.checklistitem

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first

interface ChecklistItemDataStore {
    fun getAllLiveChecklistItems(): Flow<List<CheckListModel>>
    suspend fun getAllChecklistItems(): List<CheckListModel> = getAllLiveChecklistItems().first()

    suspend fun upsertChecklistItem(checkListModel: CheckListModel)

    suspend fun deleteCheckListItem(checkListModel: CheckListModel)
    suspend fun deleteAll()
}