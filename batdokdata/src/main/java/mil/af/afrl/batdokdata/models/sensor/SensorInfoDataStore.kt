package mil.af.afrl.batdokdata.models.sensor

import kotlinx.coroutines.flow.Flow
import mil.af.afrl.batdokdata.id.SensorId


/**
 * This class is an interface for datastores that access BATDOK Sensor Information
 */
interface SensorInfoDataStore {

    fun sensorStream(): Flow<List<SensorInfo>>

    suspend fun add(sensor: SensorInfo)

    suspend fun update(sensor: SensorInfo)

    suspend fun remove(sensorId: SensorId)

    suspend fun purgeUnconnectedSensors()

    suspend fun addAlias(alias: SensorAliasInfo)
    suspend fun setAliasList(aliases: List<SensorAliasInfo>)
    fun aliasStream(): Flow<List<SensorAliasInfo>>
    suspend fun updateAlias(alias: SensorAliasInfo)
    suspend fun removeAlias(alias: SensorAliasInfo)
}
