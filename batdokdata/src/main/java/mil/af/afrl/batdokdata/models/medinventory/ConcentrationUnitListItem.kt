package mil.af.afrl.batdokdata.models.medinventory

class ConcentrationUnitListItem(
    @JvmField val id: Int,
    @JvmField val unit: String
) : Comparable<ConcentrationUnitListItem> {

    override operator fun compareTo(other: ConcentrationUnitListItem): Int {
        return unit.lowercase().compareTo(other.unit.lowercase())
    }

    companion object {
        val defaultItems: List<ConcentrationUnitListItem> = listOf(
            ConcentrationUnitListItem(0, "mg/mL"),
            ConcentrationUnitListItem(0, "mcg/mL"),
            ConcentrationUnitListItem(0, "mU/mL"),
        )
    }
}