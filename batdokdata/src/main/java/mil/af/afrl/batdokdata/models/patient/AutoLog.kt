package mil.af.afrl.batdokdata.models.patient

import java.io.Serializable
import java.time.Duration

/**
 * Holds Autologging information for Encounter Vitals
 *
 * @param isLogging Whether or not the patient is autologging to the Vitals Tab
 * @param startLogging Where or not logging should start for the patient
 * @param loggingInterval Logging interval (Defaults to 10 min)
 */
data class AutoLog(val isLogging: <PERSON>olean = false,
                   val startLogging: Boolean = false,
                   val loggingInterval: Duration  = Duration.ofMinutes(10) ) : Serializable
