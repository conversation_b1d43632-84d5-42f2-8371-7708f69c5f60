package mil.af.afrl.batdokdata.models.medinventory

/**
 * The object for a DD1380 Route List Item
 */
class MedRouteListItem(
    @JvmField val id: Int,
    @JvmField val name: String
) {
    companion object {
        val ALL = arrayOf("IV", "IO", "IM", "PO", "PR", "SL", "SQ", "IN")

        /**
         * returns alist of all of the default routes
         */
        val defaultItems: List<MedRouteListItem> = listOf(
            MedRouteListItem(0, "IV"),
            MedRouteListItem(0, "IO"),
            MedRouteListItem(0, "IM"),
            MedRouteListItem(0, "PO"),
            MedRouteListItem(0, "PR"),
            MedRouteListItem(0, "SL"),
            MedRouteListItem(0, "SQ"),
            MedRouteListItem(0, "IN"),
        )
    }
}