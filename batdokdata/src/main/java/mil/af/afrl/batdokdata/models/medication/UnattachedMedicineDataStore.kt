package mil.af.afrl.batdokdata.models.medication

import gov.afrl.batdok.encounter.ids.MedicineId
import gov.afrl.batdok.encounter.medicine.Medicine

interface UnattachedMedicineDataStore {
    suspend fun addMedicine(treatment: Medicine)
    suspend fun updateMedicine(treatment: Medicine)
    suspend fun getMedicine(id: MedicineId): Medicine

    suspend fun removeMedicine(id: MedicineId)

    suspend fun getUnattachedMedicines(): List<Medicine>
    suspend fun medicineExists(id: MedicineId): Boolean
}