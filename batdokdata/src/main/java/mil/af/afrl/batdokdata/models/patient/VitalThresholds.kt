package mil.af.afrl.batdokdata.models.patient

import java.io.Serializable
import kotlin.math.pow
import kotlin.math.roundToLong

/**
 * This class holds threshold levels for all vitals.
 *
 * Created on 9/19/17.
 */
@Deprecated("Use Threshold instead.")
open class TwoPartVitalThreshold<T : Number>(var min: T, var low: T): Serializable {
        override fun equals(other: Any?): Boolean {
                if (this === other) return true
                if (javaClass != other?.javaClass) return false

                other as TwoPartVitalThreshold<*>

                if (min != other.min) return false
                if (low != other.low) return false

                return true
        }

        override fun hashCode(): Int {
                var result = min.hashCode()
                result = 31 * result + low.hashCode()
                return result
        }
}

@Deprecated("Use Threshold instead.")
class TwoPartIntVitalThreshold(min: Int, low: Int) : TwoPartVitalThreshold<Int>(min,low)
@Deprecated("Use Threshold instead.")
open class FourPartVitalThreshold<T : Number>(var min: T, var low: T, var high: T, var max: T): Serializable {
        override fun equals(other: Any?): Boolean {
                if (this === other) return true
                if (javaClass != other?.javaClass) return false

                other as FourPartVitalThreshold<*>

                if (min != other.min) return false
                if (low != other.low) return false
                if (high != other.high) return false
                if (max != other.max) return false

                return true
        }

        override fun hashCode(): Int {
                var result = min.hashCode()
                result = 31 * result + low.hashCode()
                result = 31 * result + high.hashCode()
                result = 31 * result + max.hashCode()
                return result
        }
}

@Deprecated("Use Threshold instead.")
class FourPartIntVitalThreshold(min: Int, low: Int, high: Int, max: Int) : FourPartVitalThreshold<Int>(min,low, high, max)
@Deprecated("Use Threshold instead.")
class FourPartFloatVitalThreshold(min: Float, low: Float, high: Float, max: Float) : FourPartVitalThreshold<Float>(min,low, high, max)

/**
 * Stores threshold info for vitals.
 *
 * It can have nulls set for its min/low/high/max values and a specific number of decimal places.
 * This obviates the need for separate two-part and four-part (and int and float) thresholds,
 * and allows for other types of thresholds (e.g. those with a min and max but no low or high values).
 *
 * The end result is that the UI can be more data driven.
 *
 * @param name the name of the vital sign being monitored
 * @param decimals the number of decimal places to round to (e.g. zero for integer values)
 * @param min the minimum threshold (also minimum slider value in the threshold editor UI)
 * @param low the low threshold (can be null in which case there is no low threshold, only a minimum)
 * @param high the high threshold (can be null in which case there is no high threshold, only a maximum)
 * @param max the maximum threshold (also maximum slider value in the threshold editor UI)
 */
class Threshold(var name: String, var decimals: Int = 0, var min: Float = 0f, var low: Float? = null, var high: Float? = null, var max: Float = 100f): Serializable {

        /** Converts a [FourPartVitalThreshold] to a [Threshold]. */
        constructor(name: String, decimals: Int = 0, threshold: FourPartVitalThreshold<out Number>)
                : this(name, decimals, threshold.min.toFloat(), threshold.low.toFloat(), threshold.high.toFloat(), threshold.max.toFloat())

        /** Converts a [TwoPartVitalThreshold] to a [Threshold]. */
        constructor(name: String, decimals: Int = 0, threshold: TwoPartVitalThreshold<out Number>)
                : this(name, decimals, threshold.min.toFloat(), threshold.low.toFloat(), null, 100f)

        override fun equals(other: Any?): Boolean {
                if (this === other) return true
                if (javaClass != other?.javaClass) return false

                other as Threshold

                if (name != other.name) return false
                if (decimals != other.decimals) return false
                if (min != other.min) return false
                if (low != other.low) return false
                if (high != other.high) return false
                if (max != other.max) return false

                return true
        }

        override fun hashCode(): Int {
                var result = name.hashCode()
                result = 31 * result + decimals.hashCode()
                result = 31 * result + min.hashCode()
                result = 31 * result + low.hashCode()
                result = 31 * result + high.hashCode()
                result = 31 * result + max.hashCode()
                return result
        }

        /** Rounds numeric properties to the specified number of decimal places (from the decimals property). */
        fun round() {
                min = min.roundToDecimals(decimals)
                low = low.roundToDecimals(decimals)
                high = high.roundToDecimals(decimals)
                max = max.roundToDecimals(decimals)
        }

        /** Converts this [Threshold] to a [FourPartIntVitalThreshold]. */
        fun toFourPartIntVitalThreshold(): FourPartIntVitalThreshold {
                return FourPartIntVitalThreshold(min.toInt(), low?.toInt()?:0, high?.toInt()?:100, max.toInt())
        }

        /** Converts this [Threshold] to a [FourPartFloatVitalThreshold]. */
        fun toFourPartFloatVitalThreshold(): FourPartFloatVitalThreshold {
                return FourPartFloatVitalThreshold(min, low?:0f, high?:100f, max)
        }

        /** Converts this [Threshold] to a [TwoPartIntVitalThreshold] using the lwo threshold as the threshold value. */
        fun toTwoPartIntVitalThresholdUsingLow(): TwoPartIntVitalThreshold {
                return TwoPartIntVitalThreshold(min.toInt(), low?.toInt()?:0) // NOTE: max value is lost here
        }

        override fun toString(): String =
                "Threshold: {name: $name, decimals: $decimals, min: $min, low: ${low?:"N/A"}, high: ${high?:"N/A"}, max: $max}"
}

// TODO: put these extension functions in a file somewhere that they can be accessed from other code

/** Rounds a number to the specified number of decimal places. */
private fun Float?.roundToDecimals(decimals: Int) : Float? {
        if (this == null) return null
        return this!!.roundToDecimals(decimals)
}

/** Rounds a number to the specified number of decimal places. */
private fun Float.roundToDecimals(decimals: Int) : Float {
        val powerOfTen = 10.0.pow(decimals.toDouble()).toFloat()
        return (this * powerOfTen).roundToLong() / powerOfTen
}

/** Formats a number with the specified number of decimal places. */
fun Float.toString(decimals: Int): String = "%.${decimals}f".format(this)

data class VitalThresholds(
        var hrThreshold: Threshold = Threshold(ABBREVIATION_HEART_RATE,0,30f, 50f, 120f, 140f),
        var spo2Threshold: Threshold = Threshold(ABBREVIATION_OXYGEN_SATURATION,0, 85f, 90f),
        var respThreshold: Threshold = Threshold(ABBREVIATION_RESPIRATION_RATE,0,5f, 10f, 30f, 45f),
        var bpsThreshold: Threshold = Threshold(ABBREVIATION_BLOOD_PRESSURE_SYSTOLIC,0,60f, 90f, 160f, 200f),
        var bpdThreshold: Threshold = Threshold(ABBREVIATION_BLOOD_PRESSURE_DIASTOLIC,0,30f, 51f, 111f, 140f),
        var etco2Threshold: Threshold = Threshold(ABBREVIATION_CARBON_DIOXIDE_END_TIDAL,0,15f, 35f, 45f, 65f),
        var ibpsThreshold: Threshold = Threshold(ABBREVIATION_BLOOD_PRESSURE_SYSTOLIC_I,0,60f, 90f, 160f, 200f),
        var ibpdThreshold: Threshold = Threshold(ABBREVIATION_BLOOD_PRESSURE_DIASTOLIC_I,0,30f, 51f, 111f, 140f),
        var tempThreshold: Threshold = Threshold(ABBREVIATION_TEMPERATURE_FAHRENHEIT,1, 86f, 91.4f, 100f, 104f),
): Serializable {
        /** Lists all thresholds. */
        fun list(): List<Threshold> = listOf(hrThreshold, spo2Threshold, respThreshold, bpsThreshold, bpdThreshold, etco2Threshold, ibpsThreshold, ibpdThreshold, tempThreshold)

        /** Loads thresholds from a list.
         * There should be a threshold for each threshold type (e.g. HR, SPO2, etc). */
        fun loadFromList(list: List<Threshold>) {
                hrThreshold = list.single { it.name == ABBREVIATION_HEART_RATE }
                spo2Threshold = list.single { it.name == ABBREVIATION_OXYGEN_SATURATION }
                respThreshold = list.single { it.name == ABBREVIATION_RESPIRATION_RATE }
                bpsThreshold = list.single { it.name == ABBREVIATION_BLOOD_PRESSURE_SYSTOLIC }
                bpdThreshold = list.single { it.name == ABBREVIATION_BLOOD_PRESSURE_DIASTOLIC }
                etco2Threshold = list.single { it.name == ABBREVIATION_CARBON_DIOXIDE_END_TIDAL }
                ibpsThreshold = list.single { it.name == ABBREVIATION_BLOOD_PRESSURE_SYSTOLIC_I }
                ibpdThreshold = list.single { it.name == ABBREVIATION_BLOOD_PRESSURE_DIASTOLIC_I }
                tempThreshold = list.single { it.name == ABBREVIATION_TEMPERATURE_FAHRENHEIT }
        }

        companion object {
                // constants to avoid typos
                const val ABBREVIATION_HEART_RATE = "HR"
                const val ABBREVIATION_OXYGEN_SATURATION = "SPO2"
                const val ABBREVIATION_RESPIRATION_RATE = "Resp"
                const val ABBREVIATION_BLOOD_PRESSURE_SYSTOLIC = "Systolic"
                const val ABBREVIATION_BLOOD_PRESSURE_DIASTOLIC = "Diastolic"
                const val ABBREVIATION_CARBON_DIOXIDE_END_TIDAL = "EtCO2"
                const val ABBREVIATION_BLOOD_PRESSURE_SYSTOLIC_I = "iSystolic"
                const val ABBREVIATION_BLOOD_PRESSURE_DIASTOLIC_I = "iDiastolic"
                const val ABBREVIATION_TEMPERATURE_FAHRENHEIT = "Temp"
        }
}

/**
 * Get default adult thresholds (Same as calling the empty constructor)
 */
fun createAdultThresholds(): VitalThresholds {
    return VitalThresholds()
}

/**
 * Get default Pediatric Thresholds
 */
fun createPedThresholds(): VitalThresholds {
    return VitalThresholds(
            hrThreshold = Threshold(VitalThresholds.ABBREVIATION_HEART_RATE,0, 30f, 50f, 120f, 140f),
            spo2Threshold = Threshold(VitalThresholds.ABBREVIATION_OXYGEN_SATURATION,0, 85f, 88f),
            respThreshold = Threshold(VitalThresholds.ABBREVIATION_RESPIRATION_RATE,0,5f, 6f, 12f, 45f),
            bpsThreshold = Threshold(VitalThresholds.ABBREVIATION_BLOOD_PRESSURE_SYSTOLIC,0,60f, 110f, 140f, 200f),
            bpdThreshold = Threshold(VitalThresholds.ABBREVIATION_BLOOD_PRESSURE_DIASTOLIC,0,30f, 100f, 120f, 140f),
            etco2Threshold = Threshold(VitalThresholds.ABBREVIATION_CARBON_DIOXIDE_END_TIDAL,0,15f, 35f, 45f, 65f),
            ibpsThreshold = Threshold(VitalThresholds.ABBREVIATION_BLOOD_PRESSURE_SYSTOLIC_I,0,60f, 110f, 140f, 200f),
            ibpdThreshold = Threshold(VitalThresholds.ABBREVIATION_BLOOD_PRESSURE_DIASTOLIC_I,0,30f, 100f, 120f, 140f),
            tempThreshold = Threshold(VitalThresholds.ABBREVIATION_TEMPERATURE_FAHRENHEIT,1, 86f, 91.4f, 100f, 104f))
}
/**
 * Used to check vital status is within thresholds
 *
 * @param vital Vital to check
 * @param thresholds vital threshold
 */
fun <T : Number> validateSingleThreshold(
    vital: VitalWithRank<T>?, thresholds: FourPartVitalThreshold<T>
): Int {
        var status = STATUS_OKAY
        if (vital != null && vital.value != null && vital.value != -1) {
                if (vital.value!!.toFloat() <= thresholds.min.toFloat() || vital.value!!.toFloat() >= thresholds.max.toFloat()) {
                        status = STATUS_ALERT
                } else if (vital.value!!.toFloat() <= thresholds.low.toFloat() || vital.value!!.toFloat() >= thresholds.high.toFloat()) {
                        status = STATUS_WARN
                }
        }
        return status
}

/**
 * Used to check vital status is within thresholds
 *
 * @param vital Vital to check
 * @param thresholds vital threshold
 */
fun <T : Number> validateSingleThreshold(
    vital: VitalWithRank<T>?, thresholds: TwoPartVitalThreshold<T>
): Int {
        var status = STATUS_OKAY
        if (vital != null && vital.value != null && vital.value != -1) {
                if (vital.value!!.toFloat() <= thresholds.min.toFloat()) {
                        status = STATUS_ALERT
                } else if (vital.value!!.toFloat() <= thresholds.low.toFloat()) {
                        status = STATUS_WARN
                }
        }
        return status
}

/**
 * Used to check vital status is within thresholds
 *
 * @param vital Vital to check
 * @param thresholds vital threshold
 */
fun <T : Number> validateSingleThreshold(
        vital: VitalWithRank<T>?, thresholds: Threshold
): Int {
        var status = STATUS_OKAY
        if (vital?.value != null && vital.value != -1) {
                val vitalValue = vital.value.toFloat()
                if (vitalValue <= thresholds.min || (vitalValue >= thresholds.max && thresholds.high != null)) {
                        status = STATUS_ALERT
                } else if (thresholds.low != null && vitalValue <= thresholds.low!!.toFloat()
                        || thresholds.high != null && vitalValue >= thresholds.high!!.toFloat()) {
                        status = STATUS_WARN
                }
        }
        return status
}

/** Creates a string representation of a list of thresholds. */
fun List<Threshold>.dump() = joinToString(prefix = "[", postfix = "]", separator = ",\n") { it.toString() }

/**
 *  Determines if two lists of thresholds match.
 *  They don't need to be in the same order,
 *  but there need to be the same threshold names
 *  and each threshold should have the same values
 *  as the correspondingly named threshold
 *  in the other list.
 */
fun doThresholdsMatch(list1: List<Threshold>, list2: List<Threshold>): Boolean {
        if (list1.size != list2.size) {
                // lists are not the same length, they clearly don't match
                return false;
        }

        // make sure every element in list1 matches an element in list2
        // (the opposite should then also be true since the number of elements in each list is equal)
        return list1.map { it1 -> list2.any { it2 -> it1 == it2 } }.all { it }
}