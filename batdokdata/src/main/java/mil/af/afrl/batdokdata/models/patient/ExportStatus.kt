package mil.af.afrl.batdokdata.models.patient

import java.io.Serializable
import java.time.Instant

enum class RegisteredExporters(val exporterName: String){
    HL7("HL7 Exporter")
}

class ExportStatus @JvmOverloads @Deprecated("Use fromExportWithTime. In the next major version, it will be turned into a constructor") constructor(
    initialExportStatusMap:Map<String, Int> = mapOf(),
    initialExportTimestampMap: Map<String, Instant> = mapOf()
) : Serializable {
    companion object{
        fun fromExportWithTime(initialExportMap: Map<String, Pair<Int, Instant>> = mapOf()) = ExportStatus(
            initialExportMap.mapValues { (_, value) -> value.first },
            initialExportMap.mapValues { (_, value) -> value.second }
        )
    }


    private val exportStatusMap: MutableMap<String, Int> = initialExportStatusMap.toMutableMap()
    private val exportTimestampMap: Map<String, Instant> = initialExportTimestampMap

    fun withNewStatus(exporter: String, status: Int, timestamp: Instant) = ExportStatus(
        exportStatusMap + Pair(exporter, status),
        exportTimestampMap + Pair(exporter, timestamp)
    )

    /**
     * Returns a map of Exporters to a pair of the most recent status and timestamp
     */
    fun getExports() = exportStatusMap.mapValues { (key, value) -> Pair(value, exportTimestampMap[key] ?: Instant.now()) }

    operator fun get(exporter: String) = getExports()[exporter]
    operator fun get(exporter: RegisteredExporters) = get(exporter.exporterName)

    fun hasFailedExport() = exportStatusMap.any { it.value != 200 }
    fun getFailedExports() = exportStatusMap.filter { it.value != 200 }
    fun getFailedExportsWithTimes() = getExports().filter { it.value.first != 200 }

    @Deprecated("Use withNewStatus instead", ReplaceWith("withNewStatus"))
    fun applyOnCopy(block : ExportStatus.()->Unit): ExportStatus{
        return ExportStatus(exportStatusMap).apply {
            block()
        }
    }

    @Deprecated("Use withNewStatus instead", ReplaceWith("withNewStatus"))
    fun addExportStatus(exporter: String, statusCode: Int) {
        exportStatusMap[exporter] = statusCode
    }

    @Deprecated("Use get ([exporter]) instead", ReplaceWith("get"))
    fun getExportStatus(exporter: String) = exportStatusMap[exporter]

    @Deprecated("Use withNewStatus instead", ReplaceWith("withNewStatus"))
    fun addExportStatus(exporter: RegisteredExporters, statusCode: Int) = addExportStatus(exporter.exporterName, statusCode)
    @Deprecated("Use get ([exporter]) instead", ReplaceWith("get"))
    fun getExportStatus(exporter: RegisteredExporters) = getExportStatus(exporter.exporterName)
    @Deprecated("It doesn't have everything we need", ReplaceWith("getExports"))
    fun getAllExports() = exportStatusMap.toMap()

    @Deprecated("Don't use this. This is not a requirement, it is not a capability we want")
    fun clearExportStatusOf(exporter: String) = exportStatusMap.remove(exporter)
    @Deprecated("Don't use this. This is not a requirement, it is not a capability we want")
    fun clearExportStatusOf(exporter: RegisteredExporters) = clearExportStatusOf(exporter.exporterName)
}