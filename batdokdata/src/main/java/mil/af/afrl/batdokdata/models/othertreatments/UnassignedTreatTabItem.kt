package mil.af.afrl.batdokdata.models.othertreatments

import gov.afrl.batdok.commands.proto.addTreatment
import gov.afrl.batdok.encounter.ids.TreatmentId
import gov.afrl.batdok.encounter.treatment.CommonTreatments
import java.time.Instant
import com.google.protobuf.Any as ProtoAny

class UnassignedTreatTabItem(
    var id: TreatmentId,
    var name: String,
    var timestamp: Instant,
    var treatmentData: ProtoAny?
)

fun buildAddTreatmentCommand(item: UnassignedTreatTabItem) = addTreatment {
    this.treatment = CommonTreatments.fromString(item.name)
    this.timestamp = item.timestamp.epochSecond
    item.treatmentData?.let { this.treatmentData = it }
}