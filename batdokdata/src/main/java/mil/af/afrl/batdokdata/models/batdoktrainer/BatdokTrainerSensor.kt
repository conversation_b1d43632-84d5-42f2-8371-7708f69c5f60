package mil.af.afrl.batdokdata.models.batdoktrainer

import mil.af.afrl.batdokdata.id.BatdokTrainerSensorId
import mil.af.afrl.batman.batdokid.DomainId
import java.io.Serializable
import java.security.SecureRandom

const val AVAILABLE_NOT_SENDING = -1
const val NOT_AVAILABLE = 0
const val AVAILABLE_SENDING = 1

/**
 * A [BatdokTrainerSensor] is a data class which holds a sensor's current slider values,
 * the enabled vitals, and the current vital.
 */
data class BatdokTrainerSensor(val id: BatdokTrainerSensorId = DomainId.create(),
                               var name: String,
                               var hr: Int = 60,
                               var bps: Int = 120,
                               var bpd: Int = 80,
                               var rr: Int = 8,
                               var spo2: Int = 98,
                               var etco2: Int = 40,
                               var ibps: Int = 120,
                               var ibpd: Int = 80,
                               var temp: Float = 97.7f,
                               var isHR: Int = NOT_AVAILABLE,
                               var isBP: Int = NOT_AVAILABLE,
                               var isRR: Int = NOT_AVAILABLE,
                               var isSPO2: Int = NOT_AVAILABLE,
                               var isETCO2: Int = NOT_AVAILABLE,
                               var isIBP: Int = NOT_AVAILABLE,
                               var isTEMP: Int = NOT_AVAILABLE,
                               val sendOverBatdokNet: Boolean = true,
                               val sendToBTSpoofer: Boolean = false,
                               val btSpooferMacAddress: String? = null): Serializable {

    constructor(id: BatdokTrainerSensorId = DomainId.create(),
                name: String,
                isHR: Boolean = false,
                isBP: Boolean = false,
                isRR: Boolean = false,
                isSPO2: Boolean = false,
                isETCO2: Boolean = false,
                isIBP: Boolean = false,
                isTEMP: Boolean = false):
        this(
            id,
            name,
            isHR = if(isHR) AVAILABLE_SENDING else NOT_AVAILABLE,
            isBP = if(isBP) AVAILABLE_SENDING else NOT_AVAILABLE,
            isRR = if(isRR) AVAILABLE_SENDING else NOT_AVAILABLE,
            isSPO2 = if(isSPO2) AVAILABLE_SENDING else NOT_AVAILABLE,
            isETCO2 = if(isETCO2) AVAILABLE_SENDING else NOT_AVAILABLE,
            isIBP = if(isIBP) AVAILABLE_SENDING else NOT_AVAILABLE,
            isTEMP = if(isTEMP) AVAILABLE_SENDING else NOT_AVAILABLE
    )

    override fun hashCode(): Int {
        return this.id.hashCode()
    }

    override fun equals(other: Any?): Boolean {
        if(other !is BatdokTrainerSensor){
            return false;
        }
        return this.id == other.id
    }

    /**
     * The most recent vital created by [createNextVital].
     */
    private var vital: BatdokTrainerSensorVital = BatdokTrainerSensorVital(id, name)

    /*
     * Define some randoms to be used to select the range and sign to oscillate a vital from a BatdokTrainerSensors
     * stored value.
     */
    private val rangeRandom: SecureRandom = SecureRandom() // These do not need to be truly random so it is okay if
    private val signRandom: SecureRandom = SecureRandom()  // they follow the same oscillation pattern every time.

    /**
     * Create the next vital for a [BatdokTrainerSensor].
     */
    fun createNextVital() {
        val hr = if(isHR == AVAILABLE_SENDING) nextVital(hr) else -1
        val bps = if(isBP == AVAILABLE_SENDING) nextVital(bps) else -1
        val bpd = if(isBP == AVAILABLE_SENDING) nextVital(bpd) else -1
        val rr = if(isRR == AVAILABLE_SENDING) nextVital(rr) else -1
        val spo2 = if(isSPO2 == AVAILABLE_SENDING) nextVital(spo2) else -1
        val etco2 = if(isETCO2 == AVAILABLE_SENDING) nextVital(etco2) else -1
        val ibps = if(isIBP == AVAILABLE_SENDING) nextVital(ibps) else -1
        val ibpd = if(isIBP == AVAILABLE_SENDING) nextVital(ibpd) else -1
        val temp = if(isTEMP == AVAILABLE_SENDING) nextVitalF(temp) else -1f

        vital = BatdokTrainerSensorVital(id, name,
                                         if(hr > 300) 300 else hr,
                                         if(bps > 300) 300 else bps,
                                         if(bpd > 300) 300 else bpd,
                                         if(rr > 50) 50 else rr,
                                         if(spo2 > 100) 100 else spo2,
                                         if(etco2 > 75) 75 else etco2,
                                         if(ibps > 300) 300 else ibps,
                                         if(ibpd > 300) 300 else ibpd,
                                         if(temp > 120f) 120f else temp)
    }

    /**
     * Create the next vital for a single vital type.
     */
    private fun nextVital(vital: Int): Int {
        val nextVital = (rangeRandom.nextInt(3) * if (signRandom.nextInt(1) == 1) 1 else -1) + vital
        return if (nextVital < 0) 0 else nextVital
    }

    /**
     * Take the float, multiply it by 10, go up or down by at most 3, then divide by 10 to get the float again
     */
    private fun nextVitalF(vital: Float): Float {
        val nextVital = ((rangeRandom.nextInt(3) * if (signRandom.nextInt(1) == 1) 1 else -1) + (vital*10))/10f
        return if (nextVital < 0) 0f else nextVital
    }

    /**
     * Get the current vital of a [BatdokTrainerSensor]
     */
    fun getVital(): BatdokTrainerSensorVital {
        return vital
    }

    override fun toString(): String {
        return name
    }
}