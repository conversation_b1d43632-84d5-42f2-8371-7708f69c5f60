package mil.af.afrl.batdokdata.models.burnsheet

import mil.af.afrl.batdokdata.id.EncounterId
import mil.af.afrl.batman.batdokid.DomainId
import java.io.Serializable
import java.time.Instant
class MedCardBurnSheetInfo(
    @JvmField var id: DomainId = DomainId.create(),
    @JvmField var patientDate: Instant? = null,
    @JvmField var injuryDateTime: Instant? = null,
    @JvmField var evaluationDateTime: Instant? = null,
    @JvmField var patientSSN: String? = null,
    @JvmField var patientName: String? = null,
    @JvmField var patientWeightInKGs: Float? = null,
    @JvmField var percentTBSA: String? = null,
    @JvmField var ruleOfTens: String? = null,
    @JvmField var dailyMax: String? = null,
    @JvmField var treatmentFacility: String? = null,
    @JvmField var encounterID: EncounterId? = null,
    @JvmField var patientDisplayName: String? = null,
    @JvmField var burnTeam: Boolean? = null,
    @JvmField var weightUnit: String? = "KG"
): Serializable