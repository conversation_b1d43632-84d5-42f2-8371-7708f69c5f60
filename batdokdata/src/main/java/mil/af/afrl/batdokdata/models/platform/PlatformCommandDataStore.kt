package mil.af.afrl.batdokdata.models.platform

import com.batman.batdok.infrastructure.network.commands.PlatformCommands
import mil.af.afrl.batdokdata.id.PlatformId
import java.time.Instant

/**
 * This class is an interface for datastores that access Platform Commands
 */
interface PlatformCommandDataStore {

    suspend fun commands(): List<PlatformCommands.PlatformMessage>

    suspend fun platformEventsByDateRange(startDate: Instant, endDate: Instant): List<PlatformExecReportEvent>

    suspend fun add(command: PlatformCommands.PlatformMessage, event: String)

    suspend fun remove(id: String)

    suspend fun removeAll()

    suspend fun removeForPlatform(id: PlatformId)

    class PlatformExecReportEvent(val date: Instant, val event: String)
}