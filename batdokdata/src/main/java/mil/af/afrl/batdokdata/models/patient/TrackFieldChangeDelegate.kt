package mil.af.afrl.batdokdata.models.patient

import kotlin.properties.ReadWriteProperty
import kotlin.reflect.KMutableProperty
import kotlin.reflect.KProperty

class TrackFieldChangeDelegate<T,V>(var type:T, var value:V, val changedFields: MutableSet<KMutableProperty<*>>, private val setter: (TrackFieldChangeDelegate<T,V>.(V)->Boolean)?=null) :
    ReadWriteProperty<T, V> {
    override operator fun getValue(thisRef: T, property: KProperty<*>): V {
        return value
    }

    override operator fun setValue(thisRef: T, property: KProperty<*>, value: V) {
        val changed = value != getValue(thisRef,property)
        val updated = if(setter==null) {
            this.value = value
            true
        }else{
            setter.invoke(this,value)
        }
        if(updated && changed && property is KMutableProperty<*>){
            changedFields.add(property);
        }
    }
}