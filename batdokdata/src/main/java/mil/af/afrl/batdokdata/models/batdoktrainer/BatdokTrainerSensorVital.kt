package mil.af.afrl.batdokdata.models.batdoktrainer

import mil.af.afrl.batdokdata.id.BatdokTrainerSensorId

/**
 * A [BatdokTrainerSensorVital] is a data class responsible for holding the values of a vital for a [BatdokTrainerSensor].
 */
data class BatdokTrainerSensorVital(val id: BatdokTrainerSensorId, val batdokTrainerSensorName: String,
                                    val hr: Int = -1, val bps: Int = -1, val bpd: Int = -1, val rr: Int = -1, val spo2: Int = -1,
                                    val etco2: Int = -1, val ibps: Int = -1, val ibpd: Int = -1, val temp: Float = -1f){

    fun toBTMessage(): String{
        val builder = StringBuilder()
        return builder.buildMessage("HR", hr)
                .buildMessage("SpO2", spo2)
                .buildMessage("Resp", rr)
                .buildMessage("BP", bps, bpd)
                .buildMessage("EtCO2", etco2)
                .buildMessage("iBP", ibps, ibpd)
                .buildMessage("Temp", temp)
                .toString()
    }

    private fun StringBuilder.buildMessage(name: String, value: Int): StringBuilder{
        if(value != -1){
            if(this.isNotEmpty()){
                this.append(",")
            }
            this.append(name)
            this.append(",")
            this.append(value)
        }
        return this
    }

    private fun StringBuilder.buildMessage(name: String, value: Float): StringBuilder{
        if(value != -1f){
            if(this.isNotEmpty()){
                this.append(",")
            }
            this.append(name)
            this.append(",")
            this.append("%.1f".format(value))
        }
        return this
    }

    private fun StringBuilder.buildMessage(name: String, value1: Int, value2: Int): StringBuilder{
        if(value1 != -1 && value2 != -1){
            if(this.isNotEmpty()){
                this.append(",")
            }
            this.append(name)
            this.append(",")
            this.append(value1)
            this.append("/")
            this.append(value2)
        }
        return this
    }
}