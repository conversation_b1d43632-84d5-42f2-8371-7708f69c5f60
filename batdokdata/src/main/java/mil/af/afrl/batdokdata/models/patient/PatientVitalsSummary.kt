package mil.af.afrl.batdokdata.models.patient;

import android.graphics.Color
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import mil.af.afrl.batdokdata.models.vital.EncounterVitals
import mil.af.afrl.batdokdata.models.vital.VitalType

class PatientVitalsSummary (val vitals: EncounterVitals){

    private fun handleVital(builder: SpannableStringBuilder, vitalType: VitalType, vitalValue: String, forceDisplay: Boolean = false){
        if (forceDisplay || vitals.isVitalAvailable(vitalType)) {
            builder.append(buildVitalSpan(vitalType, vitalValue))
            builder.append("\n")
        }
    }
    
    fun getFullVitalSummary(unitConversion: (Float?) -> Float? = {it}): CharSequence {
        val builder = SpannableStringBuilder()

        handleVital(builder, VitalType.HR, vitals.getHrStr(), true)
        handleVital(builder, VitalType.SPO2, vitals.getSpo2Str(), true)
        handleVital(builder, VitalType.RESP, vitals.getRespStr(), true)
        handleVital(builder, VitalType.BPS, vitals.getBpsStr(), true)
        handleVital(builder, VitalType.BPD, vitals.getBpdStr(), true)
        handleVital(builder, VitalType.IBPS, vitals.getIbpsStr(), true)
        handleVital(builder, VitalType.IBPD, vitals.getIbpdStr(), true)
        handleVital(builder, VitalType.TEMP, vitals.getTempStr(unitConversion), true)
        handleVital(builder, VitalType.ETCO2, vitals.getEtco2Str(), true)
        return builder.removeSuffix("\n")
    }

    fun getFiveVitalSummary(): CharSequence {
        val builder = SpannableStringBuilder()

        handleVital(builder, VitalType.HR, vitals.getHrStr())
        handleVital(builder, VitalType.SPO2, vitals.getSpo2Str())
        handleVital(builder, VitalType.RESP, vitals.getRespStr())
        handleVital(builder, VitalType.BP, vitals.getBpStr())
        handleVital(builder, VitalType.ETCO2, vitals.getEtco2Str())
        return builder.removeSuffix("\n")
    }

    private fun buildVitalSpan(vitalType: VitalType, vitalValue: String): SpannableString {
        val asterisk = if (vitals.isLive) "" else "*"
        val vitalString = SpannableString("${vitalType.displayName}: $vitalValue$asterisk ")
        val color = when(vitalType){
            VitalType.HR -> Color.parseColor("#FF009900")
            VitalType.SPO2 -> Color.parseColor("#FFA0A000")
            VitalType.RESP -> Color.parseColor("#FF700070")
            VitalType.BPS -> Color.parseColor("#FF33B5E5")
            VitalType.BPD -> Color.parseColor("#FFA500")
            VitalType.IBPS -> Color.parseColor("#FF0000")
            VitalType.IBPD -> Color.parseColor("#800000")
            VitalType.TEMP -> Color.parseColor("#247554")
            VitalType.ETCO2 -> Color.parseColor("#E000E0")
            else -> Color.parseColor("#34a7ff")
        }
        vitalString.setSpan(ForegroundColorSpan(color), 0, vitalType.displayName.length + 1, 0)
        return vitalString
    }
}
