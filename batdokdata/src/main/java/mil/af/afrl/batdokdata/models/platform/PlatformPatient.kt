package mil.af.afrl.batdokdata.models.platform

import mil.af.afrl.batdokdata.id.EncounterId

/**
 * Object for a patient who is contained on a platform in CCP
 *
 * @property id The patient's EncounterId
 * @property name The patient's name
 * @property triage The patient's triage status
 * @property patientType The patient's type
 * @property rank The rank of the patient
 */
class PlatformPatient(val id: EncounterId,
                      val callsignNumber: String?,
                      val name: String?,
                      val triage: Int,
                      val patientType: Int,
                      val rank: Int): Comparable<PlatformPatient> {
    /**
     * Function to compare two platform patients' triage levels
     *
     * @param other The second patient to compare against
     */
    override fun compareTo(other: PlatformPatient): Int {
        return other.triage.compareTo(triage)
    }

    /**
     *
     * Function to check if two platform patients are the same using their IDs
     * @param other
     */
    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        if(other !is PlatformPatient) {
            return false
        }
        return id == other.id
    }

    override fun hashCode(): Int {
        return id.hashCode()
    }
}