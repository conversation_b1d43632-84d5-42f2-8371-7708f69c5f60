package mil.af.afrl.batdokdata.models.medinventory

/**
 * The object for a DD1380 Unit
 */
class MedUnitListItem(
    val id: Int,
    val unit: String
) : Comparable<MedUnitListItem> {

    override fun compareTo(other: MedUnitListItem): Int {
        fun String.getSlashCount() = count { it == '/' }
        val slashCount = unit.getSlashCount()
        val slashCount2 = other.unit.getSlashCount()
        return if (slashCount == slashCount2) {
            unit.lowercase().compareTo(other.unit.lowercase())
        } else {
            slashCount.compareTo(slashCount2)
        }
    }

    companion object {

        /**
         * returns a default list of unit names
         */
        val defaultItems: List<MedUnitListItem> = listOf(
            MedUnitListItem(0, "mg"),
            MedUnitListItem(0, "mcg"),
            MedUnitListItem(0, "mL"),
            MedUnitListItem(0, "gm"),
            MedUnitListItem(0, "units"),
            MedUnitListItem(0, "cc"),
            MedUnitListItem(0, "mg/mL"),
            MedUnitListItem(0, "mcg/mL"),
            MedUnitListItem(0, "mcg/kg/min"),
            MedUnitListItem(0, "mg/min"),
            MedUnitListItem(0, "mcg/kg"),
            MedUnitListItem(0, "mU/mL"),
            MedUnitListItem(0, "U/min"),
            MedUnitListItem(0, "mL/hr"),
        )
    }
}