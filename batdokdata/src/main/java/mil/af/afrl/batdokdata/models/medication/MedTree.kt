package mil.af.afrl.batdokdata.models.medication

import java.io.BufferedReader
import java.io.InputStream
import java.io.InputStreamReader
import java.io.Serializable

data class MedInfo(val group: String, val drug: String, val indication: String,
                   val route: String, val defaultConcentration: Double? = null,
                   val defaultConcentrationUnit: String? = null, val singleWeight: Double? = null,
                   val lowWeight: Double? = null, val highWeight: Double? = null, val unit: String? = null,
                   val page: Int = 1, val allowDecimal: Boolean = true)

class MedTree : Serializable {
    interface Node : Serializable
    @MedTreeDsl
    open class Branch(val name: String, val children: MutableList<Node> = mutableListOf(), val parent: Branch? = null) : Node {
        fun getChildBranches() = children.filterIsInstance<Branch>()
        fun getChildBranch(name: String) = getChildBranches().find { it.name == name }
        fun getChildLeafs() = children.filterIsInstance<Leaf>()
    }
    @MedTreeDsl
    open class Leaf(val route: String, val defaultConcentration: Double? = null,
                    val defaultConcentrationUnit: String? = null, val singleWeight: Double? = null,
                    val lowWeight: Double? = null, val highWeight: Double? = null, val unit: String? = null,
                    val page: Int = 1, val allowDecimal: Boolean = true, val parent: Node) : Node {
        val singleResult: Boolean? = when {
            singleWeight != null -> true
            lowWeight != null && highWeight != null -> false
            else -> null
        }
    }

    @MedTreeDsl
    private class Group(name: String, parent: Branch? = null): Branch(name, parent=parent)
    @MedTreeDsl
    private class Drug(name: String, parent: Branch? = null): Branch(name, parent=parent)
    @MedTreeDsl
    private class Indication(name: String, parent: Branch? = null): Branch(name, parent=parent)

    private val tree = mutableListOf<Branch>()

    companion object {
        @JvmStatic
        fun fromMedCsv(stream: InputStream, onlyDrips: Boolean = false) : MedTree {
            return BufferedReader(InputStreamReader(stream)).use { reader ->
                medTree {
                    reader.lineSequence()
                        .drop(1)  // Skip the header line
                        .map { it.split(",") }
                        .filter { lineSplits ->
                            if (onlyDrips) {
                                // If the route contains one of these strings, it's drippable
                                listOf("IV", "IO", "IN", "IM").any { it in lineSplits[3] }
                            } else {
                                true
                            }
                        }
                        .map {
                            MedInfo(it[0], it[1], it[2], it[3], it[4].toDoubleOrNull(), it[5].ifEmpty { null },
                                it[6].toDoubleOrNull(), it[8].toDoubleOrNull(), it[10].toDoubleOrNull(),
                                it[11].ifEmpty { it[7].ifEmpty { null } }, it[12].toIntOrNull() ?: 1,
                                it[13].isBlank())
                        }
                        .forEach(::add)
                }
            }
        }

        fun medTree(block: MedTree.() -> Unit) = MedTree().apply(block)
    }

    private inline fun <reified T : Branch> List<Node>.branch(branch: T, block: T.() -> Unit){
        var child = filterIsInstance<T>().find { it.name == branch.name }
        if (child == null) {
            child = branch
            (this as MutableList).add(child)
        }
        child.apply(block)
    }

    private fun MutableList<Node>.leaf(leaf: Leaf) = add(leaf)

    private inline fun <reified T: Branch> Branch.branch(name: T, block: T.() -> Unit) = children.branch(name, block)
    private fun Indication.leaf(medInfo: MedInfo) = children.leaf(Leaf(medInfo.route, medInfo.defaultConcentration,
        medInfo.defaultConcentrationUnit, medInfo.singleWeight, medInfo.lowWeight,
        medInfo.highWeight, medInfo.unit, medInfo.page, medInfo.allowDecimal, this))

    private inline fun Group.drug(name: String, block: Drug.() -> Unit) = branch(Drug(name, this), block)
    private inline fun Drug.indication(name: String, block: Indication.() -> Unit) = branch(Indication(name, this), block)

    private fun group(name: String, block: Group.() -> Unit) = tree.branch(Group(name), block)

    fun getTree() : List<Branch> = tree

    fun add(medInfo: MedInfo) {
        group(medInfo.group){
            drug(medInfo.drug){
                indication(medInfo.indication){
                    leaf(medInfo)
                }
            }
        }
    }
}

@DslMarker
private annotation class MedTreeDsl