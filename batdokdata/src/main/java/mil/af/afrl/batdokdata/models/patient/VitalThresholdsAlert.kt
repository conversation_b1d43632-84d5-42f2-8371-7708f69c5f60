package mil.af.afrl.batdokdata.models.patient

import mil.af.afrl.batdokdata.models.vital.VitalType
import java.io.Serializable

/**
 * Holds Threshold statuses
 * The vital with the given status will be color as follows:
 *
 * - STATUS_OKAY - Gray
 * - STATUS_WARN - Yellow
 * - STATUS_ALERT - Red
 *
 * Created on 9/23/17.
 */

data class VitalThresholdsAlert(val hr: Int = STATUS_OKAY, val spo2: Int = STATUS_OKAY, val resp: Int = STATUS_OKAY,
                                val bps: Int = STATUS_OKAY, val bpd: Int = STATUS_OKAY, val etco2: Int = STATUS_OKAY,
                                val ibps: Int? = STATUS_OKAY, val ibpd: Int? = STATUS_OKAY, val temp: Int? = STATUS_OKAY
) : Serializable
{
    fun getAlertLevelOf(vitalType: VitalType): Int
    {
        return when (vitalType) {
            VitalType.HR -> hr
            VitalType.SPO2 -> spo2
            VitalType.RESP -> resp
            VitalType.BPS -> bps
            VitalType.BPD -> bpd
            VitalType.ETCO2 -> etco2
            VitalType.IBPS -> ibps
            VitalType.IBPD -> ibpd
            VitalType.TEMP -> temp
            else -> throw Exception("Invalid vital type; query Vital to see possible args.")
        } ?: return -1
    }

    fun any(): Boolean =
        hr == STATUS_ALERT || resp == STATUS_ALERT || spo2 == STATUS_ALERT || bps == STATUS_ALERT ||
                bpd == STATUS_ALERT || etco2 == STATUS_ALERT || temp == STATUS_ALERT
}

const val STATUS_OKAY = 0
const val STATUS_WARN = 1
const val STATUS_ALERT = 2
