package mil.af.afrl.batdokdata.models.document

import mil.af.afrl.batdokdata.id.EncounterId
import java.time.Instant

interface DocumentQrCodeDataStore {
    suspend fun saveQrCode(qrCode: QrCode)
    suspend fun getLatestQrCode(encounterId: EncounterId, docType: String): QrCode?
    suspend fun getQrCode(encounterId: EncounterId, docType: String, createTime: Instant): QrCode?
    suspend fun getAllQrCodes(encounterId: EncounterId, docType: String): List<QrCode>
    suspend fun getAllQrCodes(encounterId: EncounterId): List<QrCode>
}