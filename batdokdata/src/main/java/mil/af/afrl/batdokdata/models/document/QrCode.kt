package mil.af.afrl.batdokdata.models.document

import com.batman.batdok.infrastructure.contactlesstransfer.proto.ContactlessTransferCommands
import mil.af.afrl.batdokdata.id.EncounterId
import java.io.Serializable
import java.time.Instant

class QrCode(@JvmField var encounterId: EncounterId?,
             @JvmField var docType: String,
             @JvmField var createTime: Instant,
             @JvmField var qrCodeData: ContactlessTransferCommands.ContactlessTransferMessage?
) : Comparable<QrCode>, Serializable {
    override fun compareTo(other: QrCode): Int {
        return createTime.compareTo(other.createTime)
    }
}