package mil.af.afrl.batdokdata.models.burnsheet

import mil.af.afrl.batman.batdokid.DomainId
import java.io.Serializable
import java.util.Objects

/**
 * This class contains the information to fill out the Burn Sheet
 */
class MedCardBurnSheetDataModel constructor(
    @JvmField val info: MedCardBurnSheetInfo = MedCardBurnSheetInfo(DomainId.create()),
    @JvmField var flowRowDataModelList: List<MedCardFlowRowDataModel> = listOf()
): Serializable {
    /**
     * Add the given row to the Burn Sheet. The burn sheet is limited to having 72 rows. Any more will be ignored
     * @param row The row to add
     */
    fun addRow(row: MedCardFlowRowDataModel) {
        if (flowRowDataModelList.size < 72) {
            flowRowDataModelList = flowRowDataModelList.plus(row)
        }
    }

    fun setFlowRowList(flowRowList: List<MedCardFlowRowDataModel>?) {
        this.flowRowDataModelList = ArrayList(flowRowList)
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) {
            return true
        }
        return if (other == null || other !is MedCardBurnSheetDataModel) {
            false
        } else info.id == other.info.id
    }

    override fun hashCode(): Int {
        return Objects.hash(info.id)
    }
}