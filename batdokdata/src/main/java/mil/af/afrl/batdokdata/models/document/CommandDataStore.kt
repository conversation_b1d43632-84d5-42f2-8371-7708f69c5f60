package mil.af.afrl.batdokdata.models.document

import gov.afrl.batdok.commands.proto.DocumentCommands.DocumentCommand
import kotlinx.coroutines.flow.Flow
import mil.af.afrl.batdokdata.id.EncounterId
import java.time.Instant

/**
 * This class is an interface for datastores that access commands
 */
interface CommandDataStore {
    fun liveCommands(id: EncounterId): Flow<List<DocumentCommand>>
    fun liveCommands(): Flow<List<DocumentCommand>>
    //Returns a list of Document commands where each one is all of the commands for an encounter of the patient
    fun liveCommandsWithHistory(id: EncounterId): Flow<List<DocumentCommand>>

    suspend fun commandsByEncounterId(id: EncounterId): List<DocumentCommand>

    suspend fun commandsByDateRange(encounterId: EncounterId, startDate: Instant, endDate: Instant): List<DocumentCommand>

    suspend fun commandsByDateRange(startDate: Instant, endDate: Instant): List<DocumentCommand>

    suspend fun patientCommandsNewerThan(encounterId: EncounterId, startDate: Instant): List<DocumentCommand>

    suspend fun allCommands(): List<DocumentCommand>

    suspend fun add(command: DocumentCommand)

    suspend fun add(commands: List<DocumentCommand>)

    suspend fun remove(id: EncounterId)
}