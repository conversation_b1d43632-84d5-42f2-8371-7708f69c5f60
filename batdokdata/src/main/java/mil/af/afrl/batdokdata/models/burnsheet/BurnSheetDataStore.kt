package mil.af.afrl.batdokdata.models.burnsheet

import kotlinx.coroutines.flow.Flow
import mil.af.afrl.batman.batdokid.DomainId

interface BurnSheetDataStore {
    fun liveBurnSheets(): Flow<List<MedCardBurnSheetDataModel>>
    suspend fun burnSheets(): List<MedCardBurnSheetDataModel>

    suspend fun insert(dataModel: MedCardBurnSheetDataModel)
    suspend fun insert(info: MedCardBurnSheetInfo)
    suspend fun insert(flowModel: MedCardFlowRowDataModel)

    suspend fun update(dataModel: MedCardBurnSheetDataModel)
    suspend fun update(info: MedCardBurnSheetInfo)
    suspend fun update(flowModel: MedCardFlowRowDataModel)

    suspend fun delete(id: DomainId)

    fun getLiveIdModelMap(): Flow<HashMap<DomainId, MedCardBurnSheetDataModel>>
    suspend fun getIdModelMap(): HashMap<DomainId, MedCardBurnSheetDataModel>
}