package mil.af.afrl.batdokdata.models.flightinfo

import gov.afrl.batdok.encounter.metadata.FlightInfo
import gov.afrl.batdok.encounter.metadata.FlightInfoId
import kotlinx.coroutines.flow.Flow

interface FlightInfoDataStore {
    suspend fun insert(flight: FlightInfo)
    suspend fun update(flight: FlightInfo)
    suspend fun delete(flightId: FlightInfo)
    fun getLiveFlights(): Flow<List<FlightInfo>>
    suspend fun getById(flightId: FlightInfoId): FlightInfo?
    suspend fun flightExists(flightId: FlightInfoId): <PERSON><PERSON><PERSON>
}