package mil.af.afrl.batdokdata.models.platform

import kotlinx.coroutines.flow.Flow
import mil.af.afrl.batdokdata.id.EncounterId
import mil.af.afrl.batdokdata.id.PlatformId

/**
 * This class is an interface for datastores that access CASEVAC Platforms
 */
interface PlatformDataStore {
    suspend fun platforms(): List<Platform>
    suspend fun platform(id: PlatformId): Platform?
    fun livePlatforms(): Flow<List<Platform>>
    fun livePatientsWithoutPlatform(): Flow<MutableList<PlatformPatient>>
    suspend fun patient(encounterId: EncounterId): PlatformPatient?
    suspend fun add(platform: Platform)
    suspend fun addPatientsToPlatform(platform: Platform, encounterIds: List<EncounterId>)
    suspend fun updatePatientRank(platformId: PlatformId, encounterId: EncounterId, oldRank: Int, newRank: Int)
    suspend fun updateAllPatientRanks(platformId: PlatformId)
    suspend fun removePatientsFromPlatforms(encounterId: List<EncounterId>)
    suspend fun update(platform: Platform)
    suspend fun delete(id: PlatformId)
    suspend fun deleteAll()
    suspend fun deleteTutorialPlatforms()
}