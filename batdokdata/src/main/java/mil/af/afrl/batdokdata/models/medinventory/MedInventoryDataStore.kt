package mil.af.afrl.batdokdata.models.medinventory

import gov.afrl.batdok.encounter.medicine.Medicine
import kotlinx.coroutines.flow.Flow
import mil.af.afrl.batman.batdokid.DomainId

/**
 * This class is an interface for datastores that access the BATDOK Medicine Inventory
 */
interface MedInventoryDataStore {
    companion object{
        @JvmField val ALL_MODES = "all"
    }

    fun medInventoryFlow(type: String?): Flow<List<MedInventory>>

    suspend fun medInventory(type: String?) = medInventory(type, null, true)

    suspend fun medInventory(type: String?, mode: String?, includeNullMode: Boolean): MedInventory

    suspend fun allMeds(): List<MedListItem>

    suspend fun allMedsByMode(mode: String?, includeNullMode: Boolean): List<MedListItem>

    suspend fun updateMedItemLists(medListItems: List<MedListItem>)

    suspend fun updateMedItemLists(mode: String?, medListItems: List<MedListItem>)

    suspend fun addMed(med: MedListItem)

    suspend fun addRoute(route: MedRouteListItem)

    suspend fun addUnit(unit: MedUnitListItem)

    suspend fun addConcentrationUnit(unit: ConcentrationUnitListItem)

    suspend fun removeMed(id: Int)

    suspend fun removeRoute(id: Int)

    suspend fun removeUnit(id: Int)

    suspend fun removeConcentrationUnit(id: Int)

    suspend fun retrieveSavedFastMeds(type: String?): List<SavedFastMed>

    suspend fun retrieveSavedFastMeds(type: String?, mode: String?, includeNullMode: Boolean=false): List<SavedFastMed>

    suspend fun addSavedFastMed(fastMed: SavedFastMed)

    suspend fun removeFastMed(fastMedId: DomainId)

    suspend fun updateFastMed(fastMed: SavedFastMed)

    suspend fun clearFastMeds()

    suspend fun clearFastMeds(mode: String?)

    suspend fun getCombatPillPackMeds(): List<SavedFastMed>

    suspend fun insertCombatPillPackMed(med: SavedFastMed)

    suspend fun addCombatPillPackMed(med: Medicine, type: String)

    suspend fun removeCombatPillPackMed(fastMedId: DomainId)

    suspend fun updateCombatPillPackMed(fastMed: SavedFastMed)
}
