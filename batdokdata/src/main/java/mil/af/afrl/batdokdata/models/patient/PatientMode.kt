package mil.af.afrl.batdokdata.models.patient

import com.batman.documentcommandslibrary.command.util.Mode

/**
 * Tracks the mode of the patient locally, the mode set by the owner,
 * and whether the user should be notified about the discrepency between the two
 *
 * @param localMode The [Mode] that the patient is being edited in locally. Defaults to null
 * @param ownerMode The mode that the owner of the patient is editing it in. . Defaults to null
 * @param notifyUser Whether the user should be notified if the [localMode] and [ownerMode] do not match and the [ownerMode] is installed.
 *                      Set to false by default and after the user is asked so they are only asked once. Set to true when a new owner mode is received.
 *
 */
class PatientMode(var localMode: String? = null, var ownerMode: String? = null, var notifyUser: Boolean = false) {


    companion object {
        fun modeForString(modeText: String): Mode? {
            return Mode.entries.find { it.dataString == modeText }
        }
    }

    fun shouldNotifyUser(availableModeNames:Array<String>): Boolean {
        return notifyUser
                && localMode != ownerMode
                && ownerModeIsInstalled(availableModeNames)
    }

    private fun ownerModeIsInstalled(availableModeNames:Array<String>): Boolean {
        return ownerModeName() in availableModeNames
    }

    fun localModeShortName():String {
        return when(localMode){
            null -> "Unknown"
            Mode.TACTICAL_CASUALTY_CARE_CARD.dataString -> "TCCC"
            Mode.MONITORING.dataString -> "Monitoring"
            Mode.STRATEVAC.dataString -> "CCAT"
            Mode.AMT.dataString -> "AMT"
            Mode.DNBI.dataString -> "DNBI"
            Mode.EMS.dataString -> "EMS"
            Mode.TACEVAC.dataString -> "TACEVAC"
            Mode.WORKING_DOG.dataString -> "TCCC K9"
            Mode.TCCC_MARCH.dataString -> "MARCH"
            Mode.STRATEVAC_AE.dataString -> "AE"
            Mode.STRATEVAC_ACC.dataString -> "DCR"
            Mode.PCC_MARCH.dataString -> "PCC"
            Mode.AERO_EVAC.dataString -> "Aero Evac"
            else -> localMode ?: ""
        }
    }

    fun localModeName(): String {
        return localMode ?: "Unknown"
    }

    fun ownerModeName(): String {
        return ownerMode ?: "Unknown"
    }
}